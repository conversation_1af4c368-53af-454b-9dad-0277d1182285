package error

import (
	"net/http"

	kunErr "icode.baidu.com/baidu/faas/kun/pkg/error"
)

const (
	// UnrecognizedClientException xxx
	UnrecognizedClientException kunErr.ErrorType = "UnrecognizedClientException"
)

// NewUnrecognizedClientException creates a UnrecognizedClientException
// HTTP status code is StatusForbidden (403)
func NewUnrecognizedClientException(cause string, lasterr error) kunErr.FinalError {
	return kunErr.NewGenericException(kunErr.BasicError{
		Code:    UnrecognizedClientException,
		Cause:   cause,
		Message: "Access Denied",
		Status:  http.StatusForbidden,
	}, lasterr)
}
