/**
 * Created Date: Thursday, August 24th 2017, 2:26:06 pm
 * Author: he<PERSON><PERSON><PERSON>
 * Copyright (c) 2017 Baidu.Inc
 *
 */

package console

import (
	"encoding/base64"
	"errors"
	"io/ioutil"
	"net/http"
	"strings"

	"github.com/asaskevich/govalidator"
	"github.com/aws/aws-sdk-go/service/lambda"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/dao"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/global"
	"icode.baidu.com/baidu/faas/kun/pkg/util/logs"
	"icode.baidu.com/baidu/faas/kun/pkg/util/zip"
)

// console PrepareCode 初始化代码参数
func PrepareCode(codeEntryType int, rawCode, zipFile string, function dao.Function, isUpdate bool) (*lambda.FunctionCode, error) {
	//java golang ...特殊处理,兼容console处理不了codeEntryType的情况
	if govalidator.Matches(function.Runtime, "^java|golang") {
		codeEntryType = 2
	}

	switch codeEntryType {
	case 1:
		// Raw Code
		fileName := getFileNameByHandler(function.Handler, function.Runtime)

		//如果是console页面update的请求，
		if isUpdate == true {
			//1.获取old 代码 from bos
			b, err := global.AC.Clients.Code.FaasGetCodeObject(function.FunctionName, function.Uid, function.CodeSha256, function.CodeID)
			if err != nil {
				return nil, err
			}
			files := make([]*map[string]string, 1)
			files[0] = &map[string]string{
				"fileName":  fileName,
				"fileBytes": rawCode,
			}
			zipData, err := zip.UpdateZipCode(b, files)
			if err != nil {
				return nil, err
			}
			return &lambda.FunctionCode{
				ZipFile: zipData,
			}, nil
			//2.zip包替换
		} else {
			zipData, err := zip.StreamSingleZip(fileName, []byte(rawCode))
			if err != nil {
				return nil, err
			}
			return &lambda.FunctionCode{
				ZipFile: zipData,
			}, nil
		}

	case 2:
		zipData, err := base64.StdEncoding.DecodeString(zipFile)
		if err != nil {
			return nil, err
		}
		return &lambda.FunctionCode{
			ZipFile: zipData,
		}, nil
	}
	return nil, errors.New("Invalid CodeEntryType")
}

func GetRawCode(url, handler, runtime string) (string, error) {

	d, err := GetBytesFromUrl(url)
	if err != nil {
		logs.Warnf("Read download code failed, %v", err)
		return "nil", err
	}
	fileName := getFileNameByHandler(handler, runtime)
	data, err := zip.StreamSingleUnzip(d, fileName)
	if err != nil {
		logs.Warnf("Unzip file failed, %v", err)
		return "nil", err
	}

	return string(data), nil
}

func GetBytesFromUrl(url string) ([]byte, error) {
	resp, err := http.Get(url)
	if err != nil {
		logs.Warnf("Download code failed, %v", err)
		return []byte(""), err
	}
	defer resp.Body.Close()
	return ioutil.ReadAll(resp.Body)
}

func FlattenEnvVar(o map[string]string) []*EnviromentVariables {
	ev := make([]*EnviromentVariables, 0)
	for k, v := range o {
		ev = append(ev, &EnviromentVariables{
			Key:   k,
			Value: v,
		})
	}
	return ev
}

func getFileNameByHandler(handler, runtime string) string {
	handlerParts := strings.Split(handler, ".")
	fileName := handlerParts[0] + global.AC.Cache.RuntimeCache[runtime].DefaultExt

	return fileName
}
