# ResourcePool 资源池管理模块

## 概述

ResourcePool模块为CFC调度系统提供了多资源池管理能力，支持根据函数运行时自动选择最适合的资源池，实现更精细化的资源调度。

## 核心特性

### 🎯 基于vipUser的智能集群查找
- 根据函数的vipUser标签（如"runtime_base_u22"）查找对应的集群配置
- 支持专用集群和公共集群的自动选择
- 未找到匹配集群时，自动使用公共集群（无vipUser标签的集群）

### 🚀 自动运行时匹配
- 根据函数运行时（如java8、java17、nodejs12等）自动选择合适的资源池
- 优先使用默认池，不支持时自动切换到专用池
- 无匹配池时自动降级到默认池，保证调度成功

### ⚡ 高性能缓存机制
- 启动时一次性加载所有集群配置到内存
- 运行时从缓存读取，无需访问etcd
- 实时watch机制，配置变更立即生效
- 支持多集群配置管理

### 🔄 向后兼容
- 可选启用，不启用时保持原有调度行为
- 现有调用代码无需修改
- 渐进式升级，平滑迁移

## 快速开始

### 1. 启用ResourcePool功能

```go
import (
    "icode.baidu.com/baidu/faas/kun/pkg/poolmanager/handler"
    "icode.baidu.com/baidu/faas/kun/pkg/etcd"
    "icode.baidu.com/baidu/faas/kun/pkg/util/logs"
)

// 在系统启动时初始化ResourcePool功能
err := handler.InitResourcePoolIfEnabled(
    etcdClient,
    "kun1.0", // etcd prefix
    logger,
    true,     // 启用ResourcePool功能
)
if err != nil {
    return err
}

// ResourcePool功能会自动在GetFreePod函数中生效
// 无需修改现有的调度器代码
```

### 2. 配置ResourcePool

在etcd中配置集群的ResourcePool信息：

```json
{
  "resourcePoolInfo": {
    "description": "默认资源池",
    "osType": "ubuntu16.04",
    "supportRuntimes": ["java8", "nodejs12", "python3"]
  },
  "resourcePoolConfig": {
    "enabled": true,
    "version": "1.0",
    "extraResourcePools": {
      "ubuntu2204-pool": {
        "resourcePoolInfo": {
          "description": "Ubuntu 22.04专用池",
          "osType": "ubuntu22.04",
          "supportRuntimes": ["java17", "nodejs18", "python3.10"]
        }
      }
    }
  }
}
```

### 3. 自动调度

配置完成后，系统会自动根据函数运行时选择合适的资源池：

```
函数请求: runtime=java17
↓
ResourcePool管理器: 默认池不支持 → 查找专用池 → 选择ubuntu2204-pool
↓
调度器: 在ubuntu2204-pool中查找节点 → 调度成功
```

## API 参考

### ManagerInterface

```go
type ManagerInterface interface {
    // 初始化ResourcePool缓存
    InitResourcePoolCache(etcdClient *etcd.Client, etcdPrefix string) error
    
    // 为标签增加ResourcePool信息
    EnhanceLabelsWithResourcePool(labels api.RequestLabels, runtime string, clusterID string)
    
    // 获取缓存状态（监控用）
    GetResourcePoolCacheStatus() map[string]interface{}
    
    // 停止管理器
    Stop() error
}
```

### 配置结构

```go
type ResourcePoolConfig struct {
    Enabled            bool                       `json:"enabled"`
    Version            string                     `json:"version"`
    ExtraResourcePools map[string]ResourcePool    `json:"extraResourcePools"`
}

type ResourcePool struct {
    ResourcePoolInfo `json:"resourcePoolInfo"`
    ScalingOptions   *ScalingOptions `json:"scalingOptions,omitempty"`
}

type ResourcePoolInfo struct {
    Description      string   `json:"description"`
    OSType          string   `json:"osType"`
    ContainerImage  string   `json:"containerImage,omitempty"`
    SupportRuntimes []string `json:"supportRuntimes,omitempty"`
}
```

## 运行时选择策略

### 优先级顺序

1. **🥇 默认池优先**: 首先检查默认池是否支持该运行时
2. **🥈 专用池查找**: 默认池不支持时，查找extraResourcePools中支持的池
3. **🥉 降级保护**: 没有找到支持的池时，仍然使用默认池

### 示例场景

```
场景1: java8函数调用
默认池支持java8 → 使用默认池 → 不添加resourcePool标签

场景2: java17函数调用  
默认池不支持java17 → 查找专用池 → 找到ubuntu2204-pool → 添加resourcePool=ubuntu2204-pool标签

场景3: 不支持的运行时
默认池不支持 → 专用池也不支持 → 降级到默认池 → 不添加resourcePool标签
```

## 监控和调试

### 获取缓存状态

```go
// 获取所有集群状态
status := manager.GetResourcePoolCacheStatus()

// 获取特定集群状态
clusterStatus := manager.GetClusterResourcePoolCacheStatus("cluster-id")
```

### 状态信息

```json
{
  "initialized": true,
  "clustersCount": 2,
  "clusters": {
    "cluster-1": {
      "enabled": true,
      "extraPoolsCount": 2,
      "lastUpdateTime": "2023-07-29T10:30:00Z",
      "defaultPoolConfigured": true,
      "extraPools": ["ubuntu2204-pool", "gpu-pool"]
    }
  }
}
```

## 测试

运行单元测试：

```bash
cd pkg/poolmanager/resourcepool
go test -v
```

## 注意事项

1. **etcd配置**: 确保etcd中有正确的ResourcePool配置
2. **运行时匹配**: supportRuntimes列表要准确配置
3. **向后兼容**: 可以逐步启用，不影响现有功能
4. **性能**: 使用内存缓存，运行时性能开销极小
5. **监控**: 建议监控缓存状态和选择结果

## 故障排除

### 常见问题

1. **ResourcePool不生效**: 检查enabled配置和etcd连接
2. **运行时不匹配**: 检查supportRuntimes配置
3. **缓存未更新**: 检查etcd watch机制和网络连接

### 日志级别

- `V(6)`: 详细的ResourcePool选择过程
- `Info`: 重要的状态变更和错误
- `Error`: 配置加载失败等错误

## 更多示例

参考 `examples/` 目录中的完整示例代码。
