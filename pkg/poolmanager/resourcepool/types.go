package resourcepool

import (
	"sync"
	"time"

	"icode.baidu.com/baidu/faas/kun/pkg/api"
	"icode.baidu.com/baidu/faas/kun/pkg/etcd"
	"icode.baidu.com/baidu/faas/kun/pkg/util/logs"
)

// ResourcePoolCache represents merged ResourcePool configuration for clusters with same vipUser
type ResourcePoolCache struct {
	vipUserLabel       string                       // vipUser标签值（"common"表示公共集群）
	enabled            bool                         // ResourcePool功能是否启用
	defaultPoolInfo    *api.ResourcePoolInfo        // 默认池配置（取第一个启用的集群）
	extraResourcePools map[string]*api.ResourcePool // 合并后的额外ResourcePool配置（同名去重，不同名合并）
	userPoolMappings   map[string]string            // 用户到资源池的映射关系 uid -> poolName
	lastUpdateTime     time.Time                    // 最后更新时间
}

// Manager is the ResourcePool manager implementation
type Manager struct {
	logger *logs.Logger // Using interface{} to avoid import cycle, will be *logs.Logger

	// etcd configuration (from config parameters)
	etcdPrefix string       // etcd prefix, e.g., "kun1.0"
	etcdClient *etcd.Client // 保存etcd客户端引用用于全量重新加载

	// ResourcePool related cache (merged by vipUser)
	vipUserCaches map[string]*ResourcePoolCache // key: vipUser ("common" for public clusters)
	mu            sync.RWMutex

	// stop signal
	stopChan chan struct{}
	stopped  bool

	// 全量重新加载相关
	reloadTicker *time.Ticker // 定期全量重新加载定时器
	lastReload   time.Time    // 上次全量重新加载时间
}

// ManagerConfig represents configuration parameters for ResourcePool manager
type ManagerConfig struct {
	// etcd prefix, referring to cron module configuration
	// default value: "kun1.0", consistent with etcd.Options.Prefix
	EtcdPrefix string
}
