package resourcepool

import (
	"icode.baidu.com/baidu/faas/kun/pkg/api"
	"icode.baidu.com/baidu/faas/kun/pkg/etcd"
)

// GetEtcdPrefixFromOptions gets etcd prefix from etcd options (referring to cron module)
func GetEtcdPrefixFromOptions(etcdOptions *etcd.Options) string {
	if etcdOptions.Prefix != "" {
		return etcdOptions.Prefix
	}
	return "kun1.0" // default value, consistent with defaultKunPrefix in etcd package
}

// isValidResourcePool validates if ResourcePool exists and is enabled in configuration（双开关设计）
func (m *Manager) isValidResourcePool(poolName string, clusterCache *ResourcePoolCache) bool {
	if clusterCache == nil || !clusterCache.enabled {
		m.logger.V(6).Infof("ResourcePool not enabled globally")
		return false
	}

	// default池总是有效（如果全局开关开启）
	if poolName == "default" {
		return true
	}

	// 检查单个池子是否存在且启用
	if pool, exists := clusterCache.extraResourcePools[poolName]; exists {
		if !pool.Enabled {
			m.logger.V(6).Infof("ResourcePool %s disabled individually", poolName)
			return false
		}
		return true
	}

	return false
}

// isRuntimeSupported validates if ResourcePool supports specified runtime
func (m *Manager) isRuntimeSupported(poolInfo api.ResourcePoolInfo, runtime string) bool {
	for _, supportedRuntime := range poolInfo.SupportRuntimes {
		if supportedRuntime == runtime {
			return true
		}
	}
	return false
}

// isDefaultPoolSupportRuntime checks if default pool supports specified runtime (using cache, supports multiple clusters)
func (m *Manager) isDefaultPoolSupportRuntime(runtime string, clusterCache *ResourcePoolCache) bool {
	// Read default pool runtime support info from cache
	if clusterCache.defaultPoolInfo == nil {
		// Log warning
		m.logger.Warnf("Default pool runtime support info not configured for vipUser %s, runtime %s will check extra pools", clusterCache.vipUserLabel, runtime)
		return false
	}

	// Check if default pool supports the runtime from cache configuration
	return m.isRuntimeSupported(*clusterCache.defaultPoolInfo, runtime)
}
