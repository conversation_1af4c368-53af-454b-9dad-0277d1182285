package resourcepool

import (
	"icode.baidu.com/baidu/faas/kun/pkg/api"
)

// EnhanceLabelsWithResourcePool 根据运行时和用户为标签增加ResourcePool信息
func (m *Manager) EnhanceLabelsWithResourcePool(labels api.RequestLabels, runtime string, vipUser string) {
	if m == nil {
		return
	}

	// 根据vipUser查找对应的集群配置
	clusterConfig := m.findClusterConfigByVipUser(vipUser)
	if clusterConfig == nil {
		m.logger.V(6).Infof("No cluster found for vipUser %s, using default pool", vipUser)
		return
	}

	// 从labels中获取实际用户ID（用于用户隔离判断）
	userID := labels.Get(api.LabelUserID)

	// 选择合适的ResourcePool（考虑用户隔离）
	resourcePool := m.selectResourcePoolByRuntimeAndUser(runtime, userID, clusterConfig)
	if resourcePool != "" && resourcePool != "default" {
		labels.Set(api.LabelResourcePool, resourcePool)
		m.logger.V(6).Infof("Enhanced labels with ResourcePool: %s for runtime: %s, vipUser: %s, userID: %s", resourcePool, runtime, vipUser, userID)
	}
}

// selectResourcePoolByRuntime 根据运行时自动选择ResourcePool（双开关设计）
func (m *Manager) selectResourcePoolByRuntime(runtime string, clusterConfig *ResourcePoolCache) string {
	if clusterConfig == nil || !clusterConfig.enabled {
		m.logger.V(6).Infof("ResourcePool not enabled globally, using default")
		return "default"
	}

	// 1. 🥇 优先检查默认池是否支持该运行时
	if m.isDefaultPoolSupportRuntime(runtime, clusterConfig) {
		return "default"
	}

	// 2. 🥈 如果默认池不支持，在extraResourcePools中查找支持且启用的池
	for poolName, pool := range clusterConfig.extraResourcePools {
		// 检查单个池子开关
		if !pool.Enabled {
			m.logger.V(6).Infof("ResourcePool %s disabled individually, skipping", poolName)
			continue
		}

		if m.isRuntimeSupported(pool.ResourcePoolInfo, runtime) {
			m.logger.V(6).Infof("Selected ResourcePool %s for runtime %s", poolName, runtime)
			return poolName
		}
	}

	// 3. 🥉 如果没有找到支持的池，仍然降级到默认池
	m.logger.V(6).Infof("No specific ResourcePool found for runtime %s, using default", runtime)
	return "default"
}

// selectResourcePoolByRuntimeAndUser 根据运行时和用户选择ResourcePool（用户专属池优先）
func (m *Manager) selectResourcePoolByRuntimeAndUser(runtime, userID string, clusterConfig *ResourcePoolCache) string {
	if clusterConfig == nil || !clusterConfig.enabled {
		m.logger.V(6).Infof("ResourcePool not enabled globally, using default")
		return "default"
	}

	// 1. 🥇 优先检查用户专属资源池
	if userID != "" {
		dedicatedPool := m.selectUserDedicatedPool(userID, runtime, clusterConfig)
		if dedicatedPool != "" {
			m.logger.V(6).Infof("Selected dedicated pool %s for user %s, runtime %s", dedicatedPool, userID, runtime)
			return dedicatedPool
		}
	}

	// 2. 🥈 降级到通用资源池选择逻辑
	fallbackPool := m.selectResourcePoolByRuntime(runtime, clusterConfig)

	// 3. 🥉 检查用户是否有权限访问选中的池子
	if userID != "" && fallbackPool != "default" {
		if !m.checkUserPoolAccess(userID, fallbackPool, clusterConfig) {
			m.logger.V(6).Infof("User %s has no access to pool %s, using default", userID, fallbackPool)
			return "default"
		}
	}

	return fallbackPool
}

// selectUserDedicatedPool 为用户选择专属资源池
func (m *Manager) selectUserDedicatedPool(userID, runtime string, clusterConfig *ResourcePoolCache) string {
	// 检查用户映射关系
	if clusterConfig.userPoolMappings == nil {
		return ""
	}

	// 查找用户专属池映射
	poolName, exists := clusterConfig.userPoolMappings[userID]
	if !exists {
		m.logger.V(6).Infof("No dedicated pool mapping found for user %s", userID)
		return ""
	}

	// 检查专属池是否存在且可用
	if pool, exists := clusterConfig.extraResourcePools[poolName]; exists {
		// 检查池子是否启用
		if !pool.Enabled {
			m.logger.V(6).Infof("Dedicated pool %s disabled for user %s", poolName, userID)
			return ""
		}

		// 检查池子是否支持该运行时
		if m.isRuntimeSupported(pool.ResourcePoolInfo, runtime) {
			// 检查池子类型是否允许该用户访问
			if m.isUserAllowedInPool(userID, poolName, *pool) {
				return poolName
			}
		}
	}

	return ""
}

// checkUserPoolAccess 检查用户是否有权限访问指定资源池
func (m *Manager) checkUserPoolAccess(userID, poolName string, clusterConfig *ResourcePoolCache) bool {
	// default池总是允许访问
	if poolName == "default" {
		return true
	}

	// 检查池子是否存在
	pool, exists := clusterConfig.extraResourcePools[poolName]
	if !exists {
		return false
	}

	// 检查用户是否被允许访问该池子
	return m.isUserAllowedInPool(userID, poolName, *pool)
}

// isUserAllowedInPool 检查用户是否被允许访问指定池子
func (m *Manager) isUserAllowedInPool(userID, poolName string, pool api.ResourcePool) bool {
	switch pool.PoolType {
	case api.ResourcePoolTypeShared:
		// 共享池：所有用户都可以访问
		return true

	case api.ResourcePoolTypeDedicated:
		// 专属池：只有指定用户可以访问
		for _, dedicatedUser := range pool.DedicatedUsers {
			if dedicatedUser == userID {
				return true
			}
		}
		return false

	case api.ResourcePoolTypeHybrid:
		// 混合池：专属用户优先，其他用户在有空闲时也可以访问
		// 这里简化实现，允许所有用户访问（实际应该检查资源使用情况）
		return true

	default:
		// 未指定类型，默认为共享池
		return true
	}
}

// findClusterConfigByVipUser 根据vipUser查找对应的集群配置
func (m *Manager) findClusterConfigByVipUser(vipUser string) *ResourcePoolCache {
	m.mu.RLock()
	defer m.mu.RUnlock()

	// 标准化vipUser：空值表示公共集群，使用"common"
	if vipUser == "" {
		vipUser = "common"
	}

	// 直接查找vipUser缓存
	if cache, exists := m.vipUserCaches[vipUser]; exists {
		m.logger.V(6).Infof("Found vipUser cache for %s", vipUser)
		return cache
	}

	// 如果没有找到匹配的vipUser，查找公共集群
	if cache, exists := m.vipUserCaches["common"]; exists {
		m.logger.V(6).Infof("Using common cluster cache for vipUser %s", vipUser)
		return cache
	}

	return nil
}
