package resourcepool

import (
	"testing"

	"icode.baidu.com/baidu/faas/kun/pkg/api"
	"icode.baidu.com/baidu/faas/kun/pkg/util/logs"
)

// TestDualSwitchLogic 测试双开关逻辑
func TestDualSwitchLogic(t *testing.T) {
	logger := logs.NewLogger()
	manager := &Manager{
		logger:        logger,
		vipUserCaches: make(map[string]*ResourcePoolCache),
	}

	// 测试用例1：全局开关关闭，池子开关开启
	t.Run("GlobalDisabled_PoolEnabled", func(t *testing.T) {
		clusterConfig := &ResourcePoolCache{
			enabled: false, // 全局开关关闭
			extraResourcePools: map[string]*api.ResourcePool{
				"ubuntu2204-pool": &api.ResourcePool{
					Enabled: true, // 池子开关开启
					ResourcePoolInfo: api.ResourcePoolInfo{
						SupportRuntimes: []string{"java17"},
					},
				},
			},
		}

		// 应该返回default（全局开关优先级最高）
		result := manager.selectResourcePoolByRuntime("java17", clusterConfig)
		if result != "default" {
			t.<PERSON>("Expected 'default', got '%s'", result)
		}

		// 验证池子有效性检查
		if manager.isValidResourcePool("ubuntu2204-pool", clusterConfig) {
			t.Error("Pool should be invalid when global switch is disabled")
		}
	})

	// 测试用例2：全局开关开启，池子开关关闭
	t.Run("GlobalEnabled_PoolDisabled", func(t *testing.T) {
		clusterConfig := &ResourcePoolCache{
			enabled: true, // 全局开关开启
			extraResourcePools: map[string]*api.ResourcePool{
				"ubuntu2204-pool": &api.ResourcePool{
					Enabled: false, // 池子开关关闭
					ResourcePoolInfo: api.ResourcePoolInfo{
						SupportRuntimes: []string{"java17"},
					},
				},
			},
		}

		// 应该返回default（池子被禁用）
		result := manager.selectResourcePoolByRuntime("java17", clusterConfig)
		if result != "default" {
			t.Errorf("Expected 'default', got '%s'", result)
		}

		// 验证池子有效性检查
		if manager.isValidResourcePool("ubuntu2204-pool", clusterConfig) {
			t.Error("Pool should be invalid when individually disabled")
		}
	})

	// 测试用例3：全局开关开启，池子开关开启
	t.Run("GlobalEnabled_PoolEnabled", func(t *testing.T) {
		clusterConfig := &ResourcePoolCache{
			enabled: true, // 全局开关开启
			extraResourcePools: map[string]*api.ResourcePool{
				"ubuntu2204-pool": &api.ResourcePool{
					Enabled: true, // 池子开关开启
					ResourcePoolInfo: api.ResourcePoolInfo{
						SupportRuntimes: []string{"java17"},
					},
				},
			},
		}

		// 应该返回ubuntu2204-pool
		result := manager.selectResourcePoolByRuntime("java17", clusterConfig)
		if result != "ubuntu2204-pool" {
			t.Errorf("Expected 'ubuntu2204-pool', got '%s'", result)
		}

		// 验证池子有效性检查
		if !manager.isValidResourcePool("ubuntu2204-pool", clusterConfig) {
			t.Error("Pool should be valid when both switches are enabled")
		}
	})

	// 测试用例4：多个池子，部分启用部分禁用
	t.Run("MultiplePoolsMixedStates", func(t *testing.T) {
		clusterConfig := &ResourcePoolCache{
			enabled: true, // 全局开关开启
			extraResourcePools: map[string]*api.ResourcePool{
				"ubuntu2204-pool": &api.ResourcePool{
					Enabled: true, // 启用
					ResourcePoolInfo: api.ResourcePoolInfo{
						SupportRuntimes: []string{"java17"},
					},
				},
				"gpu-pool": {
					Enabled: false, // 禁用
					ResourcePoolInfo: api.ResourcePoolInfo{
						SupportRuntimes: []string{"tensorflow"},
					},
				},
				"ai-pool": {
					Enabled: true, // 启用
					ResourcePoolInfo: api.ResourcePoolInfo{
						SupportRuntimes: []string{"pytorch"},
					},
				},
			},
		}

		// java17应该选择ubuntu2204-pool
		result1 := manager.selectResourcePoolByRuntime("java17", clusterConfig)
		if result1 != "ubuntu2204-pool" {
			t.Errorf("Expected 'ubuntu2204-pool', got '%s'", result1)
		}

		// tensorflow应该降级到default（gpu-pool被禁用）
		result2 := manager.selectResourcePoolByRuntime("tensorflow", clusterConfig)
		if result2 != "default" {
			t.Errorf("Expected 'default', got '%s'", result2)
		}

		// pytorch应该选择ai-pool
		result3 := manager.selectResourcePoolByRuntime("pytorch", clusterConfig)
		if result3 != "ai-pool" {
			t.Errorf("Expected 'ai-pool', got '%s'", result3)
		}
	})

	// 测试用例5：default池总是有效（如果全局开关开启）
	t.Run("DefaultPoolAlwaysValid", func(t *testing.T) {
		clusterConfig := &ResourcePoolCache{
			enabled:            true, // 全局开关开启
			extraResourcePools: map[string]*api.ResourcePool{},
		}

		// default池应该总是有效
		if !manager.isValidResourcePool("default", clusterConfig) {
			t.Error("Default pool should always be valid when global switch is enabled")
		}

		// 全局开关关闭时，default池也无效
		clusterConfig.enabled = false
		if manager.isValidResourcePool("default", clusterConfig) {
			t.Error("Default pool should be invalid when global switch is disabled")
		}
	})
}

// TestCronHelperDualSwitch 测试cron模块helper的双开关逻辑
// 注意：由于包依赖问题，cron helper的测试应该放在pkg/cron/resourcepool包中
func TestCronHelperDualSwitch(t *testing.T) {
	t.Skip("This test should be in pkg/cron/resourcepool package")
}
