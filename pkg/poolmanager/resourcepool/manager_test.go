package resourcepool

import (
	"testing"
	"time"

	"icode.baidu.com/baidu/faas/kun/pkg/api"
	"icode.baidu.com/baidu/faas/kun/pkg/util/logs"
)

func TestSelectResourcePoolByRuntime(t *testing.T) {
	logger := logs.NewLogger()
	manager := &Manager{
		logger:        logger,
		vipUserCaches: make(map[string]*ResourcePoolCache),
	}

	// Test case 1: nil cluster config
	result := manager.selectResourcePoolByRuntime("java8", nil)
	if result != "default" {
		t.Errorf("Expected 'default', got '%s'", result)
	}

	// Test case 2: cluster exists but disabled
	disabledConfig := &ResourcePoolCache{
		vipUserLabel: "test-vip",
		enabled:      false,
	}
	result = manager.selectResourcePoolByRuntime("java8", disabledConfig)
	if result != "default" {
		t.Errorf("Expected 'default', got '%s'", result)
	}

	// Test case 3: cluster enabled, default pool supports runtime
	enabledConfig := &ResourcePoolCache{
		vipUserLabel: "test-vip",
		enabled:      true,
		defaultPoolInfo: &api.ResourcePoolInfo{
			SupportRuntimes: []string{"java8", "nodejs12"},
		},
		extraResourcePools: make(map[string]*api.ResourcePool),
		lastUpdateTime:     time.Now(),
	}
	result = manager.selectResourcePoolByRuntime("java8", enabledConfig)
	if result != "default" {
		t.Errorf("Expected 'default', got '%s'", result)
	}

	// Test case 4: cluster enabled, default pool doesn't support, extra pool supports
	extraPoolConfig := &ResourcePoolCache{
		vipUserLabel: "test-vip",
		enabled:      true,
		defaultPoolInfo: &api.ResourcePoolInfo{
			SupportRuntimes: []string{"nodejs12"},
		},
		extraResourcePools: map[string]*api.ResourcePool{
			"java-pool": {
				ResourcePoolInfo: api.ResourcePoolInfo{
					SupportRuntimes: []string{"java8", "java11"},
				},
			},
		},
		lastUpdateTime: time.Now(),
	}
	result = manager.selectResourcePoolByRuntime("java8", extraPoolConfig)
	if result != "java-pool" {
		t.Errorf("Expected 'java-pool', got '%s'", result)
	}

	// Test case 5: cluster enabled, no pool supports runtime
	result = manager.selectResourcePoolByRuntime("python3", extraPoolConfig)
	if result != "default" {
		t.Errorf("Expected 'default', got '%s'", result)
	}
}

func TestEnhanceLabelsWithResourcePool(t *testing.T) {
	logger := logs.NewLogger()
	manager := &Manager{
		logger:        logger,
		vipUserCaches: make(map[string]*ResourcePoolCache),
	}

	// Setup test cache for vipUser "runtime_base_u22"
	manager.vipUserCaches["runtime_base_u22"] = &ResourcePoolCache{
		vipUserLabel: "runtime_base_u22",
		enabled:      true,
		defaultPoolInfo: &api.ResourcePoolInfo{
			SupportRuntimes: []string{"nodejs12"},
		},
		extraResourcePools: map[string]*api.ResourcePool{
			"java-pool": {
				ResourcePoolInfo: api.ResourcePoolInfo{
					SupportRuntimes: []string{"java8", "java11"},
				},
			},
		},
		lastUpdateTime: time.Now(),
	}

	// Setup test cache for public cluster (use "common")
	manager.vipUserCaches["common"] = &ResourcePoolCache{
		vipUserLabel: "common",
		enabled:      true,
		defaultPoolInfo: &api.ResourcePoolInfo{
			SupportRuntimes: []string{"python3"},
		},
		extraResourcePools: map[string]*api.ResourcePool{},
		lastUpdateTime:     time.Now(),
	}

	// Test case 1: should add ResourcePool label for specific vipUser
	labels := make(api.RequestLabels)
	manager.EnhanceLabelsWithResourcePool(labels, "java8", "runtime_base_u22")

	if resourcePool := labels.Get(api.LabelResourcePool); resourcePool != "java-pool" {
		t.Errorf("Expected ResourcePool label to be 'java-pool', got '%s'", resourcePool)
	}

	// Test case 2: should not add ResourcePool label for default pool
	labels2 := make(api.RequestLabels)
	manager.EnhanceLabelsWithResourcePool(labels2, "nodejs12", "runtime_base_u22")

	if resourcePool := labels2.Get(api.LabelResourcePool); resourcePool != "" {
		t.Errorf("Expected no ResourcePool label for default pool, got '%s'", resourcePool)
	}

	// Test case 3: should use public cluster for unknown vipUser
	labels3 := make(api.RequestLabels)
	manager.EnhanceLabelsWithResourcePool(labels3, "python3", "unknown_vip_user")

	if resourcePool := labels3.Get(api.LabelResourcePool); resourcePool != "" {
		t.Errorf("Expected no ResourcePool label for public cluster default pool, got '%s'", resourcePool)
	}

	// Test case 4: nil manager should not panic
	var nilManager *Manager
	labels4 := make(api.RequestLabels)
	nilManager.EnhanceLabelsWithResourcePool(labels4, "java8", "runtime_base_u22")

	if resourcePool := labels4.Get(api.LabelResourcePool); resourcePool != "" {
		t.Errorf("Expected no ResourcePool label for nil manager, got '%s'", resourcePool)
	}
}

func TestMultiClusterMerging(t *testing.T) {
	logger := logs.NewLogger()
	manager := &Manager{
		logger:        logger,
		vipUserCaches: make(map[string]*ResourcePoolCache),
	}

	// 模拟多个集群有相同的vipUser
	k8sInfo1 := &api.K8sInfo{
		NodeConfig: api.NodeConfig{
			ClusterLabels: map[string]string{
				api.LabelVipUser: "runtime_base_u22",
			},
		},
		ResourcePoolConfig: &api.ResourcePoolConfig{
			SchedulingEnabled: true,
			ExtraResourcePools: map[string]*api.ResourcePool{
				"ubuntu2204-pool": &api.ResourcePool{
					ResourcePoolInfo: api.ResourcePoolInfo{
						SupportRuntimes: []string{"java17", "nodejs18"},
					},
				},
				"gpu-pool": &api.ResourcePool{
					ResourcePoolInfo: api.ResourcePoolInfo{
						SupportRuntimes: []string{"python3.9"},
					},
				},
			},
		},
	}

	k8sInfo2 := &api.K8sInfo{
		NodeConfig: api.NodeConfig{
			ClusterLabels: map[string]string{
				api.LabelVipUser: "runtime_base_u22",
			},
		},
		ResourcePoolConfig: &api.ResourcePoolConfig{
			Enabled: true,
			ExtraResourcePools: map[string]*api.ResourcePool{
				"ubuntu2204-pool": &api.ResourcePool{ // 同名ResourcePool，应该去重
					ResourcePoolInfo: api.ResourcePoolInfo{
						SupportRuntimes: []string{"java17", "nodejs18"},
					},
				},
				"ai-pool": { // 不同名ResourcePool，应该合并
					ResourcePoolInfo: api.ResourcePoolInfo{
						SupportRuntimes: []string{"tensorflow", "pytorch"},
					},
				},
			},
		},
	}

	// 合并第一个集群
	manager.mergeClusterToVipUserCache("runtime_base_u22", k8sInfo1)

	// 合并第二个集群
	manager.mergeClusterToVipUserCache("runtime_base_u22", k8sInfo2)

	// 验证合并结果
	cache := manager.vipUserCaches["runtime_base_u22"]
	if cache == nil {
		t.Fatal("Expected merged cache for runtime_base_u22")
	}

	// 验证ResourcePool合并：同名去重，不同名合并
	expectedPools := []string{"ubuntu2204-pool", "gpu-pool", "ai-pool"}
	if len(cache.extraResourcePools) != 3 {
		t.Errorf("Expected 3 ResourcePools, got %d", len(cache.extraResourcePools))
	}

	for _, poolName := range expectedPools {
		if _, exists := cache.extraResourcePools[poolName]; !exists {
			t.Errorf("Expected ResourcePool '%s' to exist", poolName)
		}
	}

	// 验证功能：java17应该选择ubuntu2204-pool
	labels := make(api.RequestLabels)
	manager.EnhanceLabelsWithResourcePool(labels, "java17", "runtime_base_u22")

	if resourcePool := labels.Get(api.LabelResourcePool); resourcePool != "ubuntu2204-pool" {
		t.Errorf("Expected ResourcePool 'ubuntu2204-pool', got '%s'", resourcePool)
	}

	// 验证功能：tensorflow应该选择ai-pool
	labels2 := make(api.RequestLabels)
	manager.EnhanceLabelsWithResourcePool(labels2, "tensorflow", "runtime_base_u22")

	if resourcePool := labels2.Get(api.LabelResourcePool); resourcePool != "ai-pool" {
		t.Errorf("Expected ResourcePool 'ai-pool', got '%s'", resourcePool)
	}
}

func TestVipUserCacheManagement(t *testing.T) {
	logger := logs.NewLogger()
	manager := &Manager{
		logger:        logger,
		vipUserCaches: make(map[string]*ResourcePoolCache),
	}

	// 模拟集群配置
	k8sInfo := &api.K8sInfo{
		NodeConfig: api.NodeConfig{
			ClusterLabels: map[string]string{
				api.LabelVipUser: "runtime_base_u22",
			},
		},
		ResourcePoolConfig: &api.ResourcePoolConfig{
			Enabled: true,
			ExtraResourcePools: map[string]*api.ResourcePool{
				"test-pool": &api.ResourcePool{
					ResourcePoolInfo: api.ResourcePoolInfo{
						SupportRuntimes: []string{"java17"},
					},
				},
			},
		},
	}

	// 第一次合并
	manager.mergeClusterToVipUserCache("runtime_base_u22", k8sInfo)

	// 验证缓存创建
	cache := manager.vipUserCaches["runtime_base_u22"]
	if cache == nil {
		t.Fatal("Expected cache to be created")
	}

	// 第二次合并（模拟另一个集群）
	manager.mergeClusterToVipUserCache("runtime_base_u22", k8sInfo)

	// 验证ResourcePool配置正确
	if len(cache.extraResourcePools) != 1 {
		t.Errorf("Expected 1 ResourcePool, got %d", len(cache.extraResourcePools))
	}

	if _, exists := cache.extraResourcePools["test-pool"]; !exists {
		t.Error("Expected test-pool to exist")
	}

	// 验证缓存仍然存在（全量重新加载机制下，缓存由定期重新加载维护）
	if manager.vipUserCaches["runtime_base_u22"] == nil {
		t.Error("Expected cache to exist")
	}
}
