package resourcepool

import (
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"github.com/coreos/etcd/clientv3"
	"icode.baidu.com/baidu/faas/kun/pkg/api"
	"icode.baidu.com/baidu/faas/kun/pkg/etcd"
)

// InitResourcePoolCache initializes ResourcePool cache (referring to cron module implementation)
func (m *Manager) InitResourcePoolCache(etcdClient *etcd.Client, etcdPrefix string) error {
	m.stopChan = make(chan struct{})
	m.etcdPrefix = etcdPrefix
	m.etcdClient = etcdClient // 保存etcd客户端引用，用于全量重新加载
	m.lastReload = time.Now()

	// 1. Load all cluster configurations through prefix (referring to cron module)
	if err := m.loadAllClustersResourcePoolConfig(etcdClient, etcdPrefix); err != nil {
		return fmt.Errorf("failed to load initial ResourcePool configs: %v", err)
	}

	// 2. Start watch mechanism to monitor all cluster configuration changes
	go m.watchAllClustersResourcePoolConfig(etcdClient, etcdPrefix)

	// 3. 启动定期全量重新加载机制（每5分钟）
	// watch机制的拖底，防止watch长连接断开，并处理etcd集群配置中删除resourcePool的情形，确保最终和etcd配置一致性
	m.reloadTicker = time.NewTicker(5 * time.Minute)
	go m.periodicFullReload()

	m.logger.Infof("ResourcePool cache initialized successfully for %d vipUsers with etcdPrefix: %s",
		len(m.vipUserCaches), etcdPrefix)
	return nil
}

// loadAllClustersResourcePoolConfig loads all clusters' ResourcePool configurations to cache (referring to cron module's GetAllK8s implementation)
func (m *Manager) loadAllClustersResourcePoolConfig(etcdClient *etcd.Client, etcdPrefix string) error {
	// Use the same prefix construction method as cron module
	// etcdPrefix comes from config parameters, default is "kun1.0"
	keyPrefix := fmt.Sprintf("%s/k8s/", etcdPrefix)
	resp, err := etcdClient.GetWithPrefix(keyPrefix)
	if err != nil {
		return err
	}

	m.mu.Lock()
	defer m.mu.Unlock()

	m.vipUserCaches = make(map[string]*ResourcePoolCache)

	for _, kv := range resp.Kvs {
		// 从etcd key中提取clusterID
		clusterID := strings.TrimPrefix(string(kv.Key), keyPrefix)

		var k8sInfo api.K8sInfo
		if err := json.Unmarshal(kv.Value, &k8sInfo); err != nil {
			m.logger.Errorf("Failed to unmarshal config for cluster %s: %v", clusterID, err)
			continue
		}

		// 从NodeConfig.ClusterLabels中提取vipUser
		vipUser := ""
		if k8sInfo.NodeConfig.ClusterLabels != nil {
			vipUser = k8sInfo.NodeConfig.ClusterLabels[api.LabelVipUser]
		}

		// 合并到vipUser缓存中
		m.mergeClusterToVipUserCache(vipUser, &k8sInfo)
	}

	return nil
}

// mergeClusterToVipUserCache 将集群配置合并到vipUser缓存中
func (m *Manager) mergeClusterToVipUserCache(vipUser string, k8sInfo *api.K8sInfo) {
	// 标准化vipUser：空值表示公共集群，使用"common"
	if vipUser == "" {
		vipUser = api.DefaultVipUser
	}

	// 获取或创建vipUser缓存
	cache, exists := m.vipUserCaches[vipUser]
	if !exists {
		// 创建新的vipUser缓存
		cache = &ResourcePoolCache{
			vipUserLabel:       vipUser,
			enabled:            k8sInfo.ResourcePoolConfig != nil && k8sInfo.ResourcePoolConfig.SchedulingEnabled,
			defaultPoolInfo:    k8sInfo.ResourcePoolInfo,
			extraResourcePools: make(map[string]*api.ResourcePool),
			userPoolMappings:   make(map[string]string),
			lastUpdateTime:     time.Now(),
		}
		m.vipUserCaches[vipUser] = cache
		m.logger.V(6).Infof("Created new vipUser cache for '%s'", vipUser)
	} else {
		// 合并到现有缓存
		m.logger.V(6).Infof("Merged cluster into vipUser '%s'", vipUser)

		// 如果当前集群启用了ResourcePool调度，则整体启用
		if k8sInfo.ResourcePoolConfig != nil && k8sInfo.ResourcePoolConfig.SchedulingEnabled {
			cache.enabled = true
		}

		// 如果当前缓存没有defaultPoolInfo，使用当前集群的
		if cache.defaultPoolInfo == nil && k8sInfo.ResourcePoolInfo != nil {
			cache.defaultPoolInfo = k8sInfo.ResourcePoolInfo
		}

		cache.lastUpdateTime = time.Now()
	}

	// 合并extraResourcePools（同名去重，不同名合并）
	if k8sInfo.ResourcePoolConfig != nil {
		for name, pool := range k8sInfo.ResourcePoolConfig.ExtraResourcePools {
			if _, exists := cache.extraResourcePools[name]; exists {
				// 同名ResourcePool，保留第一个，记录日志
				m.logger.V(6).Infof("ResourcePool '%s' already exists in vipUser '%s', keeping existing one",
					name, vipUser)
			} else {
				// 不同名ResourcePool，直接添加
				cache.extraResourcePools[name] = pool
				m.logger.V(6).Infof("Added ResourcePool '%s' to vipUser '%s'",
					name, vipUser)
			}
		}

		// 合并用户池映射关系
		for userID, poolName := range k8sInfo.ResourcePoolConfig.UserPoolMappings {
			if _, exists := cache.userPoolMappings[userID]; !exists {
				cache.userPoolMappings[userID] = poolName
				m.logger.V(6).Infof("Added user pool mapping: %s -> %s for vipUser '%s'", userID, poolName, vipUser)
			}
		}
	}

	m.logger.V(6).Infof("Merged cluster into vipUser '%s': enabled=%v, extraPools=%d",
		vipUser, cache.enabled, len(cache.extraResourcePools))
}

// watchAllClustersResourcePoolConfig watches all clusters' etcd configuration changes (referring to cron module's watchAllConfigs implementation)
func (m *Manager) watchAllClustersResourcePoolConfig(etcdClient *etcd.Client, etcdPrefix string) {
	keyPrefix := fmt.Sprintf("%s/k8s/", etcdPrefix)
	watcher, watchChan := etcdClient.WatchPrefix(keyPrefix, 0)
	defer watcher.Close()

	for {
		select {
		case <-m.stopChan:
			return
		case watchResp := <-watchChan:
			for _, event := range watchResp.Events {
				clusterID := strings.TrimPrefix(string(event.Kv.Key), keyPrefix)

				switch event.Type {
				case clientv3.EventTypePut:
					m.logger.Infof("ResourcePool config changed for cluster %s, refreshing...", clusterID)
					if err := m.handleClusterConfigUpdate(clusterID, event.Kv.Value); err != nil {
						m.logger.Errorf("Failed to handle config update for cluster %s: %v", clusterID, err)
					}

				case clientv3.EventTypeDelete:
					m.logger.Infof("ResourcePool config deleted for cluster %s, handling removal...", clusterID)
					if err := m.handleClusterConfigDelete(clusterID); err != nil {
						m.logger.Errorf("Failed to handle config deletion for cluster %s: %v", clusterID, err)
					}
				}
			}
		}
	}
}

// handleClusterConfigUpdate handles single cluster configuration update
func (m *Manager) handleClusterConfigUpdate(clusterID string, configData []byte) error {
	var k8sInfo api.K8sInfo
	if err := json.Unmarshal(configData, &k8sInfo); err != nil {
		return err
	}

	// 检查ResourcePool调度启用状态变更
	enabled := k8sInfo.ResourcePoolConfig != nil && k8sInfo.ResourcePoolConfig.SchedulingEnabled

	// 从NodeConfig.ClusterLabels中提取vipUser
	vipUser := ""
	if k8sInfo.NodeConfig.ClusterLabels != nil {
		vipUser = k8sInfo.NodeConfig.ClusterLabels[api.LabelVipUser]
	}

	m.logger.Infof("ResourcePool config changed for cluster %s (vipUser: %s, enabled: %v), triggering full reload",
		clusterID, vipUser, enabled)

	// 配置变更时触发全量重新加载，确保立即生效
	go m.triggerFullReload()

	return nil
}

// handleClusterConfigDelete handles single cluster configuration deletion
func (m *Manager) handleClusterConfigDelete(clusterID string) error {
	// 集群配置删除，触发全量重新加载来确保数据一致性
	m.logger.Infof("Cluster %s deleted, triggering full reload to maintain consistency", clusterID)
	go m.triggerFullReload()

	return nil
}

// triggerFullReload 触发全量重新加载（异步执行）
func (m *Manager) triggerFullReload() {
	// 防止频繁重新加载（最少间隔1分钟）
	if time.Since(m.lastReload) < time.Minute {
		m.logger.V(6).Infof("Skipping full reload, last reload was %v ago", time.Since(m.lastReload))
		return
	}

	m.performFullReload()
}

// periodicFullReload 定期全量重新加载
func (m *Manager) periodicFullReload() {
	defer func() {
		if m.reloadTicker != nil {
			m.reloadTicker.Stop()
		}
	}()

	for {
		select {
		case <-m.reloadTicker.C:
			m.performFullReload()
		case <-m.stopChan:
			return
		}
	}
}

// performFullReload 执行全量重新加载
func (m *Manager) performFullReload() {
	if m.stopped {
		return
	}

	m.logger.Infof("Starting full reload of ResourcePool cache")

	// 检查etcd客户端
	if m.etcdClient == nil {
		m.logger.Errorf("etcd client not available for full reload")
		return
	}

	// 创建新的临时缓存
	tempCaches := make(map[string]*ResourcePoolCache)

	// 重新加载所有集群配置到临时缓存
	if err := m.loadAllClustersToCache(m.etcdClient, m.etcdPrefix, tempCaches); err != nil {
		m.logger.Errorf("Failed to perform full reload: %v", err)
		return
	}

	// 原子性替换缓存
	m.mu.Lock()
	m.vipUserCaches = tempCaches
	m.lastReload = time.Now()
	m.mu.Unlock()

	m.logger.Infof("Full reload completed, loaded %d vipUser caches", len(tempCaches))
}

// loadAllClustersToCache 加载所有集群配置到指定缓存
func (m *Manager) loadAllClustersToCache(etcdClient *etcd.Client, etcdPrefix string, targetCaches map[string]*ResourcePoolCache) error {
	// Use the same prefix construction method as cron module
	// etcdPrefix comes from config parameters, default is "kun1.0"
	prefix := fmt.Sprintf("%s/k8s/", etcdPrefix)

	// Get all cluster configurations from etcd (referring to cron module's GetAllK8s implementation)
	resp, err := etcdClient.Get(prefix, clientv3.WithPrefix())
	if err != nil {
		return fmt.Errorf("failed to get cluster configs from etcd: %v", err)
	}

	m.logger.V(6).Infof("Found %d cluster configurations in etcd with prefix: %s", len(resp.Kvs), prefix)

	// Process each cluster configuration
	for _, kv := range resp.Kvs {
		// Extract clusterID from key (format: "kun1.0/k8s/{clusterID}")
		clusterID := strings.TrimPrefix(string(kv.Key), prefix)
		if clusterID == "" {
			continue
		}

		// Parse cluster configuration
		var k8sInfo api.K8sInfo
		if err := json.Unmarshal(kv.Value, &k8sInfo); err != nil {
			m.logger.Errorf("Failed to unmarshal cluster config for %s: %v", clusterID, err)
			continue
		}

		// Extract vipUser from cluster labels
		vipUser := ""
		if k8sInfo.NodeConfig.ClusterLabels != nil {
			vipUser = k8sInfo.NodeConfig.ClusterLabels[api.LabelVipUser]
		}

		// Merge cluster configuration to target cache
		m.mergeClusterToTargetCache(vipUser, &k8sInfo, targetCaches)
	}

	return nil
}

// mergeClusterToTargetCache 将集群配置合并到目标缓存（用于全量重新加载）
func (m *Manager) mergeClusterToTargetCache(vipUser string, k8sInfo *api.K8sInfo, targetCaches map[string]*ResourcePoolCache) {
	// 标准化vipUser：空值表示公共集群，使用"common"
	if vipUser == "" {
		vipUser = "common"
	}

	// 获取或创建vipUser缓存
	cache, exists := targetCaches[vipUser]
	if !exists {
		// 创建新的vipUser缓存
		cache = &ResourcePoolCache{
			vipUserLabel:       vipUser,
			enabled:            k8sInfo.ResourcePoolConfig != nil && k8sInfo.ResourcePoolConfig.SchedulingEnabled,
			defaultPoolInfo:    k8sInfo.ResourcePoolInfo,
			extraResourcePools: make(map[string]*api.ResourcePool),
			userPoolMappings:   make(map[string]string),
			lastUpdateTime:     time.Now(),
		}
		targetCaches[vipUser] = cache
	} else {
		// 合并到现有缓存

		// 如果当前集群启用了ResourcePool调度，则整体启用
		if k8sInfo.ResourcePoolConfig != nil && k8sInfo.ResourcePoolConfig.SchedulingEnabled {
			cache.enabled = true
		}

		// 如果当前缓存没有defaultPoolInfo，使用当前集群的
		if cache.defaultPoolInfo == nil && k8sInfo.ResourcePoolInfo != nil {
			cache.defaultPoolInfo = k8sInfo.ResourcePoolInfo
		}

		cache.lastUpdateTime = time.Now()
	}

	// 🔑 核心合并逻辑：同名去重，不同名合并
	if k8sInfo.ResourcePoolConfig != nil {
		for name, pool := range k8sInfo.ResourcePoolConfig.ExtraResourcePools {
			if existingPool, exists := cache.extraResourcePools[name]; exists {
				// 同名ResourcePool，保留第一个，记录日志
				m.logger.V(6).Infof("ResourcePool '%s' already exists in vipUser '%s', keeping existing one",
					name, vipUser)
				_ = existingPool // 避免未使用变量警告
			} else {
				// 不同名ResourcePool，直接添加
				cache.extraResourcePools[name] = pool
				m.logger.V(6).Infof("Added ResourcePool '%s' to vipUser '%s'", name, vipUser)
			}
		}

		// 合并用户池映射关系
		for userID, poolName := range k8sInfo.ResourcePoolConfig.UserPoolMappings {
			if _, exists := cache.userPoolMappings[userID]; !exists {
				cache.userPoolMappings[userID] = poolName
				m.logger.V(6).Infof("Added user pool mapping: %s -> %s for vipUser '%s'", userID, poolName, vipUser)
			}
		}
	}
}

// Stop stops ResourcePool manager
func (m *Manager) Stop() error {
	m.mu.Lock()
	defer m.mu.Unlock()

	if m.stopped {
		return nil
	}

	close(m.stopChan)
	m.stopped = true

	// 停止定期重新加载定时器
	if m.reloadTicker != nil {
		m.reloadTicker.Stop()
		m.reloadTicker = nil
	}

	m.logger.Infof("ResourcePool Manager stopped successfully")
	return nil
}

// GetResourcePoolCacheStatus returns cache status for monitoring and debugging (supports multiple clusters)
func (m *Manager) GetResourcePoolCacheStatus() map[string]interface{} {
	m.mu.RLock()
	defer m.mu.RUnlock()

	if m.vipUserCaches == nil {
		return map[string]interface{}{
			"initialized": false,
		}
	}

	vipUserStatus := make(map[string]interface{})
	for vipUser, cache := range m.vipUserCaches {
		vipUserStatus[vipUser] = map[string]interface{}{
			"enabled":               cache.enabled,
			"extraPoolsCount":       len(cache.extraResourcePools),
			"lastUpdateTime":        cache.lastUpdateTime,
			"defaultPoolConfigured": cache.defaultPoolInfo != nil,
		}
	}

	return map[string]interface{}{
		"initialized":   true,
		"vipUsersCount": len(m.vipUserCaches),
		"lastReload":    m.lastReload,
		"vipUsers":      vipUserStatus,
	}
}

// GetVipUserResourcePoolCacheStatus returns cache status for specified vipUser
func (m *Manager) GetVipUserResourcePoolCacheStatus(vipUser string) map[string]interface{} {
	m.mu.RLock()
	defer m.mu.RUnlock()

	// 标准化vipUser：空值表示公共集群，使用"common"
	if vipUser == "" {
		vipUser = "common"
	}

	// 查找vipUser缓存
	cache, exists := m.vipUserCaches[vipUser]
	if !exists {
		return map[string]interface{}{
			"exists": false,
		}
	}

	return map[string]interface{}{
		"exists":                true,
		"vipUser":               vipUser,
		"enabled":               cache.enabled,
		"extraPoolsCount":       len(cache.extraResourcePools),
		"lastUpdateTime":        cache.lastUpdateTime,
		"defaultPoolConfigured": cache.defaultPoolInfo != nil,
		"extraPools":            m.getExtraPoolNames(cache),
	}
}

// getExtraPoolNames gets list of extra resource pool names
func (m *Manager) getExtraPoolNames(cache *ResourcePoolCache) []string {
	names := make([]string, 0, len(cache.extraResourcePools))
	for name := range cache.extraResourcePools {
		names = append(names, name)
	}
	return names
}
