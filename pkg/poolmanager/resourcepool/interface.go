// Package resourcepool provides ResourcePool management functionality
package resourcepool

import (
	"icode.baidu.com/baidu/faas/kun/pkg/api"
	"icode.baidu.com/baidu/faas/kun/pkg/etcd"
)

// ManagerInterface defines the core interface for ResourcePool manager
type ManagerInterface interface {
	// InitResourcePoolCache initializes ResourcePool cache with etcd configuration
	InitResourcePoolCache(etcdClient *etcd.Client, etcdPrefix string) error

	// EnhanceLabelsWithResourcePool enhances labels with ResourcePool information
	EnhanceLabelsWithResourcePool(labels api.RequestLabels, runtime string, vipUser string)

	// GetResourcePoolCacheStatus returns cache status for monitoring and debugging
	GetResourcePoolCacheStatus() map[string]interface{}

	// GetVipUserResourcePoolCacheStatus returns cache status for specific vipUser
	GetVipUserResourcePoolCacheStatus(vipUser string) map[string]interface{}

	// Stop stops the ResourcePool manager and cleans up resources
	Stop() error
}
