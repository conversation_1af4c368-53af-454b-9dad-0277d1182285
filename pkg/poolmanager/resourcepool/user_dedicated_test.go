package resourcepool

import (
	"testing"

	"icode.baidu.com/baidu/faas/kun/pkg/api"
	"icode.baidu.com/baidu/faas/kun/pkg/util/logs"
)

func TestUserDedicatedPoolSelection(t *testing.T) {
	logger := logs.NewLogger()
	manager := &Manager{
		logger:        logger,
		vipUserCaches: make(map[string]*ResourcePoolCache),
	}

	// 创建测试配置
	testConfig := &ResourcePoolCache{
		vipUserLabel: "common",
		enabled:      true,
		userPoolMappings: map[string]string{
			"premium-user-001": "gpu-pool",
			"vip-user-002":     "high-memory-pool",
		},
		extraResourcePools: map[string]*api.ResourcePool{
			"gpu-pool": &api.ResourcePool{
				Enabled:        true,
				PoolType:       api.ResourcePoolTypeDedicated,
				DedicatedUsers: []string{"premium-user-001"},
				ResourcePoolInfo: api.ResourcePoolInfo{
					Description:     "GPU专属池",
					SupportRuntimes: []string{"tensorflow", "pytorch"},
				},
			},
			"high-memory-pool": &api.ResourcePool{
				Enabled:        true,
				PoolType:       api.ResourcePoolTypeHybrid,
				DedicatedUsers: []string{"premium-user-001", "vip-user-002"},
				ResourcePoolInfo: api.ResourcePoolInfo{
					Description:     "高内存混合池",
					SupportRuntimes: []string{"java17", "python3.10"},
				},
			},
			"ubuntu2204-pool": &api.ResourcePool{
				Enabled:  true,
				PoolType: api.ResourcePoolTypeShared,
				ResourcePoolInfo: api.ResourcePoolInfo{
					Description:     "共享池",
					SupportRuntimes: []string{"java17", "python3.10", "nodejs18", "tensorflow"},
				},
			},
		},
	}

	tests := []struct {
		name         string
		userID       string
		runtime      string
		expectedPool string
		description  string
	}{
		{
			name:         "Premium用户访问GPU专属池",
			userID:       "premium-user-001",
			runtime:      "tensorflow",
			expectedPool: "gpu-pool",
			description:  "Premium用户应该优先使用GPU专属池",
		},

		{
			name:         "VIP用户访问高内存池",
			userID:       "vip-user-002",
			runtime:      "python3.10",
			expectedPool: "high-memory-pool",
			description:  "VIP用户应该能访问高内存混合池",
		},
		{
			name:         "普通用户无专属池映射",
			userID:       "normal-user",
			runtime:      "nodejs18",
			expectedPool: "ubuntu2204-pool",
			description:  "普通用户无专属池映射，应使用通用选择逻辑",
		},
		{
			name:         "Premium用户运行时不支持时降级",
			userID:       "premium-user-001",
			runtime:      "unsupported-runtime",
			expectedPool: "default",
			description:  "当专属池不支持运行时时应降级到默认池",
		},
		{
			name:         "空用户ID使用通用逻辑",
			userID:       "",
			runtime:      "java17",
			expectedPool: "high-memory-pool",
			description:  "空用户ID应使用通用资源池选择逻辑",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := manager.selectResourcePoolByRuntimeAndUser(tt.runtime, tt.userID, testConfig)
			if result != tt.expectedPool {
				t.Errorf("Test '%s' failed: expected '%s', got '%s'. %s",
					tt.name, tt.expectedPool, result, tt.description)
			} else {
				t.Logf("✅ Test '%s' passed: %s -> %s. %s",
					tt.name, tt.runtime, result, tt.description)
			}
		})
	}
}

func TestUserPoolAccessControl(t *testing.T) {
	logger := logs.NewLogger()
	manager := &Manager{
		logger: logger,
	}

	// 测试池子配置
	testPools := map[string]*api.ResourcePool{
		"shared-pool": &api.ResourcePool{
			PoolType: api.ResourcePoolTypeShared,
		},
		"dedicated-pool": &api.ResourcePool{
			PoolType:       api.ResourcePoolTypeDedicated,
			DedicatedUsers: []string{"user1", "user2"},
		},
		"hybrid-pool": &api.ResourcePool{
			PoolType:       api.ResourcePoolTypeHybrid,
			DedicatedUsers: []string{"user1"},
		},
	}

	tests := []struct {
		name        string
		userID      string
		poolName    string
		pool        *api.ResourcePool
		expected    bool
		description string
	}{
		{
			name:        "共享池允许所有用户",
			userID:      "any-user",
			poolName:    "shared-pool",
			pool:        testPools["shared-pool"],
			expected:    true,
			description: "共享池应该允许任何用户访问",
		},
		{
			name:        "专属池允许指定用户",
			userID:      "user1",
			poolName:    "dedicated-pool",
			pool:        testPools["dedicated-pool"],
			expected:    true,
			description: "专属池应该允许指定用户访问",
		},
		{
			name:        "专属池拒绝非指定用户",
			userID:      "user3",
			poolName:    "dedicated-pool",
			pool:        testPools["dedicated-pool"],
			expected:    false,
			description: "专属池应该拒绝非指定用户访问",
		},
		{
			name:        "混合池允许所有用户",
			userID:      "any-user",
			poolName:    "hybrid-pool",
			pool:        testPools["hybrid-pool"],
			expected:    true,
			description: "混合池应该允许所有用户访问",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := manager.isUserAllowedInPool(tt.userID, tt.poolName, *tt.pool)
			if result != tt.expected {
				t.Errorf("Test '%s' failed: expected %v, got %v. %s",
					tt.name, tt.expected, result, tt.description)
			} else {
				t.Logf("✅ Test '%s' passed: user '%s' access to '%s' = %v. %s",
					tt.name, tt.userID, tt.poolName, result, tt.description)
			}
		})
	}
}
