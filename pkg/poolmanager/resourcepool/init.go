package resourcepool

import (
	"fmt"

	"icode.baidu.com/baidu/faas/kun/pkg/etcd"
	"icode.baidu.com/baidu/faas/kun/pkg/util/logs"
)

// InitResourcePoolManager initializes ResourcePool manager
func InitResourcePoolManager(etcdClient *etcd.Client, etcdPrefix string, logger *logs.Logger) (ManagerInterface, error) {
	if etcdClient == nil || logger == nil {
		return nil, fmt.Errorf("etcd client or logger is nil")
	}
	// 1. Create ResourcePool manager instance
	manager := newManager(logger)

	// 2. Initialize ResourcePool cache (pass etcdPrefix config parameter)
	if err := manager.InitResourcePoolCache(etcdClient, etcdPrefix); err != nil {
		return nil, fmt.Errorf("failed to initialize ResourcePool Manager: %v", err)
	}

	logger.Infof("ResourcePool Manager initialized successfully with etcdPrefix: %s", etcdPrefix)
	return manager, nil
}

// newManager creates a new ResourcePool manager instance
// 只通过InitResourcePoolManager方法暴露new
func newManager(logger *logs.Logger) ManagerInterface {
	return &Manager{
		logger:        logger,
		vipUserCaches: make(map[string]*ResourcePoolCache),
	}
}

// CleanupResourcePoolManager cleans up ResourcePool manager
func CleanupResourcePoolManager(manager ManagerInterface) {
	if manager != nil {
		manager.Stop()
	}
}
