package handler

import (
	"fmt"
	"io/ioutil"
	"time"

	"icode.baidu.com/baidu/faas/kun/pkg/api"
	kunapi "icode.baidu.com/baidu/faas/kun/pkg/api"
	kunErr "icode.baidu.com/baidu/faas/kun/pkg/error"
	"icode.baidu.com/baidu/faas/kun/pkg/poolmanager/dispatcher/ctx"
	"icode.baidu.com/baidu/faas/kun/pkg/server"
	"icode.baidu.com/baidu/faas/kun/pkg/util/json"
)

// GetFreePodHandler implement function
func GetFreePodHandler(c *server.Context) {
	getPodBegin := time.Now()
	w := c.Response()
	var req api.GetFreePodRequest
	body, _ := ioutil.ReadAll(c.HTTPRequest().Body)
	if err := json.Unmarshal(body, &req); err != nil {
		c.WithWarnLog(kunErr.NewInvalidRequestContentException(err.Error(), err)).WriteTo(w)
		return
	}

	if !req.Valid() {
		c.WithWarnLog(kunErr.NewInvalidParameterValueException("request param invalid", nil)).WriteTo(w)
		return
	}

	pod, err := GetFreePod(c, &req)
	if err != nil {
		c.WithErrorLog(kunErr.NewServiceException(fmt.Sprintf("[get pod failed] %s", err.Error()), err)).WriteTo(w)
		return
	}

	if req.WithPodTrace {
		pod.PodTrace.DurationMap.Add(api.PodPeriodPMGetPod, getPodBegin)
	}
	res, _ := json.Marshal(api.GetFreePodResponse{
		Pod:   &pod.PodInfo,
		Trace: &pod.PodTrace,
	})
	c.Logger().V(2).Infof("GetFreePod success, pod=%v", pod)
	w.Write(res)
}

func GetFreePod(c *server.Context, req *api.GetFreePodRequest) (*api.PodPacket, error) {
	// 用户是否独占node
	userIsolationNode := req.Labels.Get(kunapi.LabelNodeIsolationUser)
	vpcCfgHash := req.Labels.Get(kunapi.LabelVpcConfigID)
	// 普通用户共享node
	originUserID := req.User.ID
	if userIsolationNode != kunapi.LabelNodeIsolationUser && vpcCfgHash == "" {
		// 如果1、不隔离用户 2、未设置vpc配置，就强行替换
		req.User.ID = "dummyUSER"
		defer func() {
			req.User.ID = originUserID
		}()
		req.Labels.Set(api.LabelUserID, "dummyUSER")
	}
	//label中删除独占node的标注
	req.Labels.Delete(kunapi.LabelNodeIsolationUser)

	// ResourcePool标签增强逻辑
	if resourcePoolManager != nil {
		// 从Runtime配置中获取runtime信息
		if req.Runtime != nil && req.Runtime.Name != "" {
			// 从labels中获取vipUser信息
			vipUser := req.Labels.Get(api.LabelVipUser)
			// 公共集群vipUser为空字符串，resourcePoolManager会处理这种情况
			// 使用ResourcePool管理器为标签增加ResourcePool信息
			resourcePoolManager.EnhanceLabelsWithResourcePool(req.Labels, req.Runtime.Name, vipUser)
			c.Logger().V(6).Infof("Enhanced labels with ResourcePool for runtime: %s, vipUser: %s", req.Runtime.Name, vipUser)

		}
	}

	getPodContext := &ctx.GetPodContext{
		User:             req.User,
		Runtime:          req.Runtime,
		Func:             req.GetFunctionOutput,
		ServiceType:      req.ServiceType,
		Labels:           req.Labels,
		Logger:           c.Logger(),
		RequestId:        c.RequestID(),
		WithPodTrace:     req.WithPodTrace,
		Context:          c.Context(),
		RequestExpiredAt: c.Request().HeaderParameter(api.HeaderRequestExpiredAt),
		ReservedType:     req.ReservedType,
		MCPSessionID:     req.MCPSessionID,
	}

	// 开启预留实例
	if api.ReservedInstanceEnabled {
		getPodContext.ReservedId = req.ReservedId
		getPodContext.ReservedUserId = originUserID
	}
	return dispatcherImpl.GetFreePod(getPodContext)
}
