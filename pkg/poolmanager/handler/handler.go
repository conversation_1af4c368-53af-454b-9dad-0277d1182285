package handler

import (
	"fmt"

	"icode.baidu.com/baidu/faas/kun/pkg/poolmanager/resourcepool"

	"icode.baidu.com/baidu/faas/kun/pkg/api"

	"github.com/emicklei/go-restful"

	"icode.baidu.com/baidu/faas/kun/cmd/poolmanager/app/options"
	"icode.baidu.com/baidu/faas/kun/pkg/bce/iam"
	"icode.baidu.com/baidu/faas/kun/pkg/cron/impl"
	"icode.baidu.com/baidu/faas/kun/pkg/etcd"
	funclet "icode.baidu.com/baidu/faas/kun/pkg/funclet/client"
	"icode.baidu.com/baidu/faas/kun/pkg/middleware"
	"icode.baidu.com/baidu/faas/kun/pkg/nodefsm"
	"icode.baidu.com/baidu/faas/kun/pkg/nodefsm/schedule-events"
	"icode.baidu.com/baidu/faas/kun/pkg/poolmanager/dispatcher"
	"icode.baidu.com/baidu/faas/kun/pkg/poolmanager/reserve_ctrl"
	proxy "icode.baidu.com/baidu/faas/kun/pkg/proxyagent/client"
	"icode.baidu.com/baidu/faas/kun/pkg/rpc/redis"
	"icode.baidu.com/baidu/faas/kun/pkg/server"
	"icode.baidu.com/baidu/faas/kun/pkg/server/endpoint"
	"icode.baidu.com/baidu/faas/kun/pkg/store/reserve"
	"icode.baidu.com/baidu/faas/kun/pkg/util/logs"
)

var dispatcherImpl dispatcher.PodDispatcherInterface
var userIsolation bool
var etcdClient *etcd.Client
var iamClient iam.ClientInterface

// 资源池manager，poolmanager服务启动时初始化，最顶层
var resourcePoolManager resourcepool.ManagerInterface

// Init initialize poolmanager pkg
func Init(runOptions *options.PoolManagerOptions) {
	userIsolation = runOptions.UserIsolation
	api.ReservedInstanceEnabled = runOptions.ReservedInstanceEnabled

	switch runOptions.RunningMode {
	case options.RunningModeCloud, options.RunningModeOTE:
		dispatcherImpl = InitFaaS(runOptions)
	default:
		logs.Fatalf("not supported running mode '%s'", runOptions.RunningMode)
	}
}

// InitFaaS 初始化FaaS中心运行模式
func InitFaaS(runOptions *options.PoolManagerOptions) dispatcher.PodDispatcherInterface {
	etcdClient = initEtcd(runOptions)
	funcletClient := funclet.NewFuncletClient(runOptions.FuncletOptions)

	redisClusterClient := redis.NewClient(runOptions.GenericRedisOptions)
	reserveStore := reserve.NewManager(redisClusterClient, runOptions.ReserveOptions)
	proxyClient := proxy.NewProxyAgentClient(runOptions.ProxyAgentPort)

	// 初始化状态机，当前会注册acquire、outdate、offline事件
	logger := logs.NewLogger()
	scheduleNodeControl := impl.NewScheduleNodeControlByClients(etcdClient,
		funcletClient, logger, reserveStore, nil, proxyClient)

	events := schedule.CreateCloudPMEvents(scheduleNodeControl)
	nodeFSM := nodefsm.NewFSM(etcdClient, logger)
	nodeFSM.AddEvent(events...)

	reserveCtrl := reserve_ctrl.NewReserveController(reserveStore, funcletClient, etcdClient, nodeFSM, proxyClient, runOptions.GreNetworkCidr)
	// 顺便初始化全局的resourcePoolManager
	var err error
	resourcePoolManager, err = resourcepool.InitResourcePoolManager(etcdClient, runOptions.GenericEtcdOptions.Prefix, logger)
	if err != nil {
		logs.Fatalf("init resource pool manager failed: %v", err)
	}

	return dispatcher.NewFaasDispatcher(reserveCtrl)
}

// InstallAPI xxx
func InstallAPI(container *restful.Container) {
	apis := []endpoint.ApiSingle{
		{
			Verb:    "GET",
			Path:    "hello",
			Handler: server.WrapRestRouteFunc(HelloHandler),
			Filters: []restful.FilterFunction{
				middleware.SimpleAuthTokenVerify(),
			},
		},
		{
			Verb:    "POST",
			Path:    "GetFreePod",
			Handler: server.WrapRestRouteFuncWithTrace("handler/getFreePod", GetFreePodHandler),
			Filters: []restful.FilterFunction{
				middleware.SimpleAuthTokenVerify(),
			},
		},
		{
			Verb:    "POST",
			Path:    "PutFreePod",
			Handler: server.WrapRestRouteFuncWithTrace("handler/putFreePod", PutFreePodHandler),
			Filters: []restful.FilterFunction{
				middleware.SimpleAuthTokenVerify(),
			},
		},
		{
			Verb:    "POST",
			Path:    "PutSickPod",
			Handler: server.WrapRestRouteFuncWithTrace("handler/putSickPod", PutSickPodHandler),
			Filters: []restful.FilterFunction{
				middleware.SimpleAuthTokenVerify(),
			},
		},
		{
			Verb:    "POST",
			Path:    "PutDubiousPod",
			Handler: server.WrapRestRouteFuncWithTrace("handler/putDubiousPod", PutDubiousPodHandler),
			Filters: []restful.FilterFunction{
				middleware.SimpleAuthTokenVerify(),
			},
		},
		{
			Verb:    "DELETE",
			Path:    "TimeoutPod",
			Handler: server.WrapRestRouteFuncWithTrace("handler/timeoutPod", RecycleTimeoutPodHandler),
			Filters: []restful.FilterFunction{
				middleware.SimpleAuthTokenVerify(),
			},
		},
		{
			Verb:    "POST",
			Path:    "PutMCPSessionPod",
			Handler: server.WrapRestRouteFuncWithTrace("handler/putMCPSessionPod", PutMCPSessionPodHandler),
			Filters: []restful.FilterFunction{
				middleware.SimpleAuthTokenVerify(),
			},
		},
	}

	apiversion := []endpoint.ApiVersion{
		{
			Prefix: "/v1",
			Group:  apis,
		},
	}
	endpoint.NewApiInstaller(apiversion).Install(container)
}

// HelloHandler implement hello function
func HelloHandler(c *server.Context) {
	c.Logger().V(9).Info("Hello world")
	fmt.Fprint(c.Response(), "Hello world")
}

func initEtcd(opt *options.PoolManagerOptions) *etcd.Client {
	etcdOptions := opt.GenericEtcdOptions
	etcdClient := etcd.NewClient()
	err := etcdClient.StartClient(etcdOptions)
	if err != nil {
		logs.Fatalf("start etcd client error: %v", err)
	}

	err = etcdClient.SyncNodes()
	if err != nil {
		logs.Fatalf("sync etcd node error: %v", err)
	}
	return etcdClient
}
