package handler

import (
	"testing"

	"icode.baidu.com/baidu/faas/kun/pkg/api"
	"icode.baidu.com/baidu/faas/kun/pkg/etcd"
)

// MockResourcePoolManager implements ResourcePool manager interface for testing
type MockResourcePoolManager struct {
	enhanceCalled bool
	lastRuntime   string
	lastClusterID string
}

func (m *MockResourcePoolManager) InitResourcePoolCache(etcdClient *etcd.Client, etcdPrefix string) error {
	return nil
}

func (m *MockResourcePoolManager) EnhanceLabelsWithResourcePool(labels api.RequestLabels, runtime string, vipUser string) {
	m.enhanceCalled = true
	m.lastRuntime = runtime
	m.lastClusterID = vipUser

	// Simulate adding ResourcePool label for java17
	if runtime == "java17" {
		labels.Set(api.LabelResourcePool, "ubuntu2204-pool")
	}
}

func (m *MockResourcePoolManager) GetResourcePoolCacheStatus() map[string]interface{} {
	return map[string]interface{}{
		"initialized": true,
	}
}

func (m *MockResourcePoolManager) GetVipUserResourcePoolCacheStatus(vipUser string) map[string]interface{} {
	return map[string]interface{}{
		"exists": true,
	}
}

func (m *MockResourcePoolManager) Stop() error {
	return nil
}

func TestResourcePoolIntegrationInGetFreePod(t *testing.T) {
	// Setup mock ResourcePool manager
	mockManager := &MockResourcePoolManager{}
	originalManager := resourcePoolManager
	resourcePoolManager = mockManager
	defer func() {
		resourcePoolManager = originalManager
	}()

	// Test case 1: ResourcePool manager is set and should enhance labels
	labels := make(api.RequestLabels)
	labels.Set(api.LabelVipUser, "common")

	req := &api.GetFreePodRequest{
		Runtime: &api.RuntimeConfiguration{
			Name: "java17",
		},
		Labels: labels,
	}

	// Create a mock context (we can't easily test the full GetFreePod function without complex setup)
	// So we'll test the ResourcePool logic directly

	// Simulate the ResourcePool enhancement logic from GetFreePod
	if resourcePoolManager != nil {
		if req.Runtime != nil && req.Runtime.Name != "" {
			if clusterID := req.Labels.Get(api.LabelVipUser); clusterID != "" {
				resourcePoolManager.EnhanceLabelsWithResourcePool(req.Labels, req.Runtime.Name, clusterID)
			}
		}
	}

	// Verify the enhancement was called
	if !mockManager.enhanceCalled {
		t.Error("Expected ResourcePool enhancement to be called")
	}

	if mockManager.lastRuntime != "java17" {
		t.Errorf("Expected runtime 'java17', got '%s'", mockManager.lastRuntime)
	}

	if mockManager.lastClusterID != "common" {
		t.Errorf("Expected vipUser 'common', got '%s'", mockManager.lastClusterID)
	}

	// Verify the label was added
	resourcePool := req.Labels.Get(api.LabelResourcePool)
	if resourcePool != "ubuntu2204-pool" {
		t.Errorf("Expected ResourcePool label 'ubuntu2204-pool', got '%s'", resourcePool)
	}
}

func TestResourcePoolIntegrationDisabled(t *testing.T) {
	// Test case: ResourcePool manager is nil (disabled)
	originalManager := resourcePoolManager
	resourcePoolManager = nil
	defer func() {
		resourcePoolManager = originalManager
	}()

	labels := make(api.RequestLabels)
	labels.Set(api.LabelVipUser, "common")

	req := &api.GetFreePodRequest{
		Runtime: &api.RuntimeConfiguration{
			Name: "java17",
		},
		Labels: labels,
	}

	// Simulate the ResourcePool enhancement logic from GetFreePod
	if resourcePoolManager != nil {
		if req.Runtime != nil && req.Runtime.Name != "" {
			if clusterID := req.Labels.Get(api.LabelVipUser); clusterID != "" {
				resourcePoolManager.EnhanceLabelsWithResourcePool(req.Labels, req.Runtime.Name, clusterID)
			}
		}
	}

	// Verify no ResourcePool label was added
	resourcePool := req.Labels.Get(api.LabelResourcePool)
	if resourcePool != "" {
		t.Errorf("Expected no ResourcePool label when disabled, got '%s'", resourcePool)
	}
}

func TestResourcePoolManagerNilSafety(t *testing.T) {
	// Test that the code handles nil ResourcePool manager gracefully
	originalManager := resourcePoolManager
	resourcePoolManager = nil
	defer func() {
		resourcePoolManager = originalManager
	}()

	// This should not panic
	labels := make(api.RequestLabels)
	labels.Set(api.LabelVipUser, "common")

	req := &api.GetFreePodRequest{
		Runtime: &api.RuntimeConfiguration{
			Name: "java8",
		},
		Labels: labels,
	}

	// Simulate the ResourcePool enhancement logic from GetFreePod
	// This should not panic even when resourcePoolManager is nil
	if resourcePoolManager != nil {
		if req.Runtime != nil && req.Runtime.Name != "" {
			if clusterID := req.Labels.Get(api.LabelVipUser); clusterID != "" {
				resourcePoolManager.EnhanceLabelsWithResourcePool(req.Labels, req.Runtime.Name, clusterID)
			}
		}
	}

	// Should reach here without panic
	t.Log("Nil ResourcePool manager handled gracefully")
}
