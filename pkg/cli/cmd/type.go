package cmd

import (
	"io/ioutil"
	"os"

	"icode.baidu.com/baidu/faas/kun/pkg/api"
	"icode.baidu.com/baidu/faas/kun/pkg/util/json"
)

var (
	RuntimeMap = make(map[string]*api.RuntimeConfiguration)
)

func GetFunction(filePath string) (*api.GetFunctionOutput, error) {
	f, err := os.Open(filePath)
	if err != nil {
		return nil, err
	}
	defer f.Close()
	b, err := ioutil.ReadAll(f)
	if err != nil {
		return nil, err
	}
	output := &api.GetFunctionOutput{}
	err = json.Unmarshal(b, output)
	if err != nil {
		return nil, err
	}
	return output, nil
}

func GetInvocationInput(filePath string) (*api.InvocationInput, error) {
	f, err := os.Open(filePath)
	if err != nil {
		return nil, err
	}
	defer f.Close()
	b, err := ioutil.ReadAll(f)
	if err != nil {
		return nil, err
	}
	output := &api.InvocationInput{}
	err = json.Unmarshal(b, output)
	if err != nil {
		return nil, err
	}
	return output, nil
}

func GetRuntime(runtimeName string) *api.RuntimeConfiguration {
	if v, ok := RuntimeMap[runtimeName]; !ok {
		return nil
	} else {
		return v
	}
}

func init() {
	RuntimeMap["nodejs6"] = &api.RuntimeConfiguration{
		Name:     "nodejs6.11",
		Bin:      "/bin/bash",
		Path:     "/var/faas/runtime/node-v6.11.3-linux-x64",
		SqfsPath: "/var/faas/runtime/node-v6.11.3-linux-x64.sqfs",
		Args:     []string{"/var/runtime/bootstrap"},
	}
	RuntimeMap["nodejs8"] = &api.RuntimeConfiguration{
		Name:     "nodejs8.5",
		Bin:      "/bin/bash",
		Path:     "/var/faas/runtime/node-v8.5.0-linux-x64",
		SqfsPath: "/var/faas/runtime/node-v8.5.0-linux-x64.sqfs",
		Args:     []string{"/var/runtime/bootstrap"},
	}
	RuntimeMap["nodejs10"] = &api.RuntimeConfiguration{
		Name:     "nodejs10.15",
		Bin:      "/bin/bash",
		Path:     "/var/faas/runtime/node-v10.15.3-linux-x64",
		SqfsPath: "/var/faas/runtime/node-v10.15.3-linux-x64.sqfs",
		Args:     []string{"/var/runtime/bootstrap"},
	}
	RuntimeMap["nodejs12"] = &api.RuntimeConfiguration{
		Name:     "nodejs12.2",
		Bin:      "/bin/bash",
		Path:     "/var/faas/runtime/node-v12.2.0-linux-x64",
		SqfsPath: "/var/faas/runtime/node-v12.2.0-linux-x64.sqfs",
		Args:     []string{"/var/runtime/bootstrap"},
		KataBin:  "/bin/bash",
		KataArgs: []string{"/var/runtime/node-v12.2.0-linux-x64/bootstrap"},
	}
	RuntimeMap["python2"] = &api.RuntimeConfiguration{
		Name:     "python2",
		Bin:      "/var/runtime/bin/python",
		Path:     "/var/faas/runtime/python-v2.7.14-x86_64-linux-gnu",
		SqfsPath: "/var/faas/runtime/python-v2.7.14-x86_64-linux-gnu.sqfs",
		Args:     []string{"/var/runtime/script/kunruntime.py"},
		KataBin:  "/var/runtime/python-v2.7.14-x86_64-linux-gnu/bin/python",
		KataArgs: []string{"/var/runtime/python-v2.7.14-x86_64-linux-gnu/script/kunruntime.py"},
	}
	RuntimeMap["python3"] = &api.RuntimeConfiguration{
		Name:     "python3",
		Bin:      "/var/runtime/bin/python3",
		Path:     "/var/faas/runtime/python-v3.6.2-x86_64-linux-gnu",
		SqfsPath: "/var/faas/runtime/python-v3.6.2-x86_64-linux-gnu.sqfs",
		Args:     []string{"/var/runtime/script/kunruntime.py"},
	}
	RuntimeMap["java8"] = &api.RuntimeConfiguration{
		Name:     "java8",
		Bin:      "/bin/bash",
		Path:     "/var/faas/runtime/common",
		SqfsPath: "/var/faas/runtime/common.sqfs",
		Args:     []string{"/var/runtime/bootstrap"},
	}
	RuntimeMap["java8_stream"] = &api.RuntimeConfiguration{
		Name:     "java8_stream",
		Bin:      "/bin/bash",
		Path:     "/var/faas/runtime/java8",
		SqfsPath: "/var/faas/runtime/common.sqfs",
		Args:     []string{"/var/runtime/bootstrap"},
	}
	RuntimeMap["golang"] = &api.RuntimeConfiguration{
		Name:     "golang",
		Bin:      "/bin/bash",
		Path:     "/var/faas/runtime/common",
		SqfsPath: "/var/faas/runtime/common.sqfs",
		Args:     []string{"/var/runtime/bootstrap"},
	}
	RuntimeMap["php5"] = &api.RuntimeConfiguration{
		Name:     "php5.6",
		Bin:      "/var/runtime/bin/php",
		Path:     "/var/faas/runtime/php-v5.6.40-x86_64-linux-gnu",
		SqfsPath: "/var/faas/runtime/php-v5.6.40-x86_64-linux-gnu.sqfs",
		Args:     []string{"/var/runtime/script/php5-runtime.phar"},
	}
	RuntimeMap["php7"] = &api.RuntimeConfiguration{
		Name:     "php5.6",
		Bin:      "/var/runtime/bin/php",
		Path:     "/var/faas/runtime/php-v7.2.17-x86_64-linux-gnu",
		SqfsPath: "/var/faas/runtime/php-v7.2.17-x86_64-linux-gnu.sqfs",
		Args:     []string{"/var/runtime/script/php7-runtime.phar"},
	}
	RuntimeMap["csharp"] = &api.RuntimeConfiguration{
		Name:     "csharp",
		Bin:      "/bin/bash",
		Path:     "/var/faas/runtime/common",
		SqfsPath: "/var/faas/runtime/common.sqfs",
		Args:     []string{"/var/runtime/bootstrap"},
	}
	RuntimeMap["powershell"] = &api.RuntimeConfiguration{
		Name:     "powershell",
		Bin:      "dotnet",
		Path:     "/var/faas/runtime/powershell",
		SqfsPath: "/var/faas/runtime/powershell.sqfs",
		Args:     []string{"/var/runtime/bce-cfc-pwsh-worker.dll"},
	}
	RuntimeMap["cxx11"] = &api.RuntimeConfiguration{
		Name:     "cxx11",
		Bin:      "/bin/bash",
		Path:     "/var/faas/runtime/common",
		SqfsPath: "/var/faas/runtime/common.sqfs",
		Args:     []string{"/var/runtime/bootstrap"},
	}
}
