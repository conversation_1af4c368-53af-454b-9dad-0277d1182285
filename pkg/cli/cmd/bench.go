package cmd

import (
	"bytes"
	"fmt"
	"io"
	"math"
	"net/http"
	"net/url"
	"os"
	"os/signal"
	"runtime"
	"strconv"
	"strings"
	"time"

	"github.com/rakyll/hey/requester"
	"github.com/spf13/cobra"
	"icode.baidu.com/baidu/faas/kun/pkg/bce/auth"
	cmdutil "icode.baidu.com/baidu/faas/kun/pkg/cli/cmd/util"
)

type BenchOptions struct {
	method      string
	headers     []string
	auth        *auth.BceAuth
	url         string
	body        string
	authVersion string
}

// NewBenchCmd xxx
func NewBenchCmd(f cmdutil.Factory, out io.Writer) *cobra.Command {
	cmd := &cobra.Command{
		Use:   "bench",
		Short: "bench url",
		Long:  "bench url, auto add BCE Auth HTTP header",
		Run: func(cmd *cobra.Command, args []string) {
			options := new(BenchOptions)
			cmdutil.CheckErr(options.Complete(cmd))
			cmdutil.CheckErr(options.Run(cmd, f, out))
		},
	}
	cmd.Flags().StringP("method", "m", "GET", "HTTP method, one of GET, POST, PUT, DELETE, HEAD, OPTIONS.")
	cmd.Flags().IntP("requests", "n", 200, "Number of requests to run. Default is 200.")
	cmd.Flags().IntP("conc", "c", 50, "Number of requests to run concurrently. Total number of requests cannot")
	cmd.Flags().Float64P("qps", "q", 0, "Rate limit, in queries per second (QPS). Default is no rate limit.")
	cmd.Flags().DurationP("duration", "z", 0, `Duration of application to send requests. 
		When duration is reached, application stops and exits. If duration is specified, n is ignored. 
		Examples: -z 10s -z 3m.`)
	cmd.Flags().IntP("timeout", "t", 20, "Timeout for each request in seconds. Default is 20, use 0 for infinite.")
	cmd.Flags().IntP("cpus", "p", runtime.NumCPU(), fmt.Sprintf("Number of used cpu %d cores.", runtime.NumCPU()))
	cmd.Flags().StringSlice("headers", []string{}, "The request headers")
	cmd.Flags().StringP("body", "d", "", "The request body")

	cmd.Flags().StringP("ak", "a", "", "user ak")
	cmd.Flags().StringP("sk", "s", "", "user sk")
	cmd.Flags().StringP("auth-version", "v", "v1", "auth version")

	runtime.GOMAXPROCS(cmdutil.GetFlagInt(cmd, "cpus"))
	return cmd
}

func (o *BenchOptions) Complete(cmd *cobra.Command) error {
	o.url = cmd.Flags().Arg(0)
	o.method = cmdutil.GetFlagString(cmd, "method")
	o.headers = cmdutil.GetFlagStringSlice(cmd, "headers")
	o.body = cmdutil.GetFlagString(cmd, "body")
	ak := cmdutil.GetFlagString(cmd, "ak")
	sk := cmdutil.GetFlagString(cmd, "sk")
	authVersion := cmdutil.GetFlagString(cmd, "auth-version")
	if ak != "" && sk != "" {
		o.auth = &auth.BceAuth{
			AccessKey: ak,
			SecretKey: sk,
		}
	}
	if authVersion == "v2" {
		o.authVersion = auth.BCE_AUTH_V2
	} else {
		o.authVersion = auth.BCE_AUTH_V1
	}
	return nil
}

func (o *BenchOptions) Run(cmd *cobra.Command, f cmdutil.Factory, out io.Writer) error {
	u, err := url.ParseRequestURI(o.url)
	if err != nil {
		return err
	}
	d := []byte(o.body)
	r, _ := http.NewRequest(o.method, o.url, bytes.NewReader(d))
	if o.method != "GET" {
		r.Header.Set("Content-Length", strconv.Itoa(len(d)))
	}

	r.Header.Set("Host", u.Host)
	now := time.Now().UTC()
	utcStr := auth.GetCanonicalTime(now)
	r.Header.Set("X-Bce-Date", utcStr)
	for _, h := range o.headers {
		s := strings.Split(h, ":")
		r.Header.Set(s[0], s[1])
	}
	fmt.Fprintf(out, "content-length=%d\n", len(d))
	fmt.Fprintf(out, "url=%s\n", r.URL.String())
	fmt.Fprintf(out, "headers=%v\n", r.Header)

	if o.auth == nil {
		o.auth = f.BCEAuth()
	}

	var sign string
	if o.authVersion == auth.BCE_AUTH_V2 {
		sign = o.auth.
			NewSignerV2("bj", "cfc").
			Method(o.method).
			Path(u.Path).
			Params(r.URL.Query()).
			Headers(r.Header).
			Expire(3600).
			WithSignedHeader().
			GetSign()
	} else {
		sign = o.auth.
			NewSigner().
			Method(o.method).
			Path(u.Path).
			Params(r.URL.Query()).
			Headers(r.Header).
			Expire(3600).
			WithSignedHeader().
			GetSign()
	}

	r.Header.Set("Authorization", sign)
	fmt.Fprintf(out, "sign=%s\n", sign)

	dur := cmdutil.GetFlagDuration(cmd, "duration")
	var nums int
	if dur > 0 {
		nums = math.MaxInt32
	} else {
		nums = cmdutil.GetFlagInt(cmd, "requests")
	}
	w := &requester.Work{
		Request:            r,
		RequestBody:        d,
		N:                  nums,
		C:                  cmdutil.GetFlagInt(cmd, "conc"),
		QPS:                cmdutil.GetFlagFloat64(cmd, "qps"),
		Timeout:            cmdutil.GetFlagInt(cmd, "timeout"),
		DisableCompression: false,
		DisableKeepAlives:  false,
		DisableRedirects:   false,
		H2:                 false,
		ProxyAddr:          nil,
		Output:             "",
	}
	w.Init()

	sC := make(chan os.Signal, 1)
	signal.Notify(sC, os.Interrupt)
	go func() {
		<-sC
		w.Stop()
	}()

	if dur > 0 {
		go func() {
			time.Sleep(dur)
			w.Stop()
		}()
	}
	w.Run()
	return nil
}
