/**
 * Created Date: Monday, November 13th 2017, 2:05:01 pm
 * Author: hefan<PERSON><PERSON>
 * Copyright (c) 2017 Baidu.Inc
 *
 */

package cmd

import (
	"errors"
	"fmt"
	"io"
	"net/url"
	"strconv"
	"strings"
	"time"

	"github.com/google/uuid"
	"github.com/spf13/cobra"
	"icode.baidu.com/baidu/faas/kun/pkg/bce/auth"
	cmdutil "icode.baidu.com/baidu/faas/kun/pkg/cli/cmd/util"
)

type Request struct {
	url         string
	body        string
	method      string
	headers     []string
	auth        *auth.BceAuth
	authVersion string
}

// NewInvokeCmd xxx
func NewRequestCmd(f cmdutil.Factory, out io.Writer) *cobra.Command {
	cmd := &cobra.Command{
		Use:   "request",
		Short: "Request bce api",
		Long:  "Request bce api",
		Run: func(cmd *cobra.Command, args []string) {
			options := new(Request)
			cmdutil.CheckErr(options.Complete(cmd))
			cmdutil.CheckErr(options.Validate())
			cmdutil.CheckErr(options.Run(f, out))
		},
	}
	cmd.Flags().StringP("url", "u", "", "The request url")
	cmd.Flags().StringP("body", "b", "", "The request body")
	cmd.Flags().StringP("method", "m", "", "The request method")
	cmd.Flags().StringSlice("headers", []string{}, "The request headers")
	cmd.Flags().StringP("ak", "a", "", "user ak")
	cmd.Flags().StringP("sk", "s", "", "user sk")
	cmd.Flags().StringP("auth-version", "v", "v1", "auth version")
	return cmd
}

func (o *Request) Run(f cmdutil.Factory, out io.Writer) error {
	u, err := url.ParseRequestURI(o.url)
	if err != nil {
		return err
	}
	c, _ := f.RESTClient(&url.URL{
		Host:   u.Host,
		Scheme: u.Scheme,
	}, "")
	d := []byte(o.body)
	now := time.Now().UTC()
	utcStr := auth.GetCanonicalTime(now)
	req := c.Verb(o.method).
		Resource(u.Path).
		SetHeader("Host", u.Host).
		SetHeader("X-Bce-Date", utcStr).
		Body(d)

	if o.method != "GET" {
		req.SetHeader("Content-Length", strconv.Itoa(len(d)))
	}

	// 不支持单query key拥有多值
	for n, v := range u.Query() {
		req.Param(n, v[0])
	}

	for _, h := range o.headers {
		s := strings.Split(h, ":")
		req.SetHeader(s[0], s[1])
	}

	fmt.Fprintf(out, "content-length=%d\n", len(d))
	fmt.Fprintf(out, "url=%s\n", req.URL())

	if o.auth == nil {
		o.auth = f.BCEAuth()
	}

	var sign string
	if o.authVersion == auth.BCE_AUTH_V2 {
		sign = o.auth.
			NewSignerV2("bj", "cfc").
			Now(now).
			Method(req.Verb()).
			Path(req.URL().Path).
			Params(req.URL().Query()).
			Headers(req.Header()).
			Expire(3600).
			AddIgnoredHeader("content-length", "content-type", "x-bce-date", "x-bce-request-id").
			WithSignedHeader().
			GetSign()
	} else {
		sign = o.auth.
			NewSigner().
			Now(now).
			Method(req.Verb()).
			Path(req.URL().Path).
			Params(req.URL().Query()).
			Headers(req.Header()).
			Expire(3600).
			AddIgnoredHeader("content-length", "content-type", "x-bce-date", "x-bce-request-id").
			WithSignedHeader().
			GetSign()
	}

	req.SetHeader("x-bce-request-id", uuid.New().String())
	req.SetHeader("Authorization", sign)

	fmt.Fprintf(out, "Authorization=%v\n", sign)
	fmt.Fprintln(out, "===========request headers===============")
	fmt.Fprintf(out, "headers=%v\n", req.Header())

	res := req.Do()
	r, err := res.Raw()
	var status int
	res.StatusCode(&status)
	fmt.Fprintln(out, "===========response===============")
	fmt.Fprintf(out, "res=%s,\n headers=%v,\nstatus=%d, \nerr=%v\n", string(r), res.Header(), status, err)
	return nil
}

func (o *Request) Complete(cmd *cobra.Command) error {
	o.url = cmdutil.GetFlagString(cmd, "url")
	o.method = cmdutil.GetFlagString(cmd, "method")
	if len(o.method) == 0 {
		o.method = "GET"
	}
	o.body = cmdutil.GetFlagString(cmd, "body")
	o.headers = cmdutil.GetFlagStringSlice(cmd, "headers")

	ak := cmdutil.GetFlagString(cmd, "ak")
	sk := cmdutil.GetFlagString(cmd, "sk")
	if ak != "" && sk != "" {
		o.auth = &auth.BceAuth{
			AccessKey: ak,
			SecretKey: sk,
		}
	}
	authVersion := cmdutil.GetFlagString(cmd, "auth-version")
	if authVersion == "v2" {
		o.authVersion = auth.BCE_AUTH_V2
	} else {
		o.authVersion = auth.BCE_AUTH_V1
	}
	return nil
}

func (o *Request) Validate() error {
	if len(o.url) == 0 {
		return errors.New("--url cannot be empty")
	}
	return nil
}
