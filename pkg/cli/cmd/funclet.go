package cmd

import (
	"context"
	"fmt"
	"io"
	"sync"

	"github.com/spf13/cobra"

	"icode.baidu.com/baidu/faas/kun/pkg/api"
	"icode.baidu.com/baidu/faas/kun/pkg/funclet/client"
	"icode.baidu.com/baidu/faas/kun/pkg/util/json"
)

type FuncletImpl struct {
	client client.FuncletInterface
}

type FuncletOptions struct {
	Host               string
	Port               int
	MemoryLimitInBytes int64
	PodName            string
	FunctionFilePath   string
	ContainerIDList    []string
	WarmUpNum          int
	CoolDownNum        int
	FreezeState        string
	ContainerState     string
	ServiceType        string
}

func (f *FuncletImpl) ListContainer(o *FuncletOptions, out io.Writer) {
	criteria := api.NewListPodCriteria()
	if o.MemoryLimitInBytes != 0 {
		criteria.AddMemoryLimitInBytes(o.MemoryLimitInBytes)
	} else {
		criteria = nil
	}
	l, err := f.client.ListPods(&api.FuncletClientListPodsInput{Host: o.Host, Criteria: criteria})
	if err != nil {
		fmt.Fprintf(out, "err(%v) \n", err)
		return
	}
	b, err := json.Marshal(l)
	if err != nil {
		fmt.Fprintf(out, "err(%v) \n", err)
		return
	}
	fmt.Fprintf(out, "%s\n", b)
}
func (f *FuncletImpl) ListZombieContainer(o *FuncletOptions, out io.Writer) {
	l, err := f.client.ListZombiePods(&api.FuncletClientListZombiePodsInput{Host: o.Host})
	if err != nil {
		fmt.Fprintf(out, "err(%v) \n", err)
		return
	}
	b, err := json.Marshal(l)
	if err != nil {
		fmt.Fprintf(out, "err(%v) \n", err)
		return
	}
	fmt.Fprintf(out, "%s\n", b)
}

// FunctionFilePath 可用本目录下的 wamup_one.json
func (f *FuncletImpl) WarmupOne(o *FuncletOptions, out io.Writer) {
	functionOutput, err := GetFunction(o.FunctionFilePath)
	if err != nil {
		fmt.Fprintf(out, "err(%v) \n", err)
		return
	}
	if functionOutput.Configuration == nil || functionOutput.Configuration.Runtime == nil {
		fmt.Fprintf(out, "runtime err check json file")
		return
	}

	if o.WarmUpNum > 1 {
		wg := sync.WaitGroup{}
		for i := 0; i < o.WarmUpNum; i++ {
			wg.Add(1)
			go func() {
				defer wg.Done()
				fmt.Fprintf(out, "warmup one cold container \n")
				r, err := f.client.WarmUpOne(context.TODO(), &api.FuncletClientWarmUpOneInput{
					Resource: &api.ResourceConfig{
						Memory: &o.MemoryLimitInBytes,
					},
					Function:    functionOutput,
					Runtime:     GetRuntime(*functionOutput.Configuration.Runtime),
					ServiceType: api.ServiceType(o.ServiceType),
					Host:        o.Host,
					Labels:      map[string]string{"test": "testvalue"}})
				if err != nil {
					fmt.Fprintf(out, "err(%v) response (%+v) \n", err, r)
				} else {
					fmt.Fprintf(out, "%s success \n", r.Container.Hostname)
				}
			}()
		}
		wg.Wait()
		return
	}

	labels := map[string]string{"test": "testvalue"}
	if functionOutput.Configuration.NetworkConfig != nil && functionOutput.Configuration.NetworkConfig.ProxyFloatingIP != nil {
		labels[api.LabelVpcConfigID] = "hash123"
	}

	c, err := f.client.WarmUpOne(context.TODO(), &api.FuncletClientWarmUpOneInput{
		Resource: &api.ResourceConfig{
			Memory: &o.MemoryLimitInBytes,
		},
		Function:       functionOutput,
		Runtime:        GetRuntime(*functionOutput.Configuration.Runtime),
		ServiceType:    api.ServiceType(o.ServiceType),
		Labels:         labels,
		Host:           o.Host,
		GreInterfaceIP: "************",
	},
	)
	if err != nil {
		fmt.Fprintf(out, "err(%v) response (%+v) \n", err, c)
		return
	}
	fmt.Fprintln(out, "success "+c.Container.Hostname)
}

func (f *FuncletImpl) CoolDown(o *FuncletOptions, out io.Writer) {
	if o.CoolDownNum > 1 {
		pods, err := f.client.ListPods(&api.FuncletClientListPodsInput{Host: o.Host, Criteria: nil})
		if err != nil {
			fmt.Fprintf(out, "err(%v) \n", err)
			return
		}
		wg := sync.WaitGroup{}
		for _, v := range *pods {
			if o.CoolDownNum == 0 {
				break
			}
			if v.Status == api.ContainerStatusWarm {
				wg.Add(1)
				o.CoolDownNum--
				go func(hostname string) {
					defer wg.Done()
					fmt.Fprintf(out, "cooldown container(%s) \n", hostname)
					err := f.client.CoolDown(&api.FuncletClientCoolDownInput{Host: o.Host, Pod: &api.PodInfo{
						PodName:    hostname,
						MemorySize: o.MemoryLimitInBytes,
						IP:         o.Host,
					}})
					if err != nil {
						fmt.Fprintf(out, "(%s) err(%v)  \n", hostname, err)
					} else {
						fmt.Fprintf(out, "%s success \n", hostname)
					}
				}(v.Hostname)
			}
		}
		wg.Wait()
		return
	}
	err := f.client.CoolDown(&api.FuncletClientCoolDownInput{Host: o.Host, Pod: &api.PodInfo{
		PodName:    o.PodName,
		MemorySize: o.MemoryLimitInBytes,
		IP:         o.Host,
	}})
	if err != nil {
		fmt.Fprintf(out, "err(%v) \n", err)
		return
	}
	fmt.Fprintln(out, "success")
}

func (f *FuncletImpl) UpdateResource(o *FuncletOptions, out io.Writer) {
	pods := make([]*api.ContainerInfo, len(o.ContainerIDList))
	for k, v := range o.ContainerIDList {
		pods[k] = &api.ContainerInfo{
			ContainerID: v,
		}
	}
	l, err := f.client.UpdateResource(&api.FuncletClientUpdateResourceInput{Host: o.Host, Resource: &api.ResourceConfig{
		Memory: &o.MemoryLimitInBytes,
	}, Pods: pods})
	if err != nil {
		fmt.Fprintf(out, "err(%v) \n", err)
		return
	}
	b, err := json.Marshal(l)
	if err != nil {
		fmt.Fprintf(out, "err(%v) \n", err)
		return
	}
	fmt.Fprintf(out, "%s \n", b)
}

func (f *FuncletImpl) ResetNode(o *FuncletOptions, out io.Writer) {
	err := f.client.ResetNode(&api.FuncletClientResetNodeInput{Host: o.Host, OriginalMemory: o.MemoryLimitInBytes})
	if err != nil {
		fmt.Fprintf(out, "err(%v) \n", err)
		return
	}
	fmt.Fprintln(out, "success")
}

func (f *FuncletImpl) Freeze(o *FuncletOptions, out io.Writer) {
	err := f.client.FreezeContainer(&api.FuncletClientFreezeContainerInput{Host: o.Host, PodName: o.PodName, State: api.FreezerState(o.FreezeState)})
	if err != nil {
		fmt.Fprintf(out, "err(%v) \n", err)
		return
	}
	fmt.Fprintln(out, "success")
}

func (f *FuncletImpl) Report(o *FuncletOptions, out io.Writer) {
	err := f.client.ReportContainerState(&api.FuncletClientReportContainerStateInput{Host: o.Host, PodName: o.PodName, State: o.ContainerState})
	if err != nil {
		fmt.Fprintf(out, "err(%v) \n", err)
		return
	}
	fmt.Fprintln(out, "success")
}

func (f *FuncletImpl) ListSickPods(o *FuncletOptions, out io.Writer) {
	l, err := f.client.GetSickPodList(&api.FuncletClientGetSickPodListInput{Host: o.Host})
	if err != nil {
		fmt.Fprintf(out, "err(%v) \n", err)
		return
	}
	b, err := json.Marshal(l)
	if err != nil {
		fmt.Fprintf(out, "err(%v) \n", err)
		return
	}
	fmt.Fprintf(out, "%s\n", b)
}

func (f *FuncletImpl) PutSickPod(o *FuncletOptions, out io.Writer) {
	err := f.client.PutSickPod(&api.FuncletClientPutSickPodInput{Host: o.Host}, &api.SickPodRequest{PodName: o.PodName})
	if err != nil {
		fmt.Fprintf(out, "err(%v) \n", err)
		return
	}
	if err != nil {
		fmt.Fprintf(out, "err(%v) \n", err)
		return
	}
	fmt.Fprintln(out, "success")
}

func (f *FuncletImpl) DeleteSickPod(o *FuncletOptions, out io.Writer) {
	err := f.client.DeleteSickPod(&api.FuncletClientDeleteSickPodInput{Host: o.Host, PodName: o.PodName})
	if err != nil {
		fmt.Fprintf(out, "err(%v) \n", err)
		return
	}
	if err != nil {
		fmt.Fprintf(out, "err(%v) \n", err)
		return
	}
	fmt.Fprintln(out, "success")
}

func NewFuncletCmd(out io.Writer) *cobra.Command {
	o := new(FuncletOptions)
	f := new(FuncletImpl)
	funcletCmd := &cobra.Command{
		Use:   "funclet",
		Short: "do something funclet",
		PersistentPreRun: func(cmd *cobra.Command, args []string) {
			f.client = client.NewFuncletClient(&client.FuncletClientOptions{
				AccessMode: client.AccessModeNode,
				Port:       o.Port,
			})
		},
	}

	listContainer := &cobra.Command{
		Use: "list-container",
		Run: func(cmd *cobra.Command, args []string) {
			f.ListContainer(o, out)
		},
	}

	funcletCmd.AddCommand(&cobra.Command{
		Use: "list-zombie-container",
		Run: func(cmd *cobra.Command, args []string) {
			f.ListZombieContainer(o, out)
		},
	})

	warmupOneCmd := &cobra.Command{
		Use: "warmup-one",
		Run: func(cmd *cobra.Command, args []string) {
			f.WarmupOne(o, out)
		},
	}

	coolDownCmd := &cobra.Command{
		Use: "cooldown",
		Run: func(cmd *cobra.Command, args []string) {
			f.CoolDown(o, out)
		},
	}

	updateResourceCmd := &cobra.Command{
		Use: "update-resource",
		Run: func(cmd *cobra.Command, args []string) {
			f.UpdateResource(o, out)
		},
	}

	resetNodeCmd := &cobra.Command{
		Use: "resetNode",
		Run: func(cmd *cobra.Command, args []string) {
			f.ResetNode(o, out)
		},
	}

	freezeCmd := &cobra.Command{
		Use: "freeze",
		Run: func(cmd *cobra.Command, args []string) {
			f.Freeze(o, out)
		},
	}

	reportCmd := &cobra.Command{
		Use: "report",
		Run: func(cmd *cobra.Command, args []string) {
			f.Report(o, out)
		},
	}
	listsickPodCmd := &cobra.Command{
		Use: "list-sick-pods",
		Run: func(cmd *cobra.Command, args []string) {
			f.ListSickPods(o, out)
		},
	}
	putsickPodCmd := &cobra.Command{
		Use: "put-sick-pod",
		Run: func(cmd *cobra.Command, args []string) {
			f.PutSickPod(o, out)
		},
	}

	deletesickPodCmd := &cobra.Command{
		Use: "delete-sick-pod",
		Run: func(cmd *cobra.Command, args []string) {
			f.DeleteSickPod(o, out)
		},
	}

	funcletCmd.PersistentFlags().StringVar(&o.Host, "host", "127.0.0.1", "funclet host")
	funcletCmd.PersistentFlags().IntVar(&o.Port, "port", 8231, "funclet port")
	funcletCmd.PersistentFlags().Int64VarP(&o.MemoryLimitInBytes, "memory", "m", 134217728, "MemoryLimitInBytes")
	coolDownCmd.Flags().StringVarP(&o.PodName, "pod-name", "p", "", "pod name")
	coolDownCmd.Flags().IntVar(&o.CoolDownNum, "cool-down-num", 1, "cooldown pod num if>1 cooldown the number of containers ")
	freezeCmd.Flags().StringVarP(&o.PodName, "pod-name", "p", "", "pod name")
	freezeCmd.Flags().StringVarP(&o.FreezeState, "freeze-state", "s", "FROZEN", "freeze state FROZEN|THAWED")
	updateResourceCmd.Flags().StringSliceVar(&o.ContainerIDList, "containerid-list", nil, "containerid list")
	reportCmd.Flags().StringVarP(&o.PodName, "pod-name", "p", "", "pod name")
	reportCmd.Flags().StringVarP(&o.ContainerState, "container-state", "s", "", "container-state connected")
	warmupOneCmd.Flags().StringVar(&o.FunctionFilePath, "file-path", "", "function output file path")
	warmupOneCmd.Flags().IntVar(&o.WarmUpNum, "warm-num", 1, "warm pod num if>1 warm up the number of containers ")
	warmupOneCmd.Flags().Int64VarP(&o.MemoryLimitInBytes, "memory", "m", 134217728, "MemoryLimitInBytes")
	warmupOneCmd.Flags().StringVar(&o.ServiceType, "service-type", "cfc", "service type")
	putsickPodCmd.Flags().StringVarP(&o.PodName, "pod-name", "p", "", "pod name")
	deletesickPodCmd.Flags().StringVarP(&o.PodName, "pod-name", "p", "", "pod name")
	funcletCmd.AddCommand(listContainer, coolDownCmd, updateResourceCmd, resetNodeCmd, freezeCmd, reportCmd, warmupOneCmd,
		listsickPodCmd, putsickPodCmd, deletesickPodCmd)
	return funcletCmd
}
