package cmd

import (
	"io"

	"github.com/spf13/cobra"
	cmdutil "icode.baidu.com/baidu/faas/kun/pkg/cli/cmd/util"
)

func NewKunCommand(f cmdutil.Factory, in io.Reader, out, err io.Writer) *cobra.Command {
	cmds := &cobra.Command{
		Use:   "cfc",
		Short: "cfc command invoke the FaaS function",
		Long:  "cfc command invoke the FaaS function",
		Run:   runHelp,
	}
	cmds.AddCommand(NewInvokeCmd(f, out))
	cmds.AddCommand(NewRequestCmd(f, out))
	cmds.AddCommand(NewBenchCmd(f, out))
	cmds.AddCommand(NewBenchReportCmd(f, out))
	cmds.AddCommand(NewFuncletCmd(out))
	cmds.AddCommand(NewInvokerCmd(out))
	cmds.AddCommand(NewIAMCommand(out))
	cmds.AddCommand(NewRunnerCmd(f, out))
	cmds.AddCommand(NewRuntimeCmd(f, out))
	return cmds
}

func runHelp(cmd *cobra.Command, args []string) {
	cmd.Help()
}
