package cmd

import (
	"errors"
	"fmt"
	"io"
	"net/url"
	"time"

	"github.com/spf13/cobra"
	"icode.baidu.com/baidu/faas/kun/pkg/bce/auth"
	cmdutil "icode.baidu.com/baidu/faas/kun/pkg/cli/cmd/util"
)

type InvokeOptions struct {
	functionName   string
	invocationType string
	logType        string
	auth           *auth.BceAuth
	endpoint       string
	host           string
	body           string
	authVersion    string
}

// NewInvokeCmd xxx
func NewInvokeCmd(f cmdutil.Factory, out io.Writer) *cobra.Command {
	cmd := &cobra.Command{
		Use:   "invoke",
		Short: "Invoke the user function",
		Long:  "Invoke the user function",
		Run: func(cmd *cobra.Command, args []string) {
			options := new(InvokeOptions)
			cmdutil.CheckErr(options.Complete(cmd))
			cmdutil.CheckErr(options.Validate())
			cmdutil.CheckErr(options.Run(f, out))
		},
	}
	cmd.Flags().StringP("function-name", "f", "", "The function name")
	cmd.Flags().StringP("invocation-type", "i", "RequestResponse", "The invocation type")
	cmd.Flags().StringP("ak", "a", "", "user ak")
	cmd.Flags().StringP("sk", "s", "", "user sk")
	cmd.Flags().StringP("auth-version", "v", "v1", "auth version")
	cmd.Flags().String("host", "", "host")
	cmd.Flags().String("body", "", "body")
	cmd.Flags().StringP("endpoint", "e", "", "cfc endpoint, like: http://cfc.bj.baidubce.com")
	return cmd
}

func (o *InvokeOptions) Run(f cmdutil.Factory, out io.Writer) error {
	fmt.Fprintf(out, "invoke %s\n", o.functionName)
	u, err := url.Parse(o.endpoint)
	if err != nil {
		return err
	}
	c, _ := f.RESTClient(u, "v1")
	host := u.Host
	fmt.Fprintf(out, "host=%s\n", o.host)

	if o.host != "" {
		host = o.host
	}

	now := time.Now().UTC()
	utcStr := auth.GetCanonicalTime(now)

	req := c.Post().
		Resource("functions/"+o.functionName+"/invocations").
		SetHost(host).
		SetHeader("Host", host).
		SetHeader("Content-Type", "application/json").
		SetHeader("X-Amz-Invocation-Type", o.invocationType).
		SetHeader("X-BCE-FAAS-TRIGGER", "generic").
		SetHeader("X-Bce-Date", utcStr).
		Body([]byte(o.body))

	fmt.Fprintf(out, "url=%s\n", req.URL())
	fmt.Fprintf(out, "headers=%v\n", req.Header())

	if o.auth == nil {
		o.auth = f.BCEAuth()
	}
	var sign string
	if o.authVersion == auth.BCE_AUTH_V2 {
		sign = o.auth.
			NewSignerV2("bj", "cfc").
			Method(req.Verb()).
			Path(req.URL().Path).
			Params(req.URL().Query()).
			Headers(req.Header()).
			Expire(3600).
			WithSignedHeader().
			GetSign()
	} else {
		sign = o.auth.
			NewSigner().
			Method(req.Verb()).
			Path(req.URL().Path).
			Params(req.URL().Query()).
			Headers(req.Header()).
			Expire(3600).
			WithSignedHeader().
			GetSign()
	}
	req.SetHeader("Authorization", sign)
	fmt.Fprintf(out, "Authorization:%s\n", sign)

	response := req.Do()
	fmt.Fprintf(out, "response Header=%v\n", response.Header())
	res, err := response.Raw()
	fmt.Fprintf(out, "res=%s, err=%v\n", string(res), err)
	return nil
}

func (o *InvokeOptions) Complete(cmd *cobra.Command) error {
	o.functionName = cmdutil.GetFlagString(cmd, "function-name")
	o.endpoint = cmdutil.GetFlagString(cmd, "endpoint")
	o.invocationType = cmdutil.GetFlagString(cmd, "invocation-type")
	o.host = cmdutil.GetFlagString(cmd, "host")
	o.body = cmdutil.GetFlagString(cmd, "body")
	ak := cmdutil.GetFlagString(cmd, "ak")
	sk := cmdutil.GetFlagString(cmd, "sk")
	authVersion := cmdutil.GetFlagString(cmd, "auth-version")
	if ak != "" && sk != "" {
		o.auth = &auth.BceAuth{
			AccessKey: ak,
			SecretKey: sk,
		}
	}
	if o.endpoint == "" {
		o.endpoint = "http://cfc.bj.baidubce.com"
	}
	if o.body == "" {
		o.body = "{}"
	}
	if authVersion == "v2" {
		o.authVersion = auth.BCE_AUTH_V2
	} else {
		o.authVersion = auth.BCE_AUTH_V1
	}
	return nil
}

func (o *InvokeOptions) Validate() error {
	if len(o.functionName) == 0 {
		return errors.New("--function-name cannot be empty")
	}
	return nil
}
