package cmd

import (
	"bufio"
	"encoding/json"
	"fmt"
	"io"
	"io/ioutil"
	"net/http"
	"net/url"
	"os"
	"time"

	"github.com/spf13/cobra"
	"icode.baidu.com/baidu/faas/kun/pkg/api"
	"icode.baidu.com/baidu/faas/kun/pkg/bce/sts_credential"
	"icode.baidu.com/baidu/faas/kun/pkg/cfclog"
	_ "icode.baidu.com/baidu/faas/kun/pkg/cfclog/bos"
	"icode.baidu.com/baidu/faas/kun/pkg/rest"
	"icode.baidu.com/baidu/faas/kun/pkg/util/logs"
)

type LogstashOptions struct {
	Function     string
	Credential   string // sts credential文件
	RequestCount int
	Endpoint     string
	RuntimeID    string
	TempLogDir   string
	OriginLog    string
}

type BosLogOptions struct {
	BosBucket string
}

// 创建日志文件
func createRequestLog(logdir, logtempl, reqid string) (string, error) {
	file, err := ioutil.TempFile(logdir, "log")
	if err != nil {
		return "", err
	}
	templ, err := os.Open(logtempl)
	if err != nil {
		return "", err
	}
	defer templ.Close()
	file.Close()
	logfile, err := cfclog.CreateLogWriter("bos", file.Name(), 10*1024*1024)
	if err != nil {
		return "", err
	}

	log := &cfclog.CfcLog{
		Created:   time.Now(),
		RequestID: reqid,
		Source:    "cfc",
	}
	logfile.Write(log, []byte(fmt.Sprintf("START RequestId: %s Version: $LATEST", reqid)))
	reader := bufio.NewReader(templ)
	for {
		line, _, err := reader.ReadLine()
		if err != nil {
			if err == io.EOF {
				break
			}
			return "", err
		}
		log.Created = time.Now()
		log.Source = "stdout"
		_, err = logfile.Write(log, []byte(line))
		if err != nil {
			return "", err
		}
	}
	return file.Name(), nil
}

type logClient interface {
	Report(logfile string) error
}

type bosLogClient struct {
	restcli *rest.RESTClient
	stscred *sts_credential.StsCredential
	funcfg  *api.FunctionConfiguration
}

func loadCredential(credfile string) (*sts_credential.StsCredential, error) {
	file, err := os.Open(credfile)
	if err != nil {
		return nil, err
	}
	defer file.Close()

	stscred := &sts_credential.StsCredential{}
	decoder := json.NewDecoder(file)
	err = decoder.Decode(stscred)
	if err != nil {
		return nil, err
	}
	return stscred, nil
}

func loadFunction(config string) (*api.FunctionConfiguration, error) {
	file, err := os.Open(config)
	if err != nil {
		return nil, err
	}
	defer file.Close()

	funcfg := &api.FunctionConfiguration{}
	decoder := json.NewDecoder(file)
	err = decoder.Decode(funcfg)
	if err != nil {
		return nil, err
	}
	return funcfg, nil
}

func (opts *LogstashOptions) loadConfig() (*rest.RESTClient,
	*sts_credential.StsCredential, *api.FunctionConfiguration, error) {
	baseurl, err := url.Parse(opts.Endpoint)
	if err != nil {
		logs.Error(err.Error())
		return nil, nil, nil, err
	}
	config := rest.ContentConfig{
		BackendType: rest.BackendTypeInternal,
	}
	restcli, err := rest.NewRESTClient(baseurl, "v1", config, nil)
	if err != nil {
		logs.Error(err.Error())
		return nil, nil, nil, err
	}

	stscred, err := loadCredential(opts.Credential)
	if err != nil {
		logs.Error(err.Error())
		return nil, nil, nil, err
	}

	funcfg, err := loadFunction(opts.Function)
	if err != nil {
		logs.Error(err.Error())
		return nil, nil, nil, err
	}
	return restcli, stscred, funcfg, nil
}

func reportBosLog(restcli *rest.RESTClient, userlog *api.UserRequestLog) error {
	var status int
	err := restcli.Post().
		Resource("/logstash/reportLog").
		Timeout(340 * time.Second).
		Body(userlog).
		Do().
		StatusCode(&status).
		Error()
	if status != http.StatusOK {
		logs.Error(err.Error())
		return err
	}
	return nil
}

func NewLogstashCommand() *cobra.Command {
	opts := new(LogstashOptions)
	cmd := &cobra.Command{
		Use:   "logstash",
		Short: "logstash日志上传性能压测",
	}

	bosopts := new(BosLogOptions)
	boscmd := &cobra.Command{
		Use:   "bos",
		Short: "日志推送BOS",
		Run: func(cmd *cobra.Command, args []string) {
			restcli, stscred, funcfg, err := opts.loadConfig()
			if err != nil {
				logs.Error(err.Error())
				return
			}

			for i := 0; i < opts.RequestCount; i++ {
				logfile, err := createRequestLog(opts.TempLogDir,
					opts.OriginLog, fmt.Sprintf("123e4567-%d-12d3-a456-426655440000", i))
				if err != nil {
					logs.Error(err.Error())
					return
				}

				userlog := &api.UserRequestLog{
					RequestID:  fmt.Sprintf("request-%010d", i),
					RuntimeID:  "pmpod-128-8-1539851156401078088",
					Function:   funcfg,
					LogFile:    logfile,
					Credential: stscred,
					LogConfig: &api.LogConfiguration{
						LogType: "bos",
						BosDir:  bosopts.BosBucket,
					},
				}
				logs.V(9).Infof("logfile = %s", logfile)
				err = reportBosLog(restcli, userlog)
				if err != nil {
					logs.Error(err.Error())
					return
				}
			}
		},
	}

	cmd.PersistentFlags().StringVar(&opts.Function, "function", "./json/function.json", "Function配置文件")
	cmd.PersistentFlags().StringVar(&opts.Credential, "credential", "./json/ststoken.json", "Token配置文件")
	cmd.PersistentFlags().IntVar(&opts.RequestCount, "requests", 10000, "请求次数")
	cmd.PersistentFlags().StringVar(&opts.Endpoint, "endpoint", "http://localhost:8201/", "logstash Endpoint")
	cmd.PersistentFlags().StringVar(&opts.TempLogDir, "logdir", "/tmp/kunlogs", "临时日志目录")
	cmd.PersistentFlags().StringVar(&opts.OriginLog, "templ", "", "日志模版")

	boscmd.Flags().StringVar(&bosopts.BosBucket, "bucket", "", "Bucket路径")
	cmd.AddCommand(boscmd)
	return cmd
}
