package cmd

import (
	"encoding/json"
	"fmt"
	"io"
	"log"
	"os"
	"strconv"
	"sync"

	"github.com/spf13/cobra"
	cmdutil "icode.baidu.com/baidu/faas/kun/pkg/cli/cmd/util"
)

type RuntimeOptions struct {
	StdoutFile string
	StderrFile string
	PrintEnv   bool
}

func NewRuntimeCmd(f cmdutil.Factory, out io.Writer) *cobra.Command {
	cmd := &cobra.Command{
		Use:   "runtime",
		Short: "模拟runtime",
		Run: func(cmd *cobra.Command, args []string) {
			opts := new(RuntimeOptions)
			opts.Complete(cmd)
			client := NewRuntimeClient(opts)
			client.WaitInvoke()
		},
	}
	cmd.Flags().String("stdout", "./stdout-log", "标准输出日志文件")
	cmd.Flags().String("stderr", "./stderr-log", "标准错误日志文件")
	cmd.Flags().Bool("print-env", true, "打印环境变量")
	return cmd
}

func (opts *RuntimeOptions) Complete(cmd *cobra.Command) {
	opts.StdoutFile = cmdutil.GetFlagString(cmd, "stdout")
	opts.StderrFile = cmdutil.GetFlagString(cmd, "stderr")
	opts.PrintEnv, _ = cmd.Flags().GetBool("print-env")
}

type RuntimeClient struct {
	config *RuntimeOptions
}

func NewRuntimeClient(opts *RuntimeOptions) *RuntimeClient {
	return &RuntimeClient{
		config: opts,
	}
}

func (c *RuntimeClient) WaitInvoke() {
	stdout, _ := os.Open(c.config.StdoutFile)
	stderr, _ := os.Open(c.config.StderrFile)
	pipeEnv := os.Getenv("BCE_CFC_INVOKE_PIPE")
	if len(pipeEnv) == 0 {
		log.Fatalf("BCE_CFC_INVOKE_PIPE env not set")
	}
	pipefd, err := strconv.ParseInt(pipeEnv, 10, 32)
	if err != nil {
		log.Fatalf("parse BCE_CFC_INVOKE_PIPE failed %s", err.Error())
	}
	invokePipe := os.NewFile(uintptr(pipefd), "invoke-pipe")
	decoder := json.NewDecoder(invokePipe)
	encoder := json.NewEncoder(invokePipe)
	for {
		event := &InvokeRequest{}
		err = decoder.Decode(event)
		if err != nil {
			log.Fatalf("invoke error %s", err.Error())
		}
		wg := sync.WaitGroup{}
		wg.Add(2)
		go func() {
			if c.config.PrintEnv {
				fmt.Fprintf(os.Stdout, "%v\n", os.Environ())
			}
			if stdout != nil {
				stdout.Seek(0, os.SEEK_SET)
				_, err = io.Copy(os.Stdout, stdout)
				if err != nil {
					log.Printf("copy stdout failed %s", err.Error())
				}
			}
			os.Stdout.Write([]byte{'\000'})
			os.Stdout.Sync()
			wg.Done()
		}()

		go func() {
			if stderr != nil {
				stderr.Seek(0, os.SEEK_SET)
				_, err = io.Copy(os.Stderr, stderr)
				if err != nil {
					log.Printf("copy stderr failed %s", err.Error())
				}
				os.Stderr.Write([]byte{'\000'})
			}
			os.Stderr.Write([]byte{'\000'})
			os.Stderr.Sync()
			wg.Done()
		}()
		wg.Wait()

		resp := &InvokeResponse{
			RequestID:  event.RequestID,
			Success:    true,
			FuncResult: "hello world!",
		}
		err = encoder.Encode(resp)
		if err != nil {
			log.Fatalf("send response error %s", err.Error())
		}
	}
}
