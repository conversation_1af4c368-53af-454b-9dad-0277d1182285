package cmd

import (
	"context"
	"fmt"
	"io"

	"github.com/spf13/cobra"
	"icode.baidu.com/baidu/faas/kun/pkg/invoker/client"
	"icode.baidu.com/baidu/faas/kun/pkg/util/json"
)

type InvokerImpl struct {
	client client.InvokerInterface
}

type InvokerOptions struct {
	Host               string
	Port               int
	MemoryLimitInBytes int64
	PodName            string
	InvokerFilePath    string
	ContainerID        string
}

func (invoker *InvokerImpl) Invoke(o *InvokerOptions, out io.Writer) {
	input, err := GetInvocationInput(o.InvokerFilePath)
	if err != nil {
		fmt.Fprintf(out, "err(%v) \n", err)
		return
	}
	if o.PodName != "" {
		input.PodName = o.PodName
	}
	input.FloatingIP = o.Host
	input.Host = o.Host
	if o.ContainerID != "" {
		input.ContainerID = o.ContainerID
	}
	invocationOutput, statistic, err := invoker.client.Invoke(context.Background(), nil, input)
	if err != nil {
		fmt.Fprintf(out, "err(%v) \n", err)
		return
	}
	b, err := json.Marshal(map[string]interface{}{
		"output":    invocationOutput,
		"statistic": statistic,
	})
	if err != nil {
		fmt.Fprintf(out, "err(%v) \n", err)
		return
	}
	fmt.Fprintf(out, "%s \n", b)
}

func (invoker *InvokerImpl) AllPodsReport(o *InvokerOptions, out io.Writer) {
	l, err := invoker.client.AllPodsReport(o.Host)
	if err != nil {
		fmt.Fprintf(out, "err(%v) \n", err)
		return
	}
	b, err := json.Marshal(l)
	if err != nil {
		fmt.Fprintf(out, "err(%v) \n", err)
		return
	}
	fmt.Fprintf(out, "%s \n", b)
}

func (invoker *InvokerImpl) OnePodReport(o *InvokerOptions, out io.Writer) {
	l, err := invoker.client.OnePodReport(o.Host, o.PodName)
	if err != nil {
		fmt.Fprintf(out, "err(%v) \n", err)
		return
	}
	b, err := json.Marshal(l)
	if err != nil {
		fmt.Fprintf(out, "err(%v) \n", err)
		return
	}
	fmt.Fprintf(out, "%s \n", b)
}

// funcletCmd represents the funclet command
func NewInvokerCmd(out io.Writer) *cobra.Command {
	o := new(InvokerOptions)
	invoker := new(InvokerImpl)
	invokerCmd := &cobra.Command{
		Use:   "invoker",
		Short: "do something invoker",
		PersistentPreRun: func(cmd *cobra.Command, args []string) {
			invoker.client = client.NewInvokerClient(&client.InvokerClientOptions{Port: o.Port}, nil)
		},
	}

	allPodsReportCmd := &cobra.Command{
		Use: "all-pods-report",
		Run: func(cmd *cobra.Command, args []string) {
			invoker.AllPodsReport(o, out)
		},
	}

	onePodReportCmd := &cobra.Command{
		Use: "one-pod-report",
		Run: func(cmd *cobra.Command, args []string) {
			invoker.OnePodReport(o, out)
		},
	}

	invokeCmd := &cobra.Command{
		Use: "invoke",
		Run: func(cmd *cobra.Command, args []string) {
			invoker.Invoke(o, out)
		},
	}

	invokerCmd.PersistentFlags().StringVar(&o.Host, "host", "127.0.0.1", "invoker host")
	invokerCmd.PersistentFlags().IntVar(&o.Port, "port", 8200, "invoker port")
	onePodReportCmd.Flags().StringVarP(&o.PodName, "pod-name", "p", "", "pod name")
	invokeCmd.Flags().StringVarP(&o.PodName, "pod-name", "p", "", "pod name")
	invokeCmd.Flags().StringVar(&o.InvokerFilePath, "file-path", "", "invoke json file path")
	invokeCmd.Flags().StringVarP(&o.ContainerID, "container-id", "c", "", "containerID")
	invokerCmd.AddCommand(allPodsReportCmd, onePodReportCmd, invokeCmd)
	return invokerCmd
}
