{"Code": {"Location": "http://faas-online-test.bj.bcebos.com/42f6fbc2cd374bfcb80d9967370fd8ff/xls-0213-final-1_e16e780a-6ad3-4642-8264-e6c0c942009a.zip?authorization=bce-auth-v1%2F23199f4c13224a09b2ef8125c5800895%2F2023-02-14T11%3A09%3A08Z%2F600%2Fhost%2F2c5702bcdb8191eacaf0b45f1dcc5c24e51ee0cd4f24f2a783f9deb95e530115", "RepositoryType": ""}, "Concurrency": {"ReservedConcurrentExecutions": null, "AccountReservedSum": 0}, "Configuration": {"Uid": "42f6fbc2cd374bfcb80d9967370fd8ff", "Description": "", "FunctionBrn": "brn:bce:cfc:bj:1a2cbf55b97ac8a7c760c4177db4e17d:function:xls-0213-final-1:$LATEST", "Region": "bj", "Timeout": 5, "VersionDesc": "", "UpdatedAt": "2023-02-13T19:58:16+08:00", "LastModified": "2023-02-13T19:58:16+08:00", "SourceTag": "", "CodeSha256": "FJFwSxLuXhkt0cQZYKEOfVTZIMCwt9/Ils6kpbrr3jI=", "LayerSha256": "", "CodeSize": 223, "FunctionArn": "brn:bce:cfc:bj:1a2cbf55b97ac8a7c760c4177db4e17d:function:xls-0213-final-1:$LATEST", "FunctionName": "xls-0213-final-1", "ServiceName": "default", "Handler": "index.handler", "Version": "$LATEST", "Runtime": "python3", "MemorySize": 128, "Environment": {"Variables": {}}, "CommitId": "5cf2b079-5e82-4c92-82ad-934562d3c0f7", "Role": "", "LogType": "none", "BlsLogSet": "", "VpcConfig": {"VpcId": "vpc-3y7er5j5qim8", "SubnetIds": ["sbn-92j79g43ap13"], "SecurityGroupIds": ["g-gNH12MSL"]}, "Layers": [], "PodConcurrentQuota": 1, "FunctionMaxTimeout": null, "AsyncInvokeConfig": {"MaxRetryIntervalInSeconds": null, "MaxRetryAttempts": null, "OnSuccess": null, "OnFailure": null}, "CFSConfig": {"FsName": null, "FsId": null, "SubnetID": null, "Domain": null, "RemotePath": null, "LocalPath": null, "Ovip": null, "VpcId": null}, "NetworkConfig": {"Uid": "42f6fbc2cd374bfcb80d9967370fd8ff", "FunctionName": "xls-0213-final-1", "Brn": "brn:bce:cfc:bj:1a2cbf55b97ac8a7c760c4177db4e17d:function:xls-0213-final-1:$LATEST", "VpcConfig": null, "VpcConfigStr": "{\"VpcId\":\"vpc-3y7er5j5qim8\",\"SubnetIds\":[\"sbn-92j79g43ap13\"],\"SecurityGroupIds\":[\"g-gNH12MSL\"]}", "Status": "ready", "VpcCidr": "***********/21", "ProxyInternalIP": "*************", "ProxyFloatingIP": "*************", "EnableSlave": false}}, "LogConfig": null}