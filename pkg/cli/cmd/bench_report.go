package cmd

import (
	"bytes"
	"fmt"
	"io"
	"io/ioutil"
	"log"
	"math"
	"os/exec"
	"regexp"
	"runtime"
	"strconv"
	"strings"
	"time"

	"github.com/spf13/cobra"
	"icode.baidu.com/baidu/faas/kun/pkg/bce/auth"
	cmdutil "icode.baidu.com/baidu/faas/kun/pkg/cli/cmd/util"
)

// BenchReportOptions xxx
type BenchReportOptions struct {
	auth *auth.BceAuth
	url  string
}

// NewBenchReportCmd xxx
func NewBenchReportCmd(f cmdutil.Factory, out io.Writer) *cobra.Command {
	cmd := &cobra.Command{
		Use:   "bench-report",
		Short: "bench host",
		Long:  "bench host, auto add BCE Auth HTTP header",
		Run: func(cmd *cobra.Command, args []string) {
			options := new(BenchReportOptions)
			cmdutil.CheckErr(options.Complete(cmd))
			cmdutil.CheckErr(options.Run(cmd, f, out))
		},
	}
	cmd.Flags().IntP("cpus", "p", runtime.NumCPU(), fmt.Sprintf("Number of used cpu %d cores.", runtime.NumCPU()))

	cmd.Flags().StringP("ak", "a", "", "user ak")
	cmd.Flags().StringP("sk", "s", "", "user sk")
	runtime.GOMAXPROCS(cmdutil.GetFlagInt(cmd, "cpus"))
	return cmd
}

func (o *BenchReportOptions) Complete(cmd *cobra.Command) error {
	o.url = cmd.Flags().Arg(0)
	o.url = strings.Trim(o.url, "http://")

	ak := cmdutil.GetFlagString(cmd, "ak")
	sk := cmdutil.GetFlagString(cmd, "sk")
	if len(ak) > 0 && len(sk) > 0 {
		o.auth = &auth.BceAuth{
			AccessKey: ak,
			SecretKey: sk,
		}
	}
	return nil
}

func (o *BenchReportOptions) Run(cmd *cobra.Command, f cmdutil.Factory, out io.Writer) error {
	collectcpu := false
	if strings.Index(o.url, "127.0.0.1") >= 0 {
		collectcpu = true
	}

	table := initTable()

	start := time.Now()
	fmt.Printf("Generating report of benchmarks, need several minutes ...\n")
	fmt.Printf("Collect CPU usage only if bench localhost\n\n")

	for _, duration := range funcExecuteDurations {
		// 函数执行时间可以作为参数传入
		path := fmt.Sprintf("http://%s/v1/functions/__invoke:%d__/invocations", o.url, duration)

		for _, concurrency := range funcConcurrency {
			benchWarmupRequest(path, concurrency, duration)
			time.Sleep(time.Second)

			benchTotalRequest(table, path, concurrency, duration, collectcpu)
			time.Sleep(time.Second)
		}
	}
	fmt.Printf("All bench done, cost=%.2f minutes\n", time.Now().Sub(start).Minutes())
	printTable(table)
	return nil
}

///////////////////////////////////////////////////////////////////////////////
///////////////////////////////////////////////////////////////////////////////

type ReportCell struct {
	Average  float64
	QPS      float64
	CpuUsage float64
}

type ReportRow map[int]*ReportCell
type ReportTable map[int]ReportRow

var (
	// 分别测试函数执行时间为 200ms, 100ms, 5ms 时的情况
	funcExecuteDurations = []int{200, 100, 5}
	// 分别测试并发度为 100, 250, 500, 750, 1000, 1250 时的情况
	funcConcurrency = []int{50, 100, 250, 500, 750, 1000, 1250}
)

func initTable() ReportTable {
	table := ReportTable{}
	for _, concurrency := range funcConcurrency {
		table[concurrency] = ReportRow{}
	}
	return table
}

// 为了保证执行时间都在 30-60 秒左右，需要用函数执行时间和并发度算出来
func calculateTotalRequest(concurrency, duration int) int {
	coe := math.Sqrt(200 / float64(duration))
	cal := coe * float64(concurrency*300)
	return min(int(cal), 80000)
}

func benchTotalRequest(table ReportTable, path string, concurrency, duration int, collectcpu bool) {
	var buffer bytes.Buffer
	start := time.Now()

	totalNum := calculateTotalRequest(concurrency, duration)
	fmt.Printf("Running bench (concurrency=%d duration=%d requests_num=%d)... ", concurrency, duration, totalNum)

	if collectcpu {
		go fillCpuUsage(table, concurrency, duration)
	}
	runOnce(path, &buffer, concurrency, duration, totalNum, table)
	fmt.Printf("done, cost=%.2f seconds\n", time.Now().Sub(start).Seconds())
}

// 在正式调用前需要用一些请求预热一下
func calculateWarmupRequest(concurrency int) int {
	return min(concurrency*5, 2000)
}

func benchWarmupRequest(path string, concurrency, duration int) {
	start := time.Now()
	warmNum := calculateWarmupRequest(concurrency)
	fmt.Printf("Warmup bench (concurrency=%d duration=%d requests_num=%d)... ", concurrency, duration, warmNum)
	runOnce(path, nil, concurrency, duration, warmNum, nil)
	fmt.Printf("done, cost=%.2f seconds\n", time.Now().Sub(start).Seconds())
}

func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}

var (
	avgreg = regexp.MustCompile(`Average:	(?P<avg>[0-9\.]+) secs`)
	qpsreg = regexp.MustCompile(`Requests/sec:	(?P<qps>[0-9\.]+)`)
)

func getResult(str string) (avg float64, qps float64) {
	var avgstr, qpsstr string
	if avgslice := avgreg.FindStringSubmatch(str); len(avgslice) == 2 {
		avgstr = avgslice[1]
	}
	if qpsslice := qpsreg.FindStringSubmatch(str); len(qpsslice) == 2 {
		qpsstr = qpsslice[1]
	}
	if len(avgstr) > 0 {
		avg, _ = strconv.ParseFloat(avgstr, 64)
	}
	if len(qpsstr) > 0 {
		qps, _ = strconv.ParseFloat(qpsstr, 64)
	}
	return avg, qps
}

func runOnce(path string, buffer *bytes.Buffer, concurrency, duration, num int, table ReportTable) {
	// cli bench -n300 -c250 -m POST http://127.0.0.1:8081/v1/functions/__invoke:5__/invocations
	number := fmt.Sprintf("-n%d", num)
	concur := fmt.Sprintf("-c%d", concurrency)
	cmd := exec.Command("./cli", "bench", number, concur, "-mPOST", path)
	if buffer != nil {
		buffer.Reset()
		cmd.Stdout = buffer
	} else {
		cmd.Stdout = ioutil.Discard
	}

	if err := cmd.Run(); err != nil {
		log.Fatal("cli exec error", err)
	}

	if buffer == nil {
		return
	}
	avg, qps := getResult(buffer.String())
	if table != nil {
		cell := table[concurrency][duration]
		if cell == nil {
			table[concurrency][duration] = &ReportCell{
				Average:  avg*1e3 - float64(duration),
				QPS:      qps,
				CpuUsage: 0.0,
			}
		} else {
			cell.Average = avg*1e3 - float64(duration)
			cell.QPS = qps
		}
	}
}

func printTable(table ReportTable) {
	fmt.Printf("\n")

	// table header
	fmt.Printf("|并发/invokeTime|")
	for _, duration := range funcExecuteDurations {
		fmt.Printf("%dms|", duration)
	}
	fmt.Printf("\n")

	// delimiter
	fmt.Printf("|-|")
	for i := 0; i < len(funcExecuteDurations); i++ {
		fmt.Printf("-|")
	}
	fmt.Printf("\n")

	// table body
	for _, concurrency := range funcConcurrency {
		fmt.Printf("|%d|", concurrency)
		for _, duration := range funcExecuteDurations {
			c := table[concurrency][duration]
			fmt.Printf("%.1fms/ %.0fQPS/ %.0f%%|", c.Average, c.QPS, c.CpuUsage)
		}
		fmt.Printf("\n")
	}
}

func getCpuUsage() string {
	cmd := "echo -n $[100-$(vmstat 1 2|tail -1|awk '{print $15}')]"
	out, err := exec.Command("bash", "-c", cmd).Output()
	if err != nil {
		return fmt.Sprintf("Failed to execute command: %s", cmd)
	}
	return string(out)
}

func fillCpuUsage(table ReportTable, concurrency, duration int) {
	times := 10
	sum := 0.0
	for i := 0; i < times; i++ {
		time.Sleep(200 * time.Millisecond)
		usage := getCpuUsage()
		// fmt.Printf("cpu usage=%s\n", usage)
		if d, err := strconv.Atoi(usage); err == nil {
			sum += float64(d)
		} else {
			fmt.Printf("parse error, str='%s', err='%s'", usage, err.Error())
		}
	}
	cell := table[concurrency][duration]
	if cell == nil {
		table[concurrency][duration] = &ReportCell{
			CpuUsage: sum / float64(times),
		}
	} else {
		cell.CpuUsage = sum / float64(times)
	}
}
