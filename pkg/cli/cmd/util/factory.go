package util

import (
	"net/url"

	"icode.baidu.com/baidu/faas/kun/pkg/bce/auth"
	"icode.baidu.com/baidu/faas/kun/pkg/rest"
)

// Factory provides abstractions that allow the Kubectl command to be extended across multiple types
// of resources and different API sets.
type Factory interface {
	ClientAccessFactory
}

// ClientAccessFactory holds the first level of factory methods.
// Generally provides discovery, negotiation, and no-dep calls.
type ClientAccessFactory interface {
	RESTClient(baseurl *url.URL, version string) (*rest.RESTClient, error)
	BCEAuth() *auth.BceAuth
}

type factory struct {
	ClientAccessFactory
}

// NewFactory xxx
func NewFactory(optionalClientConfig ClientConfig) Factory {
	clientAccessFactory := NewClientAccessFactory(optionalClientConfig)
	return &factory{
		ClientAccessFactory: clientAccessFactory,
	}
}

// NewClientAccessFactory xxx
func NewClientAccessFactory(optionalClientConfig ClientConfig) ClientAccessFactory {
	return &ring0Factory{}
}

type ring0Factory struct {
	rest *rest.RESTClient
	auth *auth.BceAuth
}

func (f *ring0Factory) RESTClient(baseurl *url.URL, version string) (*rest.RESTClient, error) {
	if f.rest != nil {
		return f.rest, nil
	}
	config := rest.ContentConfig{
		BackendType: rest.BackendTypeInternal,
	}
	rest, err := rest.NewRESTClient(baseurl, version, config, nil)
	if err != nil {
		return nil, err
	}
	f.rest = rest
	return rest, nil
}

func (f *ring0Factory) BCEAuth() *auth.BceAuth {
	if f.auth == nil {
		f.auth = &auth.BceAuth{
			AccessKey: "5d4e433531d14d2f91de2dc576368a51",
			SecretKey: "c13b18ad42a6405db7da9ced3bee40d2",
		}
	}
	return f.auth
}
