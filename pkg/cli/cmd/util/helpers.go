package util

import (
	"fmt"
	"os"
	"strings"
	"time"

	"github.com/spf13/cobra"
	"icode.baidu.com/baidu/faas/kun/pkg/util/logs"
)

const (
	DefaultErrorExitCode = 1
)

var fatalErrHandler = fatal

// fatal prints the message (if provided) and then exits. If V(2) or greater,
// logs.Fatal is invoked for extended information.
func fatal(msg string, code int) {
	if logs.Check(logs.V(2)) {
		logs.Fatalf(msg)
	}
	if len(msg) > 0 {
		// add newline if needed
		if !strings.HasSuffix(msg, "\n") {
			msg += "\n"
		}
		fmt.Fprint(os.Stderr, msg)
	}
	os.Exit(code)
}

// CheckErr prints a user friendly error to STDERR and exits with a non-zero
// exit code. Unrecognized errors will be printed with an "error: " prefix.
//
// This method is generic to the command in use and may be used by non-Kubectl
// commands.
func CheckErr(err error) {
	checkErr(err, fatalErrHandler)
}

// checkErr formats a given error as a string and calls the passed handleErr
// func with that string and an kubectl exit code.
func checkErr(err error, handleErr func(string, int)) {
	if err == nil {
		return
	}
	handleErr(err.Error(), DefaultErrorExitCode)
}

func GetFlagString(cmd *cobra.Command, flag string) string {
	s, err := cmd.Flags().GetString(flag)
	if err != nil {
		logs.Fatalf("error accessing flag %s for command %s: %v", flag, cmd.Name(), err)
	}
	return s
}

func GetFlagStringSlice(cmd *cobra.Command, flag string) []string {
	s, err := cmd.Flags().GetStringSlice(flag)
	if err != nil {
		logs.Fatalf("error accessing flag %s for command %s: %v", flag, cmd.Name(), err)
	}
	return s
}

func GetFlagInt(cmd *cobra.Command, flag string) int {
	s, err := cmd.Flags().GetInt(flag)
	if err != nil {
		logs.Fatalf("error accessing flag %s for command %s: %v", flag, cmd.Name(), err)
	}
	return s
}

func GetFlagFloat64(cmd *cobra.Command, flag string) float64 {
	s, err := cmd.Flags().GetFloat64(flag)
	if err != nil {
		logs.Fatalf("error accessing flag %s for command %s: %v", flag, cmd.Name(), err)
	}
	return s
}

func GetFlagDuration(cmd *cobra.Command, flag string) time.Duration {
	s, err := cmd.Flags().GetDuration(flag)
	if err != nil {
		logs.Fatalf("error accessing flag %s for command %s: %v", flag, cmd.Name(), err)
	}
	return s
}
