package cmd

import (
	"bytes"
	"fmt"
	"io"
	"log"
	"math/rand"
	"net"
	"net/url"
	"os"
	"sync"
	"time"

	"github.com/spf13/cobra"
	cmdutil "icode.baidu.com/baidu/faas/kun/pkg/cli/cmd/util"
	"icode.baidu.com/baidu/faas/kun/pkg/util/json"
)

type RunnerOptions struct {
	Endpoint   string
	Hostname   string
	StdoutFile string
	StderrFile string
}

func NewRunnerCmd(f cmdutil.Factory, out io.Writer) *cobra.Command {
	cmd := &cobra.Command{
		Use:   "runner",
		Short: "模拟runner + runtime",
		Run: func(cmd *cobra.Command, args []string) {
			opts := new(RunnerOptions)
			opts.Complete(cmd)
			client := NewRunnerClient(opts)
			client.Run()
		},
	}
	cmd.Flags().StringP("endpoint", "e", "tcp://localhost:8202", "invoker dispatcher地址")
	cmd.Flags().StringP("hostname", "n", "", "runtime名称")
	cmd.Flags().String("stdout", "./stdout-log", "标准输出日志文件")
	cmd.Flags().String("stderr", "./stderr-log", "标准错误日志文件")

	return cmd
}

func (opts *RunnerOptions) Complete(cmd *cobra.Command) error {
	opts.Endpoint = cmdutil.GetFlagString(cmd, "endpoint")
	opts.Hostname = cmdutil.GetFlagString(cmd, "hostname")
	opts.StdoutFile = cmdutil.GetFlagString(cmd, "stdout")
	opts.StderrFile = cmdutil.GetFlagString(cmd, "stderr")
	return nil
}

type RunnerClient struct {
	config    *RunnerOptions
	runtimeid string
	outch     chan *userLog
	errch     chan *userLog
}

func NewRunnerClient(opts *RunnerOptions) *RunnerClient {
	hostname := opts.Hostname
	if len(hostname) == 0 {
		hostname, _ = os.Hostname()
	}
	return &RunnerClient{
		config:    opts,
		runtimeid: hostname,
		outch:     make(chan *userLog, 10),
		errch:     make(chan *userLog, 10),
	}
}

func (c *RunnerClient) stdout() {
	req := new(bytes.Buffer)
	req.WriteString("POST /stdout HTTP/1.1\r\n")
	req.WriteString("host: localhost\r\n")

	req.WriteString(fmt.Sprintf("x-cfc-runtimeid: %s\r\n", c.runtimeid))
	req.WriteString("\r\n")
	conn, err := c.hijack(req.Bytes())
	if err != nil {
		log.Fatalf("stdout error %s", err.Error())
	}
	defer conn.Close()
	for {
		select {
		case ul := <-c.outch:
			err = ul.Write(conn)
			if err != nil {
				log.Fatalf("write stdout log error %s", err.Error())
			}
		}
	}
}

func (c *RunnerClient) stderr() {
	req := new(bytes.Buffer)
	req.WriteString("POST /stderr HTTP/1.1\r\n")
	req.WriteString("host: localhost\r\n")
	req.WriteString(fmt.Sprintf("x-cfc-runtimeid: %s\r\n", c.runtimeid))
	req.WriteString("\r\n")
	conn, err := c.hijack(req.Bytes())
	if err != nil {
		log.Fatalf("stderr error %s", err.Error())
	}
	defer conn.Close()
	for {
		select {
		case ul := <-c.errch:
			err = ul.Write(conn)
			if err != nil {
				log.Fatalf("write stderr log error %s", err.Error())
			}
		}
	}
}

type statInfo struct {
	PodName string `json:"podname,omitempty"`
	MemUsed int64  `json:"memory,omitempty"`
}

func (c *RunnerClient) status() {
	req := new(bytes.Buffer)
	req.WriteString("POST /statistic HTTP/1.1\r\n")
	req.WriteString("host: localhost\r\n")
	req.WriteString(fmt.Sprintf("x-cfc-runtimeid: %s\r\n", c.runtimeid))
	req.WriteString("\r\n")
	conn, err := c.hijack(req.Bytes())
	if err != nil {
		log.Printf("statistic error %s", err.Error())
	}
	defer conn.Close()

	var stat statInfo
	ticker := time.Tick(1 * time.Second)
	for {
		<-ticker
		stat.PodName = c.runtimeid
		stat.MemUsed = int64((rand.Int31n(20) + 40) * 1024 * 1024)
		data, _ := json.Marshal(&stat)
		data = append(data, '\n')
		_, err = conn.Write(data)
		if err != nil {
			log.Fatalf("write stat error %s", err.Error())
		}
	}
}

type InvokeRequest struct {
	RequestID       string `json:"requestid"`
	Version         string `json:"version"`
	AccessKeyID     string `json:"accessKey`
	AccessKeySecret string `json:"secretKey`
	SecurityToken   string `json:"securityToken`
	ClientContext   string `json:"clientContext"`
	EventObject     string `json:"eventObject"`
}

type InvokeResponse struct {
	RequestID  string `json:"requestid"`
	Success    bool   `json:"success"`
	FuncResult string `json:"result,omitempty"`
	FuncError  string `json:"error,omitempty"`
}

type userLog struct {
	wg *sync.WaitGroup
	rd *os.File
}

func (l *userLog) Write(w io.Writer) error {
	var err error
	if l.rd != nil {
		_, err = io.Copy(w, l.rd)
	}
	w.Write([]byte{'\000'})
	l.wg.Done()
	return err
}

func (c *RunnerClient) invoke() {
	req := new(bytes.Buffer)
	req.WriteString("POST /invoke HTTP/1.1\r\n")
	req.WriteString("host: localhost\r\n")
	req.WriteString(fmt.Sprintf("x-cfc-runtimeid: %s\r\n", c.runtimeid))
	req.WriteString("\r\n")
	conn, err := c.hijack(req.Bytes())
	if err != nil {
		log.Fatalf("invoke error %s", err.Error())
	}
	defer conn.Close()

	outlog, _ := os.Open(c.config.StdoutFile)
	errlog, _ := os.Open(c.config.StderrFile)
	decoder := json.NewDecoder(conn)
	encoder := json.NewEncoder(conn)
	for {
		event := &InvokeRequest{}
		err = decoder.Decode(event)
		if err != nil {
			log.Fatalf("invoke error %s", err.Error())
		}
		log.Println(event)
		if outlog != nil {
			log.Println("outlog", outlog.Name())
			outlog.Seek(0, os.SEEK_SET)
		}
		if errlog != nil {
			log.Println("errlog", errlog.Name())
			errlog.Seek(0, os.SEEK_SET)
		}
		wg := sync.WaitGroup{}
		wg.Add(2)
		outl := &userLog{
			wg: &wg,
			rd: outlog,
		}
		c.outch <- outl
		errl := &userLog{
			wg: &wg,
			rd: errlog,
		}
		c.errch <- errl
		wg.Wait()
		resp := &InvokeResponse{
			RequestID:  event.RequestID,
			Success:    true,
			FuncResult: "hello world!",
		}
		err = encoder.Encode(resp)
		if err != nil {
			log.Fatalf("send response error %s", err.Error())
		}
	}
}

func (c *RunnerClient) hijack(req []byte) (net.Conn, error) {
	u, err := url.Parse(c.config.Endpoint)
	if err != nil {
		return nil, err
	}
	var conn net.Conn
	if u.Scheme == "unix" {
		conn, err = net.Dial("unix", u.Path)
	} else if u.Scheme == "tcp" {
		conn, err = net.Dial("tcp", u.Host)
	} else {
		err = fmt.Errorf("unknown scheme %s", u.Scheme)
	}
	if err != nil {
		return nil, err
	}

	_, err = conn.Write(req)
	if err != nil {
		return nil, err
	}
	return conn, err
}

func (c *RunnerClient) Run() {
	go c.status()
	go c.stdout()
	go c.stderr()
	c.invoke()
}
