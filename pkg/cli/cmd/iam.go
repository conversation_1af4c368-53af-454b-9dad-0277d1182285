package cmd

import (
	"encoding/json"
	"fmt"
	"io"

	"github.com/spf13/cobra"

	iamcli "icode.baidu.com/baidu/faas/kun/pkg/bce/iam"
)

type IAMOptions struct {
	Config   string
	UserID   string
	Role     string
	Duration int
}

const (
	MaxDuration = 3600
)

func NewIAMCommand(out io.Writer) *cobra.Command {
	opts := new(IAMOptions)
	var cli iamcli.ClientInterface
	rootCmd := &cobra.Command{
		Use:   "iam",
		Short: "IAM相关",
		PersistentPreRunE: func(cmd *cobra.Command, args []string) error {
			client, err := iamcli.CreateIAMClient(opts.Config)
			if err != nil {
				return err
			}
			cli = client
			return nil
		},
	}
	assumeRole := &cobra.Command{
		Use: "assume-role",
		Run: func(cmd *cobra.Command, args []string) {
			duration := opts.Duration
			if duration < 0 || duration > MaxDuration {
				duration = MaxDuration
			}
			role, err := cli.Assume<PERSON>ole(opts.UserID, opts.Role, "", duration)
			if err != nil {
				io.WriteString(out, fmt.Sprintf("%v", err))
			} else {
				json.NewEncoder(out).Encode(role)
			}
		},
	}
	rootCmd.PersistentFlags().StringVar(&opts.Config, "config", "./config/iam_qa.yaml", "IAM配置文件")
	assumeRole.Flags().StringVar(&opts.UserID, "userid", "", "用户ID")
	assumeRole.Flags().StringVar(&opts.Role, "role", "BceServiceRole_cfc", "角色名称")
	assumeRole.Flags().IntVar(&opts.Duration, "duration", 3600, "Token持续时间(s)")
	rootCmd.AddCommand(assumeRole)
	return rootCmd
}
