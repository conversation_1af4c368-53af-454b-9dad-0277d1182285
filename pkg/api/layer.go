package api

import (
	"strings"
	"time"
)

type Layer struct {
	Id                   uint   `json:"-"`
	Uid                  string // 用户ID
	CodeSha256           string // 代码sha256
	CodeId               string // Code ID 随机生成
	CodeSize             int64  // zip包大小
	SquashFsSha256       string `gorm:"column:squashfs_sha256" json:"-"` // layer的squashFs sha256 当function layers只存在一个时直接copy bos的文件
	UncompressedCodeSize int64  // 解压后的代码大小
	CompatibleRuntimeStr string `gorm:"column:compatible_runtime"` //
	Description          string // 描述
	LicenseInfo          string // 许可
	LayerName            string // Name
	Version              int64  // 版本号 1开始
	Brn                  string // BRN 无版本号
	IsVended             *bool  `gorm:"DEFAULT:false"` // 是否为出售状态
	Enabled              *bool  `gorm:"DEFAULT:true"`  // 官方layer专属，是否开启 fasle为下线状态 只有指定成员可见如serverless， true为上线状态。所有用户可见
	CreatedAt            time.Time
	DeletedAt            time.Time

	CompatibleRuntime []string `gorm:"-" json:"-" ` // 支持的运行时 最多5个
}

func (layer *Layer) Complement() {
	runtimeList := strings.Split(layer.CompatibleRuntimeStr, ",")
	layer.CompatibleRuntime = runtimeList
}

type LayerSample struct {
	Brn         string
	CodeSize    int64
	Layer       *Layer `json:"-"`
	Description string
	Version     int64
	LayerName   string
	Content     *LayerVersionContentOutput `json:",omitempty"`
}

type LayerVersionContentInput struct {
	BosBucket    string
	BosObject    string
	ZipFile      string
	ZipFileBytes []byte `json:"-"`
}

type LayerVersionContentOutput struct {
	CodeSha256 string
	CodeSize   int64
	Location   string
}

type PublishLayerVersionInput struct {
	CompatibleRuntimes []string
	Content            *LayerVersionContentInput `valid:"required"`
	Description        string                    `valid:"optional,runelength(0|256)"`
	LayerName          string                    `valid:"required,matches(^[a-zA-Z0-9-_]+$),runelength(0|140)"`
	LicenseInfo        string                    `valid:"optional,runelength(0|512)"`
	SourceTag          string                    `valid:"optional"`
	Version            int64                     `valid:"optional"` // 仅允许cfc迁移中心来源的layer使用
}

// Please also see https://docs.aws.amazon.com/goto/WebAPI/lambda-2015-03-31/PublishLayerVersionResponse
type PublishLayerVersionOutput struct {
	CompatibleRuntimes []string
	Content            *LayerVersionContentOutput
	CreatedDate        string
	Description        string
	LayerBrn           string
	LayerVersionBrn    string
	LicenseInfo        string
	Version            int64
}

type ListLayerVersionsInput struct {
	CompatibleRuntime string
	LayerName         string `valid:"required"`
	*ListCondition
}

type ListLayerVersionsOutput struct {
	LayerVersions []*LayerVersionsListItem
	NextMarker    string
	Total         int64
	PageNo        int64 `json:"pageNo"`
	PageSize      int64 `json:"pageSize"`
}

type ListCondition struct {
	PageNo   int64
	PageSize int64
	Marker   int64
	MaxItems int64
}

type ListLayersInput struct {
	CompatibleRuntime string
	*ListCondition
}

type ListLayersOutput struct {
	Layers     []*LayersListItem
	NextMarker string
	Total      int64
	PageNo     int64 `json:"pageNo"`
	PageSize   int64 `json:"pageSize"`
}

type LayersListItem struct {
	LatestMatchingVersion *LayerVersionsListItem
	LayerBrn              string
	LayerName             string
}

type LayerVersionsListItem struct {
	CompatibleRuntimes []string
	CreatedDate        string
	Description        string
	LayerVersionBrn    string
	LicenseInfo        string
	Version            int64
}

type GetLayerVersionOutput struct {
	CompatibleRuntimes []string
	Content            *LayerVersionContentOutput
	CreatedDate        string
	Description        string
	LayerBrn           string
	LayerVersionBrn    string
	LicenseInfo        string
	Version            int64
}

type ListLayersReturn struct {
	Layers     []*Layer
	NextMarker string `json:"omitempty"`
	Total      int64  `json:"omitempty"`
}
