package api

import "time"

type CreateOrderRequest struct {
	Region        string           `json:"region"`
	OrderType     string           `json:"orderType"` //NEW
	PaymentMethod []PaymentMethod  `json:"paymentMethod"`
	Items         []OrderItemModel `json:"items"`
}

// 调用billing新建订单传参
type CreatePackageOrderRequest struct {
	Region        string                  `json:"region"`
	OrderType     string                  `json:"orderType"` //NEW
	PaymentMethod []PaymentMethod         `json:"paymentMethod"`
	Items         []OrderPackageItemModel `json:"items"`
}

type PaymentMethod struct {
	Type   string   `json:"type"`
	Values []string `json:"values"`
}

type OrderItemModel struct {
	ServiceType   string          `json:"serviceType"`
	ProductType   string          `json:"productType"`
	PaymentMethod []PaymentMethod `json:"paymentMethod"`
	Key           string          `json:"key"`
	Count         int             `json:"count"`
	Flavor        []Flavor        `json:"flavor"`
}

// 资源包下单的item
type OrderPackageItemModel struct {
	ServiceType    string          `json:"serviceType"`
	ProductType    string          `json:"productType"`
	PaymentMethod  []PaymentMethod `json:"paymentMethod"`
	SubProductType string          `json:"subProductType"`
	Key            string          `json:"key"`
	Count          int             `json:"count"`
	Time           int             `json:"time"`
	TimeUnit       string          `json:"timeUnit"`
	Extra          string          `json:"extra"`
	Flavor         []Flavor        `json:"flavor"`
	PurchaseOrder  int             `json:"purchaseOrder"`
}

// 下单后返回给前端参数
type OrderList struct {
	Order []Order `json:"order"`
}

// 返回给前端：订单id和对应资源包
type Order struct {
	OrderId   string `json:"orderId"`   //订单id
	TypeName  string `json:"typeName"`  //资源包类型名称
	TypeValue string `json:"typeValue"` //资源包类型取值
	TypeScale int    `json:"typeScale"`
	SpecName  string `json:"specName"`  //资源包规格名称
	SpecValue string `json:"specValue"` ///资源包规格取值
	Specscale int    `json:"specscale"`
}

type Flavor struct {
	Name  string `json:"name"`
	Value string `json:"value"`
	Scale int    `json:"scale"`
}

// 资源包下单接受前端参数
type ResourcePackageOrder struct {
	CouponId    string       `json:"couponId"`    //代金劵号
	Count       int          `json:"count"`       //量包个数
	Disposable  bool         `json:"disposable"`  //是否一次性代金劵
	PackageType string       `json:"packageType"` // 资源包类型 默认为空，free表示免费包
	FlavorInfo  []FlavorInfo `json:"flavorInfo"`  //量包配置
}

// 前端资源包配置
type FlavorInfo struct {
	TypeName    string `json:"typeName"`
	TypeValue   string `json:"typeValue"`
	TypeScale   int    `json:"typeScale"`
	SpecName    string `json:"specName"`
	SpecValue   string `json:"specValue"`
	Specscale   int    `json:"specscale"`
	PackageType string `json:"packageType"` // 量包类型 默认为空，free表示免费资源包, prepay表示预付费资源包
	TimeUnit    string `json:"timeUnit"`    // 量包包周期 默认为YEAR，资源月包timeUnit为MONTH
	AutoRenew   bool   `json:"autoRenew"`   // 是否自动续费
}

// 创建订单时候错误返回
type Errormsg struct {
	Message string `json:"message"`
}

type NewOrderResponce struct {
	OrderId string `json:"orderId"`
}

type QueryOrderResponce struct {
	Uuid           string `json:"uuid"`
	OrderType      string `json:"orderType"`
	AccountId      string `json:"accountId"`
	UserId         string `json:"userId"`
	ServiceType    string `json:"serviceType"`
	ProductType    string `json:"productType"`
	SubProductType string `json:"subProductType"`
	Status         string `json:"status"`
}
type OrderDetailRequest struct {
	Uuid string `json:"uuid"`
}

// 查询资源包状态入参
type PackageInfoRequest struct {
	AccountId string   `json:"accountId"`
	OrderIds  []string `json:"orderIds"`
}

// 资源包状态返回
type PackageInfoResponse struct {
	Status      string `json:"status"`
	PackageName string `json:"packageName"`
}

// 订单返回
type OrderDetail struct {
	BaseOrderID                         interface{} `json:"baseOrderId"`
	Uuid                                string      `json:"uuid"`
	Type                                string      `json:"type"`
	SubOrderType                        string      `json:"subOrderType"`
	GroupID                             string      `json:"groupId"`
	InternalStatus                      string      `json:"internalStatus"`
	AccountID                           string      `json:"accountId"`
	UserID                              string      `json:"userId"`
	ServiceType                         string      `json:"serviceType"`
	ProductType                         string      `json:"productType"`
	SubProductType                      string      `json:"subProductType"`
	Items                               []Items     `json:"items"`
	Price                               float64     `json:"price"`
	Status                              string      `json:"status"`
	CreateTime                          time.Time   `json:"createTime"`
	PurchaseTime                        interface{} `json:"purchaseTime"`
	UpdateTime                          time.Time   `json:"updateTime"`
	ActiveTime                          interface{} `json:"activeTime"`
	ResourceIds                         interface{} `json:"resourceIds"`
	PayChannel                          string      `json:"payChannel"`
	SpecificType                        int         `json:"specificType"`
	BatchID                             int         `json:"batchId"`
	Source                              string      `json:"source"`
	PayExpireTime                       time.Time   `json:"payExpireTime"`
	CampaignID                          string      `json:"campaignId"`
	OperatorName                        interface{} `json:"operatorName"`
	CashEquivalentCouponFavourablePrice float64     `json:"cashEquivalentCouponFavourablePrice"`
	CatalogPrice                        float64     `json:"catalogPrice"`
	Discount                            float64     `json:"discount"`
	FavourablePrice                     float64     `json:"favourablePrice"`
	DiscountCouponFavourablePrice       float64     `json:"discountCouponFavourablePrice"`
}

// 订单详情返回
type OrderDetailResponse struct {
	BaseOrderID                         interface{}     `json:"baseOrderId"`
	Uuid                                string          `json:"uuid"`
	Type                                string          `json:"type"`
	SubOrderType                        string          `json:"subOrderType"`
	GroupID                             string          `json:"groupId"`
	InternalStatus                      string          `json:"internalStatus"`
	AccountID                           string          `json:"accountId"`
	UserID                              string          `json:"userId"`
	ServiceType                         string          `json:"serviceType"`
	ProductType                         string          `json:"productType"`
	SubProductType                      string          `json:"subProductType"`
	Items                               []ItemsResponse `json:"items"`
	Price                               float64         `json:"price"`
	Status                              string          `json:"status"`
	CreateTime                          time.Time       `json:"createTime"`
	PurchaseTime                        interface{}     `json:"purchaseTime"`
	UpdateTime                          time.Time       `json:"updateTime"`
	ActiveTime                          interface{}     `json:"activeTime"`
	ResourceIds                         interface{}     `json:"resourceIds"`
	PayChannel                          string          `json:"payChannel"`
	SpecificType                        int             `json:"specificType"`
	BatchID                             int             `json:"batchId"`
	Source                              string          `json:"source"`
	PayExpireTime                       time.Time       `json:"payExpireTime"`
	CampaignID                          string          `json:"campaignId"`
	OperatorName                        interface{}     `json:"operatorName"`
	CashEquivalentCouponFavourablePrice float64         `json:"cashEquivalentCouponFavourablePrice"`
	CatalogPrice                        float64         `json:"catalogPrice"`
	Discount                            float64         `json:"discount"`
	FavourablePrice                     float64         `json:"favourablePrice"`
	DiscountCouponFavourablePrice       float64         `json:"discountCouponFavourablePrice"`
}

type FlavorDetail struct {
	Name  string `json:"name"`
	Value string `json:"value"`
	Scale int    `json:"scale"`
}
type Payment struct {
	DiscountRate          int           `json:"discountRate"`
	Coupons               []interface{} `json:"coupons"`
	DiscountCoupons       []interface{} `json:"discountCoupons"`
	CashEquivalentCoupons []interface{} `json:"cashEquivalentCoupons"`
}
type CurrentOriginPrice struct {
	Price        float64 `json:"price"`
	CatalogPrice float64 `json:"catalogPrice"`
	PriceID      int     `json:"priceId"`
	PriceType    string  `json:"priceType"`
	PriceName    string  `json:"priceName"`
}
type TargetPrice struct {
	Price        float64 `json:"price"`
	CatalogPrice float64 `json:"catalogPrice"`
	PriceID      int     `json:"priceId"`
	PriceType    string  `json:"priceType"`
	PriceName    string  `json:"priceName"`
}
type DetailPriceMessage struct {
	MultiYearDiscountOrder bool               `json:"multiYearDiscountOrder"`
	UseMultiYearPrice      bool               `json:"useMultiYearPrice"`
	CurrentOriginPrice     CurrentOriginPrice `json:"currentOriginPrice"`
	TargetPrice            TargetPrice        `json:"targetPrice"`
}
type PricingDetail struct {
	Cpt2Price          interface{}        `json:"cpt2Price"`
	Cpt1Price          interface{}        `json:"cpt1Price"`
	Cpt3Price          interface{}        `json:"cpt3Price"`
	CpcPrice           interface{}        `json:"cpcPrice"`
	Policy             interface{}        `json:"policy"`
	CustomPrice        bool               `json:"customPrice"`
	AlterPriceList     interface{}        `json:"alterPriceList"`
	DetailPriceMessage DetailPriceMessage `json:"detailPriceMessage"`
}
type Items struct {
	AlterCount              int            `json:"alterCount"`
	CatalogPrice            float64        `json:"catalogPrice"`
	ChargePlanStartTime     interface{}    `json:"chargePlanStartTime"`
	ChargeStartTime         interface{}    `json:"chargeStartTime"`
	CombinedServiceType     interface{}    `json:"combinedServiceType"`
	Count                   int            `json:"count"`
	DiscountWithCouponPrice float64        `json:"discountWithCouponPrice"`
	Extra                   string         `json:"extra"`
	Flavor                  []FlavorDetail `json:"flavor"`
	IsOnceCharge            bool           `json:"isOnceCharge"`
	Key                     string         `json:"key"`
	PaymentMethod           Payment        `json:"paymentMethod"`
	Price                   float64        `json:"price"`
	PricingDetail           PricingDetail  `json:"pricingDetail"`
	ProductType             string         `json:"productType"`
	RealUnitPrice           float64        `json:"realUnitPrice"`
	Region                  string         `json:"region"`
	ReleaseTime             interface{}    `json:"releaseTime"`
	ResourceActiveTime      interface{}    `json:"resourceActiveTime"`
	ResourceIDAndFees       interface{}    `json:"resourceIdAndFees"`
	ResourceIds             interface{}    `json:"resourceIds"`
	ResourceMappings        interface{}    `json:"resourceMappings"`
	ServiceType             string         `json:"serviceType"`
	Status                  interface{}    `json:"status"`
	SubItems                interface{}    `json:"subItems"`
	SubProductType          string         `json:"subProductType"`
	Time                    int            `json:"time"`
	TimeUnit                string         `json:"timeUnit"`
	UnitPrice               float64        `json:"unitPrice"`
}

type ItemsResponse struct {
	AlterCount              int            `json:"alterCount"`
	CatalogPrice            float64        `json:"catalogPrice"`
	ChargePlanStartTime     interface{}    `json:"chargePlanStartTime"`
	ChargeStartTime         interface{}    `json:"chargeStartTime"`
	CombinedServiceType     interface{}    `json:"combinedServiceType"`
	Count                   int            `json:"count"`
	DiscountWithCouponPrice float64        `json:"discountWithCouponPrice"`
	Extra                   string         `json:"extra"`
	Flavor                  []FlavorDetail `json:"flavor"`
	IsOnceCharge            bool           `json:"isOnceCharge"`
	Key                     string         `json:"key"`
	PaymentMethod           Payment        `json:"paymentMethod"`
	Price                   float64        `json:"price"`
	PricingDetail           PricingDetail  `json:"pricingDetail"`
	ProductType             string         `json:"productType"`
	RealUnitPrice           float64        `json:"realUnitPrice"`
	Region                  string         `json:"region"`
	ReleaseTime             interface{}    `json:"releaseTime"`
	ResourceActiveTime      interface{}    `json:"resourceActiveTime"`
	ResourceIDAndFees       interface{}    `json:"resourceIdAndFees"`
	ResourceIds             interface{}    `json:"resourceIds"`
	ResourceMappings        interface{}    `json:"resourceMappings"`
	ServiceType             string         `json:"serviceType"`
	Status                  interface{}    `json:"status"`
	SubItems                interface{}    `json:"subItems"`
	SubProductType          string         `json:"subProductType"`
	Time                    int            `json:"time"`
	TimeUnit                string         `json:"timeUnit"`
	UnitPrice               float64        `json:"unitPrice"`
	UnitCount               int            `json:"unitCount"`
	Configuration           []string       `json:"configuration"`
	ChargeType              []string       `json:"chargeType"`
	UnitPriceShow           string         `json:"unitPriceShow"`
}

// 查询资源包接收前端参数
type QueryPackageInfo struct {
	ServiceType      string   `json:"serviceType"`
	Region           string   `json:"region"`
	PageNo           int      `json:"pageNo"`
	PageSize         int      `json:"pageSize"`
	Status           string   `json:"status"`
	DeductInstanceId string   `json:"deductInstanceId"`
	PackageType      []string `json:"packageType"`
	DeductPolicy     string   `json:"deductPolicy"`
	OrderBy          string   `json:"orderBy"`
	Order            string   `json:"order"`
}

// 查询资源包请求billing参数
type QueryPackageInfoRequest struct {
	AccountId        string   `json:"accountId"`
	ServiceType      string   `json:"serviceType"`
	Region           string   `json:"region"`
	PageNo           int      `json:"pageNo"`
	PageSize         int      `json:"pageSize"`
	Status           string   `json:"status"`
	DeductInstanceId string   `json:"deductInstanceId"`
	PackageType      []string `json:"packageType"`
	DeductPolicy     string   `json:"deductPolicy"`
	OrderBy          string   `json:"orderBy"`
	Order            string   `json:"order"`
}

// 查询资源包请求billing参数(默认全部字段)
type QueryPackageInfoRequestAllStatus struct {
	AccountId        string   `json:"accountId"`
	ServiceType      string   `json:"serviceType"`
	Region           string   `json:"region"`
	PageNo           int      `json:"pageNo"`
	PageSize         int      `json:"pageSize"`
	DeductInstanceId string   `json:"deductInstanceId"`
	PackageType      []string `json:"packageType"`
	DeductPolicy     string   `json:"deductPolicy"`
	OrderBy          string   `json:"orderBy"`
	Order            string   `json:"order"`
}

// 查询资源包请求billing返回参数
type QueryPackageInfoResponse struct {
	TotalCount int      `json:"totalCount"`
	PageNo     int      `json:"pageNo"`
	PageSize   int      `json:"pageSize"`
	OrderBy    string   `json:"orderBy"`
	Order      string   `json:"order"`
	Result     []Result `json:"result"`
}
type Result struct {
	Id                  int                    `json:"id"`
	AccountId           string                 `json:"accountId"`
	Capacity            float64                `json:"capacity"`
	UsedCapacity        float64                `json:"usedCapacity"`
	ServiceType         string                 `json:"serviceType"`
	OrderId             string                 `json:"orderId"`
	OrderItemKey        string                 `json:"orderItemKey"`
	PackageName         string                 `json:"packageName"`
	ActiveTime          time.Time              `json:"activeTime"`
	ExpireTime          time.Time              `json:"expireTime"`
	CreateTime          time.Time              `json:"createTime"`
	Status              string                 `json:"status"`
	PackageType         string                 `json:"packageType"`
	PackageClassifyType int                    `json:"packageClassifyType"`
	DeductInstanceId    string                 `json:"deductInstanceId"`
	Region              string                 `json:"region"`
	DeductPolicy        string                 `json:"deductPolicy"`
	PackageShortId      string                 `json:"packageShortId"`
	PackageProperty     string                 `json:"packageProperty"`
	NeedThresholdNotify bool                   `json:"needThresholdNotify"`
	PackageSegVOList    []PackageSegVO         `json:"packageSegVOList"`
	AutoRenew           string                 `json:"autoRenew"`
	AutoRenewRule       QueryAutoRenewResponse `json:"-"`
}

// PackageSegVO 添加定额量包信息
type PackageSegVO struct {
	AccountId    string    `json:"accountId"`
	PackageName  string    `json:"packageName"`
	ReferId      string    `json:"referId"`
	Capacity     float64   `json:"capacity"`
	UsedCapacity float64   `json:"usedCapacity"`
	ServiceType  string    `json:"serviceType"`
	Region       string    `json:"region"`
	ActiveTime   time.Time `json:"activeTime"`
	ExpireTime   time.Time `json:"expireTime"`
	Status       string    `json:"status"`
	ExpireNotify int       `json:"expireNotify"`
	Modify       bool      `json:"modify"`
}

type OrderListResquest struct {
	ServiceType  string `json:"serviceType"`
	AccountId    string `json:"accountId"`
	Status       string `json:"status"`
	ProductType  string `json:"productType"`
	ResourceUuid string `json:"resourceUuid"`
}

type OrderResp struct {
	RequestID string `json:"requestId"`
	Code      string `json:"code"`
	Message   string `json:"message"`
}
type RealNameQualification struct {
	QualifyType              string `json:"qualifyType"`
	Status                   string `json:"status"`
	Name                     string `json:"name"`
	CorporationNum           string `json:"corporationNum"`
	ExemptStatus             string `json:"exemptStatus"`
	ExemptTime               int64  `json:"exemptTime"`
	PrimePassFlag            bool   `json:"primePassFlag"`
	SeniorPassFlag           bool   `json:"seniorPassFlag"`
	PersonalPrimePassFlag    bool   `json:"personalPrimePassFlag"`
	PersonalSeniorPassFlag   bool   `json:"personalSeniorPassFlag"`
	EnterprisePrimePassFlag  bool   `json:"enterprisePrimePassFlag"`
	EnterpriseSeniorPassFlag bool   `json:"enterpriseSeniorPassFlag"`
	AuditPassTime            int64  `json:"auditPassTime"`
}

type UpdateOrderResquest struct {
	Status    string      `json:"status"`
	Resources []Resources `json:"resources"`
}

type Resources struct {
	Key    string `json:"key"`
	Id     string `json:"id"`
	Status string `json:"status"`
}

type QueryOrderListResponse struct {
	Size   int                 `json:"size"`
	Begin  int                 `json:"begin"`
	Orders []OrderListResponse `json:"orders"`
}
type OrderListResponse struct {
	Uuid        string       `json:"uuid"`
	ResourceIds []string     `json:"resourceIds"`
	Status      string       `json:"status"`
	AccountId   string       `json:"accountId"`
	Items       []OrderItems `json:"items"`
}

type OrderItems struct {
	Region string `json:"region"`
}

type OrderResourceRequest struct {
	Region      string `json:"region"`
	Status      string `json:"status"`
	ProductType string `json:"productType"`
}

type UpdatePrepayResourceRequest struct {
	AccountId     string                `json:"accountId"`
	OrderId       string                `json:"orderId"`
	OrderStatus   string                `json:"orderStatus"`
	PackageStatus string                `json:"packageStatus"`
	PackageInfo   []PackageInfoResponse `json:"packageInfo"`
}

type CreateAutoRenewRequest struct {
	AccountId     string   `json:"accountId"`
	Region        string   `json:"region"`
	ServiceType   string   `json:"serviceType"`
	ServiceIds    []string `json:"serviceIds"`
	RenewTimeUnit string   `json:"renewTimeUnit"`
	RenewTime     int      `json:"renewTime"`
}

type QueryAutoRenewResponse struct {
	Uuid          string    `json:"uuid"`          // 自动续费规则ID
	AccountId     string    `json:"accountId"`     // 用户ID
	ServiceId     string    `json:"serviceId"`     // 资源ID
	ServiceType   string    `json:"serviceType"`   // 服务类型,如CFC
	RenewTimeUnit string    `json:"renewTimeUnit"` // 每次的续费周期，相当于续费单位，支持month，year
	RenewTime     int       `json:"renewTime"`     //每次续费时长
	Region        string    `json:"region"`        // 地域
	CreateTime    time.Time `json:"createTime"`    // 创建时间
	UpdateTime    time.Time `json:"updateTime"`    // 更新时间
	DeleteTime    time.Time `json:"deleteTime"`    // 删除时间
}

type AutoRenewResource struct {
	AccountId   string `json:"accountId"`
	ServiceType string `json:"serviceType"`
	ServiceId   string `json:"serviceId"`
	Region      string `json:"region"`
}

type OrderIDs struct {
	OrderIds []string `json:"orderIds"`
}
