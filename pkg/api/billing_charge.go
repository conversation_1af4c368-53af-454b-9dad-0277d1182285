package api

const ChargeItemFunctionCallCount = "FunctionCallCount"
const ChargeItemFunctionRunTimeCount = "FunctionRunTimeCount"
const ChargeItemPublicDataTransfer = "PublicDataTransfer"

var ChargeItems = []string{ChargeItemFunctionCallCount, ChargeItemFunctionRunTimeCount, ChargeItemPublicDataTransfer}
var ChargeItemUintMap = map[string]string{
	ChargeItemFunctionCallCount:    "次",
	ChargeItemFunctionRunTimeCount: "GB*秒",
	ChargeItemPublicDataTransfer:   "Bytes",
}

type BillingResourceQueryRequest struct {
	AccountId   string    `json:"accountId"`
	ServiceName string    `json:"serviceName"`
	Region      string    `json:"region"`
	ChargeItem  string    `json:"chargeItem"`
	StartTime   string    `json:"startTime"`
	EndTime     string    `json:"endTime"`
	UsageMeta   UsageMeta `json:"usageMeta"`
}

type BillingResourceQueryResponce []ResourceUsageResult

type UsageMeta struct {
	MinuteReducer string `json:"minuteReducer"`
	DayReducer    string `json:"dayReducer"`
	MonthReducer  string `json:"monthReducer"`
	RemoveZero    bool   `json:"removeZero"`
}

type ResourceUsageResult struct {
	Amount   float64  `json:"amount"`
	Unit     string   `json:"unit"`
	TimeSpan TimeSpan `json:"timeSpan"`
}

type TimeSpan struct {
	StartTime string `json:"startTime"`
	EndTime   string `json:"endTime"`
}

type GetResourceResp struct {
	Usage     map[string]Usage `json:"usage"`
	StartTime string           `json:"startTime"`
	EndTime   string           `json:"endTime"`
}

type Usage struct {
	Amount float64 `json:"amount"`
	Unit   string  `json:"unit"`
}
