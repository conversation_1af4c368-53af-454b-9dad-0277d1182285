package api

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func testNewRequestLabels() RequestLabels {
	return RequestLabels{
		LabelUserID:      "1",
		LabelVpcConfigID: "2",
	}
}

func TestRequestLabelsCopy(t *testing.T) {
	var labels RequestLabels

	labels = nil
	labels.Copy(nil)
	assert.Nil(t, labels)

	srcLabels := testNewRequestLabels()
	labels = nil
	labels.Copy(srcLabels)
	assert.EqualValues(t, srcLabels, labels)

	labels = testNewRequestLabels()
	labels.Copy(nil)
	assert.EqualValues(t, testNewRequestLabels(), labels)

	srcLabels = RequestLabels{LabelUserID: "meme"}
	labels = testNewRequestLabels()
	labels.Copy(srcLabels)
	for key, value := range srcLabels {
		assert.Equal(t, value, labels[key])
	}
}

func TestRequestLabelsGet(t *testing.T) {
	var labels RequestLabels
	var value string

	labels = nil
	value = labels.Get(LabelUserID)
	assert.Equal(t, "", value)

	labels = testNewRequestLabels()
	value = labels.Get(LabelUserID)
	assert.Equal(t, testNewRequestLabels()[LabelUserID], value)
}

func TestRequestLabelsSet(t *testing.T) {
	var labels RequestLabels

	labels = nil
	labels.Set(LabelUserID, "999")
	assert.Equal(t, "999", labels[LabelUserID])

	labels = testNewRequestLabels()
	labels.Set(LabelUserID, "999")
	assert.Equal(t, "999", labels[LabelUserID])
}

func TestRequestLabelsGetLabels(t *testing.T) {
	labels := RequestLabels{LabelUserID: "user1", LabelVpcConfigID: "vpc1", LabelVipUser: "vip"}
	nodeLabels, err := labels.GetLabels(ServiceTypeCFC, LabelTypeNode)
	assert.Nil(t, err)
	assert.Equal(t, labels[LabelUserID], nodeLabels[LabelUserID])
	assert.Equal(t, labels[LabelVpcConfigID], nodeLabels[LabelVpcConfigID])
	assert.Equal(t, "", nodeLabels[LabelVipUser])

	clusterLabels, err := labels.GetLabels(ServiceTypeCFC, LabelTypeCluster)
	assert.Nil(t, err)
	assert.Equal(t, RequestLabels{LabelVipUser: "vip"}, clusterLabels)

	podLabels, err := labels.GetLabels(ServiceTypeCFC, LabelTypePod)
	assert.Nil(t, err)
	assert.Equal(t, RequestLabels{}, podLabels)

	labels = nil
	clusterLabels, err = labels.GetLabels(ServiceTypeOTE, LabelTypeCluster)
	assert.Nil(t, err)
	assert.EqualValues(t, RequestLabels{}, clusterLabels)

	clusterLabels, err = labels.GetLabels(ServiceTypeCFC, LabelTypeNode)
	assert.NotNil(t, err)
}

func TestRequestLabelsSortedLabels(t *testing.T) {
	labels := RequestLabels{"a": "15", "c": "2", "b": "10"}
	assert.EqualValues(t, "15,10,2", labels.SortedLabels())

	labels = nil
	assert.EqualValues(t, "", labels.SortedLabels())
}

func TestGetLabelKeysByServiceType(t *testing.T) {
	keys, err := GetLabelKeysByServiceType(ServiceTypeCFC)
	assert.Equal(t, cfcLabelsKeys, keys)
	assert.Nil(t, err)

	keys, err = GetLabelKeysByServiceType(ServiceTypeOTE)
	assert.Equal(t, oteLabelsKeys, keys)
	assert.Nil(t, err)

	keys, err = GetLabelKeysByServiceType("fake service")
	assert.Nil(t, keys)
	assert.NotNil(t, err)
}

func TestRequestLabels_Equal(t *testing.T) {
	testCase := []struct {
		labelA RequestLabels
		labelB RequestLabels
		want   bool
	}{
		{
			labelA: RequestLabels{LabelUserID: "user1", LabelVpcConfigID: "vpc1"},
			labelB: RequestLabels{LabelUserID: "user1", LabelVpcConfigID: "vpc1"},
			want:   true,
		},
		{
			labelA: RequestLabels{LabelUserID: "user1", LabelVpcConfigID: "vpc1"},
			labelB: RequestLabels{LabelUserID: "user1", LabelVpcConfigID: "vpc2"},
			want:   false,
		},
		{
			labelA: RequestLabels{LabelUserID: "user1", LabelVpcConfigID: "vpc1"},
			labelB: RequestLabels{LabelUserID: "user1", LabelContainerType: "sercurecontainer"},
			want:   false,
		},
		{
			labelA: RequestLabels{LabelUserID: "user1", LabelVpcConfigID: "vpc1"},
			labelB: RequestLabels{},
			want:   false,
		},
		{
			labelA: RequestLabels{},
			labelB: RequestLabels{},
			want:   true,
		},
		{
			labelA: nil,
			labelB: nil,
			want:   true,
		},
		{
			labelA: nil,
			labelB: RequestLabels{},
			want:   true,
		},
		{
			labelA: nil,
			labelB: RequestLabels{LabelUserID: "user1", LabelVpcConfigID: "vpc1"},
			want:   false,
		},
	}
	for _, test := range testCase {
		got := test.labelA.Equal(test.labelB)
		assert.Equal(t, test.want, got)
	}
}
