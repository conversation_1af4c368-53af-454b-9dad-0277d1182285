package api

import (
	"encoding/base64"
	"strings"
	"time"

	"icode.baidu.com/baidu/faas/kun/pkg/bce/credential"
	"icode.baidu.com/baidu/faas/kun/pkg/bce/sts_credential"
	"icode.baidu.com/baidu/faas/kun/pkg/util/json"
)

const (
	CfcInvokeBillHeader = "X-Bd-Cfc-Invokebill"
	CfcStatisticHeader  = "X-Bd-Cfc-Statistic"
	CfcNetTrafficHeader = "X-Bd-Cfc-Nettraffic"
)

// InvocationInput function call input param
type InvocationInput struct {

	// invoker client host
	Host string
	// ContainerID xxx
	ContainerID string

	// PodName xxx
	PodName string

	// RequestID xxx
	RequestID string

	// NodeID xxx
	NodeID string

	// IP xxx
	FloatingIP string

	// User xxx
	User *credential.User

	// The object for the Lambda function location.
	Code *FunctionCodeLocation

	// A complex type that describes function metadata.
	Configuration *FunctionConfiguration

	// 日志配置
	LogConfig *LogConfiguration

	// STS Token信息
	Credential *sts_credential.StsCredential

	// LogType is a string.
	// You can set this optional parameter to Tail in the request only if
	// you specify the InvocationType parameter with value RequestResponse.
	// In this case, AWS Lambda returns the base64-encoded last 4 KB of log data
	// produced by your Lambda function in the x-amz-log-result header.
	// Valid Values: None | Tail
	LogType LogType

	// ClientContext is a json string.
	// Using ClientContext you can pass client-specific information to the function
	// you are invoking. You can then process the client information in your function
	// as you choose through the context variable.
	ClientContext string

	// EventBody xxx
	EventBody        string
	MCP              bool
	MCPSessionIDChan chan string `json:"-"`
	// 记录原请求的触发器类型
	Trigger          TriggerType      
}

// LogType is the alias of string
type LogType string

const (
	LogTypeNone = "None"
	LogTypeTail = "Tail"
)

// Valid check if logtype is valid
func (t LogType) Valid() bool {
	if strings.EqualFold(string(t), LogTypeNone) ||
		strings.EqualFold(string(t), LogTypeTail) {
		return true
	}
	return false
}

// IsLogTypeTail check if logtype is Tail
func (t LogType) IsLogTypeTail() bool {
	return strings.EqualFold(string(t), LogTypeTail)
}

// InvocationType is the alias of string
type InvocationType string

const (
	InvocationTypeEvent           = "Event"
	InvocationTypeEventRetry      = "EventRetry" // Event类型请求mqhub 失败后，同步重试类型
	InvocationTypeRequestResponse = "RequestResponse"
	InvocationTypeDryRun          = "DryRun"
	InvocationTypeMqhub           = "Mqhub"

	ReservedTypePod = "pod"
)

// InvocationOutput function call output param
type InvocationOutput struct {
	FuncResult string `json:"result,omitempty"`
	// sse第一次返回数据之后，无法在设置head，通过body返回
	InvokeBillInfo  string      `json:"invokeBillInfo,omitempty"`
	StatisticInfo   string      `json:"statisticInfo,omitempty"`
	NetTrafficInfos []string    `json:"netTrafficInfos,omitempty"`
	LogMessage      []string    `json:"log"`
	FuncError       string      `json:"errtype,omitempty"`
	ErrorInfo       string      `json:"errinfo,omitempty"`
	DurationMap     DurationMap `json:"durationMap"`
}
type LogEntry struct {
	Timestamp time.Time
	Message   string
}

type RequestStatus int

const (
	StatusNormal RequestStatus = iota
	StatusRuning
	StatusSuccess
	StatusFailed
	StatusTimeout
	StatusConcurrencyExceed
)

const InvokeTimeout = "Invoke timeout."
const InvokeConcurrencyExceed = "Invoke concurrency Exceed"

type RequestInfo struct {
	ID                int64
	RequestID         string
	RuntimeID         string
	InvokeStartTimeMS int64 // 接收到请求时间
	InitStartTimeMS   int64 // runtime初始化请求时间
	InitDoneTimeMS    int64
	InvokeDoneTimeMS  int64 // 结束请求时间
	MaxMemUsedBytes   int64
	BilledTimeMS      int64 // 计费时长
	MemorySpecSize    int64 // 内存规格大小

	Credential *sts_credential.StsCredential

	Status RequestStatus
	Input  *InvocationInput
	Output InvocationOutput
}

type RuntimeInfo struct {
	RowID          int64  `json:"-"`
	RuntimeID      string `json:"PodName"`
	UserID         string `json:"UserID"` // 用户ID，用于数据上报
	PreLoadTimeMS  int64  `json:"PreLoadTimeMS"`
	PostLoadTimeMS int64  `json:"PostLoadTimeMS"`
	PreInitTimeMS  int64  `json:"PreInitTimeMS"`
	PostInitTimeMS int64  `json:"PostInitTimeMS"`
	AcceptReqCnt   int64  `json:"AcceptReqCnt"`
	RejectReqCnt   int64  `json:"RejectReqCnt"`
	LastAccessTime int64  `json:"LastAccessTime"`
	CommitID       string `json:"CommitID"`
	MemorySize     int64  `json:"MemorySize"`
	RequestID      string `json:"RequestID,omitempty"`
	ConcurrentMode bool
	ServiceType    ServiceType
	Labels         RequestLabels // eventhub传入，相当于请求的环境变量
}

type StatisticInfo struct {
	UserID     string `json:"userid"`
	RequestID  string `json:"reqid,omitempty"`
	Region     string `json:"region"`
	Function   string `json:"function"`
	Version    string `json:"version"`
	StartTime  int64  `json:"startms"`
	Duration   int64  `json:"duration"`
	MemoryUsed int64  `json:"memused"`
	StatusCode int    `json:"status"`
}

func (si *StatisticInfo) Encode() string {
	data, _ := json.Marshal(si)
	return base64.StdEncoding.EncodeToString(data)
}

func (si *StatisticInfo) Decode(str string) error {
	data, err := base64.StdEncoding.DecodeString(str)
	if err != nil {
		return err
	}
	err = json.Unmarshal(data, si)
	if err != nil {
		return err
	}
	return nil
}

// 账单数据分三部分：调用计数+内存使用+带宽使用
type NetTrafficInfo struct {
	UserID   string `json:"userid"`
	InBytes  int64  `json:"netin,omitempty"`
	OutBytes int64  `json:"netout,omitempty"`
}

func (ti *NetTrafficInfo) Encode() string {
	data, _ := json.Marshal(ti)
	return base64.StdEncoding.EncodeToString(data)
}

func (ti *NetTrafficInfo) Decode(str string) error {
	data, err := base64.StdEncoding.DecodeString(str)
	if err != nil {
		return err
	}
	err = json.Unmarshal(data, ti)
	if err != nil {
		return err
	}
	return nil
}

type InvokeBillInfo struct {
	UserID       string `json:"userid"`
	FunctionName string `json:"functionName"`
	Count        int64  `json:"count"`
	MemUsage     int64  `json:"memuse"`
	BillTime     int64  `json:"billms"`
}

func (bi *InvokeBillInfo) Encode() string {
	data, _ := json.Marshal(bi)
	return base64.StdEncoding.EncodeToString(data)
}

func (bi *InvokeBillInfo) Decode(str string) error {
	data, err := base64.StdEncoding.DecodeString(str)
	if err != nil {
		return err
	}
	err = json.Unmarshal(data, bi)
	if err != nil {
		return err
	}
	return nil
}

type InvocationStatistic struct {
	Statistic  *StatisticInfo
	BillInfo   *InvokeBillInfo
	NetTraffic []*NetTrafficInfo
}
