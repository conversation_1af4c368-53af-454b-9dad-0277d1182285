package api

import (
	"sync"
)

const (
	// QueryVIP proxyctl 传给 proxyagent 的创建 mgre0 的 local IP，当使用 keepalived 时这里传 vip，不使用时传 proxy Node 实际的 ip
	QueryVIP = "vip"

	// QueryENIMACAddr eni 弹性网卡的 mac 地址
	QueryENIMACAddr = "eniMacAddr"

	// QueryPeerNodeIP 函数占用的 Pod 所在 Node 的 IP
	QueryPeerNodeIP = "peerNodeIP"

	// QueryPeerTunnelIP 函数占用的Pod 所在 Node 上新建的 gre tunnel 设备的 ip
	QueryPeerTunnelIP = "peerTunnelIP"

	// VpcCidr 用户配置的 vpc 的网段
	VpcCidr = "vpcCidr"
)

type NotifyOccupyRequest struct {
	RequestId  string
	ProxyHost  string
	ProxyVIP   string
	EniMacAddr string
	Cidr       string
}

type NotifyReleaseRequest struct {
	RequestId string
	ProxyHost string
}

type CheckHealthRequest struct {
	RequestId  string
	ProxyHost  string
	EniMacAddr string
}

type AddTunnelPeerRequest struct {
	RequestId    string
	ProxyHost    string // 要访问的 proxy node 的 floatingIP
	PeerNodeIP   string // 在proxy node内设置的隧道peer的node ip，即这个node的vpc内ip
	PeerTunnelIP string // 在proxy node内设置的隧道peer的gre tunnel ip，即这个node临时占用的gre设备ip
}

type RemoveTunnelPeerRequest struct {
	AddTunnelPeerRequest
}

type ProxyNodeStatus string

const (
	ProxyNodeStatusPreparing ProxyNodeStatus = "preparing" // 从 Node 开始创建到 proxyAgent 能够被访问
	ProxyNodeStatusAvailable ProxyNodeStatus = "available" // Node 初始化好，等待被用户占用
	ProxyNodeStatusInuse     ProxyNodeStatus = "inuse"
	ProxyNodeStatusObsolete  ProxyNodeStatus = "obsolete"
)

type EniStatus string

const (
	ENIStatusAvailable EniStatus = "available" // 从 Node 开始创建到 proxyAgent 能够被访问
	ENIStatusInuse     EniStatus = "inuse"     // Node 初始化好，等待被用户占用
	ENIStatusAttaching EniStatus = "attaching"
	ENIStatusDetaching EniStatus = "detaching"
)

type ProxyNodeInfo struct {
	ID                 string // GenerateID() 生成的唯一ID
	CceClusterUUID     string
	CceInstanceID      string
	BCCInstanceShortID string
	Status             ProxyNodeStatus
	FloatingIP         string
	FixIP              string
	EniMacAddr         string
	EniID              string
	EniStatus          EniStatus
	UserID             string
	VpcConfig          *VpcConfig
	VpcCidr            string

	mutex sync.Mutex
}

func (p *ProxyNodeInfo) Lock() {
	p.mutex.Lock()
}

func (p *ProxyNodeInfo) Unlock() {
	p.mutex.Unlock()
}

func (p *ProxyNodeInfo) Key() string {
	return p.ID
}

type NetworkConfig struct {
	Uid          string     `gorm:"column:uid"`            // 用户 id
	FunctionName string     `gorm:"column:function_name"`  // 函数名称
	Brn          string     `gorm:"column:brn"`            // 函数 brn
	VpcConfig    *VpcConfig `gorm:"column:-"`              // vpc 原始配置
	VpcConfigStr *string    `gorm:"column:vpc_config_str"` // 序列化成字符串的配置信息
	Status       *string    `gorm:"column:status"`         // "ready" or "notready"，表示 vpc proxy 是否已准备好

	// vpc 网段，用于调度 pod 时避免 pod 容器网络与 vpcCidr 在同一个网段，目前未开启
	VpcCidr *string `gorm:"column:vpc_cidr" json:"VpcCidr"`

	// proxy node 在 vpc 子网的 ip 地址，如 *************， 可能是一个 node 实际的 ip 或 virtual ip（开启主备 proxy node 时）
	// funclet 在 node 上根据此 ip 设置 vpc 隧道信息
	ProxyInternalIP *string `gorm:"column:proxy_internal_ip"`

	// proxy node 的 floatingIp，poolmgr 通过这个 ip 访问 proxy node 上的 agent 来创建隧道设备
	ProxyFloatingIP *string `gorm:"column:proxy_floating_ip"`

	// 以下参数目前不生效
	EnableSlave bool `gorm:"column:enable_slave"` // 是否启动主备 proxy node
}
