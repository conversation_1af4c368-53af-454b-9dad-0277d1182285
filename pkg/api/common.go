package api

import "strings"

//解析小程序云的查询参数 Filter
func ParseCBDFilter(filter string) (string, string) {
	var (
		functionName, description string
		res                       = make(map[string]string, 0)
	)

	filterSlice := strings.Split(filter, ",")
	for _, val := range filterSlice {
		filterTmp := strings.Split(val, ":")
		if len(filterTmp) > 1 {
			res[filterTmp[0]] = filterTmp[1]
		}

	}

	if v, ok := res["FunctionName"]; ok {
		functionName = v
	}

	if v, ok := res["Description"]; ok {
		description = v
	}
	return functionName, description
}

//解析小程序云的查询参数 Order
func ParseCBDOrder(order string) map[string]string {
	res := make(map[string]string, 0)
	if order == "" {
		res["CreatedAt"] = "desc"
		return res
	} else {
		orderSlice := strings.Split(order, ",")
		for _, val := range orderSlice {
			if val != "" {
				orderTmp := strings.Split(val, ":")
				if len(orderTmp) <= 1 {
					res[orderTmp[0]] = "desc"
				} else {
					if orderTmp[1] == "" {
						orderTmp[1] = "desc"
					}
					res[orderTmp[0]] = orderTmp[1]
				}
			}
		}

	}
	return res
}
