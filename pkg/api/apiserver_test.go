package api

import (
	"encoding/json"
	"testing"

	"github.com/elliotchance/testify-stats/assert"
)

func TestVpcConfigMarshal(t *testing.T) {
	vc := &VpcConfig{
		VpcID: "id-123",
		SubnetIDs: []string{
			"sbnxisa-a",
			"sbnacbd",
			"sbnsjum1",
			"sbns891",
		},
		SecurityGroupIDs: []string{
			"semcli",
			"secAbcd",
		},
	}
	res, err := json.Marshal(vc)
	assert.Nil(t, err)

	t.Log(string(res))
	assert.Equal(t, string(res), vc.String())
}

// TestCustomRuntimeConfigAndLangRuntimeMarshal func TestCustomRuntimeConfigAndLangRuntimeMarshal(t *testing.T) 测试自定义运行时配置和语言运行时的 Marshal 和 Unmarshal
func TestCustomRuntimeConfigAndLangRuntimeMarshal(t *testing.T) {
	cfg := &FunctionConfig{
		CustomRuntimeConfig: &CustomRuntimeConfig{
			Args:    []string{"--foo", "--bar"},
			Port:    8080,
			Command: []string{"/bin/sh", "-c"},
		},
		LangRuntime: "python3.9",
	}
	data, err := json.Marshal(cfg)
	if err != nil {
		t.Fatalf("marshal error: %v", err)
	}
	var out FunctionConfig
	err = json.Unmarshal(data, &out)
	if err != nil {
		t.Fatalf("unmarshal error: %v", err)
	}
	if out.CustomRuntimeConfig == nil || out.CustomRuntimeConfig.Port != 8080 || out.LangRuntime != "python3.9" {
		t.Error("CustomRuntimeConfig or LangRuntime not correctly marshaled/unmarshaled")
	}
}
