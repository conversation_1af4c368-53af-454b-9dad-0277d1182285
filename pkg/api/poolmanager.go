package api

import (
	"icode.baidu.com/baidu/faas/kun/pkg/bce/credential"
)

// GetFreePodRequest xxx
type GetFreePodRequest struct {
	User    *credential.User
	Runtime *RuntimeConfiguration
	*GetFunctionOutput
	ServiceType  ServiceType
	Labels       RequestLabels // 相当于请求的环境变量，包括userID、appID等等
	WithPodTrace bool
	ReservedType string //是否为预留实例DryRun请求
	ReservedId   string //预留实例ID
	MCPSessionID string // MCP会话ID
}

// GetFreePodResponse xxx
type GetFreePodResponse struct {
	Pod   *PodInfo  `json:"pod,omitempty"`
	Trace *PodTrace `json:"trace,omitempty"`
}

// Valid xxx
func (r GetFreePodRequest) Valid() bool {
	if len(r.User.ID) == 0 || *r.Configuration.MemorySize == int64(0) || len(*r.Configuration.CommitID) == 0 {
		return false
	}
	return true
}

// PutFreePodRequest xxx
type PutFreePodRequest struct {
	Pod *PodInfo `json:"pod"`
	*GetFunctionOutput
	ServiceType ServiceType
	Labels      RequestLabels // 相当于请求的环境变量，包括userID、appID等等
}

// PutFreePodResponse xxx
type PutFreePodResponse struct {
	Errno int `json:"errno"`
}

// PutDubiousPodRequest xxx
type PutDubiousPodRequest struct {
	Pod *PodInfo `json:"pod"`
	*GetFunctionOutput
}

// PutDubiousPodResponse xxx
type PutDubiousPodResponse struct {
	Errno int `json:"errno"`
}

// PutSickPodRequest xxx
type PutSickPodRequest struct {
	Pod *PodInfo `json:"pod"`
	*GetFunctionOutput
}

// PutSickPodResponse xxx
type PutSickPodResponse struct {
	Errno int `json:"errno"`
}

// poolmgr回收超时pod 的请求结构体
type RecycleTimeoutPodRequest struct {
	Pod *PodInfo `json:"pod"`
	*GetFunctionOutput
}

type RecycleTimeoutPodResponse struct {
	Errno int `json:"errno"`
}

type PutMCPSessionPodRequest struct {
	MCPSessionID string
	Pod          *PodInfo `json:"pod"`
	*GetFunctionOutput
}

type PutMCPSessionPodResponse struct {
	Errno int `json:"errno"`
}
