package api

import (
	"fmt"
	"regexp"
	"sort"
	"time"

	"github.com/aws/aws-sdk-go/aws/awsutil"
	"github.com/aws/aws-sdk-go/service/lambda"
)

const (
	FunctionCacheMethodUpdate = "update"
	FunctionCacheMethodDelete = "delete"

	AliasCacheMethodCreate = "create"
	AliasCacheMethodUpdate = "update"
	AliasCacheMethodDelete = "delete"

	BillingCacheMethodUpdate = "update"
	BillingCacheMethodDelete = "delete"

	PolicyCacheMethodDelete = "delete"
	PolicyCacheMethodCreate = "create"
	PolicyCacheMethodUpdate = "update"

	FunctionCacheEtcdPrefix = "function_cache"
	PolicyCacheEtcdPrefix   = "policy_cache"
	BillingCacheEtcdPrefix  = "billing_cache"
	AliasCacheEtcdPrefix    = "alias_cache"
	EventSourceEtcdPrefix   = "event_source_mapping"
	CrontabEtcdPrefix       = "crontab"
	InternalUserPrefix      = "internal_users"

	// TypeDuerOSBotNodejs 蓝图属性
	TypeDuerOSBotNodejs = "dueros-bot-nodejs"

	StartingPositionTriHorizon  = "TRIM_HORIZON"
	StartingPositionLatest      = "LATEST"
	StartingPositionAtTimeStamp = "AT_TIMESTAMP"

	DatahubTopicStartPointLatest = int64(-1)
	DatahubTopicStartPointOldest = int64(-2)

	TypeEventSourceBms            = "bms"
	TypeEventSourceExclusiveKafka = "exclusive_kafka"
	TypeEventSourceRocketmq       = "rocketmq"

	TypeEventSourceDatahubTopic = "datahub_topic"
	TypeEventSourceDatahubQueue = "datahub_queue"

	// 小程序云
	CbdSourceTag          = "cbd"
	CfcSourceTag          = ""
	CFCMigrationSourceTag = "cfc_migration"

	UserFeatureInactive  = 0
	UserFeatureActivated = 1

	CodeModeSqfs = "sqfs"

	// 死信队列操作
	TopicReadOperation  = "Read"
	TopicWriteOperation = "Write"
	NoneDeadletterTopic = ""

	// 是否允许使用安全容器
	EnableSecureContainer = "enable_secure_container"

	// 安全容器标识
	SecureContainer = "securecontainer"

	// 函数最大超时时间配额
	MaxFunctionTimeout = "max_function_timeout"
	DefaultMaxTimeout  = 300

	// 异步调用配置相关操作
	// MaxRetryAttempts                 = 3
	// MaxRetryIntervalInSeconds        = 43200        //12小时
	DestinationTypeForKafka          = "bms/topics" // 百度消息服务
	DestinationTypeForCfc            = "cfc"
	DefaultMaxRetryIntervalInSeconds = 21600 // 默认6个小时

	// 预留实例规格标识
	FunctionReservedSpec = "function_reserved_spec"

	FunctionReservedCreate = "create"
	FunctionReservedUpdate = "update"
	FunctionReservedDelete = "delete"

	FunctionReservedEtcdPrefix = "function_reserved_cache"

	FunctionReservedStatusDeleted = 4
	FunctionReserved              = "Reserved"
)

// 异步调用配置相关操作
var (
	MaxRetryAttempts                = 3
	MaxRetryIntervalInSeconds int64 = 43200 // 12小时
)

const (
	BlueprintOnline  = "online"
	BlueprintOffline = "offline"
	BlueprintDefault = "default"
	BlueprintBeta    = "beta"
	LogToSummary     = true
	LogNotToSummary  = false

	BlsDefaultLogSet    = "CFC_logset_default"
	BlsDefaultRetention = 7
)

const (
	ReservedTypeProcessving = "reserved_processing"
	ReservedTypeFail        = "reserved_fail"
	ReservedTypeSuccess     = "reserved_success"
	ReservedTypeMysql       = "reserved_mysql"
	ReservedTypeUnreserved  = "unreserved"

	Reserved_Expect_Count     = "expect"
	Reserved_Success_Count    = "success"
	Reserved_Processing_Count = "processing"
	Reserved_Fail_Count       = "fail"
)

const (
	FeatureTypeUnknown = iota
	FeatureTypeCfcEdge
)

const (
	RuntimeExpireTime      = 10 * time.Minute
	VpcWaitingTime         = 1 * time.Minute
	InternalUserExpireTime = 5 * time.Minute
)

type StartingPositionType string

var (
	RxFunctionName = regexp.MustCompile("^[a-zA-Z0-9-_]+$")
	RegVersion     = regexp.MustCompile(`^(\$LATEST|([0-9]+))$`)
)

// CFS默认配置
var (
	DefaulfRemotePath = "/"
	DefaultLocalPath  = "/mnt"
)

// Relation relationship of function and trigger
type Relation struct {
	RelationID string                 `json:"RelationId"`
	PolicySid  string                 `json:"Sid"` // 关联的policy的statement-id
	Source     string                 // 触发源
	Target     string                 // 触发函数
	Data       map[string]interface{} // 触发器自定义参数
}

// PolicyConfig xxx
type PolicyConfig struct {
	RevisionId   string                 `gorm:"-" sql:"-"`
	SourceArn    string                 `gorm:"-" sql:"-"`
	PrincipalMap map[string]string      `gorm:"-" sql:"-"`
	ConditionMap map[string]interface{} `gorm:"-" sql:"-"`
}

type GetPolicyOutput struct {
	Condition []struct {
		Source        string
		SourceAccount string
		Effect        string
		Action        string
	}
}

type GetOTEPolicyOutput struct {
	Policy     string `json:"Policy"`
	RevisionId string `json:"RevisionId"`
}

type UpdateFunctionPolicy struct {
	NewBrn    string `json:"NewBrn"`
	OldSource string `json:"OldSource"`
	NewSource string `json:"NewSource"`
}

type PublicPolicy struct {
	Sid          string                       `json:"Sid"`
	Effect       string                       `json:"Effect"`
	PrincipalMap map[string]string            `json:"Principal"`
	Action       string                       `json:"Action"`
	Resource     string                       `json:"Resource"`
	ConditionMap map[string]map[string]string `json:"Condition"`
}

type PolicyStatement struct {
	Version   string         `json:"Version"`
	ID        string         `json:"Id"`
	Statement []PublicPolicy `json:"Statement"`
}

type GetPolicyInput struct {
	FuncBrn   string
	Trigger   TriggerType
	AccountID string
}

// CodeStorage 代码存储
type CodeStorage struct {
	Location       string
	RepositoryType string
}

// FunctionConfig 函数配置
type FunctionConfig struct {
	CodeSha256     string
	SquashFsSha256 *string `gorm:"column:squashfs_sha256" json:"-"`
	LayerSha256    *string `gorm:"default:'',column:layer_sha256" json:",omitempty"`
	CodeSize       int32
	FunctionArn    string `gorm:"-" sql:"-"`
	FunctionName   string `valid:"optional,matches(^[a-zA-Z0-9-_]+$),runelength(1|64)"`
	ServiceName    string `valid:"optional,matches(^[a-zA-Z0-9-_]+$),runelength(1|50)"`
	Handler        string `valid:"optional,matches(^([a-zA-Z0-9-:\\-\\._]+)$),runelength(1|128)"`
	Version        string `valid:"optional,matches(^\\$LATEST|([0-9]+)$),runelength(1|32)"`
	Runtime        string `valid:"optional,cfc_runtime"`
	MemorySize     *int
	Environment    *Environment `gorm:"-" sql:"-"`
	CommitID       *string      `gorm:"column:commit_id" json:"CommitId"`
	CodeID         string       `json:"-"`

	Role                         *string        `gorm:"column:role"`
	LogType                      string         `gorm:"column:log_type" valid:"optional,matches(^(none|bos|kafka|fluentd_kafka|bls)$)" json:",omitempty"`
	LogBosDir                    string         `gorm:"column:log_bos_dir" valid:"optional,runelength(6|256)" json:",omitempty"`
	BlsLogSet                    string         `gorm:"column:bls_log_set" valid:"optional,matches(^([a-zA-Z0-9-:\\-\\._]+)$),runelength(1|128)"`
	ReservedConcurrentExecutions *int           `gorm:"column:concurrency" json:"-"`
	VpcConfig                    *VpcConfig     `gorm:"column:-" json:"VpcConfig,omitempty"`
	DeadLetterTopic              *string        `gorm:"default:'',column:dead_letter_topic" json:"DeadLetterTopic,omitempty"` // 用户死信队列配置
	LayerList                    []*LayerSample `gorm:"-" json:"Layers"`

	// 内部接口获取Layer包下载地址
	LayerLocation string `gorm:"-" json:",omitempty"`

	PodConcurrentQuota *int   `gorm:"column:pod_concurrency" json:"PodConcurrentQuota"`
	WorkspaceID        string `gorm:"column:workspace_id" json:"WorkspaceID,omitempty"`

	FunctionMaxTimeout *int `gorm:"-" sql:"-"`

	// 异步调用配置
	AsyncInvokeConfig *AsyncInvokeConfig `gorm:"-" json:"AsyncInvokeConfig,omitempty"`

	// 预留标识
	ReservedType string `gorm:"column:reserved_type" json:"ReservedType,omitempty"`

	// CFS相关配置
	CFSConfig *CFSConfig `gorm:"-" json:"CFSConfig,omitempty"`

	// 网络相关配置
	NetworkConfig *NetworkConfig `gorm:"-" json:"NetworkConfig,omitempty"`

	// 自定义运行时配置
	CustomRuntimeConfig *CustomRuntimeConfig `gorm:"-" json:"CustomRuntimeConfig,omitempty"`

	// 自定义运行时语言信息
	LangRuntime string `gorm:"column:lang_runtime" json:"LangRuntime,omitempty"`

	// 函数是否被禁用
	Ban *bool `gorm:"column:ban" json:"Ban"`

	// 函数限流的memorySize
	LimitMemorySize *int `gorm:"column:limit_memory_size" json:"LimitMemorySize"`

	// 函数限流的timeout
	LimitTimeout *int `gorm:"column:limit_timeout" json:"LimitTimeout"`

	LimitMaxRetryAttempts *int `gorm:"column:limit_max_retry_attempts" json:"LimitMaxRetryAttempts"`
}

// 自定义运行时函数配置
type CustomRuntimeConfig struct {
	Args    []string `json:"Args"`
	Port    int      `json:"Port"`
	Command []string `json:"Command"`
}

// CFS 相关配置
type CFSConfig struct {
	FsName     *string `json:"FsName"`     // 文件系统名称
	FsId       *string `json:"FsId"`       // 文件系统id
	SubnetID   *string `json:"SubnetID"`   // 子网ID
	Domain     *string `json:"Domain"`     // 挂载域名
	RemotePath *string `json:"RemotePath"` // CFS侧挂载路径
	LocalPath  *string `json:"LocalPath"`  // 本地目标路径
	Ovip       *string `json:"Ovip"`       // domain对应的外部虚拟ip
	VpcId      *string `json:"VpcId"`      // VPC Id
}

// Function Environment 环境变量
type Environment struct {
	Variables map[string]string
}

// iam的acl配置
type Acl struct {
	ID                string              `json:"id"`
	AccessControlList []AccessControlList `json:"accessControlList"`
}

type AccessControlList struct {
	Service    string   `json:"service"`
	Region     string   `json:"region"`
	Resource   []string `json:"resource"`
	Effect     string   `json:"effect"`
	Eid        string   `json:"eid"`
	Permission []string `json:"permission"`
}

// VpcConfig 函数 VPC 配置
type VpcConfig struct {
	VpcID            string   `json:"VpcId"`
	SubnetIDs        []string `json:"SubnetIds"`
	SecurityGroupIDs []string `json:"SecurityGroupIds"`
}

func (s *VpcConfig) Equal(t *VpcConfig) bool {
	if s.VpcID != t.VpcID ||
		len(s.SubnetIDs) != len(t.SubnetIDs) ||
		len(s.SecurityGroupIDs) != len(t.SecurityGroupIDs) {
		return false
	}

	sort.Strings(s.SubnetIDs)
	sort.Strings(t.SubnetIDs)
	for i, subnet := range s.SubnetIDs {
		if t.SubnetIDs[i] != subnet {
			return false
		}
	}

	sort.Strings(s.SecurityGroupIDs)
	sort.Strings(t.SecurityGroupIDs)
	for i, sg := range s.SecurityGroupIDs {
		if t.SecurityGroupIDs[i] != sg {
			return false
		}
	}
	return true
}

// String 生成已 vpc 配置的唯一字符串，用于生成唯一标识
func (s *VpcConfig) String() string {
	sort.Strings(s.SubnetIDs)
	sort.Strings(s.SecurityGroupIDs)

	subs := "["
	for _, id := range s.SubnetIDs {
		subs = subs + "\"" + id + "\"" + ","
	}
	subs = subs[:len(subs)-1] + "]"

	secs := "["
	for _, id := range s.SecurityGroupIDs {
		secs = secs + "\"" + id + "\"" + ","
	}
	secs = secs[:len(secs)-1] + "]"

	ret := fmt.Sprintf("{\"VpcId\":\"%s\",\"SubnetIds\":%s,\"SecurityGroupIds\":%s}", s.VpcID, subs, secs)
	return ret
}

// MarshalJSON 保证 marshal 结果是有序的，便于比较
func (s *VpcConfig) MarshalJSON() ([]byte, error) {
	return []byte(s.String()), nil
}

// 异步调用配置
type AsyncInvokeConfig struct {
	MaxRetryIntervalInSeconds *int64             `json:"MaxRetryIntervalInSeconds"` // 消息最大保留时间
	MaxRetryAttempts          *int               `json:"MaxRetryAttempts"`          // 最大失败重试次数
	OnSuccess                 *DestinationConfig `json:"OnSuccess"`                 // 异步调用成功触发目标服务
	OnFailure                 *DestinationConfig `json:"OnFailure"`                 // 异步调用失败触发目标服务
}

// 异步调用目标服务配置
type DestinationConfig struct {
	Type        string `json:"Type"`        // 触发目标服务类型，kafka of cfc
	Destination string `json:"Destination"` // 目标服务，topic or 函数brn
}

// RuntimeConfiguration
type RuntimeConfiguration struct {
	Name     string
	Bin      string
	Path     string
	SqfsPath string   `gorm:"column:sqfs_path"`
	Args     []string `gorm:"-" sql:"-"`

	// kata 容器将全部runtime挂载到容器中,执行的bin和args需要进入到对应runtime的目录中
	KataBin string `gorm:"column:kata_bin" json:",omitempty"`

	KataArgs []string `gorm:"-" sql:"-" json:",omitempty"`
}

// GetFunctionInput 和原版保持一致
type GetFunctionInput struct {
	lambda.GetFunctionInput
	RequestID string
	AccountID string
}

type GetBillingInput struct {
	AccountId string
	RequestID string
}

// FunctionCodeLocation 和原版保持一致
type FunctionCodeLocation struct {
	lambda.FunctionCodeLocation
}

// Concurrency xxx
type Concurrency struct {
	lambda.PutFunctionConcurrencyOutput
	AccountReservedSum int
}

type LogConfiguration struct {
	LogType string

	// for bos
	BosDir string

	// for bls
	BlsLogSet string

	// for other
	Params string
}

func (c LogConfiguration) String() string {
	return awsutil.Prettify(c)
}

// FunctionConfiguration 由官方的 Config 和我们自己的 CommitID 组成 + 函数来源SourceTag + 用户死信队列DeadLetterTopic
type FunctionConfiguration struct {
	lambda.FunctionConfiguration
	CommitID              *string `json:"CommitId"`
	Uid                   string  `json:"Uid"`
	SourceTag             string  `json:"SourceTag"`
	DeadLetterTopic       string  `json:"DeadLetterTopic"`
	LayerSha256           string  `json:",omitempty"`
	LayerLocation         string  `json:",omitempty"`
	Ban                   *bool   `json:"Ban"`                   // 函数是否被禁用
	LimitMemorySize       *int    `json:"LimitMemorySize"`       // 函数限流memorySize大小
	LimitTimeout          *int    `json:"LimitTimeout"`          // 函数限流timeout时长
	LimitMaxRetryAttempts *int    `json:"LimitMaxRetryAttempts"` // 函数限流超时时长

	PodConcurrentQuota int
	FunctionMaxTimeout *int               // 用户配置的最大超时时间
	AsyncInvokeConfig  *AsyncInvokeConfig // 异步调用配置
	CFSConfig          *CFSConfig         // CFS 配置
	ReservedType       string             // 预留标识
	Sse                bool               // 是否以sse形式返回
	NetworkConfig      *NetworkConfig     // VPC 配置
	// CustomRuntimeConfig函数自定义运行时的配置
	CustomRuntimeConfig *CustomRuntimeConfig

	// 自定义运行时下语言运行时(用户判断是否为mcp)
	LangRuntime string `json:"LangRuntime,omitempty"`
}

func (s *FunctionConfiguration) SetCommitID(v string) *FunctionConfiguration {
	s.CommitID = &v
	return s
}

// GetFunctionOutput 中的 Config 部分替换成上面定义的
type GetFunctionOutput struct {
	_ struct{} `type:"structure"`

	// The object for the Lambda function location.
	Code *FunctionCodeLocation `type:"structure"`

	// The concurrent execution limit set for this function. For more information,
	// see concurrent-executions.
	Concurrency *Concurrency `type:"structure"`

	// A complex type that describes function metadata.
	Configuration *FunctionConfiguration `type:"structure"`

	LogConfig *LogConfiguration `type:"structure"`

	// Returns the list of tags associated with the function.
	Tags map[string]*string `type:"map"`
}

type GetBillingOutput struct {
	AccountId     string `json:"accountId"`
	ResourceState string `json:"status"`
}

type GetInternalUsersOutput struct {
	AccountIds []string `json:"accountIds"`
}

// AccountLimit provides limits of code size and concurrency associated with
// the current account and region.
// Reference to lambda-dg.pdf, Chapter "API Reference", Section "Data Types", p443.
type AccountLimit struct {
	CodeSizeUnzipped     *int `json:"CodeSizeUnzipped"`
	CodeSizeZipped       *int `json:"CodeSizeZipped"`
	ConcurrentExecutions *int `json:"ConcurrentExecutions"`
	TotalCodeSize        *int `json:"TotalCodeSize"`

	UnreservedConcurrentExecutions        *int `json:"UnreservedConcurrentExecutions"`
	UnreservedConcurrentExecutionsMinimum *int `json:"-"`
}

// AccountUsage provides code size usage and function count associated with
// the current account and region.
// Reference to lambda-dg.pdf, Chapter "API Reference", Section "Data Types", p445.
type AccountUsage struct {
	FunctionCount *int `json:"FunctionCount"`
	TotalCodeSize *int `json:"TotalCodeSize"`
}

// AccountSettingResponse xxx
type AccountSettingResponse struct {
	AccountLimit *AccountLimit `json:"AccountLimit"`
	AccountUsage *AccountUsage `json:"AccountUsage"`
}

// FeatureActivationResponse xxx
type FeatureActivationResponse struct {
	Enabled     bool `json:"enabled"`
	InWhitelist bool `json:"inWhitelist"`
}

const (
	ServiceOnline     = 1
	ServiceDelete     = 2
	ServiceDefault    = "default"
	MaxServicePerUser = 10
)

type BanInfo struct {
	Ban                   *bool  `json:"Ban"`
	LimitMemorySize       *int   `json:"LimitMemorySize"`
	LimitTimeout          *int   `json:"LimitTimeout"`
	LimitMaxRetryAttempts *int   `json:"LimitMaxRetryAttempts"`
	FunctionBrn           string `json:"FunctionBrn"`
	AccountID             string
}
