package api

import (
	"time"

	"icode.baidu.com/baidu/faas/kun/pkg/whitelist"
)

const (
	ReservedStart = iota
	ReservedCreating
	ReservedCreated
	ReservedDeleting
	ReservedDeleted
)

const (
	ReservedMethod   = "reserved"
	UnReservedMethod = "unReserved"
)

type ReservedConfig struct {
	ReservedStep                      int   // 单个函数并发dryrun数量
	TaskExpiration                    int64 // reserved任务过期时间
	VIPUserOptions                    *whitelist.WhiteListOptions
	NodeIsolationUserWhiteListOptions *whitelist.NodeIsolationUserWhiteListOptions //用户白名单，决定独占node还是共享node
	ReservedBillingReport             bool                                         // 预留实例pod调用是否上报billing
	SyncMysqlInterval                 time.Duration                                // 同步数据库间隔
	ConcurrencyNum                    int                                          // 预留实例预留并发数
	CoolDownConcurrency               int64                                        // 并发执行pod coolDown个数
	PodMaxConcurrency                 int64                                        // pod最大并发度
	ListReservedDuration              int64                                        // 获取预留实例记录时间跨度
	MaxReservedNum                    int64                                        // 预留实例规格配置
}

func NewDefaultReservedConfig() *ReservedConfig {
	return &ReservedConfig{
		ReservedStep:                      20,
		TaskExpiration:                    5 * 60,
		CoolDownConcurrency:               20,
		PodMaxConcurrency:                 10,
		ReservedBillingReport:             false,
		VIPUserOptions:                    &whitelist.WhiteListOptions{},
		NodeIsolationUserWhiteListOptions: &whitelist.NodeIsolationUserWhiteListOptions{},
		SyncMysqlInterval:                 60,
		ConcurrencyNum:                    50,
		ListReservedDuration:              10,
		MaxReservedNum:                    40,
	}
}

type ReserveInstance struct {
	*PodInfo
	Duration    int64  `json:"duration"`
	FunctionBrn string `json:"functionBrn"`
	Region      string `json:"region"`
	UpdateType  int64  `json:"updateType"`
	MsgID       string `json:"msgID"`
	GeneratedAt int64  `json:"generatedAt"`
	ReserveID   string `json:"reserveID"`
	UseID       string `json:"useID"`
}
type EventType int

type ReserveEvent struct {
	Type            EventType
	ReserveInstance *ReserveInstance
}

type InstanceUsages struct {
	Usages []ReserveInstance `json:"usages"`
}
