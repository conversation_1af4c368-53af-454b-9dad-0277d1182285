/**
 * Created Date: Friday, August 11th 2017, 3:41:59 pm
 * Author: hefangshi
 * -----
 * Last Modified: Mon Aug 14 2017
 * Modified By: hefangshi
 * -----
 * Copyright (c) 2017 Baidu.Inc
 *
 */

package api

import (
	"context"
	"net"
	"strconv"
	"time"

	kataAgentClient "github.com/kata-containers/agent/protocols/client"
	"go.uber.org/zap/zapcore"
	runtime "k8s.io/cri-api/pkg/apis/runtime/v1alpha2"

	kunErr "icode.baidu.com/baidu/faas/kun/pkg/error"
	"icode.baidu.com/baidu/faas/kun/pkg/util/criteria"
)

// InitContainerRequest 初始化 FaaS 容器请求
type InitContainerRequest struct {
	PodName string
	*WarmUpContainerArgs
}

type WarmUpContainerArgs struct {
	Code                 *CodeStorage
	Configuration        *FunctionConfig
	RuntimeConfiguration *RuntimeConfiguration
	ServiceType          ServiceType
	Labels               RequestLabels
	KataImage            string
	GreInterfaceIP       string
	Host                 string
	NodeID               string
	SchedulingType       string
	ReservedUserId       string
	ReserveId            string
}

// InitContainerRequest2 是 lambda 版本，临时兼容客户端和服务端
type InitContainerRequest2 struct {
	PodName              string
	Code                 *FunctionCodeLocation
	Configuration        *FunctionConfiguration
	RuntimeConfiguration *RuntimeConfiguration
}

// InitOneColdContainerRequest2 是 lambda 版本，临时兼容客户端和服务端
type InitOneColdContainerRequest2 struct {
	Host                 string
	NodeID               string
	SchedulingType       string
	ResourceConfig       *ResourceConfig
	Code                 *FunctionCodeLocation
	Configuration        *FunctionConfiguration
	RuntimeConfiguration *RuntimeConfiguration
	ServiceType          ServiceType
	Labels               RequestLabels
	KataImage            string
	GreInterfaceIP       string
	ReservedUserId       string
	ReserveId            string
}

// InitContainerRequest2 是 lambda 版本，临时兼容客户端和服务端
type InitOneColdContainerRequest struct {
	ResourceConfig *ResourceConfig
	*WarmUpContainerArgs
}

type InitOneColdContainerResponse struct {
	Container   ContainerInfo
	DurationMap DurationMap // 存储重要的阶段耗时

	FError kunErr.FinalError
}

// RecoverContainerRequest 重新初始化 FaaS 容器请求
type RecoverContainerRequest struct {
	PodName       string
	Configuration *FunctionConfig
}

// QueryContainerTrafficRequest 获取 FaaS 流量使用
type QueryContainerTrafficRequest struct {
	PodName      string
	ResetTraffic bool
}

type ContainerStatusType string

const (
	ContainerStatusCold           ContainerStatusType = "cold"
	ContainerStatusWarm           ContainerStatusType = "warm"
	ContainerStatusWarmup         ContainerStatusType = "warmup"
	ContainerStatusDisconnected   ContainerStatusType = "disconnect"
	ContainerStatusZombie         ContainerStatusType = "zombie"
	ContainerStatusMemoryMismatch ContainerStatusType = "memorymismatch"
	ContainerStatusSick           ContainerStatusType = "sick"
	ContainerStatusNotReady       ContainerStatusType = "notready"
	ContainerStatusUnknown        ContainerStatusType = "unknown"
)

type FreezerState string

const (
	Undefined FreezerState = ""
	Frozen    FreezerState = "FROZEN"
	Thawed    FreezerState = "THAWED"
)

const (
	RuntimeKata = "kata"
	RuntimeRunc = "runc"
)

const MemoryLimitInBytesParam = "MemoryLimitInBytes"

const ZombieUseContainerID = "ZombieContainerId:"

const ReportStateConnected = "connected"

type ListPodCriteria struct {
	criteria.QueryCriteria
}

func NewListPodCriteria() *ListPodCriteria {
	return &ListPodCriteria{criteria.NewQueryCriteria()}
}

func (c *ListPodCriteria) AddMemoryLimitInBytes(m int64) {
	c.AddCondition(MemoryLimitInBytesParam, strconv.FormatInt(m, 10))
}

func (c *ListPodCriteria) ReadMemoryLimitInBytes() (*int64, error) {
	strVal := c.Value().Get(MemoryLimitInBytesParam)
	if strVal == "" {
		return nil, nil
	}
	memory, err := strconv.ParseInt(strVal, 10, 64)
	if err != nil {
		return nil, err
	}
	return &memory, nil
}

// ListPods 获取容器列表响应
type ListPodsResponse []*ContainerInfo

type ContainerInfo struct {
	Hostname              string // kata也是pod name
	ContainerID           string
	Status                ContainerStatusType
	HostPid               int
	ResourceStats         *ResourceStats `json:",omitempty"`
	FullContainerID       string         `json:"-"`
	CgroupParent          string         `json:"-"`
	DefaultLastAccessTime int64          `json:"-"`
	StateChannel          string         `json:"-"`
	Conn                  net.Conn       `json:"-"`
	IsolationDir          string         `json:"-"`
	MergedDir             string         `json:"-"`
	MaxFunctionTimeout    *int           `json:",omitempty"`

	KataAgentClient *kataAgentClient.AgentClient `json:"-"`
	ReserveId       string
	ReserveUserId   string

	*RuntimeInfo
}

func (c *ContainerInfo) MayHasBeenUsed() bool {
	return c.Status == ContainerStatusWarm ||
		c.Status == ContainerStatusWarmup ||
		c.Status == ContainerStatusZombie ||
		c.Status == ContainerStatusMemoryMismatch
}

func (c *ContainerInfo) NeedCoolDown() bool {
	return c.Status == ContainerStatusWarm ||
		c.Status == ContainerStatusWarmup ||
		c.Status == ContainerStatusZombie ||
		c.Status == ContainerStatusMemoryMismatch ||
		c.Status == ContainerStatusSick
}

// MarshalLogObject is marshaler for ContainerInfo
func (c *ContainerInfo) MarshalLogObject(enc zapcore.ObjectEncoder) error {
	if c == nil {
		return nil
	}
	enc.AddString("pod_name", c.Hostname)
	enc.AddString("status", string(c.Status))
	enc.AddString("container_id", c.ContainerID)
	return nil
}

// container2Pod 将ContainerInfo和NodeInfo组合为warm pod结构
func (c *ContainerInfo) Container2Pod(node *NodeInfo) *PodInfo {
	schedulingType := GetPodSchedulingType(c.ServiceType)
	// ServiceType为空时，填充为默认cfc
	if c.ServiceType == "" {
		c.ServiceType = ServiceTypeCFC
	}
	pod := &PodInfo{
		PodName:             c.Hostname,
		IP:                  node.FloatingIP,
		ServiceType:         c.ServiceType,
		Labels:              c.Labels,
		MemorySize:          Byte2Mi(c.ResourceStats.MemoryStats.Limit), // byte转为Mi
		NodeID:              node.ID,
		CommitID:            c.CommitID,
		NodeOccupiedVersion: node.OccupiedVersion,
		SchedulingType:      schedulingType,
		Reserved:            node.Reserved,
	}
	return pod
}

func Byte2Mi(i int64) int64 {
	return i / 1024 / 1024
}

// TrafficStats 流量信息
type TrafficStats struct {
	INBytes  int64
	OUTBytes int64
}

// NodeDescription node的当前信息
type NodeDescription struct {
}

// MemoryStats holds the on-demand stastistics from the memory cgroup
type MemoryStats struct {
	// Memory usage (in bytes).
	Usage int64
	// Memory limit (in bytes)
	Limit int64
	// Memory Swap Limit (in bytes)
	SwapLimit int64
}

// ResourceStats holds on-demand stastistics from various cgroup subsystems
type ResourceStats struct {
	// Memory statistics.
	MemoryStats  *MemoryStats
	FreezerState FreezerState
}

// ResourceConfig holds information about all the supported cgroup resource parameters.
// port from k8s.io/kubernetes/pkg/kubelet/cm
type ResourceConfig struct {
	// Memory limit (in bytes).
	Memory *int64
	// CPU shares (relative weight vs. other containers).
	CpuShares *uint64
	// CPU hardcap limit (in usecs). Allowed cpu time in a given period.
	CpuQuota *int64
	// CPU quota period.
	CpuPeriod *uint64
	// HugePageLimit map from page size (in bytes) to limit (in bytes)
	HugePageLimit map[int64]int64
	// Freezer state
	FreezerState *FreezerState
}

type UpdateResourceRequest struct {
	ResourceConfig *ResourceConfig
	Containers     []string
}

const OriginalMemorySizeParam = "OriginalMemorySize"

type CoolDownPodRequest struct {
	OriginalMemoryLimit *int64
}

type ResetNodeRequest struct {
	OriginalMemoryLimit *int64
}

type CreateContainer struct {
	Name    string
	Image   string
	Binds   []string
	Memory  int64
	Runtime string
	Cmd     []string
}

type FreezeRequest struct {
	PodName string
	State   FreezerState
}

type SickPodRequest struct {
	PodName string
}

type ReportContainerStateRequest struct {
	PodName string
	State   string
}

type FuncletClientRecoverInput struct {
	Host      string
	Pod       *PodInfo
	RequestID string
}

type FuncletClientWarmUpInput struct {
	Host         string
	Pod          *PodInfo
	Function     *GetFunctionOutput
	Runtime      *RuntimeConfiguration
	RequestID    string
	FetchSession string // pool manager
}

type FuncletClientWarmUpOneInput struct {
	Context        context.Context
	Host           string
	NodeID         string
	SchedulingType string
	Resource       *ResourceConfig
	Function       *GetFunctionOutput
	Runtime        *RuntimeConfiguration
	RequestID      string
	ServiceType    ServiceType
	Labels         map[string]string
	FetchSession   string // pool manager
	KataImage      string
	GreInterfaceIP string
	ReservedUserId string
	ReserveID      string
}

type FuncletClientCoolDownInput struct {
	Host      string
	Pod       *PodInfo
	RequestID string
}

type FuncletClientResetNodeInput struct {
	Host           string
	OriginalMemory int64
	RequestID      string
}

type FuncletClientListPodsInput struct {
	Host         string
	Criteria     *ListPodCriteria
	RequestID    string
	FetchSession string
}

type FuncletClientListZombiePodsInput struct {
	Host      string
	RequestID string
}

type FuncletClientDescribeInput struct {
	Host      string
	RequestID string
}

type FuncletClientUpdateResourceInput struct {
	Host      string
	Resource  *ResourceConfig
	Pods      []*ContainerInfo
	RequestID string
}

type FuncletClientAddPodInput struct {
	Host        string
	FloatingIP  string
	Image       string
	MemoryLimit int64
	RequestID   string
	Runtime     string
}

type FuncletClientDeletePodInput struct {
	Host       string
	FloatingIP string
	PodName    string
	RequestID  string
}

type FuncletClientFreezeContainerInput struct {
	Host      string
	PodName   string
	State     FreezerState
	RequestID string
}

type FuncletClientNetTrafficInput struct {
	PodName   string
	Reset     bool
	RequestID string
}

type FuncletClientReportContainerStateInput struct {
	Host      string
	PodName   string
	State     string
	RequestID string
}

type FuncletClientPutSickPodInput struct {
	Context   context.Context
	Host      string
	RequestID string
}

type FuncletClientGetSickPodListInput struct {
	Host      string
	RequestID string
}

type FuncletClientDeleteSickPodInput struct {
	Host      string
	RequestID string
	PodName   string
}

type SickPod struct {
	PodName  string
	CreateAt time.Time
}

type SickPodListResponse []*SickPod

type KataContainer struct {
	Hostname           string
	ContainerConfig    *runtime.ContainerConfig
	PodSandboxConfig   *runtime.PodSandboxConfig
	PodSandbox         *runtime.PodSandbox
	CreatedContainerID string
	Status             ContainerStatusType
	ContainerInfo      *ContainerInfo
}

type FuncletClientListImagesInput struct {
	Host      string
	RequestID string
	GetAll    bool
}

type FuncletClientListImagesResponse struct {
	Images []*runtime.Image
}

type FuncletCheckAndPullImagesInput struct {
	Host      string
	RequestID string
	Images    []string
}

type FuncletCheckAndPullImagesResponse struct {
	Results map[string]bool
	Err     error
}
