package api

import (
	"fmt"
	"regexp"
)

// TriggerType xxx
type TriggerType = string

type PrincipalServiceType = string

type TriggerUserScopeType = string

const (
	TriggerTypeApiGateway     TriggerType = "api-gateway"
	TriggerBls                TriggerType = "bls"
	TriggerTypeHTTP                       = "cfc-http-trigger"
	TriggerTypeBos                        = "bos"
	TriggerTypeDuerOS                     = "dueros"
	TriggerTypeDuEdge                     = "duedge"
	TriggerTypeCdn                        = "cdn"
	TriggerTypeCfcEdge                    = "cfc-edge"
	TriggerTypeCrontab                    = "cfc-crontab-trigger"
	TriggerTypeKafka                      = "kafka"
	TriggerTypeGeneric                    = "generic"
	TriggerTypeDatahubTopic               = "datahub_topic"
	TriggerTypeBms                        = "bms"
	TriggerTypeExclusiveKafka             = "exclusive_kafka"
	TriggerTypeRocketmq                   = "rocketmq"

	BosPrincipalService        PrincipalServiceType = "bos.baidubce.com"
	APIGatewayPrincipalService                      = "apigateway.baidubce.com"
	BlsPrincipalService                             = "bls-log.baidubce.com"
	DuerOSPrincipalService                          = "dueros.baidu.com"
	HTTPAPIPrincipalService                         = "httpapi.baidubce.com"
	DuEdgePrincipalService                          = "duedge.baidubce.com"
	CdnPrincipalService                             = "cdn.baidubce.com"
	CrontabPrincipalService                         = "crontabcfc.baidubce.com"
	CfcEdgePrincipalService                         = "cfcedge.baidubce.com"

	TriggerUserScopePublic   TriggerUserScopeType = "public"
	TriggerUserScopeInternal TriggerUserScopeType = "internal"
)

const (
	// CfcInvokeAction 由cfc自己创建policy时Action的值
	CfcInvokeAction = "cfc:InvokeFunction"

	// PolicyActionAllow 允许执行Action
	PolicyActionAllow = "Allow"
)

var (
	// RegularInvokeAction eventhub校验时，具有invokeFunction权限的Action值
	RegularInvokeAction = regexp.MustCompile(fmt.Sprintf("cfc:[*]|%s|[*]", CfcInvokeAction))

	// RegularCommonAction 匹配用户创建policy时输入的Action的值
	RegularCommonAction = regexp.MustCompile("cfc:[*]|cfc:[a-zA-Z]+|[*]")

	// RegularSourceAccount 匹配用户创建policy时输入的accountID
	RegularSourceAccount = regexp.MustCompile("^[a-z0-9]{32}$")

	// TriggersWithoutSource 只需要知道是哪个服务触发的，不区分具体触发源的触发器，故policy的Source字段始终为空。
	// 反例是bos触发器，它要区分触发源是哪个bucket，因此policy.Source是 bos/bucketName。
	TriggersWithoutSource = []string{
		DuerOSPrincipalService,
		DuEdgePrincipalService,
		CdnPrincipalService,
	}
)

// 全部触发器列表
func AllTriggerTypes() []TriggerType {
	return []TriggerType{
		TriggerTypeGeneric,
		TriggerTypeApiGateway,
		TriggerTypeHTTP,
		TriggerTypeBos,
		TriggerTypeDuerOS,
		TriggerTypeDuEdge,
		TriggerTypeCdn,
		TriggerTypeCfcEdge,
		TriggerTypeCrontab,
		TriggerTypeGeneric,
		TriggerTypeKafka,
		TriggerBls,
		TriggerTypeDatahubTopic,
	}
}

type TriggerTypeInfo struct {
	Type TriggerType
	Name string
}

// commanTriggerTypes 函数功能：返回一个包含多个TriggerTypeInfo结构体的切片，包括不同类型的触发器。
// 参数：
//
//	zone string - 区域名称，用于确定是否添加特定类型的触发器。
//
// 返回值：
//
//	[]TriggerTypeInfo - 包含多个TriggerTypeInfo结构体的切片，每个结构体表示一种类型的触发器。
func commanTriggerTypes(zone string) []TriggerTypeInfo {
	info := []TriggerTypeInfo{
		{
			Type: TriggerTypeHTTP,
			Name: "HTTP触发器",
		},
		{
			Type: TriggerTypeCrontab,
			Name: "Crontab触发器",
		},
		{
			Type: TriggerTypeBos,
			Name: "BOS触发器",
		},
		{
			Type: TriggerTypeBms,
			Name: "百度消息服务（kafka）触发器",
		},
		{
			Type: TriggerTypeExclusiveKafka,
			Name: "百度消息服务（专享版kafka）触发器",
		},
		{
			Type: TriggerTypeCdn,
			Name: "CDN触发器",
		},
		{
			Type: TriggerTypeDuEdge,
			Name: "DuEdge触发器",
		},
		{
			Type: TriggerTypeApiGateway,
			Name: "API Gateway触发器",
		},
		{
			Type: TriggerBls,
			Name: "BLS触发器",
		},
	}
	if zone == "bj" {
		info = append(info, TriggerTypeInfo{
			Type: TriggerTypeDuerOS,
			Name: "DuerOS触发器",
		})
	}
	if zone == "bj" || zone == "gz" {
		info = append(info, TriggerTypeInfo{
			Type: TriggerTypeRocketmq,
			Name: "RocketMQ触发器",
		})
	}
	return info
}

// 前端使用 百度内部用户可以使用的触发器类型
func internalTriggerTypes(zone string) []TriggerTypeInfo {
	types := commanTriggerTypes(zone)
	types = append(types, TriggerTypeInfo{
		Type: TriggerTypeDatahubTopic,
		Name: "Datahub Topic 触发器",
	})
	return types
}

// 前端使用 百度内外部用户均可用的触发器类型
func publicTriggerTypes(zone string) []TriggerTypeInfo {
	return commanTriggerTypes(zone)
}

func GetUserScopeTriggerType(tsType TriggerUserScopeType, zone string) []TriggerTypeInfo {
	switch tsType {
	case TriggerUserScopePublic:
		return publicTriggerTypes(zone)
	case TriggerUserScopeInternal:
		return internalTriggerTypes(zone)
	}
	return commanTriggerTypes(zone)
}
