package api

import (
	"fmt"
	"time"

	"icode.baidu.com/baidu/faas/kun/pkg/util/json"
)

// 获取pod的相关阶段，用来输出冷启动时的耗时信息
const (
	// poolmgr
	PodPeriodPMGetPod    = "pm_get_pod"       // poolmgr调用get pod，是整个冷启动的全部资源获取工作。主要包含获取warm pod和获取cold pod两阶段。
	PodPeriodGetWarmPod  = "pm_get_warm_pod"  // 尝试获取warm pod，冷启动时会失败
	PodPeriodGetColdPod  = "pm_get_cold_pod"  // poogmgr获取warm pod失败后尝试获取cold pod，步骤包含下面3个
	PodPeriodGetWarmNode = "pm_get_warm_node" // 获取cold pod的第一步，先拿到warm node
	PodPeriodPMWarmUp    = "pm_warm_up"       // 获取cold pod后转换为warm pod，poolmgr调用WarmUpOne
	PodPreiodSetWarmPod  = "pm_set_warm_pod"  // 将warm pod填入redis

	// funclet
	PodPeriodTotalWarmUp     = "funclet_warm_up"          // warm up总耗时
	PodPeriodChooseContainer = "funclet_choose_container" // 挑选一个cold pod
	PodPeriodWaitEni         = "funclet_wait_eni"
	PodPeriodInitRuntime     = "funclet_init_runtime"
	PodPeriodPrepareCode     = "funclet_prepare_code"
	PodPeriodFetchCode       = "funclet_fetch_code"
	PodPeriodUnzipCode       = "funclet_unzip_code"
	PodPeriodPrepareConf     = "funclet_prepare_conf"
	PodPeriodPrepareMount    = "funclet_prepare_mount"
	PodPeriodMount           = "funclet_mount"
	PodPeriodUpdateResource  = "funclet_update_resource"
	PodPeriodCreateKata      = "funclet_create_kata"
	PodPeriodStartKata       = "funclet_start_kata"
	PodPeriodWaitRunner      = "wait_runner"
)

type PodSource int
type PodReuseStatus string

const (
	PodViaWarmPod = "warm"
	PodViaColdPod = "cold"

	MaxPodCount = 20
)

const (
	PodStatusNormal  PodReuseStatus = "Normal"
	PodStatusSick                   = "Sick"
	PodStatusDubious                = "Dubious"
	PodStatusTimeout                = "Timeout" // 用户代码执行超时的pod 状态
)

type ServiceType string

const (
	ServiceTypeCFC     ServiceType = "cfc"
	ServiceTypeBCH     ServiceType = "bch"
	ServiceTypeDuedge  ServiceType = "duedge"
	ServiceTypeOTE     ServiceType = "ote"
	ServiceTypeCFCKata ServiceType = "cfckata" // 安全容器pod
)

const (
	DasouPodSource = "dasou"
)

const (
	SchedulingTypeBalance = "balance"
	SchedulingTypeCentral = "central"
)

// PodInfo 代表一个warm pod，用来存储在redis中，替换原有PodInfo
type PodInfo struct {
	NodeID              string        `json:"nodeID"`
	PodName             string        `json:"podName"`
	IP                  string        `json:"ip"`
	ServiceType         ServiceType   `json:"serviceType"`      // 请求的服务类型
	SchedulingType      string        `json:"schedulingType"`   // pod调度类型 balance/central
	Labels              RequestLabels `json:"labels,omitempty"` // 请求labels，由eventhub传入，包含cluster、node、pod labels
	MemorySize          int64         `json:"memorySize"`
	CommitID            string        `json:"commitID"`
	NodeOccupiedVersion uint64        `json:"nodeOccupiedVersion"` // node的版本号，用来剔除残留pod
	Reserved            bool          `json:"reserved"`            // 是否为预留pod
	Concurrency         int64         `json:"concurrency"`         //并发度
}

func (p *PodInfo) MarshalBinary() (data []byte, err error) {
	data, err = json.Marshal(p)
	return
}

func (p *PodInfo) String() string {
	return fmt.Sprintf("<Pod %s|%s|%s|%d|%s|%s|%s|%d>", p.PodName, p.NodeID, p.IP, p.MemorySize,
		p.CommitID, p.ServiceType, p.Labels, p.NodeOccupiedVersion)
}

type PodPacket struct {
	PodInfo
	PodTrace
}

// PodTrace eventhub 从 poolmanager 获取的，关于 pod 是如何产生的信息
type PodTrace struct {
	Source      string      `json:"source"`
	DurationMap DurationMap `json:"durationMap"`
}

type DurationMap map[string]time.Duration

// Add 以当前时间为结束点，增加一个耗时信息。同时防止map未初始化。
func (dm *DurationMap) Add(kind string, begin time.Time) {
	if *dm == nil {
		*dm = make(DurationMap)
	}

	(*dm)[kind] = time.Now().Sub(begin)
}

// Copy 将src的所有数据拷贝到dm中
func (dm *DurationMap) Copy(src DurationMap) {
	if src == nil {
		return
	}

	if *dm == nil {
		*dm = make(DurationMap)
	}

	for k, v := range src {
		(*dm)[k] = v
	}
}

// 根据服务类型设定pod调度类型
func GetPodSchedulingType(serviceType ServiceType) string {
	if serviceType == ServiceTypeCFCKata {
		return SchedulingTypeBalance
	} else {
		return SchedulingTypeCentral
	}
}
