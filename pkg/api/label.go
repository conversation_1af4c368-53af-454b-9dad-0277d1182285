package api

import (
	"errors"
	"fmt"
	"sort"
	"strings"
)

// node和pod使用的label
const (
	LabelUserID            = "userID"
	LabelAppID             = "appID"
	LabelCephfsZone        = "cephfsZone"
	LabelVpcConfigID       = "vpcConfigID"
	LabelContainerType     = "containerType"
	LabelSource            = "source"
	LabelVipUser           = "vipUser"
	LabelReserved          = "reserved"
	LabelCommonCluster     = "common"
	LabelNodeIsolationUser = "nodeIsolationUser"
	LabelResourcePool      = "resourcePool" // ResourcePool标签

	ReservedNodeType    = "node"
	ReservedPodType     = "pod"
	DefaultResourcePool = "default"
	DefaultVipUser      = "common"
)

const (
	// LabelValueBaiduInternal clusterLabel 之一，vipUser:baidu_internal
	// 标记k8s为内网集群
	LabelValueBaiduInternal = "baidu_internal"
)

// RequestLabels 是请求的label map，简化对nil map的操作
type RequestLabels map[string]string

// Copy 将labels中的值拷贝到l中
// 如果src是nil则跳过
func (l *RequestLabels) Copy(labels RequestLabels) {
	if labels == nil {
		return
	}

	if *l == nil {
		*l = make(RequestLabels)
	}

	for key, value := range labels {
		(*l)[key] = value
	}
}

// Get 返回对应label的value，如果l是空或label不存在则返回空字符串
func (l RequestLabels) Get(key string) string {
	if l == nil {
		return ""
	}
	return l[key]
}

// Delete 删除label，如果l是空或label不存在，不会报错
func (l RequestLabels) Delete(key string) {
	delete(l, key)
}

// Set 设置label，如果对象是空则自动初始化
func (l *RequestLabels) Set(key, value string) {
	if *l == nil {
		*l = make(RequestLabels)
	}
	(*l)[key] = value
}

// Equal 判断两个RequestLabels是否相同
func (l RequestLabels) Equal(label RequestLabels) bool {

	if (l == nil || len(l) == 0) && (label == nil || len(label) == 0) {
		return true
	}
	if l == nil || label == nil {
		return false
	}

	for k, v := range label {
		value, ok := l[k]
		if !ok || value != v {
			return false
		}
	}

	for k, v := range l {
		value, ok := label[k]
		if !ok || value != v {
			return false
		}
	}

	return true
}

const (
	LabelTypeCluster uint8 = iota
	LabelTypeNode
	LabelTypePod
)

// GetLabels 将labels按不同层分离出来
func (l RequestLabels) GetLabels(serviceType ServiceType, labelType uint8) (RequestLabels, error) {
	labelKeys, err := GetLabelKeysByServiceType(serviceType)
	if err != nil {
		return nil, err
	}

	var keys []string
	switch labelType {
	case LabelTypeCluster:
		keys = labelKeys.ClusterLabelKeys
	case LabelTypeNode:
		keys = labelKeys.NodeLabelKeys
	case LabelTypePod:
		keys = labelKeys.PodLabelKeys
	default:
		panic("wrong label type")
	}

	if len(keys) == 0 {
		return RequestLabels{}, nil
	}

	if l == nil {
		return nil, errors.New("empty label")
	}

	labels := make(RequestLabels)
	for _, key := range keys {
		value, ok := l[key]
		if !ok {
			// 🔑 关键修改：对于ResourcePool标签，如果缺失则跳过（向后兼容）
			if key == LabelResourcePool {
				continue
			}
			return nil, fmt.Errorf("lack of label %s", key)
		}
		labels[key] = value
	}
	return labels, nil

}

// SortedLabels 将labels按key的字典顺序排列，连接value，生成唯一的字符串
func (l RequestLabels) SortedLabels() string {
	if l == nil || len(l) == 0 {
		return ""
	}

	var size int // 用来估算字符串最终长度
	keys := make([]string, 0, len(l))
	for key, value := range l {
		keys = append(keys, key)
		size += len(strings.TrimSpace(value)) + 1
	}
	sort.Strings(keys)
	size -= 1

	var b strings.Builder
	b.Grow(size)
	for i, key := range keys {
		if i != 0 {
			// 写入分隔符
			b.WriteByte(',')
		}
		b.WriteString(strings.TrimSpace(l[key]))
	}
	return b.String()
}

type LabelKeys struct {
	ClusterLabelKeys []string
	NodeLabelKeys    []string
	PodLabelKeys     []string
}

// 各种服务的labels配置
var (
	cfcLabelsKeys = &LabelKeys{
		ClusterLabelKeys: []string{LabelVipUser, LabelResourcePool},
		NodeLabelKeys:    []string{LabelUserID, LabelVpcConfigID},
		PodLabelKeys:     nil,
	}

	cfcKataLabelsKeys = &LabelKeys{
		ClusterLabelKeys: []string{LabelContainerType},
		NodeLabelKeys:    nil,
		PodLabelKeys:     []string{LabelUserID},
	}

	bcfLabelsKeys = &LabelKeys{
		ClusterLabelKeys: []string{LabelCephfsZone},
		NodeLabelKeys:    nil,
		PodLabelKeys:     []string{LabelAppID, LabelUserID},
	}

	duedgeLabelsKeys = &LabelKeys{
		ClusterLabelKeys: nil,
		NodeLabelKeys:    nil,
		PodLabelKeys:     nil,
	}

	oteLabelsKeys = &LabelKeys{
		ClusterLabelKeys: nil,
		NodeLabelKeys:    []string{LabelUserID},
		PodLabelKeys:     nil,
	}
)

// GetLabelKeysByServiceType 根据服务类型确定每层资源的隔离label key，
func GetLabelKeysByServiceType(t ServiceType) (labelKeys *LabelKeys, err error) {
	switch t {
	case ServiceTypeCFC:
		labelKeys = cfcLabelsKeys
	case ServiceTypeCFCKata:
		labelKeys = cfcKataLabelsKeys
	case ServiceTypeBCH:
		labelKeys = bcfLabelsKeys
	case ServiceTypeDuedge:
		labelKeys = duedgeLabelsKeys
	case ServiceTypeOTE:
		labelKeys = oteLabelsKeys
	default:
		err = fmt.Errorf("unknown service type %s", t)
	}

	return
}
