package api

import (
	"fmt"
	"time"

	"github.com/elliotchance/pie/pie"
	"icode.baidu.com/baidu/faas/kun/pkg/util/json"
)

type NodeStateType string

const (
	NodeStateNil NodeStateType = ""

	NodeStateFrozen NodeStateType = "frozen"

	NodeStateScheduleBusy        NodeStateType = "SCHEDULE_busy"
	NodeStateScheduleInactive    NodeStateType = "SCHEDULE_inactive"
	NodeStateScheduleOffline     NodeStateType = "SCHEDULE_offline"
	NodeStateScheduleObsolete    NodeStateType = "SCHEDULE_obsolete"
	NodeStateScheduleError       NodeStateType = "SCHEDULE_error"
	NodeStateScheduleInitialized NodeStateType = "SCHEDULE_initialized"
	NodeStateScheduleOutdated    NodeStateType = "SCHEDULE_outdated"
	NodeStateScheduleUpgrading   NodeStateType = "SCHEDULE_upgrading"

	NodeStateK8sRaw          NodeStateType = "K8S_raw"
	NodeStateK8sError        NodeStateType = "K8S_error"
	NodeStateK8sInitializing NodeStateType = "K8S_initializing"
	NodeStateK8sInitialized  NodeStateType = "K8S_initialized"

	NodeStateCceError       NodeStateType = "CCE_error"
	NodeStateCceObsolete    NodeStateType = "CCE_obsolete"
	NodeStateCceRaw         NodeStateType = "CCE_raw"
	NodeStateCceScalingdown NodeStateType = "CCE_scalingdown"
	NodeStateCceDeleted     NodeStateType = "CCE_deleted"
)

func (t NodeStateType) IsError() bool {
	if t == NodeStateScheduleError || t == NodeStateK8sError || t == NodeStateCceError {
		return true
	}
	return false
}

func (t NodeStateType) IsNil() bool {
	return t == NodeStateNil
}

func (t NodeStateType) String() string {
	return string(t)
}

// pod级别扩缩容状态
const (
	PodNotScaling uint8 = iota
	PodScalingDown
	PodScalingUp
	PodScalingFailed
)

// PodStatus 存储node中的pod相关信息
type PodStatus struct {
	PodCount         int64     `json:"podCount"`         // 当前pod数量
	ExpectedPodCount int64     `json:"expectedPodCount"` // pod数量期望值
	ScalingStartTime time.Time `json:"scalingStartTime"` // pod扩缩容开始时间
	ScalingStatus    uint8     `json:"scalingStatus"`    // node的pod级别扩缩容状态
}

type NodeTrace struct {
	Source      string      `json:"source"`
	DurationMap DurationMap `json:"durationMap"`
}

type NodeInfo struct {
	ID                           string             `json:"id"`
	Name                         string             `json:"name"`
	ServiceType                  ServiceType        `json:"serviceType"`        // 服务类型，表示当前node可接收哪种服务的请求
	ClusterLabels                RequestLabels      `json:"clusterLabels"`      // 集群隔离方式，cron初始化时填入
	NodeLabels                   RequestLabels      `json:"nodeLabels"`         // node隔离方式，poolmgr占用时填入
	OriginalMemorySize           int64              `json:"originalMemorySize"` // 初始化 Pod 时的内存规格，单位为Mi
	MemoryAvailable              int64              `json:"memoryAvailable"`    // 总可用内存，单位为Mi
	OriginalCpuSize              int64              `json:"originalCpuSize"`    // 初始化 Pod 时的cpu requests，单位为m
	CpuAvailable                 int64              `json:"cpuAvailable"`       // 总可用cpu ，单位为m
	PodsNumAvailable             int64              `json:"podsNumAvailable"`   // 可创建Pod 的数量，需要减去deamonset和公共组件pod
	FloatingIP                   string             `json:"floatingIP"`         // floating IP
	InstanceShortId              string             `json:"instanceShortId"`    // bcc machine short id
	CceClusterUUID               string             `json:"cceClusterUUID"`     // cce cluster id
	CceInstanceID                string             `json:"cceInstanceID"`      // cce instance id
	ContainerImage               string             `json:"containerImage"`     // node runner container image
	KataContainerSpec            *KataContainerSpec `json:"kataContainerSpec"`  // node kata netmon container spec
	DsReleaseConfigMap           map[string]string  `json:"dsReleaseConfigMap"`
	OccupiedVersion              uint64             `json:"occupiedVersion"` // 每次被回收重置后加1，用来标识占用状态下的版本号
	State                        NodeStateType      `json:"state"`           // 当前状态
	StateUpdateTime              time.Time          `json:"stateUpdateTime"` // state最近更新事件，即上个成功或者进入错误状态的事件操作时间
	PrevEvent                    EventRecord        `json:"prevEvent"`       // 最近一次事件历史
	CreateTime                   time.Time          `json:"createTime"`
	UpdateTime                   time.Time          `json:"updateTime"`
	DeleteTime                   time.Time          `json:"deleteTime"`
	StartTime                    time.Time          `json:"startTime"` // 开始被使用的时间，由poolmgr占用node时写入
	InitTime                     time.Time          `json:"initTime"`  // 初始化的时间，由cron在初始化node时写入
	PodStatus                    `json:"podStatus"`
	InstanceLongID               string      `json:"instanceLongId"`               // node长id，创建弹性网卡时使用
	KataRuntimeImageID           string      `json:"kataRuntimeImageID"`           // 当前要使用的kata runtime
	AvailableKataRuntimeImageIDs pie.Strings `json:"availableKataRuntimeImageIDs"` // node上目前可用kata images
	KataImageGrayStatus          bool        `json:"kataRuntimeGrayStatus"`        // node上kata image是否在灰度中
	Flavor                       BccFlavor   `json:"flavor"`                       // 创建node时的cpu、内存配置
	Reserved                     bool        `json:"reserved"`                     // 预留标识
	GreInterfaceIP               string      `json:"greInterfaceIP"`               // 本node上要创建的gre网卡接口"mgre0"的ip地址
	ProxyHost                    string      `json:"proxyHost"`                    // 与本node建立隧道的proxy node的floatingIP地址
	VpcID                        string      `json:"vpcID"`                        // node上所属vpc网络的id
}

func (n *NodeInfo) MarshalBinary() (data []byte, err error) {
	data, err = json.Marshal(n)
	return
}

// Clone 返回原对象的深度拷贝
func (n *NodeInfo) Clone() *NodeInfo {
	newNode := *n
	newNode.ClusterLabels = nil
	newNode.NodeLabels = nil
	newNode.ClusterLabels.Copy(n.ClusterLabels)
	newNode.NodeLabels.Copy(n.NodeLabels)
	return &newNode
}

// GenerateID 通过node属性生成唯一id
func (n *NodeInfo) GenerateID() string {
	return n.CceClusterUUID + ":" + n.Name + ":" + n.InstanceShortId
}

// ClearAcquireInfo 清除被使用后被写入的属性
func (n *NodeInfo) ClearAcquireInfo() {
	n.NodeLabels = map[string]string{}
	n.StartTime = time.Time{}
	n.GreInterfaceIP = ""
	n.ProxyHost = ""
}

// ClearInitInfo 清除初始化后被写入的属性，包括使用后的遗留属性
func (n *NodeInfo) ClearInitInfo() {
	n.ClearAcquireInfo()
	n.ClusterLabels = make(RequestLabels)
	n.OriginalMemorySize = 0
	n.PodCount = 0
	n.InitTime = time.Time{}
	n.ContainerImage = ""
}

// Available 通过node的属性判断是否可用
func (n *NodeInfo) Available() bool {
	return n.FloatingIP != "" || n.MemoryAvailable == 0
}

func (n *NodeInfo) String() string {
	return fmt.Sprintf("<Node %s|%s|%s|%s|%s|%s|%d>", n.ID, n.FloatingIP, n.State, n.ServiceType,
		n.ClusterLabels, n.NodeLabels, n.OriginalMemorySize)
}

// EventRecord 用来记录事件操作日志
type EventRecord struct {
	EventName string        `json:"eventName"`
	SrcState  NodeStateType `json:"srcState"`
	DstState  NodeStateType `json:"dstState"` // 如果是空则说明事件执行不成功
	Error     string        `json:"error"`
	StartTime time.Time     `json:"startTime"`
	EndTime   time.Time     `json:"endTime"`
}

// node操作类型，用来作为前缀记录操作的错误次数
const (
	NodeOpTypeEvent         = "event"   // 事件错误，在事件成功或者转为错误状态后清除
	NodeOpTypeInspect       = "inspect" // 健康检查错误，在node被判断为健康或者时会被清除
	NodeOpTypeGlobal        = "global"  // 全局错误，不会清除
	NodeOpTypeAttach        = "attach"
	InspectErrorCallFunclet = "call_funclet"
)
