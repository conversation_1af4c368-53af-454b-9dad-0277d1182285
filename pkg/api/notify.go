package api

import (
	"icode.baidu.com/baidu/faas/kun/pkg/util/json"
)

type FunctionCacheNotifyInfo struct {
	Brn     string
	Version string
	Method  string
}

const (
	MethodCreate string = "create"
	MethodUpdate string = "update"
	MethodDelete string = "delete"
)

const (
	StateEnabled  string = "Enabled"
	StateDisabled string = "Disabled"
)

type cacheNotifyMsg interface {
	Marshal() (data []byte, err error)
	Unmarshal(data []byte) error
}

type FunctionCacheNotifyMsg struct {
	InfoList []*FunctionCacheNotifyInfo
}

func NewFunctionCacheNotifyMsg() *FunctionCacheNotifyMsg {
	return &FunctionCacheNotifyMsg{
		InfoList: make([]*FunctionCacheNotifyInfo, 0),
	}
}

func (fc *FunctionCacheNotifyMsg) AddInfo(brn, version, method string) *FunctionCacheNotifyMsg {
	info := &FunctionCacheNotifyInfo{
		Brn:     brn,
		Version: version,
		Method:  method,
	}
	fc.InfoList = append(fc.InfoList, info)
	return fc
}

func (fc *FunctionCacheNotifyMsg) Marshal() (data []byte, err error) {
	data, err = json.Marshal(fc)
	return
}

func (fc *FunctionCacheNotifyMsg) Unmarshal(data []byte) error {
	return json.Unmarshal(data, fc)
}

type PolicyCacheNotifyInfo struct {
	Key    string
	Method string
}

type PolicyCacheNotifyMsg struct {
	InfoList []*PolicyCacheNotifyInfo
}

func NewPolicyCacheNotifyMsg() *PolicyCacheNotifyMsg {
	return &PolicyCacheNotifyMsg{
		InfoList: make([]*PolicyCacheNotifyInfo, 0),
	}
}

func (pc *PolicyCacheNotifyMsg) AddInfo(key, method string) *PolicyCacheNotifyMsg {
	info := &PolicyCacheNotifyInfo{
		Key:    key,
		Method: method,
	}
	pc.InfoList = append(pc.InfoList, info)
	return pc
}

func (pc *PolicyCacheNotifyMsg) Marshal() (data []byte, err error) {
	data, err = json.Marshal(pc)
	return
}

func (pc *PolicyCacheNotifyMsg) Unmarshal(data []byte) error {
	return json.Unmarshal(data, pc)
}

type BillingCacheNotifyMsg struct {
	InfoList []*BillingCacheNotifyInfo
}

type BillingCacheNotifyInfo struct {
	Key    string
	Status string
	Method string
}

func NewBillingCacheNotifyMsg() *BillingCacheNotifyMsg {
	return &BillingCacheNotifyMsg{
		InfoList: make([]*BillingCacheNotifyInfo, 0),
	}
}

func (bc *BillingCacheNotifyMsg) AddInfo(key, method, status string) *BillingCacheNotifyMsg {
	info := &BillingCacheNotifyInfo{
		Key:    key,
		Method: method,
		Status: status,
	}
	bc.InfoList = append(bc.InfoList, info)
	return bc
}

func (bc *BillingCacheNotifyMsg) Marshal() (data []byte, err error) {
	data, err = json.Marshal(bc)
	return
}

func (bc *BillingCacheNotifyMsg) Unmarshal(data []byte) error {
	return json.Unmarshal(data, bc)
}

type BMSNotifyMsg struct {
	UUIDList []string // UUID 列表
	Method   string   // update create or delete
}

func NewBMSNotifyMsg() *BMSNotifyMsg {
	return &BMSNotifyMsg{
		UUIDList: make([]string, 0),
	}
}

func (bc *BMSNotifyMsg) AddInfo(uuid, method string) *BMSNotifyMsg {
	bc.UUIDList = append(bc.UUIDList, uuid)
	bc.Method = method
	return bc
}

func (bc *BMSNotifyMsg) Marshal() (data []byte, err error) {
	data, err = json.Marshal(bc)
	return
}

func (bc *BMSNotifyMsg) Unmarshal(data []byte) error {
	return json.Unmarshal(data, bc)
}

type CrontabNotifyMsg struct {
	UUIDList []string // UUID 列表
	Method   string   // update create or delete
}

func NewCrontabNotifyMsg() *CrontabNotifyMsg {
	return &CrontabNotifyMsg{
		UUIDList: make([]string, 0),
	}
}

func (cm *CrontabNotifyMsg) AddInfo(uuid, method string) *CrontabNotifyMsg {
	cm.UUIDList = append(cm.UUIDList, uuid)
	cm.Method = method
	return cm
}

func (cm *CrontabNotifyMsg) Marshal() (data []byte, err error) {
	data, err = json.Marshal(cm)
	return
}

func (cm *CrontabNotifyMsg) Unmarshal(data []byte) error {
	return json.Unmarshal(data, cm)
}

type AliasCacheNotifyInfo struct {
	Brn    string
	Method string
}

type AliasCacheNotifyMsg struct {
	InfoList []*AliasCacheNotifyInfo
}

func NewAliasCacheNotifyMsg() *AliasCacheNotifyMsg {
	return &AliasCacheNotifyMsg{
		InfoList: make([]*AliasCacheNotifyInfo, 0),
	}
}

func (ac *AliasCacheNotifyMsg) AddInfo(brn, method string) *AliasCacheNotifyMsg {
	info := &AliasCacheNotifyInfo{
		Brn:    brn,
		Method: method,
	}
	ac.InfoList = append(ac.InfoList, info)
	return ac
}

func (ac *AliasCacheNotifyMsg) Marshal() (data []byte, err error) {
	data, err = json.Marshal(ac)
	return
}

func (ac *AliasCacheNotifyMsg) Unmarshal(data []byte) error {
	return json.Unmarshal(data, ac)
}


type FunctionReservedNotifyMsg struct {
	UUIDList []string // UUID 列表
	Method   string   // update create or delete
}

func NewFunctionReservedNotifyMsg() *FunctionReservedNotifyMsg {
	return &FunctionReservedNotifyMsg{
		UUIDList: make([]string, 0),
	}
}

func (fr *FunctionReservedNotifyMsg) AddInfo(uuid, method string) *FunctionReservedNotifyMsg {
	fr.UUIDList = append(fr.UUIDList, uuid)
	fr.Method = method
	return fr
}

func (fr *FunctionReservedNotifyMsg) Marshal() (data []byte, err error) {
	data, err = json.Marshal(fr)
	return
}

func (fr *FunctionReservedNotifyMsg) Unmarshal(data []byte) error {
	return json.Unmarshal(data, fr)
}
