package api

import (
	"time"

	"icode.baidu.com/baidu/faas/kun/pkg/bce/sts_credential"
)

type InvokeResult struct {
	Status   string // 状态: success, timeout, failed
	MemUsed  int64  // 内存占用
	Duration int64  // 耗时 ms
}

type UserRequestLog struct {
	RequestID string
	RuntimeID string
	Function  *FunctionConfiguration
	LogFile   string

	Credential *sts_credential.StsCredential
	LogConfig  *LogConfiguration
	Result     *InvokeResult
}

type FluentdLogEntry struct {
	RequestID       string                 `json:"request_id"`
	RuntimeID       string                 `json:"runtime_id"`
	Function        *FunctionConfiguration `json:"function"`
	Result          *InvokeResult          `json:"result"`
	InvokeStartTime *time.Time             `json:"invoke_start_time,omitempty"`
}

type FunctionLog struct {
	InvokeResult
	RequestId       string
	FunctionName    string
	InvokeStartTime time.Time
	LogResults      *[]FunctionLogResult
}

type FunctionLogResult struct {
	Msg string    `json:"msg"`
	Src string    `json:"src"`
	Rid string    `json:"rid"`
	TS  time.Time `json:"ts"`
}
