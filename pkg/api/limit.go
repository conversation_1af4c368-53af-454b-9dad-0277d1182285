package api

import (
	"icode.baidu.com/baidu/faas/kun/pkg/util/bytefmt"
)

// 账户相关 limit
var (
	DefaultTotalCodeSizeLimit, _ = bytefmt.ToBytes("75GB")

	// DefaultConcurrency 默认账户并发额度
	DefaultAccountConcurrency = 100

	// DefaultAccountUnreservedConcurrencyMinimum 默认账户未预留并发度的最小值
	DefaultAccountUnreservedConcurrencyMinimum = 10
)

// 函数相关 limit
var (
	// 之前是500MB，2个70-80mb的压缩文件可能unzip超过500MB，放大
	DefaultCodeSizeLimitUnzipped, _ = bytefmt.ToBytes("10GB")
	DefaultCodeSizeLimitZipped, _   = bytefmt.ToBytes("150MB")
	// 之前是150M，对于ai相关的函数不够用
	DefaultLayerCodeSizeLimitZipped, _ = bytefmt.ToBytes("512MB")

	DefaultInvokePayloadLimitSync, _  = bytefmt.ToBytes("15MB")
	DefaultInvokePayloadLimitAsync, _ = bytefmt.ToBytes("256KB")

	DefaultEnvStrSizeLimit, _ = bytefmt.ToBytes("4KB")

	// DefaultReservedConcurrencyMinimum 函数预留并发度的最小值
	DefaultFuncReservedConcurrencyMinimum = 1

	// bos 侧创建函数的最大超时时间
	DefaultBosFunctionTimeout = 3600

	FunctionTimeoutMax    = 300
	FunctionMemorySizeMax = 1024

	CustomizedMemorySizeMax = FunctionMemorySizeMax * 3

	// 连接最大超时时间
	MaxRequestTimeout = 300

	// 单实例多并发最大限制
	DefaultPodConcurrentQuotaMax = 10

	// 单实例多并发默认值
	DefaultPodConcurrentQuota = 1

	// 用户并发度提升比例
	DefaultIncrUserConcurrency = 0.2

	ReservedInstanceEnabled = false

	StsUsers map[string]bool
)

// Cpu limit 的系数，比如计算结果为1000m，那最终设定的值为1000*0.8=800m
var CpulimitRatio = 0.6
