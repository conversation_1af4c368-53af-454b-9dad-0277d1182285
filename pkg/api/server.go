package api

const (
	//HeaderXRequestID       = "X-Bce-Request-Id"
	//HeaderXAccountID       = "X-Bce-Account-Id"
	AppNameKey    = "app"
	BceFaasUIDKey = "BCE-FAAS-UID"
	//BceFaasTriggerKey   = "X-Bce-Faas-Trigger"
	XBceFunctionError      = "X-Bce-Function-Error"
	XAmzFunctionError      = "X-Amz-Function-Error"
	HeaderXFetchSession    = "BCE-FAAS-FETCH-SESSION"
	HeaderXAuthToken       = "X-Auth-Token"
	HeaderSourceKey        = "X-Bce-Cfc-Source"
	HeaderClientip         = "Clientip"
	HeaderRequestExpiredAt = "Request-ExpiredAt"

	HeaderWorkspaceID = "X-CFC-Workspace-Id"

	HeaderServiceSource = "X-CFC-Service-Source"

	HeaderMqhubFinalRetry = "X-CFC-Mqhub-Final-Retry"

	HeaderSse              = "X-CFC-Sse" // 是否使用sse
	QueryParameterSession1 = "session_id"
	QueryParameterSession2 = "sessionId"

	HeaderPodReservedType      = "X-CFC-Reserved-Type" // 预留实例dryRun时，pod类型
	HeaderPodReservedId        = "X-CFC-Reserved-Id"   // 预留实例Id
	HeaderPodUserId            = "X-CFC-User-Id"       // 预留实例的userID
	HeaderFunctionReservedType = "X-CFC-Function-Type"

	// funclet和ReserveCollector鉴权头部
	HeaderAuthorization = "Authorization"
)

var (
	Resource       = "*"
	EnableCheckBms = true

	HeaderxBceDate           = "x-bce-date"
	HeaderxBceRequestId      = "x-bce-request-id"
	HeaderXBceInvocationType = "X-Bce-Invocation-Type"
	HeaderXBceSecurityToken  = "X-Bce-Security-Token"
	HeaderXRequestID         = "X-Bce-Request-Id"
	HeaderXAccountID         = "X-Bce-Account-Id"
	BceFaasTriggerKey        = "X-Bce-Faas-Trigger"
	HeaderXBceDate           = "X-Bce-Date"
	HeaderXSubjectToken      = "X-Subject-Token"
	HeaderXSubuserSupport    = "X-Subuser-Support"
	Headerxbcesecuritytoken  = "x-bce-security-token"
	HeaderXBceErrorType      = "X-Bce-ErrorType"
	HeaderXCFCInvokeType     = "X-CFC-Invoke-Type"
	HeaderXCFCCreateFrom     = "X-CFC-Create-From"
	HeaderXAuthType          = "X-Auth-Type"

	ReqService = "bce:cfc"
	AuthPrefix = "bce-auth-v1"
)

func InitHeader(enableInf bool) {

	if enableInf {
		HeaderxBceDate = "x-baidu-int-date"
		HeaderxBceRequestId = "x-baidu-int-request-id"
		HeaderXBceInvocationType = "X-baidu-int-Invocation-Type"
		HeaderXBceSecurityToken = "X-baidu-int-Security-Token"
		HeaderXRequestID = "X-baidu-int-Request-Id"
		HeaderXAccountID = "X-baidu-int-Account"
		//BceFaasTriggerKey = "X-baidu-int-Faas-Trigger"
		HeaderXBceDate = "X-baidu-int-Date"
		Headerxbcesecuritytoken = "x-baidu-int-security-token"
		HeaderXBceErrorType = "X-baidu-int-ErrorType"

		ReqService = "bce:cfc"
		AuthPrefix = "baidu-int-auth-v1"
	}
}
