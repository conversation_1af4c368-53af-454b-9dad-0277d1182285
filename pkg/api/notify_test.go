package api

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestNotify(t *testing.T) {
	type notifyTest struct {
		str      string
		msg      cacheNotifyMsg
		initFunc func(n *notifyTest)
	}

	cases := []*notifyTest{
		{
			str: `{"InfoList":[{"Brn":"brn","Version":"version","Method":"create"}]}`,
			msg: &FunctionCacheNotifyMsg{},
			initFunc: func(n *notifyTest) {
				m := &FunctionCacheNotifyMsg{}
				m.AddInfo("brn", "version", MethodCreate)
				n.msg = m
			},
		},
		{
			str: `{"InfoList":[{"Brn":"brn","Method":"create"}]}`,
			msg: &AliasCacheNotifyMsg{},
			initFunc: func(n *notifyTest) {
				m := &AliasCacheNotifyMsg{}
				m.AddInfo("brn", MethodCreate)
				n.msg = m
			},
		},
		{
			str: `{"InfoList":[{"Key":"brn","Method":"create"}]}`,
			msg: &PolicyCacheNotifyMsg{},
			initFunc: func(n *notifyTest) {
				m := &PolicyCacheNotifyMsg{}
				m.AddInfo("brn", MethodCreate)
				n.msg = m
			},
		},
		{
			str: `{"UUIDList":["uuid"],"Method":"create"}`,
			msg: &CrontabNotifyMsg{},
			initFunc: func(n *notifyTest) {
				m := &CrontabNotifyMsg{}
				m.AddInfo("uuid", MethodCreate)
				n.msg = m
			},
		},
		{
			str: `{"UUIDList":["uuid"],"Method":"create"}`,
			msg: &BMSNotifyMsg{},
			initFunc: func(n *notifyTest) {
				m := &BMSNotifyMsg{}
				m.AddInfo("uuid", MethodCreate)
				n.msg = m
			},
		},
		{
			str: `{"InfoList":[{"Key":"key","Status":"RUNNING","Method":"create"}]}`,
			msg: &BillingCacheNotifyMsg{},
			initFunc: func(n *notifyTest) {
				m := &BillingCacheNotifyMsg{}
				m.AddInfo("key", MethodCreate, "RUNNING")
				n.msg = m
			},
		},
		{
			str: `{"UUIDList":["uuid"],"Method":"create"}`,
			msg: &FunctionReservedNotifyMsg{},
			initFunc: func(n *notifyTest) {
				m := &FunctionReservedNotifyMsg{}
				m.AddInfo("uuid", MethodCreate)
				n.msg = m
			},
		},
	}
	for _, tc := range cases {
		if tc.initFunc != nil {
			tc.initFunc(tc)
		}
		data, err := tc.msg.Marshal()
		assert.Equal(t, nil, err)
		assert.Equal(t, string(data), tc.str)

	}

	fr := &FunctionReservedNotifyMsg{
		UUIDList: []string{"uuid"},
		Method: "create",
	}
	data, err := fr.Marshal()
	assert.Nil(t, err)
	err = fr.Unmarshal(data)
	assert.Nil(t, err)
}
