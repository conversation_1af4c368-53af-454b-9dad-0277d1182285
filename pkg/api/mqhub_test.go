package api

import (
	"github.com/stretchr/testify/assert"
	"icode.baidu.com/baidu/faas/kun/pkg/util/json"
	"testing"
)

func InitBosTriggerEvent() string{
	event := Event{
		Version: "1.0",
		EventId: "2a513199-bbb9-4ac7-b12a-60213c26810d",
		EventOrigin: "bos:realtime",
		EventTime: "2018-09-05T02:28:49Z",
		EventType: "PutObject",
	}
	content := &Content{
		UserId: "test",
		Domain: "bj.bcebos.com",
		Bucket: "bucketname",
		Object: "src/bos_test.jpg",
		Filesize: 2144,
	}
	event.Content = content

	var events = new(BosTriggerEvents)
	events.Events = append(events.Events, event)

	payload, _ := json.Marshal(events)
	return string(payload)
}

func TestGetEventPayloadInfo(t *testing.T) {
	payload := InitBosTriggerEvent()
	event_payload := GetEventPayloadInfo("bos", payload)
	assert.Equal(t, "bucketname_src/bos_test.jpg,", event_payload)
}
