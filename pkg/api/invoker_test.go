package api

import (
	"testing"
)

// TestInvocationInput_TriggerField 测试函数，用于调用 InvocationInput 的 TriggerField 字段
// 参数：t *testing.T - 类型为指针，表示需要进行单元测试的对象，值为 testing.T 类型
func TestInvocationInput_TriggerField(t *testing.T) {
	input := &InvocationInput{}
	input.Trigger = TriggerTypeHTTP
	if input.Trigger != TriggerTypeHTTP {
		t.Errorf("expected TriggerTypeHTTP, got %v", input.Trigger)
	}
	input.Trigger = TriggerTypeGeneric
	if input.Trigger != TriggerTypeGeneric {
		t.Errorf("expected TriggerTypeGeneric, got %v", input.Trigger)
	}
}
