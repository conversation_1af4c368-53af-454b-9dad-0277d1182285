package api

import (
	"github.com/elliotchance/pie/pie"
)

const (
	CCEClusterVersionV1 = "V1"
	CCEClusterVersionV2 = "V2"
)

// BceOptions
type BceOptions struct {
	AccessKeyID          string `json:"accessKeyID"`
	AccessKeySecret      string `json:"accessKeySecret"`
	CceEndpoint          string `json:"cceEndpoint"`
	CcrEndpoint          string `json:"ccrEndpoint"`
	BccEndpoint          string `json:"bccEndpoint"`
	BccOpenAPIEndpoint   string `json:"bccOpenAPIEndpoint"`
	SecurityGroupId      string `json:"securityGroupID"`
	SecurityGroupShortId string `json:"securityGroupShortId"`
	SecurityGroupName    string `json:"securityGroupName"`
	DockerHubUser        string `json:"dockerHubUser"`
	DockerHubPasswd      string `json:"dockerHubPasswd"`
	CcrProjectName       string `json:"ccrProjectName"`
}

// K8sOptions 是k8s的连接配置
type K8sOptions struct {
	Endpoint               string                     `json:"endpoint"`
	Version                string                     `json:"version"`
	Namespace              string                     `json:"namespace"`
	DaemonSetNamespace     string                     `json:"daemonSetNamespace"`
	ContainerImage         string                     `json:"containerImage"`
	DsReleaseConfigMap     map[string]DsReleaseConfig `json:"dsReleaseConfigMap"`
	ImagePullPolicy        string                     `json:"imagePullPolicy"`
	ImagePullSecrets       []string                   `json:"imagePullSecrets"`
	VolumeHostPath         string                     `json:"volumeHostPath"`
	VolumeMountPath        string                     `json:"volumeMountPath"`
	ClusterCA              string                     `json:"clusterCA"`
	RunAsNonRoot           bool                       `json:"runAsNonRoot"`
	RunAsUser              int64                      `json:"runAsUser"`
	SpecificResource       *SpecificResource          `json:"specificResource,omitempty"` // 为runner指定固定配置，默认不指定。参数格式参照k8s resources
	KataRuntimeImageID     string                     `json::"kataRuntimeImageID"`        // 当前kata runtime list
	KataRuntimeImageIDList pie.Strings                `json:"kataRuntimeImageIDList"`     //kata  node上需要提前下载好的runtime list列表
	KataContainerSpec      *KataContainerSpec         `json:"kataContainerSpec"`          //kata netmon容器相关配置

	CCEClusterAPIVersion string `json:"cceClusterAPIVersion"` // CCE 集群API接口版本，分别为V1 和 V2
}

type KataContainerSpec struct {
	Name             string   // kata容器名称netmon
	Image            string   // kata netmon镜像
	Command          []string // 启动命令
	CommandArgs      []string // 启动命令参数
	RuntimeHostPath  string   // runtime image 宿主机路径
	RuntimeMountPath string   // runtime 挂载到镜像路径
}

// runner 资源固定配置，参数内容参照k8s resources
type SpecificResource struct {
	Limits   map[string]string `json:"limits"`   // limits: 限制分配的资源。map key有效配置为cpu、memory。
	Requests map[string]string `json:"requests"` // requests: 请求分配的资源。map key有效配置为cpu、memory。
}

// OteOptions 是 ote 相关的配置信息
type OteOptions struct {
	OteEndpoint string             `json:"oteEndpoint"` // ote 兼容k8s API终端
	OteAuth     map[string]string  `json:"oteAuth"`     // ote k8s用户认证信息
	OteConfig   `json:"oteConfig"` // ote k8s相关配置信息
}

// OteConfig 是ote k8s相关配置信息
type OteConfig struct {
	IDC string `json:"idc"` // 集群的机房信息
}

type ZoneConfig struct {
	SubnetUUID    string `json:"subnetUUID"`
	InstanceTypes []int  `json:"instanceTypes"`
	VPCSubnetID   string `json:"vpcSubnetID"`
}

type DsReleaseConfig struct {
	Md5          string `json:"md5"`
	ImageID      string `json:"imageID"`
	YamlFilePath string `json:"yamlFilePath"`
}

// ScalingUpOptions 是node扩容的参数
// TODO: 未来合入ScalingOptions
type ScalingUpOptions struct {
	ZoneConfigMap            map[string]ZoneConfig `json:"zoneConfigMap"` // 多个zone与对应的subnet、instanceType等等
	Region                   string                `json:"region"`
	BccImageID               string                `json:"bccImageID"`
	OsType                   string                `json:"osType"`
	OsVersion                string                `json:"osVersion"`
	OsName                   string                `json:"osName"`
	OsArch                   string                `json:"osArch"`
	SsdMountPath             string                `json:"ssdMountPath"`
	AdminPassType            string                `json:"adminPassType"`
	AdminPass                string                `json:"adminPass"`
	Flavors                  []BccFlavor           `json:"flavors"`
	SingleScalingRequestSize int                   `json:"singleScalingRequestSize"`
	//ScalingOrder             bool                  `json:"scalingOrder"` // 扩容顺序，默认优先从小规格bcc扩容
}

const (
	AutoScalingTypeNil  = "nil"
	AutoScalingTypeNode = "node"
	AutoScalingTypePod  = "pod"
)

type BccFlavor struct {
	Cpu                  int     `json:"cpu"`
	Memory               int     `json:"memory"`
	RootDiskSizeInGb     int     `json:"rootDiskSizeInGb"`
	SsdDiskSize          int     `json:"ssdDiskSize"`
	MaxPodDeploy         int     `json:"maxPodDeploy"`         // 初始化node时允许部署的最大pod数量
	Spec                 string  `json:"spec"`                 //bcc规格
	InstanceType         int     `json:"instanceType"`         // bcc规格与机型对应关系
	Charge               float64 `json:"charge"`               // 价格
	CpuRequestLowerLimit int     `json:"cpuRequestLowerLimit"` // cpu request阈值
	RunningPodRatio      float64 `json:"runningPodRatio"`      // running pod的比例
	NodeUsedRatio        float64 `json:"nodeUsedRatio"`        // node的使用比例
}

// ScalingOptions 是扩容与缩容的参数
type ScalingOptions struct {
	AutoScalingType           string                      `json:"autoScalingType"`           // 自动扩容的方式，只能选node或pod
	ThresholdMap              map[int64]*ScalingThreshold `json:"thresholdMap"`              // 不同内存规格对应的扩缩容配置，当前只支持128
	ScalingDown               bool                        `json:"scalingDown"`               // 是否允许缩容
	ScalingTimeout            int                         `json:"scalingTimeout"`            // 扩缩容超时时间，当前暂时只用于pod扩缩容，单位是秒
	ScalingDownTimeout        int                         `json:"scalingDownTimeout"`        // 缩容等待超时时间，当前只用于node缩容，单位是秒
	MaxScalingUpSize          int                         `json:"maxScalingUpSize"`          // 一次扩容最大node数目
	MaxScalingDownSize        int                         `json:"maxscalingDownSize"`        // 一次缩容最大node数目
	ScalingDownCycleCnt       int                         `json:"scalingDownCycleCnt"`       // 缩容周期
	ScalingDownWhiteHours     []int                       `json:"scalingDownWhiteHours"`     // 避免缩容的时间，后期可以单独抽出来，做智能扩缩容
	K8sRawInitializingRatio   float64                     `json:"k8sRawInitializingRatio"`   // k8s_raw/k8s_initializing node数目到达阈值，不再扩容
	ScalingUpNodeNumThreshold int                         `json:"scalingUpNodeNumThreshold"` // 当集群中Node的个数大于该值时，才会启用k8sRawInitializingRatio检查
	MaxNodeNumThreshold       int                         `json:"maxNodeNumThreshold"`       // 当集群中Node的个数大于等于该值时，停止扩容
}

// ScalingThreshold 解析后的特定内存的扩缩容配置，表示node或pod的冗余比
type ScalingThreshold struct {
	UnoccupiedRedundancy uint64  `json:"unoccupiedRedundancy"` // 空闲的最小数量，不允许为0
	ActualRedundancy     uint64  `json:"ActualRedundancy"`     // 真正可用node数量（已经填到redis的cold node),作为缩容的最小保证
	ScalingUpTrigger     float64 `json:"scalingUpTrigger"`     // 触发扩容的比例
	// ScalingDownResult    float64 `json:"scalingDownResult"`    // 缩容后的比例
	ScalingDownTrigger float64 `json:"scalingDownTrigger"` // 触发缩容的比例

}

// NodeConfig 是node处理流程的相关配置
// TODO: 考虑提取部分公共配置到集群配置外，独立为一个全局配置
type NodeConfig struct {
	ServiceType             ServiceType       `json:"serviceType"`             // 集群的服务类型，node需要继承
	MatchLabels             map[string]string `json:"matchLabels"`             // 集群中的node需要匹配的k8s labels
	ClusterLabels           RequestLabels     `json:"clusterLabels"`           // 集群隔离方式，初始化时需要填入，供poolmgr查找辨认
	MemoryReserved          int64             `json:"memoryReserved"`          // 初始化node时为node预留的内存，单位是Mi
	PodMemoryReservedRatio  float64           `json:"podMemoryReservedRatio"`  // 初始化node时为pod预留的内存比例
	ApplyImageBatchNodeSize int               `json:"applyImageBatchNodeSize"` // 应用新runner image时批量执行node个数
	AutoApplyImage          bool              `json:"autoApplyImage"`          // 是否开启自动应用新的runner/daemonset Image， 默认是false
	SourcePodExpire         map[string]int    `json:"sourcePodExpire"`         // 不同的source label的pod对应的回收时间
	MaxNodeExpire           int               `json:"maxNodeExpire"`           // 允许node的最大存活时间，单位是秒
	SyncNode                bool              `json:"syncNode"`                // 是否读取集群node信息同步到etcd，如果不开则扩容无意义
	KataPodRebuildTimeOut   int64             `json:"kataPodRebuildTimeOut"`   // kata node的重建kata pod等待超时时间
	CpuReservedDaemonset    int64             `json:"cpuReservedDaemonset"`    // 初始化node时为node deamonset 预留的cpu requests，单位是m
	CpuUsageReservedRatio   float64           `json:"cpuUsageReservedRatio"`   // 初始化node时为pod预留的cpu使用比例, 防止cpu占用过高，导致pod pending
	DiffMemoryThreshold     int64             `json:"diffMemoryThreshold"`     // warm node内存差值阈值(真实剩余内存-redis中缓存的node库存)，单位是Mi, 超过该阈值，会触发outdate node
}

type ClusterStatusType uint8

const (
	ClusterStatusTypeNormal  ClusterStatusType = iota
	ClusterStatusTypeDeleted                   // 下线
	ClusterStatusTypeFrozen                    // 冻结，不执行会改变node数量的操作
)

// ResourcePoolConfig ResourcePool配置（功能分离开关设计 + 用户映射）
type ResourcePoolConfig struct {
	SchedulingEnabled  bool                     `json:"schedulingEnabled"`  // poolmanager调度开关：控制是否使用ResourcePool进行函数调度
	ScalingEnabled     bool                     `json:"scalingEnabled"`     // cron扩缩容开关：控制是否对ResourcePool进行资源扩缩容管理
	Version            string                   `json:"version"`            // 配置版本
	UserPoolMappings   map[string]string        `json:"userPoolMappings"`   // 用户到资源池的映射关系 uid -> poolName
	ExtraResourcePools map[string]*ResourcePool `json:"extraResourcePools"` // 额外资源池配置（使用指针提高效率）
}

// ResourcePool 资源池配置（双开关设计 + 用户专属池）
type ResourcePool struct {
	Enabled          bool                      `json:"enabled"`                  // 单个池子开关：控制该池子是否启用
	PoolType         ResourcePoolType          `json:"poolType,omitempty"`       // 池子类型：shared/dedicated/hybrid
	DedicatedUsers   []string                  `json:"dedicatedUsers,omitempty"` // 专属用户列表（仅对dedicated/hybrid类型有效）
	ResourcePoolInfo `json:"resourcePoolInfo"` // 池子基本信息
	ScalingOptions   *ScalingOptions           `json:"scalingOptions,omitempty"` // 池子扩缩容配置
}

// ResourcePoolType 资源池类型
type ResourcePoolType string

const (
	ResourcePoolTypeShared    ResourcePoolType = "shared"    // 共享池：所有用户可用
	ResourcePoolTypeDedicated ResourcePoolType = "dedicated" // 专属池：仅指定用户可用
	ResourcePoolTypeHybrid    ResourcePoolType = "hybrid"    // 混合池：优先专属用户，有空闲时其他用户可用
)

// ResourcePoolInfo 资源池基本信息
type ResourcePoolInfo struct {
	Description     string   `json:"description"`               // 资源池描述
	OSType          string   `json:"osType"`                    // 操作系统类型
	ContainerImage  string   `json:"containerImage,omitempty"`  // 容器镜像，可选
	SupportRuntimes []string `json:"supportRuntimes,omitempty"` // 支持的运行时列表
	DedicatedUsers  []string `json:"dedicatedUsers,omitempty"`  // 专属用户列表，为空表示所有用户可用
}

// K8sInfo 是k8s相关信息，存储在etcd
type K8sInfo struct {
	*BceOptions        `json:"bceOptions"`
	K8sOptions         `json:"k8sOptions"`
	OteOptions         `json:"oteOptions"`
	ScalingUpOptions   `json:"scalingUpOptions"`
	ScalingOptions     `json:"scalingOptions"`
	NodeConfig         `json:"nodeConfig"`
	CceClusterUUID     string              `json:"cceClusterUUID"`
	Status             ClusterStatusType   `json:"status"`
	ResourcePoolInfo   *ResourcePoolInfo   `json:"resourcePoolInfo,omitempty"`   // 默认池配置
	ResourcePoolConfig *ResourcePoolConfig `json:"resourcePoolConfig,omitempty"` // ResourcePool配置
}

// NewDefaultK8sInfo 生成k8sInfo，填入默认值
func NewDefaultK8sInfo() *K8sInfo {
	return &K8sInfo{
		K8sOptions: K8sOptions{
			Version:                "v1",
			RunAsUser:              0,
			Namespace:              "poolmgr",
			VolumeMountPath:        "/var/run/faas",
			ImagePullPolicy:        "IfNotPresent",
			ImagePullSecrets:       []string{"regsecret"},
			VolumeHostPath:         "/var/faas/invoker/run",
			DaemonSetNamespace:     "faas",
			SpecificResource:       nil,
			DsReleaseConfigMap:     nil,
			KataRuntimeImageID:     "",
			KataRuntimeImageIDList: []string{},
			KataContainerSpec:      nil,
			ClusterCA:              "apiVersion: v1\nclusters:\n- cluster:\n    server: https://127.0.0.1:8080\n  name: kubernetes\ncontexts:\n- context:\n    cluster: kubernetes\n    user: kubernetes-admin\n  name: kubernetes-admin@kubernetes\ncurrent-context: kubernetes-admin@kubernetes\nkind: Config\npreferences: {}\nusers:\n- name: kubernetes-admin\n",
		},
		ScalingUpOptions: ScalingUpOptions{
			OsType:        "linux",
			OsVersion:     "16.04 LTS amd64 (64bit)",
			SsdMountPath:  "/data",
			Region:        "bj",
			AdminPassType: "random",
			AdminPass:     "",
			ZoneConfigMap: map[string]ZoneConfig{},
			Flavors: []BccFlavor{
				{
					Cpu:                  2,
					Memory:               8,
					SsdDiskSize:          50,
					MaxPodDeploy:         35,
					CpuRequestLowerLimit: 42,
					RunningPodRatio:      0.8,
					NodeUsedRatio:        0.9,
				},
			},
		},
		ScalingOptions: ScalingOptions{
			AutoScalingType: AutoScalingTypeNode,
			ThresholdMap: map[int64]*ScalingThreshold{
				128: {
					UnoccupiedRedundancy: 10,
					ScalingUpTrigger:     1.5,
					// ScalingDownResult:    1.8,
					ScalingDownTrigger: 2.0,
					ActualRedundancy:   6,
				},
			},
			ScalingDown:               true,
			ScalingTimeout:            60,
			ScalingDownTimeout:        60 * 15,
			MaxScalingDownSize:        10,
			MaxScalingUpSize:          50,
			ScalingDownWhiteHours:     []int{},
			ScalingDownCycleCnt:       1,
			K8sRawInitializingRatio:   0.4,
			ScalingUpNodeNumThreshold: 50,
			MaxNodeNumThreshold:       500,
		},
		NodeConfig: NodeConfig{
			ServiceType:             ServiceTypeCFC,
			ClusterLabels:           make(RequestLabels),
			MatchLabels:             map[string]string{},
			MemoryReserved:          500,
			PodMemoryReservedRatio:  1.2,
			AutoApplyImage:          false,
			ApplyImageBatchNodeSize: 1,
			MaxNodeExpire:           86400,
			SyncNode:                true,
			KataPodRebuildTimeOut:   120,
			CpuReservedDaemonset:    505,
			CpuUsageReservedRatio:   1.0,
			DiffMemoryThreshold:     4096,
		},
		Status: ClusterStatusTypeNormal,
		BceOptions: &BceOptions{
			BccEndpoint:        "http://bcclogic.bce-internal.baidu.com",
			BccOpenAPIEndpoint: "http://bcc.bj.baidubce.com",
			CcrEndpoint:        "http://ccr.baidubce.com",
			CceEndpoint:        "http://cce.bj.baidubce.com",
		},
		ResourcePoolInfo:   new(ResourcePoolInfo),
		ResourcePoolConfig: new(ResourcePoolConfig),
	}
}

// CronResponse xxx
type CronResponse struct {
	IsSuccess bool   `json:"isSuccess"`
	Message   string `json:"message"`
}

// ModifyDuedgeNodeResponse by duedge
type ModifyDuedgeNodeResponse struct {
	IsSuccess bool      `json:"isSuccess"`
	Message   string    `json:"message"`
	Node      *NodeInfo `json:"node"`
}

// GetNodeResponse xxx
type GetNodeResponse struct {
	IsSuccess bool        `json:"isSuccess"`
	Message   string      `json:"message"`
	Node      []*NodeInfo `json:"node"`
}

const (
	K8sKubeSystemNamespace = "kube-system"
	K8sPoolmgrNamespace    = "poolmgr"
	K8sDeamonSetPodCount   = 5
)
