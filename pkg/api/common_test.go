package api_test

import (
	"github.com/stretchr/testify/assert"
	"icode.baidu.com/baidu/faas/kun/pkg/api"
	"testing"
)

func TestParseCBDFilter(t *testing.T) {
	cases := []struct {
		in_s        string
		funcName    string
		description string
	}{
		{
			in_s:        "",
			funcName:    "",
			description: "",
		},
		{
			in_s:        "FunctionName",
			funcName:    "",
			description: "",
		},
		{
			in_s:        "FunctionName:login",
			funcName:    "login",
			description: "",
		},
		{
			in_s:        "FunctionName:login,Description:test",
			funcName:    "login",
			description: "test",
		},
	}

	for _, tc := range cases {
		name, desc := api.ParseCBDFilter(tc.in_s)
		assert.Equal(t, tc.funcName, name, tc.description, desc)
	}
}

func TestParseCBDOrder(t *testing.T) {
	cases := []struct {
		in_s  string
		order map[string]string
	}{
		{
			in_s:  "",
			order: map[string]string{"CreatedAt": "desc"},
		},
		{
			in_s:  "FunctionName",
			order: map[string]string{"FunctionName": "desc"},
		},
		{
			in_s:  "FunctionName:",
			order: map[string]string{"FunctionName": "desc"},
		},
		{
			in_s:  "FunctionName:asc,Description:desc",
			order: map[string]string{"FunctionName": "asc", "Description": "desc"},
		},
	}

	for _, tc := range cases {
		orderMap := api.ParseCBDOrder(tc.in_s)
		assert.Equal(t, tc.order, orderMap)
	}
}
