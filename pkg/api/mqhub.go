package api

import (
	"github.com/asaskevich/govalidator"
	"go.uber.org/zap/zapcore"
	"icode.baidu.com/baidu/faas/kun/pkg/util/json"
)

const (
	MaxAttemptsDefault uint16 = 3
	MaxAttemptsBos            = 3

	OnSuccessType = "Success"
	OnFailureType = "Fail"
)

const (
	EventSourceMappingErrorReason = "Unrecoverable error" // 参照lambda
)

type EventMessage struct {
	EventPayload      string `valid:"required"` //用户发送到eventhub 的event
	Attempts          uint16 //尝试次数
	ErrorAttempts     uint16 //错误次数
	MaxAttempts       uint16 //最大尝试次数
	Timestamp         int64  //执行时间
	UserID            string `valid:"required"` //用户的ID
	Timeout           int    `valid:"required"` //用户设置的超时时间
	Brn               string `valid:"required"`
	FunctionName      string `valid:"required"`
	Qualifier         string
	XBceRequestId     string `valid:"required"` //request id
	LastErrMsg        string //上次执行失败的错误
	ReceiveTs         int64  //首次收到消息的时间
	Trigger           TriggerType
	DeadLetterTopic   string             //用户死信队列配置
	SourceIp          string             //用户请求的源ip
	AsyncInvokeConfig *AsyncInvokeConfig // 异步调用配置
}

func (msg *EventMessage) MarshalLogObject(enc zapcore.ObjectEncoder) error {
	enc.AddString("request_id", msg.XBceRequestId)
	enc.AddString("brn", msg.Brn)
	enc.AddString("uid", msg.UserID)
	enc.AddString("trigger", string(msg.Trigger))
	enc.AddInt64("receive_ts", msg.ReceiveTs)
	enc.AddUint16("attempts", msg.Attempts)
	enc.AddUint16("error_attempts", msg.ErrorAttempts)
	enc.AddUint16("max_attempts", msg.MaxAttempts)

	// 处理bos触发器event payload
	bosInfo := GetEventPayloadInfo(msg.Trigger, msg.EventPayload)
	enc.AddString("event_payload", bosInfo)
	return nil
}

// getBosTriggerEventInfo 获取bos触发器event信息，格式为bucket_object，多个event间用逗号隔开
func GetEventPayloadInfo(trigger string, payload string) string {
	var events = new(BosTriggerEvents)
	var res string
	if trigger == "bos" {
		json.Unmarshal([]byte(payload), &events)
		if events != nil {
			for _, e := range events.Events {
				if e.Content != nil {
					bosInfo := e.Content.Bucket + "_" + e.Content.Object
					res = res + bosInfo + ","
				}
			}
		}
	}
	return res
}

func (msg *EventMessage) Validate() error {
	_, err := govalidator.ValidateStruct(msg)
	return err
}

type SyncInvokeInput struct {
	Payload       []byte
	Brn           string
	XBceRequestId string //request id
	UserID        string
	Trigger       TriggerType
	SourceIp      string
	FailNotReport bool
}

// bos触发器event结构
type Credentials struct {
	AccessKeyId     string `json:"accessKeyId,omitempty"`
	SecretAccessKey string `json:"secretAccessKey,omitempty"`
	Expiration      string `json:"expiration,omitempty"`
}

type XVars struct {
	SaveUrl string `json:"saveUrl,omitempty"`
}

type Content struct {
	UserId       string       `json:"userId"`
	Domain       string       `json:"domain,omitempty"`
	Bucket       string       `json:"bucket"`
	Object       string       `json:"object"`
	Etag         string       `json:"etag,omitempty"`
	ContentType  string       `json:"contentType,omitempty"`
	Filesize     int          `json:"filesize,omitempty"`
	LastModified string       `json:"lastModified,omitempty"`
	Credentials  *Credentials `json:"credentials,omitempty"`
	XVars        *XVars       `json:"xVars,omitempty"`
}
type Event struct {
	Version     string   `json:"version,omitempty"`
	EventId     string   `json:"eventId,omitempty"`
	EventOrigin string   `json:"eventOrigin,omitempty"`
	EventTime   string   `json:"eventTime,omitempty"`
	EventType   string   `json:"eventType,omitempty"`
	Content     *Content `json:"content,omitempty"`
}

type BosTriggerEvents struct {
	Events []Event `json:"events,omitempty"`
}