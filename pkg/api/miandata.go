package api

import "time"

type FreeNewUser struct {
	AccountId              string    `json:"accountId"`
	Region                 string    `json:"region"`
	CreatedAt              time.Time `json:"createdAt"`
	FreepackagePurchasedAt time.Time `json:"-"`
	FreepackageOrders      string    `json:"freepackageOrders"`
}

type CreateFreeNewUserRequest struct {
	AccountId string `json:"accountId"`
	Region    string `json:"region"`
}

type UpdateFreeNewUserRequest struct {
	Region            string `json:"region"`
	FreepackageOrders string `json:"freepackageOrders"`
}

type MaindataResp struct {
	RequestID string `json:"requestId"`
	Code      string `json:"code"`
	Message   string `json:"message"`
}
