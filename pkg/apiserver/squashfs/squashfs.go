package squashfs

import (
	"fmt"
	"os/exec"
	"path/filepath"
)

const SquashFsCmd = "mksquashfs"

type SquashFsClient struct {
	options *SquashFsOptions
}

func NewSquashFsClient(options *SquashFsOptions) *SquashFsClient {
	return &SquashFsClient{
		options: options,
	}
}

func (c *SquashFsClient) GetSquashFsCodeSize() int32 {
	return c.options.MakeSquashFsCodeSize
}

func (c *SquashFsClient) GetSquashFsFileNum() int {
	return c.options.MakeSquashFsFileNum
}

func (c *SquashFsClient) MkSquashFs(sourceDir, targetFile string) error {
	cmdPath := SquashFsCmd
	if c.options.SquashFsToolsPath != "" {
		cmdPath = filepath.Join(c.options.SquashFsToolsPath, cmdPath)
	}
	arg := []string{sourceDir, targetFile}
	arg = append(arg, c.options.MkSquashFsOptions...)
	cmd := exec.Command(cmdPath, arg...)
	cmd.Env = []string{"PATH=/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin"}
	msg, err := cmd.CombinedOutput()
	if err != nil {
		return fmt.Errorf("%s %v", msg, err)
	}
	return nil
}
