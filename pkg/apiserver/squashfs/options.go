package squashfs

import (
	"github.com/spf13/pflag"
)

type SquashFsOptions struct {
	EnableSquashFs       bool     `yaml:"EnableSquashFs"`
	MakeSquashFsCodeSize int32    `yaml:"MakeSquashFsCodeSize"`
	MakeSquashFsFileNum  int      `yaml:"MakeSquashFsFileNum"`
	SquashFsToolsPath    string   `yaml:"SquashFsToolsPath"` //squashFs-tools路径
	MkSquashFsOptions    []string `yaml:"MkSquashFsOptions"` //mksuqashfs 参数
}

func NewSquashFsOptions() *SquashFsOptions {
	return &SquashFsOptions{
		EnableSquashFs:       false,
		MakeSquashFsCodeSize: 5 * 1024 * 1024,
		MakeSquashFsFileNum:  500,
		MkSquashFsOptions:    []string{"-comp", "gzip", "-Xcompression-level", "1", "-noI", "-noD", "-noF", "-no-sparse", "-no-fragments"},
	}
}

func (s *SquashFsOptions) AddFlags(fs *pflag.FlagSet) {
	// 超过以下限制将制作SquashFs镜像
	fs.BoolVar(&s.EnableSquashFs, "enable-squashfs", s.EnableSquashFs, "enable-squashfs")
	fs.Int32Var(&s.MakeSquashFsCodeSize, "make-sqfs-code-size", s.MakeSquashFsCodeSize, "if code size larger than this makes squashFs images")
	fs.IntVar(&s.MakeSquashFsFileNum, "make-sqfs-file-num", s.MakeSquashFsFileNum, "if code file num more than this makes squashFs images")
	fs.StringVar(&s.SquashFsToolsPath, "squashfs-tools-path", s.SquashFsToolsPath, "squashfs-tools-path")
	fs.StringSliceVar(&s.MkSquashFsOptions, "mksquashfs-args", s.MkSquashFsOptions, "mksquashfs options")
}
