package client

import (
	"fmt"
	"net/http"
	"net/url"
	"time"

	"icode.baidu.com/baidu/faas/kun/pkg/api"
	"icode.baidu.com/baidu/faas/kun/pkg/rest"
)

type ApiserverInterface interface {
	// ListFunctionVpcConfigs 拉取函数 vpc 配置
	ListFunctionVpcConfigs() ([]*api.NetworkConfig, error)

	// NotifyProxyReady 回调通知 apiserver，回调时传入当前的 vpc config 参数
	NotifyProxyReady(*api.NetworkConfig) error
}

type ApiserverClient struct {
	client   *rest.RESTClient
	protocol string
	host     string
	port     int
}

func NewApiserverClient(runOptions *ApiserverOptions) ApiserverInterface {
	config := rest.ContentConfig{
		BackendType: rest.BackendTypeInternal,
	}

	httpClient := &http.Client{
		Transport: &http.Transport{
			MaxIdleConnsPerHost: 300,
			MaxIdleConns:        300 * 10,
		},
	}

	baseURL, _ := url.Parse(fmt.Sprintf("%s://%s:%d", "http", runOptions.Host, runOptions.Port))
	client, _ := rest.NewRESTClient(baseURL, "inside-v1", config, httpClient)
	return &ApiserverClient{
		client:   client,
		protocol: "http",
		host:     runOptions.Host,
		port:     runOptions.Port,
	}
}

func (a *ApiserverClient) ListFunctionVpcConfigs() ([]*api.NetworkConfig, error) {
	resp := a.client.Get().
		Resource("vpc/configs").
		Timeout(30 * time.Second).
		Do()

	if resp.Error() != nil {
		return nil, resp.Error()
	}

	cfgs := make([]*api.NetworkConfig, 0)
	err := resp.Into(&cfgs)
	return cfgs, err
}

func (a *ApiserverClient) NotifyProxyReady(cfg *api.NetworkConfig) error {
	resp := a.client.Post().
		Resource("vpc/notify").
		Timeout(10 * time.Second).
		Body(cfg).
		Do()

	if resp.Error() != nil {
		return resp.Error()
	}

	var status int
	resp.StatusCode(&status)
	if status == http.StatusOK {
		return nil
	}
	return resp.Error()
}
