package billing

import (
	"github.com/emicklei/go-restful"
	"icode.baidu.com/baidu/faas/kun/cmd/apiserver/filter"
	"icode.baidu.com/baidu/faas/kun/pkg/middleware"
	"icode.baidu.com/baidu/faas/kun/pkg/server"
	"icode.baidu.com/baidu/faas/kun/pkg/server/endpoint"
)

// APIs 返回一个包含所有API信息的切片，每个元素都是一个endpoint.ApiSingle类型。
// 这些API可以用于RESTful API服务器的注册和路由。
func (r OrderRest) APIs() []endpoint.ApiSingle {
	apis := []endpoint.ApiSingle{
		endpoint.ApiSingle{
			Verb:    "POST",
			Path:    "/order_executor/execute",
			Handler: server.WrapRestRouteFunc(r.orderExecutor),
			Filters: []restful.FilterFunction{
				filter.IAMPermissionCheck("*", "", []string{"cfc:order_executor", filter.IamPermRead}),
				filter.CheckUserOrderExecutor,
			},
		},
		endpoint.ApiSingle{
			Verb:    "GET",
			Path:    "/order_executor/check",
			Handler: server.WrapRestRouteFunc(r.checkOrder),
			Filters: []restful.FilterFunction{
				filter.IAMPermissionCheck("*", "", []string{"cfc:order_check", filter.IamPermRead}),
				filter.CheckUserOrderExecutor,
			},
		},
		endpoint.ApiSingle{
			Verb:    "POST",
			Path:    "/order_package/createPackageOrder",
			Handler: server.WrapRestRouteFunc(r.createPackageOrder),
			Filters: []restful.FilterFunction{
				filter.IAMPermissionCheck("*", "", []string{"cfc:create_package_order", filter.IamPermRead}),
				filter.RealNameCheck,
			},
		},
		endpoint.ApiSingle{
			Verb:    "POST",
			Path:    "/order/detail",
			Handler: server.WrapRestRouteFunc(r.queryOrderDetail),
			Filters: []restful.FilterFunction{
				filter.IAMPermissionCheck("*", "", []string{"cfc:query_order_detail", filter.IamPermRead}),
			},
		},
		endpoint.ApiSingle{
			Verb:    "POST",
			Path:    "/order_package/getByServiceTypeV3",
			Handler: server.WrapRestRouteFunc(r.queryPackageInfo),
			Filters: []restful.FilterFunction{
				filter.IAMPermissionCheck("*", "", []string{"cfc:query_package_info", filter.IamPermRead}),
			},
		},
		endpoint.ApiSingle{
			Verb:    "GET",
			Path:    "billing/{accountId}/cfc/resources/{resourceId}",
			Handler: server.WrapRestRouteFunc(r.insideFind),
			Filters: []restful.FilterFunction{
				filter.IAMPermissionCheck("*", "", []string{"cfc:query_resource_state", filter.IamPermRead}),
				filter.CheckUserBilling,
			},
		},
		endpoint.ApiSingle{
			Verb:    "PUT",
			Path:    "billing/{accountId}/cfc/resources/{resourceId}",
			Handler: server.WrapRestRouteFunc(r.updateOrderState),
			Filters: []restful.FilterFunction{
				filter.IAMPermissionCheck("*", "", []string{"cfc:update_resource_State", filter.IamPermRead}),
				filter.CheckUserBilling,
			},
		},
		endpoint.ApiSingle{
			Verb:    "GET",
			Path:    "billing/{accountId}/resources/usage",
			Handler: server.WrapRestRouteFunc(r.getResourceUsage),
			Filters: []restful.FilterFunction{
				filter.IAMPermissionCheck("*", "", []string{"cfc:Get_Resources_Usage", filter.IamPermRead}),
			},
		},
		endpoint.ApiSingle{
			Verb:    "POST",
			Path:    "billing/newuser",
			Handler: server.WrapRestRouteFunc(r.createNewFreeUser),
			Filters: []restful.FilterFunction{
				filter.IAMPermissionCheck("*", "", []string{"cfc:Create_New_Free_User", filter.IamPermRead}),
			},
		},
		endpoint.ApiSingle{
			Verb:    "GET",
			Path:    "billing/newuser/{accountId}",
			Handler: server.WrapRestRouteFunc(r.findNewFreeUser),
			Filters: []restful.FilterFunction{
				filter.IAMPermissionCheck("*", "", []string{"cfc:Find_New_Free_User", filter.IamPermRead}),
			},
		},
		endpoint.ApiSingle{
			Verb:    "POST",
			Path:    "billing/autoRenew",
			Handler: server.WrapRestRouteFunc(r.createAutoRenew),
			Filters: []restful.FilterFunction{
				filter.IAMPermissionCheck("*", "", []string{"cfc:Create_AutoRenew", filter.IamPermRead}),
			},
		},
		endpoint.ApiSingle{
			Verb:    "DELETE",
			Path:    "billing/autoRenew/{accountId}",
			Handler: server.WrapRestRouteFunc(r.deleteAutoRenew),
			Filters: []restful.FilterFunction{
				filter.IAMPermissionCheck("*", "", []string{"cfc:Delete_AutoRenew", filter.IamPermRead}),
			},
		},
		endpoint.ApiSingle{
			Verb:    "GET",
			Path:    "billing/autoRenew/{accountId}",
			Handler: server.WrapRestRouteFunc(r.findAutoRenew),
			Filters: []restful.FilterFunction{
				filter.IAMPermissionCheck("*", "", []string{"cfc:Find_AutoRenew", filter.IamPermRead}),
			},
		},
	}
	return apis
}

func (r OrderRest) InsideAPIs() []endpoint.ApiSingle {
	apis := []endpoint.ApiSingle{
		// get invite link, GET /inside-v1/billing/{accountId}
		endpoint.ApiSingle{
			Verb:    "GET",
			Path:    "billing/{accountId}",
			Handler: server.WrapRestRouteFuncWithTrace("ApiServerHandler/insideFindBilling", r.insideFind),
			Filters: []restful.FilterFunction{
				middleware.SimpleAuthTokenVerify(),
			},
		},
		endpoint.ApiSingle{
			Verb:    "POST",
			Path:    "billing/autoRenew",
			Handler: server.WrapRestRouteFunc(r.insideCreateAutoRenew),
			Filters: []restful.FilterFunction{
				middleware.SimpleAuthTokenVerify(),
			},
		},
		endpoint.ApiSingle{
			Verb:    "DELETE",
			Path:    "billing/autoRenew/",
			Handler: server.WrapRestRouteFunc(r.insideDeleteAutoRenew),
			Filters: []restful.FilterFunction{
				middleware.SimpleAuthTokenVerify(),
			},
		},
	}
	return apis
}
