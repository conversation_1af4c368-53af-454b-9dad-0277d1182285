package billing

import (
	"fmt"
	"testing"

	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/order"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/stretchr/testify/assert"
	"icode.baidu.com/baidu/faas/kun/pkg/api"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/dbengine"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/global"
	"icode.baidu.com/baidu/faas/kun/pkg/server"
)

func TestBillingAPIS(t *testing.T) {
	r := OrderRest{}
	r.APIs()
	r.InsideAPIs()
}

func TestUpdateOrderState(t *testing.T) {
	global.MockAC()

	cases := []struct {
		in_c         *server.Context
		out_HttpCode int
	}{
		{
			in_c:         global.BuildNewKunCtx("PUT", "/v1/billing/c7ac82ae14ef42d1a4ffa3b2ececa17f/cfc/resources/111?action=START", `{"region":"bj","status":"CREATED","productType":"postpay"}`, "abc", map[string]string{}),
			out_HttpCode: 200,
		},
		{
			in_c:         global.BuildNewKunCtx("PUT", "/v1/billing/c7ac82ae14ef42d1a4ffa3b2ececa17f/cfc/resources/111?action=STOP", `{"region":"bj","status":"CREATED","productType":"postpay"}`, "abc", map[string]string{}),
			out_HttpCode: 200,
		},
		{
			in_c:         global.BuildNewKunCtx("PUT", "/v1/billing/c7ac82ae14ef42d1a4ffa3b2ececa17f/cfc/resources/111?action=DELETE", `{"region":"bj","status":"CREATED","productType":"postpay"}`, "abc", map[string]string{}),
			out_HttpCode: 200,
		},
		{
			in_c:         global.BuildNewKunCtx("PUT", "/v1/billing/c7ac82ae14ef42d1a4ffa3b2ececa17f/cfc/resources/111?action=A", `{"region":"bj","status":"CREATED","productType":"postpay"}`, "", map[string]string{}),
			out_HttpCode: 400,
		},
		{
			in_c:         global.BuildNewKunCtx("PUT", "/v1/billing/c7ac82ae14ef42d1a4ffa3b2ececa17f/cfc/resources/111?action=A", "", "abc", map[string]string{}),
			out_HttpCode: 500,
		},
	}
	for _, tc := range cases {
		var m sqlmock.Sqlmock
		m, dbengine.Engine = global.MockDB()
		m.ExpectExec(".*").WillReturnResult(sqlmock.NewResult(1, 1))
		f := OrderRest{}
		f.updateOrderState(tc.in_c)
		assert.Equal(t, tc.out_HttpCode, tc.in_c.Response().StatusCode())
	}
}

func TestCreatePackageOrder(t *testing.T) {
	global.MockAC()
	order.MockOrderManager()
	cases := []struct {
		in_c         *server.Context
		out_HttpCode int
	}{
		{
			in_c:         global.BuildNewKunCtx("post", "/v1/order_package/createPackageOrder", `{"CouponId":"","count":1,"packageType":"prepay","disposable":true,"flavorInfo":[{"typeName":"subServiceType","typeValue":"FunctionCallCount","typeScale":1,"specName":"count","specValue":"1000000000"},{"typeName":"subServiceType","typeValue":"PublicDataTransfer","typeScale":1,"specName":"capacity","specValue":"100G"},{"typeName":"subServiceType","typeValue":"FunctionRunTimeCount","typeScale":1,"specName":"count","specValue":"1000000000"}]}`, "abc", map[string]string{}),
			out_HttpCode: 200,
		},
		{
			in_c:         global.BuildNewKunCtx("post", "/v1/order_package/createPackageOrder", `{"CouponId":"","count":1,"packageType":"prepay","disposable":false,"flavorInfo":[{"typeName":"subServiceType","typeValue":"FunctionCallCount","typeScale":1,"specName":"count","specValue":"1000000000"},{"typeName":"subServiceType","typeValue":"PublicDataTransfer","typeScale":1,"specName":"capacity","specValue":"100G"},{"typeName":"subServiceType","typeValue":"FunctionRunTimeCount","typeScale":1,"specName":"count","specValue":"1000000000"}]}`, "abc", map[string]string{}),
			out_HttpCode: 200,
		},
		{
			in_c:         global.BuildNewKunCtx("post", "/v1/order_package/createPackageOrder", `{"CouponId":"","count":2,"packageType":"prepay","disposable":true,"flavorInfo":[{"typeName":"subServiceType","typeValue":"FunctionCallCount","typeScale":1,"specName":"count","specValue":"1000000000"},{"typeName":"subServiceType","typeValue":"PublicDataTransfer","typeScale":1,"specName":"capacity","specValue":"100G"},{"typeName":"subServiceType","typeValue":"FunctionRunTimeCount","typeScale":1,"specName":"count","specValue":"1000000000"}]}`, "abc", map[string]string{}),
			out_HttpCode: 500,
		},
		{
			in_c:         global.BuildNewKunCtx("post", "/v1/order_package/createPackageOrder", `{"CouponId":"","count":3,"packageType":"prepay","disposable":true,"flavorInfo":[{"typeName":"subServiceType","typeValue":"FunctionCallCount","typeScale":1,"specName":"count","specValue":"1000000000"},{"typeName":"subServiceType","typeValue":"PublicDataTransfer","typeScale":1,"specName":"capacity","specValue":"100G"},{"typeName":"subServiceType","typeValue":"FunctionRunTimeCount","typeScale":1,"specName":"count","specValue":"1000000000"}]}`, "abc", map[string]string{}),
			out_HttpCode: 500,
		},
		{
			in_c:         global.BuildNewKunCtx("post", "/v1/order_package/createPackageOrder", `{"CouponId":"","count":2,"packageType":"prepay","disposable":false,"flavorInfo":[{"typeName":"subServiceType","typeValue":"FunctionCallCount","typeScale":1,"specName":"count","specValue":"1000000000"},{"typeName":"subServiceType","typeValue":"PublicDataTransfer","typeScale":1,"specName":"capacity","specValue":"100G"},{"typeName":"subServiceType","typeValue":"FunctionRunTimeCount","typeScale":1,"specName":"count","specValue":"1000000000"}]}`, "abc", map[string]string{}),
			out_HttpCode: 500,
		},
		{
			in_c:         global.BuildNewKunCtx("post", "/v1/order_package/createPackageOrder", `{"CouponId":"","count":3,"packageType":"prepay","disposable":false,"flavorInfo":[{"typeName":"subServiceType","typeValue":"FunctionCallCount","typeScale":1,"specName":"count","specValue":"1000000000"},{"typeName":"subServiceType","typeValue":"PublicDataTransfer","typeScale":1,"specName":"capacity","specValue":"100G"},{"typeName":"subServiceType","typeValue":"FunctionRunTimeCount","typeScale":1,"specName":"count","specValue":"1000000000"}]}`, "abc", map[string]string{}),
			out_HttpCode: 500,
		},
		{
			in_c:         global.BuildNewKunCtx("post", "/v1/order_package/createPackageOrder", ``, "abc", map[string]string{}),
			out_HttpCode: 400,
		},
		{
			in_c:         global.BuildNewKunCtx("post", "/v1/order_package/createPackageOrder", `{"CouponId":"","count":1,"packageType":"prepay","disposable":true,"flavorInfo":[]}`, "abc", map[string]string{}),
			out_HttpCode: 200,
		},
		{
			in_c:         global.BuildNewKunCtx("post", "/v1/order_package/createPackageOrder", `{"CouponId":"","count":1,"packageType":"prepay","disposable":false,"flavorInfo":[]}`, "abc", map[string]string{}),
			out_HttpCode: 200,
		},
		{
			in_c:         global.BuildNewKunCtx("post", "/v1/order_package/createPackageOrder", `{"CouponId":"","count":12,"disposable":false,"flavorInfo":[]}`, "abc", map[string]string{}),
			out_HttpCode: 400,
		},
		{
			in_c:         global.BuildNewKunCtx("post", "/v1/order_package/createPackageOrder", `{"CouponId":"","packageType":"free","count":12,"disposable":false,"flavorInfo":[]}`, "abc", map[string]string{}),
			out_HttpCode: 400,
		},
		{
			in_c:         global.BuildNewKunCtx("post", "/v1/order_package/createPackageOrder", `{"CouponId":"","packageType":"free","count":1,"disposable":false,"flavorInfo":[]}`, "abc", map[string]string{}),
			out_HttpCode: 200,
		},
	}
	for i, tc := range cases {
		fmt.Println(i)
		f := OrderRest{}
		f.createPackageOrder(tc.in_c)
		assert.Equal(t, tc.out_HttpCode, tc.in_c.Response().StatusCode())
	}
}

func TestQueryOrderDetail(t *testing.T) {
	global.MockAC()
	order.MockOrderManager()
	cases := []struct {
		in_c         *server.Context
		out_HttpCode int
	}{
		{
			in_c:         global.BuildNewKunCtx("post", "/v1/order/detail", `{"uuid":"1"}`, "abc", map[string]string{}),
			out_HttpCode: 200,
		},
		{
			in_c:         global.BuildNewKunCtx("post", "/v1/order/detail", `{"uuid":"2"}`, "abc", map[string]string{}),
			out_HttpCode: 200,
		},
		{
			in_c:         global.BuildNewKunCtx("post", "/v1/order/detail", `{"uuid":"3"}`, "abc", map[string]string{}),
			out_HttpCode: 200,
		},
		{
			in_c:         global.BuildNewKunCtx("post", "/v1/order/detail", ``, "abc", map[string]string{}),
			out_HttpCode: 400,
		},
		{
			in_c:         global.BuildNewKunCtx("post", "/v1/order/detail", `{"uuid":"4"}`, "abc", map[string]string{}),
			out_HttpCode: 500,
		},
	}
	for i, tc := range cases {
		fmt.Println(i)
		f := OrderRest{}
		f.queryOrderDetail(tc.in_c)
		assert.Equal(t, tc.out_HttpCode, tc.in_c.Response().StatusCode())
	}
}

func TestQueryPackageInfo(t *testing.T) {
	global.MockAC()
	order.MockOrderManager()
	cases := []struct {
		in_c         *server.Context
		out_HttpCode int
	}{
		{
			in_c:         global.BuildNewKunCtx("post", "/v1/order_package/queryPackageInfo", ``, "abc", map[string]string{}),
			out_HttpCode: 400,
		},
		{
			in_c:         global.BuildNewKunCtx("post", "/v1/order_package/queryPackageInfo", `{"region":"bj","pageNo":1,"pageSize":10,"status":"RUNNING","packageType":[],"orderBy":"","order":"desc"}`, "abc", map[string]string{}),
			out_HttpCode: 500,
		},
		{
			in_c:         global.BuildNewKunCtx("post", "/v1/order_package/queryPackageInfo", `{"serviceType":"CFC","region":"bj","pageNo":1,"pageSize":10,"status":"RUNNING","packageType":[],"orderBy":"active_time","order":""}`, "abc", map[string]string{}),
			out_HttpCode: 200,
		},
		{
			in_c:         global.BuildNewKunCtx("post", "/v1/order_package/queryPackageInfo", `{"serviceType":"CFC","region":"bj","pageNo":1,"pageSize":10,"status":"RUNNING","packageType":[],"orderBy":"","order":"asc"}`, "abc", map[string]string{}),
			out_HttpCode: 200,
		},
	}
	for i, tc := range cases {
		fmt.Println(i)
		f := OrderRest{}
		f.queryPackageInfo(tc.in_c)
		assert.Equal(t, tc.out_HttpCode, tc.in_c.Response().StatusCode())
	}
}

func TestInsideFindRes(t *testing.T) {
	global.MockAC()

	cases := []struct {
		in_c         *server.Context
		out_HttpCode int
	}{
		{
			in_c: global.BuildNewKunCtx("GET", "/v1/billing/abc/cfc/resources/abc", "", "abc", map[string]string{
				"accountId": "abc",
			}),
			out_HttpCode: 200,
		},
		{
			in_c: global.BuildNewKunCtx("GET", "/v1/billing/abc/cfc/resources/abc", "", "abc", map[string]string{
				"accountId": "aaa",
			}),
			out_HttpCode: 200,
		},
	}
	for _, tc := range cases {
		var m sqlmock.Sqlmock
		m, dbengine.Engine = global.MockDB()
		//如果可以设置空值就好了
		m.ExpectQuery(".*").WillReturnRows(global.GetRowsBillingRes(global.GetTestBillingRes(1)))

		f := OrderRest{}
		f.insideFind(tc.in_c)
		assert.Equal(t, tc.out_HttpCode, tc.in_c.Response().StatusCode())
	}
}

func TestFindOneResource(t *testing.T) {
	var m sqlmock.Sqlmock
	m, dbengine.Engine = global.MockDB()
	//如果可以设置空值就好了
	m.ExpectQuery(".*").WillReturnRows(global.GetRowsBillingRes(global.GetTestBillingRes(1)))
	c := global.BuildNewKunCtx("GET", "/v1/billing/abc/cfc/resources/abc", "", "abc", map[string]string{
		"accountId": "abc",
	})
	_, err := findOneResource(c.Logger(), "abc")
	if err != nil {
		t.Error("FindOneResource err")
	}
}

func TestCheckOrder(t *testing.T) {

	global.MockAC()
	order.MockOrderManager()

	cases := []struct {
		in_c         *server.Context
		out_HttpCode int
	}{
		{
			in_c: global.BuildNewKunCtxWithHeaders("GET", "/v1/order_executor/check?orderId=1", "", "abc", map[string]string{}, map[string]string{
				api.HeaderXRequestID: "1",
			}),
			out_HttpCode: 200,
		},
		{
			in_c: global.BuildNewKunCtxWithHeaders("GET", "/v1/order_executor/check?orderId=2", "", "abc", map[string]string{}, map[string]string{
				api.HeaderXRequestID: "1",
			}),
			out_HttpCode: 200,
		},
		{
			in_c: global.BuildNewKunCtxWithHeaders("GET", "/v1/order_executor/check?orderId=4", "", "abc", map[string]string{}, map[string]string{
				api.HeaderXRequestID: "1",
			}),
			out_HttpCode: 200,
		},
		{
			in_c: global.BuildNewKunCtxWithHeaders("GET", "/v1/order_executor/check?orderId=5", "", "abc", map[string]string{}, map[string]string{
				api.HeaderXRequestID: "1",
			}),
			out_HttpCode: 500,
		},
	}
	for _, tc := range cases {
		var m sqlmock.Sqlmock
		m, dbengine.Engine = global.MockDB()
		//如果可以设置空值就好了
		m.ExpectQuery(".*").WillReturnRows(global.GetRowsBillingRes(global.GetTestBillingRes(1)))

		f := OrderRest{}
		f.checkOrder(tc.in_c)
		assert.Equal(t, tc.out_HttpCode, tc.in_c.Response().StatusCode())
	}
}

func TestOrderExecutor(t *testing.T) {

	global.MockAC()
	order.MockOrderManager()
	var m sqlmock.Sqlmock
	m, dbengine.Engine = global.MockDB()
	m.ExpectQuery(".*").WillReturnRows(global.GetRowsBillingRes(global.GetTestBillingRes(1)))
	m.ExpectQuery(".*").WillReturnRows(global.GetRowsBillingRes(global.GetTestBillingRes(1)))
	cases := []struct {
		in_c         *server.Context
		out_HttpCode int
		product_type string
	}{
		{
			in_c: global.BuildNewKunCtxWithHeaders("POST", "/v1/order_executor/execute", "", "abc", map[string]string{}, map[string]string{
				api.HeaderXRequestID: "1",
			}),
			out_HttpCode: 400,
		},
		{
			in_c: global.BuildNewKunCtxWithHeaders("POST", "/v1/order_executor/execute", `{}`, "abc", map[string]string{}, map[string]string{
				api.HeaderXRequestID: "1",
			}),
			out_HttpCode: 400,
		},
		{
			in_c: global.BuildNewKunCtxWithHeaders("POST", "/v1/order_executor/execute", `{"orderId":"2"}`, "abc", map[string]string{}, map[string]string{
				api.HeaderXRequestID: "1",
			}),
			out_HttpCode: 200,
		},
		{
			in_c: global.BuildNewKunCtxWithHeaders("POST", "/v1/order_executor/execute", `{"orderId":"3"}`, "abc", map[string]string{}, map[string]string{
				api.HeaderXRequestID: "1",
			}),
			out_HttpCode: 200,
		},
		{
			in_c: global.BuildNewKunCtxWithHeaders("POST", "/v1/order_executor/execute", `{"orderId":"1"}`, "abc", map[string]string{}, map[string]string{
				api.HeaderXRequestID: "1",
			}),
			out_HttpCode: 200,
		},
		{
			in_c: global.BuildNewKunCtxWithHeaders("POST", "/v1/order_executor/execute", `{"orderId":"3"}`, "abc", map[string]string{}, map[string]string{
				api.HeaderXRequestID: "1",
			}),
			out_HttpCode: 200,
			product_type: "prepay",
		},
	}
	for i, tc := range cases {
		fmt.Println(i)
		f := OrderRest{}
		var m sqlmock.Sqlmock
		m, dbengine.Engine = global.MockDB()
		if i > 1 {
			m.ExpectQuery(".*").WillReturnRows(global.GetRowsBillingRes(global.GetTestBillingResCreating(1)))
		}
		f.orderExecutor(tc.in_c)
		assert.Equal(t, tc.out_HttpCode, tc.in_c.Response().StatusCode())
	}
}

// AddUnitTest
func TestGetResourceUsage(t *testing.T) {
	global.MockAC()
	order.NewMockBillingChargeClient()
	cases := []struct {
		in_c         *server.Context
		out_HttpCode int
	}{
		{
			in_c: global.BuildNewKunCtxWithHeaders("GET", "/v1/billing//resources/usage", "", "", map[string]string{}, map[string]string{
				api.HeaderXRequestID: "1",
			}),
			out_HttpCode: 400,
		},
		{
			in_c: global.BuildNewKunCtxWithHeaders("GET", "/v1/billing/abc/resources/usage", "", "abc", map[string]string{"accountId": "abc"}, map[string]string{
				api.HeaderXRequestID: "1",
			}),
			out_HttpCode: 200,
		},
	}

	for _, tc := range cases {
		f := OrderRest{}
		f.getResourceUsage(tc.in_c)
		assert.Equal(t, tc.out_HttpCode, tc.in_c.Response().StatusCode())
	}
}
