package billing

import (
	"encoding/json"
	"fmt"
	"math"
	"net/http"
	"runtime"
	"strconv"
	"strings"
	"time"

	"icode.baidu.com/baidu/faas/kun/pkg/bce/kunbns"

	"go.uber.org/zap"

	"icode.baidu.com/baidu/faas/kun/pkg/api"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/dao"
	apiErr "icode.baidu.com/baidu/faas/kun/pkg/apiserver/error"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/global"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/maindata"
	resp "icode.baidu.com/baidu/faas/kun/pkg/apiserver/models/function"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/order"
	"icode.baidu.com/baidu/faas/kun/pkg/bce/iam"
	kunErr "icode.baidu.com/baidu/faas/kun/pkg/error"
	"icode.baidu.com/baidu/faas/kun/pkg/server"
	"icode.baidu.com/baidu/faas/kun/pkg/util/logs"
)

const (
	OrderCreatingStatus  = "CREATING"
	OrderCreatedStatus   = "CREATED"
	PackageInitStatus    = "INIT"
	PackageRunningStatus = "RUNNING"
	PackageExpiredStatus = "EXPIRED"

	AutoRenewCreatingState = 2
	AutoRenewCreatedState  = 3
	AutoRenewDeleteState   = 4

	RenewTimeUnitMonth = "month"
	RenewTimeUnitYear  = "year"

	PrepayPackageType = "prepay"
	FreePackageType   = "free"
)

var OrderInvalidStatus = []string{"CANCELLED", "CREATE_FAILED", "EXPIRED", "REFUND_SUCC", "REFUND_FAILED"}

var CreateAutoRenewStatus = map[int]string{2: "CREATING", 3: "CREATED"}

type OrderRest struct {
	Path string
}

func (f OrderRest) orderExecutor(c *server.Context) {
	response := c.Response()
	r := c.Request().Request
	defer r.Body.Close()

	orderRequest := api.NewOrderResponce{}
	if err := c.Request().ReadEntity(&orderRequest); err != nil {
		rkunErr := kunErr.NewInvalidRequestContentException("orderExecutor : decode request body err", err)
		c.WithWarnLog(rkunErr).WriteTo(response)
		return
	}
	if orderRequest.OrderId == "" {
		rkunErr := apiErr.NewMissingParametersException("orderExecutor : orderId is empty", nil)
		c.WithWarnLog(rkunErr).WriteTo(response)
		return
	}
	//查询订单
	requestId := r.Header.Get(api.HeaderXRequestID)
	queryOrderResponce, err := order.OrderManager.QueryOrder(orderRequest.OrderId, requestId, global.AC.Config.BillingEndpoint, global.AC.Clients.Iam)
	if err != nil || queryOrderResponce.AccountId == "" {
		rkunErr := apiErr.NewQueryOrderException("Query order err or order's AccountId is empty", err)
		c.WithWarnLog(rkunErr).WriteTo(response)
		return
	}
	accountId := queryOrderResponce.AccountId
	if queryOrderResponce.Status == "CREATED" {
		response.WriteHeaderAndEntity(http.StatusOK, map[string]interface{}{
			"executionStatus": "CREATED",
		})
		return
	}
	//如果是预付费，创建资源包
	if queryOrderResponce.ProductType == "prepay" {
		packageInfo, err := order.OrderManager.CreatePackageResource(global.AC.Config.BillingPackageEndpoint, global.AC.Config.BillingEndpoint, global.AC.Clients.Iam, requestId, orderRequest.OrderId)
		if err != nil {
			rkunErr := apiErr.CreatePackageException("createPackageResource  fail", err)
			c.WithWarnLog(rkunErr).WriteTo(response)
			return
		}
		//订单更新为CREATING状态，资源更新为INIT状态
		err = order.OrderManager.UpdateOrderStatus(orderRequest.OrderId, packageInfo, "CREATING", "INIT", requestId, global.AC.Config.BillingEndpoint, global.AC.Clients.Iam)
		if err != nil {
			logs.Errorf("OrderId:%v ", orderRequest.OrderId)
			rkunErr := apiErr.NewUpdateOrderException("UpdateOrder fail", err)
			c.WithWarnLog(rkunErr).WriteTo(response)
			return
		}
		response.WriteHeaderAndEntity(http.StatusOK, map[string]interface{}{
			"executionStatus": "CREATING",
		})

		// 更新预付费资源记录，主要更改订单状态为CREATING和资源包状态为INIT
		req := &api.UpdatePrepayResourceRequest{
			AccountId:     accountId,
			OrderId:       orderRequest.OrderId,
			OrderStatus:   OrderCreatingStatus,
			PackageStatus: PackageInitStatus,
			PackageInfo:   packageInfo,
		}
		updatePrepayResource(c.Logger(), req, requestId, global.AC.Config.BillingEndpoint, global.AC.Clients.Iam)
		return
	}
	//查询数据库  如果已经绑定成功CREATED 然后确认绑定的是否为此订单
	findResource, rKerr := findOneResource(c.Logger(), accountId)
	if rKerr != nil {
		c.WithWarnLog(rKerr).WriteTo(response)
		return
	}
	if findResource.CreateState == "CREATED" {
		//重新查询订单 如果为已创建返回，如果不是已创建，为无效订单更新为创建失败
		queryOrderResponce, err := order.OrderManager.QueryOrder(orderRequest.OrderId, requestId, global.AC.Config.BillingEndpoint, global.AC.Clients.Iam)
		if err != nil || queryOrderResponce.AccountId == "" {
			rkunErr := apiErr.NewQueryOrderException("Query order err or order's AccountId is empty", err)
			c.WithWarnLog(rkunErr).WriteTo(response)
			return
		}
		if queryOrderResponce.Status != "CREATED" {
			//更新资源为废弃
			err := order.OrderManager.UpdateOrderToFail(orderRequest.OrderId, requestId, global.AC.Config.BillingEndpoint, global.AC.Clients.Iam)
			if err != nil {
				rkunErr := apiErr.NewUpdateOrderException("update order to CREATE_FAILED err", err)
				c.WithWarnLog(rkunErr).WriteTo(response)
				return
			}
		}
	}
	response.WriteHeaderAndEntity(http.StatusOK, map[string]interface{}{
		"executionStatus": "CREATED",
	})
	return
}

// 创建资源包订单
func (f OrderRest) createPackageOrder(c *server.Context) {
	response := c.Response()
	r := c.Request().Request
	defer r.Body.Close()
	requestId := r.Header.Get(api.HeaderXRequestID)
	requestUser, _ := c.Request().Attribute("User").(*iam.User)
	accountId := requestUser.Domain.ID
	decoder := json.NewDecoder(r.Body)
	resourcePackageOrder := new(api.ResourcePackageOrder)
	if err := decoder.Decode(resourcePackageOrder); err != nil {
		c.WithWarnLog(kunErr.NewInvalidRequestContentException(err.Error(), nil)).WriteTo(response)
		return
	}
	flavorInfo := resourcePackageOrder.FlavorInfo
	orderRes := []api.Order{}
	//购买数量
	count := resourcePackageOrder.Count
	// 参数校验
	if count < 0 || count > 99 {
		c.WithWarnLog(kunErr.NewInvalidParameterValueException("package count invalid, package count must between 1 and 99", nil)).WriteTo(response)
		return
	}

	if strings.ToLower(resourcePackageOrder.PackageType) != PrepayPackageType && strings.ToLower(resourcePackageOrder.PackageType) != FreePackageType {
		c.WithWarnLog(kunErr.NewInvalidParameterValueException("package type invalid, package type must be prepay or free", nil)).WriteTo(response)
		return
	}
	if resourcePackageOrder.PackageType == "free" {

		if count < 0 || count > 1 {
			c.WithWarnLog(kunErr.NewInvalidParameterValueException("free package count invalid, free package count must 1", nil)).WriteTo(response)
			return
		}

		// 加锁
		lock := fmt.Sprintf("%s_cfc_freepackage", accountId)
		acquired, err := global.AC.Clients.Redis.Redis().SetNX(lock, 1, 180*time.Second).Result()

		defer global.AC.Clients.Redis.Redis().Del(lock)

		if err != nil {
			c.WithWarnLog(kunErr.NewServiceException("something wrong when proccessing this order", nil)).WriteTo(response)
			return
		}

		if !acquired {
			c.WithWarnLog(kunErr.NewTooManyRequestsException("too many requests for this order", nil)).WriteTo(response)
			return
		}

	}
	//代金劵id
	couponId := resourcePackageOrder.CouponId
	flag := 0
	// 免费资源包OrderIds
	freeOrderIdsStr := ""
	// 错误类型
	errType := make(map[string]error, 0)
	//代金劵是单笔消费，让最大金额订单使用
	if resourcePackageOrder.Disposable {
		for i := 0; i < len(flavorInfo); i++ {
			if flavorInfo[i].TypeValue == api.ChargeItemFunctionRunTimeCount {
				// 免费资源包，先查询是否有购买记录, 兼容使用openApi创建免费资源包
				if flavorInfo[i].PackageType == "free" {
					// 查询主数据，查看用户是否购买过免费资源包
					isNewUser, err := isFreeUser(accountId, requestId, global.AC.Config.MaindataBNS, global.AC.Clients.BNS, global.AC.Clients.Iam)
					if err != nil {
						errType[flavorInfo[i].TypeValue] = fmt.Errorf("query free new user fail, err: %v", err)
						logs.Errorf("query free new user fail, accountId: %v, requestId, %v, err: %v", accountId, requestId, err)
						continue
					}
					if !isNewUser {
						errType[flavorInfo[i].TypeValue] = fmt.Errorf("user has already purchased free package")
						logs.Errorf("user has already purchased free package, accountId: %v, requestId, %v", accountId, requestId)
						continue
					}
				}
				flag = i
				err, orderId := order.OrderManager.CreatePackageOrder(couponId, global.AC.Clients.Iam, requestId, global.AC.Config.Region, global.AC.Config.BillingCatalogEndpoint, accountId, count, flavorInfo[i])
				if err != nil || orderId == "" {
					logs.Errorf("CreateBillingPackageOrder fail, requestId, %v, err: %v", requestId, err)
					errType[flavorInfo[i].TypeValue] = fmt.Errorf("CreateBillingPackageOrder fail, err: %v", err)
					continue
				}
				orderTmp := api.Order{
					OrderId:   orderId,
					TypeName:  flavorInfo[i].TypeName,
					TypeValue: flavorInfo[i].TypeValue,
					TypeScale: flavorInfo[i].TypeScale,
					SpecName:  flavorInfo[i].SpecName,
					SpecValue: flavorInfo[i].SpecValue,
					Specscale: flavorInfo[i].Specscale,
				}
				orderRes = append(orderRes, orderTmp)
				if flavorInfo[i].PackageType == "free" {
					if freeOrderIdsStr == "" {
						freeOrderIdsStr = orderId
					} else {
						freeOrderIdsStr = fmt.Sprintf("%s,%s", freeOrderIdsStr, orderId)
					}
				}
			}
		}
		couponId = ""
	}
	for i := 0; i < len(flavorInfo); i++ {
		//代金劵是单笔消费，跳过最大金额订单,其他订单不使用代金劵
		if i == flag && resourcePackageOrder.Disposable {
			continue
		}
		// 免费资源包，先查询是否有购买记录, 兼容使用openApi创建免费资源包
		if flavorInfo[i].PackageType == "free" {
			// 查询主数据，查看用户是否购买过免费资源包
			isNewUser, err := isFreeUser(accountId, requestId, global.AC.Config.MaindataBNS, global.AC.Clients.BNS, global.AC.Clients.Iam)
			if err != nil {
				errType[flavorInfo[i].TypeValue] = fmt.Errorf("query free new user fail, err: %v", err)
				logs.Errorf("query free new user fail, accountId: %v, requestId, %v, err: %v", accountId, requestId, err)
				continue
			}
			if !isNewUser {
				errType[flavorInfo[i].TypeValue] = fmt.Errorf("user has already purchased free package")
				logs.Errorf("user has already purchased free package, accountId: %v, requestId, %v", accountId, requestId)
				continue
			}
		}
		//不使用代金劵；或者是通用代金劵，三个订单都可使用
		err, orderId := order.OrderManager.CreatePackageOrder(couponId, global.AC.Clients.Iam, requestId, global.AC.Config.Region, global.AC.Config.BillingCatalogEndpoint, accountId, count, flavorInfo[i])
		if err != nil || orderId == "" {
			logs.Errorf("CreateBillingPackageOrder fail, requestId, %v, err: %v", requestId, err)
			errType[flavorInfo[i].TypeValue] = fmt.Errorf("CreateBillingPackageOrder fail, err: %v", err)
			continue
		}
		orderTmp := api.Order{
			OrderId:   orderId,
			TypeName:  flavorInfo[i].TypeName,
			TypeValue: flavorInfo[i].TypeValue,
			TypeScale: flavorInfo[i].TypeScale,
			SpecName:  flavorInfo[i].SpecName,
			SpecValue: flavorInfo[i].SpecValue,
			Specscale: flavorInfo[i].Specscale,
		}
		orderRes = append(orderRes, orderTmp)
		if flavorInfo[i].PackageType == "free" {
			if freeOrderIdsStr == "" {
				freeOrderIdsStr = orderId
			} else {
				freeOrderIdsStr = fmt.Sprintf("%s,%s", freeOrderIdsStr, orderId)
			}
		}
	}

	// 更新主数据中用户购买免费资源包记录
	if freeOrderIdsStr != "" {
		err := updateFreeUser(accountId, requestId, freeOrderIdsStr, global.AC.Config.MaindataBNS, global.AC.Clients.BNS, global.AC.Clients.Iam)
		if err != nil {
			logs.Errorf("updateFreeUser fail, err: %v", err)
		}
	}

	// 根据错误类型返回
	if len(errType) == 0 {
		msg := api.OrderList{Order: orderRes}
		response.WriteHeaderAndEntity(http.StatusOK, resp.NewSuccessResp(msg))
	} else {
		c.WithWarnLog(kunErr.NewInvalidBillingException("please check you bills", nil)).WriteTo(response)
	}

}

// 查询资源包余量
func (f OrderRest) queryPackageInfo(c *server.Context) {
	response := c.Response()
	r := c.Request().Request
	defer r.Body.Close()
	requestId := r.Header.Get(api.HeaderXRequestID)
	requestUser, _ := c.Request().Attribute("User").(*iam.User)
	accountId := requestUser.Domain.ID
	decoder := json.NewDecoder(r.Body)
	queryPackageInfo := new(api.QueryPackageInfo)
	if err := decoder.Decode(queryPackageInfo); err != nil {
		c.WithWarnLog(kunErr.NewInvalidRequestContentException(err.Error(), nil)).WriteTo(response)
		return
	}
	packageInfo, err := order.OrderManager.QueryPackageInfo(accountId, queryPackageInfo, requestId, global.AC.Config.BillingPackageEndpoint, global.AC.Clients.Iam)
	if err != nil {
		rkunErr := kunErr.NewServiceException("queryPackageInfo fail", nil)
		response.WriteHeaderAndEntity(http.StatusInternalServerError, resp.NewFailResp(rkunErr))
		return
	}

	result, _ := formatPackageInfo(packageInfo, accountId, requestId)
	if len(result) > 0 {
		packageInfo.Result = result
	}

	response.WriteHeaderAndEntity(http.StatusOK, resp.NewSuccessResp(packageInfo))

}

// formatPackageInfo 格式化资源包列表
func formatPackageInfo(packageInfo *api.QueryPackageInfoResponse, accountId string, requestId string) ([]api.Result, error) {
	result := make([]api.Result, 0)
	orderIds := make([]string, 0)
	// 批量查询自动续费规则
	if packageInfo != nil && len(packageInfo.Result) > 0 {
		autoRenewResource := make([]api.AutoRenewResource, 0)
		for _, pi := range packageInfo.Result {
			if pi.ExpireTime.Before(time.Now()) {
				pi.Status = PackageExpiredStatus
				pi.UsedCapacity = pi.Capacity
			}
			// 去除免费资源包，免费资源包在资源包列表中不展示, 免费资源包为分段量包
			if pi.PackageProperty == "seg" {
				continue
			}
			resource := api.AutoRenewResource{
				AccountId:   pi.AccountId,
				ServiceType: pi.ServiceType,
				ServiceId:   pi.PackageName,
				Region:      pi.Region,
			}

			autoRenewResource = append(autoRenewResource, resource)
			result = append(result, pi)
			orderIds = append(orderIds, pi.OrderId)
		}

		// 批量查询自动续费规则
		rules, err := order.OrderManager.BatchQueryAutoRenew(requestId, autoRenewResource, global.AC.Config.BillingOrderRenewEndpoint, global.AC.Clients.Iam)
		if err != nil {
			logs.Errorf("BatchQueryAutoRenew fail, requestId: %s", requestId)
			return result, err
		}

		// 批量查询自动续费记录表
		dbRules, err := dao.BatchAutoRenewRules(accountId, orderIds)
		if err != nil {
			logs.Errorf("DB BatchQueryAutoRenew fail, requestId: %s", requestId)
			return result, err
		}
		dbRulesMap := make(map[string]string, 0)
		for _, ru := range dbRules {
			if ru.RenewState == AutoRenewCreatingState || ru.RenewState == AutoRenewCreatedState {
				dbRulesMap[ru.OrderId] = CreateAutoRenewStatus[ru.RenewState]
			}
		}

		// 根据查询结果，补充packageInfo中的AutoRenewRule
		tt, _ := time.Parse("2006-01-02 15:04:05", "2020-01-02 15:04:05")
		if len(rules) > 0 {
			for _, rule := range rules {
				if rule.DeleteTime.IsZero() || rule.DeleteTime.Before(tt) {
					for idx, pi := range result {
						if pi.PackageName == rule.ServiceId {
							result[idx].AutoRenew = "CREATED"
							result[idx].AutoRenewRule = rule
						}
					}
				}
			}
		}

		// 对比数据库中记录，查看是否为CREATING
		for idx, pi := range result {
			if pi.AutoRenew == "" {
				v, ok := dbRulesMap[pi.OrderId]
				if ok && v == "CREATING" {
					result[idx].AutoRenew = v
				}
			}
		}
	}

	return result, nil
}

// 订单详情页
func (f OrderRest) queryOrderDetail(c *server.Context) {
	response := c.Response()
	r := c.Request().Request
	defer r.Body.Close()
	requestId := r.Header.Get(api.HeaderXRequestID)
	decoder := json.NewDecoder(r.Body)
	orderDetailRequest := new(api.OrderDetailRequest)
	if err := decoder.Decode(orderDetailRequest); err != nil {
		c.WithWarnLog(kunErr.NewInvalidRequestContentException(err.Error(), nil)).WriteTo(response)
		return
	}
	orderDetail, err := order.OrderManager.QueryOrderDetail(orderDetailRequest.Uuid, requestId, global.AC.Config.BillingEndpoint, global.AC.Clients.Iam)
	if err != nil {
		rkunErr := kunErr.NewServiceException("queryOrderInfo fail", nil)
		response.WriteHeaderAndEntity(http.StatusInternalServerError, resp.NewFailResp(rkunErr))
		return
	}
	//非量包不需要订单详情页
	if orderDetail.ProductType != "prepay" {
		return
	}
	configuration := []string{}
	if orderDetail.Items[0].Flavor[0].Name == "subServiceType" {
		configuration = append(configuration, orderDetail.Items[0].Flavor[1].Name+":"+orderDetail.Items[0].Flavor[1].Value)
	} else {
		configuration = append(configuration, orderDetail.Items[0].Flavor[0].Name+":"+orderDetail.Items[0].Flavor[0].Value)
	}
	items := api.ItemsResponse{
		AlterCount:              orderDetail.Items[0].AlterCount,
		CatalogPrice:            orderDetail.Items[0].CatalogPrice,
		ChargePlanStartTime:     orderDetail.Items[0].ChargePlanStartTime,
		ChargeStartTime:         orderDetail.Items[0].ChargeStartTime,
		CombinedServiceType:     orderDetail.Items[0].CombinedServiceType,
		Count:                   orderDetail.Items[0].Count,
		DiscountWithCouponPrice: orderDetail.Items[0].DiscountWithCouponPrice,
		Extra:                   orderDetail.Items[0].Extra,
		Flavor:                  orderDetail.Items[0].Flavor,
		IsOnceCharge:            orderDetail.Items[0].IsOnceCharge,
		Key:                     orderDetail.Items[0].Key,
		PaymentMethod:           orderDetail.Items[0].PaymentMethod,
		Price:                   orderDetail.Items[0].Price,
		PricingDetail:           orderDetail.Items[0].PricingDetail,
		ProductType:             orderDetail.Items[0].ProductType,
		RealUnitPrice:           orderDetail.Items[0].RealUnitPrice,
		Region:                  orderDetail.Items[0].Region,
		ReleaseTime:             orderDetail.Items[0].ReleaseTime,
		ResourceActiveTime:      orderDetail.Items[0].ResourceActiveTime,
		ResourceIDAndFees:       orderDetail.Items[0].ResourceIDAndFees,
		ResourceIds:             orderDetail.Items[0].ResourceIds,
		ResourceMappings:        orderDetail.Items[0].ResourceMappings,
		ServiceType:             orderDetail.Items[0].ServiceType,
		Status:                  orderDetail.Items[0].Status,
		SubItems:                orderDetail.Items[0].SubItems,
		SubProductType:          orderDetail.Items[0].SubProductType,
		Time:                    orderDetail.Items[0].Time,
		TimeUnit:                orderDetail.Items[0].TimeUnit,
		UnitPrice:               orderDetail.Items[0].UnitPrice,
		UnitCount:               orderDetail.Items[0].Count,
		Configuration:           configuration,
		ChargeType:              []string{"PACKAGE"},
		UnitPriceShow:           "￥" + strconv.FormatFloat(orderDetail.Items[0].UnitPrice, 'f', 2, 64) + "/个",
	}
	orderDetailResponde := api.OrderDetailResponse{
		BaseOrderID:                         orderDetail.BaseOrderID,
		Uuid:                                orderDetail.Uuid,
		Type:                                orderDetail.Type,
		SubOrderType:                        orderDetail.SubOrderType,
		GroupID:                             orderDetail.GroupID,
		InternalStatus:                      orderDetail.InternalStatus,
		AccountID:                           orderDetail.AccountID,
		UserID:                              orderDetail.UserID,
		ServiceType:                         orderDetail.ServiceType,
		ProductType:                         orderDetail.ProductType,
		SubProductType:                      orderDetail.SubProductType,
		Items:                               []api.ItemsResponse{items},
		Price:                               orderDetail.Price,
		Status:                              orderDetail.Status,
		CreateTime:                          orderDetail.CreateTime,
		PurchaseTime:                        orderDetail.PurchaseTime,
		UpdateTime:                          orderDetail.UpdateTime,
		ActiveTime:                          orderDetail.ActiveTime,
		ResourceIds:                         orderDetail.ResourceIds,
		PayChannel:                          orderDetail.PayChannel,
		SpecificType:                        orderDetail.SpecificType,
		BatchID:                             orderDetail.BatchID,
		Source:                              orderDetail.Source,
		PayExpireTime:                       orderDetail.PayExpireTime,
		CampaignID:                          orderDetail.CampaignID,
		OperatorName:                        orderDetail.OperatorName,
		CashEquivalentCouponFavourablePrice: orderDetail.CashEquivalentCouponFavourablePrice,
		CatalogPrice:                        orderDetail.CatalogPrice,
		Discount:                            orderDetail.Discount,
		FavourablePrice:                     orderDetail.FavourablePrice,
		DiscountCouponFavourablePrice:       orderDetail.DiscountCouponFavourablePrice,
	}
	response.WriteHeaderAndEntity(http.StatusOK, resp.NewSuccessResp(orderDetailResponde))

}
func (f OrderRest) checkOrder(c *server.Context) {
	response := c.Response()
	r := c.Request().Request
	defer r.Body.Close()

	orderId := c.Request().QueryParameter("orderId")
	if orderId == "" {
		rkunErr := apiErr.NewMissingParametersException("checkOrder : orderId is empty", nil)
		c.WithWarnLog(rkunErr).WriteTo(response)
		return
	}
	requestId := r.Header.Get(api.HeaderXRequestID)
	queryOrderResponce, err := order.OrderManager.QueryOrder(orderId, requestId, global.AC.Config.BillingEndpoint, global.AC.Clients.Iam)
	if err != nil || queryOrderResponce.AccountId == "" {
		rkunErr := apiErr.NewQueryOrderException("Query order err or order's AccountId is empty", err)
		c.WithWarnLog(rkunErr).WriteTo(response)
		return
	}
	if queryOrderResponce.Status == "CREATED" {
		response.WriteHeaderAndEntity(http.StatusOK, map[string]interface{}{
			"executionStatus": "CREATED",
		})
		return
	}
	accountId := queryOrderResponce.AccountId
	//如果是预付费，绑定资源
	if queryOrderResponce.ProductType == "prepay" {
		packageInfo, err := order.OrderManager.QueryPackageStatus(accountId, orderId, requestId, global.AC.Config.BillingPackageEndpoint, global.AC.Clients.Iam)
		if err != nil {
			rkunErr := apiErr.NewQueryPackageException("Query package err", err)
			c.WithWarnLog(rkunErr).WriteTo(response)
			return
		}
		//订单更新为CREATED状态，资源更新为RUNNING状态
		err = order.OrderManager.UpdateOrderStatus(orderId, packageInfo, "CREATED", "RUNNING", requestId, global.AC.Config.BillingEndpoint, global.AC.Clients.Iam)
		if err != nil {
			rkunErr := apiErr.NewUpdateOrderException("UpdateOrder fail", err)
			c.WithWarnLog(rkunErr).WriteTo(response)
			return
		}
		response.WriteHeaderAndEntity(http.StatusOK, map[string]interface{}{
			"executionStatus": "CREATED",
		})
		// 更新预付费资源记录，主要更改订单状态为CREATED和资源包状态为RUNNING
		req := &api.UpdatePrepayResourceRequest{
			AccountId:     accountId,
			OrderId:       orderId,
			OrderStatus:   OrderCreatedStatus,
			PackageStatus: PackageRunningStatus,
			PackageInfo:   packageInfo,
		}
		updatePrepayResource(c.Logger(), req, requestId, global.AC.Config.BillingEndpoint, global.AC.Clients.Iam)
		return
	}
	findResource, rKerr := findOneResource(c.Logger(), accountId)
	if rKerr != nil {
		c.WithWarnLog(rKerr).WriteTo(response)
		return
	}
	if findResource.CreateState == "" {
		response.WriteHeaderAndEntity(http.StatusOK, map[string]interface{}{
			"executionStatus": "CREATING",
		})
		return
	}
	response.WriteHeaderAndEntity(http.StatusOK, map[string]interface{}{
		"executionStatus": findResource.CreateState,
	})
	return
}

func (r *OrderRest) updateOrderState(c *server.Context) {
	response := c.Response()
	re := c.Request().Request
	defer re.Body.Close()

	updateCondition := new(dao.BillingResource)
	updateRes := new(dao.BillingResource)
	accountId := c.Request().PathParameter("accountId")
	updateCondition.AccountId = accountId
	action := c.Request().QueryParameter("action")

	updateOrderRequest := &api.OrderResourceRequest{}
	json.NewDecoder(re.Body).Decode(updateOrderRequest)
	if updateOrderRequest.Region != global.AC.Config.Region && updateOrderRequest.Region != "global" {
		rkunErr := kunErr.NewServiceException("updateOrderState : region err , not current cfc's server region", nil)
		c.WithWarnLog(rkunErr).WriteTo(response)
		return
	}
	returnStatus := ""
	switch action {
	case "START":
		updateRes.ResourceState = "RUNNING"
		returnStatus = "STARTED"
	case "STOP":
		updateRes.ResourceState = "STOPPED"
		returnStatus = "STOPPED"
	case "DELETE":
		updateRes.ResourceState = "CLEAR"
		returnStatus = "DELETED"
	default:
		c.WithWarnLog(kunErr.NewInvalidParameterValueException("action err :is not one of the START,STOP,DELETE", nil)).WriteTo(response)
		return
	}
	rKunErr := dao.UpdateRes(*updateCondition, updateRes)
	if rKunErr != nil {
		c.WithErrorLog(rKunErr).WriteTo(response)
		return
	}
	response.WriteHeaderAndEntity(http.StatusOK, map[string]interface{}{
		"status": returnStatus,
	})
	return
}

func (r OrderRest) insideFind(c *server.Context) {
	defer c.Logger().TimeTrack(time.Now(), "insideFindResource")

	response := c.Response()
	re := c.Request().Request
	defer re.Body.Close()

	accountId := c.Request().PathParameter("accountId")
	resourceId := c.Request().PathParameter("resourceId")
	findResource, rKerr := findOneResource(c.Logger(), accountId)
	if rKerr != nil {
		c.WithWarnLog(rKerr).WriteTo(response)
		return
	}
	//查询为空，返回空
	if findResource.AccountId == "" {
		response.WriteHeaderAndEntity(http.StatusOK, map[string]interface{}{})
	} else {
		response.WriteHeaderAndEntity(http.StatusOK, map[string]interface{}{
			"id":        resourceId,
			"accountId": accountId,
			"service":   "CFC",
			"region":    global.AC.Config.Region,
			"name":      resourceId,
			"status":    findResource.ResourceState,
		})
	}
}

func findOneResource(logger *logs.Logger, accountId string) (*dao.BillingResource, error) {
	findResource := new(dao.BillingResource)
	findResource.AccountId = accountId
	defer logger.TimeTrack(time.Now(), "Find A billing resource", zap.String("account_id", findResource.AccountId))
	if rkunErr := dao.FindOneRes(findResource); rkunErr != nil {
		return nil, rkunErr
	}
	return findResource, nil
}

func (r OrderRest) getResourceUsage(c *server.Context) {
	defer c.Logger().TimeTrack(time.Now(), "getBillingCharge")

	response := c.Response()
	req := c.Request().Request
	defer req.Body.Close()
	//defer recovery(){}
	defer func() {
		if r := recover(); r != nil {
			buf := make([]byte, 4096)
			buf = buf[:runtime.Stack(buf, false)]
			logs.Errorf("Panic recovered in Run, %+v \n %s", r, buf)

		}
	}()

	accountID := c.Request().PathParameter("accountId")
	if accountID == "" {
		rkunErr := kunErr.NewServiceException("getResourceUsage : parameter error, accountID must not empty", nil)
		response.WriteHeaderAndEntity(http.StatusBadRequest, resp.NewFailResp(rkunErr))
		return
	}
	queryResourceReq := &api.BillingResourceQueryRequest{
		Region:      global.AC.Config.Region,
		AccountId:   accountID,
		ServiceName: "CFC",
	}
	requestId := req.Header.Get(api.HeaderXRequestID)
	// startTime
	timeNow := time.Now()
	if queryResourceReq.StartTime == "" {
		currentYear, currentMonth, _ := timeNow.Date()
		currentLocation := timeNow.Location()
		firstOfMonth := time.Date(currentYear, currentMonth, 1, 0, 0, 0, 0, currentLocation).UTC()
		queryResourceReq.StartTime = firstOfMonth.Format(time.RFC3339)
	}
	if queryResourceReq.EndTime == "" {
		queryResourceReq.EndTime = timeNow.UTC().Format(time.RFC3339)
	}
	queryResourceReq.UsageMeta = api.UsageMeta{
		MinuteReducer: "SumReducer",
		DayReducer:    "SumReducer",
		MonthReducer:  "SumReducer",
		RemoveZero:    false,
	}
	res := api.GetResourceResp{
		Usage: make(map[string]api.Usage, 0),
	}
	for _, item := range api.ChargeItems {
		queryResourceReq.ChargeItem = item
		resourceUsage, err := order.BillingChargeClient.QueryBillingResourceUsage(queryResourceReq, requestId, global.AC.Config.BillingChargeEndpoint, global.AC.Clients.Iam)
		itemUsage := api.Usage{}
		if err != nil {
			rkunErr := kunErr.NewServiceException("getResourceUsage : billing charge endpoint return error", err)
			response.WriteHeaderAndEntity(http.StatusInternalServerError, resp.NewFailResp(rkunErr))
			return
		}

		if len(*resourceUsage) == 0 {

			itemUsage.Amount = 0.0
		} else {
			amount := 0.0
			for _, d := range *resourceUsage {
				amount += d.Amount
			}
			amount, _ = strconv.ParseFloat(fmt.Sprintf("%.6f", amount), 64)
			itemUsage.Amount = amount
		}
		itemUsage.Unit = api.ChargeItemUintMap[item]
		res.Usage[item] = itemUsage
	}
	res.StartTime = queryResourceReq.StartTime
	res.EndTime = queryResourceReq.EndTime
	response.WriteHeaderAndEntity(http.StatusOK, resp.NewSuccessResp(res))
	return
}

// findPrepayResource 查看预付费资源
func findPrepayResource(logger *logs.Logger, accountId string, orderId string) (*dao.BillingPrepayResource, error) {
	findResource := new(dao.BillingPrepayResource)
	findResource.AccountId = accountId
	findResource.OrderId = orderId
	defer logger.TimeTrack(time.Now(), "Find A billing prepay resource", zap.String("account_id", findResource.AccountId), zap.String("order_id", findResource.OrderId))
	if rkunErr := dao.FindPrepayRes(findResource); rkunErr != nil {
		return nil, rkunErr
	}
	return findResource, nil
}

func updatePrepayResource(logger *logs.Logger, req *api.UpdatePrepayResourceRequest, requestId string, billingEndpoint string, iamClient iam.ClientInterface) (res *dao.BillingPrepayResource, err error) {
	defer logger.TimeTrack(time.Now(), "Update A billing prepay resource", zap.String("account_id", req.AccountId), zap.String("order_id", req.OrderId))
	// 1. 查询是否有记录
	fr, err := findPrepayResource(logger, req.AccountId, req.OrderId)
	if err != nil {
		logger.Errorf("Find billing prepay resource failed, accountId: %v, orderId: %v, err: %v", req.AccountId, req.OrderId, err)
		return nil, err
	}

	// 2. 从req.PackageInfo中解析packageName作为ResourceId
	resourceId := ""
	for _, pi := range req.PackageInfo {
		if pi.PackageName != "" {
			resourceId = pi.PackageName
			break
		}
	}
	// 3. 无记录，则需要新建
	if fr.ProductType == "" {
		// 获取订单详情，查询订单基本信息
		orderDetail, err := order.OrderManager.QueryOrderDetail(req.OrderId, requestId, billingEndpoint, iamClient)
		if err != nil {
			logs.Errorf("QueryOrderDetail failed, requestId: %v, uuid: %v, err: %v", requestId, req.OrderId, err)
			return nil, err
		}

		if orderDetail.ProductType == "prepay" {
			productType := orderDetail.ProductType
			// 免费包处理
			if (math.Abs(orderDetail.Price-0.00) > 0.0000001) == false {
				productType = "free"
			}

			// 获取量包的packageType，如PublicDataTransfer
			packageType := ""
			if len(orderDetail.Items) > 0 {
				for _, flavor := range orderDetail.Items[0].Flavor {
					if flavor.Name == "subServiceType" {
						packageType = flavor.Value
						break
					}
				}
			}
			resource := &dao.BillingPrepayResource{
				AccountId:     req.AccountId,
				OrderId:       req.OrderId,
				Region:        global.AC.Config.Region,
				ResourceState: req.PackageStatus,
				ResourceType:  packageType,
				OrderState:    req.OrderStatus,
				ProductType:   productType,
				ResourceId:    resourceId,
				TimeUnit:      orderDetail.Items[0].TimeUnit,
			}
			if rkunErr := dao.CreatePrepayRes(resource); rkunErr != nil {
				logs.Errorf("create billing prepay resource failed, resource: %v, err: %v", resource, rkunErr)
				return nil, rkunErr
			}
		}

	} else {
		// 4. 有记录，则需要更新订单状态、资源状态和资源包列表
		updateCondition := dao.BillingPrepayResource{
			AccountId: req.AccountId,
			OrderId:   req.OrderId,
			Region:    global.AC.Config.Region,
		}
		updateRes := &dao.BillingPrepayResource{
			ResourceState: req.PackageStatus,
			ResourceId:    resourceId,
			OrderState:    req.OrderStatus,
		}
		if rkunErr := dao.UpdatePrepayRes(updateCondition, updateRes); rkunErr != nil {
			logs.Errorf("update billing prepay resource failed, resource: %v, err: %v", updateCondition, err)
			return nil, rkunErr
		}
		res = updateRes
	}
	return
}

// createNewFreeUser 新用户开通CFC服务后，在数据中心中添加新用户记录
func (r OrderRest) createNewFreeUser(c *server.Context) {
	defer c.Logger().TimeTrack(time.Now(), "createNewFreeUser")

	response := c.Response()
	req := c.Request().Request
	defer req.Body.Close()

	// 1. 获取用户account_id
	requestUser, _ := c.Request().Attribute("User").(*iam.User)
	accountId := requestUser.Domain.ID
	if accountId == "" {
		rkunErr := kunErr.NewInvalidParameterValueException("createNewFreeUser : parameter error, accountID must not empty", nil)
		response.WriteHeaderAndEntity(http.StatusBadRequest, resp.NewFailResp(rkunErr))
		return
	}

	// 2. 访问Maindata创建新用户记录
	requestId := req.Header.Get(api.HeaderXRequestID)

	// 获取主数据endpoint
	maindataEndpoint, err := global.GetEndpointFromBNS(global.AC.Config.MaindataBNS, global.AC.Clients.BNS)
	if err != nil {
		rkunErr := kunErr.NewServiceException("createNewFreeUser: get maindata endpoint fail", err)
		response.WriteHeaderAndEntity(http.StatusInternalServerError, resp.NewFailResp(rkunErr))
	}

	newUser, err := maindata.MaindataManager.CreateNewFreeUser(accountId, requestId, maindataEndpoint, global.AC.Clients.Iam)
	if err != nil {
		rKunErr := kunErr.NewServiceException("create free new user failed", err)
		c.WithErrorLog(rKunErr).WriteTo(response)
	}
	response.WriteHeaderAndEntity(http.StatusOK, newUser)
	return
}

// findNewFreeUser 查询用户是否购买过免费资源包
func (r OrderRest) findNewFreeUser(c *server.Context) {
	defer c.Logger().TimeTrack(time.Now(), "findNewFreeUser")

	response := c.Response()
	req := c.Request().Request
	defer req.Body.Close()

	// 1. 获取用户account_id
	accountId := c.Request().PathParameter("accountId")
	if accountId == "" {
		rkunErr := kunErr.NewInvalidParameterValueException("findNewFreeUser : parameter error, accountID must not empty", nil)
		response.WriteHeaderAndEntity(http.StatusBadRequest, resp.NewFailResp(rkunErr))
		return
	}

	// 2. 请求数据中心服务，查询新用户记录
	requestId := req.Header.Get(api.HeaderXRequestID)
	isNewUser, err := isFreeUser(accountId, requestId, global.AC.Config.MaindataBNS, global.AC.Clients.BNS, global.AC.Clients.Iam)
	if err != nil {
		response.WriteHeaderAndEntity(http.StatusInternalServerError, resp.NewFailResp(err))
	}
	response.WriteHeaderAndEntity(http.StatusOK, map[string]interface{}{
		"isNewUser": isNewUser,
	})
	return
}

// isFreeUser: 查询用户是否为免费用户且未购买免费资源包
func isFreeUser(accountId string, requestId string, bnsName string, bnsClient kunbns.Client, iamClient iam.ClientInterface) (bool, error) {
	isNewUser := false
	// 1. 获取主数据endpoint
	maindataEndpoint, err := global.GetEndpointFromBNS(bnsName, bnsClient)
	if err != nil {
		rkunErr := kunErr.NewServiceException("findNewFreeUser: get maindata endpoint fail", err)
		return isNewUser, rkunErr
	}
	// 2. 查询主数据，获取用户购买免费包记录
	newUser, err := maindata.MaindataManager.QueryNewFreeUser(accountId, requestId, maindataEndpoint, iamClient)
	if err != nil {
		rKunErr := kunErr.NewServiceException("find new free user failed", err)
		return isNewUser, rKunErr
	}

	// 3. 判断用户是否购买过免费资源包
	if newUser != nil && newUser.FreepackageOrders == "" {
		isNewUser = true
	}
	return isNewUser, nil
}

// updateFreeUser 新用户购买免费资源包后，需要更新数据中心新用户记录
func updateFreeUser(accountId string, requestId string, orderIdsStr string, bnsName string, bnsClient kunbns.Client, iamClient iam.ClientInterface) error {
	// 1. 参数判断，判断订单IDs是否为空
	if len(orderIdsStr) == 0 {
		rkunErr := kunErr.NewInvalidParameterValueException("updateNewFreeUser : parameter error, orderIds must not empty", nil)
		return rkunErr
	}

	// 2. 获取主数据endpoint
	maindataEndpoint, err := global.GetEndpointFromBNS(bnsName, bnsClient)
	if err != nil {
		rkunErr := kunErr.NewServiceException("updateNewFreeUser: get maindata endpoint fail", err)
		return rkunErr
	}

	_, err = maindata.MaindataManager.UpdateNewFreeUser(accountId, requestId, global.AC.Config.Region, orderIdsStr, maindataEndpoint, global.AC.Clients.Iam)
	if err != nil {
		rKunErr := kunErr.NewServiceException("update free new user failed", err)
		return rKunErr
	}
	return nil
}

// createAutoRenew 创建自动续费规则
func (r OrderRest) createAutoRenew(c *server.Context) {
	defer c.Logger().TimeTrack(time.Now(), "createAutoRenew")

	response := c.Response()
	re := c.Request().Request
	defer re.Body.Close()

	requestUser, _ := c.Request().Attribute("User").(*iam.User)
	accountId := requestUser.Domain.ID

	decoder := json.NewDecoder(re.Body)
	autoRenewRule := new(dao.AutoRenewRule)
	if err := decoder.Decode(autoRenewRule); err != nil {
		c.WithWarnLog(kunErr.NewInvalidRequestContentException(err.Error(), nil)).WriteTo(response)
		return
	}

	// 1. 参数校验, ServiceId为空时，需传RenewTimeUnit
	if autoRenewRule.OrderId == "" || autoRenewRule.RenewTime <= 0 || (autoRenewRule.ServiceId == "" && autoRenewRule.RenewTimeUnit == "") {
		c.WithWarnLog(kunErr.NewInvalidParameterValueException("autoRenew rule parameter(orderId|autoRenewTime|renewTimeUnit) invalid", nil)).WriteTo(response)
		return
	}

	// 自动续费时间单位为month或year
	if autoRenewRule.RenewTimeUnit != "" && (strings.ToLower(autoRenewRule.RenewTimeUnit) != RenewTimeUnitMonth && strings.ToLower(autoRenewRule.RenewTimeUnit) != RenewTimeUnitYear) {
		c.WithWarnLog(kunErr.NewInvalidParameterValueException("renewTimeUnit invalid, renewTimeUnit must be month or year", nil)).WriteTo(response)
		return
	}
	// 自动续费accountId与登录accountId需一致
	if autoRenewRule.AccountId != accountId {
		c.WithWarnLog(kunErr.NewInvalidParameterValueException("autoRenew rule parameter accountId invalid", nil)).WriteTo(response)
		return
	}

	// 2. 查看是否有自动续费记录
	fr := &dao.AutoRenewRule{
		AccountId: accountId,
		OrderId:   autoRenewRule.OrderId,
	}
	if err := dao.FindAutoRenewRule(fr); err != nil {
		c.WithWarnLog(err).WriteTo(response)
		return
	}
	if fr.RenewState != 0 && fr.RenewState != AutoRenewDeleteState {
		// 已经存在，3(已完成),4(已删除)的直接退出，1(待创建)或2(创建中)的交给旁路轮训job去创建
		c.WithWarnLog(apiErr.NewResourceConflictException("autoRenew Rule exist", nil)).WriteTo(response)
		return
	}

	// 3、查询订单详情
	requestId := re.Header.Get(api.HeaderXRequestID)
	orderDetail, err := order.OrderManager.QueryOrderDetail(autoRenewRule.OrderId, requestId, global.AC.Config.BillingEndpoint, global.AC.Clients.Iam)
	if err != nil {
		logs.Errorf("query order detail fail, orderId: %v, requestId: %v, err: %v", autoRenewRule.OrderId, requestId, err)
		rkunErr := kunErr.NewServiceException("CreateAutoRenew fail, query order fail", err)
		c.WithWarnLog(rkunErr).WriteTo(response)
	}
	// 免费包不能创建自动续费
	if (math.Abs(orderDetail.Price-0.00) > 0.0000001) == false {
		rkunErr := kunErr.NewInvalidParameterValueException("free package is not allowed to create autorenew rule", nil)
		c.WithWarnLog(rkunErr).WriteTo(response)
		return
	}

	// 3、下单不勾选，兼容从资源包列表页创建自动续费, 包含ServiceId，ServiceId对应资源包packageName
	if autoRenewRule.ServiceId != "" {
		serviceIds := []string{autoRenewRule.ServiceId}
		if autoRenewRule.RenewTimeUnit == "" && len(orderDetail.Items) > 0 {
			autoRenewRule.RenewTimeUnit = strings.ToLower(orderDetail.Items[0].TimeUnit)
		}
		autoReq := &api.CreateAutoRenewRequest{
			AccountId:     autoRenewRule.AccountId,
			Region:        global.AC.Config.Region,
			ServiceType:   "CFC",
			ServiceIds:    serviceIds,
			RenewTimeUnit: autoRenewRule.RenewTimeUnit,
			RenewTime:     autoRenewRule.RenewTime,
		}

		err = order.OrderManager.CreateAutoRenew(requestId, autoReq, global.AC.Config.BillingOrderRenewEndpoint, global.AC.Clients.Iam)
		if err != nil {
			logs.Errorf("CreateAutoRenew fail, requestId:%s, orderId: %s ", requestId, autoRenewRule.OrderId)
			rkunErr := kunErr.NewServiceException("CreateAutoRenew fail", err)
			c.WithWarnLog(rkunErr).WriteTo(response)
			return
		}

		// 3.2、自动续费记录写数据库
		autoRenewRule.Region = global.AC.Config.Region
		autoRenewRule.RenewState = AutoRenewCreatedState
		if fr.RenewState == AutoRenewDeleteState {
			updatRule := dao.AutoRenewRule{
				AccountId: accountId,
				OrderId:   autoRenewRule.OrderId,
			}
			if err = dao.UpdateAutoRenewRule(updatRule, autoRenewRule); err != nil {
				logs.Errorf("UpdateAutoRenew to db fail, err: %v, accountId:%s, orderId: %s ", err, autoRenewRule.AccountId, autoRenewRule.OrderId)
			}
		} else {
			if err = dao.CreateAutoRenewRule(autoRenewRule); err != nil {
				logs.Errorf("CreateAutoRenew to db fail, err: %v, accountId:%s, orderId: %s ", err, autoRenewRule.AccountId, autoRenewRule.OrderId)
			}
		}
		response.WriteHeaderAndEntity(http.StatusOK, resp.NewSuccessResp(autoRenewRule))
		return
	}

	// 下单时勾选，不包含serviceId
	// 5. 创建自动续费规则
	autoRenewRule.RenewTimeUnit = strings.ToLower(autoRenewRule.RenewTimeUnit)
	autoRenewRule.AccountId = accountId
	autoRenewRule.Region = global.AC.Config.Region
	autoRenewRule.RenewState = AutoRenewCreatingState
	if fr.RenewState == AutoRenewDeleteState {
		updatRule := dao.AutoRenewRule{
			AccountId: accountId,
			OrderId:   autoRenewRule.OrderId,
		}
		if err = dao.UpdateAutoRenewRule(updatRule, autoRenewRule); err != nil {
			logs.Errorf("update auto renew fail, err: %v, accountId:%s, orderId: %s ", err, autoRenewRule.AccountId, autoRenewRule.OrderId)
			c.WithWarnLog(err).WriteTo(response)
			return
		}
	} else {
		if err = dao.CreateAutoRenewRule(autoRenewRule); err != nil {
			logs.Errorf("create auto renew rule fail, err: %v", err)
			c.WithWarnLog(err).WriteTo(response)
			return
		}
	}

	response.WriteHeaderAndEntity(http.StatusOK, resp.NewSuccessResp(autoRenewRule))
	return
}

// deleteAutoRenew 删除自动续费规则
func (r OrderRest) deleteAutoRenew(c *server.Context) {
	defer c.Logger().TimeTrack(time.Now(), "deleteAutoRenew")

	response := c.Response()
	re := c.Request().Request
	defer re.Body.Close()

	// 1. 获取accountId
	accountID := c.Request().PathParameter("accountId")
	if accountID == "" {
		rkunErr := kunErr.NewInvalidParameterValueException("deleteAutoRenew : parameter error, accountID must not empty", nil)
		response.WriteHeaderAndEntity(http.StatusBadRequest, resp.NewFailResp(rkunErr))
		return
	}

	// 2. 获取region参数, 缺省值为当前请求地域
	region := c.Request().QueryParameter("region")
	if region == "" {
		region = global.AC.Config.Region
	}

	// 3. 获取serviceId参数
	serviceId := c.Request().QueryParameter("serviceId")
	if serviceId == "" {
		rkunErr := kunErr.NewInvalidParameterValueException("deleteAutoRenew: parameter error, serviceId must not empty", nil)
		response.WriteHeaderAndEntity(http.StatusBadRequest, resp.NewFailResp(rkunErr))
		return
	}

	// 4. 查询自动续费规则ID
	requestId := re.Header.Get(api.HeaderXRequestID)
	rules, err := order.OrderManager.QueryAutoRenew(requestId, accountID, serviceId, region, global.AC.Config.BillingOrderRenewEndpoint, global.AC.Clients.Iam)
	if err != nil {
		rKunErr := kunErr.NewServiceException("deleteAutoRenew: query autoRenew rule fail ", err)
		response.WriteHeaderAndEntity(http.StatusBadRequest, resp.NewFailResp(rKunErr))
		return
	}

	// 5. 判断自动续费规则是否存在，或已经删除，一个serviceId对应一个ruleId
	ruleId := ""
	tt, _ := time.Parse("2006-01-02 15:04:05", "2020-01-02 15:04:05")
	for _, rule := range rules {
		if rule.DeleteTime.IsZero() || rule.DeleteTime.Before(tt) {
			ruleId = rule.Uuid
			break
		}
	}
	if ruleId == "" {
		rKunErr := kunErr.NewServiceException("deleteAutoRenew: rule not exist ", nil)
		response.WriteHeaderAndEntity(http.StatusBadRequest, resp.NewFailResp(rKunErr))
		return
	}

	// 6. 删除自动续费规则
	if err = order.OrderManager.DeleteAutoRenew(accountID, requestId, ruleId, global.AC.Config.BillingOrderRenewEndpoint, global.AC.Clients.Iam); err != nil {
		rKunErr := kunErr.NewServiceException("deleteAutoRenew: delete fail ", err)
		response.WriteHeaderAndEntity(http.StatusBadRequest, resp.NewFailResp(rKunErr))
		return
	}

	// 7. 删除数据库中自动续费规则记录
	deleteRule := &dao.AutoRenewRule{
		AccountId: accountID,
		ServiceId: serviceId,
	}
	if err := dao.DeleteAutoRenewRule(deleteRule); err != nil {
		logs.Errorf("DeleteAutoRenewRule fail, err: %v", err)
	}
	response.WriteHeaderAndEntity(http.StatusNoContent, nil)
	return
}

// findAutoRenew 查询自动续费记录
func (r OrderRest) findAutoRenew(c *server.Context) {
	defer c.Logger().TimeTrack(time.Now(), "findAutoRenew")

	response := c.Response()
	re := c.Request().Request
	defer re.Body.Close()

	// 1. 获取accountId
	accountID := c.Request().PathParameter("accountId")
	if accountID == "" {
		rkunErr := kunErr.NewInvalidParameterValueException("deleteAutoRenew : parameter error, accountID must not empty", nil)
		response.WriteHeaderAndEntity(http.StatusBadRequest, resp.NewFailResp(rkunErr))
		return
	}

	// 2. 获取region参数, 缺省值为当前请求地域
	region := c.Request().QueryParameter("region")
	if region == "" {
		region = global.AC.Config.Region
	}

	// 3. 获取serviceId参数
	serviceId := c.Request().QueryParameter("serviceId")
	if serviceId == "" {
		rkunErr := kunErr.NewInvalidParameterValueException("deleteAutoRenew: parameter error, serviceId must not empty", nil)
		response.WriteHeaderAndEntity(http.StatusBadRequest, resp.NewFailResp(rkunErr))
		return
	}

	// 4. 查询自动续费规则ID
	requestId := re.Header.Get(api.HeaderXRequestID)
	rules, err := order.OrderManager.QueryAutoRenew(requestId, accountID, serviceId, region, global.AC.Config.BillingOrderRenewEndpoint, global.AC.Clients.Iam)
	if err != nil {
		rKunErr := kunErr.NewServiceException("deleteAutoRenew: query autoRenew rule fail ", err)
		response.WriteHeaderAndEntity(http.StatusBadRequest, resp.NewFailResp(rKunErr))
		return
	}

	response.WriteHeaderAndEntity(http.StatusOK, rules)
	return
}

// insideCreateAutoRenew 创建自动续费
func (r OrderRest) insideCreateAutoRenew(c *server.Context) {
	defer c.Logger().TimeTrack(time.Now(), "insideCreateAutoRenew")

	response := c.Response()
	re := c.Request().Request
	defer re.Body.Close()

	decoder := json.NewDecoder(re.Body)
	autoRenewRule := new(dao.AutoRenewRule)
	if err := decoder.Decode(autoRenewRule); err != nil {
		rKunErr := kunErr.NewInvalidRequestContentException(err.Error(), nil)
		response.WriteHeaderAndEntity(http.StatusInternalServerError, resp.NewFailResp(rKunErr))
		return
	}
	// 1. 参数校验
	if autoRenewRule.OrderId == "" || autoRenewRule.RenewTime <= 0 || autoRenewRule.RenewTimeUnit == "" {
		rKunErr := kunErr.NewInvalidParameterValueException("autoRenew rule parameter(orderId|autoRenewTime|RenewTimeUnit) invalid", nil)
		response.WriteHeaderAndEntity(http.StatusBadRequest, resp.NewFailResp(rKunErr))
		return
	}
	autoRenewRule.RenewTimeUnit = strings.ToLower(autoRenewRule.RenewTimeUnit)

	// 2. 查询订单状态
	requestId := re.Header.Get(api.HeaderXRequestID)
	queryOrderResponse, err := order.OrderManager.QueryOrder(autoRenewRule.OrderId, requestId, global.AC.Config.BillingEndpoint, global.AC.Clients.Iam)
	if err != nil || queryOrderResponse.AccountId == "" {
		errT := fmt.Errorf("query order err: %v or order's accountId is empty, orderId: %v, requestId: %v", err, autoRenewRule.OrderId, requestId)
		logs.Warnf("insideCreateAutoRenew: query order failed: %v", errT)
		rKunErr := kunErr.NewInvalidRequestContentException(errT.Error(), nil)
		response.WriteHeaderAndEntity(http.StatusInternalServerError, resp.NewFailResp(rKunErr))
		return
	}

	// 3. 订单创建失败，取消或退款时，需删除自动续费记录
	// 工具函数，判断订单状态是否为失败状态
	validOrderFunc := func(orderStatus string, invalidStatus []string) bool {
		for _, s := range invalidStatus {
			if orderStatus == s {
				return false
			}
		}
		return true
	}

	if !validOrderFunc(queryOrderResponse.Status, OrderInvalidStatus) {
		delteRule := &dao.AutoRenewRule{
			AccountId: autoRenewRule.AccountId,
			OrderId:   autoRenewRule.OrderId,
		}
		err = dao.DeleteAutoRenewRule(delteRule)
		errT := fmt.Errorf("order invalid, delete autorenew fail err : %v", err)
		rKunErr := kunErr.NewInvalidRequestContentException(errT.Error(), nil)
		response.WriteHeaderAndEntity(http.StatusInternalServerError, resp.NewFailResp(rKunErr))
		return
	}

	// 4. 查询订单中的用量包信息
	packageInfo, err := order.OrderManager.QueryPackageStatus(autoRenewRule.AccountId, autoRenewRule.OrderId, requestId, global.AC.Config.BillingPackageEndpoint, global.AC.Clients.Iam)
	if err != nil || len(packageInfo) == 0 {
		errT := fmt.Errorf("query package status err: %v or package is empty, orderId: %v, requestId: %v", err, autoRenewRule.OrderId, requestId)
		logs.Warnf("CreateAutoRenewRule: err: %v", errT)
		rKunErr := kunErr.NewInvalidRequestContentException(errT.Error(), nil)
		response.WriteHeaderAndEntity(http.StatusInternalServerError, resp.NewFailResp(rKunErr))
		return
	}

	if packageInfo[0].Status != "RUNNING" {
		errT := fmt.Errorf("package status is not running, orderId: %v, requestId: %v", autoRenewRule.OrderId, requestId)
		logs.Warnf("CreateAutoRenewRule: err: %v", errT)
		rKunErr := kunErr.NewInvalidRequestContentException(errT.Error(), nil)
		response.WriteHeaderAndEntity(http.StatusInternalServerError, resp.NewFailResp(rKunErr))
		return
	}

	// 5. 查询自动续费记录
	rules, err := order.OrderManager.QueryAutoRenew(requestId, autoRenewRule.AccountId, packageInfo[0].PackageName, autoRenewRule.Region, global.AC.Config.BillingOrderRenewEndpoint, global.AC.Clients.Iam)
	if err != nil {
		errT := fmt.Errorf("query autoRenew failed, requestId: %s, accountId: %s, servicdId: %s, err: %v", requestId, autoRenewRule.AccountId, packageInfo[0].PackageName, err)
		logs.Warnf("CreateAutoRenewRule: query autoRenew failed, err: %v", errT)
		rKunErr := kunErr.NewInvalidRequestContentException(errT.Error(), nil)
		response.WriteHeaderAndEntity(http.StatusInternalServerError, resp.NewFailResp(rKunErr))
		return
	}

	// 6. 判断是否已经有自动续费记录
	isRuleExist := func(packageName string, rules []api.QueryAutoRenewResponse) bool {
		for _, ru := range rules {
			if ru.ServiceId == packageName {
				return true
			}
		}
		return false
	}
	if !isRuleExist(packageInfo[0].PackageName, rules) {
		// 7. 无自动续费记录，则需创建自动续费，提取量包中的packageName作为serviceIds
		serviceIds := []string{}
		for _, p := range packageInfo {
			if p.PackageName != "" {
				serviceIds = append(serviceIds, p.PackageName)
			}
		}

		// 订单状态为CREATED，资源状态为RUNNING，则可以为资源创建自动续费
		autoReq := &api.CreateAutoRenewRequest{
			AccountId:     autoRenewRule.AccountId,
			Region:        autoRenewRule.Region,
			ServiceType:   "CFC",
			ServiceIds:    serviceIds,
			RenewTimeUnit: autoRenewRule.RenewTimeUnit,
			RenewTime:     autoRenewRule.RenewTime,
		}

		err = order.OrderManager.CreateAutoRenew(requestId, autoReq, global.AC.Config.BillingOrderRenewEndpoint, global.AC.Clients.Iam)
		if err != nil {
			errT := fmt.Errorf("create autoRenew rule err: %v, orderId: %v, requestId: %v, accountId: %v", err, autoRenewRule.OrderId, requestId, autoRenewRule.AccountId)
			logs.Warnf("CreateAutoRenewRule: create autoRenew rule failed, err: %v", errT)
			rKunErr := kunErr.NewInvalidRequestContentException(errT.Error(), nil)
			response.WriteHeaderAndEntity(http.StatusInternalServerError, resp.NewFailResp(rKunErr))
			return
		}
	}
	// 8. 更新DB自动续费规则记录
	autoRenewRule.ServiceId = packageInfo[0].PackageName
	updateCondition := dao.AutoRenewRule{
		AccountId: autoRenewRule.AccountId,
		OrderId:   autoRenewRule.OrderId,
		Region:    autoRenewRule.Region,
	}

	updateRes := &dao.AutoRenewRule{
		RenewState: AutoRenewCreatedState,
		ServiceId:  autoRenewRule.ServiceId,
	}

	if err = dao.UpdateAutoRenewRule(updateCondition, updateRes); err != nil {
		logs.Warnf("UpdateAutoRenewRule failed, err: %v", err)
		errT := fmt.Errorf("UpdateAutoRenewRule failed, err: %v", err)
		rKunErr := kunErr.NewInvalidRequestContentException(errT.Error(), nil)
		response.WriteHeaderAndEntity(http.StatusInternalServerError, resp.NewFailResp(rKunErr))
		return
	}

	response.WriteHeaderAndEntity(http.StatusOK, resp.NewSuccessResp(autoRenewRule))
	return
}

// insideDeleteAutoRenew 删除DB中自动续费记录
func (r OrderRest) insideDeleteAutoRenew(c *server.Context) {
	defer c.Logger().TimeTrack(time.Now(), "insideDeleteAutoRenew")

	response := c.Response()
	re := c.Request().Request
	defer re.Body.Close()

	decoder := json.NewDecoder(re.Body)
	autoRenewRule := new(dao.AutoRenewRule)
	if err := decoder.Decode(autoRenewRule); err != nil {
		rKunErr := kunErr.NewInvalidRequestContentException(err.Error(), nil)
		response.WriteHeaderAndEntity(http.StatusInternalServerError, resp.NewFailResp(rKunErr))
		return
	}
	requestId := re.Header.Get(api.HeaderXRequestID)
	// 1. 查询自动续费是否已经删除
	rules, err := order.OrderManager.QueryAutoRenew(requestId, autoRenewRule.AccountId, autoRenewRule.ServiceId, autoRenewRule.Region, global.AC.Config.BillingOrderRenewEndpoint, global.AC.Clients.Iam)
	if err != nil {
		errT := fmt.Errorf("query autoRenew failed, requestId: %s, accountId: %s, servicdId: %s, err: %v", requestId, autoRenewRule.AccountId, autoRenewRule.ServiceId, err)
		logs.Warnf("insideDeleteAutoRenew: err: %v", errT)
		rKunErr := kunErr.NewInvalidRequestContentException(errT.Error(), nil)
		response.WriteHeaderAndEntity(http.StatusInternalServerError, resp.NewFailResp(rKunErr))
		return
	}

	if len(rules) == 0 {
		updateCondition := &dao.AutoRenewRule{
			AccountId: autoRenewRule.AccountId,
			OrderId:   autoRenewRule.OrderId,
			Region:    autoRenewRule.Region,
		}
		if err = dao.DeleteAutoRenewRule(updateCondition); err != nil {
			logs.Warnf("insideDeleteAutoRenew: delete autoRenew rule fail, err: %v", err)
			errT := fmt.Errorf("insideDeleteAutoRenew: delete autoRenew rule fail, err: %v", err)
			rKunErr := kunErr.NewInvalidRequestContentException(errT.Error(), nil)
			response.WriteHeaderAndEntity(http.StatusInternalServerError, resp.NewFailResp(rKunErr))
			return
		}
		logs.Infof("insideDeleteAutoRenew: delete success, accountId: %s, orderId: %s", autoRenewRule.AccountId, autoRenewRule.OrderId)
	}
	response.WriteHeaderAndEntity(http.StatusOK, nil)
	return
}
