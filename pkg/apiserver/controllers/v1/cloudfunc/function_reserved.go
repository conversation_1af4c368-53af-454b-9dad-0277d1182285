package cloudfunc

import (
	"fmt"
	"net/http"
	"strconv"
	"time"

	"github.com/emicklei/go-restful"
	"github.com/google/uuid"

	"icode.baidu.com/baidu/faas/kun/pkg/api"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/dao"
	apiErr "icode.baidu.com/baidu/faas/kun/pkg/apiserver/error"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/global"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/models"
	kunErr "icode.baidu.com/baidu/faas/kun/pkg/error"
	"icode.baidu.com/baidu/faas/kun/pkg/server"
	"icode.baidu.com/baidu/faas/kun/pkg/util/logs"
)

type FunctionReservedRest struct {
	Path string
}

// Create Function Reserved
func (f *FunctionReservedRest) create(c *server.Context) {
	response := c.Response()
	ctx := global.BuildApiserverContext(c.Request(), c.Response(), "createFunctionReserved")
	defer global.WriteSummary(ctx, api.LogNotToSummary)
	functionReservedReq := new(dao.FunctionReserved)
	var err error
	if err = c.Request().ReadEntity(functionReservedReq); err != nil {
		c.WithWarnLog(kunErr.NewInvalidRequestContentException("", err)).WriteTo(response)
		return
	}

	reserved := new(dao.FunctionReserved)
	reserved, err = CreateFunctionReserved(c.Request(), functionReservedReq)
	if err != nil {
		c.WithWarnLog(err).WriteTo(response)
		return
	}

	logs.Infof("create function reserved success, FunctionReserved: %+v", reserved)
	response.WriteHeaderAndEntity(http.StatusCreated, reserved)
}

// Update Function Reserved
func (f *FunctionReservedRest) update(c *server.Context) {
	response := c.Response()
	ctx := global.BuildApiserverContext(c.Request(), c.Response(), "updateFunctionReserved")
	defer global.WriteSummary(ctx, api.LogNotToSummary)
	cond := &dao.FunctionReserved{
		Uuid: c.Request().PathParameter("Uuid"),
	}

	updateReserved := new(dao.FunctionReserved)
	var err error
	if err = c.Request().ReadEntity(updateReserved); err != nil {
		c.WithWarnLog(kunErr.NewInvalidRequestContentException("", err)).WriteTo(response)
		return
	}

	if err = UpdateFunctionReserved(c.Request(), cond, updateReserved); err != nil {
		c.WithWarnLog(err).WriteTo(response)
		return
	}

	logs.Infof("update function reserved success, FunctionReserved: %+v", updateReserved)
	response.WriteHeaderAndEntity(http.StatusOK, updateReserved)
}

func (f *FunctionReservedRest) delete(c *server.Context) {
	response := c.Response()
	uuid := c.Request().PathParameter("Uuid")
	ctx := global.BuildApiserverContext(c.Request(), c.Response(), "deleteFunctionReserved")
	defer global.WriteSummary(ctx, api.LogNotToSummary)
	err := DeleteFunctionReserved(c.Request(), uuid)

	if err != nil {
		c.WithErrorLog(err).WriteTo(response)
		return
	}

	response.WriteHeaderAndEntity(http.StatusNoContent, nil)
}

func (f *FunctionReservedRest) list(c *server.Context) {
	response := c.Response()
	r := c.Request()
	ctx := global.BuildApiserverContext(c.Request(), c.Response(), "findFunctionReserved")
	defer global.WriteSummary(ctx, api.LogNotToSummary)

	funcName := r.QueryParameter("FunctionName")
	cond := &dao.FunctionReserved{
		FunctionName: funcName,
	}
	res, err := ListFunctionReserved(r, cond)
	if err != nil {
		c.WithErrorLog(err).WriteTo(response)
		return
	}
	response.WriteHeaderAndEntity(http.StatusOK, res)
}

func (f *FunctionReservedRest) find(c *server.Context) {
	response := c.Response()
	r := c.Request()
	ctx := global.BuildApiserverContext(c.Request(), c.Response(), "findFunctionReserved")
	defer global.WriteSummary(ctx, api.LogNotToSummary)

	funcBrn := r.PathParameter("FunctionBrn")
	reserved := &dao.FunctionReserved{
		FunctionBrn: funcBrn,
	}
	err := FindFunctionReserved(r, reserved)
	if err != nil {
		logs.Warnf("function reserved not find, brn: %v, err: %v", err)
		response.WriteHeaderAndEntity(http.StatusNoContent, reserved)
		return
	}
	response.WriteHeaderAndEntity(http.StatusOK, reserved)
}

func CreateFunctionReserved(request *restful.Request, reserved *dao.FunctionReserved) (r *dao.FunctionReserved, err error) {
	// 验证参数, 函数版本和别名不能都为空
	if reserved.FunctionBrn == "" {
		err = kunErr.NewInvalidParameterValueException("function brn is invalid", nil)
		return nil, err
	}

	// 验证用户是否存在
	user, err := GetUser(request)
	if err != nil {
		cause := fmt.Sprintf("user not found :%+v", user)
		err = apiErr.NewResourceNotFoundException(cause, nil)
		return nil, err
	}

	// 查看函数是否存在, 版本是否为非$LATEST
	findFunc := &dao.Function{
		Uid:         user.Domain.ID,
		FunctionBrn: reserved.FunctionBrn, // 函数function_brn
	}
	if err = models.CkeckFunction(findFunc); err != nil {
		return nil, err
	}

	// 检查申请创建预留实例个数是否合法
	if err = models.CheckReservedCount(findFunc, reserved.ReservedCount); err != nil {
		return nil, err
	}

	// 查看该函数是否已经配置预留实例
	findReserved := &dao.FunctionReserved{
		Uid:         user.Domain.ID,
		FunctionBrn: findFunc.FunctionBrn,
	}
	if err = dao.DBFindFunctionReserved(findReserved, false); err == nil && findReserved.Uuid != "" {
		return nil, apiErr.NewTestEventConflictException("function reserved is exist", nil)
	}

	// 创建function reserved
	reserved.Uid = user.Domain.ID
	reserved.Uuid = uuid.New().String()
	reserved.Status = api.ReservedStart // 开始时，状态为0
	reserved.ReservedTs = time.Now().Unix()
	models.CopyFunctionInfo(reserved, findFunc)
	if err = dao.DBCreateFunctionReserved(reserved); err != nil {
		return nil, err
	}

	// 更新函数预留状态
	f := &dao.Function{}
	f.ReservedType = "reserved"
	if err = dao.UpdateFunc(*findFunc, f); err != nil {
		logs.Warnf("update function reserved fail, err: %v", err)
	}
	return reserved, nil
}

func ListFunctionReserved(request *restful.Request, cond *dao.FunctionReserved) (reserveds []dao.FunctionReserved, err error) {
	if cond.FunctionName == "" {
		err = kunErr.NewInvalidParameterValueException("param invalid, function name is empty", nil)
		return
	}

	// 验证用户是否存在
	user, err := GetUser(request)
	if err != nil {
		cause := fmt.Sprintf("user not found :%+v", user)
		err = apiErr.NewResourceNotFoundException(cause, nil)
		return
	}

	cond.Uid = user.Domain.ID
	reserveds, err = dao.DBListFunctionReserved(cond)
	return
}

func FindFunctionReserved(request *restful.Request, cond *dao.FunctionReserved) error {
	// 验证参数
	if cond.FunctionBrn == "" {
		return kunErr.NewInvalidParameterValueException("param invalid, function brn is empty", nil)
	}

	// 验证用户是否存在
	user, err := GetUser(request)
	if err != nil {
		cause := fmt.Sprintf("user not found :%+v", user)
		err = apiErr.NewResourceNotFoundException(cause, nil)
		return err
	}

	cond.Uid = user.Domain.ID
	return dao.DBFindFunctionReserved(cond, false)
}

func UpdateFunctionReserved(request *restful.Request, cond *dao.FunctionReserved, updateReserved *dao.FunctionReserved) (err error) {
	// 验证参数
	if cond.Uuid == "" {
		err = kunErr.NewInvalidParameterValueException("params invalid, uuid is empty", nil)
		return err
	}

	// 验证用户是否存在
	user, err := GetUser(request)
	if err != nil {
		cause := fmt.Sprintf("user not found :%+v", user)
		err = apiErr.NewResourceNotFoundException(cause, nil)
		return err
	}

	// 检查函数预留实例是否存在
	if err := dao.DBFindFunctionReserved(cond, false); err != nil {
		cause := fmt.Sprintf("function reserved not exists: %+v", cond)
		err = apiErr.NewResourceNotFoundException(cause, nil)
		return err
	}

	// 查看函数是否存在
	//updateReserved.FunctionBrn = cond.FunctionBrn
	findFunc := &dao.Function{
		Uid:         user.Domain.ID,
		FunctionBrn: cond.FunctionBrn,
	}
	if err = models.CkeckFunction(findFunc); err != nil {
		return err
	}

	// 检查申请创建预留实例个数是否合法
	if err = models.CheckReservedCount(findFunc, updateReserved.ReservedCount); err != nil {
		return err
	}

	// 创建中的预留实例不能被更新
	if cond.Status == api.ReservedCreating {
		return kunErr.NewInvalidRequestContentException("function reserved is creating, cannot be updated", nil)
	}

	updateReserved.Uid = user.Domain.ID
	updateReserved.Status = api.ReservedStart
	updateReserved.ReservedTs = time.Now().Unix()
	if err = dao.DBUpdateFunctionReserved(cond, updateReserved, false); err != nil {
		return err
	}
	// 补全字段 更新function reserved
	models.CopyFunctionReserved(cond, updateReserved)
	updateReserved.CommitID = findFunc.CommitID

	// 更新函数预留状态
	if findFunc.ReservedType != "reserved" {
		f := &dao.Function{}
		f.ReservedType = "reserved"
		if err = dao.UpdateFunc(*findFunc, f); err != nil {
			logs.Warnf("update function reserved fail, err: %v", err)
		}
	}

	return nil
}

func DeleteFunctionReserved(request *restful.Request, uuid string) error {
	// 验证参数
	if uuid == "" {
		return kunErr.NewInvalidParameterValueException("params invalid, uuid is empty", nil)
	}

	// 验证用户是否存在
	user, err := GetUser(request)
	if err != nil {
		cause := fmt.Sprintf("user not found :%+v", user)
		err = apiErr.NewResourceNotFoundException(cause, nil)
		return err
	}

	// 检查函数预留实例是否存在
	findReserved := &dao.FunctionReserved{
		Uid:  user.Domain.ID,
		Uuid: uuid,
	}
	if err := dao.DBFindFunctionReserved(findReserved, false); err != nil {
		cause := fmt.Sprintf("function reserved not exists: %+v", findReserved)
		err = apiErr.NewResourceNotFoundException(cause, nil)
		return err
	}

	// 创建中的预留实例不能被删除
	if findReserved.Status == api.ReservedCreating {
		return kunErr.NewInvalidRequestContentException("function reserved is creating, cannot be deleted", nil)
	}

	cond := &dao.FunctionReserved{
		Uuid: uuid,
	}
	if err = dao.DBDeleteFunctionReserved(cond, false); err != nil {
		return err
	}

	// 更新函数预留状态
	findFunc := &dao.Function{
		Uid:         findReserved.Uid,
		FunctionBrn: findReserved.FunctionBrn,
	}
	f := &dao.Function{}
	f.ReservedType = "unReserved"
	if err = dao.UpdateFunc(*findFunc, f); err != nil {
		logs.Warnf("update function reserved fail, err: %v", err)
	}
	return nil
}

func (f *FunctionReservedRest) insideFind(c *server.Context) {
	response := c.Response()
	cond := &dao.FunctionReserved{}
	cond.Uuid = c.Request().PathParameter("Uuid")

	if err := dao.DBFindFunctionReserved(cond, true); err != nil {
		c.WithErrorLog(err).WriteTo(response)
		return
	}

	response.WriteHeaderAndEntity(http.StatusOK, cond)
}

func (f *FunctionReservedRest) insideList(c *server.Context) {
	response := c.Response()
	method := c.Request().QueryParameter("Method")
	durationStr := c.Request().QueryParameter("Duration")
	duration, _ := strconv.ParseInt(durationStr, 10, 64)
	cond := &dao.FunctionReserved{}
	// 回收预留实例时，只获取状态为Deleted的记录；其他情况获取非Deleted状态的记录
	if method == "recycle" {
		cond.Status = api.ReservedDeleted
		cond.ReservedTs = time.Now().Add(-time.Minute * time.Duration(duration)).Unix()
	}
	res, err := dao.InsideListFunctionReserved(cond)
	if err != nil {
		c.WithErrorLog(err).WriteTo(response)
		return
	}
	response.WriteHeaderAndEntity(http.StatusOK, res)
}

func (f *FunctionReservedRest) insideUpdate(c *server.Context) {
	response := c.Response()
	cond := &dao.FunctionReserved{
		Uuid: c.Request().PathParameter("Uuid"),
	}

	updateReserved := new(dao.FunctionReserved)
	var err error
	if err = c.Request().ReadEntity(updateReserved); err != nil {
		c.WithWarnLog(kunErr.NewInvalidRequestContentException("", err)).WriteTo(response)
		return
	}

	if err = dao.DBUpdateFunctionReserved(cond, updateReserved, true); err != nil {
		c.WithErrorLog(err).WriteTo(response)
		return
	}

	dao.DBFindFunctionReserved(cond, true)

	response.WriteHeaderAndEntity(http.StatusOK, cond)
}

func (f *FunctionReservedRest) insideDelete(c *server.Context) {
	response := c.Response()
	uuid := c.Request().PathParameter("Uuid")
	cond := &dao.FunctionReserved{
		Uuid: uuid,
	}
	if err := dao.DBDeleteFunctionReserved(cond, true); err != nil {
		c.WithErrorLog(err).WriteTo(response)
	}
	response.WriteHeaderAndEntity(http.StatusNoContent, nil)
}
