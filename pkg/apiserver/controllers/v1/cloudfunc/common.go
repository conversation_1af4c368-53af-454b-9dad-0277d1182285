package cloudfunc

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"strings"
	"sync"
	"time"

	"github.com/asaskevich/govalidator"
	"github.com/emicklei/go-restful"
	"github.com/jinzhu/gorm"

	"icode.baidu.com/baidu/faas/kun/pkg/api"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/dao"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/dbengine"
	apiErr "icode.baidu.com/baidu/faas/kun/pkg/apiserver/error"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/global"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/models"
	apiServer "icode.baidu.com/baidu/faas/kun/pkg/apiserver/server"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/service"
	"icode.baidu.com/baidu/faas/kun/pkg/bce/brn"
	"icode.baidu.com/baidu/faas/kun/pkg/bce/iam"
	kunErr "icode.baidu.com/baidu/faas/kun/pkg/error"
	"icode.baidu.com/baidu/faas/kun/pkg/server"
	trigger "icode.baidu.com/baidu/faas/kun/pkg/trigger/relation"
	"icode.baidu.com/baidu/faas/kun/pkg/util/logs"
	"icode.baidu.com/baidu/faas/kun/pkg/whitelist"
)

// 根据request初始化function信息
// 必须获取到的信息：1：Uid 2: Region
// 非必须获取到信息：1: functionName 3:Version
func InitFunction(rest *restful.Request) (*dao.Function, error) {
	// 可选
	qualifier := rest.QueryParameter("Qualifier")
	fName := rest.PathParameter("FunctionName")
	worksapceID := rest.Request.Header.Get(api.HeaderWorkspaceID)
	user, err := GetUser(rest)
	if err != nil {
		return nil, err
	}

	return models.InitUserFunc(user.Domain.ID, fName, qualifier, worksapceID)
}

func GetUser(rest *restful.Request) (*iam.User, error) {
	r := rest.Attribute("User")
	if r == nil {
		return nil, errors.New("User not found")
	}
	return r.(*iam.User), nil
}

func GetAliasFromReq(rest *restful.Request) (string, error) {
	qualifier := rest.QueryParameter("Qualifier")
	fName := rest.PathParameter("FunctionName")

	user, err := GetUser(rest)
	if err != nil {
		return "", err
	}
	_, _, alias, err := brn.DealFName(brn.Md5BceUid(user.Domain.ID), fName, "")
	if err != nil {
		return "", err
	}

	if qualifier != "" {
		t, _ := brn.JudgeQualifier(qualifier)
		if t == "alias" {
			return qualifier, nil
		}
	}
	if alias != "" {
		return alias, nil
	}

	return "", nil
}

// copyReqFuncToDaoFunc 将req中的函数信息复制到dao中的函数信息中，包括公共信息和私有信息
// 参数：
//
//	f (dao.Function) - req中的函数信息结构体指针
//	t (dao.Function) - dao中的函数信息结构体指针
//
// 返回值：无
func copyReqFuncToDaoFunc(f *dao.Function, t *dao.Function) {
	// common
	t.MemorySize = f.MemorySize
	t.Description = f.Description
	t.Handler = f.Handler
	t.Runtime = f.Runtime
	t.Timeout = f.Timeout
	t.Environment = f.Environment
	t.LogType = f.LogType
	t.LogBosDir = f.LogBosDir
	t.BlsLogSet = f.BlsLogSet
	t.Role = f.Role
	t.VpcConfig = f.VpcConfig
	t.PodConcurrentQuota = f.PodConcurrentQuota
	t.DeadLetterTopic = f.DeadLetterTopic
	t.LayerList = f.LayerList
	t.ServiceName = f.ServiceName
	t.AsyncInvokeConfig = f.AsyncInvokeConfig
	t.CFSConfig = f.CFSConfig
	t.VpcConfig = f.VpcConfig
	t.BlueprintTag = f.BlueprintTag
	t.CustomRuntimeConfig = f.CustomRuntimeConfig
	t.LangRuntime = f.LangRuntime
}

// vtype必须为: create,update
func validateFunc(vtype string, functions *dao.Function, byteArray []byte) error {
	// 获取最大超时时间配额，如果用户设置了最大超时时间配额，则function.Timeout与最大超时时间配额比较
	maxTimeout, err := global.AC.Clients.Ops.GetQuota(context.TODO(), functions.Uid, api.MaxFunctionTimeout)
	if err != nil {
		logs.Errorf("Get Configured MaxTimeout Failed", err)
	}

	maxMemoryLimit := api.FunctionMemorySizeMax

	if _, inWhite := global.AC.Config.SourceWhiteList.Get(functions.Uid); inWhite {
		maxMemoryLimit = api.CustomizedMemorySizeMax
	}

	//common
	if _, err := govalidator.ValidateStruct(functions); err != nil {
		return kunErr.NewInvalidParameterValueException("Validate Struct Failed", err)
	} else if err := models.CheckPtrString(functions.Description, 0, 255); err != nil {
		return kunErr.NewInvalidParameterValueException("Description length is Illegal", err)
	} else if err := models.CheckPtrString(functions.Role, 0, 64); err != nil {
		return kunErr.NewInvalidParameterValueException("Role length is Illegal", err)
	} else if err := models.CheckFunctionTimeout(functions.Timeout, 1, api.FunctionTimeoutMax, maxTimeout); err != nil {
		return kunErr.NewInvalidParameterValueException("Timeout size is Illegal", err)
	} else if _, err := models.CheckMultiple(functions.MemorySize, 128); err != nil {
		return kunErr.NewInvalidParameterValueException("MemorySize is Illegal", err)
	} else if err := models.CheckPtrIntSize(functions.MemorySize, 128, maxMemoryLimit); err != nil {
		return kunErr.NewInvalidParameterValueException("MemorySize is Illegal", err)
	} else if err := models.CheckEnvSize(functions.EnvironmentStr); err != nil {
		return kunErr.NewInvalidParameterValueException("Environment Size is Illegal", err)
	} else if err := models.CheckCodeSize(vtype, functions, byteArray); err != nil {
		return err
	} else if err := models.ValidateVpcConfig(vtype, functions); err != nil {
		return kunErr.NewInvalidParameterValueException(err.Error(), nil)
	} else if err := models.CheckPodConcurrentQuota(functions.PodConcurrentQuota, api.DefaultPodConcurrentQuotaMax); err != nil {
		return kunErr.NewInvalidParameterValueException(err.Error(), nil)
	} else if err := models.CheckAsyncInvokeConfig(functions); err != nil {
		return err
	} else if err := models.ValidateCFSConfig(functions); err != nil {
		return err
	}
	if functions.LogType == "bos" && !strings.HasPrefix(functions.LogBosDir, "bos://") {
		return kunErr.NewInvalidParameterValueException("Bos Dir is Illegal", nil)
	}
	if (functions.LogType == "kafka" || functions.LogType == "fluentd_kafka") && !strings.Contains(functions.LogBosDir, "topic=") {
		return kunErr.NewInvalidParameterValueException("Must contain topic name in log param", nil)
	}
	if functions.Runtime == "java8" || functions.Runtime == "java8_stream" {
		if err := models.CheckPtrIntSize(functions.MemorySize, 128, maxMemoryLimit); err != nil {
			return kunErr.NewInvalidParameterValueException("The memorySize must greater than 512M when the runtime is java", err)
		}
	}
	// create
	if vtype == "create" {
		if functions.FunctionName == "" || functions.Runtime == "" {
			return kunErr.NewInvalidParameterValueException("FunctionName Or Runtime Is NULL", nil)
		}
	}

	return nil
}

// SourceAlreadyUsed 同一个资源可能会有多个policy，需要过滤已读取过的资源并忽略无效的policy
func SourceAlreadyUsed(p *dao.Policy, sourceUsedMap map[string]bool) bool {
	var key string
	emptySourceTgr := false

	for _, tp := range api.TriggersWithoutSource {
		if p.PrincipalService == tp {
			emptySourceTgr = true
			break
		}
	}

	if emptySourceTgr {
		// 无效的policy，忽略它
		// 目前policy都是cfc自己创建、删除的，因此都是有效的
		if p.Source != "" || p.SourceAccount != "" || p.Action != api.CfcInvokeAction || p.Effect != api.PolicyActionAllow {
			return true
		}
		key = p.PrincipalService
	} else {
		key = p.Source
	}

	if _, used := sourceUsedMap[key]; used {
		return true
	}

	sourceUsedMap[key] = true
	return false
}

// TODO:将common.go中逻辑解耦，优化代码结构
// 返回一个err, 在上一层根据err类型决定response
func DeleteOne(function *dao.Function, c *apiServer.ApiServerContext, ctx *global.ApiserverContext) error {
	// 检查版本是否为$LATEST $LATEST 不允许删除
	if function.Version == "$LATEST" {
		return apiErr.NewResourceConflictException("$LATEST version cannot be deleted without deleting the function", nil)
	}

	var (
		aliasesTmp, funcsTmp interface{}
		err                  error
	)
	if function.Version != "" {
		// 单独删除某个版本
		// 检查version是否有别名指向，有则不能删除
		aliasesTmp, err = ctx.Observer.NewStage(global.ListAliasesStage).ObserveObject(dao.FindAlias(&dao.Alias{Uid: function.Uid, FunctionName: function.FunctionName, FunctionVersion: function.Version}))
		if err == nil {
			alias := aliasesTmp.(*[]dao.Alias)
			if len(*alias) > 0 {
				return apiErr.NewResourceConflictException("the function version has one or more aliases pointing to it", nil)
			}
		}

		// Version可能被灰度发布的版本所指向
		aliases, err := dao.FindAlias(&dao.Alias{Uid: function.Uid, FunctionName: function.FunctionName, AdditionalVersion: &function.Version})
		if err == nil {
			if len(*aliases) > 0 {
				return apiErr.NewResourceConflictException("the function version has one or more aliases pointing to it", nil)
			}
		}

		function.FunctionBrnInit()
		ctx.Context.FunctionBrn = function.FunctionBrn
	}

	// 检查resource是否存在
	// 这里列出了所有要删的版本及 latest
	funcsTmp, err = ctx.Observer.NewStage(global.ListFunctionsStage).ObserveObject(dao.FindFunc(function))
	funcs := funcsTmp.(*[]dao.Function)
	if err != nil || len(*funcs) == 0 {
		cause := fmt.Sprintf("[delete function Warning][f: %v][err: %v]", function, err)
		return apiErr.NewResourceNotFoundException(cause, nil)
	}

	// 如果本次删除的是单个版本，则需要传递Brn
	if len(*funcs) == 1 {
		function.FunctionBrn = (*funcs)[0].FunctionBrn
	}

	// 拿到函数相关的alias，准备删除触发器
	brnList := make([]string, 0)
	for _, f := range *funcs {
		alias, err := dao.FindAlias(&dao.Alias{Uid: f.Uid, FunctionName: f.FunctionName, FunctionVersion: f.Version})
		if err != nil {
			return kunErr.NewServiceException("find alias failed when delete function", err)
		}

		for _, a := range *alias {
			brnList = append(brnList, a.AliasBrn)
		}
		brnList = append(brnList, f.FunctionBrn)
		eventsourceMapList, err := dao.FindEventSource(&dao.FuncEventSource{Uid: f.Uid, FunctionBrn: f.FunctionBrn})
		if err != nil {
			return kunErr.NewServiceException("find event source mapping failed when delete function", err)
		}
		if eventsourceMapList != nil {
			for _, v := range *eventsourceMapList {
				service.DeleteOneEventSource(&v)
			}
		}

		// 如果该版本对应有预留实例，则删除
		r := &dao.FunctionReserved{Uid: f.Uid, FunctionBrn: f.FunctionBrn}
		if err = dao.DBFindFunctionReserved(r, false); err == nil && r.Id != 0 {
			if err = dao.DBDeleteFunctionReserved(r, false); err != nil {
				return kunErr.NewServiceException("delete function reserved failed", err)
			}
		}
	}

	c.DbTransaction = dbengine.DBTransaction()
	if err := ctx.Observer.NewStage(global.DeleteRelationsStage).Observe(DeleteRelations(c, brnList)); err != nil {
		c.DbTransaction.Rollback()
		return kunErr.NewServiceException("delete triggers failed", err)
	}

	// 添加删除该函数下的测试事件
	if err := ctx.Observer.NewStage(global.DeleteTestEventsStage).Observe(DeleteTestEvents(function)); err != nil {
		c.DbTransaction.Rollback()
		return kunErr.NewServiceException("delete testevents failed", err)
	}

	// 删除该函数的网络配置记录
	if err := ctx.Observer.NewStage(global.DeleteNetworkConfigStage).Observe(
		DeleteNetworkConfig(c.DbTransaction, function, len(*funcs) > 1)); err != nil {
		c.DbTransaction.Rollback()
		return kunErr.NewServiceException("delete network config failed", err)
	}

	if rkunErr := ctx.Observer.NewStage(global.DeleteFunctionStage).Observe(dao.DeleteFunc(c.DbTransaction, function)); rkunErr != nil {
		c.DbTransaction.Rollback()
		return rkunErr
	}
	if err := c.DbTransaction.Commit().Error; err != nil {
		return kunErr.NewServiceException("delete function failed because of commit failure", err)
	}

	// 初始化通知etcd msg
	fcMsg := api.NewFunctionCacheNotifyMsg()
	for _, v := range *funcs {
		fcMsg.AddInfo(v.FunctionBrn, v.Version, api.FunctionCacheMethodDelete)
	}
	dbengine.NotifyEtcdWhenChange(api.FunctionCacheEtcdPrefix, function.FunctionName, fcMsg)

	return nil
}

// 删除函数前删除它的触发器
func DeleteRelations(c *apiServer.ApiServerContext, brnList []string) error {
	var globalErr error
	for _, b := range brnList {
		if globalErr != nil {
			return globalErr
		}

		plcys, err := dao.ListPolicies(&dao.Policy{
			Resource: b,
		})
		if err != nil {
			return err
		}

		sourceUsedMap := map[string]bool{}
		var wg sync.WaitGroup

		for _, plcy := range *plcys {
			if SourceAlreadyUsed(&plcy, sourceUsedMap) {
				continue
			}

			p := api.PrincipalServiceType(plcy.PrincipalService)
			tgr := trigger.GetTriggerInterfaceFromService(p)

			wg.Add(1)
			go func(tgr trigger.TriggerInterface, plcy dao.Policy) {
				defer wg.Done()
				rlats, err := tgr.GetRelation(&c.Context, &plcy)
				if err != nil {
					globalErr = err
				}

				if len(rlats) == 0 {
					return
				}

				if _, err := tgr.DeleteRelation(c, rlats...); err != nil {
					globalErr = err
				}
			}(tgr, plcy)
		}
		wg.Wait()
	}
	return globalErr
}

func OTESimpleCheck(c *server.Context) error {
	user, err := GetUser(c.Request())
	if err != nil {
		return apiErr.NewUserNotFoundException(err.Error(), nil)
	}
	c.Logger().V(9).Infof("user token(%+v) Domain (%+v)", user, user.Domain)

	var realUser string
	realUser = user.Domain.ID
	if realUser == "default" || realUser == "Default" {
		realUser = user.ID
	}
	whiteListInfo, ok := global.AC.Config.SourceWhiteList.Get(realUser)
	if !ok || whiteListInfo.Source != whitelist.WhiteListSourceOTE {
		return kunErr.NewUnrecognizedClientException("no whitelist permission", nil)
	}
	c.Logger().V(9).Infof("hit white list %s %s", whiteListInfo.UserId, whiteListInfo.Source)
	return nil
}

func OTECheck(c *server.Context) error {
	err := OTESimpleCheck(c)
	if err != nil {
		return err
	}
	cfcBrn, err := brn.Parse(c.Request().PathParameter("FunctionName"))
	if err != nil {
		return kunErr.NewInvalidParameterValueException("brn error", nil)
	}

	accid := c.Request().HeaderParameter(api.HeaderXAccountID)
	if len(accid) == 0 {
		return kunErr.NewInvalidParameterValueException("account id not provided", nil)
	}
	if brn.Md5BceUid(accid) != cfcBrn.AccountID {
		return kunErr.NewUnrecognizedClientException("the function not belong to this user", nil)
	}
	return nil
}

func DeleteTestEvents(function *dao.Function) error {
	// 获取函数绑定的testevents
	testevents := make([]dao.UserTestEvent, 0)
	cond := &dao.UserTestEvent{
		Uid:          function.Uid,
		FunctionName: function.FunctionName,
	}

	testevents, err := dao.GetUserEvent(cond)
	if err != nil {
		return err
	}

	// 删除函数对应的测试事件
	var failList []interface{}
	if len(testevents) > 0 {
		for _, event := range testevents {
			delCond := &dao.UserTestEvent{
				Uuid: event.Uuid,
			}
			err := dao.DeleteUserEvent(delCond)
			if err != nil {
				failList = append(failList, event)
			}
		}
	}

	if len(failList) > 0 {
		cause := fmt.Sprintf("[delete user test event failed][failEvents: %+v]", failList)
		return kunErr.NewServiceException(cause, nil)
	}

	return nil
}

// DeleteNetworkConfig
// 删除函数的情况：
// 1、只删一个非 latest 的版本。此时按照版本的 brn 删除对应的 vpc 配置
// 2、删除整个函数，此时函数可能存在一个latest+版本，也可能只有 latest。此时按照函数名字删除所有版本的 vpc 配置
func DeleteNetworkConfig(tx *gorm.DB, function *dao.Function, deleteAllVersions bool) error {
	cond := &dao.NetworkConfig{}
	cond.Uid = function.Uid
	if deleteAllVersions {
		cond.FunctionName = function.FunctionName
	} else {
		cond.Brn = function.FunctionBrn
	}

	return dao.DeleteNetworkConfig(tx, cond)
}

// 判断函数是否存在
func IsFunctionExist(uid, funcName string) error {
	findFunc := &dao.Function{
		Uid: uid,
	}
	findFunc.FunctionName = funcName
	if rKunErr := dao.FindOneFunc(findFunc); rKunErr != nil {
		cause := fmt.Sprintf("[function not found]")
		err := apiErr.NewResourceNotFoundException(cause, nil)
		return err
	}
	return nil
}

// 判断函数BRN是否存在
func IsFunctionBrnExist(functionBrn string) error {
	findFunc := &dao.Function{
		FunctionBrn: functionBrn,
	}
	if rKunErr := dao.FindOneFunc(findFunc); rKunErr != nil {
		cause := fmt.Sprintf("[functionBrn not found]")
		err := apiErr.NewResourceNotFoundException(cause, nil)
		return err
	}
	return nil
}

// 判断别名是否存在
func IsAliasExist(aliasBrn string) error {
	findAlias := &dao.Alias{
		AliasBrn: aliasBrn,
	}
	if rKunErr := dao.FindOneAlias(findAlias); rKunErr != nil {
		cause := fmt.Sprintf("[alias not found]")
		err := apiErr.NewResourceNotFoundException(cause, nil)
		return err
	}
	return nil
}

func CheckRuntime(runtime string) error {
	rc := &dao.RuntimeConfig{
		RuntimeConfiguration: api.RuntimeConfiguration{
			Name: runtime,
		},
	}

	if err := dao.FindRuntimeConf(rc); err != nil {
		return apiErr.NewResourceNotFoundException("runtime not found", nil)
	}

	if time.Now().After(rc.DeprecatedAt) {
		return kunErr.NewInvalidParameterValueException("the runtime is no longer supported, please try new version", nil)
	}

	return nil
}

func CheckAcl(environment *api.Environment) error {
	if environment == nil {
		return nil
	}
	for k, v := range environment.Variables {
		// 环境变量里面设置了高级权限策略
		if k == "ACL" {
			acl := &api.Acl{}
			if err := json.Unmarshal([]byte(v), &acl); err != nil {
				return kunErr.NewInvalidRequestContentException("please check your acl config", err)
			}
			if acl.ID == "" || len(acl.AccessControlList) == 0 {
				return kunErr.NewInvalidRequestContentException("please check your acl config", nil)
			}
			for _, accessControl := range acl.AccessControlList {
				if accessControl.Service == "" || accessControl.Region == "" || accessControl.Effect == "" ||
					len(accessControl.Resource) == 0 || len(accessControl.Permission) == 0 {
					return kunErr.NewInvalidRequestContentException("please check your acl config", nil)
				}
			}
		}
	}
	return nil
}
