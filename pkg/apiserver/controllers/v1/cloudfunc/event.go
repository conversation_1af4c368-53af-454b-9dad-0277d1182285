package cloudfunc

import (
	"encoding/json"
	"fmt"
	"regexp"

	"github.com/emicklei/go-restful"
	"github.com/google/uuid"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/dao"
	apiErr "icode.baidu.com/baidu/faas/kun/pkg/apiserver/error"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/global"
	kunErr "icode.baidu.com/baidu/faas/kun/pkg/error"
	"icode.baidu.com/baidu/faas/kun/pkg/util/bytefmt"
)

const (
	maxTestEvent  = 10
	onlineStatus  = 1
	offlineStatus = 2
	testEventSize = 25
)

var (
	regLetterAndDigit    = regexp.MustCompile(`^[a-z0-9A-Z-_]+$`)
	DefaultEventLimit, _ = bytefmt.ToBytes("6MB")
)

func CheckTestEvent(event *dao.UserTestEvent) error {
	if event.FunctionName == "" {
		return fmt.Errorf("FunctionName is empty")
	}

	if event.Title == "" || !(regLetterAndDigit.MatchString(event.Title) && len(event.Title) <= testEventSize) {
		return fmt.Errorf("title is invalid")
	}

	if err := CheckEvent(event.Event); err != nil {
		return err
	}

	return nil
}

//判断Event是否为json字符串
func CheckEvent(event string) error {
	if len(event) > int(DefaultEventLimit) {
		return fmt.Errorf("event length invalid")
	}
	emptyObj := make(map[string]interface{})
	if err := json.Unmarshal([]byte(event), &emptyObj); err != nil {
		return fmt.Errorf("event invalid")
	}
	return nil
}

func CreateNewUserEvent(request *restful.Request, event *dao.UserTestEvent, ctx *global.ApiserverContext) (err error) {
	//参数验证
	if rKunErr := CheckTestEvent(event); rKunErr != nil {
		cause := fmt.Sprintf("[params invalid][user event: %v],[err :%v]", event, rKunErr)
		err = kunErr.NewInvalidParameterValueException(cause, rKunErr)
		return err

	}
	//验证用户是否存在
	user, err := GetUser(request)
	if err != nil {
		cause := fmt.Sprintf("[user not found][user event: %v]", event)
		err = apiErr.NewResourceNotFoundException(cause, nil)
		return
	}
	//查看函数是否存在
	if rKunErr := ctx.Observer.NewStage(global.CheckFunctionStage).Observe(IsFunctionExist(user.Domain.ID, event.FunctionName)); rKunErr != nil {
		return rKunErr
	}

	//查找同一个函数下是否有同名的测试事件
	findEvent := &dao.UserTestEvent{
		Uid:          user.Domain.ID,
		FunctionName: event.FunctionName,
		Title:        event.Title,
	}
	if rKunErr := ctx.Observer.NewStage(global.CheckTesteventStage).Observe(FindUserTestEvent(findEvent)); rKunErr != nil {
		return rKunErr
	}

	//查看同一个函数下测试事件数是否超限
	var (
		eventCountTmp  interface{}
		userEventCount int
	)
	if eventCountTmp, err = ctx.Observer.NewStage(global.CountUserTesteventStage).ObserveObject(GetUserTestEventCount(event, user.Domain.ID)); err != nil {
		ctx.Logger.Errorf("query the count of user testevents failed, event: %+v", event)
	}
	userEventCount = eventCountTmp.(int)
	if userEventCount >= maxTestEvent {
		cause := fmt.Sprintf("[user test event limit exceeded][user event: %v]", event)
		err = apiErr.NewTestEventStorageExceededException(cause, nil)
		return err
	}

	//添加测试事件
	event.Uid = user.Domain.ID
	event.Uuid = uuid.New().String()
	err = ctx.Observer.NewStage(global.QueryDatabaseStage).Observe(dao.CreateUserEvent(event))

	return err
}

func GetUserTestEventCount(e *dao.UserTestEvent, uid string) (total int, err error) {
	cond := &dao.UserTestEvent{
		Uid:          uid,
		FunctionName: e.FunctionName,
	}

	total, err = dao.GetUserEventCount(cond)
	return
}

func GetUserTestEvent(request *restful.Request, req *dao.UserTestEvent, ctx *global.ApiserverContext) ([]dao.UserTestEvent, error) {
	//参数验证
	if req.FunctionName == "" {
		cause := fmt.Sprintf("[params invalid, functionName is empty][user event: %v]", req)
		err := kunErr.NewInvalidParameterValueException(cause, nil)
		return nil, err

	}
	//验证用户是否存在
	user, err := GetUser(request)
	if err != nil {
		cause := fmt.Sprintf("[user not found][user event: %v]", user)
		err = apiErr.NewResourceNotFoundException(cause, nil)
		return nil, err
	}
	//查看函数是否存在
	if rKunErr := ctx.Observer.NewStage(global.CheckFunctionStage).Observe(IsFunctionExist(user.Domain.ID, req.FunctionName)); rKunErr != nil {
		return nil, rKunErr
	}

	cond := &dao.UserTestEvent{
		Uid:          user.Domain.ID,
		FunctionName: req.FunctionName,
	}

	userEvents := make([]dao.UserTestEvent, 0)
	var (
		userEventsTmp interface{}
	)
	userEventsTmp, err = ctx.Observer.NewStage(global.QueryDatabaseStage).ObserveObject(dao.GetUserEvent(cond))
	userEvents = userEventsTmp.([]dao.UserTestEvent)
	return userEvents, err
}

func GetOneUserTestEvent(request *restful.Request, e *dao.UserTestEvent, ctx *global.ApiserverContext) (*dao.UserTestEvent, error) {
	//参数验证
	if e.Uuid == "" {
		cause := fmt.Sprintf("[params invalid, uuid is empty][user event: %v]", e)
		err := kunErr.NewInvalidParameterValueException(cause, nil)
		return nil, err

	}
	//验证用户是否存在
	user, err := GetUser(request)
	if err != nil {
		cause := fmt.Sprintf("[user not found][user event: %v]", user)
		err = apiErr.NewResourceNotFoundException(cause, nil)
		return nil, err
	}
	// 查看用户必须为登录用户
	e.Uid = user.Domain.ID
	err = ctx.Observer.NewStage(global.QueryDatabaseStage).Observe(dao.GetOneUserEvent(e))
	return e, err
}

func UpdateUserTestEvent(request *restful.Request, cond *dao.UserTestEvent, updateEvent *dao.UserTestEvent, ctx *global.ApiserverContext) error {
	//参数验证
	if cond.Uuid == "" {
		cause := fmt.Sprintf("[params invalid, uuid is empty][user event: %v]", cond)
		err := kunErr.NewInvalidParameterValueException(cause, nil)
		return err

	}
	if rKunErr := CheckEvent(updateEvent.Event); rKunErr != nil {
		cause := fmt.Sprintf("[params invalid][user event: %v] [err: %v]", cond, rKunErr)
		err := kunErr.NewInvalidParameterValueException(cause, rKunErr)
		return err
	}

	//验证用户是否存在
	user, err := GetUser(request)
	if err != nil {
		cause := fmt.Sprintf("[user not found][user event: %v]", user)
		err = apiErr.NewResourceNotFoundException(cause, nil)
		return err
	}

	//查看用户测试事件是否存在
	findEvent := &dao.UserTestEvent{
		Uuid: cond.Uuid,
		Uid:  user.Domain.ID,
	}
	if rKerr := ctx.Observer.NewStage(global.CheckTesteventStage).Observe(dao.GetOneUserEvent(findEvent)); rKerr != nil {
		cause := fmt.Sprintf("[user event not find][user event: %v]", findEvent)
		err := apiErr.NewResourceNotFoundException(cause, nil)
		return err
	}

	err = ctx.Observer.NewStage(global.QueryDatabaseStage).Observe(dao.UpdateUserEvent(*cond, updateEvent))
	return err
}

func FindUserTestEvent(e *dao.UserTestEvent) error {
	rKunErr := dao.IsUserEventExist(e)
	if rKunErr == nil && e.ID != 0 {
		cause := fmt.Sprintf("[user test event exists][user event: %v]", e)
		err := apiErr.NewTestEventConflictException(cause, nil)
		return err
	}
	return nil
}

func DeleteUserTestEvent(request *restful.Request, uuid string, ctx *global.ApiserverContext) error {
	//参数验证
	if uuid == "" {
		cause := fmt.Sprintf("[params invalid, uuid is empty]")
		err := kunErr.NewInvalidParameterValueException(cause, nil)
		return err

	}
	//验证用户是否存在
	user, err := GetUser(request)
	if err != nil {
		cause := fmt.Sprintf("[user not found][user event: %v]", user)
		err = apiErr.NewResourceNotFoundException(cause, nil)
		return err
	}

	//查看用户测试事件是否存在
	findEvent := &dao.UserTestEvent{
		Uuid: uuid,
		Uid:  user.Domain.ID,
	}
	if rKerr := ctx.Observer.NewStage(global.CheckTesteventStage).Observe(dao.GetOneUserEvent(findEvent)); rKerr != nil {
		cause := fmt.Sprintf("[user event not find][user event: %v]", findEvent)
		err := apiErr.NewResourceNotFoundException(cause, nil)
		return err
	}

	cond := new(dao.UserTestEvent)
	cond.Uuid = uuid

	err = ctx.Observer.NewStage(global.QueryDatabaseStage).Observe(dao.DeleteUserEvent(cond))
	return err
}

func GetStandardTestEvent(req *dao.StandardTestEvent) ([]dao.StandardTestEvent, error) {
	//参数验证
	if req.Status != offlineStatus && req.Status != onlineStatus {
		cause := fmt.Sprintf("[params invalid, status invalid][standard event: %v]", req)
		err := kunErr.NewInvalidParameterValueException(cause, nil)
		return nil, err
	}

	standardEvents := make([]dao.StandardTestEvent, 0)
	standardEvents, err := dao.GetStandardEvent(req)

	return standardEvents, err
}

func GetOneStandardTestEvent(e *dao.StandardTestEvent) (*dao.StandardTestEvent, error) {
	//参数验证
	if e.ID <= 0 {
		cause := fmt.Sprintf("[params invalid, ID invalid][standard event: %v]", e)
		err := kunErr.NewInvalidParameterValueException(cause, nil)
		return nil, err
	}

	err := dao.GetOneStandardEvent(e)

	return e, err
}
