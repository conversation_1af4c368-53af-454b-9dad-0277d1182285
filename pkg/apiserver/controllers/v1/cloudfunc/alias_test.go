package cloudfunc

import (
	"testing"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/stretchr/testify/assert"

	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/dbengine"
	apiErr "icode.baidu.com/baidu/faas/kun/pkg/apiserver/error"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/global"
	apiServer "icode.baidu.com/baidu/faas/kun/pkg/apiserver/server"
	"icode.baidu.com/baidu/faas/kun/pkg/server"
)

func TestCreateAlias2(t *testing.T) {
	global.MockAC()
	cases := []struct {
		in_c         *server.Context
		out_HttpCode int
	}{
		{
			//FunctionVersion错误
			in_c:         global.BuildNewKunCtx("POST", "/v1/functions/myfunc/versions", `{"Name":"cccc","FunctionVersion":"ccc"}`, "uiduid", map[string]string{}),
			out_HttpCode: 400,
		},
		{
			//Name错误
			in_c:         global.BuildNewKunCtx("POST", "/v1/functions/myfunc/versions", `{"Name":"1111","FunctionVersion":"11"}`, "uiduid", map[string]string{}),
			out_HttpCode: 400,
		},
	}
	for _, tc := range cases {
		var m sqlmock.Sqlmock
		m, dbengine.Engine = global.MockDB()
		m.ExpectQuery(".*").WillReturnRows(global.GetRowsFunctions(global.GetTestFunc(1)))

		f := AliasResource{}
		f.create(tc.in_c)
		assert.Equal(t, tc.out_HttpCode, tc.in_c.Response().StatusCode())
	}
}

func TestCreateAlias(t *testing.T) {
	global.MockAC()
	var m sqlmock.Sqlmock
	m, dbengine.Engine = global.MockDB()
	cases := []struct {
		in_c         *server.Context
		out_HttpCode int
		sqlFunc      func()
	}{
		{
			in_c:         global.BuildNewKunCtx("POST", "/v1/functions/myfunc/aliases", "err Json BODY", "uiduid", map[string]string{}),
			out_HttpCode: 400,
		},
		{
			in_c:         global.BuildNewKunCtx("POST", "/v1/functions/myfunc/aliases", "{}", "", map[string]string{}),
			out_HttpCode: 400,
		},
		{
			in_c:         global.BuildNewKunCtx("POST", "/v1/functions/myfunc/aliases", "{}", "uiduid", map[string]string{}),
			out_HttpCode: 400,
		},
		{
			in_c:         global.BuildNewKunCtx("POST", "/v1/functions/myfunc/aliases", `{"Name":"cccc","FunctionVersion":"11"}`, "uiduid", map[string]string{}),
			out_HttpCode: 201,
			sqlFunc: func() {
				m.ExpectQuery(".*").WillReturnRows(global.GetRowsFunctions(global.GetTestFunc(1)))
				m.ExpectExec(".*").WillReturnResult(sqlmock.NewResult(1, 1))
			},
		},
		{
			in_c:         global.BuildNewKunCtx("POST", "/v1/functions/myfunc/aliase", `{"Name":"test","FunctionVersion":"12","AdditionalVersionWeights":{"1":0.12}}`, "uiduid", map[string]string{}),
			out_HttpCode: 201,
			sqlFunc: func() {
				m.ExpectQuery(".*").WillReturnRows(global.GetRowsFunctions(global.GetTestFunc(1)))
				m.ExpectQuery(".*").WillReturnRows(global.GetRowsFunctions(global.GetTestFunc(1)))
				m.ExpectExec(".*").WillReturnResult(sqlmock.NewResult(1, 1))
			},
		},
	}
	for _, tc := range cases {
		//ms, mcMap := global.GetMetric()
		f := AliasResource{}
		if tc.sqlFunc != nil {
			tc.sqlFunc()
		}
		f.create(tc.in_c)
		assert.Equal(t, tc.out_HttpCode, tc.in_c.Response().StatusCode())
	}
}

func TestUpdateAlias(t *testing.T) {
	global.MockAC()

	cases := []struct {
		in_c         *server.Context
		out_HttpCode int
		sqlFunc      func()
	}{
		{
			in_c:         global.BuildNewKunCtx("PUT", "/v1/functions/myfunc/aliases", "err Json BODY", "uiduid", map[string]string{}),
			out_HttpCode: 400,
			sqlFunc: func() {
				var m sqlmock.Sqlmock
				m, dbengine.Engine = global.MockDB()
				m.ExpectQuery("SELECT").WillReturnRows(global.GetRowsAliases(global.GetTestAlias(1)))
				m.ExpectExec(".*").WillReturnResult(sqlmock.NewResult(1, 1))
			},
		},
		{
			in_c:         global.BuildNewKunCtx("PUT", "/v1/functions/myfunc/aliases", "{}", "", map[string]string{}),
			out_HttpCode: 400,
			sqlFunc: func() {
				var m sqlmock.Sqlmock
				m, dbengine.Engine = global.MockDB()
				m.ExpectQuery("SELECT").WillReturnRows(global.GetRowsAliases(global.GetTestAlias(1)))
				m.ExpectExec(".*").WillReturnResult(sqlmock.NewResult(1, 1))
			},
		},
		{
			in_c:         global.BuildNewKunCtx("PUT", "/v1/functions/myfunc/aliases/ccc", `{"FunctionVersion":"1"}`, "uiduid", map[string]string{}),
			out_HttpCode: 200,
			sqlFunc: func() {
				var m sqlmock.Sqlmock
				m, dbengine.Engine = global.MockDB()
				m.ExpectQuery(".*").WillReturnRows(global.GetRowsFunctions(global.GetTestFunc(1)))
				m.ExpectQuery("SELECT").WillReturnRows(global.GetRowsAliases(global.GetTestAlias(1)))
				m.ExpectExec(".*").WillReturnResult(sqlmock.NewResult(1, 1))
				m.ExpectQuery(".*").WillReturnRows(global.GetRowsAliases(global.GetTestAlias(1)))
				m.ExpectQuery(".*").WillReturnRows(global.GetRowsFunctions(global.GetTestFunc(1)))
			},
		},
		{
			in_c:         global.BuildNewKunCtx("PUT", "/v1/functions/myfunc/aliases/ccc", `{"FunctionVersion":"11"}`, "uiduid", map[string]string{}),
			out_HttpCode: 404,
			sqlFunc: func() {
				var m sqlmock.Sqlmock
				m, dbengine.Engine = global.MockDB()
				m.ExpectQuery("SELECT").WillReturnRows(global.GetRowsAliases(global.GetTestAlias(1)))
				m.ExpectExec(".*").WillReturnResult(sqlmock.NewResult(1, 1))
			},
		},
		{
			in_c:         global.BuildNewKunCtx("PUT", "/v1/functions/myfunc/aliases", `{"FunctionVersion":"11","AdditionalVersionWeights":{"1":0.1}}`, "uiduid", map[string]string{}),
			out_HttpCode: 200,
			sqlFunc: func() {
				var m sqlmock.Sqlmock
				m, dbengine.Engine = global.MockDB()
				m.ExpectQuery(".*").WillReturnRows(global.GetRowsFunctions(global.GetTestFunc(1)))
				m.ExpectQuery(".*").WillReturnRows(global.GetRowsFunctions(global.GetTestFunc(1)))
				m.ExpectQuery("SELECT").WillReturnRows(global.GetRowsAliases(global.GetTestAlias(1)))
				m.ExpectExec(".*").WillReturnResult(sqlmock.NewResult(1, 1))
				m.ExpectQuery(".*").WillReturnRows(global.GetRowsAliases(global.GetTestAlias(1)))
				m.ExpectQuery(".*").WillReturnRows(global.GetRowsFunctions(global.GetTestFunc(1)))
			},
		},
	}
	for _, tc := range cases {

		f := AliasResource{}
		if tc.sqlFunc != nil {
			tc.sqlFunc()
		}
		f.update(tc.in_c)
		assert.Equal(t, tc.out_HttpCode, tc.in_c.Response().StatusCode())
	}
}
func TestDelAlias(t *testing.T) {
	global.MockAC()
	cases := []struct {
		in_c         *apiServer.ApiServerContext
		out_HttpCode int
	}{
		{
			in_c:         global.BuildNewApiServerCtx("PUT", "/v1/functions/myfunc/aliases/ccc", "{}", "", map[string]string{}),
			out_HttpCode: 400,
		},
	}
	for _, tc := range cases {
		var m sqlmock.Sqlmock
		m, dbengine.Engine = global.MockDB()

		m.ExpectQuery(".*").WillReturnRows(global.GetRowsFunctions(global.GetTestFunc(1)))
		m.ExpectExec(".*").WillReturnResult(sqlmock.NewResult(1, 1))
		f := AliasResource{}
		f.delete(tc.in_c)
		assert.Equal(t, tc.out_HttpCode, tc.in_c.Response().StatusCode())
	}
}
func TestFindAlias(t *testing.T) {
	global.MockAC()

	cases := []struct {
		in_c         *server.Context
		out_HttpCode int
	}{
		{
			in_c:         global.BuildNewKunCtx("GET", "/v1/functions/myfunc/aliases/ccc", "{}", "", map[string]string{}),
			out_HttpCode: 400,
		},
		{
			in_c:         global.BuildNewKunCtx("PUT", "/v1/functions/myfunc/aliases/ccc", "", "uiduid", map[string]string{}),
			out_HttpCode: 200,
		},
	}
	for _, tc := range cases {
		var m sqlmock.Sqlmock
		m, dbengine.Engine = global.MockDB()
		m.ExpectQuery(".*").WillReturnRows(global.GetRowsFunctions(global.GetTestFunc(1)))
		f := AliasResource{}
		f.find(tc.in_c)
		assert.Equal(t, tc.out_HttpCode, tc.in_c.Response().StatusCode())
	}
}

func TestFindAliasByCondition(t *testing.T) {
	global.MockAC()

	cases := []struct {
		in_c         *server.Context
		out_HttpCode int
	}{
		{
			in_c:         global.BuildNewKunCtx("GET", "/v1/functions/myfunc/aliases", "{}", "", map[string]string{}),
			out_HttpCode: 400,
		},
		{
			in_c:         global.BuildNewKunCtx("PUT", "/v1/functions/myfunc/aliases?Marker=a", "", "uiduid", map[string]string{}),
			out_HttpCode: 400,
		},
		{
			in_c:         global.BuildNewKunCtx("PUT", "/v1/functions/myfunc/aliases", "", "uiduid", map[string]string{}),
			out_HttpCode: 200,
		},
	}
	for _, tc := range cases {
		var m sqlmock.Sqlmock
		m, dbengine.Engine = global.MockDB()
		m.ExpectQuery(".*").WillReturnRows(global.GetRowsFunctions(global.GetTestFunc(2)))
		m.ExpectQuery(".*").WillReturnRows(global.GetRowsFunctions(global.GetTestFunc(1)))
		f := AliasResource{}
		f.findByCondition(tc.in_c)
		assert.Equal(t, tc.out_HttpCode, tc.in_c.Response().StatusCode())
	}
}

func TestInsideFind1(t *testing.T) {
	global.MockAC()
	cases := []struct {
		in_c         *server.Context
		out_HttpCode int
		sqlFunc      func()
	}{
		{
			in_c:         global.BuildNewKunCtx("GET", "/v1/functions/aliases/functionbrn", "", "uiduid", map[string]string{}),
			out_HttpCode: 200,
			sqlFunc: func() {
				var m sqlmock.Sqlmock
				m, dbengine.Engine = global.MockDB()
				m.ExpectQuery(".*").WillReturnRows(global.GetRowsAliases(global.GetTestAlias(2)))
			},
		},
	}

	for _, tc := range cases {
		f := AliasResource{}
		if tc.sqlFunc != nil {
			tc.sqlFunc()
		}
		f.insideFind(tc.in_c)
		assert.Equal(t, tc.out_HttpCode, tc.in_c.Response().StatusCode())
	}
}

func TestValidateRequest(t *testing.T) {
	cases := []struct {
		in_c aliasRequest
		err  error
	}{
		{
			in_c: aliasRequest{
				AdditionalVersionWeights: map[string]float64{
					"1": 0.1,
				},
			},
			err: nil,
		},

		// weight非法的情况
		{
			in_c: aliasRequest{
				AdditionalVersionWeights: map[string]float64{
					"1": 1.1,
				},
			},
			err: apiErr.NewInvalidRequestException("Invalid weight found", "The weight is greater than 1 or less than 0", nil),
		},
		{
			in_c: aliasRequest{
				AdditionalVersionWeights: map[string]float64{
					"1": -0.5,
				},
			},
			err: apiErr.NewInvalidRequestException("Invalid weight found", "The weight is greater than 1 or less than 0", nil),
		},
		{
			in_c: aliasRequest{
				AdditionalVersionWeights: map[string]float64{
					"1": 0.5,
					"2": 0.4,
				},
			},
			err: apiErr.NewTooManyParametersException("There are more than one canary version", nil),
		},
		{
			in_c: aliasRequest{
				AdditionalVersionWeights: map[string]float64{
					"$LATEST": 0.1,
				},
			},
			err: apiErr.NewInvalidRequestException("Invalid Version found", "One or more Version  pointer to $LATEST", nil),
		},
	}

	for _, tc := range cases {
		_, _, res := validateRequest(tc.in_c)
		assert.Equal(t, res, tc.err)
	}
}
