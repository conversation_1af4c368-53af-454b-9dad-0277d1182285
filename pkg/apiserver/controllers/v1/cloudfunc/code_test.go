package cloudfunc

import (
	"errors"
	"testing"

	"icode.baidu.com/baidu/faas/kun/pkg/util/logs"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/stretchr/testify/assert"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/code"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/dbengine"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/global"
	"icode.baidu.com/baidu/faas/kun/pkg/server"
)

var mockFolders = []code.CommonPrefix{
	{Prefix: "c7ac82ae14ef42d1a4ffa3b2ececa17f/"},
	{Prefix: "61ac159868074c47b7364716296dc029/"},
	{Prefix: ".trash/"},
	{Prefix: "blueprints/"},
}

func TestCodeAPIs(t *testing.T) {
	CodeRest{}.InsideAPIs()
}

func TestSweepCode(t *testing.T) {
	global.MockAC()
	code.MockCode()
	global.RegisterFaasRuntimeGovalidatorTag()
	var m sqlmock.Sqlmock
	m, dbengine.Engine = global.MockDB()

	m.ExpectQuery(".*").WillReturnRows(global.GetRowsFunctions(global.GetTestFunc(2)))
	m.ExpectQuery(".*").WillReturnRows(global.GetRowsFunctions(global.GetTestFunc(2)))

	r := &Sweeper{
		Logger: logs.NewLogger(),
	}
	err := r.sweepCode(mockFolders)
	assert.Equal(t, err, nil)
	return
}

func TestVerifyResult(t *testing.T) {
	global.MockAC()
	code.MockCode()
	global.RegisterFaasRuntimeGovalidatorTag()
	var m sqlmock.Sqlmock
	m, dbengine.Engine = global.MockDB()

	m.ExpectQuery(".*").WillReturnRows(global.GetRowsFunctions(global.GetTestFunc(2)))
	m.ExpectQuery(".*").WillReturnRows(global.GetRowsFunctions(global.GetTestFunc(2)))

	r := &Sweeper{}
	r.verifyResult(mockFolders)
	return
}

func TestSweepTrash(t *testing.T) {
	global.MockAC()
	code.MockCode()
	var m sqlmock.Sqlmock
	m, dbengine.Engine = global.MockDB()

	m.ExpectQuery(".*").WillReturnRows(global.GetRowsFunctions(global.GetTestFunc(2)))
	m.ExpectQuery(".*").WillReturnRows(global.GetRowsFunctions(global.GetTestFunc(2)))

	r := &Sweeper{}
	err := r.sweepTrash()

	assert.Nil(t, err)
}

func TestRemoveCode(t *testing.T) {
	global.MockAC()
	code.MockCode()

	cases := []struct {
		in_c         *server.Context
		out_HttpCode int
	}{
		{
			in_c:         global.BuildNewKunCtx("GET", "/inside-v1/functions/codefile?trashExpiration=100", ``, "uiduid", map[string]string{}),
			out_HttpCode: 400,
		},
		{
			in_c:         global.BuildNewKunCtx("GET", "/inside-v1/functions/codefile?trashExpiration=100&&codeExpiration=100", ``, "uiduid", map[string]string{}),
			out_HttpCode: 200,
		},
	}
	for _, tc := range cases {
		f := CodeRest{}
		f.remove(tc.in_c)
		assert.Equal(t, tc.out_HttpCode, tc.in_c.Response().StatusCode())
	}
}

func TestIsUid(t *testing.T) {
	x := isUID("123")
	assert.Equal(t, false, x)

	x = isUID("c7ac82ae14ef42d1a4ffa3b2ececa17f")
	assert.Equal(t, true, x)
}

func TestSweepTrashFailed(t *testing.T) {
	global.MockAC()
	global.AC.Clients.Code = code.MockFaultyCode()
	r := &Sweeper{
		Logger: logs.NewLogger(),
	}
	err := r.sweepTrash()
	assert.NotNil(t, err)
}

func TestVeryOneUserFailed(t *testing.T) {
	var m sqlmock.Sqlmock
	m, dbengine.Engine = global.MockDB()
	code.MockFaultyCode()
	sp := &Sweeper{
		Logger: logs.NewLogger(),
	}

	cases := []struct {
		f func()
	}{
		{
			f: func() {
				m.ExpectQuery("SELECT").WillReturnError(errors.New("some error"))
			},
		},
		{
			f: func() {
				m.ExpectQuery("SELECT").WillReturnRows(global.GetRowsFunctions(global.GetTestFunc(1)))
			},
		},
	}
	for _, tc := range cases {
		tc.f()
		err := sp.verifyOneUser("uid")
		assert.NotNil(t, err)
	}
}
