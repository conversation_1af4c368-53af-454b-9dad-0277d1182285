package cloudfunc

import (
	"crypto/rand"
	"crypto/rsa"
	"crypto/x509"
	"encoding/pem"
	"fmt"
	"net/http"
	"strings"

	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/global"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/models"

	restful "github.com/emicklei/go-restful"
	"icode.baidu.com/baidu/faas/kun/cmd/apiserver/filter"
	"icode.baidu.com/baidu/faas/kun/pkg/api"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/dao"
	apiErr "icode.baidu.com/baidu/faas/kun/pkg/apiserver/error"
	"icode.baidu.com/baidu/faas/kun/pkg/console"
	kunErr "icode.baidu.com/baidu/faas/kun/pkg/error"
	"icode.baidu.com/baidu/faas/kun/pkg/server"
	"icode.baidu.com/baidu/faas/kun/pkg/server/endpoint"
	"icode.baidu.com/baidu/faas/kun/pkg/util/logs"
	"icode.baidu.com/baidu/faas/kun/pkg/util/zip"
)

type BlueprintRest struct {
	Path string
}

// PublicAPIs xxx
func (r BlueprintRest) PublicAPIs() []endpoint.ApiSingle {
	apis := []endpoint.ApiSingle{
		// list blueprints, GET /2015-03-31/blueprints
		endpoint.ApiSingle{
			Verb:    "GET",
			Path:    "/blueprints",
			Handler: server.WrapRestRouteFunc(r.list),
		},
		// get blueprints, GET /2015-03-31/blueprints/{UUID}
		endpoint.ApiSingle{
			Verb:    "GET",
			Path:    "/blueprints/{UUID}",
			Handler: server.WrapRestRouteFunc(r.find),
		},
		// create blueprintFunc, POST /2015-03-31/blueprints/{UUID}/function
		endpoint.ApiSingle{
			Verb:    "POST",
			Path:    "/blueprints/{UUID}/function",
			Handler: server.WrapRestRouteFunc(r.createBlueprintFunc),
			Filters: []restful.FilterFunction{
				filter.IAMPermissionCheck("*", "", []string{"cfc:CreateFunctions", filter.IamPermWrite}),
				filter.RealNameCheck,
				filter.BillingCheck,
				filter.CheckCFCWhiteList(),
			},
		},
	}
	return apis
}

func (r BlueprintRest) list(c *server.Context) {
	response := c.Response()
	ctx := global.BuildApiserverContext(c.Request(), c.Response(), "blueprints_list")
	bp := &dao.Blueprint{
		Status: api.BlueprintOnline,
	}

	var (
		bpsTmp interface{}
		err    error
	)
	if bpsTmp, err = ctx.Observer.NewStage(global.QueryDatabaseStage).ObserveObject(dao.ListBlueprints(bp)); err != nil {
		c.WithWarnLog(err).WriteTo(response)
	} else {
		bps := bpsTmp.(*[]dao.Blueprint)
		bpsRes := models.FormatBlueprints(bps)
		response.WriteHeaderAndEntity(http.StatusOK, bpsRes)
	}
	defer global.WriteSummary(ctx, api.LogNotToSummary)
}

func (r BlueprintRest) find(c *server.Context) {
	response := c.Response()

	ctx := global.BuildApiserverContext(c.Request(), c.Response(), "blueprints_find")
	ruuid := c.Request().PathParameter("UUID")
	logs.V(6).Infof("request_uuid=%s", ruuid)

	bp := new(dao.Blueprint)
	bp.Uuid = ruuid

	if rkunErr := ctx.Observer.NewStage(global.QueryDatabaseStage).Observe(dao.FindOneBlueprint(bp)); rkunErr != nil {
		c.WithWarnLog(rkunErr).WriteTo(response)
		return
	}

	bp.Layers = models.DealLayers(bp.LayersStr)
	defer global.WriteSummary(ctx, api.LogNotToSummary)
	response.WriteHeaderAndEntity(http.StatusOK, bp)
}

type CreateBlueprintFunctionReq struct {
	FunctionName string
	ServiceName  string
	Environment  *api.Environment
}

// 根据蓝图创建函数
func (r *BlueprintRest) createBlueprintFunc(c *server.Context) {
	response := c.Response()
	ctx := global.BuildApiserverContext(c.Request(), c.Response(), "createBlueprintFunc")
	var functionReq CreateBlueprintFunctionReq
	var (
		byteArrayTmp interface{}
		err          error
	)
	if err = c.Request().ReadEntity(&functionReq); err != nil {
		c.WithWarnLog(kunErr.NewInvalidRequestContentException("", err)).WriteTo(response)
		return
	}

	ruuid := c.Request().PathParameter("UUID")
	bp := new(dao.Blueprint)
	bp.Uuid = ruuid
	if rkunErr := ctx.Observer.NewStage(global.QueryDatabaseStage).Observe(dao.FindOneBlueprint(bp)); rkunErr != nil {
		c.WithWarnLog(apiErr.NewResourceNotFoundException("Create function failed, blueprint not found", rkunErr)).WriteTo(response)
		return
	}

	models.DealBlueprint(bp)
	fName := functionReq.FunctionName
	cFunc := dao.Function{
		BlueprintTag: bp.Name,
		FunctionConfig: api.FunctionConfig{
			FunctionName: fName,
			ServiceName:  functionReq.ServiceName,
			Runtime:      bp.Runtime,
			Handler:      bp.Handler,
			MemorySize:   bp.MemorySize,
		},
	}
	cFunc.Timeout = bp.Timeout

	// 添加蓝图默认的官方layer
	cFunc.LayerList = models.GetLayers(bp.LayersStr)

	if functionReq.Environment != nil {
		cFunc.Environment = functionReq.Environment
	} else {
		cFunc.Environment = &api.Environment{
			Variables: make(map[string]string),
		}
	}

	url := ctx.Observer.NewStage(global.GeneratePresignedUrlStage).ObserveString(global.AC.Clients.Code.GeneratePresignedUrl(bp.BosObjKey))

	byteArrayTmp, _ = ctx.Observer.NewStage(global.GetBytesFromBosStage).ObserveObject(global.AC.Clients.Code.FaasGetBytesFromCodeStorage(bp.BosObjKey))
	byteArray := byteArrayTmp.([]byte)
	if bp.CodeUpdateType == api.TypeDuerOSBotNodejs {
		if byteArrayTmp, err = ctx.Observer.NewStage(global.UpdateDuerOSCodeStage).ObserveObject(updateDuerOSCode(url, byteArray, bp)); err != nil {
			c.WithErrorLog(kunErr.NewServiceException("error happend when create rsa keys", err)).WriteTo(response)
			return
		}
		byteArray = byteArrayTmp.([]byte)
	}

	NewFunc := &dao.Function{}
	if NewFunc, err = CreateNewFunction(c, cFunc, byteArray, ctx); err != nil {
		c.WithErrorLog(err).WriteTo(response)
		return
	}
	// 蓝图部署数加1
	updateBlueprint := new(dao.Blueprint)
	condBlueprint := dao.Blueprint{
		Uuid: ruuid,
	}

	updateBlueprint.Uuid = ruuid
	deployments := *bp.Deployments + 1
	updateBlueprint.Deployments = &deployments
	if err = dao.UpdateBlueprint(condBlueprint, updateBlueprint); err != nil {
		c.Logger().Errorf("update blueprint deployments fail, err:%s", err.Error())
	}

	NewFunc.DealResFunction()
	defer global.WriteSummary(ctx, api.LogNotToSummary)
	response.WriteHeaderAndEntity(http.StatusCreated, NewFunc)
}

func updateDuerOSCode(url string, oldByteArray []byte, bp *dao.Blueprint) ([]byte, error) {
	pri, pub, err := createKeyPair()
	if err != nil {
		return nil, err
	}
	rawCode, _ := console.GetRawCode(url, bp.Handler, bp.Runtime)
	rawCode = `/*
* 如果需要使用监控统计功能，请将PUBLIC KEY 复制到DuerOS DBP平台
* 文档参考：https://dueros.baidu.com/didp/doc/dueros-bot-platform/dbp-deploy/authentication_markdown

` + pub + `
*/
` + rawCode

	files := make([]*map[string]string, 2)
	files[0] = &map[string]string{
		"fileName":  "index.js",
		"fileBytes": rawCode,
	}
	files[1] = &map[string]string{
		"fileName":  "rsaKeys.js",
		"fileBytes": fmt.Sprintf("module.exports.privateKey=`%s`\nmodule.exports.publicKey=`%s`", strings.TrimSpace(pri), strings.TrimSpace(pub)),
	}

	byteArray, err := zip.UpdateZipCode(oldByteArray, files)

	if err != nil {
		logs.Warnf("UpdateZipCode code failed, %v", err)
		return nil, err
	}
	return byteArray, nil
}

func createKeyPair() (pri, pub string, err error) {

	priKey, err := rsa.GenerateKey(rand.Reader, 1024)

	if err != nil {
		return "", "", err
	}

	pubKey := priKey.Public()

	bufPri := x509.MarshalPKCS1PrivateKey(priKey)
	priKeyPem := &pem.Block{
		Type:  "RSA PRIVATE KEY",
		Bytes: bufPri,
	}

	bufPub, err := x509.MarshalPKIXPublicKey(pubKey)
	if err != nil {
		return "", "", err
	}

	pubKeyPem := &pem.Block{
		Type:  "PUBLIC KEY",
		Bytes: bufPub,
	}

	return string(pem.EncodeToMemory(priKeyPem)), string(pem.EncodeToMemory(pubKeyPem)), nil
}
