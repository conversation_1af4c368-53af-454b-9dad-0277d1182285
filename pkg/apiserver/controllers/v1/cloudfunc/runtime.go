package cloudfunc

import (
	"icode.baidu.com/baidu/faas/kun/pkg/api"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/global"
	"net/http"

	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/dao"
	"icode.baidu.com/baidu/faas/kun/pkg/server"
)

type RuntimeRest struct {
}

func (r RuntimeRest) find(c *server.Context) {
	rName := c.Request().PathParameter("RuntimeName")
	RuntimeConfigRes := new(dao.RuntimeConfig)
	RuntimeConfigRes.Name = rName

	ctx := global.BuildApiserverContext(c.Request(), c.Response(), "find_runtime_conf")
	if rkunErr := ctx.Observer.NewStage(global.QueryDatabaseStage).Observe(dao.FindRuntimeConf(RuntimeConfigRes)); rkunErr != nil {
		c.WithWarnLog(rkunErr).WriteTo(c.Response())
		return
	}
	RuntimeConfigRes.DealRuntimeConfiguration()
	defer global.WriteSummary(ctx, api.LogNotToSummary)
	c.Response().WriteHeaderAndEntity(http.StatusOK, RuntimeConfigRes)
}
