package cloudfunc

import (
	"errors"
	"io/ioutil"
	"net/http"
	"strings"

	"github.com/asaskevich/govalidator"
	restful "github.com/emicklei/go-restful"

	"icode.baidu.com/baidu/faas/kun/cmd/apiserver/filter"
	"icode.baidu.com/baidu/faas/kun/pkg/api"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/global"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/models"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/service"
	bceBrn "icode.baidu.com/baidu/faas/kun/pkg/bce/brn"
	kunErr "icode.baidu.com/baidu/faas/kun/pkg/error"
	"icode.baidu.com/baidu/faas/kun/pkg/server"
	"icode.baidu.com/baidu/faas/kun/pkg/server/endpoint"
	"icode.baidu.com/baidu/faas/kun/pkg/util/base64"
)

type LayerRest struct{}

// PublicAPIs xxx
func (r LayerRest) PublicAPIs() []endpoint.ApiSingle {
	apis := []endpoint.ApiSingle{

		// PublishLayerVersion 发布版本
		endpoint.ApiSingle{
			Verb:    "POST",
			Path:    "/layers/{LayerName}/versions",
			Handler: server.WrapRestRouteFunc(r.publish),
			Filters: []restful.FilterFunction{
				filter.IAMPermissionCheck(api.Resource, "", []string{"cfc:PublishLayer", filter.IamPermWrite}),
			},
		},

		// ListLayerVersions 返回指定LayerName的所有版本
		// /layers/LayerName/versions?CompatibleRuntime=CompatibleRuntime&Marker=Marker&MaxItems=MaxItems HTTP/1.1
		endpoint.ApiSingle{
			Verb:    "GET",
			Path:    "/layers/{LayerName}/versions",
			Handler: server.WrapRestRouteFunc(r.listVersion),
			Filters: []restful.FilterFunction{
				filter.IAMPermissionCheck(api.Resource, "", []string{"cfc:PublishLayer", filter.IamPermWrite}),
			},
		},

		// GetLayerVersion 通过LayerName和版本获取详情
		// /layers/LayerName/versions/VersionNumber HTTP/1.1
		endpoint.ApiSingle{
			Verb:    "GET",
			Path:    "/layers/{LayerName}/versions/{VersionNumber}",
			Handler: server.WrapRestRouteFunc(r.find),
			Filters: []restful.FilterFunction{
				filter.IAMPermissionCheck(api.Resource, "", []string{"cfc:GetLayerVersion", filter.IamPermWrite}),
			},
		},

		// GET /v1/layers?find=LayerVersion&Brn=Brn 通过完整BRN获取Layer详情
		// GET /v1/layers?CompatibleRuntime=CompatibleRuntime&Marker=Marker&MaxItems=MaxItems 获取layer列表
		endpoint.ApiSingle{
			Verb:    "GET",
			Path:    "/layers",
			Handler: server.WrapRestRouteFunc(r.find),
			Filters: []restful.FilterFunction{
				filter.IAMPermissionCheck(api.Resource, "", []string{"cfc:GetLayer", filter.IamPermWrite}),
			},
		},

		// 删除Version
		endpoint.ApiSingle{
			Verb:    "DELETE",
			Path:    "/layers/{LayerName}/versions/{VersionNumber}",
			Handler: server.WrapRestRouteFunc(r.deleteVersion),
			Filters: []restful.FilterFunction{
				filter.IAMPermissionCheck(api.Resource, "", []string{"cfc:DeleteLayer", filter.IamPermWrite}),
				filter.BillingCheck,
				filter.CheckCFCWhiteList(),
			},
		},
		// 删除Layer
		endpoint.ApiSingle{
			Verb:    "DELETE",
			Path:    "/layers/{LayerName}",
			Handler: server.WrapRestRouteFunc(r.delete),
			Filters: []restful.FilterFunction{
				filter.IAMPermissionCheck(api.Resource, "", []string{"cfc:DeleteLayer", filter.IamPermWrite}),
				filter.BillingCheck,
				filter.CheckCFCWhiteList(),
			},
		},
	}
	return apis
}

func (r LayerRest) ConsoleApi() []endpoint.ApiSingle {
	apis := []endpoint.ApiSingle{

		// ListCfcVendedLayers 获取CFC官方Layer
		endpoint.ApiSingle{
			Verb:    "GET",
			Path:    "/console/vendedlayers",
			Handler: server.WrapRestRouteFunc(r.find),
			Filters: []restful.FilterFunction{
				filter.IAMAuthenticateCheck,
			},
		},

		// ListCfcVendedLayerVersion 获取CFC官方Layer版本
		endpoint.ApiSingle{
			Verb:    "GET",
			Path:    "/console/layers/{LayerName}/versions",
			Handler: server.WrapRestRouteFunc(r.listVersion),
			Filters: []restful.FilterFunction{
				filter.IAMAuthenticateCheck,
			},
		},
		// console 发布版本
		endpoint.ApiSingle{
			Verb:    "POST",
			Path:    "/console/layers/{LayerName}/versions",
			Handler: server.WrapRestRouteFunc(r.consolePublish),
			Filters: []restful.FilterFunction{
				filter.IAMAuthenticateCheck,
			},
		},
	}
	return apis
}

// 发布版本
func (layerRest *LayerRest) publish(c *server.Context) {

	response := c.Response()
	user, err := GetUser(c.Request())
	if err != nil {
		c.WithWarnLog(kunErr.NewInvalidParameterValueException("", err)).WriteTo(response)
		return
	}

	input := new(api.PublishLayerVersionInput)
	if err := c.Request().ReadEntity(input); err != nil {
		c.WithWarnLog(kunErr.NewInvalidRequestContentException("", err)).WriteTo(response)
		return
	}
	layerNameStr := c.Request().PathParameter("LayerName")
	layerName, err := bceBrn.GetLayerName(layerNameStr)
	if err != nil {
		c.WithWarnLog(kunErr.NewInvalidParameterValueException("", err)).WriteTo(response)
		return
	}
	input.LayerName = layerName

	// check args
	_, err = govalidator.ValidateStruct(input)
	if err != nil {
		c.WithWarnLog(kunErr.NewInvalidParameterValueException("", err)).WriteTo(response)
		return
	}

	err = service.ValidateCompatibleRuntimeList(input.CompatibleRuntimes)
	if err != nil {
		c.WithWarnLog(err).WriteTo(response)
		return
	}

	if len(input.Content.ZipFile) > 0 {
		input.Content.ZipFileBytes, _ = base64.DecodeString(input.Content.ZipFile)
	}

	// 仅允许cfc迁移中心来源的layer指定版本号
	if input.Version != 0 {
		if input.SourceTag != api.CFCMigrationSourceTag {
			c.WithWarnLog(kunErr.NewInvalidParameterValueException("", err)).WriteTo(response)
			return
		}
	}

	output, err := service.PublishLayer(user.Domain.ID, input, c)
	if err != nil {
		c.WithErrorLog(err).WriteTo(response)
		return
	}

	response.WriteHeaderAndEntity(http.StatusCreated, output)
}

// 1. /v1/layers?find=LayerVersion&Brn=Brn
// 2. /v1/layers?CompatibleRuntime=CompatibleRuntime&Marker=Marker&MaxItems=MaxItems
func (layerRest *LayerRest) find(c *server.Context) {
	response := c.Response()
	user, err := GetUser(c.Request())
	if err != nil {
		c.WithWarnLog(kunErr.NewInvalidParameterValueException("", err)).WriteTo(response)
		return
	}

	var (
		find             = c.Request().QueryParameter("find")
		brn              = c.Request().QueryParameter("Brn")
		layerNameStr     = c.Request().PathParameter("LayerName")
		versionNumberStr = c.Request().PathParameter("VersionNumber")
		getVendedLayers  = false
	)

	// GetLayerVersionByBrn 通过完整BRN获取Layer详情
	if find == "LayerVersion" {
		if brn == "" {
			err := errors.New("brn is invalid")
			c.WithWarnLog(kunErr.NewInvalidParameterValueException("", err)).WriteTo(response)
			return
		}
		layerBrn, err := bceBrn.ParseLayerBrn(brn)
		if err != nil {
			c.WithWarnLog(kunErr.NewInvalidParameterValueException("", err)).WriteTo(response)
			return
		}
		output, err := service.GetLayerVersionByBrn(user.Domain.ID, layerBrn)
		if err != nil {
			c.WithWarnLog(err).WriteTo(response)
			return
		}

		response.WriteHeaderAndEntity(http.StatusOK, output)
		return
	}

	// GetLayerVersion  通过版本号获取Layer详情
	if layerNameStr != "" && versionNumberStr != "" {
		layerName, err := bceBrn.GetLayerName(layerNameStr)
		if err != nil {
			c.WithWarnLog(kunErr.NewInvalidParameterValueException("", err)).WriteTo(response)
			return
		}
		versionNumber, err := govalidator.ToInt(versionNumberStr)
		if err != nil {
			c.WithWarnLog(kunErr.NewInvalidParameterValueException("VersionNumber validate", err)).WriteTo(response)
			return
		}
		brn := bceBrn.GenerateLayerVersionBrn(global.AC.Config.Region, user.Domain.ID, layerName, versionNumber)
		output, err := service.GetLayerVersionByBrn(user.Domain.ID, brn)
		if err != nil {
			c.WithWarnLog(err).WriteTo(response)
			return
		}

		response.WriteHeaderAndEntity(http.StatusOK, output)
		return
	}

	// GET /v1/layers?CompatibleRuntime=CompatibleRuntime&Marker=Marker&MaxItems=MaxItems 获取layer列表
	compatibleRuntime := c.Request().QueryParameter("CompatibleRuntime")
	err = service.ValidateCompatibleRuntime(compatibleRuntime)
	if err != nil {
		c.WithWarnLog(err).WriteTo(response)
		return
	}
	listCond, err := models.GetListConditionFromRequest(c.Request())
	if err != nil {
		c.WithWarnLog(kunErr.NewInvalidParameterValueException("", err)).WriteTo(response)
		return
	}

	input := &api.ListLayersInput{
		CompatibleRuntime: compatibleRuntime,
		ListCondition:     listCond,
	}

	if strings.HasPrefix(c.Request().Request.URL.Path, "/v1/console/vendedlayers") {
		getVendedLayers = true
	}

	output, err := service.ListLayers(user.Domain.ID, input, getVendedLayers)
	if err != nil {
		c.WithWarnLog(err).WriteTo(response)
		return
	}

	response.WriteHeaderAndEntity(http.StatusOK, output)
}

func (layerRest *LayerRest) deleteVersion(c *server.Context) {
	response := c.Response()

	var (
		err           error
		versionNumber int64
	)

	layerNameStr := c.Request().PathParameter("LayerName")
	versionNumberStr := c.Request().PathParameter("VersionNumber")

	if versionNumberStr == "" {
		c.WithWarnLog(kunErr.NewInvalidParameterValueException("VersionNumber validate", err)).WriteTo(response)
		return
	}

	versionNumber, err = govalidator.ToInt(versionNumberStr)
	if err != nil {
		c.WithWarnLog(kunErr.NewInvalidParameterValueException("VersionNumber validate", err)).WriteTo(response)
		return
	}

	layerName, err := bceBrn.GetLayerName(layerNameStr)
	if err != nil {
		c.WithWarnLog(kunErr.NewInvalidParameterValueException("", err)).WriteTo(response)
		return
	}

	user, err := GetUser(c.Request())
	if err != nil {
		c.WithWarnLog(kunErr.NewInvalidParameterValueException("", err)).WriteTo(response)
		return
	}

	layer := &api.Layer{
		Uid:       user.Domain.ID,
		Version:   versionNumber,
		LayerName: layerName,
	}

	err = service.DeleteLayerVersion(layer)
	if err != nil {
		c.WithWarnLog(err).WriteTo(response)
		return
	}
	response.WriteHeaderAndEntity(http.StatusNoContent, nil)
}

func (layerRest *LayerRest) listVersion(c *server.Context) {
	response := c.Response()

	user, err := GetUser(c.Request())
	if err != nil {
		c.WithWarnLog(kunErr.NewInvalidParameterValueException("", err)).WriteTo(response)
		return
	}
	getVendedLayers := false
	layerNameStr := c.Request().PathParameter("LayerName")
	layerName, err := bceBrn.GetLayerName(layerNameStr)
	if err != nil {
		c.WithWarnLog(kunErr.NewInvalidParameterValueException("", err)).WriteTo(response)
		return
	}

	listCond, err := models.GetListConditionFromRequest(c.Request())
	if err != nil {
		c.WithWarnLog(kunErr.NewInvalidParameterValueException("", err)).WriteTo(response)
		return
	}

	compatibleRuntime := c.Request().QueryParameter("CompatibleRuntime")
	err = service.ValidateCompatibleRuntime(compatibleRuntime)
	if err != nil {
		c.WithWarnLog(err).WriteTo(response)
		return
	}

	input := &api.ListLayerVersionsInput{
		CompatibleRuntime: compatibleRuntime,
		LayerName:         layerName,
		ListCondition:     listCond,
	}

	// check args
	_, err = govalidator.ValidateStruct(input)
	if err != nil {
		c.WithWarnLog(kunErr.NewInvalidParameterValueException("", err)).WriteTo(response)
		return
	}

	if strings.HasPrefix(c.Request().Request.URL.Path, "/v1/console/layers") {
		// console 发过来的LayerName 必须为完整的BRN
		// 判断BRN的accountID和用户是否一致
		getVendedLayers = true
	}

	// 获取list
	output, err := service.ListLayerVersions(user.Domain.ID, input, getVendedLayers)
	if err != nil {
		c.WithWarnLog(err).WriteTo(response)
		return
	}

	response.WriteHeaderAndEntity(http.StatusOK, output)
}

func (layerRest *LayerRest) delete(c *server.Context) {
	response := c.Response()
	layerNameStr := c.Request().PathParameter("LayerName")

	layerName, err := bceBrn.GetLayerName(layerNameStr)
	if err != nil {
		c.WithWarnLog(kunErr.NewInvalidParameterValueException("", err)).WriteTo(response)
		return
	}

	user, err := GetUser(c.Request())
	if err != nil {
		c.WithWarnLog(kunErr.NewInvalidParameterValueException("", err)).WriteTo(response)
		return
	}

	layer := &api.Layer{
		Uid:       user.Domain.ID,
		LayerName: layerName,
	}

	err = service.DeleteLayer(layer)
	if err != nil {
		c.WithWarnLog(err).WriteTo(response)
		return
	}
	response.WriteHeaderAndEntity(http.StatusNoContent, nil)
}

// console发布版本 form提交zip包
func (layerRest *LayerRest) consolePublish(c *server.Context) {

	r := c.Request()
	response := c.Response()
	user, err := GetUser(c.Request())
	if err != nil {
		c.WithWarnLog(kunErr.NewInvalidParameterValueException("", err)).WriteTo(response)
		return
	}
	input := new(api.PublishLayerVersionInput)
	err = r.Request.ParseMultipartForm(51 * 1024 * 1024)
	if err != nil {
		c.WithWarnLog(kunErr.NewInvalidParameterValueException("", err)).WriteTo(response)
		return
	}

	input.Content = &api.LayerVersionContentInput{
		BosBucket: r.Request.FormValue("BosBucket"),
		BosObject: r.Request.FormValue("BosObject"),
	}
	if compatibleRuntimes := r.Request.FormValue("CompatibleRuntimes"); len(compatibleRuntimes) > 0 {
		input.CompatibleRuntimes = strings.Split(strings.TrimSpace(compatibleRuntimes), ",")
	}

	input.Description = r.Request.FormValue("Description")
	input.LicenseInfo = r.Request.FormValue("LicenseInfo")
	layerNameStr := c.Request().PathParameter("LayerName")
	layerName, err := bceBrn.GetLayerName(layerNameStr)
	if err != nil {
		c.WithWarnLog(kunErr.NewInvalidParameterValueException("", err)).WriteTo(response)
		return
	}
	input.LayerName = layerName

	err = service.ValidateCompatibleRuntimeList(input.CompatibleRuntimes)
	if err != nil {
		c.WithWarnLog(err).WriteTo(response)
		return
	}

	file, _, err := r.Request.FormFile("ZipFile")
	if err != nil {
		c.WithWarnLog(kunErr.NewInvalidRequestContentException("", err))
	} else {
		defer file.Close()
		byteArray, err := ioutil.ReadAll(file)
		if err != nil {
			c.WithWarnLog(kunErr.NewInvalidParameterValueException("", err)).WriteTo(response)
			return
		}
		input.Content.ZipFileBytes = byteArray
	}

	_, err = govalidator.ValidateStruct(input)
	if err != nil {
		c.WithWarnLog(kunErr.NewInvalidParameterValueException("", err)).WriteTo(response)
		return
	}

	output, err := service.PublishLayer(user.Domain.ID, input, c)
	if err != nil {
		c.WithErrorLog(err).WriteTo(response)
		return
	}

	response.WriteHeaderAndEntity(http.StatusCreated, output)
}
