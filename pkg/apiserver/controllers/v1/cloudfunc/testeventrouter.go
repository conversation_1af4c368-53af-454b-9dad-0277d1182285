package cloudfunc

import (
	restful "github.com/emicklei/go-restful"
	"icode.baidu.com/baidu/faas/kun/cmd/apiserver/filter"
	"icode.baidu.com/baidu/faas/kun/pkg/server"
	"icode.baidu.com/baidu/faas/kun/pkg/server/endpoint"
)

type TestEvent struct {
}

//TestEventAPIs xxx
func (e TestEvent) TestEventAPIs() []endpoint.ApiSingle {
	apis := []endpoint.ApiSingle{
		//create user test event POST /v1/testevent/userevent
		endpoint.ApiSingle{
			Verb:    "POST",
			Path:    "/testevent/userevent",
			Handler: server.WrapRestRouteFunc(e.createUserTestEvent),
			Filters: []restful.FilterFunction{
				filter.IAMPermissionCheck("*", "", []string{"cfc:CreateUserTestEvent", filter.IamPermWrite}),
			},
		},

		//get user test events GET /v1/testevent/userevents/
		endpoint.ApiSingle{
			Verb:    "GET",
			Path:    "/testevent/userevents",
			Handler: server.WrapRestRouteFunc(e.getUserTestEvent),
			Filters: []restful.FilterFunction{
				filter.IAMPermissionCheck("*", "", []string{"cfc:GetUserTestEvent", filter.IamPermList}),
			},
		},

		//get user test event by uuid GET /v1/testevent/userevent/{UUID}
		endpoint.ApiSingle{
			Verb:    "GET",
			Path:    "/testevent/userevent/{UUID}",
			Handler: server.WrapRestRouteFunc(e.getOneUserTestEvent),
			Filters: []restful.FilterFunction{
				filter.IAMPermissionCheck("*", "", []string{"cfc:GetOneUserTestEvent", filter.IamPermList}),
			},
		},

		//update user test event POST /v1/testevent/userevent/
		endpoint.ApiSingle{
			Verb:    "PUT",
			Path:    "/testevent/userevent/{UUID}",
			Handler: server.WrapRestRouteFunc(e.updateUserTestEvent),
			Filters: []restful.FilterFunction{
				filter.IAMPermissionCheck("*", "", []string{"cfc:UpdateUserTestEvent", filter.IamPermWrite}),
			},
		},

		//delete user test event DELETE /v1/testevent/userevent/
		endpoint.ApiSingle{
			Verb:    "DELETE",
			Path:    "/testevent/userevent/{UUID}",
			Handler: server.WrapRestRouteFunc(e.deleteUserTestEvent),
			Filters: []restful.FilterFunction{
				filter.IAMPermissionCheck("*", "", []string{"cfc:DeleteUserTestEvent", filter.IamPermWrite}),
			},
		},
		//get standard test event GET /v1/testevent/standardevents/
		endpoint.ApiSingle{
			Verb:    "GET",
			Path:    "/testevent/standardevents",
			Handler: server.WrapRestRouteFunc(e.getStandardTestEvent),
			Filters: []restful.FilterFunction{
				filter.IAMPermissionCheck("*", "", []string{"cfc:GetStandardTestEvent", filter.IamPermList}),
			},
		},

		//get standard test event by ID  GET /v1/testevent/standardevent/
		endpoint.ApiSingle{
			Verb:    "GET",
			Path:    "/testevent/standardevent/{ID}",
			Handler: server.WrapRestRouteFunc(e.getOneStandardTestEvent),
			Filters: []restful.FilterFunction{
				filter.IAMPermissionCheck("*", "", []string{"cfc:GetOneStandardTestEvent", filter.IamPermList}),
			},
		},
	}
	return apis
}
