package cloudfunc

import (
	"icode.baidu.com/baidu/faas/kun/pkg/api"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/dao"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/global"
	kunErr "icode.baidu.com/baidu/faas/kun/pkg/error"
	"icode.baidu.com/baidu/faas/kun/pkg/server"
	"icode.baidu.com/baidu/faas/kun/pkg/util/json"
	"net/http"
	"strconv"
)

func (e TestEvent) createUserTestEvent(c *server.Context) {
	response := c.Response()
	r := c.Request().Request
	defer r.Body.Close()

	ctx := global.BuildApiserverContext(c.Request(), c.Response(), "createUserTestEvent")

	decoder := json.NewDecoder(r.Body)
	newTestEvent := &dao.UserTestEvent{}
	if err := decoder.Decode(newTestEvent); err != nil {
		c.WithWarnLog(kunErr.NewInvalidRequestContentException(err.Error(), nil)).WriteTo(response)
		return
	}

	rKerr := CreateNewUserEvent(c.Request(), newTestEvent, ctx)
	if rKerr != nil {
		c.WithErrorLog(rKerr).WriteTo(response)
		return
	}

	defer global.WriteSummary(ctx, api.LogNotToSummary)
	response.WriteHeaderAndEntity(http.StatusCreated, map[string]interface{}{
		"TestEvent": newTestEvent,
	})
}

func (e TestEvent) getUserTestEvent(c *server.Context) {
	response := c.Response()
	r := c.Request()

	ctx := global.BuildApiserverContext(c.Request(), c.Response(), "getUserTestEvent")
	funcName := r.QueryParameter("FunctionName")
	userTestEvent := &dao.UserTestEvent{}
	userTestEvent.FunctionName = funcName
	userEvents, rKerr := GetUserTestEvent(c.Request(), userTestEvent, ctx)
	if rKerr != nil {
		c.WithErrorLog(rKerr).WriteTo(response)
		return
	}

	defer global.WriteSummary(ctx, api.LogNotToSummary)
	response.WriteHeaderAndEntity(http.StatusOK, map[string]interface{}{
		"TestEvent": userEvents,
	})
}

func (e TestEvent) getOneUserTestEvent(c *server.Context) {
	response := c.Response()
	uuid := c.Request().PathParameter("UUID")
	userTestEvent := &dao.UserTestEvent{}
	userTestEvent.Uuid = uuid

	ctx := global.BuildApiserverContext(c.Request(), c.Response(), "getOneUserTestEvent")
	userEvent, rKerr := GetOneUserTestEvent(c.Request(), userTestEvent, ctx)
	if rKerr != nil {
		c.WithErrorLog(rKerr).WriteTo(response)
		return
	}

	defer global.WriteSummary(ctx, api.LogNotToSummary)
	response.WriteHeaderAndEntity(http.StatusOK, map[string]interface{}{
		"TestEvent": userEvent,
	})
}

func (e TestEvent) updateUserTestEvent(c *server.Context) {
	response := c.Response()

	ctx := global.BuildApiserverContext(c.Request(), c.Response(), "updateUserTestEvent")
	cond := &dao.UserTestEvent{}
	cond.Uuid = c.Request().PathParameter("UUID")

	tmpTestEvent := &dao.UserTestEvent{}
	updateEvent := &dao.UserTestEvent{}
	if err := c.Request().ReadEntity(tmpTestEvent); err != nil {
		c.WithWarnLog(kunErr.NewInvalidRequestContentException("", err)).WriteTo(response)
		return
	}

	updateEvent.Event = tmpTestEvent.Event

	rKerr := UpdateUserTestEvent(c.Request(), cond, updateEvent, ctx)
	if rKerr != nil {
		c.WithErrorLog(rKerr).WriteTo(response)
		return
	}

	updateEvent.Uuid = cond.Uuid

	defer global.WriteSummary(ctx, api.LogNotToSummary)
	response.WriteHeaderAndEntity(http.StatusOK, map[string]interface{}{
		"TestEvent": updateEvent,
	})
}

func (e TestEvent) deleteUserTestEvent(c *server.Context) {
	response := c.Response()
	uuid := c.Request().PathParameter("UUID")

	ctx := global.BuildApiserverContext(c.Request(), c.Response(), "deleteUserTestEvent")
	rKerr := DeleteUserTestEvent(c.Request(), uuid, ctx)
	if rKerr != nil {
		c.WithErrorLog(rKerr).WriteTo(response)
		return
	}

	defer global.WriteSummary(ctx, api.LogNotToSummary)
	response.WriteHeaderAndEntity(http.StatusNoContent, nil)
}

func (e TestEvent) getStandardTestEvent(c *server.Context) {
	response := c.Response()
	r := c.Request()

	ctx := global.BuildApiserverContext(c.Request(), c.Response(), "getStandardTestEvent")
	statusStr := r.QueryParameter("Status")
	if statusStr == "" {
		err := kunErr.NewInvalidParameterValueException("[params invalid, status is empty]", nil)
		c.WithErrorLog(err).WriteTo(response)
		return
	}
	status, err := strconv.Atoi(statusStr)
	if err != nil {
		c.WithErrorLog(err).WriteTo(response)
		return
	}

	standardTestEvent := &dao.StandardTestEvent{}
	standardTestEvent.Status = status

	standardEvents := make([]dao.StandardTestEvent, 0)
	var standardEventTmp interface{}
	if standardEventTmp, err = ctx.Observer.NewStage(global.QueryDatabaseStage).ObserveObject(GetStandardTestEvent(standardTestEvent)); err != nil {
		c.WithErrorLog(err).WriteTo(response)
		return
	}

	standardEvents = standardEventTmp.([]dao.StandardTestEvent)
	defer global.WriteSummary(ctx, api.LogNotToSummary)
	response.WriteHeaderAndEntity(http.StatusOK, map[string]interface{}{
		"TestEvent": standardEvents,
	})
}

func (e TestEvent) getOneStandardTestEvent(c *server.Context) {
	response := c.Response()
	r := c.Request()

	ctx := global.BuildApiserverContext(c.Request(), c.Response(), "getOneStandardTestEvent")
	idStr := r.PathParameter("ID")
	if idStr == "" {
		err := kunErr.NewInvalidParameterValueException("[params invalid, ID is empty]", nil)
		c.WithErrorLog(err).WriteTo(response)
		return
	}
	id, err := strconv.Atoi(idStr)
	if err != nil {
		c.WithErrorLog(err).WriteTo(response)
		return
	}

	standardTestEvent := &dao.StandardTestEvent{}
	standardTestEvent.ID = id

	var standEventTmp interface{}
	if standEventTmp, err = ctx.Observer.NewStage(global.QueryDatabaseStage).ObserveObject(GetOneStandardTestEvent(standardTestEvent)); err != nil {
		c.WithErrorLog(err).WriteTo(response)
		return
	}
	standardTestEvent = standEventTmp.(*dao.StandardTestEvent)
	defer global.WriteSummary(ctx, api.LogNotToSummary)
	response.WriteHeaderAndEntity(http.StatusOK, map[string]interface{}{
		"TestEvent": standardTestEvent,
	})
}
