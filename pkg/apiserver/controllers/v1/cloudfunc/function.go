package cloudfunc

import (
	"io/ioutil"
	"net/http"
	"strconv"
	"strings"
	"time"

	bosSdk "github.com/baidubce/bce-sdk-go/services/bos"
	"github.com/emicklei/go-restful"
	"github.com/google/uuid"
	"go.uber.org/zap"

	"icode.baidu.com/baidu/faas/kun/pkg/api"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/dao"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/dbengine"
	apiErr "icode.baidu.com/baidu/faas/kun/pkg/apiserver/error"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/global"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/models"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/models/function"
	apiServer "icode.baidu.com/baidu/faas/kun/pkg/apiserver/server"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/service"
	"icode.baidu.com/baidu/faas/kun/pkg/bce/bls"
	"icode.baidu.com/baidu/faas/kun/pkg/bce/bos"
	"icode.baidu.com/baidu/faas/kun/pkg/bce/brn"
	"icode.baidu.com/baidu/faas/kun/pkg/bce/iam"
	kunErr "icode.baidu.com/baidu/faas/kun/pkg/error"
	"icode.baidu.com/baidu/faas/kun/pkg/server"
	"icode.baidu.com/baidu/faas/kun/pkg/util/base64"
	"icode.baidu.com/baidu/faas/kun/pkg/util/convert"
	"icode.baidu.com/baidu/faas/kun/pkg/util/json"
	"icode.baidu.com/baidu/faas/kun/pkg/util/logs"
	"icode.baidu.com/baidu/faas/kun/pkg/whitelist"
)

// 上传code
type codeFile struct {
	BosBucket string
	BosObject string
	ZipFile   string
	Publish   bool // 更新时候使用
	DryRun    bool // 更新时候使用
}

// req body
type FunctionReq struct {
	Code    codeFile
	Publish bool
	dao.Function
}

// FunctionRest xxx
type FunctionRest struct {
	Path string
}

var NewClientWrapper = bos.NewClient
var NewBlsClientWrapper = bls.NewClient

// create create 创建函数
//
// 根据传入的FunctionReq结构体，创建一个新的函数。如果指定了BOS参数或者ZipFile参数，则会先将代码上传到BOS，然后再创建函数；否则，直接使用ZipFile参数创建函数。
// 如果指定了Publish参数，则会发布代码并返回新发布的函数信息；否则，只会返回新创建的函数信息。
//
// 参数:
//
//	c (*server.Context) - Context对象指针，包含HTTP请求和响应相关信息
//
// 返回值:
//
//	无
func (f *FunctionRest) create(c *server.Context) {
	response := c.Response()
	functionReq := new(FunctionReq)
	var err error
	if err = c.Request().ReadEntity(functionReq); err != nil {
		c.WithWarnLog(kunErr.NewInvalidRequestContentException("", err)).WriteTo(response)
		return
	}

	workspaceID := c.Request().Request.Header.Get(api.HeaderWorkspaceID)
	if workspaceID != "" {
		functionReq.WorkspaceID = workspaceID
		functionReq.FunctionName = workspaceID + "_" + functionReq.FunctionName
	}
	// BOS参数与zipfile参数不允许同时存在
	if (functionReq.Code.BosBucket != "" || functionReq.Code.BosObject != "") && functionReq.Code.ZipFile != "" {
		c.WithWarnLog(apiErr.NewTooManyParametersException("Both BOS Parameter and zipfile parameter exist", nil)).WriteTo(response)
		return
	}

	// BOS参数与zipfile参数也不能同时为空
	if (functionReq.Code.BosBucket == "" || functionReq.Code.BosObject == "") && functionReq.Code.ZipFile == "" {
		c.WithWarnLog(apiErr.NewMissingParametersException("Both BOS parameter and zipfile parameter do not exist", nil)).WriteTo(response)
		return
	}

	if functionReq.SourceTag == api.CFCMigrationSourceTag {
		// 迁移中心来的请求, BOS参数必须存在
		if functionReq.Code.BosBucket == "" || functionReq.Code.BosObject == "" {
			c.WithWarnLog(apiErr.NewMissingParametersException("cfc migration BOS parameter do not exist", nil)).WriteTo(response)
			return
		}
		// 迁移中心来的请求, 不允许设置publish
		if functionReq.Publish {
			c.WithWarnLog(apiErr.NewInvalidRequestException("cfc migration should not set publish parameter", "", nil)).WriteTo(response)
			return
		}
	}

	// upload code to bos
	var byteArray []byte
	if functionReq.Code.BosBucket != "" && functionReq.Code.BosObject != "" {
		bosClient, err := NewClientWrapper(c)
		if err != nil {
			_ = c.WithErrorLog(apiErr.NewInitBosClientException("", "Init User BOS client failed", nil)).WriteTo(response)
			return
		}
		if functionReq.SourceTag == api.CFCMigrationSourceTag {
			// 迁移中心来的请求, 从CFC code storage中取得函数code
			codeConfig := global.AC.Config.CodeConfiguration
			bosClient, err = bosSdk.NewClient(codeConfig.AccessKey, codeConfig.SecretAccessKey, codeConfig.MigrationSericeBosEndpoint)
			if err != nil {
				_ = c.WithErrorLog(apiErr.NewInitBosClientException("", "Init User BOS client failed", nil)).WriteTo(response)
				return
			}
		}

		byteArray, err = DownloadCodeFromUserBos(bosClient, functionReq.Code.BosBucket, functionReq.Code.BosObject)
		if err != nil {
			c.WithWarnLog(err).WriteTo(response)
			return
		}
	} else if functionReq.Code.ZipFile != "" {
		byteArray, _ = base64.DecodeString(functionReq.Code.ZipFile)
	}
	apiserverContext := global.BuildApiserverContext(c.Request(), c.Response(), "create")
	defer global.WriteSummary(apiserverContext, api.LogNotToSummary)
	var (
		NewFuncTmp, pubFunc interface{}
	)

	if NewFuncTmp, err = CreateNewFunction(c, functionReq.Function, byteArray, apiserverContext); err != nil {
		c.WithWarnLog(err).WriteTo(response)
		return
	}

	NewFunc := NewFuncTmp.(*dao.Function)
	// 新增一条记录，发布代码
	if functionReq.Publish {
		if pubFunc, err = apiserverContext.Observer.NewStage(global.PublishFunctionStage).ObserveObject(models.PublishNewFunc(*NewFunc, "1")); err != nil {
			c.WithErrorLog(kunErr.NewServiceException("Insert Function success, BUT Publish failed", err)).WriteTo(response)
			return
		} else {
			// 返回新发布的代码
			publishF := pubFunc.(*dao.Function)
			publishF.DealResFunction()
			response.WriteHeaderAndEntity(http.StatusCreated, publishF)
			return
		}
	}
	NewFunc.DealResFunction()
	response.WriteHeaderAndEntity(http.StatusCreated, NewFunc)
}

func (f FunctionRest) find(c *server.Context) {
	response := c.Response()
	onlyFuncName := models.CheckVersionAndAlias(c.Request())
	ctx := global.BuildApiserverContext(c.Request(), c.Response(), "find")
	defer global.WriteSummary(ctx, api.LogNotToSummary)
	ctx.SpanContext = c.Context()
	findFunc, rKerr := findOneFunctionNew(c.Request(), c.Logger(), ctx)
	if rKerr != nil {
		c.WithWarnLog(rKerr).WriteTo(response)
		return
	}
	ctx.Context.FunctionBrn = findFunc.FunctionBrn
	alias, _ := GetAliasFromReq(c.Request())
	if alias != "" {
		findFunc.FunctionBrn = brn.GenerateFuncBrnString(findFunc.Region, findFunc.Uid, findFunc.FunctionName, alias)
		// findFunc.DealResFunction()
	}
	var (
		urlTmp interface{}
		err    error
		url    string
	)
	// 从bos上下载代码时，如果有workspaceID,则需要将FunctionName加上workspaceID
	if findFunc.WorkspaceID != "" {
		findFunc.FunctionName = findFunc.WorkspaceID + "_" + findFunc.FunctionName
	}
	if urlTmp, err = ctx.Observer.NewStage(global.DownloadCodeStage).ObserveObject(global.AC.Clients.Code.FaasCodeDownloadUrl(findFunc.FunctionName, findFunc.Uid, findFunc.CodeSha256, findFunc.CodeID)); err != nil {
		c.Logger().Errorf("get download url failed, [func: %v][errmsg:%s]", findFunc, err.Error())
	}
	url = urlTmp.(string)
	// 去掉函数名称workspaceID前缀
	findFunc.DealResFunction()
	r := map[string]interface{}{
		"Code":          api.CodeStorage{Location: url},
		"Configuration": findFunc,
	}
	if onlyFuncName && *findFunc.ReservedConcurrentExecutions != 0 {
		r["Concurrency"] = map[string]int{
			"ReservedConcurrentExecutions": *findFunc.ReservedConcurrentExecutions,
		}
	}

	response.WriteHeaderAndEntity(http.StatusOK, r)
}

func (f FunctionRest) insideFind(c *server.Context) {
	defer c.Logger().TimeTrack(time.Now(), "insideFind")
	response := c.Response()
	ctx := global.BuildApiserverContext(c.Request(), c.Response(), "insideFind")
	ctx.SpanContext = c.Context()

	uid := c.Request().HeaderParameter(api.BceFaasUIDKey)
	eventHubtrigger := c.Request().HeaderParameter(api.BceFaasTriggerKey)
	if uid == "" {
		c.WithErrorLog(apiErr.NewUserNotFoundException("insideFind userid not found", nil)).WriteTo(response)
		return
	}
	c.Request().SetAttribute("User", &iam.User{
		Domain: &iam.Domain{
			ID: uid,
		},
	})

	findFunc := new(dao.Function)
	var rKerr error
	switch eventHubtrigger {
	case api.TriggerTypeDuerOS, api.TriggerTypeBos, api.TriggerTypeCdn:
		// 从这三个触发器来的请求必须是完整的brn
		funcBrn := c.Request().PathParameter("FunctionName")
		findFunc.FunctionBrn, rKerr = getVersionBrn(funcBrn)
		ctx.Context.FunctionBrn = findFunc.FunctionBrn
		if rKerr != nil {
			c.WithWarnLog(rKerr).WriteTo(response)
			return
		}
		if rkunErr := ctx.Observer.NewStage(global.FindOneFunctionStage).Observe(dao.FindOneFunc(findFunc)); rkunErr != nil {
			c.WithWarnLog(rkunErr).WriteTo(response)
			return
		}
		findFunc.DealResFunction()
	default:
		findFunc, rKerr = findOneFunctionNew(c.Request(), c.Logger(), ctx)
		if rKerr != nil {
			c.WithWarnLog(rKerr).WriteTo(response)
			c.Logger().Infof("uid %s", uid)
			return
		}
	}

	if findFunc.WorkspaceID != "" {
		findFunc.FunctionName = findFunc.WorkspaceID + "_" + findFunc.FunctionName
	}

	// _, span := trace.SpanFromContext(c.Context()).Tracer().Start(c.Context(), "insideFindHandler/downLoadCode")
	var (
		urlTmp, conCurrencyTmp interface{}
		err                    error
		logConfig              *api.LogConfiguration
		codeStorage            = api.CodeStorage{}
	)
	whiteListInfo, _ := global.AC.Config.SourceWhiteList.Get(findFunc.Uid)
	isInWhiteList := whiteListInfo != nil && whiteListInfo.Source == whitelist.WhiteListSourceSquashFs
	if findFunc.SquashFsSha256 != nil && *findFunc.SquashFsSha256 != "" && (global.AC.Config.EnableSquashFs || isInWhiteList) {
		// 返回squashFs镜像
		if urlTmp, err = ctx.Observer.NewStage(global.DownloadCodeStage).ObserveObject(global.AC.Clients.Code.FaasSquashFsCodeDownloadUrl(findFunc.FunctionName, findFunc.Uid, findFunc.CodeID)); err != nil {
			c.Logger().Errorf("get squashFs download url failed, [func: %v][errmsg:%s]", findFunc, err.Error())
		}
		codeStorage.Location = urlTmp.(string)
		codeStorage.RepositoryType = api.CodeModeSqfs
		findFunc.CodeSha256 = *findFunc.SquashFsSha256
	} else {
		if urlTmp, err = ctx.Observer.NewStage(global.DownloadCodeStage).ObserveObject(global.AC.Clients.Code.FaasCodeDownloadUrl(findFunc.FunctionName, findFunc.Uid, findFunc.CodeSha256, findFunc.CodeID)); err != nil {
			c.Logger().Errorf("get download url failed, [func: %v][errmsg:%s]", findFunc, err.Error())
		}
		codeStorage.Location = urlTmp.(string)
	}
	if findFunc.LogType == "bos" {
		logConfig = &api.LogConfiguration{
			LogType: findFunc.LogType,
			BosDir:  findFunc.LogBosDir,
		}
	} else if findFunc.LogType == "kafka" || findFunc.LogType == "fluentd_kafka" {
		logConfig = &api.LogConfiguration{
			LogType: findFunc.LogType,
			Params:  findFunc.LogBosDir,
		}
	} else if findFunc.LogType == "bls" {
		logConfig = &api.LogConfiguration{
			LogType:   findFunc.LogType,
			BlsLogSet: findFunc.BlsLogSet,
		}
	} else {
		logConfig = nil
	}
	// span.End()

	if conCurrencyTmp, err = ctx.Observer.NewStage(global.CountReservedConcurrencyStage).ObserveObject(dao.CountUserReservedConcurrency(c.Context(), uid)); err != nil {
		c.Logger().Errorf("get user reserved concurrency sum failed, [errmsg:%s]", err.Error())
	}
	con := &api.Concurrency{
		AccountReservedSum: conCurrencyTmp.(int),
	}
	if *findFunc.ReservedConcurrentExecutions != 0 {
		v := int64(*findFunc.ReservedConcurrentExecutions)
		con.ReservedConcurrentExecutions = &v
	}
	defer global.WriteSummary(ctx, api.LogNotToSummary)

	if netCfg, err := dao.FindOneNetworkConfig(&dao.NetworkConfig{
		NetworkConfig: api.NetworkConfig{
			Brn: findFunc.FunctionBrn,
		},
	}); err != nil {
		c.WithWarnLog(err).WriteTo(response)
		return
	} else {
		if netCfg != nil && netCfg.VpcConfigStr != nil {
			findFunc.NetworkConfig = &netCfg.NetworkConfig
		}
	}

	// _, span = trace.SpanFromContext(c.Context()).Tracer().Start(c.Context(), "insideFindHandler/downloadLayer")
	// 内部接口返回下载zip包地址
	if findFunc.LayerList != nil && findFunc.LayerSha256 != nil && *findFunc.LayerSha256 != "" {
		for _, layerSample := range findFunc.LayerList {
			location := global.AC.Clients.Code.FaasLayerDownloadUrl(layerSample.Layer.LayerName, layerSample.Layer.Uid, layerSample.Layer.CodeId)
			layerSample.Content = &api.LayerVersionContentOutput{
				CodeSha256: layerSample.Layer.CodeSha256,
				CodeSize:   layerSample.Layer.CodeSize,
				Location:   location,
			}
		}
		findFunc.LayerLocation = global.AC.Clients.Code.FaasFunctionLayersSquashFsDownloadUrl(findFunc.FunctionName, findFunc.Uid, *findFunc.LayerSha256)
	}
	// span.End()

	findFunc.DealResFunction()
	response.WriteHeaderAndEntity(http.StatusOK, map[string]interface{}{
		"Code":          codeStorage,
		"Concurrency":   con,
		"Configuration": findFunc,
		"LogConfig":     logConfig,
	})
}

func (f FunctionRest) findByCondition(c *server.Context) {
	response := c.Response()
	r := c.Request()
	// lambda api
	markerStr := r.QueryParameter("Marker")
	maxItemStr := r.QueryParameter("MaxItems")
	version := r.QueryParameter("FunctionVersion")
	// bce api 支持分页
	pageNoStr := r.QueryParameter("page")
	pageSizeStr := r.QueryParameter("pageSize")
	// bce api 支持单字段排序、functionName模糊检索
	orderBy := r.QueryParameter("orderBy")
	sort := r.QueryParameter("order")
	searchFN := r.QueryParameter("searchFN")
	// bce api 支持rumtime过滤
	runtime := r.QueryParameter("runtime")
	serviceName := r.QueryParameter("serviceName")
	blueprintTag := r.QueryParameter("blueprint")
	findFunc, err := InitFunction(r)
	if err != nil {
		c.WithWarnLog(apiErr.NewInitFuncMetaException(err.Error(), "", nil)).WriteTo(response)
		return
	}
	switch version {
	case "":
		findFunc.Version = "$LATEST"
	case "ALL":
		findFunc.Version = ""
	default:
		findFunc.Version = version
	}
	if orderBy == "" {
		orderBy = "CreatedAt"
	}
	if sort == "" {
		sort = "DESC"
	}
	var (
		count                             int64
		marker, maxItem, pageNo, pageSize int64
		resMap                            = make(map[string]interface{})
		functionResSlice                  = make([]dao.Function, 0)
		functionsTmp, countTmp            interface{}
	)
	// 分页
	pageNo, pageSize, marker, maxItem, err = models.ParseParams(pageNoStr, pageSizeStr, markerStr, maxItemStr)
	if err != nil {
		c.WithWarnLog(kunErr.NewInvalidParameterValueException("[parse pageNo|pageSize|Marker|MaxItems fail]", err))
		return
	}
	apiserverContext := global.BuildApiserverContext(c.Request(), c.Response(), "list_function")
	defer global.WriteSummary(apiserverContext, api.LogNotToSummary)
	apiserverContext.Context.FunctionBrn = findFunc.FunctionBrn

	conditon := &function.FunctionListCond{
		Version:      version,
		PageNo:       pageNo,
		PageSize:     pageSize,
		Marker:       marker,
		MaxItems:     maxItem,
		OrderBy:      orderBy,
		Sort:         sort,
		SearchFN:     searchFN,
		Runtime:      runtime,
		ServiceName:  serviceName,
		BlueprintTag: blueprintTag,
	}
	if _, ok := r.Request.Header[api.HeaderWorkspaceID]; ok {
		workspaceID := r.Request.Header.Get(api.HeaderWorkspaceID)
		conditon.WorkspaceID = &workspaceID
	}

	if functionsTmp, countTmp, err = apiserverContext.Observer.NewStage(global.ListFunctionsStage).ObserveObjects(function.ListFunctionWithConditions(findFunc, conditon)); err != nil {
		c.WithWarnLog(kunErr.NewServiceException("[Query database failed]", err))
		return
	}
	functionResSlice = functionsTmp.([]dao.Function)
	count = countTmp.(int64)
	resMap["Functions"] = models.FormatFunctionRes(functionResSlice)
	if pageNoStr != "" || pageSizeStr != "" {
		resMap["Total"] = count
	} else if maxItemStr != "" {
		if count > (maxItem + marker) {
			resMap["NextMarker"] = strconv.FormatInt((maxItem + marker), 10)
		}
	}
	response.WriteHeaderAndEntity(http.StatusOK, resMap)
}

func (f *FunctionRest) updateCode(c *server.Context) {
	response := c.Response()
	ctx := global.BuildApiserverContext(c.Request(), c.Response(), "updateCode")
	defer global.WriteSummary(ctx, api.LogNotToSummary)
	var (
		updateFuncTmp interface{}
		err           error
	)
	if updateFuncTmp, err = ctx.Observer.NewStage(global.InitFunctionStage).ObserveObject(InitFunction(c.Request())); err != nil {
		c.WithWarnLog(apiErr.NewInitFuncMetaException(err.Error(), "", nil)).WriteTo(response)
		return
	}
	updateFunc := updateFuncTmp.(*dao.Function)
	if updateFunc.Version != "" && updateFunc.Version != "$LATEST" {
		c.WithWarnLog(apiErr.NewInitFuncMetaException("only $LATEST version can be updated", "", nil)).WriteTo(response)
		return
	}
	updateFunc.Version = "$LATEST"
	updateFunc.FunctionBrnInit()
	ctx.Context.FunctionBrn = updateFunc.FunctionBrn
	condFunc := new(dao.Function)
	condFunc.FunctionBrn = updateFunc.FunctionBrn
	codeFileReq := codeFile{}
	if err := c.Request().ReadEntity(&codeFileReq); err != nil {
		c.WithWarnLog(kunErr.NewInvalidRequestContentException("", err)).WriteTo(response)
		return
	}

	// BOS参数与zipfile不允许同时存在
	if (codeFileReq.BosBucket != "" || codeFileReq.BosObject != "") && codeFileReq.ZipFile != "" {
		c.WithWarnLog(apiErr.NewTooManyParametersException("Both BOS Parameter and zipfile parameter exist", nil)).WriteTo(response)
		return
	}

	// 相关参数也不可同时不存在
	if codeFileReq.ZipFile == "" && (codeFileReq.BosBucket == "" || codeFileReq.BosObject == "") {
		c.WithWarnLog(apiErr.NewMissingParametersException("Both BOS parameter and zipfile parameter do not exist", nil)).WriteTo(response)
		return
	}

	// 只有Code不为空时，才重新生成CommitID
	if codeFileReq.ZipFile != "" || (codeFileReq.BosBucket != "" && codeFileReq.BosObject != "") {
		updateFunc.CommitID = convert.String(uuid.New().String())
	}

	if codeFileReq.BosBucket != "" && codeFileReq.BosObject != "" {
		bosClient, err := NewClientWrapper(c)
		if err != nil {
			c.WithErrorLog(apiErr.NewInitBosClientException("", "Init User BOS client failed", nil)).WriteTo(response)
			return
		}

		byteArray, err := DownloadCodeFromUserBos(bosClient, codeFileReq.BosBucket, codeFileReq.BosObject)
		if err != nil {
			c.WithWarnLog(err).WriteTo(response)
			return
		}

		if err := ctx.Observer.NewStage(global.UploadCodeStage).Observe(uploadCodeToBosFromUserBos(byteArray, updateFunc)); err != nil {
			c.WithErrorLog(err).WriteTo(response)
			return
		}
	} else {
		if err := ctx.Observer.NewStage(global.UploadCodeStage).Observe(uploadCodeToBos(codeFileReq.ZipFile, updateFunc)); err != nil {
			c.WithErrorLog(err).WriteTo(response)
			return
		}
	}
	if rkunErr := ctx.Observer.NewStage(global.QueryDatabaseStage).Observe(dao.UpdateFunc(*condFunc, updateFunc)); rkunErr != nil {
		c.WithErrorLog(rkunErr).WriteTo(response)
		return
	}
	if rkunErr := ctx.Observer.NewStage(global.CheckFunctionStage).Observe(dao.FindOneFunc(condFunc)); rkunErr != nil {
		c.WithErrorLog(rkunErr).WriteTo(response)
		return
	}
	// 新增一条记录，发布代码
	if codeFileReq.Publish {
		var (
			nextVersionTmp, pubFunc interface{}
			nextVersionStr          string
		)
		if nextVersionTmp, err = ctx.Observer.NewStage(global.GetNextVersionStage).ObserveObject(models.GetNextVersion(condFunc.Uid, condFunc.Region, condFunc.FunctionName)); err != nil {
			c.WithErrorLog(apiErr.NewResourceNotFoundException("Update Function Code success, But Publish failed, When GetNextVersion", err)).WriteTo(response)
			return
		}
		nextVersionStr = nextVersionTmp.(string)
		if pubFunc, err = ctx.Observer.NewStage(global.PublishFunctionStage).ObserveObject(models.PublishNewFunc(*condFunc, nextVersionStr)); err != nil {
			c.WithErrorLog(kunErr.NewServiceException("Update Function Code success, But Publish failed", err)).WriteTo(response)
			return
		} else {
			// 返回新发布的代码
			publishF := pubFunc.(*dao.Function)
			publishF.DealResFunction()
			response.WriteEntity(publishF)
			return
		}
	}
	condFunc.DealResFunction()
	response.WriteEntity(condFunc)
}

// 禁封函数，直接修改ban字段
// 通过降低memory_size和timeout对函数做限流
// 由于禁封需要立刻生效，更新数据库时要立刻刷缓存，禁封操作频率低，刷缓存不会带来太多额外的性能代价
// 禁封函数时不允许函数重试，ban == true 时同步把timeout设为1
func (f *FunctionRest) banFunction(c *server.Context) {
	response := c.Response()
	ctx := global.BuildApiserverContext(c.Request(), c.Response(), "banFunction")
	defer global.WriteSummary(ctx, api.LogNotToSummary)

	// 限流函数只需要修改memory_size和timeout字段，禁止修改其他信息
	// 禁封函数直接把ban设置为true即可
	functionBrn := ctx.Request.PathParameter("FunctionBrn")
	banInfo := &api.BanInfo{}
	updateFunc := &dao.Function{}
	if err := c.Request().ReadEntity(banInfo); err != nil {
		c.WithWarnLog(kunErr.NewInvalidRequestContentException("", err)).WriteTo(response)
		return
	}
	updateFunc.FunctionBrn = functionBrn

	if banInfo.Ban != nil {
		updateFunc.Ban = banInfo.Ban
	}
	if banInfo.LimitMemorySize != nil {
		updateFunc.LimitMemorySize = banInfo.LimitMemorySize
	}
	if banInfo.LimitTimeout != nil {
		updateFunc.LimitTimeout = banInfo.LimitTimeout
	}
	if banInfo.LimitMaxRetryAttempts != nil {
		updateFunc.LimitMaxRetryAttempts = banInfo.LimitMaxRetryAttempts
	}

	// 这里不需要uid，默认所有函数都可以禁封
	findFunction := &dao.Function{
		FunctionBrn: updateFunc.FunctionBrn,
	}
	findFunction.Version = updateFunc.Version

	// 更新函数信息
	if rkunErr := ctx.Observer.NewStage(global.PublishFunctionStage).Observe(dao.UpdateFunc(*findFunction, updateFunc)); rkunErr != nil {
		c.WithErrorLog(rkunErr).WriteTo(response)
		return
	}
	// 查询最新函数信息
	if rkunErr := ctx.Observer.NewStage(global.CheckFunctionStage).Observe(dao.FindOneFunc(findFunction)); rkunErr != nil {
		c.WithErrorLog(rkunErr).WriteTo(response)
		return
	}
	updateFunc.VpcConfig = findFunction.VpcConfig
	updateFunc.DealResFunction()
	response.WriteEntity(findFunction)
}

// 解封函数，直接将ban设为false，将其他值设为0即可
func (f *FunctionRest) unblockFunction(c *server.Context) {
	response := c.Response()
	ctx := global.BuildApiserverContext(c.Request(), c.Response(), "banFunction")
	defer global.WriteSummary(ctx, api.LogNotToSummary)

	functionBrn := ctx.Request.PathParameter("FunctionBrn")
	updateFunc := &dao.Function{}
	updateFunc.FunctionBrn = functionBrn

	updateFunc.Ban = convert.Bool(false)
	updateFunc.LimitMemorySize = convert.Int(0)
	updateFunc.LimitTimeout = convert.Int(0)
	updateFunc.LimitMaxRetryAttempts = convert.Int(0)

	// 这里不需要uid，默认所有函数都可以解封
	findFunction := &dao.Function{
		FunctionBrn: updateFunc.FunctionBrn,
	}
	findFunction.Version = updateFunc.Version
	// 更新函数信息
	if rkunErr := ctx.Observer.NewStage(global.PublishFunctionStage).Observe(dao.UpdateFunc(*findFunction, updateFunc)); rkunErr != nil {
		c.WithErrorLog(rkunErr).WriteTo(response)
		return
	}
	// 查询最新函数信息
	if rkunErr := ctx.Observer.NewStage(global.CheckFunctionStage).Observe(dao.FindOneFunc(findFunction)); rkunErr != nil {
		c.WithErrorLog(rkunErr).WriteTo(response)
		return
	}
	updateFunc.VpcConfig = findFunction.VpcConfig
	updateFunc.DealResFunction()
	response.WriteEntity(findFunction)
}

func (f *FunctionRest) updateConfiguration(c *server.Context) {
	response := c.Response()
	ctx := global.BuildApiserverContext(c.Request(), c.Response(), "updateConfiguration")
	defer global.WriteSummary(ctx, api.LogNotToSummary)
	var (
		updateFuncTmp interface{}
		err           error
	)
	if updateFuncTmp, err = ctx.Observer.NewStage(global.InitFunctionStage).ObserveObject(InitFunction(c.Request())); err != nil {
		c.WithWarnLog(apiErr.NewInitFuncMetaException(err.Error(), "", nil)).WriteTo(response)
		return
	}
	updateFunc := &dao.Function{}
	updateFunc = updateFuncTmp.(*dao.Function)
	fReq := new(FunctionReq)
	if err := c.Request().ReadEntity(fReq); err != nil {
		c.WithWarnLog(kunErr.NewInvalidRequestContentException("", err)).WriteTo(response)
		return
	}
	if updateFunc.Version == "" {
		if fReq.Version == "" {
			updateFunc.Version = "$LATEST"
		} else {
			updateFunc.Version = fReq.Version
		}
	} else {
		// updateFunc.Version 应与请求体中 fReq.Version相同
		if fReq.Version != "" && updateFunc.Version != fReq.Version {
			c.WithWarnLog(kunErr.NewInvalidParameterValueException("Version in RequestBody and URL are different", nil)).WriteTo(response)
			return
		}
	}
	// 非LATEST版本函数的配置只能修改bos地址等配置，其他不能修改
	if updateFunc.Version == "$LATEST" {
		copyReqFuncToDaoFunc(&fReq.Function, updateFunc)
	} else {
		updateFunc.LogType = fReq.LogType
		updateFunc.LogBosDir = fReq.LogBosDir
		updateFunc.BlsLogSet = fReq.BlsLogSet

		// 非LATEST版本函数可以修改异步调用配置
		updateFunc.AsyncInvokeConfig = fReq.AsyncInvokeConfig
	}
	updateFunc.FunctionBrnInit()
	ctx.Context.FunctionBrn = updateFunc.FunctionBrn
	updateFunc.CommitID = convert.String(uuid.New().String())
	updateFunc.UpdateFunctionPre()
	condFunc := new(dao.Function)
	condFunc.FunctionBrn = updateFunc.FunctionBrn
	// 判断是否配置了LogType
	if updateFunc.LogType != "" {
		if updateFunc.LogType == "none" {
			// 如果设成none，就强制清空日志具体配置
			updateFunc.LogBosDir = ""
			updateFunc.BlsLogSet = ""
		} else if updateFunc.LogType == "bos" {
			// validate the bosPath configured by the user, add "/" if neccessary
			bosPath := updateFunc.LogBosDir
			if !strings.HasSuffix(bosPath, "/") {
				updateFunc.LogBosDir = bosPath + "/"
			}
		} else if updateFunc.LogType == "bls" {
			SetBlsLogStore(c, updateFunc)
		}
	}

	findFunction := &dao.Function{
		Uid:         updateFunc.Uid,
		FunctionBrn: updateFunc.FunctionBrn,
	}
	if rkunErr := ctx.Observer.NewStage(global.CheckFunctionStage).Observe(dao.FindOneFunc(findFunction)); rkunErr != nil {
		c.WithErrorLog(rkunErr).WriteTo(response)
		return
	}

	if fReq.Runtime != "" && findFunction.Runtime != fReq.Runtime {
		err := CheckRuntime(fReq.Runtime)
		if err != nil {
			c.WithWarnLog(err).WriteTo(response)
			return
		}
	}

	err = CheckAcl(fReq.Environment)
	if err != nil {
		c.WithWarnLog(err).WriteTo(response)
		return
	}
	if rkunErr := ctx.Observer.NewStage(global.CheckParamsStage).Observe(validateFunc("update", updateFunc, []byte{})); rkunErr != nil {
		c.WithWarnLog(rkunErr).WriteTo(response)
		return
	}
	if updateFunc.Version == "$LATEST" {
		// 请求中不带Layers字段时，fReq.LayerList=nil，不改变函数原有的Layer配置
		// 请求中带Layers字段时，fReq.LayerList != nil，区分空[]和非空[]，空[]时需要将函数配置的Layer置空
		if len(fReq.LayerList) > 0 {
			layerSampleList, layerBrnList, rkunErr := service.CheckFunctionLayers(updateFunc.Uid, int64(findFunction.CodeSize), fReq.LayerList)
			if rkunErr != nil {
				c.Logger().Errorf("checkFunctionLayer error: %v", rkunErr)
				c.WithWarnLog(rkunErr).WriteTo(response)
				return
			}
			c.Logger().Info("[updateConfiguration] checkFunctionLayer done")
			// 有变化
			if findFunction.LayersStr == nil || strings.Join(layerBrnList, ",") != *findFunction.LayersStr {
				var layerSha256 interface{}
				layerSha256, err = ctx.Observer.NewStage(global.UploadLayerStage).ObserveObject(service.UploadFunctionLayersSquashFs(updateFunc.Uid,
					findFunction.FunctionName, layerSampleList))
				if err != nil {
					c.Logger().Errorf("uploadLayersSquashFs error: %v", rkunErr)
					e := models.OptimizeErrorCode("upload layer file failed", err)
					c.WithErrorLog(e).WriteTo(response)
					return
				}
				c.Logger().Info("[updateConfiguration] uploadLayersSquashFs done")
				layersStr := strings.Join(layerBrnList, ",")
				updateFunc.LayersStr = &layersStr
				updateFunc.LayerSha256 = layerSha256.(*string)
			}
		} else {
			if fReq.LayerList != nil {
				emptyStr := ""
				updateFunc.LayersStr = &emptyStr
				updateFunc.LayerSha256 = &emptyStr
			}
		}

		if err := updateNetworkConfig(c, updateFunc); err != nil {
			c.Logger().Errorf("updateNetworkConfig error: %v", err)
			c.WithErrorLog(err).WriteTo(response)
			return
		}
	}

	// 更新异步调用配置
	if updateFunc.AsyncInvokeConfig != nil {
		if rkunErr := ctx.Observer.NewStage(global.CheckAsyncConfigStage).Observe(models.CheckAsyncInvokeConfig(updateFunc)); rkunErr != nil {
			c.Logger().Errorf("checkAsyncConfig error: %v", rkunErr)
			c.WithErrorLog(rkunErr).WriteTo(response)
			return
		}
	}

	// 更新函数信息
	if rkunErr := ctx.Observer.NewStage(global.PublishFunctionStage).Observe(dao.UpdateFunc(*condFunc, updateFunc)); rkunErr != nil {
		c.Logger().Errorf("publishFunction error: %v", rkunErr)
		c.WithErrorLog(rkunErr).WriteTo(response)
		return
	}
	c.Logger().Info("[updateConfiguration] updateFunction data done")
	// 查询最新函数信息
	if rkunErr := ctx.Observer.NewStage(global.CheckFunctionStage).Observe(dao.FindOneFunc(condFunc)); rkunErr != nil {
		c.Logger().Errorf("find function error: %v", rkunErr)
		c.WithErrorLog(rkunErr).WriteTo(response)
		return
	}
	c.Logger().Info("[updateConfiguration] find new function data done")
	condFunc.VpcConfig = updateFunc.VpcConfig
	condFunc.DealResFunction()
	response.WriteEntity(condFunc)
}

func (f *FunctionRest) getConfiguration(c *server.Context) {
	response := c.Response()
	ctx := global.BuildApiserverContext(c.Request(), c.Response(), "getConfiguration")
	defer global.WriteSummary(ctx, api.LogNotToSummary)
	ctx.SpanContext = c.Context()
	findFunc, rkunErr := findOneFunctionNew(c.Request(), c.Logger(), ctx)
	if rkunErr != nil {
		c.WithWarnLog(rkunErr).WriteTo(response)
		return
	}
	response.WriteEntity(findFunc)
}

func (f *FunctionRest) delete(c *apiServer.ApiServerContext) {
	response := c.Response()
	ctx := global.BuildApiserverContext(c.Request(), c.Response(), "delete")
	defer global.WriteSummary(ctx, api.LogNotToSummary)
	var (
		funcTmp interface{}
		err     error
	)
	if funcTmp, err = ctx.Observer.NewStage(global.InitFunctionStage).ObserveObject(InitFunction(c.Request())); err != nil {
		c.WithWarnLog(apiErr.NewInitFuncMetaException(err.Error(), "", nil)).WriteTo(response)
		return
	}
	function := &dao.Function{}
	function = funcTmp.(*dao.Function)
	errInDelFunc := DeleteOne(function, c, ctx)
	if errInDelFunc != nil {
		c.WithErrorLog(errInDelFunc).WriteTo(response)
		return
	}
	response.WriteHeaderAndEntity(http.StatusNoContent, nil)
}

func findOneFunctionNew(request *restful.Request, logger *logs.Logger, context *global.ApiserverContext) (*dao.Function, error) {
	var functionTmp interface{}
	functionTmp, err := context.Observer.NewStage(global.InitFunctionStage).ObserveObject(InitFunction(request))
	if err != nil {
		rkunErr := apiErr.NewInitFuncMetaException(err.Error(), "", err)
		return nil, rkunErr
	}
	function := functionTmp.(*dao.Function)
	if function.Version == "" {
		function.Version = "$LATEST"
	}
	function.FunctionBrnInit()
	findFunc := new(dao.Function)
	findFunc.FunctionBrn = function.FunctionBrn
	reservedType := request.HeaderParameter(api.HeaderFunctionReservedType)
	defer logger.TimeTrack(time.Now(), "Find A func", zap.String("function_brn", findFunc.FunctionBrn))
	if reservedType == api.FunctionReserved {
		if rkunErr := dao.FindOneReservedFunc(findFunc); rkunErr != nil {
			return nil, rkunErr
		}
	} else {
		if rkunErr := context.Observer.NewStage(global.FindOneFunctionStage).Observe(dao.FindOneFunc(findFunc)); rkunErr != nil {
			return nil, rkunErr
		}
	}

	netCfg, rkunErr := dao.FindOneNetworkConfig(&dao.NetworkConfig{
		NetworkConfig: api.NetworkConfig{
			Brn: findFunc.FunctionBrn,
		},
	})
	if rkunErr != nil {
		return nil, rkunErr
	}
	if netCfg != nil && netCfg.VpcConfigStr != nil {
		findFunc.VpcConfig = &api.VpcConfig{}
		if err = json.Unmarshal([]byte(*netCfg.VpcConfigStr), findFunc.VpcConfig); err != nil {
			return nil, apiErr.NewInitFuncMetaException(err.Error(), "", err)
		}
	}

	findFunc.DealResFunction()
	return findFunc, nil
}

// CreateNewFunction 创建一个新的函数，并返回该函数的信息和错误信息。
// 参数c：包含上下文信息的结构体指针，包括请求、日志记录器等。
// 参数fReq：包含函数元数据的结构体，包括函数名称、运行时、环境等。
// 参数byteArray：包含函数代码的字节切片。
// 参数context：包含全局上下文信息的结构体指针，包括观察者、客户端等。
// 返回值newFunc：包含创建的函数信息的结构体指针，包括函数名称、函数 BRN、函数 ID、函数状态等。
// 返回值error：表示操作过程中出现的任何错误，如果没有错误，则返回 nil。
func CreateNewFunction(c *server.Context, fReq dao.Function, byteArray []byte, context *global.ApiserverContext) (*dao.Function, error) {
	request := c.Request()
	var newFuncTmp interface{}
	var err error
	if newFuncTmp, err = context.Observer.NewStage(global.InitFunctionStage).ObserveObject(InitFunction(request)); err != nil {
		rkunErr := apiErr.NewInitFuncMetaException(err.Error(), "", err)
		c.Logger().Errorf("Init function meta failed: %v", err)
		return nil, rkunErr
	}

	err = CheckRuntime(fReq.Runtime)
	if err != nil {
		c.Logger().Errorf("Check runtime failed: %v", err)
		return nil, err
	}

	err = CheckAcl(fReq.Environment)
	if err != nil {
		c.Logger().Errorf("Check acl failed: %v", err)
		return nil, err
	}

	newFunc := newFuncTmp.(*dao.Function)
	copyReqFuncToDaoFunc(&fReq, newFunc)
	newFunc.CommitID = convert.String(uuid.New().String())
	// 仅仅允许创建指定版本的函数且发布参数不能设定成true
	if fReq.SourceTag == api.CFCMigrationSourceTag && fReq.Version != "" {
		newFunc.Version = fReq.Version
	} else {
		newFunc.Version = "$LATEST"
	}
	newFunc.FunctionName = fReq.FunctionName
	newFunc.FunctionBrnInit()
	newFunc.SourceTag = fReq.SourceTag
	// 创建函数时，若LogType字段传入为空，设为默认值none
	if newFunc.LogType == "" {
		newFunc.LogType = "none"
	} else if newFunc.LogType == "bls" {
		SetBlsLogStore(c, newFunc)
	}

	newFunc.CreateFunctionPre()

	if rkunErr := validateFunc("create", newFunc, byteArray); rkunErr != nil {
		c.Logger().Errorf("Validate function meta failed: %v", rkunErr)
		return nil, rkunErr
	}

	// 设置1800s超时
	if request.HeaderParameter(api.HeaderXCFCCreateFrom) == "x-bce-bos" {
		*newFunc.Timeout = api.DefaultBosFunctionTimeout
	}

	context.Context.FunctionBrn = newFunc.FunctionBrn
	// 查找是否存在同用户同名
	findFunc := new(dao.Function)
	findFunc.FunctionBrn = newFunc.FunctionBrn

	if rKunErr := context.Observer.NewStage(global.CheckFunctionStage).Observe(dao.FindOneFunc(findFunc)); rKunErr == nil && findFunc.FunctionName != "" {
		e := apiErr.NewResourceConflictException("brn exists : "+newFunc.FunctionBrn, nil)
		c.Logger().Errorf("Function already exists: %v", e)
		return nil, e
	}

	// 查询服务是否存在
	if rKunErr := GetFunctionService(newFunc, context); rKunErr != nil {
		e := apiErr.NewServiceNotFoundException("service not exists : "+newFunc.ServiceName, nil)
		c.Logger().Errorf("Service not exists: %v", e)
		return nil, e
	}

	// 检查layer，处理 layer 参数
	layerSampleList := make([]*api.LayerSample, 0)
	layerStrList := make([]string, 0)
	if len(fReq.LayerList) > 0 {
		layerSampleList, layerStrList, err = service.CheckFunctionLayers(newFunc.Uid, int64(len(byteArray)), fReq.LayerList)
		if err != nil {
			c.Logger().Errorf("Check layer failed: %v", err)
			return nil, err
		}
	}

	var codeSha256, codeSize, codeId, squashFsSha256, layerSha256 interface{}
	if codeSha256, squashFsSha256, codeSize, codeId, err = context.Observer.NewStage(global.UploadCodeStage).ObserveMoreObjects(global.AC.Clients.Code.FaasUploadCode(byteArray, newFunc.FunctionName, newFunc.Uid)); err != nil {
		e := models.OptimizeErrorCode("upload code to bos failed", err)
		c.Logger().Errorf("Upload code to bos failed: %v", e)
		return nil, e
	}

	newFunc.CodeSha256 = codeSha256.(string)
	newFunc.CodeSize = codeSize.(int32)
	newFunc.CodeID = codeId.(string)
	newFunc.SquashFsSha256 = squashFsSha256.(*string)

	if len(layerSampleList) > 0 {
		layerSha256, err = context.Observer.NewStage(global.UploadLayerStage).ObserveObject(service.UploadFunctionLayersSquashFs(newFunc.Uid, newFunc.FunctionName, layerSampleList))
		if err != nil {
			e := models.OptimizeErrorCode("upload layer file failed", err)
			c.Logger().Errorf("Upload layer file failed: %v", e)
			return nil, e
		}
		if layerSha256 != nil {
			newFunc.LayerSha256 = layerSha256.(*string)
		}
	}

	layersStr := strings.Join(layerStrList, ",")
	newFunc.LayersStr = &layersStr

	// 开始处理网络配置
	var networkCfg *dao.NetworkConfig
	var shouldNotifyProxyCtrl = false
	networkCfg, err = genNetworkConfig(newFunc)
	if err != nil {
		c.Logger().Errorf("Generate network config failed: %v", err)
		return nil, err
	}
	if networkCfg != nil {
		cfgCond := &dao.NetworkConfig{}
		cfgCond.VpcConfigStr = networkCfg.VpcConfigStr
		cfgCond.Uid = networkCfg.Uid
		existedCfg, err := dao.FindOneNetworkConfig(cfgCond)
		if err != nil {
			c.Logger().Errorf("Find network config failed: %v", err)
			return nil, err
		}

		// 如果该用户已存在相同 vpc 配置的其它函数，那么可以复用已有的 proxy node，因此无需去通知 proxyCtrl 再使用新的 proxy node 了
		// 如果不存在，则需要通知
		if existedCfg != nil {
			networkCfg.Status = existedCfg.Status
			networkCfg.VpcCidr = existedCfg.VpcCidr
			networkCfg.ProxyInternalIP = existedCfg.ProxyInternalIP
			networkCfg.ProxyFloatingIP = existedCfg.ProxyFloatingIP
			networkCfg.EnableSlave = existedCfg.EnableSlave
		} else {
			shouldNotifyProxyCtrl = true
		}
	}

	dbTrans := dbengine.DBTransaction()
	if err = context.Observer.NewStage(global.CreateFunctionStage).Observe(dao.CreateFunc(dbTrans, newFunc)); err != nil {
		dbTrans.Rollback()
		c.Logger().Errorf("Create function failed: %v", err)
		return nil, err
	}

	if networkCfg != nil {
		if err = dao.CreateNetworkConfig(dbTrans, networkCfg); err != nil {
			c.Logger().Errorf("Create network config failed: %v", err)
			return nil, err
		}
		if shouldNotifyProxyCtrl {
			go func() {
				if err = global.AC.Clients.Proxy.AskForProxy(&networkCfg.NetworkConfig); err != nil {
					c.Logger().Errorf("notify proxyctrl failed, err: %v", err)
				}
			}()
		}
	}
	dbTrans.Commit()
	return newFunc, nil
}

// 生成要写入 network 数据库表的记录
func genNetworkConfig(newFunc *dao.Function) (*dao.NetworkConfig, error) {
	if newFunc.VpcConfig == nil {
		return nil, nil
	}

	vpcInfo, err := global.AC.Clients.Vpc.GetVpc(newFunc.Uid, newFunc.VpcConfig.VpcID)
	if err != nil {
		return nil, err
	}

	var b []byte
	b, err = json.Marshal(newFunc.VpcConfig) // 已重写 MarshalJSON 方法，先 sort 再序列化
	if err != nil {
		return nil, err
	}
	return &dao.NetworkConfig{
		NetworkConfig: api.NetworkConfig{
			VpcConfig:       newFunc.VpcConfig,
			VpcConfigStr:    convert.String(string(b)),
			Uid:             newFunc.Uid,
			Brn:             newFunc.FunctionBrn,
			FunctionName:    newFunc.FunctionName,
			VpcCidr:         convert.String(vpcInfo.Vpc.Cidr),
			Status:          convert.String("notready"),
			ProxyInternalIP: convert.String(""),
			ProxyFloatingIP: convert.String(""),
		},
	}, nil
}

func getVersionBrn(funcBrn string) (string, error) {
	brnSections, _ := brn.Parse(funcBrn)
	resource := strings.Split(brnSections.Resource, ":")
	if len(resource) != 3 {
		return "", apiErr.NewInitFuncMetaException("function brn is invalid", "", nil)
	}
	qualifier := resource[2]
	if !api.RegVersion.MatchString(qualifier) {
		aliasRes := &dao.Alias{AliasBrn: funcBrn}
		if rkunErr := dao.FindOneAlias(aliasRes); rkunErr != nil {
			return "", rkunErr
		}
		brnSections.Resource = resource[0] + ":" + resource[1] + ":" + aliasRes.FunctionVersion
		funcBrn = brnSections.String()
	}
	return funcBrn, nil
}

// upload code to bos
func uploadCodeToBos(zipFile string, updateFunc *dao.Function) error {
	if zipFile != "" {
		byteArray, err := base64.DecodeString(zipFile)
		if err != nil {
			return kunErr.NewInvalidParameterValueException("ZipFile invalid", err)
		}
		if err = models.CheckCodeSize("update", updateFunc, byteArray); err != nil {
			return err
		}
		updateFunc.CodeSha256, updateFunc.SquashFsSha256, updateFunc.CodeSize, updateFunc.CodeID, err = global.AC.Clients.Code.FaasUploadCode(byteArray, updateFunc.FunctionName, updateFunc.Uid)
		if err != nil {
			err = models.OptimizeErrorCode("upload code to bos failed", err)
			return err
		}
	}
	return nil
}

// 与uploadCodeToBos不同，本函数省去了解压过程。主要用于用户bos的上传
// 复用updateCodeToBos似乎修改成本更大，故额外添加了此函数
func uploadCodeToBosFromUserBos(byteArray []byte, updateFunc *dao.Function) error {
	var err error
	if err = models.CheckCodeSize("update", updateFunc, byteArray); err != nil {
		return err
	}
	updateFunc.CodeSha256, updateFunc.SquashFsSha256, updateFunc.CodeSize, updateFunc.CodeID, err = global.AC.Clients.Code.FaasUploadCode(byteArray, updateFunc.FunctionName, updateFunc.Uid)
	if err != nil {
		err = models.OptimizeErrorCode("upload code to bos from user bos failed", err)
		return err
	}
	return nil
}

// 从用户BOS下载代码，并且返回[]byte以及错误代码
func DownloadCodeFromUserBos(bosClient *bosSdk.Client, bosBucket string, bosObject string) ([]byte, error) {
	meta, err := bosClient.GetObjectMeta(bosBucket, bosObject)
	if err != nil {
		return nil, apiErr.NewObjectMetaNotFoundException("", nil)
	}
	if uint64(meta.ContentLength) > api.DefaultCodeSizeLimitZipped {
		return nil, apiErr.NewObjectTooLargeException("", nil)
	}
	object, err := bosClient.BasicGetObject(bosBucket, bosObject)
	if err != nil {
		return nil, apiErr.NewObjectNotFoundException("", nil)
	}
	byteArray, err := ioutil.ReadAll(object.Body)
	if err != nil {
		return nil, err
	}
	return byteArray, nil
}

func GetFunctionService(newFunc *dao.Function, context *global.ApiserverContext) error {
	if newFunc.ServiceName == "" {
		newFunc.ServiceName = api.ServiceDefault
	}
	condService := new(dao.Service)
	condService.ServiceName = newFunc.ServiceName
	condService.Uid = newFunc.Uid
	condService.Region = newFunc.Region

	// 查找是否存在服务
	if rKunErr := context.Observer.NewStage(global.CheckServiceStage).Observe(dao.FindOneService(condService)); rKunErr != nil {
		// 如果是默认的项目自动创建
		if condService.ServiceName == api.ServiceDefault {
			newService := condService
			newService.Status = api.ServiceOnline
			if err := context.Observer.NewStage(global.CreateServiceStage).Observe(dao.CreateService(newService)); err == nil {
				return nil
			}
		}
		return rKunErr
	}
	return nil
}

func SetBlsLogStore(c *server.Context, f *dao.Function) {
	cli, err := NewBlsClientWrapper(c)
	if err != nil {
		logs.Errorf("init user bls client fail, err: %+v", err)
		return
	}
	// 如果日志选择上传bls，日志集为空，则为用户创建默认日志集CFC_logset_default
	if f.BlsLogSet == "" {
		// 查看默认日志集是否存在
		logStore, _ := bls.DescribeLogStore(api.BlsDefaultLogSet, cli)
		if logStore != nil && logStore.LogStoreName == api.BlsDefaultLogSet {
			// 默认日志集存在，则将BlsLogSet置为默认日志集
			f.BlsLogSet = logStore.LogStoreName
			return
		}
		// 默认日志集不存在，需新建
		err = bls.CreateLogStore(api.BlsDefaultLogSet, api.BlsDefaultRetention, cli)
		if err == nil {
			logs.Infof("create bls logstore success")
			// 创建成功，则将BlsLogSet置为默认日志集
			f.BlsLogSet = api.BlsDefaultLogSet
		} else {
			logs.Errorf("create bls logstore fail, err: %+v", err)
			// 创建失败，则将LogType置为默认值none
			f.LogType = "none"
		}
	}
	// 为logStore创建索引requestId
	CreateRequestIdIndex(f.BlsLogSet, cli)
}

// 更新 network_config 表
// 有新建记录、更新记录中的 vpc 配置、清除记录中 vpc 配置三种可能
func updateNetworkConfig(c *server.Context, updateFunc *dao.Function) error {
	if updateFunc.VpcConfig == nil {
		// 未变更 vpc config，不需要处理
		return nil
	}

	oldNetworkCfg, err := dao.FindOneNetworkConfig(&dao.NetworkConfig{
		NetworkConfig: api.NetworkConfig{
			Brn: updateFunc.FunctionBrn,
		},
	})
	if err != nil {
		return err
	}

	cond := dao.NetworkConfig{}
	cond.Brn = updateFunc.FunctionBrn

	// 处理删除 vpc 配置的情况，删除后 proxyctrl 健康检查判断是否不再占用 proxyNode
	cfg := updateFunc.VpcConfig
	if len(cfg.SubnetIDs) == 0 && len(cfg.SecurityGroupIDs) == 0 && len(cfg.VpcID) == 0 {
		// 传空数组表示删除vpcConfig
		updateFunc.VpcConfig = nil
		if oldNetworkCfg != nil && oldNetworkCfg.VpcConfigStr != nil && *oldNetworkCfg.VpcConfigStr != "" {
			// 存在 vpc 配置，现在给去掉
			if err = dao.DeleteNetworkConfig(nil, &cond); err != nil {
				return err
			}
			return nil
		}
	}

	// 处理创建或变更的情况
	newCfg, err := genNetworkConfig(updateFunc)
	if err != nil {
		return err
	}

	checkRepetitionCond := &dao.NetworkConfig{}
	checkRepetitionCond.VpcConfigStr = newCfg.VpcConfigStr
	checkRepetitionCond.Uid = newCfg.Uid
	existedCfg, err := dao.FindOneNetworkConfig(checkRepetitionCond)
	if err != nil {
		return err
	}

	// 如果该用户已存在相同 vpc 配置的其它函数，那么可以复用已有的 proxy node，因此无需去通知 proxyCtrl 再使用新的 proxy node 了
	// 如果不存在，则需要通知
	var shouldNotifyProxyCtrl = false
	if existedCfg != nil {
		if existedCfg.Brn == updateFunc.FunctionBrn {
			// 这里查到的 existedCfg 就是这个函数本身的 vpc 配置，说明更新传入了相同的 vpc 配置，返回即可
			return nil
		}

		newCfg.Status = existedCfg.Status
		newCfg.VpcCidr = existedCfg.VpcCidr
		newCfg.EnableSlave = existedCfg.EnableSlave
		if existedCfg.ProxyFloatingIP != nil {
			newCfg.ProxyFloatingIP = existedCfg.ProxyFloatingIP
		}
		if existedCfg.ProxyInternalIP != nil {
			newCfg.ProxyInternalIP = existedCfg.ProxyInternalIP
		}
	} else {
		shouldNotifyProxyCtrl = true
	}

	if oldNetworkCfg == nil {
		if err = dao.CreateNetworkConfig(nil, newCfg); err != nil {
			return err
		}
	} else {
		if err = dao.UpdateNetworkConfig(nil, cond, newCfg); err != nil {
			return err
		}
	}
	if shouldNotifyProxyCtrl {
		go func() {
			if err = global.AC.Clients.Proxy.AskForProxy(&newCfg.NetworkConfig); err != nil {
				c.Logger().Errorf("notify proxyctrl failed, err: %v", err)
			}
		}()
	}
	return nil
}
