package cloudfunc

import (
	"fmt"
	"net/http"
	"time"

	"github.com/emicklei/go-restful"

	"icode.baidu.com/baidu/faas/kun/pkg/api"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/dao"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/dbengine"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/global"
	"icode.baidu.com/baidu/faas/kun/pkg/bce/brn"
	kunErr "icode.baidu.com/baidu/faas/kun/pkg/error"
	"icode.baidu.com/baidu/faas/kun/pkg/middleware"
	"icode.baidu.com/baidu/faas/kun/pkg/server"
	"icode.baidu.com/baidu/faas/kun/pkg/server/endpoint"
	"icode.baidu.com/baidu/faas/kun/pkg/util/json"
)

type NetworkRest struct{}

// InsideAPIs xxx
func (f NetworkRest) InsideAPIs() []endpoint.ApiSingle {
	apis := []endpoint.ApiSingle{
		{
			Verb:    "GET",
			Path:    "/vpc/configs",
			Handler: server.WrapRestRouteFuncWithTrace("ApiServerHandler/insideListVpcConfigs", f.insideList),
			Filters: []restful.FilterFunction{
				middleware.SimpleAuthTokenVerify(),
			},
		},
		{
			Verb:    "POST",
			Path:    "/vpc/notify",
			Handler: server.WrapRestRouteFuncWithTrace("ApiServerHandler/NotifyProxyReady", f.notifyProxyReady),
			Filters: []restful.FilterFunction{
				middleware.SimpleAuthTokenVerify(),
			},
		},
	}
	return apis
}

func (n *NetworkRest) insideList(c *server.Context) {
	defer c.Logger().TimeTrack(time.Now(), "insideList")
	response := c.Response()
	ctx := global.BuildApiserverContext(c.Request(), c.Response(), "insideList")
	ctx.SpanContext = c.Context()

	res, err := dao.ListVpcConfigs()
	if err != nil {
		c.WithWarnLog(err).WriteTo(response)
		return
	}

	// 补齐 VpcConfig 参数
	for _, cfg := range res {
		cfg.VpcConfig = new(api.VpcConfig)
		if err = json.Unmarshal([]byte(*cfg.VpcConfigStr), cfg.VpcConfig); err != nil {
			c.WithWarnLog(kunErr.NewServiceException("", err)).WriteTo(response)
			return
		}
	}

	c.Response().WriteHeaderAndEntity(http.StatusOK, res)
}

func (n *NetworkRest) notifyProxyReady(c *server.Context) {
	defer c.Logger().TimeTrack(time.Now(), "notifyProxyReady")
	response := c.Response()
	ctx := global.BuildApiserverContext(c.Request(), c.Response(), "notifyProxyReady")
	ctx.SpanContext = c.Context()

	var cfg api.NetworkConfig
	if err := ctx.Request.ReadEntity(&cfg); err != nil {
		c.WithWarnLog(err).WriteTo(response)
		return
	}

	// 多个函数可能有相同的vpc配置，此次可能会更新多个 function
	cond := dao.NetworkConfig{}
	cond.VpcConfigStr = cfg.VpcConfigStr

	res, err := dao.FindNetworkConfigs(&cond)
	if err != nil {
		c.WithWarnLog(kunErr.NewServiceException(fmt.Sprintf("find brns failed, err: %v", err), nil)).WriteTo(response)
	}

	updatedCfg := &dao.NetworkConfig{}
	updatedCfg.VpcConfigStr = cfg.VpcConfigStr
	updatedCfg.ProxyInternalIP = cfg.ProxyInternalIP
	updatedCfg.ProxyFloatingIP = cfg.ProxyFloatingIP
	updatedCfg.Status = cfg.Status

	if err := dao.UpdateNetworkConfig(nil, cond, updatedCfg); err != nil {
		c.WithWarnLog(err).WriteTo(response)
		return
	}

	for _, nc := range res {
		brnStruct, _ := brn.ParseFunction(nc.Brn)
		fcMsg := api.NewFunctionCacheNotifyMsg().AddInfo(nc.Brn, brnStruct.Version, "update")
		dbengine.NotifyEtcdWhenChange(api.FunctionCacheEtcdPrefix, nc.Brn, fcMsg)
	}

	c.Response().WriteHeader(http.StatusOK)
}
