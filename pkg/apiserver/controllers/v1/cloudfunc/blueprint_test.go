package cloudfunc

import (
	"bytes"
	"net/http"
	"net/http/httptest"
	"strings"
	"testing"
	"unsafe"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/emicklei/go-restful"
	"github.com/stretchr/testify/assert"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/code"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/dbengine"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/global"
	"icode.baidu.com/baidu/faas/kun/pkg/bce/iam"
	"icode.baidu.com/baidu/faas/kun/pkg/server"
	"icode.baidu.com/baidu/faas/kun/pkg/util/json"
)

func TestList(t *testing.T) {
	global.MockAC()

	cases := []struct {
		in_c         *server.Context
		out_HttpCode int
	}{
		{
			in_c:         global.BuildNewKunCtx("GET", "/v1/blueprints", ``, "uiduid", map[string]string{}),
			out_HttpCode: 200,
		},
	}

	for _, tc := range cases {
		var m sqlmock.Sqlmock
		m, dbengine.Engine = global.MockDB()
		m.ExpectQuery(".*").WillReturnRows(global.GetRowsBlueprints(global.GetTestBlueprint(1)))
		m.ExpectQuery(".*").WillReturnRows(global.GetRowsBlueprints(global.GetTestBlueprint(2)))
		b := BlueprintRest{}
		b.list(tc.in_c)
		assert.Equal(t, tc.out_HttpCode, tc.in_c.Response().StatusCode())
	}
}

func TestFind(t *testing.T) {
	global.MockAC()
	code.MockCode()

	cases := []struct {
		in_c         *server.Context
		out_HttpCode int
	}{
		{
			in_c:         global.BuildNewKunCtx("GET", "/v1/blueprints", ``, "uiduid", map[string]string{"UUID": "00457f0b-20d8-4f3d-8555-c233c7ebd495"}),
			out_HttpCode: 200,
		},
	}
	for _, tc := range cases {
		var m sqlmock.Sqlmock
		m, dbengine.Engine = global.MockDB()
		m.ExpectQuery(".*").WillReturnRows(global.GetRowsBlueprints(global.GetTestBlueprint(1)))

		f := BlueprintRest{}
		f.find(tc.in_c)
		assert.Equal(t, tc.out_HttpCode, tc.in_c.Response().StatusCode())
	}
}

/*
1.初始化MockServer, dbengine
2.创建request，写入uuid、User信息、body等。
3.创建response，设置request accept类型
4.执行创建函数
5.关闭mockserver
*/
func TestCreateBlueprintFuncDueros(t *testing.T) {
	global.MockAC()
	code.MockCode()
	global.RegisterFaasRuntimeGovalidatorTag()

	var m sqlmock.Sqlmock
	m, dbengine.Engine = global.MockDB()

	expBp := global.GetTestBlueprint(1)
	expFunc := global.GetTestFunc(1)

	// dao.FindBlueprint
	req1 := "SELECT * FROM `blueprints` WHERE (`blueprints`.`uuid` = ?)"
	m.ExpectQuery(global.FixedFullRe(req1)).
		WillReturnRows(global.GetRowsBlueprints(expBp))

	m.ExpectQuery("SELECT").WillReturnRows(
		global.GetRowsRuntimeConfig(global.GetTestRuntimeConfig(1)))

	// dao.FindFunc
	m.ExpectQuery(".*").
		WillReturnRows(global.GetRowsFunctions(expFunc))

	m.ExpectQuery(".*").
		WillReturnRows(global.GetRowsLayer(global.GetTestLayers(1)))

	// dao.FindOneFunc
	req2 := "SELECT * FROM `functions` WHERE (`functions`.`function_brn` = ?) AND (deleted_at IS NULL OR deleted_at='0000-00-00 00:00:00') ORDER BY `functions`.`id` ASC LIMIT 1"
	m.ExpectQuery(global.FixedFullRe(req2)).
		WillReturnRows(global.GetRowsFunctions(global.GetTestFunc(0)))

	m.ExpectQuery("SELECT").WillReturnRows(global.GetRowsService(global.GetTestService(1)))

	m.ExpectBegin()
	// dao.CreateFunc
	req3 := "INSERT INTO"
	m.ExpectExec(req3).
		WillReturnResult(sqlmock.NewResult(1, 1))

	m.ExpectCommit()

	cr := map[string]string{
		"FunctionName": "test_dueros_function",
	}

	body := new(bytes.Buffer)
	json.NewEncoder(body).Encode(cr)
	bodyReader := strings.NewReader(body.String())
	request := httptest.NewRequest("POST", "/v1/blueprints/64c907c0-74e1-4b5e-9c60-34f9df76fb6d/function", bodyReader)
	request.Header.Add("Content-Type", restful.MIME_JSON)
	restReq := restful.NewRequest(request)

	uuidMap := make(map[string]string, 1)
	uuidMap["UUID"] = "64c907c0-74e1-4b5e-9c60-34f9df76fb6d"
	var uuid *map[string]string = (*map[string]string)(unsafe.Pointer(uintptr(unsafe.Pointer(restReq)) + uintptr(unsafe.Sizeof(restReq.Request))))
	*uuid = uuidMap

	domain := iam.Domain{
		ID:   "85ae1423f09f4660becb15d46402e9cd",
		Name: "PASSPORT:2500402428",
	}
	iamuser := iam.User{
		ID:     "85ae1423f09f4660becb15d46402e9cd",
		Name:   "PASSPORT:2500402428",
		Domain: &domain,
	}
	restReq.SetAttribute("User", &iamuser)

	response := httptest.NewRecorder()
	restRsp := restful.NewResponse(response)
	restRsp.SetRequestAccepts(restful.MIME_JSON)

	f := BlueprintRest{}
	f.createBlueprintFunc(server.BuildContext(restReq, restRsp, ""))

	if response.Code != http.StatusCreated {
		t.Error(1)
	}
}
