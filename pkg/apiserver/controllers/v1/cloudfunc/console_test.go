package cloudfunc

import (
	"bytes"
	"crypto/sha256"
	"crypto/tls"
	"fmt"
	"net/http"
	"net/http/httptest"
	"net/url"
	"strings"
	"testing"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/emicklei/go-restful"
	"github.com/stretchr/testify/assert"

	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/code"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/dbengine"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/global"
	"icode.baidu.com/baidu/faas/kun/pkg/bce/auth"
	"icode.baidu.com/baidu/faas/kun/pkg/bce/iam"
	"icode.baidu.com/baidu/faas/kun/pkg/server"
	"icode.baidu.com/baidu/faas/kun/pkg/util/json"
)

func TestConsoleInvitation(t *testing.T) {
	global.MockAC()

	hash := sha256.New()
	hash.Write([]byte("PASSPORT:**********"))
	account := fmt.Sprintf("%x", hash.Sum(nil))
	bceAuth := auth.NewBceAuth(
		"77b3184c8da340d0954eee0acc247f57",
		"4e24782423d342128636bb26773c01fe")
	headers := http.Header{}
	headers.Add("Host", "bce.baidu.com")
	params := url.Values{}
	params.Add("product", "CFC")
	params.Add("account", account)
	signature := bceAuth.NewSigner().Method("GET").Path("/").
		Headers(headers).Params(params).Expire(3600).
		WithSignedHeader().GetSign()
	fmt.Println(signature)

	invreq := InvitationRequest{
		Product:    "CFC",
		Account:    account,
		InviteAuth: signature,
	}
	body := new(bytes.Buffer)
	json.NewEncoder(body).Encode(invreq)
	fmt.Println(body.String())
	bodyReader := strings.NewReader(body.String())
	request := httptest.NewRequest("POST", "/v1/console/invitation", bodyReader)
	restReq := restful.NewRequest(request)
	domain := iam.Domain{
		ID:   "85ae1423f09f4660becb15d46402e9cd",
		Name: "PASSPORT:**********",
	}
	iamuser := iam.User{
		ID:     "85ae1423f09f4660becb15d46402e9cd",
		Name:   "PASSPORT:**********",
		Domain: &domain,
	}
	restReq.SetAttribute("User", &iamuser)
	response := httptest.NewRecorder()
	restRsp := restful.NewResponse(response)

	f := FunctionRest{}
	f.consoleInvitation(server.BuildContext(restReq, restRsp, ""))
	if response.Code != http.StatusOK {
		t.Error(1)
	}
}

func TestConsoleUpdate(t *testing.T) {
	global.MockAC()
	code.MockCode()
	global.RegisterFaasRuntimeGovalidatorTag()

	cases := []struct {
		in_c         *server.Context
		out_HttpCode int
	}{
		{
			in_c: global.BuildNewKunCtx("PUT", "/v1/console/functions/myfunc", "err Json BODY", "uiduid", map[string]string{
				"FunctionName": "myfunc",
			}),
			out_HttpCode: 400,
		},
		{
			//验证raw code 错误
			in_c: global.BuildNewKunCtx("PUT", "/v1/console/functions/myfunc", "{}", "uiduid", map[string]string{
				"FunctionName": "myfunc",
			}),
			out_HttpCode: 500,
		},
		{
			//验证function memSize/Runtime错误
			in_c: global.BuildNewKunCtx("PUT", "/v1/console/functions/myfunc", `{"name":"test_hello","qualifier":"$LATEST","_":"1527065368765","query":{"_":"1527065368765"},"CodeEntryType":1,"EnviromentVariables":[],"Handler":"index.handler","Id":11788,"Uid":"c7ac82ae14ef42d1a4ffa3b2ececa17f","Description":"","FunctionBrn":"brn:bce:cfc:bj:640c8817bd1de2928d47256dd0620ce5:function:test_hello:$LATEST","Region":"bj","Timeout":3,"VersionDesc":"","UpdatedAt":"2018-05-16T16:00:24+08:00","LastModified":"2018-05-16T16:00:24+08:00","CodeSha256":"/WqEwofcyEv6llzIWnLSfzZnlqNsr/tbJE52wwnKZ/k=","CodeSize":239,"FunctionArn":"brn:bce:cfc:bj:640c8817bd1de2928d47256dd0620ce5:function:test_hello:$LATEST","FunctionName":"test_hello","Version":"$LATEST","Runtime":"nod?ej.11","MemorySize":129,"Environment":{"Variables":{}},"CommitId":"9b227713-de61-469a-9a8a-e49a4cda9807","LogType":"none","EventSourceMap":[],"RawCode":"exports.handler = (event, context, callback) => {\n    console.log(\"ccccccm\");\n    callback(null, \"Hello world!\");\n};"}`, "uiduid", map[string]string{
				"FunctionName": "myfunc",
			}),
			out_HttpCode: 400,
		},
		{
			//验证user校验 错误
			in_c: global.BuildNewKunCtx("PUT", "/v1/console/functions/myfunc", `{"name":"test_hello","qualifier":"$LATEST","_":"1527065368765","query":{"_":"1527065368765"},"CodeEntryType":1,"EnviromentVariables":[],"Handler":"index.handler","Id":11788,"Uid":"c7ac82ae14ef42d1a4ffa3b2ececa17f","Description":"","CFSConfig":{"FsId": "cfs-7SvzfA1i6d"},"FunctionBrn":"brn:bce:cfc:bj:640c8817bd1de2928d47256dd0620ce5:function:test_hello:$LATEST","Region":"bj","Timeout":3,"VersionDesc":"","UpdatedAt":"2018-05-16T16:00:24+08:00","LastModified":"2018-05-16T16:00:24+08:00","CodeSha256":"/WqEwofcyEv6llzIWnLSfzZnlqNsr/tbJE52wwnKZ/k=","CodeSize":239,"FunctionArn":"brn:bce:cfc:bj:640c8817bd1de2928d47256dd0620ce5:function:test_hello:$LATEST","FunctionName":"test_hello","Version":"$LATEST","Runtime":"node?j.11","MemorySize":128,"Environment":{"Variables":{}},"CommitId":"9b227713-de61-469a-9a8a-e49a4cda9807","LogType":"none","EventSourceMap":[],"RawCode":"exports.handler = (event, context, callback) => {\n    console.log(\"ccccccm\");\n    callback(null, \"Hello world!\");\n};"}`, "", map[string]string{
				"FunctionName": "myfunc",
			}),
			out_HttpCode: 400,
		},
		{
			//验证version校验 错误
			in_c: global.BuildNewKunCtx("PUT", "/v1/console/functions/myfunc", `{"name":"test_hello","qualifier":"1","_":"1527065368765","query":{"_":"1527065368765"},"CodeEntryType":1,"EnviromentVariables":[],"Handler":"index.handler","Id":11788,"Uid":"c7ac82ae14ef42d1a4ffa3b2ececa17f","Description":"","FunctionBrn":"brn:bce:cfc:bj:640c8817bd1de2928d47256dd0620ce5:function:test_hello:1","Region":"bj","Timeout":3,"VersionDesc":"","UpdatedAt":"2018-05-16T16:00:24+08:00","LastModified":"2018-05-16T16:00:24+08:00","CodeSha256":"/WqEwofcyEv6llzIWnLSfzZnlqNsr/tbJE52wwnKZ/k=","CodeSize":239,"FunctionArn":"brn:bce:cfc:bj:640c8817bd1de2928d47256dd0620ce5:function:test_hello:1","FunctionName":"test_hello","Version":"1","Runtime":"node?j.11","MemorySize":128,"Environment":{"Variables":{}},"CommitId":"9b227713-de61-469a-9a8a-e49a4cda9807","LogType":"none","EventSourceMap":[],"RawCode":"exports.handler = (event, context, callback) => {\n    console.log(\"ccccccm\");\n    callback(null, \"Hello world!\");\n};"}`, "uiduid", map[string]string{
				"FunctionName": "myfunc",
			}),
			out_HttpCode: 400,
		},
		{
			in_c: global.BuildNewKunCtx("PUT", "/v1/console/functions/myfunc", `{"qualifier":"$LATEST","_":"1527065368765","query":{"_":"1527065368765"},"CodeEntryType":1,"Handler":"index.handler","Id":11788,"Uid":"c7ac82ae14ef42d1a4ffa3b2ececa17f","Description":"","FunctionBrn":"brn:bce:cfc:bj:640c8817bd1de2928d47256dd0620ce5:function:test_hello:$LATEST","Region":"bj","Timeout":3,"VersionDesc":"","UpdatedAt":"2018-05-16T16:00:24+08:00","LastModified":"2018-05-16T16:00:24+08:00","CodeSha256":"/WqEwofcyEv6llzIWnLSfzZnlqNsr/tbJE52wwnKZ/k=","CodeSize":239,"FunctionArn":"brn:bce:cfc:bj:640c8817bd1de2928d47256dd0620ce5:function:test_hello:$LATEST","FunctionName":"test_hello","Version":"$LATEST","Runtime":"nodejs6.11","DeadLetterTopic":"mytopic","MemorySize":128,"Environment":{"Variables":{}},"CommitId":"9b227713-de61-469a-9a8a-e49a4cda9807","LogType":"none","EventSourceMap":[],"RawCode":"exports.handler = (event, context, callback) => {\n    console.log(\"ccccccm\");\n    callback(null, \"Hello world!\");\n};"}`, "uiduid", map[string]string{
				"FunctionName": "myfunc",
			}),
			out_HttpCode: 200,
		},
		{
			in_c: global.BuildNewKunCtx("PUT", "/v1/console/functions/myfunc", `{"qualifier":"$LATEST","_":"1527065368765","query":{"_":"1527065368765"},"CodeEntryType":1,"Handler":"index.handler","Id":11788,"Uid":"c7ac82ae14ef42d1a4ffa3b2ececa17f","Description":"","FunctionBrn":"brn:bce:cfc:bj:640c8817bd1de2928d47256dd0620ce5:function:test_hello:$LATEST","Region":"bj","Timeout":3,"VersionDesc":"","UpdatedAt":"2018-05-16T16:00:24+08:00","LastModified":"2018-05-16T16:00:24+08:00","CodeSha256":"/WqEwofcyEv6llzIWnLSfzZnlqNsr/tbJE52wwnKZ/k=","CodeSize":239,"FunctionArn":"brn:bce:cfc:bj:640c8817bd1de2928d47256dd0620ce5:function:test_hello:$LATEST","FunctionName":"test_hello","Version":"$LATEST","Runtime":"nodejs6.11","DeadLetterTopic":"","MemorySize":128,"Environment":{"Variables":{}},"CommitId":"9b227713-de61-469a-9a8a-e49a4cda9807","LogType":"none","EventSourceMap":[],"RawCode":"exports.handler = (event, context, callback) => {\n    console.log(\"ccccccm\");\n    callback(null, \"Hello world!\");\n};"}`, "uiduid", map[string]string{
				"FunctionName": "myfunc",
			}),
			out_HttpCode: 200,
		},
	}
	for _, tc := range cases {
		var m sqlmock.Sqlmock
		m, dbengine.Engine = global.MockDB()
		m.ExpectQuery("SELECT").WillReturnRows(global.GetRowsFunctions(global.GetTestFunc(1)))
		m.ExpectQuery("SELECT").WillReturnRows(global.GetRowsFunctions(global.GetTestFunc(1)))
		m.ExpectExec("UPDATE").WillReturnResult(sqlmock.NewResult(1, 1))
		m.ExpectQuery("SELECT").WillReturnRows(global.GetRowsFunctions(global.GetTestFunc(1)))
		m.ExpectQuery("SELECT").WillReturnRows(global.GetRowsLayer(global.GetTestLayers(1)))
		m.ExpectQuery("SELECT").WillReturnRows(global.GetRowsFunctions(global.GetTestFunc(1)))

		f := FunctionRest{}
		f.consoleUpdate(tc.in_c)
		assert.Equal(t, tc.out_HttpCode, tc.in_c.Response().StatusCode())
	}
}

func TestConsoleNewFunc(t *testing.T) {
	global.MockAC()
	code.MockCode()
	global.RegisterFaasRuntimeGovalidatorTag()
	cases := []struct {
		in_c         *server.Context
		out_HttpCode int
		sqlFunc      func()
	}{
		{
			in_c:         global.BuildNewKunCtx("POST", "/v1/console/functions/myfunc", "err Json BODY", "uiduid", map[string]string{}),
			out_HttpCode: 400,
			sqlFunc: func() {
				var m sqlmock.Sqlmock
				m, dbengine.Engine = global.MockDB()
				m.ExpectQuery("SELECT").WillReturnRows(global.GetRowsRuntimeConfig(global.GetTestRuntimeConfig(1)))
			},
		},
		{
			//验证raw code 错误
			in_c:         global.BuildNewKunCtx("POST", "/v1/console/functions/myfunc", "{}", "uiduid", map[string]string{}),
			out_HttpCode: 500,
			sqlFunc: func() {
				var m sqlmock.Sqlmock
				m, dbengine.Engine = global.MockDB()
				m.ExpectQuery("SELECT").WillReturnRows(global.GetRowsRuntimeConfig(global.GetTestRuntimeConfig(1)))
			},
		},
		{
			//验证function memSize/Runtime错误
			in_c:         global.BuildNewKunCtx("POST", "/v1/console/functions/myfunc", `{"name":"test_hello","qualifier":"$LATEST","_":"1527065368765","query":{"_":"1527065368765"},"CodeEntryType":1,"EnviromentVariables":[],"Handler":"index.handler","Id":11788,"Uid":"c7ac82ae14ef42d1a4ffa3b2ececa17f","Description":"","FunctionBrn":"brn:bce:cfc:bj:640c8817bd1de2928d47256dd0620ce5:function:test_hello:$LATEST","Region":"bj","Timeout":3,"VersionDesc":"","UpdatedAt":"2018-05-16T16:00:24+08:00","LastModified":"2018-05-16T16:00:24+08:00","CodeSha256":"/WqEwofcyEv6llzIWnLSfzZnlqNsr/tbJE52wwnKZ/k=","CodeSize":239,"FunctionArn":"brn:bce:cfc:bj:640c8817bd1de2928d47256dd0620ce5:function:test_hello:$LATEST","FunctionName":"test_hello","Version":"$LATEST","Runtime":"nod?ej.11","MemorySize":129,"Environment":{"Variables":{}},"CommitId":"9b227713-de61-469a-9a8a-e49a4cda9807","LogType":"none","EventSourceMap":[],"RawCode":"exports.handler = (event, context, callback) => {\n    console.log(\"ccccccm\");\n    callback(null, \"Hello world!\");\n};"}`, "uiduid", map[string]string{}),
			out_HttpCode: 400,
			sqlFunc: func() {
				var m sqlmock.Sqlmock
				m, dbengine.Engine = global.MockDB()
				m.ExpectQuery("SELECT").WillReturnRows(global.GetRowsRuntimeConfig(global.GetTestRuntimeConfig(1)))
			},
		},
		{
			//验证user校验 错误
			in_c:         global.BuildNewKunCtx("POST", "/v1/console/functions/myfunc", `{"name":"test_hello","qualifier":"$LATEST","_":"1527065368765","query":{"_":"1527065368765"},"CodeEntryType":1,"EnviromentVariables":[],"Handler":"index.handler","Id":11788,"Uid":"c7ac82ae14ef42d1a4ffa3b2ececa17f","Description":"","FunctionBrn":"brn:bce:cfc:bj:640c8817bd1de2928d47256dd0620ce5:function:test_hello:$LATEST","Region":"bj","Timeout":3,"VersionDesc":"","UpdatedAt":"2018-05-16T16:00:24+08:00","LastModified":"2018-05-16T16:00:24+08:00","CodeSha256":"/WqEwofcyEv6llzIWnLSfzZnlqNsr/tbJE52wwnKZ/k=","CodeSize":239,"FunctionArn":"brn:bce:cfc:bj:640c8817bd1de2928d47256dd0620ce5:function:test_hello:$LATEST","FunctionName":"test_hello","Version":"$LATEST","Runtime":"nod?ej.11","MemorySize":129,"Environment":{"Variables":{}},"CommitId":"9b227713-de61-469a-9a8a-e49a4cda9807","LogType":"none","EventSourceMap":[],"RawCode":"exports.handler = (event, context, callback) => {\n    console.log(\"ccccccm\");\n    callback(null, \"Hello world!\");\n};"}`, "", map[string]string{}),
			out_HttpCode: 400,
			sqlFunc: func() {
				var m sqlmock.Sqlmock
				m, dbengine.Engine = global.MockDB()
				m.ExpectQuery("SELECT").WillReturnRows(global.GetRowsRuntimeConfig(global.GetTestRuntimeConfig(1)))
			},
		},
		{
			in_c:         global.BuildNewKunCtx("POST", "/v1/console/functions/myfunc", `{"name":"test_hello","qualifier":"$LATEST","_":"1527065368765","query":{"_":"1527065368765"},"CodeEntryType":1,"EnviromentVariables":[],"Handler":"index.handler","Id":11788,"Uid":"c7ac82ae14ef42d1a4ffa3b2ececa17f","Description":"","FunctionBrn":"brn:bce:cfc:bj:640c8817bd1de2928d47256dd0620ce5:function:test_hello:$LATEST","Region":"bj","Timeout":3,"VersionDesc":"","UpdatedAt":"2018-05-16T16:00:24+08:00","LastModified":"2018-05-16T16:00:24+08:00","CodeSha256":"/WqEwofcyEv6llzIWnLSfzZnlqNsr/tbJE52wwnKZ/k=","CodeSize":239,"FunctionArn":"brn:bce:cfc:bj:640c8817bd1de2928d47256dd0620ce5:function:test_hello:$LATEST","FunctionName":"test_hello","Version":"$LATEST","Runtime":"nodejs6.11","MemorySize":128,"Environment":{"Variables":{}},"CommitId":"9b227713-de61-469a-9a8a-e49a4cda9807","LogType":"none","EventSourceMap":[],"DeadLetterTopic":"myTopic","RawCode":"exports.handler = (event, context, callback) => {\n    console.log(\"ccccccm\");\n    callback(null, \"Hello world!\");\n};"}`, "uiduid", map[string]string{}),
			out_HttpCode: 201,
			sqlFunc: func() {
				req1 := "SELECT * FROM `functions`  WHERE (`functions`.`function_brn` = ?) AND (deleted_at IS NULL OR deleted_at='0000-00-00 00:00:00') ORDER BY `functions`.`id` ASC LIMIT 1"
				var m sqlmock.Sqlmock
				m, dbengine.Engine = global.MockDB()
				m.ExpectQuery("SELECT").WillReturnRows(global.GetRowsRuntimeConfig(global.GetTestRuntimeConfig(1)))
				m.ExpectQuery("SELECT").WillReturnRows(global.GetRowsFunctions(global.GetTestFunc(1)))
				m.ExpectQuery("SELECT").WillReturnRows(global.GetRowsLayer(global.GetTestLayers(1)))
				m.ExpectQuery(global.FixedFullRe(req1)).WillReturnRows(global.GetRowsFunctions(nil))
				m.ExpectQuery("SELECT").WillReturnRows(global.GetRowsService(global.GetTestService(1)))
				m.ExpectBegin()
				m.ExpectExec(".*").WillReturnResult(sqlmock.NewResult(1, 1))
				m.ExpectCommit()
			},
		},
		//{
		//	in_c:         global.BuildNewKunCtx("POST", "/v1/console/functions/myfunc", `{"name":"test_hello","qualifier":"$LATEST","_":"1527065368765","query":{"_":"1527065368765"},"CodeEntryType":1,"EnviromentVariables":[],"Handler":"index.handler","Id":11788,"Uid":"c7ac82ae14ef42d1a4ffa3b2ececa17f","Description":"","FunctionBrn":"brn:bce:cfc:bj:640c8817bd1de2928d47256dd0620ce5:function:test_hello:$LATEST","Region":"bj","Timeout":3,"VersionDesc":"","UpdatedAt":"2018-05-16T16:00:24+08:00","LastModified":"2018-05-16T16:00:24+08:00","CodeSha256":"/WqEwofcyEv6llzIWnLSfzZnlqNsr/tbJE52wwnKZ/k=","CodeSize":239,"FunctionArn":"brn:bce:cfc:bj:640c8817bd1de2928d47256dd0620ce5:function:test_hello:$LATEST","FunctionName":"test_hello","Version":"$LATEST","Runtime":"nodejs6.11","MemorySize":128,"Environment":{"Variables":{}},"CommitId":"9b227713-de61-469a-9a8a-e49a4cda9807","LogType":"none","EventSourceMap":[],"RawCode":"exports.handler = (event, context, callback) => {\n    console.log(\"ccccccm\");\n    callback(null, \"Hello world!\");\n};"}`, "uiduid", map[string]string{}),
		//	out_HttpCode: 201,
		//	sqlFunc: func() {
		//		var m sqlmock.Sqlmock
		//		m, dbengine.Engine = global.MockDB()
		//		m.ExpectQuery("SELECT").WillReturnRows(global.GetRowsRuntimeConfig(global.GetTestRuntimeConfig(1)))
		//		m.ExpectQuery("SELECT").WillReturnRows(global.GetRowsFunctions(global.GetTestFunc(1)))
		//		m.ExpectExec(".*").WillReturnResult(sqlmock.NewResult(1, 1))
		//	},
		//},
	}
	for _, tc := range cases {
		f := FunctionRest{}
		if tc.sqlFunc != nil {
			tc.sqlFunc()
		}
		f.consoleNewFunc(tc.in_c)
		assert.Equal(t, tc.out_HttpCode, tc.in_c.Response().StatusCode())
	}
}

func TestFindAllFuncs(t *testing.T) {
	global.MockAC()
	code.MockCode()

	cases := []struct {
		in_c         *server.Context
		out_HttpCode int
	}{
		{
			in_c:         global.BuildNewKunCtx("GET", "/v1/console/functions/myfunc", "", "", map[string]string{"FunctionName": "myfunc"}),
			out_HttpCode: 400,
		},
		{
			in_c:         global.BuildNewKunCtx("GET", "/v1/console/functions/myfunc", "", "uiduid", map[string]string{"FunctionName": "myfunc"}),
			out_HttpCode: 200,
		},
	}
	for _, tc := range cases {
		var m sqlmock.Sqlmock
		m, dbengine.Engine = global.MockDB()
		m.ExpectQuery(".*").WillReturnRows(global.GetRowsFunctions(global.GetTestFunc(1)))
		m.ExpectQuery(".*").WillReturnRows(global.GetRowsNetworkConfigs(global.GetTestNetworkConfig(1)))
		m.ExpectQuery(".*").WillReturnRows(global.GetRowsFunctions(global.GetTestFunc(2)))
		m.ExpectQuery(".*").WillReturnRows(global.GetRowsEventSource(global.GetTestEventSource(1)))
		f := FunctionRest{}

		tlsClientConfig := http.DefaultTransport.(*http.Transport).TLSClientConfig
		http.DefaultTransport.(*http.Transport).TLSClientConfig = &tls.Config{InsecureSkipVerify: true}

		f.findFuncALL(tc.in_c)

		http.DefaultTransport.(*http.Transport).TLSClientConfig = tlsClientConfig
		assert.Equal(t, tc.out_HttpCode, tc.in_c.Response().StatusCode())
	}
}

func TestConsoleAssistAuthorization(t *testing.T) {
	global.MockAC()

	// if os.Getenv("TestWithConsole") == "" {
	// 	t.Skip("TestConsoleInvitation")
	// }

	hash := sha256.New()
	hash.Write([]byte("PASSPORT:**********"))
	account := fmt.Sprintf("%x", hash.Sum(nil))
	bceAuth := auth.NewBceAuth(
		"77b3184c8da340d0954eee0acc247f57",
		"4e24782423d342128636bb26773c01fe")
	headers := http.Header{}
	headers.Add("Host", "bce.baidu.com")
	params := url.Values{}
	callbackUrl := "http://www.baidu.com"
	params.Add("product", "duedge")
	params.Add("user", account)
	params.Add("ori_url", callbackUrl)
	signature := bceAuth.NewSigner().Method("GET").Path("/").
		Headers(headers).Params(params).Expire(3600).
		WithSignedHeader().GetSign()
	fmt.Println(signature)

	reqAssist := AssistAuthorizationRequest{
		Product: "duedge",
		User:    account,
		Auth:    signature,
		OriUrl:  callbackUrl,
	}
	body := new(bytes.Buffer)
	json.NewEncoder(body).Encode(reqAssist)
	fmt.Println(body.String())
	bodyReader := strings.NewReader(body.String())
	request := httptest.NewRequest("POST", "/v1/console/assist/authorization", bodyReader)
	restReq := restful.NewRequest(request)
	domain := iam.Domain{
		ID:   "85ae1423f09f4660becb15d46402e9cd",
		Name: "PASSPORT:**********",
	}
	iamuser := iam.User{
		ID:     "85ae1423f09f4660becb15d46402e9cd",
		Name:   "PASSPORT:**********",
		Domain: &domain,
	}
	restReq.SetAttribute("User", &iamuser)
	response := httptest.NewRecorder()
	restRsp := restful.NewResponse(response)
	restRsp.SetRequestAccepts(restful.MIME_JSON)

	f := FunctionRest{}
	f.consoleAssistAuthorization(server.BuildContext(restReq, restRsp, ""))
	t.Logf("%s", response.Body)
	if response.Code != http.StatusOK {
		t.Error(1)
	}
}

func TestSetLogQL(t *testing.T) {
	exc := []string{
		"{functionName=\"functionName\", qualifier=\"qualifier\"}",
		"{functionName=\"functionName\", qualifier=\"qualifier\", status=\"status\"}",
		"{functionName=\"functionName\", qualifier=\"qualifier\"}|=\"filter\"",
	}

	cases := []struct {
		functionName string
		qualifier    string
		status       string
		filters      string
	}{
		// 直接查询，不含status
		{
			functionName: "functionName",
			qualifier:    "qualifier",
		},

		// 加入status查询
		{
			functionName: "functionName",
			qualifier:    "qualifier",
			status:       "status",
		},

		// 查询，加入logQL过滤条件
		{
			functionName: "functionName",
			qualifier:    "qualifier",
			filters:      "filter",
		},
	}

	for i := 0; i < len(exc); i++ {
		assert.Equal(t, exc[i], setLogQL(cases[i].functionName, cases[i].status, cases[i].filters, cases[i].qualifier))
	}
}

func TestConsoleViewLog(t *testing.T) {
	global.MockAC()
	var m sqlmock.Sqlmock
	m, dbengine.Engine = global.MockDB()
	fr := &FunctionRest{}

	cases := []struct {
		c        *server.Context
		httpCode int
		sqlFunc  func()
	}{
		// case 1
		// 不加入时间限制
		// 预计返回400
		{
			c: global.BuildNewKunCtx(
				"GET",
				"/v1/console/functions/test-func/log",
				"",
				"uiduid",
				map[string]string{},
			),
			httpCode: 400,
			sqlFunc: func() {
				f := global.GetTestFunc(1)
				f[0].LogType = "loki"
				nc := global.GetTestNetworkConfig(1)
				m.ExpectQuery(".*").WillReturnRows(global.GetRowsNetworkConfigs(nc))
				m.ExpectQuery(".*").WillReturnRows(global.GetRowsFunctions(f))
			},
		},
		// case 2
		// qualifier参数不存在
		// 预计返回400
		{
			c: global.BuildNewKunCtx(
				"GET",
				"/v1/console/functions/test-func/log?start=123&end=123",
				"",
				"uiduid",
				map[string]string{},
			),
			httpCode: 400,
			sqlFunc: func() {
				f := global.GetTestFunc(1)
				f[0].LogType = "loki"
				nc := global.GetTestNetworkConfig(1)
				m.ExpectQuery(".*").WillReturnRows(global.GetRowsNetworkConfigs(nc))
				m.ExpectQuery(".*").WillReturnRows(global.GetRowsFunctions(f))
			},
		},
		// case 3
		// uid为空
		// 404
		// 此处会触发404，因为在init function的时候就会报错
		{
			c: global.BuildNewKunCtx(
				"GET",
				"/v1/console/functions/test-func/log?start=123&end=123",
				"",
				"",
				map[string]string{},
			),
			httpCode: 404,
			sqlFunc: func() {
				f := global.GetTestFunc(1)
				f[0].LogType = "loki"
				nc := global.GetTestNetworkConfig(1)
				m.ExpectQuery(".*").WillReturnRows(global.GetRowsNetworkConfigs(nc))
				m.ExpectQuery(".*").WillReturnRows(global.GetRowsFunctions(f))
			},
		},
		// Limit out of range . => 404
		{
			c: global.BuildNewKunCtx(
				"GET",
				"/v1/console/functions/test-func/log?start=123&end=123&qualifier=$LATEST&limit=8888888888888888888888888888",
				"",
				"uiduid",
				map[string]string{},
			),
			httpCode: 400,
			sqlFunc:  nil,
		},
		// limit <= 0
		{
			c: global.BuildNewKunCtx(
				"GET",
				"/v1/console/functions/test-func/log?start=123&end=123&qualifier=$LATEST&limit=-1",
				"",
				"uiduid",
				map[string]string{},
			),
			httpCode: 400,
			sqlFunc: func() {
				f := global.GetTestFunc(1)
				f[0].LogType = "loki"
				nc := global.GetTestNetworkConfig(1)
				m.ExpectQuery(".*").WillReturnRows(global.GetRowsNetworkConfigs(nc))
				m.ExpectQuery(".*").WillReturnRows(global.GetRowsFunctions(f))
			},
		},
	}

	for _, v := range cases {
		if v.sqlFunc != nil {
			v.sqlFunc()
		}
		fr.consoleViewLog(v.c)
		assert.Equal(t, v.httpCode, v.c.Response().StatusCode())
	}
}
