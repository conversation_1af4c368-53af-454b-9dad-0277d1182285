package cloudfunc

import (
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/global"
	"net/http"
	"testing"

	"github.com/stretchr/testify/assert"
)

// TestPublicAPIs 本文件第一个被执行的case
func TestAPIs(t *testing.T) {
	BlueprintRest{}.PublicAPIs()
	RuntimeRest{}.APIs()
	AliasResource{}.APIs()
	FunctionRest{}.APIs()
	FunctionRest{}.ConsoleAPIs()
	FunctionRest{}.InviteAPIs()
	FunctionRest{}.InsideAPIs()
	VersionResource{}.APIs()
}

func TestRuntimefind(t *testing.T) {
	global.MockAC()
	global.MockDB()
	f := RuntimeRest{}
	c := global.BuildNewKunCtx("GET", "/inside-v1/runtimes/nodejs6.11/configuration", "", "", map[string]string{
		"RuntimeName": "nodejs6.11",
	})
	f.find(c)
	assert.Equal(t, http.StatusInternalServerError, c.Response().StatusCode())
}
