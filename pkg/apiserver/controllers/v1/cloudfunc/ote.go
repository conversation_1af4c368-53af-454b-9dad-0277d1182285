package cloudfunc

import (
	"net/http"

	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/global"

	"icode.baidu.com/baidu/faas/kun/pkg/api"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/dao"
	"icode.baidu.com/baidu/faas/kun/pkg/bce/brn"
	"icode.baidu.com/baidu/faas/kun/pkg/server"
)

func (f FunctionRest) oteFind(c *server.Context) {
	// TODO change 数据结构
	response := c.Response()

	ctx := global.BuildApiserverContext(c.Request(), c.Response(), "oteFind")
	defer global.WriteSummary(ctx, api.LogNotToSummary)

	if err := ctx.Observer.NewStage(global.CheckParamsStage).Observe(OTECheck(c)); err != nil {
		c.WithWarnLog(err).WriteTo(response)
		return
	}

	cfcBrn, _ := brn.Parse(c.Request().PathParameter("FunctionName"))
	findFunc := new(dao.Function)
	findFunc.FunctionBrn = cfcBrn.String()
	ctx.Context.FunctionBrn = findFunc.FunctionBrn
	if rkunErr := ctx.Observer.NewStage(global.CheckFunctionStage).Observe(dao.FindOneFunc(findFunc)); rkunErr != nil {
		c.WithWarnLog(rkunErr).WriteTo(response)
		return
	}
	findFunc.DealResFunction()

	var (
		urlTmp, accountReservedSumTmp interface{}
		err                           error
	)
	if urlTmp, err = ctx.Observer.NewStage(global.DownloadCodeStage).ObserveObject(global.AC.Clients.Code.FaasCodeDownloadUrl(findFunc.FunctionName, findFunc.Uid, findFunc.CodeSha256, findFunc.CodeID)); err != nil {
		c.Logger().Errorf("get download url failed, [func: %v][errmsg:%s]", findFunc, err.Error())
	}

	url := urlTmp.(string)
	var logConfig *api.LogConfiguration
	if findFunc.LogType == "bos" {
		logConfig = &api.LogConfiguration{
			LogType: "bos",
			BosDir:  findFunc.LogBosDir,
		}
	} else {
		logConfig = nil
	}

	if accountReservedSumTmp, err = ctx.Observer.NewStage(global.CountReservedConcurrencyStage).ObserveObject(dao.CountUserReservedConcurrency(c.Context(), findFunc.Uid)); err != nil {
		c.Logger().Errorf("get user reserved concurrency sum failed, [errmsg:%s]", err.Error())
	}

	con := &api.Concurrency{
		AccountReservedSum: accountReservedSumTmp.(int),
	}

	if *findFunc.ReservedConcurrentExecutions != 0 {
		v := int64(*findFunc.ReservedConcurrentExecutions)
		con.ReservedConcurrentExecutions = &v
	}

	response.WriteHeaderAndEntity(http.StatusOK, map[string]interface{}{
		"Code":          api.CodeStorage{Location: url},
		"Concurrency":   con,
		"Configuration": findFunc,
		"LogConfig":     logConfig,
	})
}

func (r RuntimeRest) oteFind(c *server.Context) {
	response := c.Response()

	err := OTESimpleCheck(c)
	if err != nil {
		c.WithWarnLog(err).WriteTo(response)
		return
	}

	ctx := global.BuildApiserverContext(c.Request(), c.Response(), "ote_runtime_conf_find")
	defer global.WriteSummary(ctx, api.LogNotToSummary)

	rName := c.Request().PathParameter("RuntimeName")
	RuntimeConfigRes := new(dao.RuntimeConfig)
	RuntimeConfigRes.Name = rName

	if rkunErr := ctx.Observer.NewStage(global.QueryDatabaseStage).Observe(dao.FindRuntimeConf(RuntimeConfigRes)); rkunErr != nil {
		c.WithWarnLog(rkunErr).WriteTo(c.Response())
		return
	}
	RuntimeConfigRes.DealRuntimeConfiguration()
	c.Response().WriteHeaderAndEntity(http.StatusOK, RuntimeConfigRes)
}
