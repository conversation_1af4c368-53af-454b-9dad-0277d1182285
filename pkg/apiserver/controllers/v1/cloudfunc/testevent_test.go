package cloudfunc

import (
	"github.com/DATA-DOG/go-sqlmock"
	"github.com/stretchr/testify/assert"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/dbengine"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/global"
	"icode.baidu.com/baidu/faas/kun/pkg/server"
	"testing"
)

func TestCreateUserEvent(t *testing.T) {
	global.MockAC()
	cases := []struct {
		in_c         *server.Context
		out_HttpCode int
	}{
		{
			in_c:         global.BuildNewKunCtx("POST", "/testevent/userevent", `{"FunctionName":"myFunc","Title":"test1","Event":"{\"key1\":\"value1\"}"}`, "123", map[string]string{}),
			out_HttpCode: 201,
		},
		{
			in_c:         global.BuildNewKunCtx("POST", "/testevent/userevent", "err <PERSON><PERSON> BODY", "123", map[string]string{}),
			out_HttpCode: 400,
		},
		{
			in_c:         global.BuildNewKunCtx("POST", "/testevent/userevent", `{"FunctionName":"myFunc","Title":"test1","Event":"{\"key1\":\"value1\"}"}`, "", map[string]string{}),
			out_HttpCode: 404,
		},
		{
			in_c:         global.BuildNewKunCtx("POST", "/testevent/userevent", `{"FunctionName":"myFunc","Title":"test1","Event":"{\"key1\":\"value1\"}"}`, "123", map[string]string{}),
			out_HttpCode: 201,
		},
		{
			in_c:         global.BuildNewKunCtx("POST", "/testevent/userevent", `{"FunctionName":"myFunc","Title":"test1","Event":"{\"key1\":\"value1\"},"}`, "123", map[string]string{}),
			out_HttpCode: 400,
		},
		{
			in_c:         global.BuildNewKunCtx("POST", "/testevent/userevent", `{"FunctionName":"myFunc","Title":"!qwer","Event":"{\"key1\":\"value1\"}"}`, "123", map[string]string{}),
			out_HttpCode: 400,
		},
		{
			in_c:         global.BuildNewKunCtx("POST", "/testevent/userevent", `{"FunctionName":"myFunc","Title":"1asdaASDASWA1234erdfDFERTs","Event":"{\"key1\":\"value1\"}"}`, "123", map[string]string{}),
			out_HttpCode: 400,
		},
	}
	for _, tc := range cases {
		var m sqlmock.Sqlmock
		m, dbengine.Engine = global.MockDB()
		m.ExpectQuery(".*").WillReturnRows(global.GetRowsFunctions(global.GetTestFunc(1)))
		m.ExpectQuery(".*").WillReturnRows(global.GetRowsUserTestEvents(global.GetUserTestEvents(1)))
		m.ExpectExec(".*").WillReturnResult(sqlmock.NewResult(1, 1))
		e := TestEvent{}
		e.createUserTestEvent(tc.in_c)
		assert.Equal(t, tc.out_HttpCode, tc.in_c.Response().StatusCode())
	}
}

func TestGetUserTestEvent(t *testing.T) {
	global.MockAC()
	var m sqlmock.Sqlmock
	m, dbengine.Engine = global.MockDB()
	m.ExpectQuery(".*").WillReturnRows(global.GetRowsUserTestEvents(global.GetUserTestEvents(1)))
	cases := []struct {
		in_c         *server.Context
		out_HttpCode int
	}{
		{
			in_c:         global.BuildNewKunCtx("GET", "/testevent/userevents?FunctionName=myFunc", ``, "123", map[string]string{}),
			out_HttpCode: 500,
		},
		{
			in_c:         global.BuildNewKunCtx("GET", "/testevent/userevents", ``, "123", map[string]string{}),
			out_HttpCode: 400,
		},
		{
			in_c:         global.BuildNewKunCtx("GET", "/testevent/userevents?FunctionName=myFunc", ``, "", map[string]string{}),
			out_HttpCode: 404,
		},
	}
	for _, tc := range cases {
		e := TestEvent{}
		e.getUserTestEvent(tc.in_c)
		assert.Equal(t, tc.out_HttpCode, tc.in_c.Response().StatusCode())
	}
}

func TestGetOneUserTestEvent(t *testing.T) {
	global.MockAC()

	cases := []struct {
		in_c         *server.Context
		out_HttpCode int
	}{
		{
			in_c:         global.BuildNewKunCtx("GET", "/testevent/userevent", ``, "123", map[string]string{"UUID": "2311ff9e-58ea-451f-9956-cd62883df022"}),
			out_HttpCode: 200,
		},
		{
			in_c:         global.BuildNewKunCtx("GET", "/testevent/userevent", ``, "123", map[string]string{"UUID": ""}),
			out_HttpCode: 400,
		},
		{
			in_c:         global.BuildNewKunCtx("GET", "/testevent/userevent", ``, "", map[string]string{"UUID": "2311ff9e-58ea-451f-9956-cd62883df022"}),
			out_HttpCode: 404,
		},
	}
	for _, tc := range cases {
		var m sqlmock.Sqlmock
		m, dbengine.Engine = global.MockDB()
		m.ExpectQuery(".*").WillReturnRows(global.GetRowsUserTestEvents(global.GetUserTestEvents(1)))
		e := TestEvent{}
		e.getOneUserTestEvent(tc.in_c)
		assert.Equal(t, tc.out_HttpCode, tc.in_c.Response().StatusCode())
	}
}

func TestUpdateUserTestEvent(t *testing.T) {
	global.MockAC()

	cases := []struct {
		in_c         *server.Context
		out_HttpCode int
	}{
		{
			in_c:         global.BuildNewKunCtx("PUT", "/testevent/userevent", `{"Event":"{\"key1\":\"value1\"}"}`, "123", map[string]string{"UUID": "2311ff9e-58ea-451f-9956-cd62883df022"}),
			out_HttpCode: 200,
		},
		{
			in_c:         global.BuildNewKunCtx("PUT", "/testevent/userevent", `{"Event":"{\"key1\":\"value1\"}"}`, "123", map[string]string{"UUID": ""}),
			out_HttpCode: 400,
		},
		{
			in_c:         global.BuildNewKunCtx("PUT", "/testevent/userevent", `{"Event":"{\"key1\":\"value1\"}"}`, "", map[string]string{"UUID": "2311ff9e-58ea-451f-9956-cd62883df022"}),
			out_HttpCode: 404,
		},
	}
	for _, tc := range cases {
		var m sqlmock.Sqlmock
		m, dbengine.Engine = global.MockDB()
		m.ExpectQuery(".*").WillReturnRows(global.GetRowsUserTestEvents(global.GetUserTestEvents(1)))
		m.ExpectExec(".*").WillReturnResult(sqlmock.NewResult(1, 1))
		e := TestEvent{}
		e.updateUserTestEvent(tc.in_c)
		assert.Equal(t, tc.out_HttpCode, tc.in_c.Response().StatusCode())
	}
}

func TestDeleteUserTestEvent(t *testing.T) {
	global.MockAC()

	cases := []struct {
		in_c         *server.Context
		out_HttpCode int
	}{
		{
			in_c:         global.BuildNewKunCtx("DELETE", "/testevent/userevent", ``, "123", map[string]string{"UUID": "2311ff9e-58ea-451f-9956-cd62883df022"}),
			out_HttpCode: 204,
		},
		{
			in_c:         global.BuildNewKunCtx("DELETE", "/testevent/userevent", ``, "123", map[string]string{}),
			out_HttpCode: 400,
		},
		{
			in_c:         global.BuildNewKunCtx("DELETE", "/testevent/userevent", ``, "", map[string]string{"UUID": "2311ff9e-58ea-451f-9956-cd62883df022"}),
			out_HttpCode: 404,
		},
	}
	for _, tc := range cases {
		var m sqlmock.Sqlmock
		m, dbengine.Engine = global.MockDB()
		m.ExpectQuery(".*").WillReturnRows(global.GetRowsUserTestEvents(global.GetUserTestEvents(1)))
		m.ExpectExec("^DELETE (.+)").WillReturnResult(sqlmock.NewResult(0, 0))
		e := TestEvent{}
		e.deleteUserTestEvent(tc.in_c)
		assert.Equal(t, tc.out_HttpCode, tc.in_c.Response().StatusCode())
	}
}

func TestGetStandardTestEvent(t *testing.T) {
	global.MockAC()

	cases := []struct {
		in_c         *server.Context
		out_HttpCode int
	}{
		{
			in_c:         global.BuildNewKunCtx("GET", "/testevent/standardevent?Status=1", "", "123", map[string]string{}),
			out_HttpCode: 200,
		},
		{
			in_c:         global.BuildNewKunCtx("GET", "/testevent/standardevent?Status=0", "", "123", map[string]string{}),
			out_HttpCode: 400,
		},
	}
	for _, tc := range cases {
		var m sqlmock.Sqlmock
		m, dbengine.Engine = global.MockDB()
		m.ExpectQuery(".*").WillReturnRows(global.GetRowsStandardTestEvents(global.GetStandardTestEvents(1)))
		e := TestEvent{}
		e.getStandardTestEvent(tc.in_c)
		assert.Equal(t, tc.out_HttpCode, tc.in_c.Response().StatusCode())
	}
}

func TestGetOneStandardTestEvent(t *testing.T) {
	global.MockAC()

	cases := []struct {
		in_c         *server.Context
		out_HttpCode int
	}{
		{
			in_c:         global.BuildNewKunCtx("GET", "/testevent/standardevent", "", "123", map[string]string{"ID": "1"}),
			out_HttpCode: 200,
		},
		{
			in_c:         global.BuildNewKunCtx("GET", "/testevent/standardevent", "", "123", map[string]string{"ID": "0"}),
			out_HttpCode: 400,
		},
		{
			in_c:         global.BuildNewKunCtx("GET", "/testevent/standardevent", "", "123", map[string]string{"ID": ""}),
			out_HttpCode: 400,
		},
	}
	for _, tc := range cases {
		var m sqlmock.Sqlmock
		m, dbengine.Engine = global.MockDB()
		m.ExpectQuery(".*").WillReturnRows(global.GetRowsStandardTestEvents(global.GetStandardTestEvents(1)))
		e := TestEvent{}
		e.getOneStandardTestEvent(tc.in_c)
		assert.Equal(t, tc.out_HttpCode, tc.in_c.Response().StatusCode())
	}
}
