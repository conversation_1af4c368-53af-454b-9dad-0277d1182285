package cloudfunc

import (
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/dao"
	"net/http"

	"icode.baidu.com/baidu/faas/kun/pkg/api"

	apiErr "icode.baidu.com/baidu/faas/kun/pkg/apiserver/error"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/global"
	"icode.baidu.com/baidu/faas/kun/pkg/server"
)

type CfcEdgeRest struct {
	Path string
}

func (r CfcEdgeRest) ConsoleGetUserActivation(c *server.Context) {
	response := c.Response()

	ctx := global.BuildApiserverContext(c.Request(), c.Response(), "ConsoleGetUserActivation")

	user, err := GetUser(c.Request())
	if err != nil {
		c.WithErrorLog(apiErr.NewInitFuncMetaException(err.Error(), "", err)).WriteTo(response)
		return
	}
	var whiteStatus, enabled bool
	whiteStatus, err = global.AC.Clients.Ops.CheckUserACL(user.Domain.ID, api.CFCEdgeFeatureType)
	if err != nil {
		c.WithErrorLog(apiErr.NewInitFuncMetaException("get cfc@edge white list failed", "", err)).WriteTo(response)
		return
	}

	edgeActivation := &dao.FeatureActivation{
		Type:   api.FeatureTypeCfcEdge,
		Status: api.UserFeatureActivated,
		Uid:    user.Domain.ID,
	}
	err = ctx.Observer.NewStage(global.QueryDatabaseStage).Observe(dao.FindOneFeatureActivation(edgeActivation))
	if err == nil {
		enabled = true
	}
	res := api.FeatureActivationResponse{
		Enabled:     enabled,
		InWhitelist: whiteStatus,
	}
	defer global.WriteSummary(ctx, api.LogNotToSummary)
	response.WriteHeaderAndEntity(http.StatusOK, res)
}

func (r CfcEdgeRest) ConsoleUserFeatureActivate(c *server.Context) {
	response := c.Response()

	ctx := global.BuildApiserverContext(c.Request(), c.Response(), "ConsoleUserFeatureActivate")

	user, err := GetUser(c.Request())
	if err != nil {
		c.WithErrorLog(apiErr.NewInitFuncMetaException(err.Error(), "", err)).WriteTo(response)
		return
	}
	status, err := global.AC.Clients.Ops.CheckUserACL(user.Domain.ID, api.CFCEdgeFeatureType)
	if err != nil {
		c.WithErrorLog(apiErr.NewInitFuncMetaException("get cfc@edge white list failed", "", err)).WriteTo(response)
		return
	}
	if status != true {
		c.WithErrorLog(apiErr.NewInitFuncMetaException("user not in white list", "", err)).WriteTo(response)
		return
	}
	edgeActivation := &dao.FeatureActivation{
		Uid:  user.Domain.ID,
		Type: api.FeatureTypeCfcEdge,
	}

	res := make([]dao.FeatureActivation, 0)
	var (
		resTmp interface{}
	)
	if resTmp, err = ctx.Observer.NewStage(global.FindFeatureActivationListStage).ObserveObject(dao.FindFeatureActivation(edgeActivation)); err != nil {
		edgeActivation.Status = api.UserFeatureActivated
		if err = ctx.Observer.NewStage(global.CreateFeatureActivationStage).Observe(dao.CreateFeatureActivation(edgeActivation)); err != nil {
			c.WithErrorLog(apiErr.NewInitFuncMetaException("activate cfc@edge failed", "", err)).WriteTo(response)
			return
		}
		response.WriteHeaderAndEntity(http.StatusOK, nil)
		return
	}
	res = resTmp.([]dao.FeatureActivation)
	if res[0].Status == api.UserFeatureActivated {
		c.WithErrorLog(apiErr.NewInitFuncMetaException("user has activated cfc@edge", "", err)).WriteTo(response)
		return
	} else {
		if err := ctx.Observer.NewStage(global.UpdateFeatureActivationStatusStage).Observe(dao.UpdateFeatureActivationStatus(*edgeActivation, api.UserFeatureActivated)); err != nil {
			c.WithErrorLog(apiErr.NewInitFuncMetaException("activate cfc@edge failed", "", err)).WriteTo(response)
			return
		}
	}

	defer global.WriteSummary(ctx, api.LogNotToSummary)
	response.WriteHeaderAndEntity(http.StatusOK, nil)
}

func (r CfcEdgeRest) ConsoleCancelUserFeatureActivation(c *server.Context) {
	response := c.Response()

	ctx := global.BuildApiserverContext(c.Request(), c.Response(), "ConsoleCancelUserFeatureActivation")

	user, err := GetUser(c.Request())
	if err != nil {
		c.WithErrorLog(apiErr.NewInitFuncMetaException(err.Error(), "", err)).WriteTo(response)
		return
	}
	edgeActivation := &dao.FeatureActivation{
		Uid:    user.Domain.ID,
		Type:   api.FeatureTypeCfcEdge,
		Status: api.UserFeatureActivated,
	}

	var resTmp interface{}
	res := make([]dao.FeatureActivation, 0)
	if resTmp, err = ctx.Observer.NewStage(global.FindFeatureActivationListStage).ObserveObject(dao.FindFeatureActivation(edgeActivation)); err != nil {
		c.WithErrorLog(apiErr.NewInitFuncMetaException("user has not activated cfc@edge feature", "", err)).WriteTo(response)
		return
	}

	res = resTmp.([]dao.FeatureActivation)
	cond := dao.FeatureActivation{
		ID:     res[0].ID,
		Uid:    user.Domain.ID,
		Type:   api.FeatureTypeCfcEdge,
		Status: api.UserFeatureActivated,
	}

	if err := ctx.Observer.NewStage(global.UpdateFeatureActivationStatusStage).Observe(dao.UpdateFeatureActivationStatus(cond, api.UserFeatureInactive)); err != nil {
		c.WithErrorLog(apiErr.NewInitFuncMetaException("close user cfc@edge activation failed", "", err)).WriteTo(response)
		return
	}

	defer global.WriteSummary(ctx, api.LogNotToSummary)
	response.WriteHeaderAndEntity(http.StatusOK, nil)
}
