package cloudfunc

import (
	"fmt"
	"net/http"
	"testing"

	sqlmock "github.com/DATA-DOG/go-sqlmock"
	"github.com/jinzhu/gorm"
	"github.com/stretchr/testify/assert"
	code "icode.baidu.com/baidu/faas/kun/pkg/apiserver/code"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/dbengine"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/global"
	"icode.baidu.com/baidu/faas/kun/pkg/server"
	"icode.baidu.com/baidu/faas/kun/pkg/util/base64"
)

func TestPublicAPIs(t *testing.T) {
	l := LayerRest{}
	res := l.PublicAPIs()
	assert.Equal(t, 6, len(res))

}

func TestPublishLayerVersion(t *testing.T) {
	global.MockAC()
	code.MockCode()
	global.RegisterFaasRuntimeGovalidatorTag()
	var m sqlmock.Sqlmock
	m, dbengine.Engine = global.MockDB()
	var (
		method   = http.MethodPost
		uri      = "/v1/layers/layer1/versions"
		uid      = "uid"
		pathMapV = map[string]string{
			"LayerName": "layer1",
		}
	)

	cases := []struct {
		in_c         *server.Context
		out_HttpCode int
	}{
		{
			in_c:         global.BuildNewKunCtx(method, uri, "err Json BODY", "", pathMapV),
			out_HttpCode: 400,
		},
		{
			in_c:         global.BuildNewKunCtx(method, uri, "err Json BODY", uid, pathMapV),
			out_HttpCode: 400,
		},
		{
			in_c:         global.BuildNewKunCtx(method, uri, fmt.Sprintf(`{"CompatibleRuntimes":["python2"],"Content":{"ZipFile":"%s"},"Description":"test","LicenseInfo":"test"}`, zipFile), uid, pathMapV),
			out_HttpCode: 201,
		},
		{
			in_c:         global.BuildNewKunCtx(method, uri, fmt.Sprintf(`{"CompatibleRuntimes":["python2"],"Content":{"ZipFile":"%s"},"Description":"test","LicenseInfo":"test"}`, zipLinkFile), uid, pathMapV),
			out_HttpCode: 400,
		},
	}
	for k, tc := range cases {
		f := LayerRest{}
		if tc.out_HttpCode == http.StatusCreated || tc.out_HttpCode == http.StatusBadRequest {
			//m.ExpectExec("UPDATE").WillReturnResult(sqlmock.NewResult(1, 1))
			m.ExpectQuery("SELECT").WillReturnError(gorm.ErrRecordNotFound)
			m.ExpectQuery("SELECT").WillReturnRows(global.GetRowsFunctions(global.GetTestFunc(1)))
			m.ExpectQuery("SELECT").WillReturnRows(global.GetRowsLayer(global.GetTestLayers(1)))
			m.ExpectExec("INSERT").WillReturnResult(sqlmock.NewResult(1, 1))
		}
		f.publish(tc.in_c)
		if tc.out_HttpCode != tc.in_c.Response().StatusCode() {
			continue
		}
		assert.Equal(t, tc.out_HttpCode, tc.in_c.Response().StatusCode())
		t.Logf("k %d response %v", k, tc.in_c.Response().ResponseWriter)
	}
}

func TestGetLayerVersion(t *testing.T) {
	global.MockAC()
	code.MockCode()
	global.RegisterFaasRuntimeGovalidatorTag()
	var m sqlmock.Sqlmock
	m, dbengine.Engine = global.MockDB()
	var (
		method   = http.MethodGet
		uri      = "/v1/layers/layer1/versions/1"
		uid      = "uid"
		pathMapV = map[string]string{
			"LayerName":     "layer1",
			"VersionNumber": "1",
		}
	)
	cases := []struct {
		in_c         *server.Context
		out_HttpCode int
		sqlFunc      func()
	}{
		{
			in_c: global.BuildNewKunCtx(method, uri, "", uid, map[string]string{
				"LayerName":     "layer1",
				"VersionNumber": "aaa",
			}),
			out_HttpCode: 400,
			sqlFunc: func() {
				m.ExpectQuery("SELECT").WillReturnError(gorm.ErrRecordNotFound)
			},
		},
		{
			in_c:         global.BuildNewKunCtx(method, uri, "", uid, pathMapV),
			out_HttpCode: 404,
		},
		{
			in_c:         global.BuildNewKunCtx(method, uri, "", "", pathMapV),
			out_HttpCode: 400,
		},
		{
			in_c:         global.BuildNewKunCtx(method, uri, "", uid, pathMapV),
			out_HttpCode: 200,
			sqlFunc: func() {
				m.ExpectQuery("SELECT").WillReturnRows(global.GetRowsLayer(global.GetTestLayers(1)))
			},
		},
	}
	for k, tc := range cases {
		f := LayerRest{}
		if tc.sqlFunc != nil {
			tc.sqlFunc()
		}
		f.find(tc.in_c)
		assert.Equal(t, tc.out_HttpCode, tc.in_c.Response().StatusCode())
		t.Logf("k %d response %v", k, tc.in_c.Response().ResponseWriter)
	}
}

func TestListLayers(t *testing.T) {
	global.MockAC()
	code.MockCode()
	global.RegisterFaasRuntimeGovalidatorTag()
	var m sqlmock.Sqlmock
	m, dbengine.Engine = global.MockDB()
	var (
		method   = http.MethodGet
		uid      = "uid"
		pathMapV = map[string]string{}
	)
	// /v1/layers?CompatibleRuntime=CompatibleRuntime&Marker=Marker&MaxItems=MaxItems
	cases := []struct {
		in_c         *server.Context
		out_HttpCode int
		sqlFunc      func()
	}{
		{
			in_c:         global.BuildNewKunCtx(method, "/v1/layers?CompatibleRuntime=python2&Marker=0&MaxItems=10", "", uid, pathMapV),
			out_HttpCode: 200,
			sqlFunc: func() {
				m.ExpectQuery("SELECT").WillReturnRows(sqlmock.NewRows([]string{"count(*)"}).AddRow(2))
				m.ExpectQuery("SELECT").WillReturnRows(global.GetRowsLayer(global.GetTestLayers(2)))
				m.ExpectQuery("SELECT").WillReturnRows(global.GetRowsLayer(global.GetTestLayers(1)))
				m.ExpectQuery("SELECT").WillReturnRows(global.GetRowsLayer(global.GetTestLayers(1)))
			},
		},
		{
			in_c:         global.BuildNewKunCtx(method, "/v1/layers?CompatibleRuntime=python2&Marker=0&MaxItems=10", "", uid, pathMapV),
			out_HttpCode: 200,
			sqlFunc: func() {
				m.ExpectQuery("SELECT").WillReturnRows(sqlmock.NewRows([]string{"count(*)"}).AddRow(0))
				m.ExpectQuery("SELECT").WillReturnError(gorm.ErrRecordNotFound)
			},
		},
		{
			in_c:         global.BuildNewKunCtx(method, "/v1/layers?CompatibleRuntime=python5&page=0&pageSize=10", "", uid, pathMapV),
			out_HttpCode: 400,
		},
		{
			in_c:         global.BuildNewKunCtx(method, "/v1/layers?CompatibleRuntime=python5&page=0&pageSize=10", "", "", pathMapV),
			out_HttpCode: 400,
		},
		{
			in_c:         global.BuildNewKunCtx(method, "/v1/layers?CompatibleRuntime=python2&page=0&pageSize=10", "", uid, pathMapV),
			out_HttpCode: 200,
			sqlFunc: func() {
				m.ExpectQuery("SELECT").WillReturnRows(sqlmock.NewRows([]string{"count(*)"}).AddRow(2))
				m.ExpectQuery("SELECT").WillReturnRows(global.GetRowsLayer(global.GetTestLayers(2)))
				m.ExpectQuery("SELECT").WillReturnRows(global.GetRowsLayer(global.GetTestLayers(1)))
				m.ExpectQuery("SELECT").WillReturnRows(global.GetRowsLayer(global.GetTestLayers(1)))
			},
		},
	}
	for k, tc := range cases {
		f := LayerRest{}
		if tc.sqlFunc != nil {
			tc.sqlFunc()
		}
		f.find(tc.in_c)
		assert.Equal(t, tc.out_HttpCode, tc.in_c.Response().StatusCode())
		t.Logf("k %d response %v", k, tc.in_c.Response().ResponseWriter)
	}
}

func TestGetLayerVersionByBrn(t *testing.T) {
	global.MockAC()
	code.MockCode()
	global.RegisterFaasRuntimeGovalidatorTag()
	var m sqlmock.Sqlmock
	m, dbengine.Engine = global.MockDB()
	var (
		method   = http.MethodGet
		uid      = "uid"
		pathMapV = map[string]string{}
	)
	cases := []struct {
		in_c         *server.Context
		out_HttpCode int
		sqlFunc      func()
	}{
		{
			in_c:         global.BuildNewKunCtx(method, "/v1/layers?find=LayerVersion&Brn=brn:bce:cfc:bj:********************************:layer:layer1:1", "", uid, pathMapV),
			out_HttpCode: 200,
			sqlFunc: func() {
				m.ExpectQuery("SELECT").WillReturnRows(global.GetRowsLayer(global.GetTestLayers(1)))
			},
		},
		// brn 错误
		{
			in_c:         global.BuildNewKunCtx(method, "/v1/layers?find=LayerVersion&Brn=brn:bce:cfc:bj:********************************:laylayer1", "", uid, pathMapV),
			out_HttpCode: 400,
		},
		{
			in_c:         global.BuildNewKunCtx(method, "/v1/layers?find=LayerVersion&Brn=brn:bce:cfc:bj:********************************:laylayer1", "", "", pathMapV),
			out_HttpCode: 400,
		},
		{
			in_c:         global.BuildNewKunCtx(method, "/v1/layers?find=LayerVersion&Brn=brn:bce:cfc:bj:********************************:layer:layer1:1", "", uid, pathMapV),
			out_HttpCode: 404,
			sqlFunc: func() {
				m.ExpectQuery("SELECT").WillReturnError(gorm.ErrRecordNotFound)
			},
		},
	}
	for k, tc := range cases {
		f := LayerRest{}
		if tc.sqlFunc != nil {
			tc.sqlFunc()
		}
		f.find(tc.in_c)
		assert.Equal(t, tc.out_HttpCode, tc.in_c.Response().StatusCode())
		t.Logf("k %d response %v", k, tc.in_c.Response().ResponseWriter)
	}
}

func TestListLayerVersions(t *testing.T) {
	global.MockAC()
	code.MockCode()
	global.RegisterFaasRuntimeGovalidatorTag()
	var m sqlmock.Sqlmock
	m, dbengine.Engine = global.MockDB()
	var (
		method   = http.MethodGet
		uid      = "uid"
		uri      = "/v1/layers/layer1/versions"
		pathMapV = map[string]string{
			"LayerName": "layer1",
		}
	)
	// /v1/layers?CompatibleRuntime=CompatibleRuntime&Marker=Marker&MaxItems=MaxItems
	cases := []struct {
		in_c         *server.Context
		out_HttpCode int
		sqlFunc      func()
	}{
		{
			in_c:         global.BuildNewKunCtx(method, uri+"?CompatibleRuntime=python2&Marker=0&MaxItems=10", "", uid, pathMapV),
			out_HttpCode: 404,
			sqlFunc: func() {
				m.ExpectQuery("SELECT").WillReturnRows(sqlmock.NewRows([]string{"count(*)"}).AddRow(0))
			},
		},
		{
			in_c:         global.BuildNewKunCtx(method, uri, "", uid, pathMapV),
			out_HttpCode: 200,
			sqlFunc: func() {
				m.ExpectQuery("SELECT").WillReturnRows(sqlmock.NewRows([]string{"count(*)"}).AddRow(1))
				m.ExpectQuery("SELECT").WillReturnRows(global.GetRowsLayer(global.GetTestLayers(1)))
			},
		},
		{
			in_c:         global.BuildNewKunCtx(method, uri+"?CompatibleRuntime=python2&page=0&pageSize=10", "", uid, pathMapV),
			out_HttpCode: 200,
			sqlFunc: func() {
				m.ExpectQuery("SELECT").WillReturnRows(sqlmock.NewRows([]string{"count(*)"}).AddRow(1))
				m.ExpectQuery("SELECT").WillReturnRows(global.GetRowsLayer(global.GetTestLayers(1)))
			},
		},
		{
			in_c:         global.BuildNewKunCtx(method, "/v1/layers?CompatibleRuntime=python2&Marker=0&MaxItems=10", "", uid, map[string]string{}),
			out_HttpCode: 400,
		},
		{
			in_c:         global.BuildNewKunCtx(method, "/v1/layers?CompatibleRuntime=python2&Marker=0&MaxItems=10", "", "", map[string]string{}),
			out_HttpCode: 400,
		},
		{
			in_c:         global.BuildNewKunCtx(method, "/v1/layers?CompatibleRuntime=python2&Marker=cuowu&MaxItems=10", "", uid, pathMapV),
			out_HttpCode: 400,
		},
		{
			in_c: global.BuildNewKunCtx(method, "/v1/layers?CompatibleRuntime=python2&Marker=0&MaxItems=10", "", uid, map[string]string{
				"LayerName": "layerxxx/*-",
			}),
			out_HttpCode: 400,
		},
	}
	for k, tc := range cases {
		f := LayerRest{}
		if tc.sqlFunc != nil {
			tc.sqlFunc()
		}
		f.listVersion(tc.in_c)
		if !assert.Equal(t, tc.out_HttpCode, tc.in_c.Response().StatusCode()) {
			t.Logf("k %d response %v", k, tc.in_c.Response().ResponseWriter)
		}
	}
}

func TestDeleteLayerVersion(t *testing.T) {
	global.MockAC()
	code.MockCode()
	global.RegisterFaasRuntimeGovalidatorTag()
	var m sqlmock.Sqlmock
	m, dbengine.Engine = global.MockDB()
	var (
		method = http.MethodDelete
		uid    = "uid"
	)
	// /v1/layers?CompatibleRuntime=CompatibleRuntime&Marker=Marker&MaxItems=MaxItems
	cases := []struct {
		in_c         *server.Context
		out_HttpCode int
		sqlFunc      func()
	}{
		{
			in_c: global.BuildNewKunCtx(method, "/v1/layers/layer1/versions/1", "", uid, map[string]string{
				"LayerName": "layer1",
			}),
			out_HttpCode: 400,
		},
		{
			in_c: global.BuildNewKunCtx(method, "/v1/layers/layer1/versions/1", "", uid, map[string]string{
				"LayerName":     "layer1",
				"VersionNumber": "xx",
			}),
			out_HttpCode: 400,
		},
		{
			in_c: global.BuildNewKunCtx(method, "/v1/layers/layer1/versions/1", "", "", map[string]string{
				"LayerName":     "layer1",
				"VersionNumber": "xx",
			}),
			out_HttpCode: 400,
		},
		{
			in_c: global.BuildNewKunCtx(method, "/v1/layers/layer1/versions/1", "", uid, map[string]string{
				"LayerName":     "layer1",
				"VersionNumber": "1",
			}),
			out_HttpCode: 204,
			sqlFunc: func() {
				m.ExpectQuery("SELECT").WillReturnRows(global.GetRowsLayer(global.GetTestLayers(1)))
				m.ExpectExec("UPDATE").WillReturnResult(sqlmock.NewResult(1, 1))
			},
		},
		{
			in_c: global.BuildNewKunCtx(method, "/v1/layers/layer1/versions/1", "", uid, map[string]string{
				"LayerName":     "layer1",
				"VersionNumber": "1",
			}),
			out_HttpCode: 404,
			sqlFunc: func() {
				m.ExpectQuery("SELECT").WillReturnError(gorm.ErrRecordNotFound)
			},
		},
	}
	for k, tc := range cases {
		f := LayerRest{}
		if tc.sqlFunc != nil {
			tc.sqlFunc()
		}
		f.deleteVersion(tc.in_c)
		assert.Equal(t, tc.out_HttpCode, tc.in_c.Response().StatusCode())
		t.Logf("k %d response %v", k, tc.in_c.Response().ResponseWriter)
	}
}

func TestDeleteLayer(t *testing.T) {
	global.MockAC()
	code.MockCode()
	global.RegisterFaasRuntimeGovalidatorTag()
	var m sqlmock.Sqlmock
	m, dbengine.Engine = global.MockDB()
	var (
		method = http.MethodDelete
		uid    = "uid"
	)
	// /v1/layers?CompatibleRuntime=CompatibleRuntime&Marker=Marker&MaxItems=MaxItems
	cases := []struct {
		in_c         *server.Context
		out_HttpCode int
		sqlFunc      func()
	}{
		{
			in_c: global.BuildNewKunCtx(method, "/v1/layers/layer1/versions/1", "", uid, map[string]string{
				"LayerName": "",
			}),
			out_HttpCode: 400,
		},
		{
			in_c: global.BuildNewKunCtx(method, "/v1/layers/layer1/versions/1", "", uid, map[string]string{
				"LayerName": "layer1xxxx*////",
			}),
			out_HttpCode: 400,
		},
		{
			in_c: global.BuildNewKunCtx(method, "/v1/layers/layer1", "", uid, map[string]string{
				"LayerName": "layer1",
			}),
			out_HttpCode: 204,
			sqlFunc: func() {
				m.ExpectQuery("SELECT").WillReturnRows(global.GetRowsLayer(global.GetTestLayers(2)))
				m.ExpectExec("UPDATE").WillReturnResult(sqlmock.NewResult(1, 1))
			},
		},
		{
			in_c: global.BuildNewKunCtx(method, "/v1/layers/layer1", "", uid, map[string]string{
				"LayerName": "layer1",
			}),
			out_HttpCode: 404,
			sqlFunc: func() {
				m.ExpectQuery("SELECT").WillReturnError(gorm.ErrRecordNotFound)
			},
		},
	}
	for k, tc := range cases {
		f := LayerRest{}
		if tc.sqlFunc != nil {
			tc.sqlFunc()
		}
		f.delete(tc.in_c)
		assert.Equal(t, tc.out_HttpCode, tc.in_c.Response().StatusCode())
		t.Logf("k %d response %v", k, tc.in_c.Response().ResponseWriter)
	}
}

func TestPublishLayerConsoleVersion(t *testing.T) {
	global.MockAC()
	code.MockCode()
	global.RegisterFaasRuntimeGovalidatorTag()
	var m sqlmock.Sqlmock
	m, dbengine.Engine = global.MockDB()
	var (
		method   = http.MethodPost
		uri      = "/v1/layers/layer1/versions"
		uid      = "uid"
		pathMapV = map[string]string{
			"LayerName": "layer1",
		}
	)
	bodyFile, _ := base64.DecodeString(zipFile)

	mockMutipartFileBody := fmt.Sprintf(`--__X_PAW_BOUNDARY__
Content-Disposition: form-data; name="ZipFile"; filename="test_event_97465_67879f9d-d5f1-47b9-92b2-b405196106f4.zip"
Content-Type: application/zip

%s
--__X_PAW_BOUNDARY__
Content-Disposition: form-data; name="CompatibleRuntimes"

python2,nodejs8.5
--__X_PAW_BOUNDARY__
Content-Disposition: form-data; name="Description"

desc
--__X_PAW_BOUNDARY__
Content-Disposition: form-data; name="LicenseInfo"

lice
--__X_PAW_BOUNDARY__--`, bodyFile)

	cases := []struct {
		in_c         *server.Context
		out_HttpCode int
		sqlFunc      func()
	}{
		{
			in_c:         global.BuildNewKunCtx(method, uri, "err Json BODY", "", pathMapV),
			out_HttpCode: 400,
		},
		{
			in_c:         global.BuildNewKunCtx(method, uri, "err Json BODY", uid, pathMapV),
			out_HttpCode: 400,
		},
		{
			in_c:         global.BuildNewKunCtxWithHeaders1(method, uri, mockMutipartFileBody, uid, pathMapV),
			out_HttpCode: 201,
			sqlFunc: func() {
				m.ExpectQuery("SELECT").WillReturnError(gorm.ErrRecordNotFound)
				m.ExpectQuery("SELECT").WillReturnRows(global.GetRowsFunctions(global.GetTestFunc(1)))
				m.ExpectQuery("SELECT").WillReturnRows(global.GetRowsLayer(global.GetTestLayers(1)))
				m.ExpectExec("INSERT").WillReturnResult(sqlmock.NewResult(1, 1))
			},
		},
	}
	for k, tc := range cases {
		f := LayerRest{}
		if tc.sqlFunc != nil {
			tc.sqlFunc()
		}
		f.consolePublish(tc.in_c)
		assert.Equal(t, tc.out_HttpCode, tc.in_c.Response().StatusCode())
		t.Logf("k %d response %v", k, tc.in_c.Response().ResponseWriter)
	}
}

var zipFile = "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"
var zipLinkFile = "UEsDBAoAAAAAAMSbjVDCVPv+DQAAAA0AAAAKABwAYmx1ZXByaW50c1VUCQADP02UXj9NlF51eAsAAQT2AQAABBQAAAAuLi9ibHVlcHJpbnRzUEsBAh4DCgAAAAAAxJuNUMJU+/4NAAAADQAAAAoAGAAAAAAAAAAAAO2hAAAAAGJsdWVwcmludHNVVAUAAz9NlF51eAsAAQT2AQAABBQAAABQSwUGAAAAAAEAAQBQAAAAUQAAAAAA"
