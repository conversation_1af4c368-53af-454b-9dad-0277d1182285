package cloudfunc

import (
	"net/http"
	"net/http/httptest"
	"testing"

	blsSdk "github.com/baidubce/bce-sdk-go/services/bls"
	blsapi "github.com/baidubce/bce-sdk-go/services/bls/api"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/dao"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/global"

	"github.com/baidubce/bce-sdk-go/bce"
	"github.com/stretchr/testify/assert"
	"icode.baidu.com/baidu/faas/kun/pkg/server"
)

func newBlsClientMock(c *server.Context) (*blsSdk.Client, error) {
	ts := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
	}))

	b, _ := initBlsClient(ts.URL)
	return b, nil
}
func initBlsClient(endpoint string) (*blsSdk.Client, error) {
	return blsSdk.NewClient("abc", "abc", endpoint)
}
func TestPullLogRecord(t *testing.T) {
	global.MockAC()
	NewBlsClientWrapper = newBlsClientMock

	cases := []struct {
		in_c         *server.Context
		out_HttpCode int
	}{
		{
			in_c:         global.BuildNewKunCtx("GET", "/v1/logstore/CFC_logset_default/pullrecord", "", "", map[string]string{}),
			out_HttpCode: 400,
		},
		{
			in_c:         global.BuildNewKunCtx("GET", "/v1/logstore/CFC_logset_default/pullrecord?functionBrn=brn&query=r", ``, "uiduid", map[string]string{}),
			out_HttpCode: 400,
		},
		{
			in_c:         global.BuildNewKunCtx("GET", "/v1/logstore/CFC_logset_default/pullrecord?functionBrn=brn&startDateTime=2021-08-26T05:38:39Z&endDateTime=2021-08-27T19:38:39Z&limit=3", ``, "uiduid", map[string]string{}),
			out_HttpCode: 400,
		},
	}
	for _, tc := range cases {
		f := BlsRest{}
		f.pullLogRecord(tc.in_c)
		assert.Equal(t, tc.out_HttpCode, tc.in_c.Response().StatusCode())
	}

}

func TestListLogStore(t *testing.T) {
	global.MockAC()
	NewBlsClientWrapper = newBlsClientMock

	cases := []struct {
		in_c         *server.Context
		out_HttpCode int
	}{
		{
			in_c:         global.BuildNewKunCtx("GET", "/v1/logstore", "", "", map[string]string{}),
			out_HttpCode: 500,
		},
		{
			in_c:         global.BuildNewKunCtx("GET", "/v1/logstore?namePattern=brn", ``, "uiduid", map[string]string{}),
			out_HttpCode: 500,
		},
	}
	for _, tc := range cases {
		f := BlsRest{}
		f.listLogStore(tc.in_c)
		assert.Equal(t, tc.out_HttpCode, tc.in_c.Response().StatusCode())
	}

}

func TestPullRecordFromBls(t *testing.T) {
	t.Skip()
	b, _ := initBlsClient("url")
	records := []blsLogRecord{}
	markers := []logStreamMarker{}
	respJson := blsLogRecordResult{
		IsTruncated: false,
	}
	args := &blsapi.PullLogRecordArgs{}
	err := pullRecordFromBls("logStore", "logStream", &respJson, &records, &markers, args, b)
	assert.NotNil(t, err)
}

func TestHandlePullRecordResult(t *testing.T) {
	records := []blsLogRecord{}
	markers := []logStreamMarker{}
	respJson := blsLogRecordResult{
		IsTruncated: false,
	}
	result := []blsapi.LogRecord{}
	log := blsapi.LogRecord{
		Message: "msg",
	}
	result = append(result, log)
	resp := blsapi.PullLogRecordResult{
		IsTruncated: true,
		Result:      result,
	}
	handlePullRecordResult("logStream", &resp, &respJson, &records, &markers)

}

func TestQueryRecordFromBls(t *testing.T) {
	t.Skip()
	b, _ := initBlsClient("url")
	records := []blsLogRecord{}
	args := &blsapi.QueryLogRecordArgs{}
	err := queryRecordFromBls("logStore", &records, args, b)
	assert.NotNil(t, err)
}

func TestHandleQueryRecordResult(t *testing.T) {
	records := []blsLogRecord{}
	//构建不同row
	expectedRows := [][]interface{}{
		{
			"1641540733355",
			"cfclog_d33b162f5289cd2d6421894b62546940_success",
			int64(7),
			"msg",
			"748ca3b8-83a7-4b0a-a2f8-f8d9c9fb4e59",
			"success",
		},
	}
	resp := blsapi.QueryLogResult{
		ResultSet: &blsapi.ResultSet{
			Columns: []string{
				"@timestamp",
				"@stream",
				"@raw",
				"log",
				"requestId",
				"status",
			},
			Rows: expectedRows,
		},
	}
	handleQueryRecordResult(&resp, &records)
	expectedRows = [][]interface{}{
		{
			"1641540733355",
			int64(7),
			"msg",
			"748ca3b8-83a7-4b0a-a2f8-f8d9c9fb4e59",
			"success",
		},
	}
	resp = blsapi.QueryLogResult{
		ResultSet: &blsapi.ResultSet{
			Columns: []string{
				"@timestamp",
				"@raw",
				"log",
				"requestId",
				"status",
			},
			Rows: expectedRows,
		},
	}
	handleQueryRecordResult(&resp, &records)
	expectedRows = [][]interface{}{
		{
			int64(7),
			"msg",
			"748ca3b8-83a7-4b0a-a2f8-f8d9c9fb4e59",
			"success",
		},
	}
	resp = blsapi.QueryLogResult{
		ResultSet: &blsapi.ResultSet{
			Columns: []string{
				"@raw",
				"log",
				"requestId",
				"status",
			},
			Rows: expectedRows,
		},
	}
	handleQueryRecordResult(&resp, &records)
	expectedRows = [][]interface{}{
		{
			"msg",
			"748ca3b8-83a7-4b0a-a2f8-f8d9c9fb4e59",
			"success",
		},
	}
	resp = blsapi.QueryLogResult{
		ResultSet: &blsapi.ResultSet{
			Columns: []string{
				"log",
				"requestId",
				"status",
			},
			Rows: expectedRows,
		},
	}
	handleQueryRecordResult(&resp, &records)
	expectedRows = [][]interface{}{
		{
			"748ca3b8-83a7-4b0a-a2f8-f8d9c9fb4e59",
			"success",
		},
	}
	resp = blsapi.QueryLogResult{
		ResultSet: &blsapi.ResultSet{
			Columns: []string{
				"requestId",
				"status",
			},
			Rows: expectedRows,
		},
	}
	handleQueryRecordResult(&resp, &records)
}

func TestCreateRequestIdIndex(t *testing.T) {
	t.Skip()
	b, _ := initBlsClient("url")
	err := CreateRequestIdIndex("logStore", b)
	assert.NotNil(t, err)
}

func TestUpdateRequestIdIndex(t *testing.T) {
	t.Skip()
	b, _ := initBlsClient("url")
	err := updateRequestIdIndex("logStore", b)
	assert.NotNil(t, err)
}

func TestGetLogStream(t *testing.T) {
	b, _ := initBlsClient("url")
	cases := []struct {
		in_c         *server.Context
		out_HttpCode int
	}{
		{
			in_c:         global.BuildNewKunCtx("GET", "/v1/logstore", "", "", map[string]string{}),
			out_HttpCode: 400,
		},
		{
			in_c:         global.BuildNewKunCtx("GET", "/v1/logstore?functionBrn=brn&status=success", ``, "uiduid", map[string]string{}),
			out_HttpCode: 400,
		},
		{
			in_c:         global.BuildNewKunCtx("GET", "/v1/logstore?functionBrn=brn&marker=l:m", ``, "uiduid", map[string]string{}),
			out_HttpCode: 400,
		},
	}
	for i, tc := range cases {
		_, _, err := getLogStream("logStore", tc.in_c.Request(), b)
		if i == 0 {
			assert.NotNil(t, err)
		} else {
			assert.Nil(t, err)
		}

	}
}

func TestCreateDefaultLogStore(t *testing.T) {
	t.Skip("test")
	b, _ := initBlsClient("url")
	err := createDefaultLogStore(b)
	assert.NotNil(t, err)
}

func TestHandlerBlsError(t *testing.T) {
	t.Skip()
	b, _ := initBlsClient("url")
	cases := []struct {
		real_err *bce.BceServiceError
	}{
		{
			real_err: &bce.BceServiceError{
				Code: "LogStoreNotFound",
			},
		},
		{
			real_err: &bce.BceServiceError{
				Code: "LogStreamNotFound",
			},
		},
		{
			real_err: &bce.BceServiceError{
				Code: "InvalidQuery",
			},
		},
		{
			real_err: &bce.BceServiceError{
				Code: "IndexAlreadyExist",
			},
		},
		{
			real_err: &bce.BceServiceError{
				Code: "InvalidSQL",
			},
		},
		{
			real_err: &bce.BceServiceError{
				Code: "InternalError",
			},
		},
		//{
		//	real_err: &bce.BceServiceError{
		//		Code: "InvalidQuery",
		//		Message: "no index found for logStore RequestId",
		//	},
		//},
	}

	for i, tc := range cases {
		err := handlerBlsError("logStore", tc.real_err, b)
		if i == 1 || i == 3 {
			assert.Nil(t, err)
		} else {
			assert.NotNil(t, err)
		}

	}
}
func TestGetLogStreamName(t *testing.T) {
	logStreamName := getLogStreamName("brn", "status")
	assert.NotNil(t, logStreamName)
	logStreamName = getLogStreamNameBrn("brn")
	assert.NotNil(t, logStreamName)
}

func TestGetLogStreamMarker(t *testing.T) {
	markerMap := getLogStreamMarker("logStream1:marker::logStream2:marker")
	assert.NotNil(t, markerMap)
}

func TestSetBlsLogStore(t *testing.T) {
	global.MockAC()
	NewBlsClientWrapper = newBlsClientMock
	newFunc := &dao.Function{
		Uid:         "uiduid",
		FunctionBrn: "bce:cfc:bj:75f2642400b9aba21947780cc0bff054",
	}
	cases := []struct {
		in_c         *server.Context
		out_HttpCode int
	}{
		{
			in_c:         global.BuildNewKunCtx("POST", "/v1/functions", `{"FunctionName":"test-h"}`, "uiduid", map[string]string{}),
			out_HttpCode: 201,
		},
	}
	for _, tc := range cases {
		SetBlsLogStore(tc.in_c, newFunc)
		newFunc.BlsLogSet = "bls"
		SetBlsLogStore(tc.in_c, newFunc)
	}
}
