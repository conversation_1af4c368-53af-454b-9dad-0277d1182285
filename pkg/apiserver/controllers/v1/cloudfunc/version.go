/* function 版本相关接口操作 */

package cloudfunc

import (
	"icode.baidu.com/baidu/faas/kun/pkg/api"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/global"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/models/function"
	"net/http"
	"strconv"

	"github.com/emicklei/go-restful"
	"icode.baidu.com/baidu/faas/kun/cmd/apiserver/filter"

	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/dao"
	apiErr "icode.baidu.com/baidu/faas/kun/pkg/apiserver/error"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/models"
	kunErr "icode.baidu.com/baidu/faas/kun/pkg/error"
	"icode.baidu.com/baidu/faas/kun/pkg/server"
	"icode.baidu.com/baidu/faas/kun/pkg/server/endpoint"
)

type VersionResource struct{}

// APIs xxx
func (a VersionResource) APIs() []endpoint.ApiSingle {
	apis := []endpoint.ApiSingle{
		// create new version, POST /2015-03-31/functions/{FunctionName}/versions
		endpoint.ApiSingle{
			Verb:    "POST",
			Path:    "/functions/{FunctionName}/versions",
			Handler: server.WrapRestRouteFunc(a.create),
			Filters: []restful.FilterFunction{
				filter.IAMPermissionCheck(api.Resource, "", []string{"cfc:PublishVersion", filter.IamPermWrite}),
				server.WrapRestFilterFunction(filter.EmptyBodyPayloadToJson),
			},
		},
		// list version, GET /2015-03-31/functions/{FunctionName}/versions
		endpoint.ApiSingle{
			Verb:    "GET",
			Path:    "/functions/{FunctionName}/versions",
			Handler: server.WrapRestRouteFunc(a.findByCondition),
			Filters: []restful.FilterFunction{
				filter.IAMPermissionCheck(api.Resource, "", []string{"cfc:ListVersionsByFunction", filter.IamPermList}),
				server.WrapRestFilterFunction(filter.ValidatorQueryParameter),
			},
		},
	}
	return apis
}

func (a *VersionResource) create(c *server.Context) {
	response := c.Response()

	ctx := global.BuildApiserverContext(c.Request(), c.Response(), "versions_create")
	initFunction := &dao.Function{}
	var (
		initFuncTmp interface{}
		err         error
	)
	if initFuncTmp, err = ctx.Observer.NewStage(global.InitFunctionStage).ObserveObject(InitFunction(c.Request())); err != nil {
		apiErr.NewInitFuncMetaException(err.Error(), "", nil).WithErrorLog().WriteTo(response)
		return
	}

	initFunction = initFuncTmp.(*dao.Function)
	funcVerReq := function.FunctionVersionReq{}
	if err := c.Request().ReadEntity(&funcVerReq); err != nil {
		c.WithWarnLog(kunErr.NewInvalidRequestContentException("", err)).WriteTo(response)
		return
	}
	//判断函数是否存在
	if rKerr := ctx.Observer.NewStage(global.CheckFunctionStage).Observe(IsFunctionExist(initFunction.Uid, initFunction.FunctionName)); rKerr != nil {
		c.WithWarnLog(rKerr).WriteTo(response)
		return
	}
	var newFunc *dao.Function
	newFunc, rkunErr := function.CreateNewVersion(funcVerReq, initFunction, ctx)
	if rkunErr != nil {
		c.WithErrorLog(rkunErr).WriteTo(response)
		return
	}

	defer global.WriteSummary(ctx, api.LogNotToSummary)
	response.WriteHeaderAndEntity(http.StatusCreated, newFunc)

}

//已经支持数字marker，如果需要支持不透明marker,后续会采用des对称加密marker
func (a VersionResource) findByCondition(c *server.Context) {
	response := c.Response()
	markerStr := c.Request().QueryParameter("Marker")
	maxItemStr := c.Request().QueryParameter("MaxItems")

	ctx := global.BuildApiserverContext(c.Request(), c.Response(), "version_findByCondition")
	findFunc := &dao.Function{}
	var (
		findFuncTmp, versionsTmp, countTmp interface{}
		marker, maxItem, count             int64
		resMap                             = make(map[string]interface{})
		err                                error
	)
	if findFuncTmp, err = ctx.Observer.NewStage(global.InitFunctionStage).ObserveObject(InitFunction(c.Request())); err != nil {
		c.WithWarnLog(apiErr.NewInitFuncMetaException(err.Error(), "", nil)).WriteTo(response)
		return
	}

	findFunc = findFuncTmp.(*dao.Function)
	ctx.Context.FunctionBrn = findFunc.FunctionBrn
	//判断函数是否存在
	if rKerr := ctx.Observer.NewStage(global.CheckFunctionStage).Observe(IsFunctionExist(findFunc.Uid, findFunc.FunctionName)); rKerr != nil {
		c.WithWarnLog(apiErr.NewResourceNotFoundException("function not found ", rKerr)).WriteTo(response)
		return
	}

	_, _, marker, maxItem, err = models.ParseParams("", "", markerStr, maxItemStr)
	if err != nil {
		c.WithWarnLog(kunErr.NewInvalidParameterValueException("[parse Marker|MaxItems fail]", err))
		return
	}

	condition := &function.VersionListCond{
		SearchFN: findFunc.FunctionName,
		Marker:   marker,
		MaxItems: maxItem,
	}
	if versionsTmp, countTmp, err = ctx.Observer.NewStage(global.ListVersionsStage).ObserveObjects(function.ListFunctionVersions(findFunc, condition)); err != nil {
		c.WithWarnLog(kunErr.NewServiceException("[Query database failed]", err))
		return
	}

	functions := versionsTmp.([]dao.Function)
	count = countTmp.(int64)
	if maxItemStr != "" {
		if count > (maxItem + marker) {
			resMap["NextMarker"] = strconv.FormatInt((maxItem + marker), 10)
		}
	}

	defer global.WriteSummary(ctx, api.LogNotToSummary)
	resMap["Versions"] = models.FormatFunctionRes(functions)
	response.WriteHeaderAndEntity(http.StatusOK, resMap)
}
