package cloudfunc

import (
	"net/http"

	"icode.baidu.com/baidu/faas/kun/pkg/api"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/global"

	"icode.baidu.com/baidu/faas/kun/pkg/server"
)

type CFSRest struct {
	Path string
}

func (cfs CFSRest) list(c *server.Context) {

	//CFSCLient.ListCFSInfosInSpecificSubnet
	response := c.Response()
	r := c.Request()
	ctx := global.BuildApiserverContext(c.Request(), c.Response(), "listCFSInfosInSpecificSubnet")
	defer global.WriteSummary(ctx, api.LogNotToSummary)

	user, err := GetUser(c.Request())

	if err != nil {
		respErr := c.WithErrorLog(err)
		response.WriteHeaderAndEntity(http.StatusForbidden, respErr)
		return
	}

	uid := user.Domain.ID

	subnetId := r.PathParameter("SubnetId")

	fsInfos, err := global.AC.Clients.CFS.ListCFSInfosInSpecificSubnet(uid, subnetId)

	if err != nil {
		c.Logger().Warnf("cfs  not find, uid: %s, subnetId: %v", uid, subnetId)
		response.WriteHeaderAndEntity(http.StatusNoContent, fsInfos)
		return
	}
	response.WriteHeaderAndEntity(http.StatusOK, fsInfos)

}
