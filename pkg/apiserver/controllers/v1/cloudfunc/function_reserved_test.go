package cloudfunc

import (
	"testing"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/stretchr/testify/assert"

	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/dbengine"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/global"
	"icode.baidu.com/baidu/faas/kun/pkg/server"
)

func TestCreateFunctionReserved(t *testing.T) {
	global.MockAC()

	cases := []struct {
		in_c         *server.Context
		out_HttpCode int
		sqlFunc      func()
	}{
		{
			in_c:         global.BuildNewKunCtx("POST", "/functions/reserved", "err Json BODY", "123", map[string]string{}),
			out_HttpCode: 400,
		},
		// function brn为空
		{
			in_c:         global.BuildNewKunCtx("POST", "/functions/reserved", `{"FunctionBrn":"","ReservedCount":2}`, "123", map[string]string{}),
			out_HttpCode: 400,
		},
		// 用户不存在
		{
			in_c:         global.BuildNewKunCtx("POST", "/functions/reserved", `{"FunctionBrn":"brn:bce:cfc:bj:640c8817bd1de2928d47256dd0620ce5:function:test","ReservedCount":2}`, "", map[string]string{}),
			out_HttpCode: 404,
		},
		//函数不存在
		{
			in_c: global.BuildNewKunCtx("POST", "/functions/reserved", `{"FunctionBrn":"brn:bce:cfc:bj:640c8817bd1de2928d47256dd0620ce5:function:test","ReservedCount":2}`, "123", map[string]string{}),
			sqlFunc: func() {
				var m sqlmock.Sqlmock
				m, dbengine.Engine = global.MockDB()
				m.ExpectQuery("SELECT").WillReturnRows(global.GetRowsFunctions(global.GetTestFunc(0)))
			},
			out_HttpCode: 404,
		},
		// 预留实例已经存在
		{
			in_c: global.BuildNewKunCtx("POST", "/functions/reserved", `{"FunctionBrn":"brn:bce:cfc:bj:640c8817bd1de2928d47256dd0620ce5:function:test","ReservedCount":2}`, "123", map[string]string{}),
			sqlFunc: func() {
				var m sqlmock.Sqlmock
				m, dbengine.Engine = global.MockDB()
				m.ExpectQuery("SELECT").WillReturnRows(global.GetRowsFunctions(global.GetTestFunc(1)))
				m.ExpectQuery("SELECT").WillReturnRows(global.GetRowsFunctionReserveds(global.GetTestFunctionReserveds(2)))
				m.ExpectQuery("SELECT").WillReturnRows(global.GetRowsFunctionReserveds(global.GetTestFunctionReserveds(1)))
			},
			out_HttpCode: 409,
		},
		{
			in_c: global.BuildNewKunCtx("POST", "/functions/reserved", `{"FunctionBrn":"brn:bce:cfc:bj:640c8817bd1de2928d47256dd0620ce5:function:test","ReservedCount":2}`, "123", map[string]string{}),
			sqlFunc: func() {
				var m sqlmock.Sqlmock
				m, dbengine.Engine = global.MockDB()
				m.ExpectQuery("SELECT").WillReturnRows(global.GetRowsFunctions(global.GetTestFunc(1)))
				m.ExpectQuery("SELECT").WillReturnRows(global.GetRowsFunctionReserveds(global.GetTestFunctionReserveds(1)))
				m.ExpectExec(".*").WillReturnResult(sqlmock.NewResult(1, 1))
			},
			out_HttpCode: 201,
		},
	}

	for _, tc := range cases {
		f := FunctionReservedRest{}
		f.PublicAPIs()
		if tc.sqlFunc != nil {
			tc.sqlFunc()
		}
		f.create(tc.in_c)
		assert.Equal(t, tc.out_HttpCode, tc.in_c.Response().StatusCode())
	}
}

func TestUpdateFunctionReserved(t *testing.T) {
	global.MockAC()
	cases := []struct {
		in_c         *server.Context
		out_HttpCode int
		sqlFunc      func()
	}{
		{
			in_c:         global.BuildNewKunCtx("POST", "/functions/reserved", "err Json BODY", "123", map[string]string{}),
			out_HttpCode: 400,
		},
		// 预留实例uuid为空
		{
			in_c:         global.BuildNewKunCtx("POST", "/functions/reserved", `{"FunctionBrn":"","ReservedCount":2}`, "123", map[string]string{}),
			out_HttpCode: 400,
		},
		// 用户不存在
		{
			in_c:         global.BuildNewKunCtx("PUT", "/functions/reserved/", `{"ReservedCount":2}`, "", map[string]string{"Uuid": "uuiduuid"}),
			out_HttpCode: 404,
		},
		// 函数预留实例不存在
		{
			in_c:         global.BuildNewKunCtx("PUT", "/functions/reserved", `{"FunctionBrn":"brn:bce:cfc:bj:640c8817bd1de2928d47256dd0620ce5:function:test","ReservedCount":2}`, "123", map[string]string{"Uuid": "uuiduuid"}),
			out_HttpCode: 404,
		},
		// 函数不存在
		{
			in_c:         global.BuildNewKunCtx("POST", "/functions/reserved", `{"FunctionBrn":"brn:bce:cfc:bj:640c8817bd1de2928d47256dd0620ce5:function:test","ReservedCount":2}`, "123", map[string]string{"Uuid": "uuiduuid"}),
			out_HttpCode: 404,
			sqlFunc: func() {
				var m sqlmock.Sqlmock
				m, dbengine.Engine = global.MockDB()
				m.ExpectQuery("SELECT").WillReturnRows(global.GetRowsFunctionReserveds(global.GetTestFunctionReserveds(0)))
			},
		},
		{
			in_c: global.BuildNewKunCtx("POST", "/functions/reserved", `{"FunctionBrn":"brn:bce:cfc:bj:640c8817bd1de2928d47256dd0620ce5:function:test","ReservedCount":2}`, "123", map[string]string{"Uuid": "uuiduuid"}),
			sqlFunc: func() {
				var m sqlmock.Sqlmock
				m, dbengine.Engine = global.MockDB()
				m.ExpectQuery("SELECT").WillReturnRows(global.GetRowsFunctions(global.GetTestFunc(1)))
				m.ExpectQuery("SELECT").WillReturnRows(global.GetRowsFunctionReserveds(global.GetTestFunctionReserveds(1)))
				m.ExpectQuery("SELECT").WillReturnRows(global.GetRowsFunctions(global.GetTestFunc(1)))
				m.ExpectExec("UPDATE").WillReturnResult(sqlmock.NewResult(1, 1))
			},
			out_HttpCode: 200,
		},
	}

	for _, tc := range cases {
		f := FunctionReservedRest{}
		if tc.sqlFunc != nil {
			tc.sqlFunc()
		}
		f.update(tc.in_c)
		assert.Equal(t, tc.out_HttpCode, tc.in_c.Response().StatusCode())
	}
}

func TestDeleteFunctionReserved(t *testing.T) {
	global.MockAC()
	cases := []struct {
		in_c         *server.Context
		out_HttpCode int
		sqlFunc      func()
	}{
		{
			in_c:         global.BuildNewKunCtx("DELETE", "/functions/reserved", "err Json BODY", "123", map[string]string{}),
			out_HttpCode: 400,
		},
		// function brn为空
		{
			in_c:         global.BuildNewKunCtx("DELETE", "/functions/reserved", `{"FunctionBrn":"","ReservedCount":2}`, "123", map[string]string{}),
			out_HttpCode: 400,
		},
		// 用户不存在
		{
			in_c:         global.BuildNewKunCtx("DELETE", "/functions/reserved/", `{"ReservedCount":2}`, "", map[string]string{"Uuid": "uiuuid"}),
			out_HttpCode: 404,
		},
		// 函数预留实例不存在
		{
			in_c:         global.BuildNewKunCtx("DELETE", "/functions/reserved", `{"FunctionBrn":"brn:bce:cfc:bj:640c8817bd1de2928d47256dd0620ce5:function:test","ReservedCount":2}`, "123", map[string]string{"Uuid": "uiuuid"}),
			out_HttpCode: 404,
		},
		{
			in_c: global.BuildNewKunCtx("DELETE", "/functions/reserved", `{"FunctionBrn":"brn:bce:cfc:bj:640c8817bd1de2928d47256dd0620ce5:function:test","ReservedCount":2}`, "123", map[string]string{"Uuid": "uiuuid"}),
			sqlFunc: func() {
				var m sqlmock.Sqlmock
				m, dbengine.Engine = global.MockDB()
				m.ExpectQuery("SELECT").WillReturnRows(global.GetRowsFunctions(global.GetTestFunc(1)))
				m.ExpectExec("UPDATE").WillReturnResult(sqlmock.NewResult(0, 0))
			},
			out_HttpCode: 204,
		},
	}

	for _, tc := range cases {
		f := FunctionReservedRest{}
		if tc.sqlFunc != nil {
			tc.sqlFunc()
		}
		f.delete(tc.in_c)
		assert.Equal(t, tc.out_HttpCode, tc.in_c.Response().StatusCode())
	}
}

func TestFindFunctionReserved(t *testing.T) {
	global.MockAC()
	cases := []struct {
		in_c         *server.Context
		out_HttpCode int
		sqlFunc      func()
	}{
		// function brn为空
		{
			in_c:         global.BuildNewKunCtx("GET", "/functions/reserved", `{"FunctionBrn":"","ReservedCount":2}`, "123", map[string]string{}),
			out_HttpCode: 204,
		},
		// 用户不存在
		{
			in_c:         global.BuildNewKunCtx("GET", "/functions/reserved/", `{"ReservedCount":2}`, "", map[string]string{"FunctionBrn": "brn:bce:cfc:bj:640c8817bd1de2928d47256dd0620ce5:function:test"}),
			out_HttpCode: 204,
		},
		{
			in_c: global.BuildNewKunCtx("GET", "/functions/reserved", `{"FunctionBrn":"brn:bce:cfc:bj:640c8817bd1de2928d47256dd0620ce5:function:test","ReservedCount":2}`, "123", map[string]string{"FunctionBrn": "brn:bce:cfc:bj:640c8817bd1de2928d47256dd0620ce5:function:test"}),
			sqlFunc: func() {
				var m sqlmock.Sqlmock
				m, dbengine.Engine = global.MockDB()
				m.ExpectQuery("SELECT").WillReturnRows(global.GetRowsFunctions(global.GetTestFunc(1)))
			},
			out_HttpCode: 200,
		},
	}

	for _, tc := range cases {
		f := FunctionReservedRest{}
		if tc.sqlFunc != nil {
			tc.sqlFunc()
		}
		f.find(tc.in_c)
		assert.Equal(t, tc.out_HttpCode, tc.in_c.Response().StatusCode())
	}
}

func TestListFunctionReserved(t *testing.T) {
	global.MockAC()
	cases := []struct {
		in_c         *server.Context
		out_HttpCode int
		sqlFunc      func()
	}{
		// functionName为空
		{
			in_c:         global.BuildNewKunCtx("GET", "/functions/reserved", `{"FunctionBrn":"","ReservedCount":2}`, "123", map[string]string{}),
			out_HttpCode: 400,
		},
		// 用户不存在
		{
			in_c:         global.BuildNewKunCtx("GET", "/functions/reserved?FunctionName=test", `{"ReservedCount":2}`, "", map[string]string{}),
			out_HttpCode: 404,
		},
		{
			in_c: global.BuildNewKunCtx("GET", "/functions/reserved?FunctionName=test", `{"FunctionBrn":"brn:bce:cfc:bj:640c8817bd1de2928d47256dd0620ce5:function:test","ReservedCount":2}`, "123", map[string]string{}),
			sqlFunc: func() {
				var m sqlmock.Sqlmock
				m, dbengine.Engine = global.MockDB()
				m.ExpectQuery("SELECT").WillReturnRows(global.GetRowsFunctions(global.GetTestFunc(1)))
			},
			out_HttpCode: 200,
		},
	}

	for _, tc := range cases {
		f := FunctionReservedRest{}
		if tc.sqlFunc != nil {
			tc.sqlFunc()
		}
		f.list(tc.in_c)
		assert.Equal(t, tc.out_HttpCode, tc.in_c.Response().StatusCode())
	}
}

func TestInsideFindFunctionReserved(t *testing.T) {
	global.MockAC()
	cases := []struct {
		in_c         *server.Context
		out_HttpCode int
		sqlFunc      func()
	}{
		{
			in_c: global.BuildNewKunCtx("GET", "/functions/reserved", `{"FunctionBrn":"brn:bce:cfc:bj:640c8817bd1de2928d47256dd0620ce5:function:test","ReservedCount":2}`, "123", map[string]string{"Uuid": "uuiduuid"}),
			sqlFunc: func() {
				var m sqlmock.Sqlmock
				m, dbengine.Engine = global.MockDB()
				m.ExpectQuery("SELECT").WillReturnRows(global.GetRowsFunctions(global.GetTestFunc(1)))
			},
			out_HttpCode: 200,
		},
	}

	for _, tc := range cases {
		f := FunctionReservedRest{}
		if tc.sqlFunc != nil {
			tc.sqlFunc()
		}
		f.insideFind(tc.in_c)
		assert.Equal(t, tc.out_HttpCode, tc.in_c.Response().StatusCode())
	}
}

func TestInsideListFunctionReserved(t *testing.T) {
	global.MockAC()
	cases := []struct {
		in_c         *server.Context
		out_HttpCode int
		sqlFunc      func()
	}{
		{
			in_c: global.BuildNewKunCtx("GET", "/functions/reserved", `{"FunctionBrn":"brn:bce:cfc:bj:640c8817bd1de2928d47256dd0620ce5:function:test","ReservedCount":2}`, "123", map[string]string{}),
			sqlFunc: func() {
				var m sqlmock.Sqlmock
				m, dbengine.Engine = global.MockDB()
				m.ExpectQuery("SELECT").WillReturnRows(global.GetRowsFunctions(global.GetTestFunc(1)))
			},
			out_HttpCode: 200,
		},
	}

	for _, tc := range cases {
		f := FunctionReservedRest{}
		if tc.sqlFunc != nil {
			tc.sqlFunc()
		}
		f.insideList(tc.in_c)
		assert.Equal(t, tc.out_HttpCode, tc.in_c.Response().StatusCode())
	}
}

func TestInsideUpdateFunctionReserved(t *testing.T) {
	global.MockAC()
	cases := []struct {
		in_c         *server.Context
		out_HttpCode int
		sqlFunc      func()
	}{
		{
			in_c: global.BuildNewKunCtx("PUT", "/functions/reserved", `{"FunctionBrn":"brn:bce:cfc:bj:640c8817bd1de2928d47256dd0620ce5:function:test","ReservedCount":2}`, "123", map[string]string{"Uuid": "uuiduuid"}),
			sqlFunc: func() {
				var m sqlmock.Sqlmock
				m, dbengine.Engine = global.MockDB()
				m.ExpectExec("UPDATE").WillReturnResult(sqlmock.NewResult(1, 1))
			},
			out_HttpCode: 200,
		},
	}

	for _, tc := range cases {
		f := FunctionReservedRest{}
		if tc.sqlFunc != nil {
			tc.sqlFunc()
		}
		f.insideUpdate(tc.in_c)
		assert.Equal(t, tc.out_HttpCode, tc.in_c.Response().StatusCode())
	}
}

func TestInsideDeleteFunctionReserved(t *testing.T) {
	global.MockAC()
	cases := []struct {
		in_c         *server.Context
		out_HttpCode int
		sqlFunc      func()
	}{
		{
			in_c: global.BuildNewKunCtx("Delete", "/functions/reserved", `{"FunctionBrn":"brn:bce:cfc:bj:640c8817bd1de2928d47256dd0620ce5:function:test","ReservedCount":2}`, "123", map[string]string{"Uuid": "uuiduuid"}),
			sqlFunc: func() {
				var m sqlmock.Sqlmock
				m, dbengine.Engine = global.MockDB()
				m.ExpectExec("UPDATE").WillReturnResult(sqlmock.NewResult(1, 1))
			},
			out_HttpCode: 200,
		},
	}

	for _, tc := range cases {
		f := FunctionReservedRest{}
		if tc.sqlFunc != nil {
			tc.sqlFunc()
		}
		f.insideUpdate(tc.in_c)
		assert.Equal(t, tc.out_HttpCode, tc.in_c.Response().StatusCode())
	}
}
