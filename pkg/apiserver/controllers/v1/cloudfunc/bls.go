package cloudfunc

import (
	"crypto/md5"
	"fmt"
	"net/http"
	"sort"
	"strconv"
	"strings"

	"github.com/baidubce/bce-sdk-go/bce"
	"github.com/emicklei/go-restful"
	"icode.baidu.com/baidu/faas/kun/pkg/api"
	apiErr "icode.baidu.com/baidu/faas/kun/pkg/apiserver/error"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/models/function"
	"icode.baidu.com/baidu/faas/kun/pkg/bce/bls"
	kunErr "icode.baidu.com/baidu/faas/kun/pkg/error"
	"icode.baidu.com/baidu/faas/kun/pkg/server"
	"icode.baidu.com/baidu/faas/kun/pkg/util/logs"

	blscli "github.com/baidubce/bce-sdk-go/services/bls"
	blsapi "github.com/baidubce/bce-sdk-go/services/bls/api"
	"icode.baidu.com/baidu/faas/kun/pkg/util/json"
)

// BlsRest xxx
type BlsRest struct {
	Path string
}

type blsLogRecordResult struct {
	Result      []blsLogRecord    `json:"result"`
	IsTruncated bool              `json:"isTruncated"`
	Marker      string            `json:"marker"`
	NextMarker  []logStreamMarker `json:"nextMarker"`
}

type blsLogRecord struct {
	Message   blsLogMsg `json:"message"`
	Timestamp int64     `json:"timestamp"`
	Sequence  int       `json:"sequence"`
}

type blsLogMsg struct {
	Log       string `json:"log"`
	RequestId string `json:"requestId"`
	Status    string `json:"status"`
}

type blsLogMsgV2 struct {
	Log       []string `json:"log"`
	RequestId string   `json:"requestId"`
	Status    string   `json:"status"`
}

type logStreamMarker struct {
	Logstream string `json:"logstream"`
	Marker    string `json:"marker"`
}

func (b *BlsRest) pullLogRecord(c *server.Context) {
	response := c.Response()
	r := c.Request()
	var err error
	var cli *blscli.Client
	logStore := r.PathParameter("logStoreName")
	functionBrn := r.QueryParameter("functionBrn")
	startTime := r.QueryParameter("startDateTime")
	endTime := r.QueryParameter("endDateTime")
	limitStr := r.QueryParameter("limit")
	limit, _ := strconv.Atoi(limitStr)

	requestId := r.QueryParameter("requestId")
	query := r.QueryParameter("query")
	if query != "" {
		query = "match log:'" + query + "'"
	} else if requestId != "" {
		query = "match requestId:'" + requestId + "'"
	}

	records := []blsLogRecord{}
	markers := []logStreamMarker{}
	respJson := blsLogRecordResult{
		IsTruncated: false,
	}

	if logStore == "" || functionBrn == "" {
		c.WithWarnLog(kunErr.NewInvalidParameterValueException("logStore or functionBrn is Illegal", nil)).WriteTo(response)
		return
	}
	// 客户端初始化
	cli, err = NewBlsClientWrapper(c)
	if err != nil {
		c.WithErrorLog(apiErr.NewInitBlsClientException("", "Init User Bls client failed", nil)).WriteTo(response)
		return
	}

	// 获取查询bls 的logStream
	markerMap, logStreams, err := getLogStream(logStore, r, cli)
	if err != nil {
		c.WithWarnLog(err).WriteTo(response)
		return
	}

	for _, logStream := range logStreams {
		marker, _ := markerMap[logStream]
		// query为空使用的是bls的 pull record接口
		if query == "" {
			args := &blsapi.PullLogRecordArgs{
				LogStreamName: logStream,
				StartDateTime: blsapi.DateTime(startTime),
				EndDateTime:   blsapi.DateTime(endTime),
				Limit:         limit,
				Marker:        marker,
			}
			err := pullRecordFromBls(logStore, logStream, &respJson, &records, &markers, args, cli)
			if err != nil {
				c.WithWarnLog(err).WriteTo(response)
				return
			}
		} else {
			// query不为空使用的是bls的 query record接口
			args := &blsapi.QueryLogRecordArgs{
				LogStreamName: logStream,
				Query:         query,
				StartDateTime: blsapi.DateTime(startTime),
				EndDateTime:   blsapi.DateTime(endTime),
				Limit:         limit,
			}
			err := queryRecordFromBls(logStore, &records, args, cli)
			if err != nil {
				c.WithWarnLog(err).WriteTo(response)
				return
			}
		}
	}
	// 按照时间排序
	sort.Slice(records, func(i, j int) bool {
		if records[i].Timestamp < records[j].Timestamp {
			return true
		}
		return false
	})
	respJson.Result = records
	respJson.NextMarker = markers
	response.WriteHeaderAndEntity(http.StatusOK, function.NewSuccessResp(respJson))
}

func (b *BlsRest) listLogStore(c *server.Context) {
	response := c.Response()
	r := c.Request()
	var err error
	var cli *blscli.Client
	namePattern := r.QueryParameter("namePattern")
	order := r.QueryParameter("order")
	orderBy := r.QueryParameter("orderBy")
	pageNoStr := r.QueryParameter("pageNo")
	pageSizeStr := r.QueryParameter("pageSize")
	if pageSizeStr == "" {
		pageSizeStr = "10000"
	}
	pageNo, _ := strconv.Atoi(pageNoStr)
	pageSize, _ := strconv.Atoi(pageSizeStr)
	args := &blsapi.QueryConditions{
		NamePattern: namePattern,
		Order:       order,
		OrderBy:     orderBy,
		PageNo:      pageNo,
		PageSize:    pageSize,
	}
	cli, err = NewBlsClientWrapper(c)
	if err != nil {
		c.WithErrorLog(apiErr.NewInitBlsClientException("", "Init User Bls client failed", nil)).WriteTo(response)
		return
	}
	resp, err := bls.ListLogStore(args, cli)
	if err != nil {
		c.WithWarnLog(err).WriteTo(response)
		return
	}
	response.WriteHeaderAndEntity(http.StatusOK, function.NewSuccessResp(resp))
}

func (b *BlsRest) listLogstream(c *server.Context) {
	response := c.Response()
	r := c.Request()
	var err error
	var cli *blscli.Client
	logStore := c.Request().PathParameter("logStoreName")
	namePattern := r.QueryParameter("namePattern")
	order := r.QueryParameter("order")
	orderBy := r.QueryParameter("orderBy")
	pageNoStr := r.QueryParameter("pageNo")
	pageSizeStr := r.QueryParameter("pageSize")
	if pageSizeStr == "" {
		pageSizeStr = "10000"
	}
	pageNo, _ := strconv.Atoi(pageNoStr)
	pageSize, _ := strconv.Atoi(pageSizeStr)
	args := &blsapi.QueryConditions{
		NamePattern: namePattern,
		Order:       order,
		OrderBy:     orderBy,
		PageNo:      pageNo,
		PageSize:    pageSize,
	}
	cli, err = NewBlsClientWrapper(c)
	if err != nil {
		c.WithErrorLog(apiErr.NewInitBlsClientException("", "Init User Bls client failed", nil)).WriteTo(response)
		return
	}
	resp, err := bls.ListLogstream(logStore, args, cli)
	if err != nil {
		c.WithWarnLog(err).WriteTo(response)
		return
	}
	response.WriteHeaderAndEntity(http.StatusOK, function.NewSuccessResp(resp))
}

func (b *BlsRest) queryLogRecord(c *server.Context) {
	response := c.Response()
	r := c.Request()
	var err error
	var cli *blscli.Client
	logStore := r.PathParameter("logStoreName")
	functionBrn := r.QueryParameter("functionBrn")
	if logStore == "" || functionBrn == "" {
		c.WithWarnLog(kunErr.NewInvalidParameterValueException("logStore or functionBrn is Illegal", nil)).WriteTo(response)
		return
	}
	logStream := getLogStreamNameBrn(functionBrn)
	query := r.QueryParameter("query")
	startTime := r.QueryParameter("startDateTime")
	endTime := r.QueryParameter("endDateTime")
	limitStr := r.QueryParameter("limit")
	limit, _ := strconv.Atoi(limitStr)
	logReq := &blsapi.QueryLogRecordArgs{
		LogStreamName: logStream + "_success",
		Query:         query,
		StartDateTime: blsapi.DateTime(startTime),
		EndDateTime:   blsapi.DateTime(endTime),
		Limit:         limit,
	}
	if err := checkQueryArgs(logReq); err != nil {
		c.WithWarnLog(err).WriteTo(response)
		return
	}
	cli, err = bls.NewClient(c)
	if err != nil {
		c.WithErrorLog(apiErr.NewInitBlsClientException("", "Init User Bls client failed", nil)).WriteTo(response)
		return
	}

	logRsp, err := bls.QueryLogRecord(logStore, logReq, cli)
	if err != nil {
		c.WithWarnLog(kunErr.NewServiceException("", err)).WriteTo(response)
		return
	}
	response.WriteHeaderAndEntity(http.StatusOK, function.NewSuccessResp(logRsp))

}

func checkQueryArgs(args *blsapi.QueryLogRecordArgs) error {
	if args == nil {
		return kunErr.NewInvalidParameterValueException("query params are Illegal", nil)
	} else {
		if args.Query == "" || args.StartDateTime == "" || args.EndDateTime == "" {
			return kunErr.NewInvalidParameterValueException("one of the params that query, startDateTime, endDateTime is empty", nil)
		} else if args.Limit < 0 {
			return kunErr.NewInvalidParameterValueException("the limit params is Illegal", nil)
		}
	}
	return nil
}

// 调用bls 拉取日志
func pullRecordFromBls(logStore string, logStream string, respJson *blsLogRecordResult, records *[]blsLogRecord, markers *[]logStreamMarker,
	args *blsapi.PullLogRecordArgs, cli *blscli.Client) error {
	resp, err := bls.PullLogRecord(logStore, args, cli)
	if err != nil {
		err = handlerBlsError(logStore, err, cli)
		if err != nil {
			return err
		}
	}
	handlePullRecordResult(logStream, resp, respJson, records, markers)
	return nil
}

// 处理返回结果 格式化结果
func handlePullRecordResult(logStream string, resp *blsapi.PullLogRecordResult, respJson *blsLogRecordResult, records *[]blsLogRecord, markers *[]logStreamMarker) {
	if resp != nil && resp.IsTruncated != false {
		respJson.IsTruncated = resp.IsTruncated
		logMarker := logStreamMarker{
			Logstream: logStream,
			Marker:    resp.NextMarker,
		}
		*markers = append(*markers, logMarker)
	}
	if resp != nil && len(resp.Result) > 0 {
		for _, record := range resp.Result {
			str := []byte(record.Message)
			msg := &blsLogMsg{}
			err := json.Unmarshal(str, msg)
			if err != nil {
				logs.Warnf(fmt.Sprintf("Unmarshal err %s", err))
				continue
			}
			record := blsLogRecord{
				Message:   *msg,
				Timestamp: record.Timestamp,
				Sequence:  record.Sequence,
			}
			*records = append(*records, record)
		}
	}
}

// 调用bls 查询日志 并处理成统一格式
func queryRecordFromBls(logStore string, records *[]blsLogRecord, args *blsapi.QueryLogRecordArgs, cli *blscli.Client) error {
	resp, err := bls.QueryLogRecord(logStore, args, cli)
	if err != nil {
		err = handlerBlsError(logStore, err, cli)
		if err != nil {
			return err
		}
	}
	handleQueryRecordResult(resp, records)
	return nil
}

// 处理返回结果 格式化结果
func handleQueryRecordResult(resp *blsapi.QueryLogResult, records *[]blsLogRecord) {
	if resp != nil && resp.ResultSet != nil && len(resp.ResultSet.Rows) > 0 {
		//确定timestamp，log，requestId，status的索引
		columns := resp.ResultSet.Columns
		var timestampIndex, rawIndex int
		for index, value := range columns {
			if value == "@timestamp" {
				timestampIndex = index
			} else if value == "@raw" {
				rawIndex = index
			}
		}
		for _, record := range resp.ResultSet.Rows {
			logs.V(9).Infof("record=%s", record)
			str, ok := record[rawIndex].(string)
			if !ok {
				logs.Warnf(fmt.Sprintf("log rawBytes type error %+v", record))
				continue
			}
			msgV2 := &blsLogMsgV2{}
			err := json.Unmarshal([]byte(str), msgV2)
			if err != nil {
				logs.Warnf(fmt.Sprintf("Unmarshal err %s", err))
				continue
			}
			logs.V(9).Infof("msgV2=%s", *msgV2)
			time, ok := record[timestampIndex].(float64)
			if !ok {
				logs.Warnf(fmt.Sprintf("timestampIndex type error %+v", record[timestampIndex]))
				continue
			}

			// 首先进行 JSON 序列化
			jsonLog, err := json.Marshal(msgV2.Log)
			if err != nil {
				logs.Warnf(fmt.Sprintf("Unmarshal err %+v, %s", msgV2.Log, err))
				continue
			}

			msg := &blsLogMsg{}
			msg.RequestId = msgV2.RequestId
			msg.Status = msgV2.Status
			msg.Log = string(jsonLog)

			record := blsLogRecord{
				Message:   *msg,
				Timestamp: int64(time),
			}
			*records = append(*records, record)
		}
	}
}

// 创建requestId 和 log 两个字段索引
func CreateRequestIdIndex(logStore string, cli *blscli.Client) error {
	logfield := blsapi.LogField{
		Type: "text",
	}
	fields := make(map[string]blsapi.LogField)
	fields["requestId"] = logfield
	fields["log"] = logfield
	err := bls.CreateIndex(logStore, false, fields, cli)
	if err != nil {
		err = handlerBlsError(logStore, err, cli)
		if err != nil {
			logs.Errorf("create bls index fail, err: %+v", err)
		}
	}
	return err
}

// 更新requestId 和 log 两个字段索引
func updateRequestIdIndex(logStore string, cli *blscli.Client) error {
	logfield := blsapi.LogField{
		Type: "text",
	}
	fields := make(map[string]blsapi.LogField)
	fields["requestId"] = logfield
	fields["log"] = logfield
	err := bls.UpdateIndex(logStore, false, fields, cli)
	if err != nil {
		err = handlerBlsError(logStore, err, cli)
		if err != nil {
			logs.Errorf("update bls index fail, err: %+v", err)
		}
	}
	return err
}

// 根据传参情况获取logStream
func getLogStream(logStore string, r *restful.Request, cli *blscli.Client) (map[string]string, []string, error) {
	functionBrn := r.QueryParameter("functionBrn")
	markerMap := getLogStreamMarker(r.QueryParameter("marker"))
	logStreamBrn := getLogStreamNameBrn(functionBrn)
	status := r.QueryParameter("status")
	logStreams := []string{}
	if status == "" {
		// 1.1) marker相当于logstream查询的游标 优先根据marker获取logstreams
		if len(markerMap) > 0 {
			for k := range markerMap {
				logStreams = append(logStreams, k)
			}
		} else {
			// 1.2) 查询bls 获取所有logstream
			args := &blsapi.QueryConditions{
				NamePattern: logStreamBrn,
			}
			resp, err := bls.ListLogStream(logStore, args, cli)
			if err != nil {
				err = handlerBlsError(logStore, err, cli)
				if err != nil {
					return markerMap, logStreams, err
				}
			}
			if len(resp.Result) > 0 {
				for _, record := range resp.Result {
					logStreams = append(logStreams, record.LogStreamName)
				}
			}
		}
	} else {
		// 2) 如果指定了状态，logstream是固定的
		logStream := getLogStreamName(functionBrn, status)
		logStreams = append(logStreams, logStream)
	}

	return markerMap, logStreams, nil
}

// 创建默认LogStore
func createDefaultLogStore(cli *blscli.Client) error {
	err := bls.CreateLogStore(api.BlsDefaultLogSet, api.BlsDefaultRetention, cli)
	if err == nil {
		logs.Infof("create bls logstore success")
	} else {
		logs.Errorf("create bls logstore fail, err: %+v", err)
	}
	return err
}

// 处理bls错误信息，容错处理
func handlerBlsError(logStore string, err error, cli *blscli.Client) error {

	if realErr, ok := err.(*bce.BceServiceError); ok {
		// 如果默认的LogStore不存在需要先创建
		if realErr.Code == "LogStoreNotFound" {
			if logStore == api.BlsDefaultLogSet {
				err = createDefaultLogStore(cli)
				if err == nil {
					return nil
				}
			}
			return apiErr.NewLogStoreNotFoundtException(realErr.Message, "logStore not exist", nil)
		} else if realErr.Code == "LogStreamNotFound" {
			// 没有创建logStream的时间返回空 日志未创建为空
			return nil

		} else if realErr.Code == "IndexAlreadyExist" {
			// 索引已经存在则更新
			updateRequestIdIndex(logStore, cli)
			return nil
		} else if realErr.Code == "InvalidQuery" {
			if strings.Index(realErr.Message, "no index found for logStore") > -1 || strings.Index(realErr.Message, "not found") > -1 {
				// 为用户创建默认索引
				err = CreateRequestIdIndex(logStore, cli)
				if err == nil {
					return nil
				}
				// 没有创建可查询的索引 抛出错误提示
				return apiErr.NewBlsNoIndexRequestException("", "no index found for logStore", nil)
			}
			// 语法错误
			return apiErr.NewBlsQueryInvalidException("", realErr.Message, nil)
		} else if realErr.Code == "InvalidSQL" || realErr.Code == "InternalError" {
			// 语法错误
			return apiErr.NewBlsQueryInvalidException("", realErr.Message, nil)
		}
	}
	return err
}

// logStream = cfclog_md5(brn)_status
func getLogStreamName(brn string, status string) string {
	data := md5.Sum([]byte(brn))
	return fmt.Sprintf("cfclog_%x_%s", data, status)
}

// logStream brn md5的部分
func getLogStreamNameBrn(brn string) string {
	data := md5.Sum([]byte(brn))
	return fmt.Sprintf("cfclog_%x", data)
}

// marker格式logstream1:marker1::logstream2:marker2
func getLogStreamMarker(marker string) map[string]string {
	markerSlice := strings.Split(marker, "::")
	markerMap := make(map[string]string)
	for _, lm := range markerSlice {
		lmSlice := strings.Split(lm, ":")
		if len(lmSlice) == 2 {
			markerMap[lmSlice[0]] = lmSlice[1]
		}

	}
	return markerMap
}
