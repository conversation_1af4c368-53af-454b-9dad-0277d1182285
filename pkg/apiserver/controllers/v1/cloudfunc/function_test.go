package cloudfunc

import (
	"icode.baidu.com/baidu/faas/kun/pkg/util/convert"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/DATA-DOG/go-sqlmock"
	bosSdk "github.com/baidubce/bce-sdk-go/services/bos"
	"github.com/stretchr/testify/assert"

	"icode.baidu.com/baidu/faas/kun/pkg/api"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/code"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/dao"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/dbengine"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/global"
	apiServer "icode.baidu.com/baidu/faas/kun/pkg/apiserver/server"
	"icode.baidu.com/baidu/faas/kun/pkg/server"
)

func newClientMock(c *server.Context) (*bosSdk.Client, error) {
	ts := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
		bytes := []byte{
			80, 75, 3, 4, 20, 0, 8, 0, 8, 0, 123, 137, 170, 76, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 13, 0, 16, 0, 100, 117, 101, 114, 111, 115, 98, 111, 116, 46, 116,
			120, 116, 85, 88, 12, 0, 217, 12, 244, 90, 217, 12, 244, 90, 246, 239, 186, 158, 75, 41, 77, 45, 202, 47, 6, 0, 80, 75, 7, 8, 177, 111, 113, 99, 8, 0, 0, 0,
			6, 0, 0, 0, 80, 75, 3, 4, 10, 0, 0, 0, 0, 0, 130, 137, 170, 76, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 9, 0, 16, 0, 95, 95, 77, 65, 67, 79, 83, 88, 47, 85, 88,
			12, 0, 227, 12, 244, 90, 227, 12, 244, 90, 246, 239, 186, 158, 80, 75, 3, 4, 20, 0, 8, 0, 8, 0, 123, 137, 170, 76, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 24, 0,
			16, 0, 95, 95, 77, 65, 67, 79, 83, 88, 47, 46, 95, 100, 117, 101, 114, 111, 115, 98, 111, 116, 46, 116, 120, 116, 85, 88, 12, 0, 217, 12, 244, 90, 217, 12, 244,
			90, 246, 239, 186, 158, 99, 96, 21, 99, 103, 96, 98, 96, 240, 77, 76, 86, 240, 15, 86, 136, 80, 128, 2, 144, 24, 3, 39, 16, 27, 49, 48, 48, 238, 0, 210, 64,
			62, 227, 43, 6, 162, 128, 99, 72, 72, 16, 132, 5, 210, 193, 104, 1, 100, 108, 66, 83, 194, 2, 21, 231, 103, 96, 16, 79, 206, 207, 213, 75, 44, 40, 200, 73, 213,
			11, 73, 173, 40, 113, 205, 75, 206, 79, 201, 204, 75, 135, 232, 119, 7, 18, 2, 12, 12, 82, 8, 53, 57, 137, 197, 37, 165, 197, 169, 41, 41, 137, 37, 169, 202, 1,
			193, 80, 123, 194, 129, 132, 22, 3, 131, 10, 66, 93, 110, 106, 73, 34, 80, 77, 162, 85, 124, 182, 175, 139, 103, 73, 106, 110, 104, 113, 106, 81, 72, 98, 122, 49,
			88, 125, 35, 144, 200, 100, 96, 48, 199, 162, 30, 168, 220, 39, 49, 41, 53, 39, 190, 188, 212, 40, 191, 194, 44, 181, 56, 53, 171, 180, 52, 167, 52, 199, 44, 183,
			176, 36, 173, 48, 41, 49, 49, 35, 23, 168, 185, 180, 36, 77, 215, 194, 218, 208, 216, 196, 200, 208, 220, 210, 194, 228, 18, 207, 151, 40, 144, 193, 157, 167, 98,
			69, 64, 116, 82, 65, 78, 102, 113, 137, 129, 193, 2, 14, 168, 3, 25, 161, 30, 135, 209, 48, 192, 249, 233, 136, 123, 83, 193, 165, 64, 193, 85, 223, 190, 218, 177,
			207, 205, 72, 255, 118, 87, 151, 113, 78, 188, 215, 234, 164, 169, 159, 52, 206, 243, 76, 125, 177, 233, 227, 35, 151, 143, 167, 79, 196, 156, 173, 158, 207, 230,
			155, 253, 213, 130, 173, 150, 231, 140, 128, 63, 155, 208, 85, 221, 210, 221, 29, 123, 31, 214, 197, 28, 241, 156, 51, 209, 123, 207, 177, 67, 154, 46, 210, 57, 7,
			254, 184, 253, 114, 191, 86, 49, 171, 113, 167, 197, 226, 159, 165, 158, 30, 49, 39, 230, 229, 29, 255, 156, 234, 188, 227, 211, 249, 107, 0, 80, 75, 7, 8, 211, 146,
			124, 33, 92, 1, 0, 0, 234, 1, 0, 0, 80, 75, 1, 2, 21, 3, 20, 0, 8, 0, 8, 0, 123, 137, 170, 76, 177, 111, 113, 99, 8, 0, 0, 0, 6, 0, 0, 0, 13, 0, 12, 0, 0, 0, 0, 0, 0,
			0, 0, 64, 164, 129, 0, 0, 0, 0, 100, 117, 101, 114, 111, 115, 98, 111, 116, 46, 116, 120, 116, 85, 88, 8, 0, 217, 12, 244, 90, 217, 12, 244, 90, 80, 75, 1, 2, 21, 3,
			10, 0, 0, 0, 0, 0, 130, 137, 170, 76, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 9, 0, 12, 0, 0, 0, 0, 0, 0, 0, 0, 64, 253, 65, 83, 0, 0, 0, 95, 95, 77, 65, 67, 79, 83, 88,
			47, 85, 88, 8, 0, 227, 12, 244, 90, 227, 12, 244, 90, 80, 75, 1, 2, 21, 3, 20, 0, 8, 0, 8, 0, 123, 137, 170, 76, 211, 146, 124, 33, 92, 1, 0, 0, 234, 1, 0, 0, 24, 0,
			12, 0, 0, 0, 0, 0, 0, 0, 0, 64, 164, 129, 138, 0, 0, 0, 95, 95, 77, 65, 67, 79, 83, 88, 47, 46, 95, 100, 117, 101, 114, 111, 115, 98, 111, 116, 46, 116, 120, 116, 85,
			88, 8, 0, 217, 12, 244, 90, 217, 12, 244, 90, 80, 75, 5, 6, 0, 0, 0, 0, 3, 0, 3, 0, 220, 0, 0, 0, 60, 2, 0, 0, 0, 0,
		}
		w.Write(bytes)
	}))

	b, _ := initBosClient(ts.URL)
	return b, nil
}

// var symlinkZipFile = "UEsDBAoAAAAAAMSbjVDCVPv+DQAAAA0AAAAKABwAYmx1ZXByaW50c1VUCQADP02UXj9NlF51eAsAAQT2AQAABBQAAAAuLi9ibHVlcHJpbnRzUEsDBBQAAAAIAMOTmlDxG1zIfwAAAMMAAAAIABwAaW5kZXguanNVVAkAA61ipV6tYqVedXgLAAEE9gEAAAQUAAAAbY1NCsJADIX3c4owm7agvUCpa2/gQroYp0GLIZEklYp4d5niqrh7fO+vmg3BXKfsVRdweYi6tbfEI6FCD8lenKHGJ7LvIAs7LkUkokvK9wb6A7wDFMeEsCW51vGIRAInURpjszHXpb/wHFl4j8tkHodt4vdcsKLPyrB2wqcLX1BLAQIeAwoAAAAAAMSbjVDCVPv+DQAAAA0AAAAKABgAAAAAAAAAAADtoQAAAABibHVlcHJpbnRzVVQFAAM/TZRedXgLAAEE9gEAAAQUAAAAUEsBAh4DFAAAAAgAw5OaUPEbXMh/AAAAwwAAAAgAGAAAAAAAAQAAAKSBUQAAAGluZGV4LmpzVVQFAAOtYqVedXgLAAEE9gEAAAQUAAAAUEsFBgAAAAACAAIAngAAABIBAAAAAA=="

func NewBosCodeManagerMock(bosClient *bosSdk.Client, bucketName string) code.CodeManagerInterface {
	return code.MockCode()
}

func TestCreateFunc(t *testing.T) {
	global.MockAC()
	code.MockCode()
	NewClientWrapper = newClientMock
	global.RegisterFaasRuntimeGovalidatorTag()

	cases := []struct {
		in_c         *server.Context
		out_HttpCode int
		sqlFunc      func()
	}{
		{
			//验证function Timeout错误
			in_c:         global.BuildNewKunCtx("POST", "/v1/functions", `{"FunctionName":"test-h","Code":{"ZipFile":"cc"},"MemorySize":128,"Description":"op","Timeout":800}`, "uiduid", map[string]string{}),
			out_HttpCode: 400,
			sqlFunc: func() {
				var m sqlmock.Sqlmock
				m, dbengine.Engine = global.MockDB()

				m.ExpectQuery("SELECT").WillReturnRows(global.GetRowsRuntimeConfig(global.GetTestRuntimeConfig(1)))
			},
		},
		{
			//	验证function MemorySize错误
			in_c:         global.BuildNewKunCtx("POST", "/v1/functions", `{"FunctionName":"test-h","Code":{"ZipFile":""},"MemorySize":121,"Description":"op","Timeout":80}`, "uiduid", map[string]string{}),
			out_HttpCode: 400,

			sqlFunc: func() {
				var m sqlmock.Sqlmock
				m, dbengine.Engine = global.MockDB()

				m.ExpectQuery("SELECT").WillReturnRows(global.GetRowsRuntimeConfig(global.GetTestRuntimeConfig(1)))
			},
		},
		{
			//	验证function MemorySize错误
			in_c:         global.BuildNewKunCtx("POST", "/v1/functions", `{"FunctionName":"test-h","Code":{"ZipFile":""},"MemorySize":1029,"Description":"op","Timeout":80}`, "uiduid", map[string]string{}),
			out_HttpCode: 400,

			sqlFunc: func() {
				var m sqlmock.Sqlmock
				m, dbengine.Engine = global.MockDB()

				m.ExpectQuery("SELECT").WillReturnRows(global.GetRowsRuntimeConfig(global.GetTestRuntimeConfig(1)))
			},
		},
		{
			//	验证function Description错误
			in_c:         global.BuildNewKunCtx("POST", "/v1/functions", `{"FunctionName":"test-h","Code":{"ZipFile":""},"MemorySize":128,"Description":"MemorySizeMemorySizeMemorySizeMemorySizeMemorySizeMemorySizeMemorySizeMemorySizeMemorySizeMemorySizeMemorySizeMemorySizeMemorySizeMemorySizeMemorySizeMemorySizeMemorySizeMemorySizeMemorySizeMemorySizeMemorySizeMemorySizeMemorySizeMemorySizeMemorySizeMemorySize","Timeout":80}`, "uiduid", map[string]string{}),
			out_HttpCode: 400,

			sqlFunc: func() {
				var m sqlmock.Sqlmock
				m, dbengine.Engine = global.MockDB()

				m.ExpectQuery("SELECT").WillReturnRows(global.GetRowsRuntimeConfig(global.GetTestRuntimeConfig(1)))
			},
		},
		{
			in_c:         global.BuildNewKunCtx("POST", "/v1/functions", `{"FunctionName":"test-h","Code":{"ZipFile":"UEsDBBQAAAAIALm5TEv12PdqZwAAAHYAAAAIABwAaW5kZXguanNVVAkAA66G31mvht9ZdXgLAAEE9QEAAAQUAAAANYpBCoMwFAXX5hSvWUUQLyC67jVS/dTSR74kv1Uovbu6cFYDM7Itmq20c0wTJaNHkK8kazBqMtlOieQjju8a/YCfq6qjFKW01Gfwq5b5BZNivu4cDq4/pA/ZwN+FVKyaOd3O59+5HVBLAQIeAxQAAAAIALm5TEv12PdqZwAAAHYAAAAIABgAAAAAAAEAAACkgQAAAABpbmRleC5qc1VUBQADrobfWXV4CwABBPUBAAAEFAAAAFBLBQYAAAAAAQABAE4AAACpAAAAAAA="},"MemorySize":128,"Description":"op","FunctionBrn":"brn:bce:cfc:bj:1a2cbf55b97ac8a7c760c4177db4e17d:function:test:$LATEST","Handler":"index.handler","Runtime":"nodejs6.11","Timeout":8}`, "uiduid", map[string]string{}),
			out_HttpCode: 201,
			sqlFunc: func() {
				var m sqlmock.Sqlmock
				m, dbengine.Engine = global.MockDB()
				m.ExpectQuery(".*").WillReturnRows(global.GetRowsRuntimeConfig(global.GetTestRuntimeConfig(1)))
				m.ExpectQuery(".*").WillReturnRows(global.GetRowsFunctions(global.GetTestFunc(2)))
				m.ExpectExec(".*").WillReturnResult(sqlmock.NewResult(1, 1))
				m.ExpectQuery(".*").WillReturnRows(global.GetRowsFunctions(global.GetTestFunc(1)))
				m.ExpectQuery(".*").WillReturnRows(global.GetRowsFunctions(global.GetTestFunc(1)))
			},
		},
		{
			in_c:         global.BuildNewKunCtx("POST", "/v1/functions", `{"FunctionName":"test-h","Code":{"ZipFile":"UEsDBBQAAAAIALm5TEv12PdqZwAAAHYAAAAIABwAaW5kZXguanNVVAkAA66G31mvht9ZdXgLAAEE9QEAAAQUAAAANYpBCoMwFAXX5hSvWUUQLyC67jVS/dTSR74kv1Uovbu6cFYDM7Itmq20c0wTJaNHkK8kazBqMtlOieQjju8a/YCfq6qjFKW01Gfwq5b5BZNivu4cDq4/pA/ZwN+FVKyaOd3O59+5HVBLAQIeAxQAAAAIALm5TEv12PdqZwAAAHYAAAAIABgAAAAAAAEAAACkgQAAAABpbmRleC5qc1VUBQADrobfWXV4CwABBPUBAAAEFAAAAFBLBQYAAAAAAQABAE4AAACpAAAAAAA="},"MemorySize":512,"Description":"op","FunctionBrn":"brn:bce:cfc:bj:1a2cbf55b97ac8a7c760c4177db4e17d:function:test:$LATEST","Handler":"faas-java-demo-1.0.0.jar:com.baidu.demo.App","Runtime":"java8","Timeout":8}`, "uiduid", map[string]string{}),
			out_HttpCode: 201,
			sqlFunc: func() {
				var m sqlmock.Sqlmock
				m, dbengine.Engine = global.MockDB()
				m.ExpectQuery(".*").WillReturnRows(global.GetRowsRuntimeConfig(global.GetTestRuntimeConfig(1)))
				m.ExpectQuery(".*").WillReturnRows(global.GetRowsFunctions(global.GetTestFunc(2)))
				m.ExpectExec(".*").WillReturnResult(sqlmock.NewResult(1, 1))
				m.ExpectQuery(".*").WillReturnRows(global.GetRowsFunctions(global.GetTestFunc(1)))
				m.ExpectQuery(".*").WillReturnRows(global.GetRowsFunctions(global.GetTestFunc(1)))
			},
		},
		{
			in_c:         global.BuildNewKunCtx("POST", "/v1/functions", `{"FunctionName":"test-h","Code":{"BosBucket":"hjj-bucket-test","BosObject":"index.py.zip"},"MemorySize":512,"Description":"op","FunctionBrn":"brn:bce:cfc:bj:1a2cbf55b97ac8a7c760c4177db4e17d:function:test:$LATEST","Handler":"faas-java-demo-1.0.0.jar:com.baidu.demo.App","Runtime":"java8","Timeout":8}`, "uiduid", map[string]string{}),
			out_HttpCode: 201,
			sqlFunc: func() {
				var m sqlmock.Sqlmock
				m, dbengine.Engine = global.MockDB()
				m.ExpectQuery(".*").WillReturnRows(global.GetRowsRuntimeConfig(global.GetTestRuntimeConfig(1)))
				m.ExpectQuery(".*").WillReturnRows(global.GetRowsFunctions(global.GetTestFunc(2)))
				m.ExpectExec(".*").WillReturnResult(sqlmock.NewResult(1, 1))
				m.ExpectQuery(".*").WillReturnRows(global.GetRowsFunctions(global.GetTestFunc(1)))
				m.ExpectQuery(".*").WillReturnRows(global.GetRowsFunctions(global.GetTestFunc(1)))
			},
		},
		{
			in_c:         global.BuildNewKunCtx("POST", "/v1/functions", `{"FunctionName":"test-h","Code":{"BosBucket":"hjj-bucket-test","BosObject":"index.py.zip","ZipFile":"123"},"MemorySize":512,"Description":"op","FunctionBrn":"brn:bce:cfc:bj:1a2cbf55b97ac8a7c760c4177db4e17d:function:test:$LATEST","Handler":"faas-java-demo-1.0.0.jar:com.baidu.demo.App","Runtime":"java8","Timeout":8}`, "uiduid", map[string]string{}),
			out_HttpCode: 400,
		},
		{
			in_c:         global.BuildNewKunCtx("POST", "/v1/functions", `{"FunctionName":"test-h","Code":{"BosBucket":"","BosObject":"","ZipFile":""},"MemorySize":512,"Description":"op","FunctionBrn":"brn:bce:cfc:bj:1a2cbf55b97ac8a7c760c4177db4e17d:function:test:$LATEST","Handler":"faas-java-demo-1.0.0.jar:com.baidu.demo.App","Runtime":"java8","Timeout":8}`, "uiduid", map[string]string{}),
			out_HttpCode: 400,
		},
		{
			in_c:         global.BuildNewKunCtx("POST", "/v1/functions", `{"FunctionName":"test-h","Code":{"ZipFile":"UEsDBBQAAAAIALm5TEv12PdqZwAAAHYAAAAIABwAaW5kZXguanNVVAkAA66G31mvht9ZdXgLAAEE9QEAAAQUAAAANYpBCoMwFAXX5hSvWUUQLyC67jVS/dTSR74kv1Uovbu6cFYDM7Itmq20c0wTJaNHkK8kazBqMtlOieQjju8a/YCfq6qjFKW01Gfwq5b5BZNivu4cDq4/pA/ZwN+FVKyaOd3O59+5HVBLAQIeAxQAAAAIALm5TEv12PdqZwAAAHYAAAAIABgAAAAAAAEAAACkgQAAAABpbmRleC5qc1VUBQADrobfWXV4CwABBPUBAAAEFAAAAFBLBQYAAAAAAQABAE4AAACpAAAAAAA="},"MemorySize":512,"Description":"op","FunctionBrn":"brn:bce:cfc:bj:1a2cbf55b97ac8a7c760c4177db4e17d:function:test:$LATEST","Handler":"faas-java-demo-1.0.0.jar:com.baidu.demo.App","Runtime":"java8","Timeout":8, "DeadLetterTopic":"c7ac82ae14ef42d1a4ffa3b2ececa17f__dead_letter"}`, "uiduid", map[string]string{}),
			out_HttpCode: 400,
		},

		// runtime depreate
		{
			in_c:         global.BuildNewKunCtx("POST", "/v1/functions", `{"FunctionName":"test-h","Code":{"BosBucket":"hjj-bucket-test","BosObject":"index.py.zip"},"MemorySize":512,"Description":"op","FunctionBrn":"brn:bce:cfc:bj:1a2cbf55b97ac8a7c760c4177db4e17d:function:test:$LATEST","Handler":"faas-java-demo-1.0.0.jar:com.baidu.demo.App","Runtime":"java8","Timeout":8}`, "uiduid", map[string]string{}),
			out_HttpCode: 400,
			sqlFunc: func() {
				var m sqlmock.Sqlmock
				m, dbengine.Engine = global.MockDB()

				rcs := global.GetTestRuntimeConfig(1)
				rcs[0].DeprecatedAt = time.Date(2011, 1, 1, 1, 1, 11, 1, time.Local)
				m.ExpectQuery("SELECT").WillReturnRows(global.GetRowsRuntimeConfig(rcs))
			},
		},
		{
			in_c:         global.BuildNewKunCtx("POST", "/v1/functions", `{"FunctionName":"test-h","Code":{"ZipFile":"UEsDBAoAAAAAAMSbjVDCVPv+DQAAAA0AAAAKABwAYmx1ZXByaW50c1VUCQADP02UXj9NlF51eAsAAQT2AQAABBQAAAAuLi9ibHVlcHJpbnRzUEsDBBQAAAAIAMOTmlDxG1zIfwAAAMMAAAAIABwAaW5kZXguanNVVAkAA61ipV6tYqVedXgLAAEE9gEAAAQUAAAAbY1NCsJADIX3c4owm7agvUCpa2/gQroYp0GLIZEklYp4d5niqrh7fO+vmg3BXKfsVRdweYi6tbfEI6FCD8lenKHGJ7LvIAs7LkUkokvK9wb6A7wDFMeEsCW51vGIRAInURpjszHXpb/wHFl4j8tkHodt4vdcsKLPyrB2wqcLX1BLAQIeAwoAAAAAAMSbjVDCVPv+DQAAAA0AAAAKABgAAAAAAAAAAADtoQAAAABibHVlcHJpbnRzVVQFAAM/TZRedXgLAAEE9gEAAAQUAAAAUEsBAh4DFAAAAAgAw5OaUPEbXMh/AAAAwwAAAAgAGAAAAAAAAQAAAKSBUQAAAGluZGV4LmpzVVQFAAOtYqVedXgLAAEE9gEAAAQUAAAAUEsFBgAAAAACAAIAngAAABIBAAAAAA=="},"MemorySize":512,"Description":"op","FunctionBrn":"brn:bce:cfc:bj:1a2cbf55b97ac8a7c760c4177db4e17d:function:test:$LATEST","Handler":"faas-java-demo-1.0.0.jar:com.baidu.demo.App","Runtime":"python3","Timeout":8}`, "uiduid", map[string]string{}),
			out_HttpCode: 400,
			sqlFunc: func() {
				var m sqlmock.Sqlmock
				m, dbengine.Engine = global.MockDB()
				m.ExpectQuery(".*").WillReturnRows(global.GetRowsRuntimeConfig(global.GetTestRuntimeConfig(1)))
				m.ExpectQuery(".*").WillReturnRows(global.GetRowsFunctions(global.GetTestFunc(2)))
			},
		},
	}
	for i, tc := range cases {
		f := FunctionRest{}
		if tc.sqlFunc != nil {
			tc.sqlFunc()
		}
		if i == 4 {
			tc.in_c.Request().Request.Header.Set("X-CFC-Workspace-Id", "test")
			tc.in_c.Request().Request.Header.Set(api.HeaderXCFCCreateFrom, "x-bce-bos")
		}
		f.create(tc.in_c)
		if tc.out_HttpCode != tc.in_c.Response().StatusCode() {
			continue
		}
		assert.Equal(t, tc.out_HttpCode, tc.in_c.Response().StatusCode())
	}
}

func TestFindFunc(t *testing.T) {
	global.MockAC()
	code.MockCode()

	cases := []struct {
		in_c         *server.Context
		out_HttpCode int
		sqlFunc      func()
	}{
		{
			in_c:         global.BuildNewKunCtx("GET", "/v1/functions/myfunc", "", "", map[string]string{}),
			out_HttpCode: 400,
		},
		{
			in_c:         global.BuildNewKunCtx("GET", "/v1/functions/myfunc", ``, "uiduid", map[string]string{}),
			out_HttpCode: 200,
			sqlFunc: func() {
				var m sqlmock.Sqlmock
				m, dbengine.Engine = global.MockDB()
				m.ExpectQuery(".*").WillReturnRows(global.GetRowsFunctions(global.GetTestFunc(1)))
				nc := global.GetTestNetworkConfig(1)
				m.ExpectQuery(".*").WillReturnRows(global.GetRowsNetworkConfigs(nc))
			},
		},
		{
			in_c:         global.BuildNewKunCtx("GET", "/v1/functions/myfunc", ``, "uiduid", map[string]string{}),
			out_HttpCode: 200,
			sqlFunc: func() {
				var m sqlmock.Sqlmock
				m, dbengine.Engine = global.MockDB()
				m.ExpectQuery(".*").WillReturnRows(global.GetRowsFunctions(global.GetTestFunc(1)))
				nc := global.GetTestNetworkConfig(1)
				m.ExpectQuery(".*").WillReturnRows(global.GetRowsNetworkConfigs(nc))
			},
		},
	}
	for i, tc := range cases {
		f := FunctionRest{}
		if tc.sqlFunc != nil {
			tc.sqlFunc()
		}
		if i == 2 {
			tc.in_c.Request().Request.Header.Set("X-CFC-Workspace-Id", "test")
		}
		f.find(tc.in_c)
		assert.Equal(t, tc.out_HttpCode, tc.in_c.Response().StatusCode())
	}
}

func TestInsideFind(t *testing.T) {
	global.MockAC()
	code.MockCode()

	cases := []struct {
		in_c         *server.Context
		out_HttpCode int
	}{
		{
			in_c:         global.BuildNewKunCtx("GET", "/v1/functions/myfunc", "", "", map[string]string{}),
			out_HttpCode: 401,
		},
		{
			in_c:         global.BuildNewKunCtx("GET", "/v1/functions/myfunc", ``, "uiduid", map[string]string{}),
			out_HttpCode: 401,
		},
		{
			in_c:         global.BuildNewKunCtx("GET", "/v1/functions/myfunc", ``, "uiduid", map[string]string{}),
			out_HttpCode: 200,
		},
		{
			in_c: global.BuildNewKunCtx("GET", "/v1/functions/brn:dueros", ``, "uiduid", map[string]string{
				"FunctionName": "brn:bce:cfc:bj:640c881:function:test:alias1",
			}),
			out_HttpCode: 200,
		},
	}
	for _, tc := range cases {
		var m sqlmock.Sqlmock
		m, dbengine.Engine = global.MockDB()
		f := FunctionRest{}
		if tc.out_HttpCode == 200 {
			tc.in_c.HTTPRequest().Header.Set(api.BceFaasUIDKey, "uiduid")
		}

		if tc.out_HttpCode == 200 && tc.in_c.Request().PathParameter("FunctionName") == "brn:bce:cfc:bj:640c881:function:test:alias1" {
			m.ExpectQuery(".*").WillReturnRows(global.GetRowsAliases(global.GetTestAlias(1)))
			tc.in_c.HTTPRequest().Header.Set(api.BceFaasTriggerKey, api.TriggerTypeDuerOS)
		}

		m.ExpectQuery(".*").WillReturnRows(global.GetRowsFunctions(global.GetTestFunc(1)))
		m.ExpectQuery(".*").WillReturnRows(global.GetRowsNetworkConfigs(global.GetTestNetworkConfig(0)))
		m.ExpectQuery(".*").WillReturnRows(global.GetCount(1))
		m.ExpectQuery(".*").WillReturnRows(global.GetRowsNetworkConfigs(global.GetTestNetworkConfig(0)))

		f.insideFind(tc.in_c)
		assert.Equal(t, tc.out_HttpCode, tc.in_c.Response().StatusCode())
	}
}

func TestInsideFindReservedFunc(t *testing.T) {
	t.Skip()
	global.MockAC()
	code.MockCode()

	cases := []struct {
		in_c         *server.Context
		out_HttpCode int
	}{
		{
			in_c:         global.BuildNewKunCtx("GET", "/v1/functions/myfunc", ``, "uiduid", map[string]string{}),
			out_HttpCode: 500,
		},
		{
			in_c:         global.BuildNewKunCtx("GET", "/v1/functions/myfunc", ``, "uiduid", map[string]string{}),
			out_HttpCode: 200,
		},
		{
			in_c:         global.BuildNewKunCtx("GET", "/v1/functions/myfunc", ``, "uiduid", map[string]string{}),
			out_HttpCode: 500,
		},
		{
			in_c:         global.BuildNewKunCtx("GET", "/v1/functions/myfunc", ``, "uiduid", map[string]string{}),
			out_HttpCode: 200,
		},
	}
	for i, tc := range cases {
		var m sqlmock.Sqlmock
		m, dbengine.Engine = global.MockDB()
		f := FunctionRest{}
		tc.in_c.HTTPRequest().Header.Set(api.BceFaasUIDKey, "uiduid")
		if i < 2 {
			tc.in_c.HTTPRequest().Header.Set(api.HeaderFunctionReservedType, api.FunctionReserved)
		}
		if i == 1 || i == 3 {
			m.ExpectQuery(".*").WillReturnRows(global.GetRowsFunctions(global.GetTestFunc(1)))
		}

		f.insideFind(tc.in_c)
		assert.Equal(t, tc.out_HttpCode, tc.in_c.Response().StatusCode())
	}
}

func TestFindByCondition(t *testing.T) {
	global.MockAC()
	code.MockCode()

	cases := []struct {
		in_c         *server.Context
		out_HttpCode int
	}{
		{
			in_c:         global.BuildNewKunCtx("GET", "/v1/functions", "", "", map[string]string{}),
			out_HttpCode: 400,
		},
		{
			in_c:         global.BuildNewKunCtx("GET", "/v1/functions", "", "", map[string]string{}),
			out_HttpCode: 400,
		},
		{
			in_c:         global.BuildNewKunCtx("GET", "/v1/functions?pageSize=1&page=0", ``, "uiduid", map[string]string{}),
			out_HttpCode: 200,
		},
		{
			in_c:         global.BuildNewKunCtx("GET", "/v1/functions?MaxItems=1&Marker=1", ``, "uiduid", map[string]string{}),
			out_HttpCode: 200,
		},
		{
			in_c:         global.BuildNewKunCtx("GET", "/v1/functions?MaxItems=1&Marker=1&FunctionVersion=ALL", ``, "uiduid", map[string]string{}),
			out_HttpCode: 200,
		},
		{
			in_c:         global.BuildNewKunCtx("GET", "/v1/functions?MaxItems=1&Marker=1&FunctionVersion=ALL", ``, "uiduid", map[string]string{}),
			out_HttpCode: 200,
		},
	}
	for i, tc := range cases {
		var m sqlmock.Sqlmock
		m, dbengine.Engine = global.MockDB()
		m.ExpectQuery(".*").WillReturnRows(global.GetRowsFunctions(global.GetTestFunc(1)))
		m.ExpectQuery(".*").WillReturnRows(global.GetRowsFunctions(global.GetTestFunc(2)))
		f := FunctionRest{}

		if i == 5 {
			tc.in_c.Request().Request.Header.Set("X-CFC-Workspace-Id", "test")
		}
		f.findByCondition(tc.in_c)
		assert.Equal(t, tc.out_HttpCode, tc.in_c.Response().StatusCode())
	}
}

func TestUpdateCode(t *testing.T) {
	global.MockAC()
	code.MockCode()
	NewClientWrapper = newClientMock

	cases := []struct {
		in_c         *server.Context
		out_HttpCode int
		sqlFunc      func()
	}{
		{
			in_c:         global.BuildNewKunCtx("PUT", "/v1/functions/myfunc/code", "err Json BODY", "uiduid", map[string]string{}),
			out_HttpCode: 400,
		},
		{
			//验证版本错误
			in_c: global.BuildNewKunCtx("PUT", "/v1/functions/brn:bce:cfc:bj:75f2642400b9aba21947780cc0bff054:function:myfunc:123/code", `{"ZipFile":"cc"}`, "uiduid", map[string]string{
				"FunctionName": "brn:bce:cfc:bj:75f2642400b9aba21947780cc0bff054:function:myfunc:123",
			}),
			out_HttpCode: 400,
		},
		{
			in_c:         global.BuildNewKunCtx("PUT", "/v1/functions/myfunc/code", `{"ZipFile":"UEsDBBQAAAAIALm5TEv12PdqZwAAAHYAAAAIABwAaW5kZXguanNVVAkAA66G31mvht9ZdXgLAAEE9QEAAAQUAAAANYpBCoMwFAXX5hSvWUUQLyC67jVS/dTSR74kv1Uovbu6cFYDM7Itmq20c0wTJaNHkK8kazBqMtlOieQjju8a/YCfq6qjFKW01Gfwq5b5BZNivu4cDq4/pA/ZwN+FVKyaOd3O59+5HVBLAQIeAxQAAAAIALm5TEv12PdqZwAAAHYAAAAIABgAAAAAAAEAAACkgQAAAABpbmRleC5qc1VUBQADrobfWXV4CwABBPUBAAAEFAAAAFBLBQYAAAAAAQABAE4AAACpAAAAAAA="}`, "uiduid", map[string]string{}),
			out_HttpCode: 200,
			sqlFunc: func() {
				var m sqlmock.Sqlmock
				m, dbengine.Engine = global.MockDB()

				m.ExpectQuery("SELECT").WillReturnRows(global.GetRowsFunctions(global.GetTestFunc(1)))
				m.ExpectExec("UPDATE").WillReturnResult(sqlmock.NewResult(1, 1))
				m.ExpectQuery("SELECT").WillReturnRows(global.GetRowsFunctions(global.GetTestFunc(1)))
				m.ExpectQuery("SELECT").WillReturnRows(global.GetRowsFunctions(global.GetTestFunc(1)))
				m.ExpectQuery("SELECT").WillReturnRows(global.GetRowsFunctions(global.GetTestFunc(1)))
			},
		},
		{
			in_c:         global.BuildNewKunCtx("PUT", "/v1/functions/myfunc/code", `{"ZipFile":"","BosBucket":"","BosObject":""}`, "uiduid", map[string]string{}),
			out_HttpCode: 400,
		},
		{
			in_c:         global.BuildNewKunCtx("PUT", "/v1/functions/myfunc/code", `{"ZipFile":"","BosBucket":"hjj-bucket-test","BosObject":"index.py.zip"}`, "uiduid", map[string]string{}),
			out_HttpCode: 200,
			sqlFunc: func() {
				var m sqlmock.Sqlmock
				m, dbengine.Engine = global.MockDB()

				m.ExpectQuery("SELECT").WillReturnRows(global.GetRowsFunctions(global.GetTestFunc(1)))
				m.ExpectExec("UPDATE").WillReturnResult(sqlmock.NewResult(1, 1))
				m.ExpectQuery("SELECT").WillReturnRows(global.GetRowsFunctions(global.GetTestFunc(1)))
				m.ExpectQuery("SELECT").WillReturnRows(global.GetRowsFunctions(global.GetTestFunc(1)))
				m.ExpectQuery("SELECT").WillReturnRows(global.GetRowsFunctions(global.GetTestFunc(1)))
			},
		},
		{
			in_c:         global.BuildNewKunCtx("PUT", "/v1/functions/myfunc/code", `{"ZipFile":"1","BosBucket":"1","BosObject":"1"}`, "uiduid", map[string]string{}),
			out_HttpCode: 400,
		},
		{
			in_c:         global.BuildNewKunCtx("PUT", "/v1/functions/myfunc/code", `{"ZipFile":"UEsDBAoAAAAAAMSbjVDCVPv+DQAAAA0AAAAKABwAYmx1ZXByaW50c1VUCQADP02UXj9NlF51eAsAAQT2AQAABBQAAAAuLi9ibHVlcHJpbnRzUEsDBBQAAAAIAMOTmlDxG1zIfwAAAMMAAAAIABwAaW5kZXguanNVVAkAA61ipV6tYqVedXgLAAEE9gEAAAQUAAAAbY1NCsJADIX3c4owm7agvUCpa2/gQroYp0GLIZEklYp4d5niqrh7fO+vmg3BXKfsVRdweYi6tbfEI6FCD8lenKHGJ7LvIAs7LkUkokvK9wb6A7wDFMeEsCW51vGIRAInURpjszHXpb/wHFl4j8tkHodt4vdcsKLPyrB2wqcLX1BLAQIeAwoAAAAAAMSbjVDCVPv+DQAAAA0AAAAKABgAAAAAAAAAAADtoQAAAABibHVlcHJpbnRzVVQFAAM/TZRedXgLAAEE9gEAAAQUAAAAUEsBAh4DFAAAAAgAw5OaUPEbXMh/AAAAwwAAAAgAGAAAAAAAAQAAAKSBUQAAAGluZGV4LmpzVVQFAAOtYqVedXgLAAEE9gEAAAQUAAAAUEsFBgAAAAACAAIAngAAABIBAAAAAA=="}`, "uiduid", map[string]string{}),
			out_HttpCode: 400,
			sqlFunc: func() {
				var m sqlmock.Sqlmock
				m, dbengine.Engine = global.MockDB()

				m.ExpectQuery("SELECT").WillReturnRows(global.GetRowsFunctions(global.GetTestFunc(1)))
			},
		},
	}
	for _, tc := range cases {
		f := FunctionRest{}
		if tc.sqlFunc != nil {
			tc.sqlFunc()
		}
		f.updateCode(tc.in_c)
		if tc.out_HttpCode != tc.in_c.Response().StatusCode() {
			continue
		}
		assert.Equal(t, tc.out_HttpCode, tc.in_c.Response().StatusCode())
	}
}

func TestUpdateConfiguration(t *testing.T) {
	global.MockAC()
	code.MockCode()
	NewClientWrapper = newClientMock
	global.RegisterFaasRuntimeGovalidatorTag()
	var m sqlmock.Sqlmock
	m, dbengine.Engine = global.MockDB()

	cases := []struct {
		in_c         *server.Context
		out_HttpCode int
		sqlFunc      func()
	}{
		{
			in_c:         global.BuildNewKunCtx("PUT", "/v1/functions/myfunc/configuration", "err Json BODY", "uiduid", map[string]string{}),
			out_HttpCode: 400,
		},
		{
			in_c:         global.BuildNewKunCtx("PUT", "/v1/functions/myfunc/configuration", `{"Timeout":80}`, "uiduid", map[string]string{}),
			out_HttpCode: 200,
			sqlFunc: func() {
				m.ExpectQuery("SELECT").WillReturnRows(global.GetRowsFunctions(global.GetTestFunc(1)))
				m.ExpectExec("UPDATE").WillReturnResult(sqlmock.NewResult(1, 1))
				m.ExpectQuery("SELECT").WillReturnRows(global.GetRowsAliases(global.GetTestAlias(1)))
				m.ExpectQuery("SELECT").WillReturnRows(global.GetRowsFunctions(global.GetTestFunc(1)))
			},
		},
		{
			in_c:         global.BuildNewKunCtx("PUT", "/v1/functions/myfunc/configuration", `{"Timeout":80, "Layers":[]}`, "uiduid", map[string]string{}),
			out_HttpCode: 200,
			sqlFunc: func() {
				m.ExpectQuery("SELECT").WillReturnRows(global.GetRowsFunctions(global.GetTestFunc(1)))
				m.ExpectExec("UPDATE").WillReturnResult(sqlmock.NewResult(1, 1))
				m.ExpectQuery("SELECT").WillReturnRows(global.GetRowsAliases(global.GetTestAlias(1)))
				m.ExpectQuery("SELECT").WillReturnRows(global.GetRowsFunctions(global.GetTestFunc(1)))
			},
		},
		{
			in_c:         global.BuildNewKunCtx("PUT", "/v1/functions/myfunc/configuration", `{"Timeout":80, "Layers":[{"Brn":"brn:bce:cfc:bj:640c8817bd1de2928d47256dd0620ce5:layer:consoletestlayer1:2"}]}`, "uiduid", map[string]string{}),
			out_HttpCode: 200,
			sqlFunc: func() {
				m.ExpectQuery("SELECT").WillReturnRows(global.GetRowsFunctions(global.GetTestFunc(1)))
				m.ExpectQuery("SELECT").WillReturnRows(global.GetRowsLayer(global.GetTestLayers(1)))
				m.ExpectExec("UPDATE").WillReturnResult(sqlmock.NewResult(1, 1))
				m.ExpectQuery("SELECT").WillReturnRows(global.GetRowsAliases(global.GetTestAlias(1)))
				m.ExpectQuery("SELECT").WillReturnRows(global.GetRowsFunctions(global.GetTestFunc(1)))
			},
		},
		{
			//验证版本错误
			in_c: global.BuildNewKunCtx("PUT", "/v1/functions/brn:bce:cfc:bj:75f2642400b9aba21947780cc0bff054:function:myfunc:$LATEST/configuration", `{"Version":"2", "LogType":"none"}`, "uiduid", map[string]string{
				"FunctionName": "brn:bce:cfc:bj:75f2642400b9aba21947780cc0bff054:function:myfunc:$LATEST",
			}),
			out_HttpCode: 400,
		},
		{
			in_c:         global.BuildNewKunCtx("PUT", "/v1/functions/myfunc/configuration", `{"Version":"80"}`, "uiduid", map[string]string{}),
			out_HttpCode: 200,
			sqlFunc: func() {
				m.ExpectQuery("SELECT").WillReturnRows(global.GetRowsFunctions(global.GetTestFunc(1)))
				m.ExpectExec("UPDATE").WillReturnResult(sqlmock.NewResult(1, 1))
				m.ExpectQuery("SELECT").WillReturnRows(global.GetRowsAliases(global.GetTestAlias(1)))
				m.ExpectQuery("SELECT").WillReturnRows(global.GetRowsFunctions(global.GetTestFunc(1)))
			},
		},
		{
			in_c:         global.BuildNewKunCtx("PUT", "/v1/functions/myfunc/configuration", `{"Code":{"ZipFile":"UEsDBBQAAAAIALm5TEv12PdqZwAAAHYAAAAIABwAaW5kZXguanNVVAkAA66G31mvht9ZdXgLAAEE9QEAAAQUAAAANYpBCoMwFAXX5hSvWUUQLyC67jVS/dTSR74kv1Uovbu6cFYDM7Itmq20c0wTJaNHkK8kazBqMtlOieQjju8a/YCfq6qjFKW01Gfwq5b5BZNivu4cDq4/pA/ZwN+FVKyaOd3O59+5HVBLAQIeAxQAAAAIALm5TEv12PdqZwAAAHYAAAAIABgAAAAAAAEAAACkgQAAAABpbmRleC5qc1VUBQADrobfWXV4CwABBPUBAAAEFAAAAFBLBQYAAAAAAQABAE4AAACpAAAAAAA="},"MemorySize":512,"Description":"op","FunctionBrn":"brn:bce:cfc:bj:1a2cbf55b97ac8a7c760c4177db4e17d:function:test:$LATEST","Handler":"faas-java-demo-1.0.0.jar:com.baidu.demo.App","Runtime":"java8","Timeout":8}`, "uiduid", map[string]string{}),
			out_HttpCode: 200,
			sqlFunc: func() {
				m.ExpectQuery("SELECT").WillReturnRows(global.GetRowsFunctions(global.GetTestFunc(1)))
				m.ExpectQuery("SELECT").WillReturnRows(global.GetRowsRuntimeConfig(global.GetTestRuntimeConfig(1)))
				m.ExpectExec("UPDATE").WillReturnResult(sqlmock.NewResult(1, 1))
				m.ExpectQuery("SELECT").WillReturnRows(global.GetRowsAliases(global.GetTestAlias(1)))
				m.ExpectQuery("SELECT").WillReturnRows(global.GetRowsFunctions(global.GetTestFunc(1)))
			},
		},
		{
			in_c:         global.BuildNewKunCtx("PUT", "/v1/functions/myfunc/configuration", `{"Code":{"BosBucket":"hjj-bucket-test","BosObject":"index.py.zip"},"MemorySize":512,"Description":"op","FunctionBrn":"brn:bce:cfc:bj:1a2cbf55b97ac8a7c760c4177db4e17d:function:test:$LATEST","Handler":"faas-java-demo-1.0.0.jar:com.baidu.demo.App","Runtime":"java8","Timeout":8,"DeadLetterTopic":""}`, "uiduid", map[string]string{}),
			out_HttpCode: 200,
			sqlFunc: func() {
				m.ExpectQuery("SELECT").WillReturnRows(global.GetRowsFunctions(global.GetTestFunc(1)))
				m.ExpectQuery("SELECT").WillReturnRows(global.GetRowsRuntimeConfig(global.GetTestRuntimeConfig(1)))
				m.ExpectExec("UPDATE").WillReturnResult(sqlmock.NewResult(1, 1))
				m.ExpectQuery("SELECT").WillReturnRows(global.GetRowsAliases(global.GetTestAlias(1)))
				m.ExpectQuery("SELECT").WillReturnRows(global.GetRowsFunctions(global.GetTestFunc(1)))
			},
		},
		{
			in_c:         global.BuildNewKunCtx("PUT", "/v1/functions/myfunc/configuration", `{"Code":{"BosBucket":"hjj-bucket-test","BosObject":"index.py.zip"},"MemorySize":512,"Description":"op","FunctionBrn":"brn:bce:cfc:bj:1a2cbf55b97ac8a7c760c4177db4e17d:function:test:$LATEST","Handler":"faas-java-demo-1.0.0.jar:com.baidu.demo.App","Runtime":"java8","Timeout":8,"DeadLetterTopic":"c7ac82ae14ef42d1a4ffa3b2ececa17f__dead_letter"}`, "uiduid", map[string]string{}),
			out_HttpCode: 500,
			sqlFunc: func() {
				m.ExpectQuery("SELECT").WillReturnRows(global.GetRowsFunctions(global.GetTestFunc(1)))
				m.ExpectQuery("SELECT").WillReturnRows(global.GetRowsRuntimeConfig(global.GetTestRuntimeConfig(1)))
			},
		},

		// deprecated runtime
		{
			in_c:         global.BuildNewKunCtx("PUT", "/v1/functions/myfunc/configuration", `{"Code":{"BosBucket":"hjj-bucket-test","BosObject":"index.py.zip"},"MemorySize":512,"Description":"op","FunctionBrn":"brn:bce:cfc:bj:1a2cbf55b97ac8a7c760c4177db4e17d:function:test:$LATEST","Handler":"faas-java-demo-1.0.0.jar:com.baidu.demo.App","Runtime":"java8","Timeout":8,"DeadLetterTopic":""}`, "uiduid", map[string]string{}),
			out_HttpCode: 400,
			sqlFunc: func() {
				m.ExpectQuery("SELECT").WillReturnRows(global.GetRowsFunctions(global.GetTestFunc(1)))
				rcs := global.GetTestRuntimeConfig(1)
				rcs[0].DeprecatedAt = time.Date(2011, 1, 1, 1, 1, 1, 1, time.Local)

				m.ExpectQuery("SELECT").WillReturnRows(global.GetRowsRuntimeConfig(rcs))
			},
		},
	}
	for k, tc := range cases {
		f := FunctionRest{}
		if tc.sqlFunc != nil {
			tc.sqlFunc()
		}
		f.updateConfiguration(tc.in_c)
		assert.Equal(t, tc.out_HttpCode, tc.in_c.Response().StatusCode())
		t.Logf("k %d response %+v", k, tc.in_c.Response().ResponseWriter)
	}
}

func TestGetConfiguration(t *testing.T) {
	global.MockAC()
	code.MockCode()
	var m sqlmock.Sqlmock
	m, dbengine.Engine = global.MockDB()
	m.ExpectQuery(".*").WillReturnRows(global.GetRowsFunctions(global.GetTestFunc(1)))
	m.ExpectQuery(".*").WillReturnRows(global.GetRowsNetworkConfigs(global.GetTestNetworkConfig(1)))
	cases := []struct {
		in_c         *server.Context
		out_HttpCode int
	}{
		{
			in_c:         global.BuildNewKunCtx("GET", "/v1/functions/myfunc", "", "", map[string]string{}),
			out_HttpCode: 400,
		},
		{
			in_c:         global.BuildNewKunCtx("GET", "/v1/functions/myfunc", ``, "uiduid", map[string]string{}),
			out_HttpCode: 200,
		},
	}
	for _, tc := range cases {
		f := FunctionRest{}
		f.getConfiguration(tc.in_c)
		assert.Equal(t, tc.out_HttpCode, tc.in_c.Response().StatusCode())
	}
}

func TestDeleteFunc(t *testing.T) {
	global.MockAC()
	var m sqlmock.Sqlmock
	m, dbengine.Engine = global.MockDB()
	m.ExpectQuery("SELECT").WillReturnRows(global.GetRowsFunctions(global.GetTestFunc(1)))
	m.ExpectQuery("SELECT").WillReturnRows(global.GetRowsAliases(nil))
	m.ExpectQuery("SELECT").WillReturnRows(global.GetRowsEventSource(nil))
	m.ExpectQuery("SELECT").WillReturnRows(global.GetRowsFunctionReserveds(nil))
	m.ExpectBegin()
	m.ExpectQuery("SELECT").WillReturnRows(global.GetRowsPolicy(nil))
	m.ExpectQuery("SELECT").WillReturnRows(global.GetRowsUserTestEvents(nil))
	m.ExpectExec("^DELETE (.+)").WillReturnResult(sqlmock.NewResult(0, 0))
	m.ExpectExec("^UPDATE (.+)").WillReturnResult(sqlmock.NewResult(0, 1))
	m.ExpectQuery("SELECT").WillReturnRows(global.GetRowsAliases(nil))
	m.ExpectExec("^DELETE (.+)").WillReturnResult(sqlmock.NewResult(0, 0))
	m.ExpectQuery("SELECT").WillReturnRows(global.GetRowsPolicy(nil))
	m.ExpectExec("^DELETE (.+)").WillReturnResult(sqlmock.NewResult(0, 0))
	m.ExpectCommit()

	cases := []struct {
		in_c         *apiServer.ApiServerContext
		out_HttpCode int
	}{
		{
			in_c:         global.BuildNewApiServerCtx("DELETE", "/v1/functions/myfunc", "", "", map[string]string{}),
			out_HttpCode: 400,
		},
		{
			in_c: global.BuildNewApiServerCtx("DELETE", "/v1/functions/myfunc", "", "uiduid", map[string]string{
				"FunctionName": "brn:bce:cfc:bj:75f2642400b9aba21947780cc0bff054:function:myfunc:$LATEST", //$LATEST不能被删除
			}),
			out_HttpCode: 409,
		},
		{
			in_c:         global.BuildNewApiServerCtx("DELETE", "/v1/functions/myfunc", "", "uiduid", map[string]string{"FunctionName": "myfunc", "Uid": "uiduid"}),
			out_HttpCode: 204,
		},
	}
	for _, tc := range cases {
		f := FunctionRest{}
		f.delete(tc.in_c)
		assert.Equal(t, tc.out_HttpCode, tc.in_c.Response().StatusCode())
	}
}

func TestUploadCodeToBos(t *testing.T) {
	global.MockAC()
	code.MockCode()
	cases := []struct {
		in_zipFile    string
		in_updateFunc *dao.Function
		out_err       error
	}{
		{
			in_zipFile: "",
			//in_updateFunc: updateFunc,
			out_err: nil,
		},
		{
			in_zipFile: "UEsDBBQACAgIAIumvE4AAAAAAAAAAAAAAAAIAAAAaW5kZXgucHkVjMsOgjAQAO9N+g8rXNTYVEHk8TMG6EJJmi2pW5C/t85xMpn8pOMn6GEhjbTBerD1JEUO6qpg9GahuYPIk2r+RgqDE9iejMNwxg2Jb6kixi9fOikgEZBjIMgKwv1t0Tn/KMpn9aqb9t4PYxrMNvsBUEsHCGs/OTtwAAAAdwAAAFBLAQIUABQACAgIAIumvE5rPzk7cAAAAHcAAAAIAAAAAAAAAAAAAAAAAAAAAABpbmRleC5weVBLBQYAAAAAAQABADYAAACmAAAAAAA=",
			//in_updateFunc: updateFunc,
			out_err: nil,
		},
		// {
		// 	in_zipFile: symlinkZipFile,
		// 	//in_updateFunc: updateFunc,
		// 	out_err: apiErr.NewInvalidZipFileException("unzip cross border", nil),
		// },
	}

	for _, tc := range cases {
		var m sqlmock.Sqlmock
		m, dbengine.Engine = global.MockDB()
		m.ExpectQuery("SELECT").WillReturnRows(global.GetRowsFunctions(global.GetTestFunc(1)))
		updateFunc := &dao.Function{
			Uid:         "uiduid",
			FunctionBrn: "bce:cfc:bj:75f2642400b9aba21947780cc0bff054",
		}
		updateFunc.FunctionName = "myFunc"
		tc.in_updateFunc = updateFunc
		err := uploadCodeToBos(tc.in_zipFile, tc.in_updateFunc)
		assert.Equal(t, tc.out_err, err)
	}
}

func TestUploadCodeToBosFromUserBos(t *testing.T) {
	global.MockAC()
	code.MockCode()

	// byteArray, _ := base64.DecodeString(symlinkZipFile)
	cases := []struct {
		in_object     []byte
		in_updateFunc *dao.Function
		out_err       error
	}{
		{
			in_object: []byte{
				80, 75, 3, 4, 20, 0, 8, 0, 8, 0, 123, 137, 170, 76, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 13, 0, 16, 0, 100, 117, 101, 114, 111, 115, 98, 111, 116, 46, 116,
				120, 116, 85, 88, 12, 0, 217, 12, 244, 90, 217, 12, 244, 90, 246, 239, 186, 158, 75, 41, 77, 45, 202, 47, 6, 0, 80, 75, 7, 8, 177, 111, 113, 99, 8, 0, 0, 0,
				6, 0, 0, 0, 80, 75, 3, 4, 10, 0, 0, 0, 0, 0, 130, 137, 170, 76, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 9, 0, 16, 0, 95, 95, 77, 65, 67, 79, 83, 88, 47, 85, 88,
				12, 0, 227, 12, 244, 90, 227, 12, 244, 90, 246, 239, 186, 158, 80, 75, 3, 4, 20, 0, 8, 0, 8, 0, 123, 137, 170, 76, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 24, 0,
				16, 0, 95, 95, 77, 65, 67, 79, 83, 88, 47, 46, 95, 100, 117, 101, 114, 111, 115, 98, 111, 116, 46, 116, 120, 116, 85, 88, 12, 0, 217, 12, 244, 90, 217, 12, 244,
				90, 246, 239, 186, 158, 99, 96, 21, 99, 103, 96, 98, 96, 240, 77, 76, 86, 240, 15, 86, 136, 80, 128, 2, 144, 24, 3, 39, 16, 27, 49, 48, 48, 238, 0, 210, 64,
				62, 227, 43, 6, 162, 128, 99, 72, 72, 16, 132, 5, 210, 193, 104, 1, 100, 108, 66, 83, 194, 2, 21, 231, 103, 96, 16, 79, 206, 207, 213, 75, 44, 40, 200, 73, 213,
				11, 73, 173, 40, 113, 205, 75, 206, 79, 201, 204, 75, 135, 232, 119, 7, 18, 2, 12, 12, 82, 8, 53, 57, 137, 197, 37, 165, 197, 169, 41, 41, 137, 37, 169, 202, 1,
				193, 80, 123, 194, 129, 132, 22, 3, 131, 10, 66, 93, 110, 106, 73, 34, 80, 77, 162, 85, 124, 182, 175, 139, 103, 73, 106, 110, 104, 113, 106, 81, 72, 98, 122, 49,
				88, 125, 35, 144, 200, 100, 96, 48, 199, 162, 30, 168, 220, 39, 49, 41, 53, 39, 190, 188, 212, 40, 191, 194, 44, 181, 56, 53, 171, 180, 52, 167, 52, 199, 44, 183,
				176, 36, 173, 48, 41, 49, 49, 35, 23, 168, 185, 180, 36, 77, 215, 194, 218, 208, 216, 196, 200, 208, 220, 210, 194, 228, 18, 207, 151, 40, 144, 193, 157, 167, 98,
				69, 64, 116, 82, 65, 78, 102, 113, 137, 129, 193, 2, 14, 168, 3, 25, 161, 30, 135, 209, 48, 192, 249, 233, 136, 123, 83, 193, 165, 64, 193, 85, 223, 190, 218, 177,
				207, 205, 72, 255, 118, 87, 151, 113, 78, 188, 215, 234, 164, 169, 159, 52, 206, 243, 76, 125, 177, 233, 227, 35, 151, 143, 167, 79, 196, 156, 173, 158, 207, 230,
				155, 253, 213, 130, 173, 150, 231, 140, 128, 63, 155, 208, 85, 221, 210, 221, 29, 123, 31, 214, 197, 28, 241, 156, 51, 209, 123, 207, 177, 67, 154, 46, 210, 57, 7,
				254, 184, 253, 114, 191, 86, 49, 171, 113, 167, 197, 226, 159, 165, 158, 30, 49, 39, 230, 229, 29, 255, 156, 234, 188, 227, 211, 249, 107, 0, 80, 75, 7, 8, 211, 146,
				124, 33, 92, 1, 0, 0, 234, 1, 0, 0, 80, 75, 1, 2, 21, 3, 20, 0, 8, 0, 8, 0, 123, 137, 170, 76, 177, 111, 113, 99, 8, 0, 0, 0, 6, 0, 0, 0, 13, 0, 12, 0, 0, 0, 0, 0, 0,
				0, 0, 64, 164, 129, 0, 0, 0, 0, 100, 117, 101, 114, 111, 115, 98, 111, 116, 46, 116, 120, 116, 85, 88, 8, 0, 217, 12, 244, 90, 217, 12, 244, 90, 80, 75, 1, 2, 21, 3,
				10, 0, 0, 0, 0, 0, 130, 137, 170, 76, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 9, 0, 12, 0, 0, 0, 0, 0, 0, 0, 0, 64, 253, 65, 83, 0, 0, 0, 95, 95, 77, 65, 67, 79, 83, 88,
				47, 85, 88, 8, 0, 227, 12, 244, 90, 227, 12, 244, 90, 80, 75, 1, 2, 21, 3, 20, 0, 8, 0, 8, 0, 123, 137, 170, 76, 211, 146, 124, 33, 92, 1, 0, 0, 234, 1, 0, 0, 24, 0,
				12, 0, 0, 0, 0, 0, 0, 0, 0, 64, 164, 129, 138, 0, 0, 0, 95, 95, 77, 65, 67, 79, 83, 88, 47, 46, 95, 100, 117, 101, 114, 111, 115, 98, 111, 116, 46, 116, 120, 116, 85,
				88, 8, 0, 217, 12, 244, 90, 217, 12, 244, 90, 80, 75, 5, 6, 0, 0, 0, 0, 3, 0, 3, 0, 220, 0, 0, 0, 60, 2, 0, 0, 0, 0,
			},
			//in_updateFunc: updateFunc,
			out_err: nil,
		},
		// {
		// 	in_object: byteArray,
		// 	//in_updateFunc: updateFunc,
		// 	out_err: apiErr.NewInvalidZipFileException("unzip cross border", nil),
		// },
	}

	for _, tc := range cases {
		var m sqlmock.Sqlmock
		m, dbengine.Engine = global.MockDB()
		m.ExpectQuery("SELECT").WillReturnRows(global.GetRowsFunctions(global.GetTestFunc(1)))
		updateFunc := &dao.Function{
			Uid:         "uiduid",
			FunctionBrn: "bce:cfc:bj:75f2642400b9aba21947780cc0bff054",
		}
		updateFunc.FunctionName = "myFunc"
		tc.in_updateFunc = updateFunc
		err := uploadCodeToBosFromUserBos(tc.in_object, tc.in_updateFunc)
		assert.Equal(t, tc.out_err, err)
	}
}

func TestDownloadCodeFromUserBos(t *testing.T) {
	cases := []struct {
		BosBucket string
		BosObject string
		out_err   error
	}{
		{
			BosBucket: "hjj-bucket-test",
			BosObject: "index.py.zip",
			out_err:   nil,
		},
	}

	bosClient, _ := newClientMock(nil)
	for _, tc := range cases {
		_, err := DownloadCodeFromUserBos(bosClient, tc.BosBucket, tc.BosObject)
		assert.Equal(t, tc.out_err, err)
	}
}

func initBosClient(endpoint string) (*bosSdk.Client, error) {
	return bosSdk.NewClient("abc", "abc", endpoint)
}

func TestBanFunction(t *testing.T) {
	global.MockAC()
	code.MockCode()

	cases := []struct {
		in_c           *server.Context
		out_HttpCode   int
		sqlFunc        func()
		functionConfig api.FunctionConfig
	}{
		{
			in_c: global.BuildNewKunCtx("PUT", "/inside-v1/functions/function_brn_0/ban", `{"Ban": true}`, "uid_0",
				map[string]string{"FunctionBrn": "function_brn_0", "Uid": "uiduid"}),
			out_HttpCode: http.StatusOK,
			sqlFunc: func() {
				var m sqlmock.Sqlmock
				m, dbengine.Engine = global.MockDB()
				m.ExpectExec("^UPDATE (.*)").WillReturnResult(sqlmock.NewResult(1, 1))
				m.ExpectQuery("SELECT").WillReturnRows(global.GetRowsFunctions(global.GetTestBanFunc(1)))
				m.ExpectQuery("SELECT").WillReturnRows(global.GetRowsFunctions(global.GetTestBanFunc(1)))
			},
			functionConfig: api.FunctionConfig{
				Ban: convert.Bool(true),
			},
		},
		{
			in_c: global.BuildNewKunCtx("PUT", "/inside-v1/functions/function_brn_0/ban", `{"LimitTimeout": 1, "LimitMemorySize": 128}`, "uid_0",
				map[string]string{"FunctionBrn": "function_brn_0", "Uid": "uiduid"}),
			out_HttpCode: http.StatusOK,
			sqlFunc: func() {
				var m sqlmock.Sqlmock
				m, dbengine.Engine = global.MockDB()
				m.ExpectExec("^UPDATE (.*)").WillReturnResult(sqlmock.NewResult(1, 1))
				m.ExpectQuery("SELECT").WillReturnRows(global.GetRowsFunctions(global.GetTestBanFunc(1)))
				m.ExpectQuery("SELECT").WillReturnRows(global.GetRowsFunctions(global.GetTestBanFunc(1)))
			},
			functionConfig: api.FunctionConfig{
				LimitTimeout:    convert.Int(1),
				LimitMemorySize: convert.Int(128),
			},
		},
		{
			in_c: global.BuildNewKunCtx("PUT", "/inside-v1/functions/function_brn_0/ban", `{"LimitMaxRetryAttempts": 1}`, "uid_0",
				map[string]string{"FunctionBrn": "function_brn_0", "Uid": "uiduid"}),
			out_HttpCode: http.StatusOK,
			sqlFunc: func() {
				var m sqlmock.Sqlmock
				m, dbengine.Engine = global.MockDB()
				m.ExpectExec("^UPDATE (.*)").WillReturnResult(sqlmock.NewResult(1, 1))
				m.ExpectQuery("SELECT").WillReturnRows(global.GetRowsFunctions(global.GetTestBanFunc(1)))
				m.ExpectQuery("SELECT").WillReturnRows(global.GetRowsFunctions(global.GetTestBanFunc(1)))
			},
			functionConfig: api.FunctionConfig{
				LimitMaxRetryAttempts: convert.Int(1),
			},
		},
	}

	for i, tc := range cases {
		f := FunctionRest{}
		if tc.sqlFunc != nil {
			tc.sqlFunc()
		}
		tc.in_c.Request().Request.Header.Set("X-CFC-Workspace-Id", "test")
		tc.in_c.Request().Request.Header.Set("X-Auth-Token", "cfc-auth-2018")
		f.banFunction(tc.in_c)
		assert.Equal(t, tc.out_HttpCode, tc.in_c.Response().StatusCode())
		switch i {
		case 0:
			assert.Equal(t, *tc.functionConfig.Ban, true)
		case 1:
			assert.Equal(t, *tc.functionConfig.LimitMemorySize, 128)
			assert.Equal(t, *tc.functionConfig.LimitTimeout, 1)
		case 2:
			assert.Equal(t, *tc.functionConfig.LimitMaxRetryAttempts, 1)
		}
	}
}

func TestUnBlockFunction(t *testing.T) {
	global.MockAC()
	code.MockCode()

	cases := []struct {
		in_c           *server.Context
		out_HttpCode   int
		sqlFunc        func()
		functionConfig api.FunctionConfig
	}{
		{
			in_c: global.BuildNewKunCtx("PUT", "/inside-v1/functions/function_brn_0/unblock", `{}`, "uid_0",
				map[string]string{"FunctionBrn": "function_brn_0", "Uid": "uiduid"}),
			out_HttpCode: http.StatusOK,
			sqlFunc: func() {
				var m sqlmock.Sqlmock
				m, dbengine.Engine = global.MockDB()
				m.ExpectExec("^UPDATE (.*)").WillReturnResult(sqlmock.NewResult(1, 1))
				m.ExpectQuery("SELECT").WillReturnRows(global.GetRowsFunctions(global.GetTestFunc(1)))
				m.ExpectQuery("SELECT").WillReturnRows(global.GetRowsFunctions(global.GetTestFunc(1)))
			},
			functionConfig: api.FunctionConfig{
				Ban:                   convert.Bool(false),
				LimitMemorySize:       convert.Int(0),
				LimitTimeout:          convert.Int(0),
				LimitMaxRetryAttempts: convert.Int(0),
			},
		},
	}

	for _, tc := range cases {
		f := FunctionRest{}
		if tc.sqlFunc != nil {
			tc.sqlFunc()
		}
		tc.in_c.Request().Request.Header.Set("X-CFC-Workspace-Id", "test")
		tc.in_c.Request().Request.Header.Set("X-Auth-Token", "cfc-auth-2018")
		f.unblockFunction(tc.in_c)
		assert.Equal(t, tc.out_HttpCode, tc.in_c.Response().StatusCode())
		assert.Equal(t, *tc.functionConfig.LimitMaxRetryAttempts, 0)
		assert.Equal(t, *tc.functionConfig.LimitTimeout, 0)
		assert.Equal(t, *tc.functionConfig.LimitMemorySize, 0)
		assert.Equal(t, *tc.functionConfig.Ban, false)
	}
}
