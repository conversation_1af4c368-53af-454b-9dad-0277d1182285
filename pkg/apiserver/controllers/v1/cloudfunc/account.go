package cloudfunc

import (
	"net/http"

	"github.com/emicklei/go-restful"
	"icode.baidu.com/baidu/faas/kun/cmd/apiserver/filter"
	"icode.baidu.com/baidu/faas/kun/pkg/api"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/dao"
	apiErr "icode.baidu.com/baidu/faas/kun/pkg/apiserver/error"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/global"
	"icode.baidu.com/baidu/faas/kun/pkg/bce/iam"
	"icode.baidu.com/baidu/faas/kun/pkg/server"
	"icode.baidu.com/baidu/faas/kun/pkg/server/endpoint"
	"icode.baidu.com/baidu/faas/kun/pkg/util/convert"
)

// AccountRest xxx
type AccountRest struct{}

// APIs xxx
func (r *AccountRest) APIs() []endpoint.ApiSingle {
	apis := []endpoint.ApiSingle{
		// find account-setting by id, GET /2016-08-19/account-settings/
		endpoint.ApiSingle{
			Verb:    "GET",
			Path:    "/account-settings/",
			Handler: server.WrapRestRouteFunc(r.getAccountSettings),
			Filters: []restful.FilterFunction{
				filter.IAMPermissionCheck("*", "", []string{"cfc:GetAccountSettings", filter.IamPermRead}),
			},
		},
	}
	return apis
}

func (r *AccountRest) getAccountSettings(c *server.Context) {
	ctx := global.BuildApiserverContext(c.Request(), c.Response(), "getAccountSettings")
	user, err := GetUser(c.Request())
	if err != nil {
		c.WithWarnLog(apiErr.NewUserNotFoundException(err.Error(), nil)).WriteTo(c.Response())
		return
	}

	var (
		funcTmp interface{}
		funcs   *[]dao.Function
	)
	if funcTmp, err = ctx.Observer.NewStage(global.ListFunctionsStage).ObserveObject(getUserAllFuncs(user.Domain.ID)); err != nil {
		c.WithWarnLog(err).WriteTo(c.Response())
		return
	}
	funcs = funcTmp.(*[]dao.Function)
	resp := &api.AccountSettingResponse{
		AccountUsage: getAccountUsage(funcs),
		AccountLimit: getAccountLimit(c, user, funcs),
	}

	defer global.WriteSummary(ctx, api.LogNotToSummary)
	c.Response().WriteHeaderAndEntity(http.StatusOK, resp)
}

// FunctionCount 只计算$LATEST数量，CodeSize计算所有版本的code大小。
func getAccountUsage(funcs *[]dao.Function) *api.AccountUsage {
	count, size := 0, 0
	for _, f := range *funcs {
		size += int(f.CodeSize)
		if f.Version == "$LATEST" {
			count++
		}
	}

	return &api.AccountUsage{
		FunctionCount: convert.Int(count),
		TotalCodeSize: convert.Int(size),
	}
}

func getAccountLimit(c *server.Context, user *iam.User, funcs *[]dao.Function) *api.AccountLimit {
	accountConcurrency, err := global.AC.Clients.Ops.GetQuota(c.Context(), user.ID, "user_concurrency")
	if err != nil {
		// 访问opscenter失败，使用默认值
		accountConcurrency = api.DefaultAccountConcurrency
		c.Logger().Errorf("get quota from opscenter failed", err.Error())
	}

	reservedSum := countReservedSum(funcs)

	return &api.AccountLimit{
		CodeSizeUnzipped:     makeIntPointer(api.DefaultCodeSizeLimitUnzipped),
		CodeSizeZipped:       makeIntPointer(api.DefaultCodeSizeLimitZipped),
		ConcurrentExecutions: &accountConcurrency,
		TotalCodeSize:        makeIntPointer(api.DefaultTotalCodeSizeLimit),

		UnreservedConcurrentExecutions: convert.Int(accountConcurrency - reservedSum),
	}
}

func getUserAllFuncs(uid string) (*[]dao.Function, error) {
	findCond := &dao.Function{Uid: uid}
	funcs, err := dao.FindFunc(findCond)
	if err != nil {
		return nil, err
	}
	return funcs, nil
}

func countReservedSum(funcs *[]dao.Function) int {
	reservedSum := 0
	for _, f := range *funcs {
		if f.Version == "$LATEST" {
			reservedSum += *f.ReservedConcurrentExecutions
		}
	}
	return reservedSum
}

func makeIntPointer(n uint64) *int {
	x := int(n)
	return &x
}
