package cloudfunc

import (
	"fmt"
	"testing"

	"github.com/stretchr/testify/assert"

	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/dao"
)

func TestCheckEvent(t *testing.T) {
	cases := []struct {
		in_event string
		out_err  error
	}{
		{
			in_event: "{\"key1\":\"value1\"}",
			out_err:  nil,
		},
		{
			in_event: "{\"key1\":\"value1\"1}",
			out_err:  fmt.<PERSON>rro<PERSON>("event invalid"),
		},
	}
	for _, tc := range cases {
		err := CheckEvent(tc.in_event)
		assert.Equal(t, tc.out_err, err)
	}
}

func TestCheckTestEvent(t *testing.T) {
	cases := []struct {
		in_event *dao.UserTestEvent
		out_err  error
	}{
		{
			in_event: &dao.UserTestEvent{},
			out_err:  fmt.<PERSON>rrorf("FunctionName is empty"),
		},
		{
			in_event: &dao.UserTestEvent{
				FunctionName: "test1",
			},
			out_err: fmt.<PERSON>rro<PERSON>("title is invalid"),
		},
		{
			in_event: &dao.UserTestEvent{
				FunctionName: "test1",
				Title:        "!test",
			},
			out_err: fmt.Errorf("title is invalid"),
		},
		{
			in_event: &dao.UserTestEvent{
				FunctionName: "test1",
				Title:        "testtesttesttesttesttesttest",
			},
			out_err: fmt.Errorf("title is invalid"),
		},
		{
			in_event: &dao.UserTestEvent{
				FunctionName: "test1",
				Title:        "test_1",
				Event:        "{\"key1\":\"value1\"1}",
			},
			out_err: fmt.Errorf("event invalid"),
		},
		{
			in_event: &dao.UserTestEvent{
				FunctionName: "test1",
				Title:        "test_1",
				Event:        "{\"key1\":\"value1\"}",
			},
			out_err: nil,
		},
	}
	for _, tc := range cases {
		err := CheckTestEvent(tc.in_event)
		assert.Equal(t, tc.out_err, err)
	}
}
