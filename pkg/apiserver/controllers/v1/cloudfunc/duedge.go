package cloudfunc

import (
	"net/http"

	"icode.baidu.com/baidu/faas/kun/pkg/api"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/dao"
	apiErr "icode.baidu.com/baidu/faas/kun/pkg/apiserver/error"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/global"
	"icode.baidu.com/baidu/faas/kun/pkg/bce/brn"
	kunErr "icode.baidu.com/baidu/faas/kun/pkg/error"
	"icode.baidu.com/baidu/faas/kun/pkg/server"
	"icode.baidu.com/baidu/faas/kun/pkg/whitelist"
)

func (f FunctionRest) duEdgeFind(c *server.Context) {
	response := c.Response()

	ctx := global.BuildApiserverContext(c.Request(), c.Response(), "duEdgeFind")
	user, err := GetUser(c.Request())
	if err != nil {
		c.WithWarnLog(apiErr.NewUserNotFoundException(err.Error(), nil)).WriteTo(response)
		return
	}
	c.Logger().V(9).Infof("user token(%+v) Domain (%+v)", user, user.Domain)
	var realUser string
	realUser = user.Domain.ID
	if realUser == "default" || realUser == "Default" {
		realUser = user.ID
	}
	whiteListInfo, ok := global.AC.Config.SourceWhiteList.Get(realUser)
	if !ok || whiteListInfo.Source != whitelist.WhiteListSourceDuEdge {
		c.WithWarnLog(kunErr.NewUnrecognizedClientException("no whitelist permission", nil)).WriteTo(response)
		return
	}
	c.Logger().V(9).Infof("hit white list %s %s", whiteListInfo.UserId, whiteListInfo.Source)
	// 解析brn
	cfcBrn, err := brn.Parse(c.Request().PathParameter("FunctionName"))
	if err != nil {
		c.WithWarnLog(kunErr.NewInvalidParameterValueException("brn error", nil)).WriteTo(response)
		return
	}
	// 通过passport 获取userid
	name := c.Request().QueryParameter("name")
	if name == "" {
		c.WithWarnLog(kunErr.NewInvalidParameterValueException("name error", nil)).WriteTo(response)
		return
	}
	userAccount, err := global.AC.Clients.Iam.GetAccount(c.Request().QueryParameter("name"))
	if err != nil {
		c.WithWarnLog(kunErr.NewUnrecognizedClientException("get cloud user error", nil)).WriteTo(response)
		return
	}
	if brn.Md5BceUid(userAccount.Account.DomainId) != cfcBrn.AccountID {
		c.WithWarnLog(kunErr.NewUnrecognizedClientException("the function not belong to this name", nil)).WriteTo(response)
		return
	}
	findFunc := new(dao.Function)
	findFunc.FunctionBrn = cfcBrn.String()
	ctx.Context.FunctionBrn = findFunc.FunctionBrn
	if rkunErr := ctx.Observer.NewStage(global.CheckFunctionStage).Observe(dao.FindOneFunc(findFunc)); rkunErr != nil {
		c.WithWarnLog(rkunErr).WriteTo(response)
		return
	}
	findFunc.DealResFunction()

	var (
		plcysTmp, urlTmp interface{}
	)
	// 检查是否有duedge触发器
	if global.AC.Config.EnableCheckDuedgeTrigger {
		plcy := &dao.Policy{
			Resource:         findFunc.FunctionBrn,
			PrincipalService: api.DuEdgePrincipalService,
			Effect:           api.PolicyActionAllow,
			Action:           api.CfcInvokeAction,
		}

		if plcysTmp, err = ctx.Observer.NewStage(global.ListPolicyStage).ObserveObject(dao.ListPolicies(plcy)); err != nil {
			c.WithWarnLog(err).WriteTo(response)
			return
		}

		plcySlice := plcysTmp.(*[]dao.Policy)
		found := false
		for _, p := range *plcySlice {
			if p.Source == "" && p.SourceAccount == "" {
				found = true
				break
			}
		}
		if !found {
			c.WithWarnLog(apiErr.NewResourceNotFoundException("duedge trigger not found", nil)).WriteTo(response)
			return
		}
	}

	if urlTmp, err = ctx.Observer.NewStage(global.DownloadCodeStage).ObserveObject(global.AC.Clients.Code.FaasCodeDownloadUrl(findFunc.FunctionName, findFunc.Uid, findFunc.CodeSha256, findFunc.CodeID)); err != nil {
		c.Logger().Errorf("get download url failed, [func: %v][errmsg:%s]", findFunc, err.Error())
	}
	url := urlTmp.(string)
	defer global.WriteSummary(ctx, api.LogNotToSummary)
	response.WriteHeaderAndEntity(http.StatusOK, map[string]interface{}{
		"Code":          api.CodeStorage{Location: url},
		"Configuration": findFunc,
	})
}
