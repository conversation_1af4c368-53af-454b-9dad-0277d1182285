package cloudfunc

import (
	"net/http"
	"testing"

	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/global"

	"github.com/stretchr/testify/assert"
)

// TestPublicAPIs 本文件第一个被执行的case
func TestCFSPublicAPIs(t *testing.T) {
	CFSRest{}.PublicAPIs()
}

func TestCFSRestList(t *testing.T) {
	global.MockAC()
	global.MockDB()
	cfs := CFSRest{}
	c := global.BuildNewKunCtx("GET", "/cfsInfo/{SubnetId}", "", "uid", map[string]string{
		"SubnetId": "subnetid",
	})

	cfs.list(c)
	assert.Equal(t, http.StatusOK, c.Response().StatusCode())
}
