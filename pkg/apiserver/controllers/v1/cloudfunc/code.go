package cloudfunc

import (
	"fmt"
	"math"
	"net/http"
	"strconv"
	"strings"
	"time"

	"go.uber.org/zap"
	"icode.baidu.com/baidu/faas/kun/pkg/api"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/code"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/dao"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/global"
	kunErr "icode.baidu.com/baidu/faas/kun/pkg/error"
	"icode.baidu.com/baidu/faas/kun/pkg/server"
	"icode.baidu.com/baidu/faas/kun/pkg/server/endpoint"
	"icode.baidu.com/baidu/faas/kun/pkg/util/logs"
)

// CodeRest xxx
type CodeRest struct {
	Path string
}

type Sweeper struct {
	CodeExpiration  int64
	TrashExpiration int64
	Logger          *logs.Logger
	Junks           []string
}

// InsideAPIs xxx
func (r CodeRest) InsideAPIs() []endpoint.ApiSingle {
	apis := []endpoint.ApiSingle{
		// GET /inside-v1/functions/codefile
		endpoint.ApiSingle{
			Verb:    "GET",
			Path:    "/functions/codefile",
			Handler: server.WrapRestRouteFunc(r.remove),
		},
	}
	return apis
}

func (r *CodeRest) remove(c *server.Context) {
	codeReq := c.Request().QueryParameter("codeExpiration")
	trashReq := c.Request().QueryParameter("trashExpiration")

	codeExpiration, err1 := strconv.ParseInt(codeReq, 10, 64)
	trashExpiration, err2 := strconv.ParseInt(trashReq, 10, 64)
	if err1 != nil || err2 != nil {
		c.WithErrorLog(kunErr.NewInvalidParameterValueException("invalid expiration parameter, task exited", nil)).WriteTo(c.Response())
		return
	}

	c.Logger().Info("recieve a sweep request.", zap.Int64("codeExpiration", codeExpiration), zap.Int64("trashExpiration", trashExpiration))

	s := &Sweeper{
		CodeExpiration:  codeExpiration,
		TrashExpiration: trashExpiration,
		Logger:          c.Logger(),
		Junks:           make([]string, 0),
	}

	go func() {
		folders, err := s.getFolderListFromBos()
		if err != nil {
			s.Logger.Errorf("list folders from bos failed, err: %v", err)
			return
		}

		s.sweepCode(folders)
		if err := s.verifyResult(folders); err != nil {
			s.Logger.Errorf("verify result failed, err: %v", err)
			return
		}
		s.Logger.Info("verify result done.")

		s.sweepTrash()
	}()

	c.Response().WriteHeader(http.StatusOK)
}

func (r *Sweeper) sweepCode(folders []code.CommonPrefix) (err error) {
	for _, f := range folders {
		// 检查是否为 uid，过滤掉 .trash、blueprints 文件夹
		folderName := f.Prefix[0 : len(f.Prefix)-1]
		if !isUID(folderName) {
			r.Logger.Infof("folder name '%s' is not a uid, ingore it", folderName)
			continue
		}

		if err = r.scanOneUser(folderName); err != nil {
			r.Logger.Error("scan user's code failed", zap.String("error", err.Error()), zap.String("uid", folderName))
		}

		time.Sleep(100 * time.Millisecond)
	}

	r.deleteJunks()
	r.Logger.Info("sweep code done.")
	return
}

func (r *Sweeper) scanOneUser(uid string) error {
	needed, err := r.getNeededObjects(uid)
	if err != nil {
		return err
	}

	var res *code.ListObjectsResult
	args := &code.ListObjectsArgs{
		MaxKeys: 1000,
		Prefix:  uid,
	}

	for {
		if res != nil {
			args.Marker = res.NextMarker
		}

		res, err = global.AC.Clients.Code.ListObjects(args)
		if err != nil {
			return err
		}

		r.filterJunk(res, needed)

		if res.IsTruncated == false {
			break
		}
	}
	return nil
}

// 一个函数有 .zip .sqfs 两个文件
// 若函数引用了 layers，那么还有一个与 layers 合并后的 .sqfs 文件
func (r *Sweeper) getNeededObjects(uid string) (map[string]bool, error) {
	fs := make(map[string]bool, 0)
	funcs, err := dao.FindAllFuncs(&dao.Function{Uid: uid})
	if err != nil {
		return nil, err
	}

	for _, f := range *funcs {
		if f.CodeID == "" {
			continue
		}

		fs[code.GetFuncZipKey(uid, f.FunctionName, f.CodeID)] = true
		fs[code.GetFuncSquashfsKey(uid, f.FunctionName, f.CodeID)] = true
		if f.LayerSha256 != nil && *f.LayerSha256 != "" {
			fs[code.GetFunctionLayersSquashFsKey(uid, f.FunctionName, *f.LayerSha256)] = true
		}
	}
	return fs, nil
}

// 过滤出没有函数引用、且过期的 object
func (r *Sweeper) filterJunk(res *code.ListObjectsResult, needed map[string]bool) {
	for _, obj := range res.Contents {
		if strings.Contains(obj.Key, "/layer/") {
			continue
		}

		if _, ok := needed[obj.Key]; !ok && r.isExpired(obj.LastModified, r.CodeExpiration) {
			r.Logger.Info("found an obsolete code zipfile", zap.String("objectKey", obj.Key))
			r.Junks = append(r.Junks, obj.Key)
		}
	}
}

func (r *Sweeper) verifyResult(folders []code.CommonPrefix) (ret error) {
	for _, f := range folders {
		folderName := f.Prefix[0 : len(f.Prefix)-1]
		if !isUID(folderName) {
			continue
		}

		if err := r.verifyOneUser(folderName); err != nil {
			ret = err
		}
		time.Sleep(100 * time.Millisecond)
	}

	return
}

func (r *Sweeper) verifyOneUser(uid string) error {
	funcs, err := dao.FindAllFuncs(&dao.Function{Uid: uid})
	if err != nil {
		r.Logger.Errorf("find funcs failed: %v", err)
		return err
	}

	keys := make([]string, 0)
	for _, f := range *funcs {
		if f.CodeID == "" {
			continue
		}

		keys = append(keys, code.GetFuncZipKey(f.Uid, f.FunctionName, f.CodeID))
		keys = append(keys, code.GetFuncSquashfsKey(f.Uid, f.FunctionName, f.CodeID))

		if f.LayerSha256 != nil && *f.LayerSha256 != "" {
			keys = append(keys, code.GetFunctionLayersSquashFsKey(f.Uid, f.FunctionName, *f.LayerSha256))
		}
	}

	var res *code.ListObjectsResult
	currObjects := make(map[string]bool, 0)

	args := &code.ListObjectsArgs{
		MaxKeys: 1000,
		Prefix:  uid,
	}

	for {
		if res != nil {
			args.Marker = res.NextMarker
		}

		res, err = global.AC.Clients.Code.ListObjects(args)
		if err != nil {
			r.Logger.Errorf("list user %s objects failed, err: %v", uid, err)
			return err
		}

		for _, obj := range res.Contents {
			currObjects[obj.Key] = true
		}

		if res.IsTruncated == false {
			break
		}
	}

	succ := true
	for _, k := range keys {
		if _, ok := currObjects[k]; !ok {
			r.Logger.Errorf("missing object: %s, need to check out", k)
			succ = false

			err := global.AC.Clients.Code.CopyObjectFromTrash(k)
			if err != nil {
				r.Logger.Errorf("%s failed to recover from trash, err: %v", k, err)
			} else {
				r.Logger.Infof("%s recovered from trash successfully", k)
			}
		}
	}

	if !succ {
		return fmt.Errorf("verify %s abnormal", uid)
	}
	return nil
}

// 读取 .trash 文件夹里的所有 object，清除掉 expiration 之前的
func (r *Sweeper) sweepTrash() (err error) {
	args := &code.ListObjectsArgs{
		Prefix:  ".trash",
		MaxKeys: 1000,
	}

	var res *code.ListObjectsResult

	for {
		if res != nil {
			args.Marker = res.NextMarker
		}

		res, err = global.AC.Clients.Code.ListObjects(args)
		if err != nil {
			r.Logger.Errorf("list objects failed, err: %v", err)
			return
		}

		for _, obj := range res.Contents {
			if r.isExpired(obj.LastModified, r.TrashExpiration) {
				r.Logger.Info("going to remove the object in trash", zap.String("objectKey", obj.Key))
				r.Junks = append(r.Junks, obj.Key)
			}
		}

		if res.IsTruncated == false {
			break
		}

		time.Sleep(100 * time.Millisecond)
	}

	r.deleteJunks()
	r.Logger.Info("sweep trash done.")
	return
}

// 获取 bucket 文件夹列表
func (r *Sweeper) getFolderListFromBos() (fs []code.CommonPrefix, err error) {
	args := &code.ListObjectsArgs{
		Delimiter: "/",
		MaxKeys:   1000,
	}

	var res *code.ListObjectsResult

	for {
		if res != nil {
			args.Marker = res.NextMarker
		}

		res, err = global.AC.Clients.Code.ListObjects(args)
		if err != nil {
			return nil, err
		}
		fs = append(fs, res.CommonPrefixes...)
		if res.IsTruncated == false {
			break
		}
	}
	return
}

func (r *Sweeper) isExpired(lastModifyTime string, expiration int64) bool {
	t, err := time.Parse(time.RFC3339, lastModifyTime)
	if err != nil {
		r.Logger.Errorf("time parse object LastModifyTime failed, err: %v", err)
		return true
	}

	return time.Now().Unix()-t.Unix() > expiration
}

// 批量删除，每次最多删 1000 个
func (r *Sweeper) deleteJunks() {
	length := len(r.Junks)
	batch := int(math.Ceil(float64(length) / 1000))

	for i := 0; i < batch; i++ {
		start := i * 1000
		end := (i + 1) * 1000
		if end > length {
			end = length
		}

		currBatch := r.Junks[start:end]

		if err := global.AC.Clients.Code.DeleteMultiObjects(currBatch); err != nil {
			r.Logger.Errorf("delete objects failed, err: %v", err)
		}

		time.Sleep(200 * time.Millisecond)
	}

	r.Junks = make([]string, 0)
	return
}

func isUID(prefix string) bool {
	return api.RegularSourceAccount.MatchString(prefix)
}
