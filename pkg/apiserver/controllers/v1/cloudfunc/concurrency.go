package cloudfunc

import (
	"fmt"
	"net/http"
	"context"

	restful "github.com/emicklei/go-restful"
	"icode.baidu.com/baidu/faas/kun/cmd/apiserver/filter"
	"icode.baidu.com/baidu/faas/kun/pkg/api"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/dao"
	apiErr "icode.baidu.com/baidu/faas/kun/pkg/apiserver/error"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/global"
	kunErr "icode.baidu.com/baidu/faas/kun/pkg/error"
	"icode.baidu.com/baidu/faas/kun/pkg/server"
	"icode.baidu.com/baidu/faas/kun/pkg/server/endpoint"
	"icode.baidu.com/baidu/faas/kun/pkg/util/convert"
)

// ConcurrencyRest xxx
type ConcurrencyRest struct{}

type concurrencyReq struct {
	ReservedConcurrentExecutions *int `json:"ReservedConcurrentExecutions"`
}

// APIs xxx
func (r *ConcurrencyRest) APIs() []endpoint.ApiSingle {
	apis := []endpoint.ApiSingle{
		// put function concurrency setting
		// PUT /2017-10-31/functions/{FunctionName}/concurrency/
		endpoint.ApiSingle{
			Verb:    "PUT",
			Path:    "/functions/{FunctionName}/concurrency",
			Handler: server.WrapRestRouteFunc(r.putFunctionConcurrency),
			Filters: []restful.FilterFunction{
				filter.IAMPermissionCheck("*", "", []string{"cfc:PutFunctionConcurrency", filter.IamPermWrite}),
			},
		},
		// delete function concurrency setting
		// DELETE /2017-10-31/functions/{FunctionName}/concurrency/
		endpoint.ApiSingle{
			Verb:    "DELETE",
			Path:    "/functions/{FunctionName}/concurrency",
			Handler: server.WrapRestRouteFunc(r.deleteFunctionConcurrency),
			Filters: []restful.FilterFunction{
				filter.IAMPermissionCheck("*", "", []string{"cfc:DeleteFunctionConcurrency", filter.IamPermWrite}),
			},
		},
	}
	return apis
}

func (r *ConcurrencyRest) putFunctionConcurrency(c *server.Context) {
	resp := c.Response()

	ctx := global.BuildApiserverContext(c.Request(), c.Response(), "putFunctionConcurrency")
	reqFunc, err := precheck(c, ctx)
	if err != nil {
		c.WithWarnLog(err).WriteTo(resp)
		return
	}

	ctx.Context.FunctionBrn = reqFunc.FunctionBrn
	currReq := &concurrencyReq{}
	if err := ctx.Observer.NewStage(global.ValidateConcurrencyStage).Observe(validateConcurrency(c, currReq, reqFunc)); err != nil {
		c.WithWarnLog(err).WriteTo(resp)
		return
	}

	// 更新所有版本
	reqFunc.Version = ""
	uf := &dao.Function{}
	uf.ReservedConcurrentExecutions = currReq.ReservedConcurrentExecutions

	if err := ctx.Observer.NewStage(global.QueryDatabaseStage).Observe(dao.UpdateConcurrency(*reqFunc, uf)); err != nil {
		c.WithWarnLog(err).WriteTo(resp)
		return
	}

	defer global.WriteSummary(ctx, api.LogNotToSummary)
	resp.WriteHeaderAndEntity(http.StatusOK, currReq)
}

func (r *ConcurrencyRest) deleteFunctionConcurrency(c *server.Context) {
	resp := c.Response()
	ctx := global.BuildApiserverContext(c.Request(), c.Response(), "deleteFunctionConcurrency")
	reqFunc, err := precheck(c, ctx)
	if err != nil {
		c.WithWarnLog(err).WriteTo(resp)
		return
	}

	ctx.Context.FunctionBrn = reqFunc.FunctionBrn
	reqFunc.Version = "$LATEST"
	if err = ctx.Observer.NewStage(global.CheckFunctionStage).Observe(dao.FindOneFunc(reqFunc)); err != nil {
		c.WithWarnLog(err).WriteTo(resp)
		return
	}

	findCond := dao.Function{Uid: reqFunc.Uid}
	findCond.FunctionName = reqFunc.FunctionName
	uf := &dao.Function{}
	uf.ReservedConcurrentExecutions = convert.Int(0)

	if err := ctx.Observer.NewStage(global.QueryDatabaseStage).Observe(dao.UpdateConcurrency(findCond, uf)); err != nil {
		c.WithWarnLog(err).WriteTo(resp)
		return
	}

	defer global.WriteSummary(ctx, api.LogNotToSummary)
	resp.WriteHeader(http.StatusNoContent)
}

func validateConcurrency(c *server.Context, currReq *concurrencyReq, reqFunc *dao.Function) (err error) {
	// 1. check用户输入
	if err = c.Request().ReadEntity(currReq); err != nil {
		return kunErr.NewInvalidRequestContentException("", err)
	}

	if currReq.ReservedConcurrentExecutions == nil || *currReq.ReservedConcurrentExecutions < api.DefaultFuncReservedConcurrencyMinimum {
		return apiErr.NewInitFuncMetaException("invalid reservedConcurrentExecutions", "", nil)
	}

	// 2. 累加已预留的额度
	reservedConcurrencySum, err := getReservedSum(reqFunc)
	if err != nil {
		return err
	}

	// 3. 获取账户额度
	accountConcurrency, err := global.AC.Clients.Ops.GetQuota(context.TODO(), reqFunc.Uid, "user_concurrency")
	if err != nil {
		return kunErr.NewServiceException("get quota from opscenter failed", err)
	}

	// 4. 计算是否超额
	if *currReq.ReservedConcurrentExecutions+reservedConcurrencySum+api.DefaultAccountUnreservedConcurrencyMinimum > accountConcurrency {
		msg := fmt.Sprintf("Specified ReservedConcurrentExecutions for function decreases account's UnreservedConcurrentExecution below its minimum value of [%d].",
			api.DefaultAccountUnreservedConcurrencyMinimum)
		return apiErr.NewInitFuncMetaException(msg, "", nil)
	}
	return nil
}

// 累加用户所有函数已预留的额度，只取 $LATEST 版本。
func getReservedSum(reqFunc *dao.Function) (sum int, err error) {
	findCond := &dao.Function{Uid: reqFunc.Uid}
	findCond.Version = "$LATEST"
	found := false

	funcs, err := dao.FindFunc(findCond)
	if err != nil {
		return
	}

	for _, f := range *funcs {
		if f.FunctionName == reqFunc.FunctionName {
			found = true
			continue
		}
		sum += *f.ReservedConcurrentExecutions
	}

	if found == false {
		err = apiErr.NewResourceNotFoundException("function not found", err)
	}
	return
}

func precheck(c *server.Context, ctx *global.ApiserverContext) (*dao.Function, error) {
	var (
		funcTmp  interface{}
		function *dao.Function
		err      error
	)
	if funcTmp, err = ctx.Observer.NewStage(global.InitFunctionStage).ObserveObject(InitFunction(c.Request())); err != nil {
		return function, apiErr.NewInitFuncMetaException(err.Error(), "", err)
	}

	function = funcTmp.(*dao.Function)
	if function.Version != "" {
		return function, apiErr.NewInitFuncMetaException("Aliases and versions do not support this operation", "", nil)
	}

	return function, nil
}
