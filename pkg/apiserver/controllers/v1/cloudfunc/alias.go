package cloudfunc

import (
	"net/http"
	"strconv"
	"time"

	"github.com/asaskevich/govalidator"
	"github.com/emicklei/go-restful"

	"icode.baidu.com/baidu/faas/kun/cmd/apiserver/filter"
	"icode.baidu.com/baidu/faas/kun/pkg/api"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/dao"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/dbengine"
	apiErr "icode.baidu.com/baidu/faas/kun/pkg/apiserver/error"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/global"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/models"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/models/function"
	apiServer "icode.baidu.com/baidu/faas/kun/pkg/apiserver/server"
	"icode.baidu.com/baidu/faas/kun/pkg/bce/brn"
	kunErr "icode.baidu.com/baidu/faas/kun/pkg/error"
	"icode.baidu.com/baidu/faas/kun/pkg/server"
	"icode.baidu.com/baidu/faas/kun/pkg/server/endpoint"
	"icode.baidu.com/baidu/faas/kun/pkg/util/convert"
)

type AliasResource struct {
}

// APIs xxx
func (p AliasResource) APIs() []endpoint.ApiSingle {
	apis := []endpoint.ApiSingle{
		// create new alias, POST /2015-03-31/functions/{FunctionName}/aliases
		endpoint.ApiSingle{
			Verb:    "POST",
			Path:    "/functions/{FunctionName}/aliases",
			Handler: server.WrapRestRouteFunc(p.create),
			Filters: []restful.FilterFunction{
				filter.IAMPermissionCheck(api.Resource, "", []string{"cfc:CreateAlias", filter.IamPermWrite}),
			},
		},
		// get alias by name, GET /2015-03-31/functions/{FunctionName}/aliases/{aliasName}
		endpoint.ApiSingle{
			Verb:    "GET",
			Path:    "/functions/{FunctionName}/aliases/{aliaName}",
			Handler: server.WrapRestRouteFuncWithTrace("ApiServerHandler/getFunctionAliases",p.find),
			Filters: []restful.FilterFunction{
				filter.IAMPermissionCheck(api.Resource, "", []string{"cfc:GetAlias", filter.IamPermRead}),
			},
		},
		// update alias by name, PUT /2015-03-31/functions/{FunctionName}/aliases/{aliasName}
		endpoint.ApiSingle{
			Verb:    "PUT",
			Path:    "/functions/{FunctionName}/aliases/{aliaName}",
			Handler: server.WrapRestRouteFunc(p.update),
			Filters: []restful.FilterFunction{
				filter.IAMPermissionCheck(api.Resource, "", []string{"cfc:UpdateAlias", filter.IamPermWrite}),
			},
		},
		// list alias, GET /2015-03-31/functions/{FunctionName}/aliases
		endpoint.ApiSingle{
			Verb:    "GET",
			Path:    "/functions/{FunctionName}/aliases",
			Handler: server.WrapRestRouteFunc(p.findByCondition),
			Filters: []restful.FilterFunction{
				filter.IAMPermissionCheck(api.Resource, "", []string{"cfc:ListAliases", filter.IamPermList}),
				server.WrapRestFilterFunction(filter.ValidatorQueryParameter),
			},
		},
		// delete alias by name, DELETE /2015-03-31/functions/{FunctionName}/aliases/{aliasName}
		endpoint.ApiSingle{
			Verb:    "DELETE",
			Path:    "/functions/{FunctionName}/aliases/{aliaName}",
			Handler: apiServer.WrapRestRouteFunc(p.delete),
			Filters: []restful.FilterFunction{
				filter.IAMPermissionCheck(api.Resource, "", []string{"cfc:DeleteAlias", filter.IamPermWrite}),
			},
		},
	}
	return apis
}

// 由于map无法直接存放于mysql中，同时考虑到与AWS保持一致
// 所以不再直接使用dao.Alias作为request body
type aliasRequest struct {
	Id                       uint
	AliasBrn                 string
	AliasArn                 string
	FunctionName             string
	FunctionVersion          string
	Name                     string
	Description              *string
	Uid                      string
	UpdatedAt                time.Time
	CreatedAt                time.Time
	AdditionalVersionWeights map[string]float64 // 用于接受额外版本信息，与AWS保持一致
}

func (p *AliasResource) create(c *server.Context) {
	response := c.Response()

	ctx := global.BuildApiserverContext(c.Request(), c.Response(), "aliases_create")
	aliasReq := aliasRequest{}
	if err := c.Request().ReadEntity(&aliasReq); err != nil {
		c.WithWarnLog(kunErr.NewInvalidRequestContentException("", err)).WriteTo(response)
		return
	}
	if aliasReq.FunctionVersion == "" || aliasReq.Name == "" {
		c.WithWarnLog(apiErr.NewMissingParametersException("version or name is empty", nil)).WriteTo(c.Response())
		return
	}

	var (
		additionalVersion       string
		additionalVersionWeight float64

		funcTmp interface{}
		err     error
	)

	additionalVersion, additionalVersionWeight, err = validateRequest(aliasReq)
	if err != nil {
		c.WithWarnLog(err).WriteTo(response)
		return
	}

	if funcTmp, err = ctx.Observer.NewStage(global.InitFunctionStage).ObserveObject(InitFunction(c.Request())); err != nil {
		c.WithWarnLog(apiErr.NewInitFuncMetaException(err.Error(), "", nil)).WriteTo(response)
		return
	}

	f := &dao.Function{}
	f = funcTmp.(*dao.Function)
	if f.Version != "" && f.Version != aliasReq.FunctionVersion {
		c.WithWarnLog(apiErr.NewInitFuncMetaException("your's brn function version != reqeset FunctionVersion", "f.Version != aliasReq.FunctionVersion", nil)).WriteTo(response)
		return
	}
	f.Version = aliasReq.FunctionVersion

	ctx.Context.FunctionBrn = f.FunctionBrn
	//校验是否存在对应版本的function
	if rKunErr := ctx.Observer.NewStage(global.CheckFunctionStage).Observe(dao.FindOneFunc(f)); rKunErr != nil {
		c.WithWarnLog(apiErr.NewResourceNotFoundException("function not exists : "+f.FunctionName, nil)).WriteTo(response)
		return
	}

	// 判断是否存在第二版本指向，如果存在额外指向的版本，该版本的函数也必须存在
	// 根据uid、FunctionName以及Version可以唯一确定函数信息
	if additionalVersion != "" {
		f1 := &dao.Function{}
		f1.Version = additionalVersion
		f1.FunctionName = f.FunctionName
		f1.Uid = f.Uid
		if rKunErr := dao.FindOneFunc(f1); rKunErr != nil {
			c.WithWarnLog(apiErr.NewResourceNotFoundException("function not exists : "+f.FunctionName, nil)).WriteTo(response)
			return
		}

	}

	newAlias := setAlias(aliasReq)
	newAlias.FunctionName = f.FunctionName
	newAlias.Uid = f.Uid
	newAlias.AliasBrn = brn.GenerateFuncBrnString(f.Region, f.Uid, f.FunctionName, aliasReq.Name)

	if _, err := govalidator.ValidateStruct(newAlias); err != nil {
		c.WithWarnLog(kunErr.NewInvalidParameterValueException("", err)).WriteTo(response)
		return
	} else if err := models.CheckPtrString(newAlias.Description, 0, 125); err != nil {
		c.WithWarnLog(apiErr.NewInitFuncMetaException(err.Error(), "Description length is Illegal", nil)).WriteTo(response)
		return
	} else if err := models.CheckAliasName(newAlias.Name); err != nil {
		c.WithWarnLog(apiErr.NewInitFuncMetaException(err.Error(), "", err)).WriteTo(response)
		return
	}

	if aliasReq.Description == nil {
		newAlias.Description = convert.String("")
	} else {
		newAlias.Description = aliasReq.Description
	}

	if aliasReq.AdditionalVersionWeights == nil {
		newAlias.AdditionalVersion = convert.String("")
		newAlias.AdditionalVersionWeight = convert.Float64(0)
	} else {
		newAlias.AdditionalVersion = &additionalVersion
		newAlias.AdditionalVersionWeight = &additionalVersionWeight
	}

	if rKunErr := ctx.Observer.NewStage(global.CreateAliasStage).Observe(dao.CreateAlias(newAlias)); rKunErr != nil {
		c.WithErrorLog(rKunErr).WriteTo(response)
		return
	}

	newAlias.DealAlias()

	aliasReq.AliasBrn = newAlias.AliasBrn
	aliasReq.AliasArn = newAlias.AliasArn

	defer global.WriteSummary(ctx, api.LogNotToSummary)

	// 考虑到与AWS保持一致返回如下结构，这里依然返回aliasReq，而不是返回dao.Alias
	// "AdditionalVersionWeights": {
	// 		"AdditionalVersion":	"AdditionalVersionWeights"
	//	}
	response.WriteHeaderAndEntity(http.StatusCreated, &aliasRequest{
		Id:                       newAlias.Id,
		Name:                     newAlias.Name,
		FunctionName:             newAlias.FunctionName,
		FunctionVersion:          newAlias.FunctionVersion,
		CreatedAt:                newAlias.CreatedAt,
		UpdatedAt:                newAlias.UpdatedAt,
		Description:              newAlias.Description,
		AliasArn:                 newAlias.AliasArn,
		AliasBrn:                 newAlias.AliasBrn,
		Uid:                      newAlias.Uid,
		AdditionalVersionWeights: aliasReq.AdditionalVersionWeights,
	})

}
func (p *AliasResource) update(c *server.Context) {
	response := c.Response()
	ctx := global.BuildApiserverContext(c.Request(), c.Response(), "aliases_update")
	aName := c.Request().PathParameter("aliaName")

	aliasReq := aliasRequest{}
	if err := c.Request().ReadEntity(&aliasReq); err != nil {
		c.WithWarnLog(kunErr.NewInvalidRequestContentException("", err)).WriteTo(response)
		return
	}
	if _, err := govalidator.ValidateStruct(aliasReq); err != nil {
		c.WithWarnLog(apiErr.NewInitFuncMetaException(err.Error(), "", nil)).WriteTo(response)
		return
	} else if err := models.CheckPtrString(aliasReq.Description, 0, 125); err != nil {
		c.WithWarnLog(apiErr.NewInitFuncMetaException(err.Error(), "Description length is Illegal", nil)).WriteTo(response)
		return
	} else if aliasReq.FunctionVersion == "" {
		c.WithWarnLog(apiErr.NewMissingParametersException("version is empty", nil)).WriteTo(response)
		return
	}

	var (
		additionalVersion       string
		additionalVersionWeight float64
		funcTmp, newAliasTmp    interface{}
		err                     error
	)

	additionalVersion, additionalVersionWeight, err = validateRequest(aliasReq)
	if err != nil {
		c.WithWarnLog(err).WriteTo(response)
		return
	}

	a := new(dao.Alias)
	updateAlias := new(dao.Alias)
	f := &dao.Function{}
	if funcTmp, err = ctx.Observer.NewStage(global.InitFunctionStage).ObserveObject(InitFunction(c.Request())); err != nil {
		c.WithWarnLog(apiErr.NewInitFuncMetaException(err.Error(), "", nil)).WriteTo(response)
		return
	}

	f = funcTmp.(*dao.Function)
	ctx.Context.FunctionBrn = f.FunctionBrn
	a.AliasBrn = brn.GenerateFuncBrnString(f.Region, f.Uid, f.FunctionName, aName)

	//判断别名是否存在
	if rKerr := ctx.Observer.NewStage(global.CheckAliasStage).Observe(IsAliasExist(a.AliasBrn)); rKerr != nil {
		c.WithWarnLog(apiErr.NewResourceNotFoundException(rKerr.Error(), nil)).WriteTo(response)
		return
	}
	f.Version = aliasReq.FunctionVersion
	//校验是否存在对应版本的function
	if rKunErr := ctx.Observer.NewStage(global.CheckFunctionStage).Observe(dao.FindOneFunc(f)); rKunErr != nil {
		c.WithWarnLog(apiErr.NewResourceNotFoundException("function not exists : "+f.FunctionName, nil)).WriteTo(response)
		return
	}

	if additionalVersion != "" {
		f1 := &dao.Function{}
		f1.Uid = f.Uid
		f1.FunctionName = f.FunctionName
		f1.Version = additionalVersion
		if rKunErr := dao.FindOneFunc(f1); rKunErr != nil {
			c.WithWarnLog(apiErr.NewResourceNotFoundException("function not exists : "+f.FunctionName, nil)).WriteTo(response)
			return
		}
	}

	updateAlias.Description = aliasReq.Description
	updateAlias.FunctionVersion = aliasReq.FunctionVersion
	updateAlias.AdditionalVersion = &additionalVersion
	updateAlias.AdditionalVersionWeight = &additionalVersionWeight

	// update过程中可能取消额外版本指向（即请求中不含额外版本信息，则置为空，与aws保持一致）
	if additionalVersion == "" {
		updateAlias.AdditionalVersion = convert.String("")
		updateAlias.AdditionalVersionWeight = convert.Float64(0)
	}
	newAlias := &dao.Alias{}

	if newAliasTmp, err = ctx.Observer.NewStage(global.UpdateAliasStage).ObserveObject(dao.UpdateAlias(*a, updateAlias)); err != nil {
		c.WithErrorLog(err).WriteTo(response)
		return
	}

	newAlias = newAliasTmp.(*dao.Alias)
	newAlias.DealAlias()

	defer global.WriteSummary(ctx, api.LogNotToSummary)

	// 同理，与AWS返回结构保持一致，返回值不能直接返回数据库内容
	response.WriteHeaderAndEntity(http.StatusOK, &aliasRequest{
		Id:                       newAlias.Id,
		Name:                     newAlias.Name,
		FunctionName:             newAlias.FunctionName,
		FunctionVersion:          newAlias.FunctionVersion,
		CreatedAt:                newAlias.CreatedAt,
		UpdatedAt:                newAlias.UpdatedAt,
		Description:              newAlias.Description,
		AliasArn:                 newAlias.AliasArn,
		AliasBrn:                 newAlias.AliasBrn,
		Uid:                      newAlias.Uid,
		AdditionalVersionWeights: aliasReq.AdditionalVersionWeights,
	})
}
func (p *AliasResource) delete(c *apiServer.ApiServerContext) {
	response := c.Response()
	aName := c.Request().PathParameter("aliaName")

	ctx := global.BuildApiserverContext(c.Request(), c.Response(), "aliases_delete")
	a := new(dao.Alias)
	f := &dao.Function{}
	var (
		funcTmp interface{}
		err     error
	)
	if funcTmp, err = ctx.Observer.NewStage(global.InitFunctionStage).ObserveObject(InitFunction(c.Request())); err != nil {
		c.WithWarnLog(apiErr.NewInitFuncMetaException(err.Error(), "", nil)).WriteTo(response)
		return
	}
	f = funcTmp.(*dao.Function)
	ctx.Context.FunctionBrn = f.FunctionBrn
	a.AliasBrn = brn.GenerateFuncBrnString(f.Region, f.Uid, f.FunctionName, aName)

	if rKunErr := ctx.Observer.NewStage(global.FindOneAliasStage).Observe(dao.FindOneAlias(a)); rKunErr != nil {
		c.WithWarnLog(rKunErr).WriteTo(response)
		return
	}

	c.DbTransaction = dbengine.DBTransaction()
	if err := ctx.Observer.NewStage(global.DeleteRelationsStage).Observe(DeleteRelations(c, []string{a.AliasBrn})); err != nil {
		c.DbTransaction.Rollback()
		c.WithErrorLog(err).WriteTo(response)
		return
	}

	if rKunErr := ctx.Observer.NewStage(global.DeleteAliasStage).Observe(dao.DeleteAlias(a, c.DbTransaction)); rKunErr != nil {
		c.DbTransaction.Rollback()
		c.WithErrorLog(rKunErr).WriteTo(response)
		return
	}
	if err := c.DbTransaction.Commit().Error; err != nil {
		c.WithErrorLog(kunErr.NewServiceException("delete alias failed because of commit failure", err)).WriteTo(c.Response())
		return
	}

	defer global.WriteSummary(ctx, api.LogNotToSummary)
	response.WriteHeaderAndEntity(http.StatusNoContent, nil)
}

func (p AliasResource) find(c *server.Context) {
	response := c.Response()
	aName := c.Request().PathParameter("aliaName")

	ctx := global.BuildApiserverContext(c.Request(), c.Response(), "aliases_find")
	a := new(dao.Alias)

	f := &dao.Function{}
	var (
		funcTmp interface{}
		err     error
	)
	if funcTmp, err = ctx.Observer.NewStage(global.InitFunctionStage).ObserveObject(InitFunction(c.Request())); err != nil {
		c.WithWarnLog(apiErr.NewInitFuncMetaException(err.Error(), "", nil)).WriteTo(response)
		return
	}

	f = funcTmp.(*dao.Function)
	ctx.Context.FunctionBrn = f.FunctionBrn
	a.AliasBrn = brn.GenerateFuncBrnString(f.Region, f.Uid, f.FunctionName, aName)

	if rKunErr := ctx.Observer.NewStage(global.FindOneAliasStage).Observe(dao.FindOneAlias(a)); rKunErr != nil {
		c.WithWarnLog(rKunErr).WriteTo(response)
		return
	}
	a.DealAlias()

	defer global.WriteSummary(ctx, api.LogNotToSummary)

	// 由于数据库中该字段默认不为NULL，为了防止返回["":0]这种情况出现，这里增加一次判断
	canary := map[string]float64{}
	if a.AdditionalVersion != nil && *a.AdditionalVersion != "" {
		canary[*a.AdditionalVersion] = *a.AdditionalVersionWeight
	}

	response.WriteHeaderAndEntity(http.StatusOK, &aliasRequest{
		Id:                       a.Id,
		Uid:                      a.Uid,
		Name:                     a.Name,
		AliasArn:                 a.AliasArn,
		AliasBrn:                 a.AliasBrn,
		CreatedAt:                a.CreatedAt,
		UpdatedAt:                a.UpdatedAt,
		FunctionName:             a.FunctionName,
		FunctionVersion:          a.FunctionVersion,
		Description:              a.Description,
		AdditionalVersionWeights: canary,
	})
}

// 用于内部灰度发布时的别名获取
func (p AliasResource) insideFind(c *server.Context) {
	defer c.Logger().TimeTrack(time.Now(), "insideFind")
	response := c.Response()
	functionBrn := c.Request().PathParameter("FunctionBrn")
	ctx := global.BuildApiserverContext(c.Request(), c.Response(), "insideFind")
	// 可以直接根据BRN的唯一性找寻相关别名
	a := new(dao.Alias)
	a.AliasBrn = functionBrn
	res, err := dao.FindAlias(a)
	if err != nil {
		c.WithWarnLog(err).WriteTo(response)
		return
	}
	alias := dao.Alias{}

	defer global.WriteSummary(ctx, api.LogNotToSummary)
	if len(*res) == 0 {
		c.WithWarnLog(apiErr.NewResourceNotFoundException("Alias not found", nil)).WriteTo(response)
		return
	}
	alias = (*res)[0]
	response.WriteHeaderAndEntity(http.StatusOK, alias)
}

func (p AliasResource) findByCondition(c *server.Context) {
	response := c.Response()

	ctx := global.BuildApiserverContext(c.Request(), c.Response(), "aliases_findByCondition")
	version := c.Request().QueryParameter("FunctionVersion")
	markerStr := c.Request().QueryParameter("Marker")
	maxItemStr := c.Request().QueryParameter("MaxItems")

	a := new(dao.Alias)
	f := &dao.Function{}
	var (
		funcTmp, aliasSliceTmp, countTmp interface{}
		marker, maxItem, count           int64
		resMap                           = make(map[string]interface{})
		err                              error
	)
	if funcTmp, err = ctx.Observer.NewStage(global.InitFunctionStage).ObserveObject(InitFunction(c.Request())); err != nil {
		c.WithWarnLog(apiErr.NewInitFuncMetaException(err.Error(), "", nil)).WriteTo(response)
		return
	}

	f = funcTmp.(*dao.Function)

	ctx.Context.FunctionBrn = f.FunctionBrn
	a.FunctionName = f.FunctionName
	a.Uid = f.Uid

	//Query's version 与 f.Version 的值存在2X2的排列组合可能性
	if version != "" && f.Version != "" && version != f.Version {
		c.WithWarnLog(kunErr.NewInvalidParameterValueException("the derived version from the function brn does not match the query parameter FunctionVersion", nil)).WriteTo(response)
		return
	} else if version == "" && f.Version != "" {
		version = f.Version
	}
	a.FunctionVersion = version

	//查看函数是否存在
	if rKunErr := ctx.Observer.NewStage(global.CheckFunctionStage).Observe(IsFunctionExist(f.Uid, f.FunctionName)); rKunErr != nil {
		c.WithWarnLog(apiErr.NewResourceNotFoundException("function not found", nil)).WriteTo(response)
		return
	}

	_, _, marker, maxItem, err = models.ParseParams("", "", markerStr, maxItemStr)
	if err != nil {
		c.WithWarnLog(kunErr.NewInvalidParameterValueException("[parse Marker|MaxItems fail]", err)).WriteTo(response)
		return
	}

	aliasesCond := &function.AliasesCond{
		Marker:   marker,
		MaxItems: maxItem,
	}

	if aliasSliceTmp, countTmp, err = ctx.Observer.NewStage(global.ListAliasesStage).ObserveObjects(function.ListAliasesByCondition(a, aliasesCond)); err != nil {
		c.WithWarnLog(kunErr.NewServiceException("[Query database failed]", err))
		return
	}

	aliasSlice := aliasSliceTmp.([]dao.Alias)
	count = countTmp.(int64)
	if maxItemStr != "" {
		if count > (maxItem + marker) {
			resMap["NextMarker"] = strconv.FormatInt((maxItem + marker), 10)
		}
	}

	ml := make([]interface{}, 0)
	for _, aliasRes := range aliasSlice {
		aliasRes.DealAlias()
		ml = append(ml, aliasRes)
	}
	resMap["Aliases"] = ml

	defer global.WriteSummary(ctx, api.LogNotToSummary)
	response.WriteHeaderAndEntity(http.StatusOK, resMap)
}

// 本函数用于检测请求的合法性
func validateRequest(request aliasRequest) (string, float64, error) {
	var (
		version string
		weight  float64
	)
	// 请求中不含额外版本信息的情况
	if request.AdditionalVersionWeights == nil {
		return version, weight, nil
	}
	// 有时候additionalVersionWeights字段不为nil，但是为空。如下所示：
	// {
	// 		"Version": "123",
	// 		"AdditionalVersionWeights": {}
	// }
	// 此时返回400
	if request.AdditionalVersionWeights != nil && len(request.AdditionalVersionWeights) == 0 {
		return version, weight, apiErr.NewMissingParametersException("The Additional Version Parameters is missing", nil)
	}

	// 请求中如果额外版本字段不为空，则其「有且仅有」一个额外版本指向
	// 不允许出现如下所示情况
	// {
	// 		"AdditionalVersionWeights": {
	// 			"1": 0.12,
	// 			"2": 0.24,
	// }
	if request.AdditionalVersionWeights != nil && len(request.AdditionalVersionWeights) != 1 {
		return version, weight, apiErr.NewTooManyParametersException("There are more than one canary version", nil)
	}

	//
	for version, weight = range request.AdditionalVersionWeights {
		// 权值范围必须位于[0,1]范围内

		if weight > 1 || weight < 0 {
			return version, weight, apiErr.NewInvalidRequestException("Invalid weight found", "The weight is greater than 1 or less than 0", nil)
		}

		// 若进入此循环，则表示必定存在额外版本指向，故不管是stable版本还是canary版本均不可指向$LATEST
		if request.FunctionVersion == "$LATEST" || version == "$LATEST" {
			return version, weight, apiErr.NewInvalidRequestException("Invalid Version found", "One or more Version  pointer to $LATEST", nil)
		}
		return version, weight, nil
	}

	return version, weight, nil
}

func setAlias(request aliasRequest) *dao.Alias {
	return &dao.Alias{
		Id:              request.Id,
		Name:            request.Name,
		CreatedAt:       request.CreatedAt,
		UpdatedAt:       request.UpdatedAt,
		FunctionVersion: request.FunctionVersion,
		Description:     request.Description,
	}
}
