package cloudfunc

import (
	"crypto/md5"
	"crypto/sha256"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strings"

	"github.com/google/uuid"

	"icode.baidu.com/baidu/faas/kun/pkg/api"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/dao"
	apiErr "icode.baidu.com/baidu/faas/kun/pkg/apiserver/error"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/global"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/models"
	"icode.baidu.com/baidu/faas/kun/pkg/bce/bos"
	"icode.baidu.com/baidu/faas/kun/pkg/bce/brn"
	"icode.baidu.com/baidu/faas/kun/pkg/bce/iam"
	"icode.baidu.com/baidu/faas/kun/pkg/bce/opscenter"
	"icode.baidu.com/baidu/faas/kun/pkg/console"
	kunErr "icode.baidu.com/baidu/faas/kun/pkg/error"
	"icode.baidu.com/baidu/faas/kun/pkg/server"
	"icode.baidu.com/baidu/faas/kun/pkg/util/bytefmt"
	"icode.baidu.com/baidu/faas/kun/pkg/util/convert"
	"icode.baidu.com/baidu/faas/kun/pkg/util/json"
	"icode.baidu.com/baidu/faas/kun/pkg/util/logs"
)

// BosLogLengthLimit 定义可返回bos日志的最大长度为10M，超过这个长度就拒绝返回
const (
	BosLogLengthLimit = 8 * bytefmt.Megabyte //2M
)

// NewFunctionRequest 新建函数
type NewFunctionRequest struct {
	CodeEntryType       int
	ZipFile             string
	RawCode             string
	EnviromentVariables []console.EnviromentVariables
	dao.Function
}

// 在编辑框更新函数代码用这个接口，上传zip包用v2接口
func (f FunctionRest) consoleUpdate(c *server.Context) {
	response := c.Response()
	//console update code && update configuration

	r := c.Request().Request
	defer r.Body.Close()
	decoder := json.NewDecoder(r.Body)
	newFunction := new(NewFunctionRequest)
	if err := decoder.Decode(newFunction); err != nil {
		c.WithWarnLog(kunErr.NewInvalidRequestContentException(err.Error(), nil)).WriteTo(response)
		return
	}

	ctx := global.BuildApiserverContext(c.Request(), c.Response(), "consoleUpdate")
	//update configuration
	var (
		updateFuncTmp, codeSha256, codeSize, codeId, squashFsSha256 interface{}
		err                                                         error
	)

	if updateFuncTmp, err = ctx.Observer.NewStage(global.InitFunctionStage).ObserveObject(InitFunction(c.Request())); err != nil {
		c.WithWarnLog(apiErr.NewInitFuncMetaException(err.Error(), "", nil)).WriteTo(response)
		return
	}

	updateFunc := updateFuncTmp.(*dao.Function)
	newFunction.FunctionName = updateFunc.FunctionName
	ctx.Context.FunctionBrn = updateFunc.FunctionBrn
	input := newFunction.Function
	input.Environment = &api.Environment{
		Variables: make(map[string]string),
	}

	// AMIS不支持flatten object，做一次转换
	for _, v := range newFunction.EnviromentVariables {
		input.Environment.Variables[v.Key] = v.Value
	}
	copyReqFuncToDaoFunc(&input, updateFunc)

	if (updateFunc.Version != "" && updateFunc.Version != "$LATEST") || (input.Version != "" && input.Version != "$LATEST") {
		c.WithWarnLog(kunErr.NewInvalidParameterValueException("only $LATEST version can be updated", nil)).WriteTo(response)
		return
	}
	updateFunc.Version = "$LATEST"
	updateFunc.FunctionBrnInit()

	// check函数是否存在
	findFunc := new(dao.Function)
	findFunc.FunctionBrn = updateFunc.FunctionBrn
	findFunc.Uid = updateFunc.Uid
	if rkunErr := ctx.Observer.NewStage(global.CheckFunctionStage).Observe(dao.FindOneFunc(findFunc)); rkunErr != nil {
		c.WithErrorLog(rkunErr).WriteTo(response)
		return
	}
	condFunc := new(dao.Function)
	condFunc.FunctionBrn = updateFunc.FunctionBrn
	updateFunc.CommitID = convert.String(uuid.New().String())
	newFunction.Function.CodeID = findFunc.CodeID

	funcCode, err := console.PrepareCode(newFunction.CodeEntryType, newFunction.RawCode, newFunction.ZipFile, newFunction.Function, true)
	if err != nil {
		c.WithWarnLog(kunErr.NewServiceException("Update code failed", err)).WriteTo(response)
		return
	}
	if rkunErr := validateFunc("update", updateFunc, funcCode.ZipFile); rkunErr != nil {
		c.WithWarnLog(rkunErr).WriteTo(response)
		return
	}

	updateFunc.UpdateFunctionPre()
	if updateFunc.LogType == "bls" {
		SetBlsLogStore(c, updateFunc)
	}

	if updateFunc.AsyncInvokeConfig != nil {
		if rkunErr := ctx.Observer.NewStage(global.CheckAsyncConfigStage).Observe(models.CheckAsyncInvokeConfig(updateFunc)); rkunErr != nil {
			c.WithErrorLog(rkunErr).WriteTo(response)
			return
		}
	}

	if updateFunc.CFSConfig != nil {
		if rkunErr := ctx.Observer.NewStage(global.ValidateCFSConfigStage).Observe(models.ValidateCFSConfig(updateFunc)); rkunErr != nil {
			c.WithErrorLog(rkunErr).WriteTo(response)
			return
		}
	}

	//update code
	if codeSha256, squashFsSha256, codeSize, codeId, err = ctx.Observer.NewStage(global.UploadCodeStage).ObserveMoreObjects(global.AC.Clients.Code.FaasUploadCode(funcCode.ZipFile, updateFunc.FunctionName, updateFunc.Uid)); err != nil {
		err = models.OptimizeErrorCode("Upload code failed", err)
		c.WithErrorLog(err).WriteTo(response)
		return
	}

	updateFunc.CodeSha256 = codeSha256.(string)
	updateFunc.CodeSize = codeSize.(int32)
	updateFunc.CodeID = codeId.(string)
	updateFunc.SquashFsSha256 = squashFsSha256.(*string)

	if rkunErr := ctx.Observer.NewStage(global.UpdateFunctionStage).Observe(dao.UpdateFunc(*condFunc, updateFunc)); rkunErr != nil {
		c.WithWarnLog(rkunErr).WriteTo(response)
		return
	}

	defer global.WriteSummary(ctx, api.LogNotToSummary)
	response.WriteHeaderAndEntity(200, map[string]interface{}{
		"Msg":    "",
		"Status": 0,
	})
}

type InvitationRequest struct {
	Product    string `json:"product"`
	Account    string `json:"account"`
	InviteAuth string `json:"invite_auth"`
}

func (f FunctionRest) consoleInvitation(c *server.Context) {
	response := c.Response()
	r := c.Request().Request
	invitation := new(InvitationRequest)
	err := json.NewDecoder(r.Body).Decode(invitation)
	if err != nil {
		c.WithWarnLog(kunErr.NewInvalidRequestContentException("decode request body failed", err)).WriteTo(response)
		return
	}
	if invitation.Product != "CFC" {
		c.WithWarnLog(apiErr.NewInitFuncMetaException("product mismatch", invitation.Product, nil)).WriteTo(response)
		return
	}

	// 1. 认证请求
	authReq := iam.AuthenticationRequest{}
	authReq.Authorization = invitation.InviteAuth
	authReq.RequestId = c.Request().HeaderParameter(api.HeaderXRequestID)
	params := url.Values{}
	params.Add("product", "CFC")
	params.Add("account", invitation.Account)
	authReq.Request.WithUri("/").WithMethod("GET").WithHost("bce.baidu.com").WithParams(params)
	_, err = global.AC.Clients.Iam.AuthenticateWithAKSK(&authReq)
	if err != nil {
		c.WithWarnLog(apiErr.NewInitFuncMetaException("iam authenticate failed.", "", err)).WriteTo(response)
		return
	}

	// 2. 开通白名单
	requestUser, ok := c.Request().Attribute("User").(*iam.User)
	if !ok {
		c.WithErrorLog(apiErr.NewInitFuncMetaException("invalid request userinfo.", "", nil)).WriteTo(response)
		return
	}

	// 2. 校验用户
	hash := sha256.New()
	hash.Write([]byte(requestUser.Domain.Name))
	account := fmt.Sprintf("%x", hash.Sum(nil))
	if account != invitation.Account {
		logs.V(6).Infof("user mismatch %s %s. invitation.Account: %s ", requestUser.Domain.Name, account, invitation.Account)
		e := apiErr.NewInitFuncMetaException("user mismatch", fmt.Sprintf("user mismatch Account: %s. invitation.Account: %s ", account, invitation.Account), nil)
		c.WithErrorLog(e).WriteTo(response)
		return
	}
	accountid := requestUser.Domain.ID
	cli, err := opscenter.NewOpsCenter(global.AC.Clients.Iam, global.AC.Config.WhiteListEndpoint)
	if err != nil {
		c.WithErrorLog(kunErr.NewServiceException("create white list setting client failed.", err)).WriteTo(response)
		return
	}
	err = cli.AddUserNavigationList("CFC", accountid)
	if err != nil {
		c.WithErrorLog(kunErr.NewServiceException("add cfc white list failed.", err)).WriteTo(response)
		return
	}
	response.WriteHeader(http.StatusOK)
}

func (f FunctionRest) consoleNewFunc(c *server.Context) {
	response := c.Response()
	r := c.Request().Request
	defer r.Body.Close()

	ctx := global.BuildApiserverContext(c.Request(), c.Response(), "consoleNewFunc")
	decoder := json.NewDecoder(r.Body)
	newFunction := new(NewFunctionRequest)
	if err := decoder.Decode(newFunction); err != nil {
		c.WithWarnLog(kunErr.NewInvalidRequestContentException(err.Error(), nil)).WriteTo(response)
		return
	}

	input := newFunction.Function
	input.Environment = &api.Environment{
		Variables: make(map[string]string),
	}

	// AMIS不支持flatten object，做一次转换
	for _, v := range newFunction.EnviromentVariables {
		input.Environment.Variables[v.Key] = v.Value
	}

	funcCode, err := console.PrepareCode(newFunction.CodeEntryType, newFunction.RawCode, newFunction.ZipFile, newFunction.Function, false)

	if err != nil {
		c.WithWarnLog(kunErr.NewServiceException("Update code failed", err)).WriteTo(response)
		return
	}

	NewFunc, rKerr := CreateNewFunction(c, input, funcCode.ZipFile, ctx)
	if rKerr != nil {
		c.WithErrorLog(rKerr).WriteTo(response)
		return
	}

	NewFunc.DealResFunction()

	defer global.WriteSummary(ctx, api.LogNotToSummary)
	response.WriteHeaderAndEntity(http.StatusCreated, map[string]interface{}{
		"Function": NewFunc,
	})

}

func (f FunctionRest) findFuncALL(c *server.Context) {
	response := c.Response()

	ctx := global.BuildApiserverContext(c.Request(), c.Response(), "findFuncAll")
	findFunc, rKerr := findOneFunctionNew(c.Request(), c.Logger(), ctx)
	if rKerr != nil {
		c.WithErrorLog(rKerr).WriteTo(response)
		return
	}
	//处理根据alias情况
	qualifier := c.Request().QueryParameter("Qualifier")
	if qualifier == "" {
		qualifier = "$LATEST"
	}

	// workspaceID不为空，则functionName需要重新加前缀
	if findFunc.WorkspaceID != "" {
		findFunc.FunctionName = findFunc.WorkspaceID + "_" + findFunc.FunctionName
	}

	findFunc.FunctionBrn = brn.GenerateFuncBrnString(findFunc.Region, findFunc.Uid, findFunc.FunctionName, qualifier)
	ctx.Context.FunctionBrn = findFunc.FunctionBrn
	var (
		urlTmp, rawCodeTmp, funcTmp, funcEventSourceTmp interface{}
		err                                             error
		url, rawCode                                    string
	)

	if urlTmp, err = ctx.Observer.NewStage(global.DownloadCodeStage).ObserveObject(global.AC.Clients.Code.FaasCodeDownloadUrl(findFunc.FunctionName, findFunc.Uid, findFunc.CodeSha256, findFunc.CodeID)); err != nil {
		c.Logger().Errorf("get download url failed, [func: %s][errmsg:%s]", findFunc, err.Error())
	}

	url = urlTmp.(string)
	if r := global.AC.Cache.RuntimeCache[findFunc.Runtime]; r.Editable {
		if rawCodeTmp, err = ctx.Observer.NewStage(global.GetRawCodeStage).ObserveObject(console.GetRawCode(url, findFunc.Handler, findFunc.Runtime)); err != nil {
			c.WithErrorLog(kunErr.NewServiceException("get func code raw failed", err)).WriteTo(response)
			return
		}
		rawCode = rawCodeTmp.(string)
	} else {
		rawCode = "此类型Runtime不支持在线编辑"
	}

	ev := console.FlattenEnvVar(findFunc.Environment.Variables)

	findVer := new(dao.Function)
	findVer.Uid = findFunc.Uid
	findVer.Region = findFunc.Region
	findVer.FunctionName = findFunc.FunctionName
	findVer.SourceTag = api.CfcSourceTag

	if funcTmp, err = ctx.Observer.NewStage(global.ListFunctionsStage).ObserveObject(dao.FindFunc(findVer)); err != nil {
		c.WithErrorLog(kunErr.NewServiceException("get func list failed", err)).WriteTo(response)
		return
	}

	o := funcTmp.(*[]dao.Function)
	v := make([]string, 0)
	for _, f := range *o {
		v = append(v, f.Version)
	}

	//eventsourceMAP
	if funcEventSourceTmp, err = ctx.Observer.NewStage(global.ListEventSourceStage).ObserveObject(dao.FindEventSource(&dao.FuncEventSource{FunctionBrn: findFunc.FunctionBrn})); err != nil {
		c.WithErrorLog(kunErr.NewServiceException("get event source failed", err)).WriteTo(response)
		return
	}

	// 去掉functionName的前缀
	findFunc.DealResFunction()
	funcEventSourceSlice := funcEventSourceTmp.(*[]dao.FuncEventSource)
	r := map[string]interface{}{
		"Code":                api.CodeStorage{Location: url},
		"Configuration":       findFunc,
		"RawCode":             rawCode,
		"EnviromentVariables": ev,
		"Version":             v,
		"EventSourceMap":      funcEventSourceSlice,
	}

	if *findFunc.ReservedConcurrentExecutions != 0 {
		r["Concurrency"] = map[string]int{
			"ReservedConcurrentExecutions": *findFunc.ReservedConcurrentExecutions,
		}
	}

	defer global.WriteSummary(ctx, api.LogNotToSummary)
	response.WriteHeaderAndEntity(http.StatusOK, r)
}

// consoleViewLog handle the log request. This function will download log from Bos.
func (f FunctionRest) consoleViewLog(c *server.Context) {
	response := c.Response()

	ctx := global.BuildApiserverContext(c.Request(), c.Response(), "consoleViewLog")
	findFunc, rKerr := findOneFunctionNew(c.Request(), c.Logger(), ctx)
	if rKerr != nil {
		err := c.WithWarnLog(apiErr.NewResourceNotFoundException("findOneFunction", rKerr))
		writeViewLogError(err, response, "function not found")
		return
	}

	//bosPath为日志流名称如：$LATEST_201811261140_e8f8e9b480b84b76c4c70a62d750c788
	bosPath := c.Request().QueryParameter("path")
	if len(bosPath) == 0 {
		err := c.WithErrorLog(kunErr.NewInvalidParameterValueException("invalid bosPath", nil))
		writeViewLogError(err, response, bosPath)
		return
	}

	//logBosDir为bos：//+bucket名称+前缀([文件夹]/函数名/LATEST_日期)，如: bos://cfc-auto-test/dueros-coffe/$LATEST_20181126
	logBosDir := findFunc.LogBosDir

	c.Logger().Debugf("[logBosDir: %s][bosPath:%s]", logBosDir, bosPath)

	invalidBosDir := kunErr.NewInvalidParameterValueException("invalid bos dir", nil)
	if findFunc.LogType != "bos" {
		writeViewLogError(c.WithErrorLog(invalidBosDir), response, bosPath)
		return
	}

	//Get the bucketName
	u, err := url.Parse(logBosDir)
	if err != nil || u.Scheme != "bos" || len(u.Host) < 1 {
		writeViewLogError(c.WithErrorLog(invalidBosDir), response, bosPath)
		return
	}
	bucketName := u.Host

	//Get the objectName.The first letter of objectName should not be "/"
	//objectName为 [文件夹】/FunctionName/日志流名称/如：jjj/test_bos/$LATEST_201811261440_49b883064e5d9545f83d5d4030aee93d
	objectName := findFunc.FunctionName + "/" + bosPath
	if len(u.Path) > 0 {
		temp := strings.Trim(u.Path, "/")
		if len(temp) > 0 {
			objectName = fmt.Sprintf("%s/%s", temp, objectName)
		}
	}

	c.Logger().Debugf("the log dir is: [%v/%v]", bucketName, objectName)

	bosClient, err := bos.NewClient(c)
	if err != nil {
		writeViewLogError(c.WithErrorLog(kunErr.NewServiceException("create bos client fail", err)), response, bosPath)
		return
	}

	//Get the metadata of the log
	meta, rKerr := bosClient.GetObjectMeta(bucketName, objectName)
	if rKerr != nil {
		err := apiErr.NewResourceNotFoundException("GetObjectMeta", rKerr)
		writeViewLogError(c.WithErrorLog(err), response, bucketName+"/"+objectName)
		return
	}
	//the contents of the log are too large. Please download manually
	if meta.ContentLength >= BosLogLengthLimit {
		err := apiErr.NewLogContentLengthExceededException("meta.ContentLength is larger than BosLogLengthLimit", nil)
		writeViewLogError(c.WithErrorLog(err), response, bucketName+"/"+objectName)
		return
	}

	//Get the rawdata of the log
	responseHeaders := map[string]string{"ContentType": "text/plain"}
	//rangeStart := 0
	//rangeEnd := 100
	res, rKerr := bosClient.GetObject(bucketName, objectName, responseHeaders)
	if rKerr != nil {
		err := apiErr.NewResourceNotFoundException("GetObject", rKerr)
		writeViewLogError(c.WithErrorLog(err), response, bucketName+"/"+objectName)
		return
	}

	defer res.Body.Close()

	defer global.WriteSummary(ctx, api.LogNotToSummary)
	wErr := writeBosLogToConsole(response, res.Body, bucketName+"/"+objectName)
	if wErr != nil {
		err := c.WithErrorLog(kunErr.NewServiceException("writeBosLogToConsole", wErr))
		writeViewLogError(err, response, bosPath)
		return
	}
}

// consoleViewLog handle the log request. This function will download log from Bos.
func (f FunctionRest) consoleValidateVpcPermission(c *server.Context) {
	attr := c.Request().Attribute("User")
	user, _ := attr.(*iam.User)
	accountid := user.Domain.ID

	permission := map[string]bool{
		"result": false,
	}

	for _, whiteUid := range global.AC.Config.VpcPermissionWhiteList {
		if whiteUid == accountid {
			permission["result"] = true
		}
	}

	c.Response().WriteHeader(http.StatusOK)
	c.Response().WriteEntity(permission)
}

// writeViewLogError 纯文本返回
func writeViewLogError(err kunErr.FinalError, w http.ResponseWriter, s string) kunErr.FinalError {
	w.Header().Set("Content-Type", "text/plain")
	//w.Header().Set("X-Amzn-ErrorType", string(err.Code))
	//w.Header().Set("X-Bce-ErrorType", string(err.Code))

	w.WriteHeader(err.Status)
	w.Write([]byte(err.BasicError.Error()))

	return err
}

// writeBosLogToConsole 纯文本返回
func writeBosLogToConsole(w http.ResponseWriter, stream io.ReadCloser, s string) error {
	w.Header().Set("Content-Type", "text/plain")

	w.WriteHeader(http.StatusOK)

	w.Write([]byte("====LOG_DATA_START " + s + "====\n"))
	_, err := io.Copy(w, stream)
	w.Write([]byte("\n"))
	if err != nil {
		return err
	}
	w.Write([]byte("====LOG_DATA_END " + s + "====\n"))

	return nil
}

type AssistAuthorizationRequest struct {
	User    string `json:"user"`
	OriUrl  string `json:"ori_url"`
	Auth    string `json:"auth"`
	Product string `json:"product"`
}

func (f FunctionRest) consoleAssistAuthorization(c *server.Context) {
	response := c.Response()
	r := c.Request().Request
	assistAuthorization := new(AssistAuthorizationRequest)
	err := json.NewDecoder(r.Body).Decode(assistAuthorization)
	if err != nil {
		c.WithWarnLog(kunErr.NewInvalidRequestContentException("decode request body failed", err)).WriteTo(response)
		return
	}

	c.Logger().Debugf("assistAuthorization %+v", assistAuthorization)

	// 1. 认证请求
	authReq := iam.AuthenticationRequest{}
	authReq.Authorization = assistAuthorization.Auth
	authReq.RequestId = c.Request().HeaderParameter(api.HeaderXRequestID)
	params := url.Values{}
	params.Add("product", assistAuthorization.Product)
	params.Add("user", assistAuthorization.User)
	params.Add("ori_url", assistAuthorization.OriUrl)
	authReq.Request.WithUri("/").WithMethod("GET").WithHost("bce.baidu.com").WithParams(params)

	productAccount, err := global.AC.Clients.Iam.AuthenticateWithAKSK(&authReq)
	if err != nil {
		c.WithWarnLog(apiErr.NewInitFuncMetaException("iam authenticate failed.", "", err)).WriteTo(response)
		return
	}
	c.Logger().Debugf("product account %s %s", productAccount.User.Domain.ID, productAccount.User.Domain.Name)

	wl, ok := global.AC.Config.SourceWhiteList.Get(productAccount.User.ID)
	if !ok {
		c.WithWarnLog(apiErr.NewInitFuncMetaException("whitelist mismatch.", "", err)).WriteTo(response)
		return
	}
	if wl.Source != assistAuthorization.Product {
		c.WithWarnLog(apiErr.NewInitFuncMetaException("no whitelist permission", "", err)).WriteTo(response)
		return
	}
	// 获取登陆console的用户
	requestUser, ok := c.Request().Attribute("User").(*iam.User)
	if !ok {
		c.WithErrorLog(apiErr.NewInitFuncMetaException("invalid request userinfo.", "", nil)).WriteTo(response)
		return
	}
	// 判断登陆用户和需要绑定的用户是同一个用户
	// 2. 校验用户
	hash := sha256.New()
	hash.Write([]byte(requestUser.Domain.Name))
	account := fmt.Sprintf("%x", hash.Sum(nil))
	if account != assistAuthorization.User {
		e := apiErr.NewInitFuncMetaException("user mismatch", fmt.Sprintf("user mismatch User: %s. assistAuthorization.User: %s ", account, assistAuthorization.User), nil)
		c.WithErrorLog(e).WriteTo(response)
		return
	}
	h := md5.New()
	// md5 -s cfcduedge 4533864059770e125e8f0f2a6452fda0
	io.WriteString(h, requestUser.Domain.ID+assistAuthorization.User+"4533864059770e125e8f0f2a6452fda0")
	auth := fmt.Sprintf("%x", h.Sum(nil))
	m := make(map[string]string)
	m["user"] = assistAuthorization.User
	m["iam_id"] = requestUser.Domain.ID
	m["auth"] = auth
	response.WriteHeaderAndEntity(http.StatusOK, m)
}

// setLogQL函数用于将相关请求参数转换为logQL语句
func setLogQL(functionName string, status string, filters string, qualifier string) string {
	var logQL string
	functionName = "\"" + functionName + "\""
	qualifier = "\"" + qualifier + "\""
	if status == "" {
		logQL = fmt.Sprintf("{functionName=%s, qualifier=%s}", functionName, qualifier)
	} else {
		status = "\"" + status + "\""
		logQL = fmt.Sprintf("{functionName=%s, qualifier=%s, status=%s}", functionName, qualifier, status)
	}

	f := strings.Split(filters, " ")
	for i := 0; i < len(f); i++ {
		if f[i] != "" {
			f[i] = "\"" + f[i] + "\""
			logQL += "|=" + f[i]
		}
	}

	return logQL
}
