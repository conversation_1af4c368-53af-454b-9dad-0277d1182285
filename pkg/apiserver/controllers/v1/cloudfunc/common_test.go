package cloudfunc

import (
	"fmt"
	"testing"
	"time"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/stretchr/testify/assert"

	"icode.baidu.com/baidu/faas/kun/pkg/api"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/dao"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/dbengine"
	apiErr "icode.baidu.com/baidu/faas/kun/pkg/apiserver/error"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/global"
	kunErr "icode.baidu.com/baidu/faas/kun/pkg/error"
	"icode.baidu.com/baidu/faas/kun/pkg/util/convert"
)

func TestIsFunctionExist(t *testing.T) {
	global.MockAC()
	var m sqlmock.Sqlmock
	m, dbengine.Engine = global.MockDB()
	m.ExpectQuery("SELECT").WillReturnRows(global.GetRowsFunctions(nil))
	m.ExpectQuery("SELECT").WillReturnRows(global.GetRowsFunctions(global.GetTestFunc(1)))

	cases := []struct {
		in_uid   string
		in_fname string
		out_err  error
	}{
		{
			in_uid:   "",
			in_fname: "",
			out_err:  apiErr.NewResourceNotFoundException(fmt.Sprintf("[function not found]"), nil),
		},
		{
			in_uid:   "uiduid",
			in_fname: "myfunc",
			out_err:  nil,
		},
	}

	for _, tc := range cases {
		err := IsFunctionExist(tc.in_uid, tc.in_fname)
		assert.Equal(t, tc.out_err, err)
	}
}

func TestIsFunctionBrnExist(t *testing.T) {
	global.MockAC()
	var m sqlmock.Sqlmock
	m, dbengine.Engine = global.MockDB()
	m.ExpectQuery("SELECT").WillReturnRows(global.GetRowsFunctions(nil))
	m.ExpectQuery("SELECT").WillReturnRows(global.GetRowsFunctions(global.GetTestFunc(1)))

	cases := []struct {
		in_brn  string
		out_err error
	}{
		{
			in_brn:  "",
			out_err: apiErr.NewResourceNotFoundException(fmt.Sprintf("[functionBrn not found]"), nil),
		},
		{
			in_brn:  "brn:bce:cfc:bj:640c8817bd1de2928d47256dd0620ce5:function:testjzx:$LATEST",
			out_err: nil,
		},
	}

	for _, tc := range cases {
		err := IsFunctionBrnExist(tc.in_brn)
		assert.Equal(t, tc.out_err, err)
	}
}

func TestIsAliasExist(t *testing.T) {
	global.MockAC()
	var m sqlmock.Sqlmock
	m, dbengine.Engine = global.MockDB()
	m.ExpectQuery("SELECT").WillReturnRows(global.GetRowsAliases(nil))
	m.ExpectQuery("SELECT").WillReturnRows(global.GetRowsAliases(global.GetTestAlias(1)))

	cases := []struct {
		in_aliasBrn string
		out_err     error
	}{
		{
			in_aliasBrn: "",
			out_err:     apiErr.NewResourceNotFoundException(fmt.Sprintf("[alias not found]"), nil),
		},
		{
			in_aliasBrn: "brn:bce:cfc:bj:640c8817bd1de2928d47256dd0620ce5:function:test_whx:testalias1",
			out_err:     nil,
		},
	}

	for _, tc := range cases {
		err := IsAliasExist(tc.in_aliasBrn)
		assert.Equal(t, tc.out_err, err)
	}

}

func TestDeleteTestEvents(t *testing.T) {
	global.MockAC()
	var m sqlmock.Sqlmock
	m, dbengine.Engine = global.MockDB()
	m.ExpectQuery("SELECT").WillReturnRows(global.GetRowsUserTestEvents(nil))

	findFunc := &dao.Function{
		Uid: "uiduid",
	}
	findFunc.FunctionName = "myFunc"
	cases := []struct {
		in_func *dao.Function
		out_err error
	}{
		{
			in_func: findFunc,
			out_err: nil,
		},
	}

	for _, tc := range cases {
		err := DeleteTestEvents(tc.in_func)
		assert.Equal(t, tc.out_err, err)
	}
}

func TestCheckRuntime(t *testing.T) {
	global.MockAC()
	var m sqlmock.Sqlmock
	m, dbengine.Engine = global.MockDB()

	cases := []struct {
		runtime string
		sqlFunc func()
		res     error
	}{
		// 404的情况
		{
			runtime: "invalid runtime",
			sqlFunc: func() {
				m.ExpectQuery("SELECT").WillReturnRows(global.GetRowsRuntimeConfig(nil))
			},
			res: apiErr.NewResourceNotFoundException("runtime not found", nil),
		},
		// runtime已下线情况
		{
			runtime: "nodejs8.5",
			sqlFunc: func() {
				runtimeConfigs := []dao.RuntimeConfig{
					{
						RuntimeConfiguration: api.RuntimeConfiguration{Name: "nodejs8.5"},
						DeprecatedAt:         time.Date(2018, 1, 1, 1, 1, 1, 1, time.Local),
					},
				}
				m.ExpectQuery("SELECT").WillReturnRows(global.GetRowsRuntimeConfig(runtimeConfigs))
			},
			res: kunErr.NewInvalidParameterValueException("the runtime is no longer supported, please try new version", nil),
		},
		// 正常情况
		{
			runtime: "nodejs8.5",
			sqlFunc: func() {
				runtimeConfigs := []dao.RuntimeConfig{
					{
						RuntimeConfiguration: api.RuntimeConfiguration{Name: "nodejs8.5"},
						DeprecatedAt:         time.Date(2028, 1, 1, 1, 1, 1, 1, time.Local),
					},
				}
				m.ExpectQuery("SELECT").WillReturnRows(global.GetRowsRuntimeConfig(runtimeConfigs))
			},

			res: nil,
		},
	}

	for _, tc := range cases {
		tc.sqlFunc()
		err := CheckRuntime(tc.runtime)
		assert.Equal(t, tc.res, err)
	}
}

func TestCheckAcl(t *testing.T) {
	variables := map[string]string{}
	variables["ACL"] = `{
    "id":"bos_access",
    "accessControlList":[
        {
            "service":"bce:bos",
            "region":"*",
            "resource":[
                "cfc-stack-for-guangda/*"
            ],
            "effect":"Allow",
            "permission":[
                "FULL_CONTROL"
            ]
        }
    ]
}`
	environment := &api.Environment{Variables: variables}
	CheckAcl(environment)
	environment.Variables["ACL"] = `{
    "id":"bos_access",
    "accessControlList":[
        {
            "region":"*",
            "resource":[
                "cfc-stack-for-guangda/*"
            ],
            "effect":"Allow",
            "permission":[
                "FULL_CONTROL"
            ]
        }
    ]
}`
	CheckAcl(environment)
	environment.Variables["ACL"] = `{
    "id":"bos_access",
    "accessControlList":[
        {
            "service1":"bce:bos",
            "region":"*",
            "resource":[
                "cfc-stack-for-guangda/*"
            ],
            "effect":"Allow",
            "permission":[
                "FULL_CONTROL"
            ]
        }
    ]
}`
	CheckAcl(environment)
	environment.Variables["ACL"] = `{
    "id":"",
    "accessControlList":[
        {
            "service1":"bce:bos",
            "region":"*",
            "resource":[
                "cfc-stack-for-guangda/*"
            ],
            "effect":"Allow",
            "permission":[
                "FULL_CONTROL"
            ]
        }
    ]
}`
	CheckAcl(environment)
}

// TestValidateFunc 测试函数，验证函数是否符合要求。包括超时、并发量、内存大小、日志类型等。
// 参数：t *testing.T - 单元测试的对象指针，用于输出错误信息。
// 返回值：无
func TestValidateFunc(t *testing.T) {
	global.MockAC()
	des := "abcedefghiabcedefghiabcedefghiabcedefghiabcedefghiabcedefghiabcedefghiabcedefghiabcedefghiabcedefghiabcedefghiabcede" +
		"fghiabcedefghiabcedefghiabcedefghiabcedefghiabcedefghiabcedefghiabcedefghiabcedefghiabcedefghiabcedefg" +
		"hiabcedefghiabcedefghiabcedefghiabcedefghiabcedefghiabcedefghiabcedefghiabcedefghiabcedefghiabcedefghi"
	role := "testtesttesttesttesttesttesttesttesttesttesttesttesttesttesttesttesttest"
	funct := &dao.Function{}
	timeout := 360
	cases := []struct {
		vtype     string
		functions *dao.Function
		byteArray []byte
	}{
		{
			vtype:     "create",
			functions: &dao.Function{},
			byteArray: []byte{},
		},
		{
			vtype:     "create",
			functions: &dao.Function{Description: &des},
			byteArray: []byte{},
		},
		{
			vtype:     "create",
			functions: &dao.Function{},
			byteArray: []byte{},
		},
		{
			vtype:     "create",
			functions: &dao.Function{},
			byteArray: []byte{},
		},
		{
			vtype:     "create",
			functions: &dao.Function{},
			byteArray: []byte{},
		},
	}

	for i, tc := range cases {
		if i == 0 {
			tc.functions.Timeout = &timeout
		}
		err := validateFunc(tc.vtype, tc.functions, tc.byteArray)
		if err == nil {
			t.Fatal("err")
		}
	}
	funct.Role = &role
	err := validateFunc("", funct, []byte{})
	if err == nil {
		t.Fatal("err")
	}

	funct.Role = nil
	concurrent := 12
	funct.PodConcurrentQuota = &concurrent
	err = validateFunc("", funct, []byte{})
	if err == nil {
		t.Fatal("err")
	}
	funct.PodConcurrentQuota = nil
	memerySize := 129
	funct.MemorySize = &memerySize
	err = validateFunc("", funct, []byte{})
	if err == nil {
		t.Fatal("err")
	}

	memerySize = 2048
	err = validateFunc("", funct, []byte{})
	if err == nil {
		t.Fatal("err")
	}

	memerySize = 128
	funct.LogType = "bos"
	funct.LogBosDir = "bos//"

	err = validateFunc("", funct, []byte{})
	if err == nil {
		t.Fatal("err")
	}

	funct.LogType = "kafka"
	funct.LogBosDir = "bos://"
	err = validateFunc("", funct, []byte{})
	if err == nil {
		t.Fatal("err")
	}

	funct.Runtime = "java8"
	memerySize = 2048
	err = validateFunc("", funct, []byte{})
	if err == nil {
		t.Fatal("err")
	}

	maxRetryAttempts := 4
	asyncConf := api.AsyncInvokeConfig{
		MaxRetryAttempts: &maxRetryAttempts,
	}
	funct.AsyncInvokeConfig = &asyncConf
	fmt.Println(*funct.AsyncInvokeConfig.MaxRetryAttempts)
	err = validateFunc("", funct, []byte{})
	if err == nil {
		t.Fatal("err")
	}

	maxRetryIntervalInSeconds := int64(43201)
	asyncConf1 := api.AsyncInvokeConfig{
		MaxRetryIntervalInSeconds: &maxRetryIntervalInSeconds,
	}
	funct.AsyncInvokeConfig = &asyncConf1
	funct.CFSConfig = &api.CFSConfig{
		FsName:     convert.String(""),
		FsId:       convert.String(""),
		Domain:     convert.String(""),
		SubnetID:   convert.String(""),
		RemotePath: convert.String(""),
		LocalPath:  convert.String(""),
	}
	validateFunc("", funct, []byte{})

}

// TestCopyReqFuncToDaoFunc_CustomRuntimeFields 测试CopyReqFuncToDaoFunc函数，特定场景下的自定义运行时字段
// 参数：t *testing.T - 单元测试对象指针，表示当前执行的是哪个测试用例
func TestCopyReqFuncToDaoFunc_CustomRuntimeFields(t *testing.T) {
	src := &dao.Function{
		FunctionConfig: api.FunctionConfig{
			CustomRuntimeConfig: &api.CustomRuntimeConfig{
				Args:    []string{"--foo"},
				Port:    9000,
				Command: []string{"/bin/sh"},
			},
			LangRuntime: "python3.9",
		},
	}
	dst := &dao.Function{}
	copyReqFuncToDaoFunc(src, dst)
	assert.NotNil(t, dst.CustomRuntimeConfig)
	assert.Equal(t, 9000, dst.CustomRuntimeConfig.Port)
	assert.Equal(t, "python3.9", dst.LangRuntime)
}
