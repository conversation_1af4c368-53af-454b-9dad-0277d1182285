package cloudfunc

import (
	"github.com/DATA-DOG/go-sqlmock"
	"github.com/stretchr/testify/assert"
	"icode.baidu.com/baidu/faas/kun/pkg/api"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/dao"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/dbengine"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/global"
	"icode.baidu.com/baidu/faas/kun/pkg/server"
	"testing"
)

func TestConsoleGetUserActivation(t *testing.T) {
	global.MockAC()
	cases := []struct {
		in_c         *server.Context
		mock_data    []dao.FeatureActivation
		out_HttpCode int
	}{
		{
			in_c:         global.BuildNewKunCtx("GET", "/v1/console/feature/activation", "", "uid-right", map[string]string{}),
			mock_data:    global.GetFeatureActivation(1),
			out_HttpCode: 200,
		},
		{
			in_c:         global.BuildNewKunCtx("GET", "/v1/console/feature/activation", "", "", map[string]string{}),
			mock_data:    global.GetFeatureActivation(0),
			out_HttpCode: 400,
		},
	}
	for _, tc := range cases {
		var m sqlmock.Sqlmock
		m, dbengine.Engine = global.MockDB()
		m.ExpectQuery("^SELECT \\* FROM `feature_activations`").WillReturnRows(global.GetRowsFeatureActivation(tc.mock_data))

		f := CfcEdgeRest{}
		f.ConsoleGetUserActivation(tc.in_c)
		assert.Equal(t, tc.out_HttpCode, tc.in_c.Response().StatusCode())
	}
}

func TestConsoleUserFeatureActivate(t *testing.T) {
	global.MockAC()
	activateData := global.GetFeatureActivation(1)
	inactivateData := global.GetFeatureActivation(1)
	inactivateData[0].Status = api.UserFeatureInactive
	cases := []struct {
		in_c         *server.Context
		mock_data    []dao.FeatureActivation
		out_HttpCode int
	}{
		{
			in_c:         global.BuildNewKunCtx("POST", "/v1/console/feature/activation", "", "c7ac82ae14ef42d1a4ffa3b2ececa17f", map[string]string{}),
			mock_data:    global.GetFeatureActivation(0),
			out_HttpCode: 200,
		},
		{
			in_c:         global.BuildNewKunCtx("POST", "/v1/console/feature/activation", "", "c7ac82ae14ef42d1a4ffa3b2ececa17f", map[string]string{}),
			mock_data:    activateData,
			out_HttpCode: 400,
		},
		{
			in_c:         global.BuildNewKunCtx("POST", "/v1/console/feature/activation", "", "", map[string]string{}),
			mock_data:    activateData,
			out_HttpCode: 400,
		},
	}
	for _, tc := range cases {
		var m sqlmock.Sqlmock
		m, dbengine.Engine = global.MockDB()
		m.ExpectQuery("^SELECT \\* FROM `feature_activations`").WillReturnRows(global.GetRowsFeatureActivation(tc.mock_data))
		req := "INSERT INTO `feature_activations` (`uid`,`type`,`status`,`updated_at`,`created_at`) VALUES (?,?,?,?,?)"
		m.ExpectExec(global.FixedFullRe(req)).WillReturnResult(sqlmock.NewResult(1, 1))

		f := CfcEdgeRest{}
		f.ConsoleUserFeatureActivate(tc.in_c)
		assert.Equal(t, tc.out_HttpCode, tc.in_c.Response().StatusCode())
	}
}

func TestConsoleCancelUserFeatureActivation(t *testing.T) {
	global.MockAC()
	cases := []struct {
		in_c         *server.Context
		mock_data    []dao.FeatureActivation
		out_HttpCode int
	}{
		{
			in_c:         global.BuildNewKunCtx("POST", "/v1/console/feature/activation", "", "c7ac82ae14ef42d1a4ffa3b2ececa17f", map[string]string{}),
			mock_data:    global.GetFeatureActivation(0),
			out_HttpCode: 400,
		},
		{
			in_c:         global.BuildNewKunCtx("POST", "/v1/console/feature/activation", "", "c7ac82ae14ef42d1a4ffa3b2ececa17f", map[string]string{}),
			mock_data:    global.GetFeatureActivation(1),
			out_HttpCode: 200,
		},
		{
			in_c:         global.BuildNewKunCtx("POST", "/v1/console/feature/activation", "", "c7ac82ae14ef42d1a4ffa3b2ececa17f", map[string]string{}),
			mock_data:    global.GetFeatureActivation(0),
			out_HttpCode: 400,
		},
	}
	for _, tc := range cases {
		var m sqlmock.Sqlmock
		m, dbengine.Engine = global.MockDB()
		m.ExpectQuery("^SELECT \\* FROM `feature_activations`").WillReturnRows(global.GetRowsFeatureActivation(tc.mock_data))
		req2 := "^UPDATE `feature_activations`"
		m.ExpectExec(req2).WillReturnResult(sqlmock.NewResult(0, 1))

		f := CfcEdgeRest{}
		f.ConsoleCancelUserFeatureActivation(tc.in_c)
		assert.Equal(t, tc.out_HttpCode, tc.in_c.Response().StatusCode())
	}
}
