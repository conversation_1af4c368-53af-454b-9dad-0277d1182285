package cloudfunc

import (
	"github.com/emicklei/go-restful"

	"icode.baidu.com/baidu/faas/kun/cmd/apiserver/filter"
	"icode.baidu.com/baidu/faas/kun/pkg/api"
	apiServer "icode.baidu.com/baidu/faas/kun/pkg/apiserver/server"
	"icode.baidu.com/baidu/faas/kun/pkg/middleware"
	"icode.baidu.com/baidu/faas/kun/pkg/server"
	"icode.baidu.com/baidu/faas/kun/pkg/server/endpoint"
)

// ConsoleAPIs xxx
func (f FunctionRest) ConsoleAPIs() []endpoint.ApiSingle {
	apis := []endpoint.ApiSingle{
		// get function, GET /2015-03-31/console/functions/{FunctionName}
		endpoint.ApiSingle{
			Verb:    "GET",
			Path:    "/console/functions/{FunctionName}",
			Handler: server.WrapRestRouteFunc(f.findFuncALL),
			Filters: []restful.FilterFunction{
				filter.IAMPermissionCheck(api.Resource, "", []string{"cfc:GetFunction", filter.IamPermRead}),
			},
		},
		// create function, POST /2015-03-31/console/functions
		endpoint.ApiSingle{
			Verb:    "POST",
			Path:    "/console/functions",
			Handler: server.WrapRestRouteFunc(f.consoleNewFunc),
			Filters: []restful.FilterFunction{
				filter.IAMPermissionCheck(api.Resource, "", []string{"cfc:CreateFunctions", filter.IamPermWrite}),
				filter.BillingCheck,
				filter.CheckCFCWhiteList(),
			},
		},
		// update function, POST /2015-03-31/console/functions/{FunctionName}
		endpoint.ApiSingle{
			Verb:    "PUT",
			Path:    "/console/functions/{FunctionName}",
			Handler: server.WrapRestRouteFunc(f.consoleUpdate),
			Filters: []restful.FilterFunction{
				filter.IAMPermissionCheck(api.Resource, "", []string{"cfc:UpdateFunctionCode", filter.IamPermWrite}),
			},
		},
		//look up function log, GET /2015-03-31/console/functions/{FunctionName}/log/{BosPath}
		endpoint.ApiSingle{
			Verb:    "GET",
			Path:    "/console/functions/{FunctionName}/log",
			Handler: server.WrapRestRouteFunc(f.consoleViewLog),
			Filters: []restful.FilterFunction{
				filter.IAMPermissionCheck(api.Resource, "", []string{"cfc:ViewLog", filter.IamPermRead}),
			},
		},
		{
			Verb:    "GET",
			Path:    "/console/functions/vpc/permission",
			Handler: server.WrapRestRouteFunc(f.consoleValidateVpcPermission),
			Filters: []restful.FilterFunction{
				filter.IAMPermissionCheck(api.Resource, "", []string{"cfc:ValidateVpcPermission", filter.IamPermRead}),
			},
		},
	}
	return apis
}

// 白名单邀请
// InviteAPIs xxx
func (f FunctionRest) InviteAPIs() []endpoint.ApiSingle {
	apis := []endpoint.ApiSingle{
		// create invite link, GET /2015-03-31/console/invitation
		endpoint.ApiSingle{
			Verb:    "POST",
			Path:    "/console/invitation",
			Handler: server.WrapRestRouteFunc(f.consoleInvitation),
			Filters: []restful.FilterFunction{
				filter.IAMAuthenticateCheck,
			},
		},
	}
	return apis
}

// 协助其他服务授权
// AssistAuthorizationAPIs xxx
func (f FunctionRest) AssistAuthorizationAPIs() []endpoint.ApiSingle {
	apis := []endpoint.ApiSingle{
		// create invite link, GET /2015-03-31/console/assist/authorization
		endpoint.ApiSingle{
			Verb:    "POST",
			Path:    "/console/assist/authorization",
			Handler: server.WrapRestRouteFunc(f.consoleAssistAuthorization),
			Filters: []restful.FilterFunction{
				filter.IAMAuthenticateCheck,
			},
		},
	}
	return apis
}

// InsideAPIs xxx
func (f FunctionRest) InsideAPIs() []endpoint.ApiSingle {
	apis := []endpoint.ApiSingle{
		// get invite link, GET /inside-v1/functions/{FunctionName}
		endpoint.ApiSingle{
			Verb:    "GET",
			Path:    "/functions/{FunctionName}",
			Handler: server.WrapRestRouteFuncWithTrace("ApiServerHandler/insideGetFunction", f.insideFind),
			Filters: []restful.FilterFunction{
				middleware.SimpleAuthTokenVerify(),
			},
		},
		// get invite link, Post /inside-v1/functions/{FunctionBrn}/ban
		endpoint.ApiSingle{
			Verb:    "PUT",
			Path:    "/functions/{FunctionBrn}/ban",
			Handler: server.WrapRestRouteFunc(f.banFunction),
			Filters: []restful.FilterFunction{
				middleware.SimpleAuthTokenVerify(),
			},
		},
		// get invite link, POST /inside-v1/functions/{FunctionBrn}/unblock
		endpoint.ApiSingle{
			Verb:    "PUT",
			Path:    "/functions/{FunctionBrn}/unblock",
			Handler: server.WrapRestRouteFunc(f.unblockFunction),
			Filters: []restful.FilterFunction{
				middleware.SimpleAuthTokenVerify(),
			},
		},
	}
	return apis
}

func (f AliasResource) InsideAPIs() []endpoint.ApiSingle {
	apis := []endpoint.ApiSingle{
		// Get /inside-v1/functions/alias/{FunctionBrn}
		endpoint.ApiSingle{
			Verb:    "GET",
			Path:    "/functions/aliases/{FunctionBrn}",
			Handler: server.WrapRestRouteFunc(f.insideFind),
			Filters: []restful.FilterFunction{
				middleware.SimpleAuthTokenVerify(),
			},
		},
	}
	return apis
}

// APIs xxx
func (f FunctionRest) APIs() []endpoint.ApiSingle {
	apis := []endpoint.ApiSingle{
		// create function, POST /2015-03-31/functions
		endpoint.ApiSingle{
			Verb:    "POST",
			Path:    "/functions",
			Handler: server.WrapRestRouteFunc(f.create),
			Filters: []restful.FilterFunction{
				filter.IAMPermissionCheck(api.Resource, "", []string{"cfc:CreateFunctions", filter.IamPermWrite}),
				filter.RealNameCheck,
				filter.BillingCheck,
				filter.CheckCFCWhiteList(),
			},
		},
		// list function, GET /2015-03-31/functions
		endpoint.ApiSingle{
			Verb:    "GET",
			Path:    "/functions",
			Handler: server.WrapRestRouteFunc(f.findByCondition),
			Filters: []restful.FilterFunction{
				filter.IAMPermissionCheck(api.Resource, "", []string{"cfc:ListFunctions", filter.IamPermList}),
				server.WrapRestFilterFunction(filter.ValidatorQueryParameter),
			},
		},
		// get function, POST /2015-03-31/functions/{FunctionName}
		endpoint.ApiSingle{
			Verb:    "GET",
			Path:    "/functions/{FunctionName}",
			Handler: server.WrapRestRouteFunc(f.find),
			Filters: []restful.FilterFunction{
				filter.IAMPermissionCheck(api.Resource, "", []string{"cfc:GetFunction", filter.IamPermRead}),
			},
		},
		// update function code, PUT /2015-03-31/functions/{FunctionName}/code
		endpoint.ApiSingle{
			Verb:    "PUT",
			Path:    "/functions/{FunctionName}/code",
			Handler: server.WrapRestRouteFunc(f.updateCode),
			Filters: []restful.FilterFunction{
				filter.IAMPermissionCheck(api.Resource, "", []string{"cfc:UpdateFunctionCode", filter.IamPermWrite}),
			},
		},
		// update function configuration, put /2015-03-31/functions/{FunctionName}/configuration
		endpoint.ApiSingle{
			Verb:    "PUT",
			Path:    "/functions/{FunctionName}/configuration",
			Handler: server.WrapRestRouteFunc(f.updateConfiguration),
			Filters: []restful.FilterFunction{
				filter.IAMPermissionCheck(api.Resource, "", []string{"cfc:UpdateFunctionConfiguration", filter.IamPermWrite}),
			},
		},
		// get function configuration, GET /2015-03-31/functions/{FunctionName}/configuration
		endpoint.ApiSingle{
			Verb:    "GET",
			Path:    "/functions/{FunctionName}/configuration",
			Handler: server.WrapRestRouteFunc(f.getConfiguration),
			Filters: []restful.FilterFunction{
				filter.IAMPermissionCheck(api.Resource, "", []string{"cfc:GetFunctionConfiguration", filter.IamPermRead}),
			},
		},
		// delete function, DELETE /2015-03-31/functions/{FunctionName}
		endpoint.ApiSingle{
			Verb:    "DELETE",
			Path:    "/functions/{FunctionName}",
			Handler: apiServer.WrapRestRouteFunc(f.delete),
			Filters: []restful.FilterFunction{
				filter.IAMPermissionCheck(api.Resource, "", []string{"cfc:DeleteFunction", filter.IamPermWrite}),
			},
		},
		// get duedge function, POST /2015-03-31/duedge/functions/{FunctionName}
		endpoint.ApiSingle{
			Verb:    "GET",
			Path:    "/duedge/functions/{FunctionName}",
			Handler: server.WrapRestRouteFunc(f.duEdgeFind),
			Filters: []restful.FilterFunction{
				filter.IAMPermissionCheck(api.Resource, "", []string{"cfc:GetFunction", filter.IamPermRead}),
			},
		},
		// get ote function, POST /2015-03-31/ote/functions/{FunctionName}
		endpoint.ApiSingle{
			Verb:    "GET",
			Path:    "/ote/functions/{FunctionName}",
			Handler: server.WrapRestRouteFunc(f.oteFind),
			Filters: []restful.FilterFunction{
				filter.IAMPermissionCheck(api.Resource, "", []string{"cfc:GetFunction"}),
			},
		},
	}

	apis = append(apis, new(VersionResource).APIs()...)
	apis = append(apis, new(AliasResource).APIs()...)
	apis = append(apis, new(BlsRest).PublicAPIs()...)
	apis = append(apis, new(CFSRest).PublicAPIs()...)

	return apis
}

func (cfs CFSRest) PublicAPIs() []endpoint.ApiSingle {
	apis := []endpoint.ApiSingle{
		//获取用户指定子网下的所有CFS信息
		{
			Verb: "GET",
			//TODOLZ subnetId改小写
			Path:    "/cfsInfo/{SubnetId}",
			Handler: server.WrapRestRouteFuncWithTrace("ApiServerHandler/listCFSInfosInSpecificSubnet", cfs.list),
			Filters: []restful.FilterFunction{
				filter.IAMAuthenticateCheck,
			},
		},
	}
	return apis
}

// APIs xxx
func (r RuntimeRest) APIs() []endpoint.ApiSingle {
	apis := []endpoint.ApiSingle{
		// get runtime configuration, GET /inside-v1/runtimes/{RuntimeName}/configuration
		endpoint.ApiSingle{
			Verb:    "GET",
			Path:    "/runtimes/{RuntimeName}/configuration",
			Handler: server.WrapRestRouteFuncWithTrace("APiServerHandler/getRuntimesConfiguration", r.find),
		},
	}
	return apis
}

// PublicAPIs xxx
func (r RuntimeRest) PublicAPIs() []endpoint.ApiSingle {
	apis := []endpoint.ApiSingle{
		// get runtime configuration, GET /2015-03-31/ote/runtimes/{RuntimeName}/configuration
		endpoint.ApiSingle{
			Verb:    "GET",
			Path:    "/ote/runtimes/{RuntimeName}/configuration",
			Handler: server.WrapRestRouteFuncWithTrace("APiServerHandler/getOteRuntimesConfiguration", r.oteFind),
			Filters: []restful.FilterFunction{
				filter.IAMAuthenticateCheck,
			},
		},
	}
	return apis
}

func (c CfcEdgeRest) ConsoleAPIs() []endpoint.ApiSingle {
	apis := []endpoint.ApiSingle{
		endpoint.ApiSingle{
			Verb:    "GET",
			Path:    "/console/feature/activation",
			Handler: server.WrapRestRouteFunc(c.ConsoleGetUserActivation),
			Filters: []restful.FilterFunction{
				filter.IAMAuthenticateCheck,
			},
		},
		endpoint.ApiSingle{
			Verb:    "POST",
			Path:    "/console/feature/activation",
			Handler: server.WrapRestRouteFunc(c.ConsoleUserFeatureActivate),
			Filters: []restful.FilterFunction{
				filter.IAMAuthenticateCheck,
			},
		},
		endpoint.ApiSingle{
			Verb:    "DELETE",
			Path:    "/console/feature/activation",
			Handler: server.WrapRestRouteFunc(c.ConsoleCancelUserFeatureActivation),
			Filters: []restful.FilterFunction{
				filter.IAMAuthenticateCheck,
			},
		},
	}
	return apis
}

// function reserved apis
func (f FunctionReservedRest) PublicAPIs() []endpoint.ApiSingle {
	apis := []endpoint.ApiSingle{
		endpoint.ApiSingle{
			Verb:    "POST",
			Path:    "/functions/reserved",
			Handler: server.WrapRestRouteFunc(f.create),
			Filters: []restful.FilterFunction{
				filter.IAMPermissionCheck(api.Resource, "", []string{"CreateFunctionReserved", filter.IamPermWrite}),
			},
		},
		endpoint.ApiSingle{
			Verb:    "GET",
			Path:    "/functions/reserved",
			Handler: server.WrapRestRouteFunc(f.list),
			Filters: []restful.FilterFunction{
				filter.IAMPermissionCheck(api.Resource, "", []string{"GetFunctionReserved", filter.IamPermRead}),
			},
		},
		endpoint.ApiSingle{
			Verb:    "GET",
			Path:    "/functions/reserved/{FunctionBrn}",
			Handler: server.WrapRestRouteFunc(f.find),
			Filters: []restful.FilterFunction{
				filter.IAMPermissionCheck(api.Resource, "", []string{"GetFunctionReserved", filter.IamPermRead}),
			},
		},
		endpoint.ApiSingle{
			Verb:    "PUT",
			Path:    "/functions/reserved/{Uuid}",
			Handler: server.WrapRestRouteFunc(f.update),
			Filters: []restful.FilterFunction{
				filter.IAMPermissionCheck(api.Resource, "", []string{"UpdateFunctionReserved", filter.IamPermWrite}),
			},
		},
		endpoint.ApiSingle{
			Verb:    "DELETE",
			Path:    "/functions/reserved/{Uuid}",
			Handler: server.WrapRestRouteFunc(f.delete),
			Filters: []restful.FilterFunction{
				filter.IAMPermissionCheck(api.Resource, "", []string{"DeleteFunctionReserved", filter.IamPermWrite}),
			},
		},
	}
	return apis
}

// InsideAPIs xxx
func (f FunctionReservedRest) InsideAPIs() []endpoint.ApiSingle {
	apis := []endpoint.ApiSingle{
		// GET /inside-v1/functions/reserved/{Uuid}
		endpoint.ApiSingle{
			Verb:    "GET",
			Path:    "/functions/reserved/{Uuid}",
			Handler: server.WrapRestRouteFuncWithTrace("ApiServerHandler/insideGetFunctionReserved", f.insideFind),
			Filters: []restful.FilterFunction{
				middleware.SimpleAuthTokenVerify(),
			},
		},
		endpoint.ApiSingle{
			Verb:    "GET",
			Path:    "/functions/reserved",
			Handler: server.WrapRestRouteFuncWithTrace("ApiServerHandler/insideListFunctionReserved", f.insideList),
			Filters: []restful.FilterFunction{
				middleware.SimpleAuthTokenVerify(),
			},
		},
		endpoint.ApiSingle{
			Verb:    "PUT",
			Path:    "/functions/reserved/{Uuid}",
			Handler: server.WrapRestRouteFuncWithTrace("ApiServerHandler/insideUpdateFunctionReserved", f.insideUpdate),
			Filters: []restful.FilterFunction{
				middleware.SimpleAuthTokenVerify(),
			},
		},
		endpoint.ApiSingle{
			Verb:    "DELETE",
			Path:    "/functions/reserved/{Uuid}",
			Handler: server.WrapRestRouteFuncWithTrace("ApiServerHandler/insideDeleteFunctionReserved", f.insideDelete),
			Filters: []restful.FilterFunction{
				middleware.SimpleAuthTokenVerify(),
			},
		},
	}
	return apis
}

// BlsRest APIs xxx
func (f BlsRest) PublicAPIs() []endpoint.ApiSingle {
	apis := []endpoint.ApiSingle{
		endpoint.ApiSingle{
			Verb:    "GET",
			Path:    "/logstore",
			Handler: server.WrapRestRouteFunc(f.listLogStore),
			Filters: []restful.FilterFunction{
				filter.IAMPermissionCheck(api.Resource, "", []string{"cfc:QueryLogRecord", filter.IamPermWrite}),
			},
		},
		endpoint.ApiSingle{
			Verb:    "GET",
			Path:    "/logstore/{logStoreName}/logstream",
			Handler: server.WrapRestRouteFunc(f.listLogstream),
			Filters: []restful.FilterFunction{
				filter.IAMPermissionCheck(api.Resource, "", []string{"cfc:QueryLogRecord", filter.IamPermWrite}),
			},
		},
		endpoint.ApiSingle{
			Verb:    "GET",
			Path:    "/logstore/{logStoreName}/queryrecord",
			Handler: server.WrapRestRouteFunc(f.queryLogRecord),
			Filters: []restful.FilterFunction{
				filter.IAMPermissionCheck(api.Resource, "", []string{"cfc:QueryLogRecord", filter.IamPermWrite}),
			},
		},
		endpoint.ApiSingle{
			Verb:    "GET",
			Path:    "/logstore/{logStoreName}/pullrecord",
			Handler: server.WrapRestRouteFunc(f.pullLogRecord),
			Filters: []restful.FilterFunction{
				filter.IAMPermissionCheck(api.Resource, "", []string{"cfc:PullLogRecord", filter.IamPermWrite}),
			},
		},
		endpoint.ApiSingle{
			Verb:    "GET",
			Path:    "/logstore",
			Handler: server.WrapRestRouteFunc(f.listLogStore),
			Filters: []restful.FilterFunction{
				filter.IAMPermissionCheck(api.Resource, "", []string{"cfc:QueryLogRecord", filter.IamPermWrite}),
			},
		},
	}

	return apis
}
