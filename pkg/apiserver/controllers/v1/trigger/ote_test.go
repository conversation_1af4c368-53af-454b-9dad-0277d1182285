package trigger

import (
	"github.com/DATA-DOG/go-sqlmock"
	"github.com/stretchr/testify/assert"
	"icode.baidu.com/baidu/faas/kun/pkg/api"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/dao"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/dbengine"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/global"
	"icode.baidu.com/baidu/faas/kun/pkg/server"
	"icode.baidu.com/baidu/faas/kun/pkg/util/convert"
	"testing"
)

func TestListOTEPolicy(t *testing.T) {
	global.MockAC()

	var m sqlmock.Sqlmock
	m, dbengine.Engine = global.MockDB()
	m.ExpectQuery(".*").WillReturnRows(global.GetRowsPolicy(global.GetTestPolicy(1)))
	m.ExpectQuery(".*").WillReturnRows(global.GetRowsFunctions(getTestOTEFunc()))

	cases := []struct {
		in_c         *server.Context
		out_HttpCode int
	}{
		{
			in_c: global.BuildNewKunCtx("GET",
				"/v1/functions/brn:bce:cfc:bj:cd64f99c69d7c404b61de0a4f1865834:function:CDNTrigger:1/policy",
				``, "c7ac82ae14ef42d1a4ffa3b2ececa17f", map[string]string{
					"FunctionName": "brn:bce:cfc:bj:cd64f99c69d7c404b61de0a4f1865834:function:CDNTrigger:1",
				}),
			out_HttpCode: 200,
		},
	}

	f := PolicyRest{}
	for _, tc := range cases {
		tc.in_c.HTTPRequest().Header.Set("X-Bce-Account-Id", "df391b08c64c426a81645468c75163a5")
		f.otelist(tc.in_c)
		assert.Equal(t, tc.out_HttpCode, tc.in_c.Response().StatusCode())
	}
}

func getTestOTEFunc() (ret []dao.Function) {
	u := dao.Function{
		Id:             uint(123),
		Uid:            "c7ac82ae14ef42d1a4ffa3b2ececa17f",
		FunctionBrn:    "brn:bce:cfc:bj:cd64f99c69d7c404b61de0a4f1865834:function:CfcEdgeTrigger:1",
		Region:         "bj",
		Timeout:        convert.Int(3),
		EnvironmentStr: `{"Variables":{"key":"value"}}`,
		FunctionConfig: api.FunctionConfig{
			CodeSha256:                   "cNBGAjstP9SgRKYdf8I3ang/4dPWdcrTUYzpSD4bD8s=",
			CodeSize:                     int32(123),
			FunctionName:                 "CfcEdgeTrigger",
			Handler:                      "index.handler",
			Version:                      "1",
			Runtime:                      "nodejs6.11",
			MemorySize:                   convert.Int(128),
			CommitID:                     convert.String("commit id"),
			LogType:                      "bos",
			CodeID:                       "5afb2719-b470-4bce-a9d8-11379f8c57ce",
			ReservedConcurrentExecutions: convert.Int(1),
		},
	}
	ret = append(ret, u)
	return
}
