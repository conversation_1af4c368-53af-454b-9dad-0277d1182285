package trigger

import (
	"net/http"

	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/controllers/v1/cloudfunc"
	apiErr "icode.baidu.com/baidu/faas/kun/pkg/apiserver/error"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/global"
	"icode.baidu.com/baidu/faas/kun/pkg/bce/cdn"
	kunErr "icode.baidu.com/baidu/faas/kun/pkg/error"
	"icode.baidu.com/baidu/faas/kun/pkg/server"
)

// 访问cdn获取域名列表
func (r RelationRest) listDomains(c *server.Context) {
	response := c.Response()
	nextMarker := c.Request().QueryParameter("nextMarker")

	user, err := cloudfunc.GetUser(c.Request())
	if err != nil {
		c.WithErrorLog(apiErr.NewInitFuncMetaException(err.Error(), "", err)).WriteTo(response)
		return
	}

	info := cdn.NewRequestInfo(user.Domain.ID, c.RequestID())
	domains, err := global.AC.Clients.Cdn.ListDomains(info, nextMarker)
	if err != nil {
		c.WithErrorLog(kunErr.NewServiceException("list domains from cdn failed", err)).WriteTo(response)
		return
	}

	response.WriteHeaderAndEntity(http.StatusOK, domains)
}
