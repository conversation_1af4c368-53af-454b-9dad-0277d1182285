package trigger

import (
	"net/http"

	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/controllers/v1/cloudfunc"
	apiErr "icode.baidu.com/baidu/faas/kun/pkg/apiserver/error"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/global"
	"icode.baidu.com/baidu/faas/kun/pkg/bce/apigateway"
	kunErr "icode.baidu.com/baidu/faas/kun/pkg/error"
	"icode.baidu.com/baidu/faas/kun/pkg/server"
)

// listGroups函数用于在创建触发器时获取APIGateway中对应分组列表
func (r RelationRest) listGroups(c *server.Context) {
	response := c.Response()

	user, err := cloudfunc.GetUser(c.Request())
	if err != nil {
		_ = c.WithWarnLog(apiErr.NewInitFuncMetaException("Get user failed", "", nil)).
			WriteTo(response)
		return
	}

	info := apigateway.NewRequestInfo(user.Domain.ID, c.RequestID())
	groups, err := global.AC.Clients.ApiGateway.ListGroups(info)
	if err != nil {
		// 用户未开通API Gateway
		if err.Error() == apigateway.NotActiveAllowUser {
			response.WriteHeaderAndEntity(http.StatusOK, map[string]interface{}{
				"success": true,
				"code":    "NotActiveAllowUser",
			})
			return
		}
		_ = c.WithWarnLog(kunErr.NewServiceException("list groups from api-gateway failed", err)).WriteTo(response)
		return
	}

	response.WriteHeaderAndEntity(http.StatusOK, groups)
}
