package trigger

import (
	"icode.baidu.com/baidu/faas/kun/pkg/api"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/controllers/v1/cloudfunc"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/dao"
	apiErr "icode.baidu.com/baidu/faas/kun/pkg/apiserver/error"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/global"
	"icode.baidu.com/baidu/faas/kun/pkg/bce/brn"
	"icode.baidu.com/baidu/faas/kun/pkg/server"
	"icode.baidu.com/baidu/faas/kun/pkg/util/json"
	"net/http"
)

func (r PolicyRest) otelist(c *server.Context) {
	response := c.Response()

	ctx := global.BuildApiserverContext(c.Request(), c.Response(), "policy_otelist")
	if err := ctx.Observer.NewStage(global.OTECheckStage).Observe(cloudfunc.OTECheck(c)); err != nil {
		c.WithWarnLog(err).WriteTo(response)
		return
	}

	cfcBrn, _ := brn.Parse(c.Request().PathParameter("FunctionName"))
	findFunc := new(dao.Function)
	findFunc.FunctionBrn = cfcBrn.String()
	if rkunErr := ctx.Observer.NewStage(global.CheckFunctionStage).Observe(dao.FindOneFunc(findFunc)); rkunErr != nil {
		c.WithWarnLog(rkunErr).WriteTo(response)
		return
	}
	ctx.Context.FunctionBrn = findFunc.FunctionBrn
	findFunc.DealResFunction()

	po := &dao.Policy{
		Resource: cfcBrn.String(),
	}

	var (
		policysTmp  interface{}
		policySlice *[]dao.Policy
		err         error
	)
	policysTmp, err = ctx.Observer.NewStage(global.ListPolicyStage).ObserveObject(dao.ListPolicies(po))
	policySlice = policysTmp.(*[]dao.Policy)
	if err != nil || len(*policySlice) == 0 {
		c.WithWarnLog(apiErr.NewResourceNotFoundException("policy not found", err)).WriteTo(response)
		return
	}
	var retSlice []map[string]interface{}
	for index := range *policySlice {
		(*policySlice)[index].DealPolicy()
		ret := dealPolicyOutput(&(*policySlice)[index])
		retSlice = append(retSlice, *ret)
	}

	statementByte, _ := json.Marshal(map[string]interface{}{
		"Version":   "v1",
		"Id":        "default",
		"Statement": retSlice,
	})

	defer global.WriteSummary(ctx, api.LogNotToSummary)
	response.WriteHeaderAndEntity(http.StatusOK, map[string]interface{}{
		"Policy":     string(statementByte),
		"RevisionId": findFunc.CommitID,
	})
}
