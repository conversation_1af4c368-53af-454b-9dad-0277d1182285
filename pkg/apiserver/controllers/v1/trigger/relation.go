package trigger

import (
	"errors"
	"net/http"
	"regexp"
	"strings"
	"sync"

	"github.com/google/uuid"

	"icode.baidu.com/baidu/faas/kun/pkg/api"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/controllers/v1/cloudfunc"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/dao"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/dbengine"
	apiErr "icode.baidu.com/baidu/faas/kun/pkg/apiserver/error"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/global"
	apiServer "icode.baidu.com/baidu/faas/kun/pkg/apiserver/server"
	"icode.baidu.com/baidu/faas/kun/pkg/bce/brn"
	kunErr "icode.baidu.com/baidu/faas/kun/pkg/error"
	"icode.baidu.com/baidu/faas/kun/pkg/server"
	trigger "icode.baidu.com/baidu/faas/kun/pkg/trigger/relation"
)

var InvalidScopeType = errors.New("invalid scope type")

// RelationRest xxx
type RelationRest struct {
	Path string
}

// relation缓存
type relationCache struct {
	Map     sync.Map
	Wg      sync.WaitGroup
	ListErr error
}

// 获取用户能用的Trigger List
func (r RelationRest) listType(c *server.Context) {
	response := c.Response()
	user, err := cloudfunc.GetUser(c.Request())
	if err != nil {
		c.WithErrorLog(kunErr.NewServiceException("get user failed", err)).WriteTo(response)
		return
	}
	userID := user.Domain.ID
	internelCache := global.AC.Cache.InternalUserCache
	triggerTypes := api.GetUserScopeTriggerType(api.TriggerUserScopePublic, global.AC.Config.Region)
	if _, ok := internelCache[userID]; ok {
		triggerTypes = api.GetUserScopeTriggerType(api.TriggerUserScopeInternal, global.AC.Config.Region)
	}
	response.WriteHeaderAndEntity(http.StatusOK, map[string]interface{}{
		"RelationTypes": triggerTypes,
	})
}

// 从policy中读取触发源，向触发源请求触发器配置
func (r RelationRest) list(c *server.Context) {
	response := c.Response()

	ctx := global.BuildApiserverContext(c.Request(), c.Response(), "relation_list")
	scopeType := c.Request().QueryParameter("ScopeType")
	triggerMap, err := parseScopeType(scopeType)
	if err != nil {
		c.WithErrorLog(kunErr.NewInvalidParameterValueException("invalid scope type", err)).WriteTo(response)
		return
	}

	plcy := dao.Policy{
		Resource: c.Request().QueryParameter("FunctionBrn"),
	}
	if _, err := brn.Parse(plcy.Resource); err != nil {
		c.WithErrorLog(kunErr.NewInvalidParameterValueException("invalid function brn", err)).WriteTo(response)
		return
	}

	var (
		policysTmp  interface{}
		policySlice *[]dao.Policy
	)

	if policysTmp, err = ctx.Observer.NewStage(global.ListPolicyStage).ObserveObject(dao.ListPolicies(&plcy)); err != nil {
		c.WithErrorLog(kunErr.NewServiceException("list relation fail", err)).WriteTo(response)
		return
	}

	policySlice = policysTmp.(*[]dao.Policy)
	sourceUsedMap := make(map[string]bool)
	index := 0
	rc := &relationCache{}

	for _, plcy := range *policySlice {
		if cloudfunc.SourceAlreadyUsed(&plcy, sourceUsedMap) == true {
			continue
		}

		p := api.PrincipalServiceType(plcy.PrincipalService)
		tgr := trigger.GetTriggerInterfaceFromService(p)

		if triggerMap[tgr.GetTriggerType()] != true {
			continue
		}

		rc.Wg.Add(1)

		go func(tgr trigger.TriggerInterface, plcy dao.Policy, index int) {
			defer rc.Wg.Done()
			var (
				retTmp interface{}
				err    error
			)
			if retTmp, err = ctx.Observer.NewStage(global.GetRelationsStage).ObserveObject(tgr.GetRelation(c, &plcy)); err != nil {
				rc.ListErr = err
				return
			}

			ret := retTmp.([]*api.Relation)
			rc.Map.Store(index, ret)
		}(tgr, plcy, index)
		index++
	}
	rc.Wg.Wait()

	if rc.ListErr != nil {
		c.WithErrorLog(rc.ListErr).WriteTo(response)
		return
	}

	relationSlice := make([]*api.Relation, 0)
	for i := 0; i < index; i++ {
		v, _ := rc.Map.Load(i)
		relationSlice = append(relationSlice, v.([]*api.Relation)...)
	}

	defer global.WriteSummary(ctx, api.LogNotToSummary)
	response.WriteHeaderAndEntity(http.StatusOK, map[string]interface{}{
		"Relation": relationSlice,
	})
}

func (r *RelationRest) check(c *apiServer.ApiServerContext) {
	response := c.Response()

	relationReq, ok := c.Request().Attribute("relationReq").(*api.Relation)

	if !ok {
		c.WithWarnLog(kunErr.NewInvalidParameterValueException("invalid trigger config", nil)).WriteTo(response)
		return
	}

	tgr, err := getTriggerFromRlat(relationReq)
	if err != nil {
		c.WithWarnLog(err).WriteTo(response)
		return
	}

	c.DbTransaction = dbengine.DBTransaction()
	defer func() {
		if r := recover(); r != nil {
			c.DbTransaction.Rollback()
		} else if err != nil {
			c.DbTransaction.Rollback()
		}
	}()
	if err := tgr.CheckRelation(c, relationReq); err != nil {
		c.WithWarnLog(err).WriteTo(response)
		return
	}
	c.DbTransaction.Commit()

	response.WriteHeaderAndEntity(http.StatusOK, map[string]interface{}{
		"isConflict": false,
	})
}

func (r *RelationRest) create(c *apiServer.ApiServerContext) {
	response := c.Response()

	ctx := global.BuildApiserverContext(c.Request(), c.Response(), "relation_create")
	relationReq, ok := c.Request().Attribute("relationReq").(*api.Relation)
	if !ok {
		c.WithWarnLog(kunErr.NewInvalidParameterValueException("invalid trigger config", nil)).WriteTo(response)
		return
	}

	tgr, err := getTriggerFromRlat(relationReq)
	if err != nil {
		c.WithWarnLog(err).WriteTo(response)
		return
	}

	c.DbTransaction = dbengine.DBTransaction()
	var (
		policyToCreateTmp interface{}
		policyToCreate    *dao.Policy
	)
	if policyToCreateTmp, err = ctx.Observer.NewStage(global.CheckIfNeedCreatePolicyStage).ObserveObject(tgr.CheckIfNeedCreatePolicy(relationReq)); err != nil {
		c.DbTransaction.Rollback()
		c.WithWarnLog(err).WriteTo(response)
		return
	}
	if policyToCreateTmp != nil {
		policyToCreate = policyToCreateTmp.(*dao.Policy)
	} else {
		policyToCreate = nil
	}

	if policyToCreate != nil {
		if err = ctx.Observer.NewStage(global.CreatePolicyStage).Observe(createPolicy(c, relationReq, policyToCreate)); err != nil {
			c.DbTransaction.Rollback()
			c.WithWarnLog(err).WriteTo(response)
			return
		}
	}

	if err = ctx.Observer.NewStage(global.CreateRelationStage).Observe(tgr.CreateRelation(c, relationReq)); err != nil {
		if policyToCreate != nil { //&& deletePolicy(policyToCreate) != nil
			c.Logger().Info("delete policy failed after create relation failed")
		}
		c.DbTransaction.Rollback()
		c.WithWarnLog(err).WriteTo(response)
		return
	}
	if err := c.DbTransaction.Commit().Error; err != nil {
		c.WithErrorLog(kunErr.NewServiceException("delete function failed because of commit failure", err)).WriteTo(c.Response())
		return
	}

	defer global.WriteSummary(ctx, api.LogNotToSummary)
	response.WriteHeaderAndEntity(http.StatusCreated, map[string]interface{}{
		"Relation": relationReq,
	})
}

func (r *RelationRest) update(c *server.Context) {
	response := c.Response()

	ctx := global.BuildApiserverContext(c.Request(), c.Response(), "relation_update")
	relationReq, ok := c.Request().Attribute("relationReq").(*api.Relation)
	if !ok {
		c.WithWarnLog(kunErr.NewInvalidParameterValueException("invalid trigger config", nil)).WriteTo(response)
		return
	}

	tgr, err := getTriggerFromRlat(relationReq)
	if err != nil {
		c.WithWarnLog(err).WriteTo(response)
		return
	}

	if err := ctx.Observer.NewStage(global.CheckPolicyStage).Observe(tgr.VerifyInputPolicySource(relationReq.Source)); err != nil {
		c.WithWarnLog(kunErr.NewInvalidParameterValueException("invalid trigger source", err)).WriteTo(response)
		return
	}

	if err := ctx.Observer.NewStage(global.UpdateRelationStage).Observe(tgr.UpdateRelation(c, relationReq)); err != nil {
		c.WithWarnLog(err).WriteTo(response)
		return
	}

	defer global.WriteSummary(ctx, api.LogNotToSummary)
	response.WriteHeaderAndEntity(http.StatusOK, map[string]interface{}{
		"Relation": relationReq,
	})
}

func (r *RelationRest) delete(c *apiServer.ApiServerContext) {
	response := c.Response()
	relationReq := &api.Relation{
		RelationID: c.Request().QueryParameter("RelationId"),
		Target:     c.Request().QueryParameter("Target"),
		Source:     c.Request().QueryParameter("Source"),
	}

	ctx := global.BuildApiserverContext(c.Request(), c.Response(), "relation_delete")
	if relationReq.RelationID == "" || relationReq.Target == "" || relationReq.Source == "" {
		c.WithWarnLog(kunErr.NewInvalidParameterValueException("missing parameter", nil)).WriteTo(response)
		return
	}

	tgr, err := getTriggerFromRlat(relationReq)
	if err != nil {
		c.WithWarnLog(err).WriteTo(response)
		return
	}

	if err := ctx.Observer.NewStage(global.CheckPolicyStage).Observe(tgr.VerifyInputPolicySource(relationReq.Source)); err != nil {
		c.WithWarnLog(kunErr.NewInvalidParameterValueException("invalid trigger source", err)).WriteTo(response)
		return
	}

	var ret interface{}
	c.DbTransaction = dbengine.DBTransaction()
	if ret, err = ctx.Observer.NewStage(global.DeleteRelationsStage).ObserveObject(tgr.DeleteRelation(c, relationReq)); err != nil {
		c.DbTransaction.Rollback()
		c.WithWarnLog(err).WriteTo(response)
		return
	}

	var (
		plcyToDeleteTmp interface{}
		plcyToDelete    *dao.Policy
	)
	if plcyToDeleteTmp, err = ctx.Observer.NewStage(global.CheckIfNeedDeletePolicyStage).ObserveObject(tgr.CheckIfNeedDeletePolicy(relationReq, ret)); err != nil {
		c.WithWarnLog(err).WriteTo(response)
	}

	if plcyToDeleteTmp != nil {
		plcyToDelete = plcyToDeleteTmp.(*dao.Policy)
	} else {
		plcyToDelete = nil
	}

	if plcyToDelete != nil {
		if err = ctx.Observer.NewStage(global.DeletePolicyStage).Observe(dao.DeletePolicy(plcyToDelete, c.DbTransaction)); err != nil {
			c.DbTransaction.Rollback()
			c.WithWarnLog(err).WriteTo(response)
			return
		}
	}
	if err := c.DbTransaction.Commit().Error; err != nil {
		c.WithErrorLog(kunErr.NewServiceException("delete relation failed because of commit failure", err)).WriteTo(response)
		return
	}

	defer global.WriteSummary(ctx, api.LogNotToSummary)
	response.WriteHeaderAndEntity(http.StatusNoContent, nil)
}

func createPolicy(c *apiServer.ApiServerContext, rlatReq *api.Relation, plcy *dao.Policy) error {
	statementId := "cfc-" + uuid.New().String()
	plcy.StatementId = statementId
	plcy.Effect = api.PolicyActionAllow
	plcy.Action = api.CfcInvokeAction

	user, err := cloudfunc.GetUser(c.Request())
	if err != nil {
		return apiErr.NewUserNotFoundException("", errors.New("User not found"))
	}
	plcy.FunctionUid = user.Domain.ID

	targetFuncBrn, _ := brn.Parse(rlatReq.Target)
	plcy.FunctionName = strings.Split(targetFuncBrn.Resource, ":")[1]

	if err = dao.CreatePolicy(plcy, c.DbTransaction); err != nil {
		return kunErr.NewServiceException("dao create policy failed", err)
	}
	// 如果是bls触发器需要额外创建一个定时触发器
	if plcy.PrincipalService == api.BlsPrincipalService {
		policy := &dao.Policy{
			Resource:         plcy.Resource,
			PrincipalService: api.CrontabPrincipalService,
		}
		polSlice, err := dao.ListPolicies(policy)
		if err != nil {
			return kunErr.NewServiceException("creating bls policy failed", err)
		}
		if len(*polSlice) == 0 {
			newPlcy := &dao.Policy{}
			newPlcy.PrincipalService = api.CrontabPrincipalService
			newPlcy.Source = "cfc-crontab-trigger/v1/"
			newPlcy.StatementId = "cfc-" + uuid.New().String()
			newPlcy.FunctionUid = user.Domain.ID
			newPlcy.FunctionName = strings.Split(targetFuncBrn.Resource, ":")[1]
			newPlcy.Effect = api.PolicyActionAllow
			newPlcy.Action = api.CfcInvokeAction
			newPlcy.Resource = plcy.Resource
			if err = dao.CreatePolicy(newPlcy, c.DbTransaction); err != nil {
				return kunErr.NewServiceException("dao create policy failed", err)
			}
		}
	}
	rlatReq.PolicySid = statementId
	return nil
}

func getTriggerFromRlat(rlatReq *api.Relation) (trigger.TriggerInterface, error) {
	sourceSlice := strings.SplitN(rlatReq.Source, "/", 2)
	if len(sourceSlice) == 0 {
		return nil, kunErr.NewInvalidParameterValueException("invalid trigger source", nil)
	}

	triggerType := trigger.GetTriggerInterface(api.TriggerType(sourceSlice[0]))
	if triggerType == nil {
		return nil, kunErr.NewInvalidParameterValueException("invalid trigger source", nil)
	}
	return triggerType, nil
}

func parseScopeType(s string) (m map[api.TriggerType]bool, err error) {
	if s == "all" {
		return getScopeTriggerMap(true), nil
	}
	if s == "" {
		s = "!cfc-edge"
	}
	var isNegative bool
	if s[0] == '!' {
		isNegative = true
		s = s[1:]
	}
	var dataSets []string
	if s[0] == '(' {
		if !isValidBracketsRule(s) {
			return nil, InvalidScopeType
		}
		dataSets = strings.Split(s[1:len(s)-1], ",")
	} else {
		dataSets = []string{s}
	}
	var defaultFlag bool
	if isNegative {
		defaultFlag = true
	}
	m = getScopeTriggerMap(defaultFlag)
	for _, data := range dataSets {
		if _, ok := m[data]; ok {
			m[data] = !defaultFlag
		}
	}
	return
}

func getScopeTriggerMap(defaultAccess bool) (m map[api.TriggerType]bool) {
	allTriggers := api.AllTriggerTypes()
	m = make(map[api.TriggerType]bool, 0)
	for _, v := range allTriggers {
		m[v] = defaultAccess
	}
	return
}

func isValidBracketsRule(s string) bool {
	if s[0] != '(' || s[len(s)-1] != ')' {
		return false
	}
	if regexp.MustCompile(`^[a-zA-Z\-(),]*$`).MatchString(s) != true {
		return false
	}
	stacks := make([]rune, 0)
	for k, c := range s {
		if k != 0 && c == '(' {
			return false
		}
		stacks = append(stacks, c)
		if c == ')' {
			break
		}
	}
	if len(stacks) < len(s) {
		return false
	}
	return true
}
