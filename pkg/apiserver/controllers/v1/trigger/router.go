package trigger

import (
	"github.com/emicklei/go-restful"
	"icode.baidu.com/baidu/faas/kun/pkg/api"

	"icode.baidu.com/baidu/faas/kun/cmd/apiserver/filter"
	apiServer "icode.baidu.com/baidu/faas/kun/pkg/apiserver/server"
	"icode.baidu.com/baidu/faas/kun/pkg/server"
	"icode.baidu.com/baidu/faas/kun/pkg/server/endpoint"
)

// PublicAPIs xxx
func (r PolicyRest) PublicAPIs() []endpoint.ApiSingle {
	apis := []endpoint.ApiSingle{
		endpoint.ApiSingle{
			Verb:    "GET",
			Path:    "/functions/{FunctionName}/policy",
			Handler: server.WrapRestRouteFuncWithTrace("ApiServerHandler/getPolicy", r.list),
			Filters: []restful.FilterFunction{
				filter.IAMPermissionCheck(api.Resource, "", []string{"cfc:ListPolicies", filter.IamPermList}),
			},
		},
		endpoint.ApiSingle{
			Verb:    "POST",
			Path:    "/functions/{FunctionName}/policy",
			Handler: server.WrapRestRouteFunc(r.create),
			Filters: []restful.FilterFunction{
				filter.IAMPermissionCheck(api.Resource, "", []string{"cfc:CreatePolicy", filter.IamPermWrite}),
			},
		},
		endpoint.ApiSingle{
			Verb:    "PUT",
			Path:    "/functions/{FunctionName}/policy",
			Handler: apiServer.WrapRestRouteFunc(r.updateApiGateway),
			Filters: []restful.FilterFunction{
				filter.IAMPermissionCheck(api.Resource, "", []string{"cfc:UpdatePolicy", filter.IamPermWrite}),
			},
		},
		endpoint.ApiSingle{
			Verb:    "DELETE",
			Path:    "/functions/{FunctionName}/policy",
			Handler: apiServer.WrapRestRouteFunc(r.deleteApiGateway),
			Filters: []restful.FilterFunction{
				filter.IAMPermissionCheck(api.Resource, "", []string{"cfc:RemovePolicy", filter.IamPermWrite}),
			},
		},
		endpoint.ApiSingle{
			Verb:    "DELETE",
			Path:    "/functions/{FunctionName}/policy/{StatementId}",
			Handler: apiServer.WrapRestRouteFunc(r.delete),
			Filters: []restful.FilterFunction{
				filter.IAMPermissionCheck(api.Resource, "", []string{"cfc:RemovePolicy", filter.IamPermWrite}),
			},
		},
		endpoint.ApiSingle{
			Verb:    "GET",
			Path:    "/ote/functions/{FunctionName}/policy",
			Handler: server.WrapRestRouteFunc(r.otelist),
			Filters: []restful.FilterFunction{
				filter.IAMPermissionCheck(api.Resource, "", []string{"cfc:ListPolicies", filter.IamPermList}),
			},
		},
	}
	return apis
}

// ConsoleAPIs xxx
func (r RelationRest) ConsoleAPIs() []endpoint.ApiSingle {
	apis := []endpoint.ApiSingle{
		endpoint.ApiSingle{
			Verb:    "GET",
			Path:    "/console/relation/type",
			Handler: server.WrapRestRouteFunc(r.listType),
			Filters: []restful.FilterFunction{
				filter.IAMPermissionCheck(api.Resource, "", []string{"cfc:ListRelationTypes", filter.IamPermList}),
			},
		},
		endpoint.ApiSingle{
			Verb:    "GET",
			Path:    "/console/relation",
			Handler: server.WrapRestRouteFunc(r.list),
			Filters: []restful.FilterFunction{
				filter.IAMPermissionCheck(api.Resource, "", []string{"cfc:ListRelations", filter.IamPermList}),
				filter.CheckFunctionExist,
			},
		},
		endpoint.ApiSingle{
			Verb:    "POST",
			Path:    "/console/relation",
			Handler: apiServer.WrapRestRouteFunc(r.create),
			Filters: []restful.FilterFunction{
				filter.IAMPermissionCheck(api.Resource, "", []string{"cfc:CreateRelation", filter.IamPermWrite}),
				filter.CheckFunctionExist,
				filter.CheckTriggerCondition,
			},
		},
		endpoint.ApiSingle{
			Verb:    "DELETE",
			Path:    "/console/relation",
			Handler: apiServer.WrapRestRouteFunc(r.delete),
			Filters: []restful.FilterFunction{
				filter.IAMPermissionCheck(api.Resource, "", []string{"cfc:DeleteRelation", filter.IamPermWrite}),
				filter.CheckFunctionExist,
			},
		},
		endpoint.ApiSingle{
			Verb:    "PUT",
			Path:    "/console/relation",
			Handler: server.WrapRestRouteFunc(r.update),
			Filters: []restful.FilterFunction{
				filter.IAMPermissionCheck(api.Resource, "", []string{"cfc:UpdateRelation", filter.IamPermWrite}),
				filter.CheckFunctionExist,
				filter.CheckTriggerCondition,
			},
		},
		endpoint.ApiSingle{
			Verb:    "GET",
			Path:    "/console/relation/domains",
			Handler: server.WrapRestRouteFunc(r.listDomains),
			Filters: []restful.FilterFunction{
				filter.IAMPermissionCheck(api.Resource, "", []string{"cfc:ListCdnDomains", filter.IamPermList}),
			},
		},
		endpoint.ApiSingle{
			Verb:    "GET",
			Path:    "/console/relation/groups",
			Handler: server.WrapRestRouteFunc(r.listGroups),
			Filters: []restful.FilterFunction{
				filter.IAMPermissionCheck(api.Resource, "", []string{"cfc:ListAPIGatewayGroups", filter.IamPermList}),
			},
		},
		endpoint.ApiSingle{
			Verb:    "PUT",
			Path:    "/console/relation/check",
			Handler: apiServer.WrapRestRouteFunc(r.check),
			Filters: []restful.FilterFunction{
				filter.IAMPermissionCheck(api.Resource, "", []string{"cfc:ListAPIGatewayGroups", filter.IamPermList}),
				filter.CheckFunctionExist,
				filter.CheckTriggerCondition,
			},
		},
	}
	return apis
}

// PublicAPIs xxx
func (r RelationRest) PublicAPIs() []endpoint.ApiSingle {
	apis := []endpoint.ApiSingle{
		endpoint.ApiSingle{
			Verb:    "GET",
			Path:    "/relation",
			Handler: server.WrapRestRouteFunc(r.list),
			Filters: []restful.FilterFunction{
				filter.IAMPermissionCheck(api.Resource, "", []string{"cfc:ListRelations", filter.IamPermList}),
				filter.CheckFunctionExist,
			},
		},
		endpoint.ApiSingle{
			Verb:    "POST",
			Path:    "/relation",
			Handler: apiServer.WrapRestRouteFunc(r.create),
			Filters: []restful.FilterFunction{
				filter.IAMPermissionCheck(api.Resource, "", []string{"cfc:CreateRelation", filter.IamPermWrite}),
				filter.CheckFunctionExist,
				filter.CheckTriggerCondition,
			},
		},
		endpoint.ApiSingle{
			Verb:    "DELETE",
			Path:    "/relation",
			Handler: apiServer.WrapRestRouteFunc(r.delete),
			Filters: []restful.FilterFunction{
				filter.IAMPermissionCheck(api.Resource, "", []string{"cfc:DeleteRelation", filter.IamPermWrite}),
				filter.CheckFunctionExist,
			},
		},
		endpoint.ApiSingle{
			Verb:    "PUT",
			Path:    "/relation",
			Handler: server.WrapRestRouteFunc(r.update),
			Filters: []restful.FilterFunction{
				filter.IAMPermissionCheck(api.Resource, "", []string{"cfc:UpdateRelation", filter.IamPermWrite}),
				filter.CheckFunctionExist,
				filter.CheckTriggerCondition,
			},
		},
	}
	return apis
}
