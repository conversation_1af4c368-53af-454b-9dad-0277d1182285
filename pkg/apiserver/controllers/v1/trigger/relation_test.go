package trigger

import (
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/global"
	"testing"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/stretchr/testify/assert"
	"icode.baidu.com/baidu/faas/kun/pkg/api"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/code"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/dbengine"
	apiServer "icode.baidu.com/baidu/faas/kun/pkg/apiserver/server"
	"icode.baidu.com/baidu/faas/kun/pkg/server"
	trigger "icode.baidu.com/baidu/faas/kun/pkg/trigger/relation"
)

func TestConsoleAPIS(t *testing.T) {
	r := RelationRest{}
	r.ConsoleAPIs()
}
func TestListRelationType(t *testing.T){
	global.MockAC()

	cases := []struct {
		in_c         *server.Context
		out_HttpCode int
	}{
		{
			in_c:         global.BuildNewKunCtx("GET", "/v1/console/relation/type", ``, "userID1", map[string]string{}),
			out_HttpCode: 200,
		},
	}

	f := RelationRest{}
	for _, tc := range cases {
		f.listType(tc.in_c)
		assert.Equal(t, tc.out_HttpCode, tc.in_c.Response().StatusCode())
	}
}

func TestListRelation(t *testing.T) {
	registerTrigger()

	var m sqlmock.Sqlmock
	m, dbengine.Engine = global.MockDB()
	m.ExpectQuery(".*").WillReturnRows(global.GetRowsPolicy(global.GetTestPolicy(3)))

	cases := []struct {
		in_c         *server.Context
		out_HttpCode int
	}{
		{
			in_c:         global.BuildNewKunCtx("GET", "/v1/console/relation?FunctionBrn=brn:bce:cfc:bj:640c8817bd1de2928d47256dd0620ce5:function:boshello:$LATEST", ``, "", map[string]string{}),
			out_HttpCode: 200,
		},
	}

	f := RelationRest{}
	for _, tc := range cases {
		f.list(tc.in_c)
		assert.Equal(t, tc.out_HttpCode, tc.in_c.Response().StatusCode())
	}
}

func TestDeleteRelation(t *testing.T) {
	registerTrigger()
	code.MockCode()

	var m sqlmock.Sqlmock
	m, dbengine.Engine = global.MockDB()
	m.ExpectBegin()
	m.ExpectQuery(".*").WillReturnRows(global.GetRowsPolicy(global.GetTestPolicy(1)))
	m.ExpectExec(".*").WillReturnResult(sqlmock.NewResult(1, 1))
	m.ExpectCommit()

	cases := []struct {
		in_c         *apiServer.ApiServerContext
		out_HttpCode int
	}{
		{
			in_c:         global.BuildNewApiServerCtx("DELETE", "/v1/console/relation?RelationId=1234567&Target=brn:bce:cfc:bj:640c8817bd1de2928d47256dd0620ce5:function:hello:$LATEST&Source=dueros", "", "", map[string]string{}),
			out_HttpCode: 204,
		},
		{
			in_c:         global.BuildNewApiServerCtx("DELETE", "/v1/console/relation?RelationId=1234567&Target=brn:bce:cfc:bj:640c8817bd1de2928d47256dd0620ce5:function:hello:$LATEST&Source=cfc-http-trigger/v1/CFCAPIInvalid", "", "", map[string]string{}),
			out_HttpCode: 400,
		},
		{
			in_c:         global.BuildNewApiServerCtx("DELETE", "/v1/console/relation?RelationId=1234567&Target=brn:bce:cfc:bj:640c8817bd1de2928d47256dd0620ce5:function:hello:$LATEST&Source=boss", "", "", map[string]string{}),
			out_HttpCode: 400,
		},
	}

	f := RelationRest{}
	for _, tc := range cases {
		f.delete(tc.in_c)
		assert.Equal(t, tc.out_HttpCode, tc.in_c.Response().StatusCode())
	}
}

func TestUpdateRelation(t *testing.T) {
	code.MockCode()
	registerTrigger()

	cases := []struct {
		in_c         *server.Context
		out_HttpCode int
	}{
		{
			in_c:         global.BuildNewKunCtx("PUT", "/v1/console/relation", `{"RelationId":"1234567", "Target":"brn:bce:cfc:bj:640c8817bd1de2928d47256dd0620ce5:function:hello:$LATEST", "Source":"dueros","Data":{"Status":"enabled","EventType":["PutObject"],"Prefix":["/json1"],"Suffix":[".ppf"]}}`, "", map[string]string{}),
			out_HttpCode: 200,
		},
	}

	cases[0].in_c.Request().SetAttribute("relationReq", &api.Relation{
		Target: "brn:bce:cfc:bj:640c8817bd1de2928d47256dd0620ce5:function:hello:$LATEST",
		Source: "dueros",
	})

	f := RelationRest{}
	for _, tc := range cases {
		f.update(tc.in_c)
		assert.Equal(t, tc.out_HttpCode, tc.in_c.Response().StatusCode())
	}
}
func TestCreateRelation(t *testing.T) {
	code.MockCode()
	registerTrigger()

	var m sqlmock.Sqlmock
	m, dbengine.Engine = global.MockDB()
	m.ExpectBegin()
	m.ExpectQuery(".*").WillReturnRows(global.GetRowsPolicy(global.GetTestPolicy(0)))
	m.ExpectExec(".*").WillReturnResult(sqlmock.NewResult(1, 1))
	m.ExpectCommit()

	cases := []struct {
		in_c         *apiServer.ApiServerContext
		out_HttpCode int
	}{
		{
			in_c:         global.BuildNewApiServerCtx("POST", "/v1/console/relation", `{"Target":"brn:bce:cfc:bj:640c8817bd1de2928d47256dd0620ce5:function:hello:$LATEST", "Source":"dueros"}`, "uid", map[string]string{}),
			out_HttpCode: 201,
		},
	}

	cases[0].in_c.Request().SetAttribute("relationReq", &api.Relation{
		Target: "brn:bce:cfc:bj:640c8817bd1de2928d47256dd0620ce5:function:hello:$LATEST",
		Source: "dueros",
	})

	f := RelationRest{}
	for _, tc := range cases {
		f.create(tc.in_c)
		assert.Equal(t, tc.out_HttpCode, tc.in_c.Response().StatusCode())
	}
}

func TestCheckRelation(t *testing.T) {
	code.MockCode()
	registerTrigger()
	_, dbengine.Engine = global.MockDB()

	cases := []struct{
		in_c         *apiServer.ApiServerContext
		out_HttpCode int
	}{
		{
			in_c:         global.BuildNewApiServerCtx("PUT", "/v1/console/relation/check", `{"Target":"brn:bce:cfc:bj:640c8817bd1de2928d47256dd0620ce5:function:hello:$LATEST", "Source":"cfc-http-trigger/v1/CFCAPI"}`, "uid", map[string]string{}),
			out_HttpCode: 400,
		},
		{
			in_c:         global.BuildNewApiServerCtx("PUT", "/v1/console/relation/check", `{"Target":"brn:bce:cfc:bj:640c8817bd1de2928d47256dd0620ce5:function:hello:$LATEST", "dueros"}`, "uid", map[string]string{}),
			out_HttpCode: 200,
		},
	}

	cases[0].in_c.Request().SetAttribute("relationReq", &api.Relation{
		Target: "brn:bce:cfc:bj:640c8817bd1de2928d47256dd0620ce5:function:hello:$LATEST",
		Source: "cfc-http-trigger/v1/CFCAPI",
	})
	cases[1].in_c.Request().SetAttribute("relationReq", &api.Relation{
		Target: "brn:bce:cfc:bj:640c8817bd1de2928d47256dd0620ce5:function:hello:$LATEST",
		Source:"dueros",
	})

	f := RelationRest{}
	for _, tc := range cases {
		f.check(tc.in_c)
		assert.Equal(t, tc.out_HttpCode, tc.in_c.Response().StatusCode())
	}
}

func registerTrigger() {
	trigger.RegisterTrigger(trigger.NewBosTrigger())
	trigger.RegisterTrigger(trigger.NewDuerosTrigger())
}

func TestGetScopeTriggerMap(t *testing.T) {
	getScopeTriggerMap(true)
}

func TestParseScopeType(t *testing.T) {
	cases := []struct {
		rule string
		out  map[api.TriggerType]bool
		err  error
	}{
		{
			rule: "",
			out: map[api.TriggerType]bool{
				api.TriggerTypeApiGateway: true,
				api.TriggerTypeHTTP:       true,
				api.TriggerTypeBos:        true,
				api.TriggerTypeDuerOS:     true,
				api.TriggerTypeDuEdge:     true,
				api.TriggerTypeCdn:        true,
				api.TriggerTypeCfcEdge:    false,
				api.TriggerTypeCrontab:    true,
				api.TriggerTypeGeneric:    true,
			},
			err: nil,
		},
		{
			rule: "all",
			out: map[api.TriggerType]bool{
				api.TriggerTypeApiGateway: true,
				api.TriggerTypeHTTP:       true,
				api.TriggerTypeBos:        true,
				api.TriggerTypeDuerOS:     true,
				api.TriggerTypeDuEdge:     true,
				api.TriggerTypeCdn:        true,
				api.TriggerTypeCfcEdge:    true,
				api.TriggerTypeCrontab:    true,
				api.TriggerTypeGeneric:    true,
			},
			err: nil,
		},
		{
			rule: "!cfc-edge",
			out: map[api.TriggerType]bool{
				api.TriggerTypeApiGateway: true,
				api.TriggerTypeHTTP:       true,
				api.TriggerTypeBos:        true,
				api.TriggerTypeDuerOS:     true,
				api.TriggerTypeDuEdge:     true,
				api.TriggerTypeCdn:        true,
				api.TriggerTypeCfcEdge:    false,
				api.TriggerTypeCrontab:    true,
				api.TriggerTypeGeneric:    true,
			},
			err: nil,
		},
		{
			rule: "dueros",
			out: map[api.TriggerType]bool{
				api.TriggerTypeApiGateway: false,
				api.TriggerTypeHTTP:       false,
				api.TriggerTypeBos:        false,
				api.TriggerTypeDuerOS:     true,
				api.TriggerTypeDuEdge:     false,
				api.TriggerTypeCdn:        false,
				api.TriggerTypeCfcEdge:    false,
				api.TriggerTypeCrontab:    false,
				api.TriggerTypeGeneric:    false,
			},
			err: nil,
		},
		{
			rule: "(cdn,bos)",
			out: map[api.TriggerType]bool{
				api.TriggerTypeApiGateway: false,
				api.TriggerTypeHTTP:       false,
				api.TriggerTypeBos:        true,
				api.TriggerTypeDuerOS:     false,
				api.TriggerTypeDuEdge:     false,
				api.TriggerTypeCdn:        true,
				api.TriggerTypeCfcEdge:    false,
				api.TriggerTypeCrontab:    false,
				api.TriggerTypeGeneric:    false,
			},
			err: nil,
		},
		{
			rule: "!(bos,duedge)",
			out: map[api.TriggerType]bool{
				api.TriggerTypeApiGateway: true,
				api.TriggerTypeHTTP:       true,
				api.TriggerTypeBos:        false,
				api.TriggerTypeDuerOS:     true,
				api.TriggerTypeDuEdge:     false,
				api.TriggerTypeCdn:        true,
				api.TriggerTypeCfcEdge:    true,
				api.TriggerTypeCrontab:    true,
				api.TriggerTypeGeneric:    true,
			},
			err: nil,
		},
		{
			rule: "!(bos,duedge",
			out:  nil,
			err:  InvalidScopeType,
		},
		{
			rule: "!(bos,#duedge)",
			out:  nil,
			err:  InvalidScopeType,
		},
		{
			rule: "!(bos,(duedge)",
			out:  nil,
			err:  InvalidScopeType,
		},
		{
			rule: "(bos,(duedge),cdn)",
			out:  nil,
			err:  InvalidScopeType,
		},
	}
	for _, tc := range cases {
		res, err := parseScopeType(tc.rule)
		if err != tc.err {
			t.Errorf("input %s, expect err %v, but got err %v", tc.rule, tc.err, err)
		}
		for key, expect := range tc.out {
			if v, ok := res[key]; ok {
				if v != expect {
					t.Errorf("input %s, expect res %v, but got %v", tc.rule, tc.out, res)
				}
			} else {
				t.Errorf("input %s, expect res %v, but got %v", tc.rule, tc.out, res)
			}
		}
	}
}

func TestIsValidBracketsRule(t *testing.T) {
	cases := []struct {
		rule string
		res  bool
	}{
		{
			rule: "(test,oh-yes)",
			res:  true,
		},
		{
			rule: "test",
			res:  false,
		},
		{
			rule: "(test#,oh-no)",
			res:  false,
		},
		{
			rule: "(test),oh-no)",
			res:  false,
		},
		{
			rule: "((test),oh-no)",
			res:  false,
		},
		{
			rule: "(test,oh-no",
			res:  false,
		},
	}
	for _, tc := range cases {
		res := isValidBracketsRule(tc.rule)
		if res != tc.res {
			t.Errorf("input %s, expect res %v, but got %v", tc.rule, tc.res, res)
		}
	}
}
