package trigger

import (
	"fmt"
	"github.com/DATA-DOG/go-sqlmock"
	"github.com/stretchr/testify/assert"
	"icode.baidu.com/baidu/faas/kun/pkg/api"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/dbengine"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/global"
	apiServer "icode.baidu.com/baidu/faas/kun/pkg/apiserver/server"
	"icode.baidu.com/baidu/faas/kun/pkg/server"
	"icode.baidu.com/baidu/faas/kun/pkg/trigger/relation"
	"testing"
)

func TestPolicyAPIS(t *testing.T) {
	r := PolicyRest{}
	r.PublicAPIs()
}

func TestInsideList(t *testing.T) {
	global.MockAC()

	var m sqlmock.Sqlmock
	m, dbengine.Engine = global.MockDB()

	cases := []struct {
		in_c         *server.Context
		out_HttpCode int
		fc           func(*server.Context)
	}{
		{
			in_c: global.BuildNewKunCtx("GET", "/v1/functions/hello/policy", ``, "uiduid", map[string]string{
				"FunctionBrn": "brn:bce:cfc:bj:640c8817bd1de2928d47256dd0620ce5:function:test:$LATEST",
			}),
			out_HttpCode: 400,
			fc:           func(c *server.Context) {},
		},
		{
			in_c: global.BuildNewKunCtx("GET", "/v1/functions/hello/policy", ``, "c7ac82ae14ef42d1a4ffa3b2ececa17f", map[string]string{
				"FunctionBrn": "brn:bce:cfc:bj:640c8817bd1de2928d47256dd0620ce5:function:test:alias1",
			}),
			out_HttpCode: 200,
			fc: func(c *server.Context) {
				trigger := relation.NewDuerosTrigger()
				relation.RegisterTrigger(trigger)
				c.Request().Request.Header.Add(api.BceFaasTriggerKey, "dueros")
				m.ExpectQuery(".*").WillReturnRows(global.GetRowsPolicy(global.GetTestPolicy(1)))
			},
		},
	}

	f := PolicyRest{}
	for _, tc := range cases {
		tc.fc(tc.in_c)
		f.insideList(tc.in_c)
		assert.Equal(t, tc.out_HttpCode, tc.in_c.Response().StatusCode())
	}
}

func TestListPolicy(t *testing.T) {
	global.MockAC()

	var m sqlmock.Sqlmock
	m, dbengine.Engine = global.MockDB()

	cases := []struct {
		in_c         *server.Context
		out_HttpCode int
		fc           func()
	}{
		{
			in_c: global.BuildNewKunCtx("GET", "/v1/functions/hello/policy", ``, "uiduid", map[string]string{
				"FunctionName": "hello",
			}),
			out_HttpCode: 200,
			fc: func() {
				m.ExpectQuery(".*").WillReturnRows(global.GetRowsPolicy(global.GetTestPolicy(1)))
				m.ExpectQuery(".*").WillReturnRows(global.GetRowsFunctions(global.GetTestFunc(1)))
			},
		},
		{
			in_c: global.BuildNewKunCtx("GET", "/v1/functions/hello/policy", ``, "c7ac82ae14ef42d1a4ffa3b2ececa17f", map[string]string{
				"FunctionName": "brn:bce:cfc:bj:640c8817bd1de2928d47256dd0620ce5:function:test:alias1",
			}),
			out_HttpCode: 200,
			fc: func() {
				m.ExpectQuery(".*").WillReturnRows(global.GetRowsAliases(global.GetTestAlias(1)))
				m.ExpectQuery(".*").WillReturnRows(global.GetRowsAliases(global.GetTestAlias(1)))
				m.ExpectQuery(".*").WillReturnRows(global.GetRowsPolicy(global.GetTestPolicy(1)))
			},
		},
	}

	f := PolicyRest{}
	for _, tc := range cases {
		tc.fc()
		f.list(tc.in_c)
		assert.Equal(t, tc.out_HttpCode, tc.in_c.Response().StatusCode())
	}
}

func TestDeletePolicy(t *testing.T) {
	global.MockAC()

	var m sqlmock.Sqlmock
	m, dbengine.Engine = global.MockDB()
	m.ExpectQuery(".*").WillReturnRows(global.GetRowsFunctions(global.GetTestFunc(1)))
	m.ExpectQuery(".*").WillReturnRows(global.GetRowsPolicy(global.GetTestPolicy(1)))
	m.ExpectBegin()
	m.ExpectQuery(".*").WillReturnRows(global.GetRowsPolicy(global.GetTestPolicy(1)))
	m.ExpectExec(".*").WillReturnResult(sqlmock.NewResult(1, 1))
	m.ExpectCommit()

	cases := []struct {
		in_c         *apiServer.ApiServerContext
		out_HttpCode int
	}{
		{
			in_c: global.BuildNewApiServerCtx("DELETE", "/v1/functions/hello/policy", ``, "uiduid", map[string]string{
				"FunctionName": "hello",
				"StatementId":  "1234567",
			}),
			out_HttpCode: 204,
		},
	}

	f := PolicyRest{}
	for _, tc := range cases {
		f.delete(tc.in_c)
		assert.Equal(t, tc.out_HttpCode, tc.in_c.Response().StatusCode())
	}
}

func TestCreatePolicy(t *testing.T) {
	global.MockAC()
	registerTrigger()

	var m sqlmock.Sqlmock
	m, dbengine.Engine = global.MockDB()
	m.ExpectQuery(".*").WillReturnRows(global.GetRowsFunctions(global.GetTestFunc(1)))
	m.ExpectQuery(".*").WillReturnRows(global.GetRowsPolicy(global.GetTestPolicy(0)))
	m.ExpectExec(".*").WillReturnResult(sqlmock.NewResult(1, 1))

	cases := []struct {
		in_c         *server.Context
		out_HttpCode int
	}{
		{
			in_c: global.BuildNewKunCtx("POST", "/v1/functions/hello/policy", `{"Principal":"bos.baidubce.com", "StatementId":"1234567","Action":"cfc:InvokeFunction","SourceAccount":"12345678912345678912345678900000"}`, "uiduid", map[string]string{
				"FunctionName": "hello",
			}),
			out_HttpCode: 201,
		},
		{
			in_c: global.BuildNewKunCtx("POST", "/v1/functions/hello/policy", `{"Principal":"abc.baidubce.com", "StatementId":"1234567","Action":"cfc:InvokeFunction","SourceAccount":"12345678912345678912345678900000"}`, "uiduid", map[string]string{
				"FunctionName": "hello",
			}),
			out_HttpCode: 400,
		},
		{
			in_c: global.BuildNewKunCtx("POST", "/v1/functions/hello/policy", `{"Principal":"", "StatementId":"1234567","Action":"cfc:InvokeFunction","SourceAccount":"12345678912345678912345678900000"}`, "", map[string]string{
				"FunctionName": "hello",
			}),
			out_HttpCode: 400,
		},
		{
			in_c: global.BuildNewKunCtx("POST", "/v1/functions/hello/policy", `{"RevisionId": "0000-1111-2222-3333","Principal":"bos.baidubce.com", "StatementId":"1234567","Action":"cfc:InvokeFunction","SourceAccount":"12345678912345678912345678900000"}`, "", map[string]string{
				"FunctionName": "hello",
			}),
			out_HttpCode: 400,
		},
	}

	f := PolicyRest{}
	for _, tc := range cases {
		f.create(tc.in_c)
		assert.Equal(t, tc.out_HttpCode, tc.in_c.Response().StatusCode())
	}
}

func TestUpdateApiGateway(t *testing.T) {
	global.MockAC()
	registerTrigger()

	var m sqlmock.Sqlmock
	m, dbengine.Engine = global.MockDB()
	m.ExpectQuery(".*").WillReturnRows(global.GetRowsFunctions(global.GetTestFunc(1)))
	m.ExpectQuery(".*").WillReturnRows(global.GetRowsPolicy(global.GetApiGatewayPolicy(1)))
	m.ExpectExec(".*").WillReturnResult(sqlmock.NewResult(1, 1))

	m.ExpectQuery(".*").WillReturnRows(global.GetRowsFunctions(global.GetTestFunc(1)))
	m.ExpectQuery(".*").WillReturnRows(global.GetRowsPolicy(global.GetApiGatewayPolicy(0)))

	m.ExpectQuery(".*").WillReturnRows(global.GetRowsFunctions(global.GetTestFunc(0)))

	cases := []struct {
		in_c         *apiServer.ApiServerContext
		out_HttpCode int
	}{
		{
			in_c: global.BuildNewApiServerCtx("PUT", "/v1/functions/hello/policy", `{"NewBrn": "brn:bce:cfc:bj:640c8817bd1de2928d47256dd0620ce5:function:test0308:$LATEST","Source": "api-gateway/GWGP-LqsMHhpNwET/test"}`, "uiduid", map[string]string{
				"FunctionName": "hello",
			}),
			out_HttpCode: 200,
		},
		{
			in_c: global.BuildNewApiServerCtx("PUT", "/v1/functions/hello/policy", `{"NewBrn": "brn:bce:cfc:bj:640c8817bd1de2928d47256dd0620ce5:function:test0308:$LATEST","Source": "api-gateway/GWGP-LqsMHhpNwET/test"}`, "uiduid", map[string]string{
				"FunctionName": "hello",
			}),
			out_HttpCode: 404,
		},
		{
			in_c: global.BuildNewApiServerCtx("PUT", "/v1/functions/hello/policy", `{"NewBrn": "brn:bce:cfc:bj:640c8817bd1de2928d47256dd0620ce5:function:test0308:$LATEST","Source": "api-gateway/GWGP-LqsMHhpNwET/test"}`, "", map[string]string{
				"FunctionName": "hello",
			}),
			out_HttpCode: 400,
		},
	}

	f := PolicyRest{}
	for i, tc := range cases {
		fmt.Println(i)
		f.updateApiGateway(tc.in_c)
		assert.Equal(t, tc.out_HttpCode, tc.in_c.Response().StatusCode())
	}
}

func TestDeleteApiGateway(t *testing.T) {
	global.MockAC()

	var m sqlmock.Sqlmock
	m, dbengine.Engine = global.MockDB()
	m.ExpectQuery(".*").WillReturnRows(global.GetRowsFunctions(global.GetTestFunc(1)))
	m.ExpectQuery(".*").WillReturnRows(global.GetRowsPolicy(global.GetTestPolicy(1)))
	m.ExpectBegin()
	m.ExpectQuery(".*").WillReturnRows(global.GetRowsPolicy(global.GetTestPolicy(1)))
	m.ExpectExec(".*").WillReturnResult(sqlmock.NewResult(1, 1))
	m.ExpectCommit()

	cases := []struct {
		in_c         *apiServer.ApiServerContext
		out_HttpCode int
	}{
		{
			in_c: global.BuildNewApiServerCtx("DELETE", "/v1/functions/hello/policy?GroupId=GWGP-gahfajttJwg&ApiName=apitrigger1234", ``, "uiduid", map[string]string{
				"FunctionName": "hello",
				"StatementId":  "1234567",
			}),
			out_HttpCode: 204,
		},
	}

	f := PolicyRest{}
	for _, tc := range cases {
		f.deleteApiGateway(tc.in_c)
		assert.Equal(t, tc.out_HttpCode, tc.in_c.Response().StatusCode())
	}
}
