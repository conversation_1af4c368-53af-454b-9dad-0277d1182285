package trigger

import (
	"testing"

	"github.com/stretchr/testify/assert"

	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/global"
	"icode.baidu.com/baidu/faas/kun/pkg/server"
)

func TestListGroups(t *testing.T) {
	global.MockAC()

	cases := []struct {
		in_c         *server.Context
		out_HttpCode int
	}{
		{
			in_c:         global.BuildNewKunCtx("GET", "/v1/console/relation/groups", ``, "uiduid", map[string]string{}),
			out_HttpCode: 200,
		},
		{
			in_c:         global.BuildNewKunCtx("GET", "/v1/console/relation/groups", ``, "", map[string]string{}),
			out_HttpCode: 400,
		},
		{
			in_c:         global.BuildNewKunCtx("GET", "/v1/console/relation/groups", ``, "errorUid", map[string]string{}),
			out_HttpCode: 200,
		},
	}

	f := RelationRest{}
	for _, tc := range cases {
		f.listGroups(tc.in_c)
		assert.Equal(t, tc.out_HttpCode, tc.in_c.Response().StatusCode())
	}
}
