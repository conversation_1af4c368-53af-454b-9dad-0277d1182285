package trigger

import (
	"errors"
	"net/http"
	"strings"

	"github.com/emicklei/go-restful"
	"icode.baidu.com/baidu/faas/kun/pkg/api"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/controllers/v1/cloudfunc"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/dao"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/dbengine"
	apiErr "icode.baidu.com/baidu/faas/kun/pkg/apiserver/error"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/global"
	apiServer "icode.baidu.com/baidu/faas/kun/pkg/apiserver/server"
	"icode.baidu.com/baidu/faas/kun/pkg/bce/brn"
	kunErr "icode.baidu.com/baidu/faas/kun/pkg/error"
	"icode.baidu.com/baidu/faas/kun/pkg/middleware"
	"icode.baidu.com/baidu/faas/kun/pkg/server"
	"icode.baidu.com/baidu/faas/kun/pkg/server/endpoint"
	trigger "icode.baidu.com/baidu/faas/kun/pkg/trigger/relation"
	"icode.baidu.com/baidu/faas/kun/pkg/util/json"
)

// PolicyRest xxx
type PolicyRest struct {
	Path string
}

// InsideAPIs 向eventhub返回要校验的policy
func (r PolicyRest) InsideAPIs() []endpoint.ApiSingle {
	apis := []endpoint.ApiSingle{
		endpoint.ApiSingle{
			Verb:    "GET",
			Path:    "/functions/{FunctionBrn}/policy",
			Handler: server.WrapRestRouteFunc(r.insideList),
			Filters: []restful.FilterFunction{
				middleware.SimpleAuthTokenVerify(),
			},
		},
	}
	return apis
}

func (r PolicyRest) insideList(c *server.Context) {
	response := c.Response()

	t := c.Request().HeaderParameter(api.BceFaasTriggerKey)
	funcBrn := c.Request().PathParameter("FunctionBrn")

	triggerInterface := trigger.GetTriggerInterface(api.TriggerType(t))
	if triggerInterface == nil || funcBrn == "" {
		c.WithWarnLog(apiErr.NewMissingParametersException("trigger or function brn is empty", nil)).WriteTo(response)
		return
	}

	po := &dao.Policy{
		Resource:         funcBrn,
		PrincipalService: string(triggerInterface.GetPrincipalService()),
	}

	policySlice, rkunErr := dao.ListPolicies(po)
	if rkunErr != nil {
		c.WithWarnLog(rkunErr).WriteTo(response)
		return
	}

	response.WriteHeaderAndEntity(http.StatusOK, map[string]interface{}{
		"Condition": policySlice,
	})
}

func (r PolicyRest) list(c *server.Context) {
	response := c.Response()

	ctx := global.BuildApiserverContext(c.Request(), c.Response(), "policy_list")
	function, err := getFunction(c.Request(), ctx)
	if err != nil {
		c.WithWarnLog(err).WriteTo(response)
		return
	}

	ctx.Context.FunctionBrn = function.FunctionBrn

	po := &dao.Policy{
		Resource: function.FunctionBrn,
	}

	var (
		policysTmp  interface{}
		policySlice *[]dao.Policy
	)

	policysTmp, err = ctx.Observer.NewStage(global.ListPolicyStage).ObserveObject(dao.ListPolicies(po))
	policySlice = policysTmp.(*[]dao.Policy)
	if err != nil || len(*policySlice) == 0 {
		c.WithWarnLog(apiErr.NewResourceNotFoundException("policy not found", err)).WriteTo(response)
		return
	}

	var retSlice []map[string]interface{}
	for index := range *policySlice {
		(*policySlice)[index].DealPolicy()
		ret := dealPolicyOutput(&(*policySlice)[index])
		retSlice = append(retSlice, *ret)
	}

	defer global.WriteSummary(ctx, api.LogNotToSummary)
	statementByte, _ := json.Marshal(map[string]interface{}{
		"Version":   "v1",
		"Id":        "default",
		"Statement": retSlice,
	})

	response.WriteHeaderAndEntity(http.StatusOK, map[string]interface{}{
		"Policy":     string(statementByte),
		"RevisionId": function.CommitID,
	})
}

func (r *PolicyRest) create(c *server.Context) {
	response := c.Response()
	ctx := global.BuildApiserverContext(c.Request(), c.Response(), "policy_create")
	policyReq := new(dao.Policy)
	if err := c.Request().ReadEntity(&policyReq); err != nil {
		c.WithWarnLog(kunErr.NewInvalidRequestContentException("", err)).WriteTo(response)
		return
	}

	if err := ctx.Observer.NewStage(global.CheckPolicyStage).Observe(checkPolicyInput(policyReq)); err != nil {
		c.WithWarnLog(kunErr.NewInvalidParameterValueException("invalid input parameter", err)).WriteTo(response)
		return
	}

	function, err := getFunction(c.Request(), ctx)
	if err != nil {
		c.WithWarnLog(err).WriteTo(response)
		return
	}

	ctx.Context.FunctionBrn = function.FunctionBrn

	// 检查 StatementId 是否重复
	repeatedPolicy := &dao.Policy{
		StatementId: policyReq.StatementId,
		Resource:    function.FunctionBrn,
	}

	var (
		policysTmp  interface{}
		policySlice *[]dao.Policy
	)

	policysTmp, err = ctx.Observer.NewStage(global.ListPolicyStage).ObserveObject(dao.ListPolicies(repeatedPolicy))
	policySlice = policysTmp.(*[]dao.Policy)
	if err != nil || len(*policySlice) != 0 {
		c.WithWarnLog(apiErr.NewResourceConflictException("StatementId repeated", err)).WriteTo(response)
		return
	}

	// RevisionId 用于保证基于最新的函数改动创建 policy
	if policyReq.RevisionId != "" && policyReq.RevisionId != *function.CommitID {
		c.WithWarnLog(apiErr.NewResourceConflictException("", nil)).WriteTo(response)
		return
	}

	policyReq.Resource = function.FunctionBrn
	policyReq.Effect = api.PolicyActionAllow
	policyReq.FunctionUid = function.Uid
	policyReq.FunctionName = function.FunctionName

	if rkunErr := ctx.Observer.NewStage(global.CreatePolicyStage).Observe(dao.CreatePolicy(policyReq, nil)); rkunErr != nil {
		c.WithWarnLog(kunErr.NewServiceException("create policy failed", rkunErr)).WriteTo(response)
		return
	}

	policyReq.DealPolicy()
	statementByte, _ := json.Marshal(dealPolicyOutput(policyReq))

	defer global.WriteSummary(ctx, api.LogNotToSummary)
	response.WriteHeaderAndEntity(http.StatusCreated, map[string]interface{}{
		"Statement": string(statementByte),
	})
}

func (r *PolicyRest) updateApiGateway(c *apiServer.ApiServerContext) {
	response := c.Response()
	ctx := global.BuildApiserverContext(c.Request(), c.Response(), "policy_update")
	updateFunctionPolicy := new(api.UpdateFunctionPolicy)
	if err := c.Request().ReadEntity(&updateFunctionPolicy); err != nil {
		c.WithWarnLog(kunErr.NewInvalidRequestContentException("", err)).WriteTo(response)
		return
	}

	function, err := getFunction(c.Request(), ctx)
	if err != nil {
		c.WithWarnLog(err).WriteTo(response)
		return
	}

	ctx.Context.FunctionBrn = function.FunctionBrn

	// 检查触发源是否存在
	policy := &dao.Policy{
		Source:   updateFunctionPolicy.OldSource,
		Resource: function.FunctionBrn,
	}

	var (
		policysTmp  interface{}
		policySlice *[]dao.Policy
	)

	policysTmp, err = ctx.Observer.NewStage(global.ListPolicyStage).ObserveObject(dao.ListPolicies(policy))
	policySlice = policysTmp.(*[]dao.Policy)
	if err != nil || len(*policySlice) == 0 {
		c.WithWarnLog(apiErr.NewResourceNotFoundException("Policy not exist", err)).WriteTo(response)
		return
	}
	updatePolicy := new(dao.Policy)
	updatePolicy.Source = updateFunctionPolicy.NewSource
	updatePolicy.Resource = updateFunctionPolicy.NewBrn
	brn := strings.Split(updatePolicy.Resource, ":")
	if len(brn) != 8 {
		c.WithWarnLog(apiErr.NewResourceNotFoundException("illegal brn", err)).WriteTo(response)
		return
	}
	updatePolicy.FunctionName = brn[6]

	if rkunErr := ctx.Observer.NewStage(global.CreatePolicyStage).Observe(dao.UpdatePolicy((*policySlice)[0], updatePolicy, c.DbTransaction)); rkunErr != nil {
		c.WithWarnLog(kunErr.NewServiceException("update policy failed", rkunErr)).WriteTo(response)
		return
	}

	defer global.WriteSummary(ctx, api.LogNotToSummary)
	response.WriteHeaderAndEntity(200, map[string]interface{}{
		"Status": "success",
	})
}

func (r *PolicyRest) deleteApiGateway(c *apiServer.ApiServerContext) {

	ctx := global.BuildApiserverContext(c.Request(), c.Response(), "policy_delete")
	function, err := getFunction(c.Request(), ctx)
	if err != nil {
		c.WithWarnLog(err).WriteTo(c.Response())
		return
	}
	groupId := c.Request().QueryParameter("GroupId")
	apiName := c.Request().QueryParameter("ApiName")
	if groupId != "" && apiName != "" {
		ctx.Context.FunctionBrn = function.FunctionBrn
	} else {
		c.WithWarnLog(apiErr.NewMissingParametersException("groupId or apiName cannot be empty", nil)).WriteTo(c.Response())
		return
	}

	plcy := &dao.Policy{
		Resource: function.FunctionBrn,
		Source:   "api-gateway/" + groupId + "/" + apiName,
	}

	if rkunErr := ctx.Observer.NewStage(global.FindOnePolicyStage).Observe(dao.FindOnePolicy(plcy)); rkunErr != nil {
		c.WithWarnLog(rkunErr).WriteTo(c.Response())
		return
	}

	c.DbTransaction = dbengine.DBTransaction()
	if rkunErr := ctx.Observer.NewStage(global.DeletePolicyStage).Observe(dao.DeletePolicy(plcy, c.DbTransaction)); rkunErr != nil {
		c.DbTransaction.Rollback()
		c.WithWarnLog(rkunErr).WriteTo(c.Response())
		return
	}
	if err := c.DbTransaction.Commit().Error; err != nil {
		c.WithErrorLog(kunErr.NewServiceException("delete function failed because of commit failure", err)).WriteTo(c.Response())
		return
	}

	defer global.WriteSummary(ctx, api.LogNotToSummary)
	c.Response().WriteHeaderAndEntity(http.StatusNoContent, nil)
}
func (r *PolicyRest) delete(c *apiServer.ApiServerContext) {
	sid := c.Request().PathParameter("StatementId")
	if sid == "" {
		c.WithWarnLog(apiErr.NewMissingParametersException("statement-id cannot be empty", nil)).WriteTo(c.Response())
		return
	}

	ctx := global.BuildApiserverContext(c.Request(), c.Response(), "policy_delete")
	function, err := getFunction(c.Request(), ctx)
	if err != nil {
		c.WithWarnLog(err).WriteTo(c.Response())
		return
	}

	ctx.Context.FunctionBrn = function.FunctionBrn

	plcy := &dao.Policy{
		Resource:    function.FunctionBrn,
		StatementId: sid,
	}

	if rkunErr := ctx.Observer.NewStage(global.FindOnePolicyStage).Observe(dao.FindOnePolicy(plcy)); rkunErr != nil {
		c.WithWarnLog(rkunErr).WriteTo(c.Response())
		return
	}

	c.DbTransaction = dbengine.DBTransaction()
	if rkunErr := ctx.Observer.NewStage(global.DeletePolicyStage).Observe(dao.DeletePolicy(plcy, c.DbTransaction)); rkunErr != nil {
		c.DbTransaction.Rollback()
		c.WithWarnLog(rkunErr).WriteTo(c.Response())
		return
	}
	if err := c.DbTransaction.Commit().Error; err != nil {
		c.WithErrorLog(kunErr.NewServiceException("delete function failed because of commit failure", err)).WriteTo(c.Response())
		return
	}

	defer global.WriteSummary(ctx, api.LogNotToSummary)
	c.Response().WriteHeaderAndEntity(http.StatusNoContent, nil)
}

func getFunction(request *restful.Request, ctx *global.ApiserverContext) (*dao.Function, error) {
	var (
		funcTmp  interface{}
		function *dao.Function
		err      error
	)
	if funcTmp, err = ctx.Observer.NewStage(global.InitFunctionStage).ObserveObject(cloudfunc.InitFunction(request)); err != nil {
		return nil, apiErr.NewInitFuncMetaException(err.Error(), "", err)
	}

	alias, isAlias := "", false

	function = funcTmp.(*dao.Function)
	if function.Version == "" {
		function.Version = "$LATEST"
	} else {
		// 上面逻辑未出错说明别名/版本检查无误，下面若解析到别名存在，就说明目的版本是别名
		hashedAccountID := brn.Md5BceUid(function.Uid)
		qualifier := request.QueryParameter("Qualifier")
		_, _, alias, _ = brn.DealFName(hashedAccountID, request.PathParameter("FunctionName"), "")

		if alias != "" {
			isAlias = true
		} else if qualifier != "" {
			t, _ := brn.JudgeQualifier(qualifier)
			if t == "alias" {
				isAlias = true
				alias = qualifier
			}
		}
	}

	if isAlias == true {
		function.FunctionBrn = brn.GenerateFuncBrnString(function.Region, function.Uid, function.FunctionName, alias)
		if err = ctx.Observer.NewStage(global.CheckFunctionStage).Observe(dao.FindOneAlias(&dao.Alias{
			AliasBrn: function.FunctionBrn,
		})); err != nil {
			return nil, apiErr.NewResourceNotFoundException("alias not found", err)
		}

	} else {
		function.FunctionBrnInit()
		if err = ctx.Observer.NewStage(global.CheckFunctionStage).Observe(dao.FindOneFunc(function)); err != nil {
			return nil, apiErr.NewResourceNotFoundException("function not found", err)
		}
	}

	return function, nil
}

// dealPolicyOutput 使用 lambda 的返回结构
func dealPolicyOutput(po *dao.Policy) *map[string]interface{} {
	ret := map[string]interface{}{
		"Sid":       po.StatementId,
		"Effect":    po.Effect,
		"Principal": po.PrincipalMap,
		"Action":    po.Action,
		"Resource":  po.Resource,
		"Condition": po.ConditionMap,
	}
	return &ret
}

func checkPolicyInput(p *dao.Policy) error {
	if err := verifyPolicyCommonParam(p); err != nil {
		return err
	}

	tgr := trigger.GetTriggerInterfaceFromService(api.PrincipalServiceType(p.PrincipalService))
	if tgr == nil {
		return errors.New("invalid principal service")
	}

	return tgr.VerifyInputPolicySource(p.Source)
}

func verifyPolicyCommonParam(p *dao.Policy) error {
	if p.PrincipalService == "" || p.StatementId == "" || p.Action == "" {
		return errors.New("principal, statement-id and action cann't be empty")
	}

	if r := api.RegularCommonAction.FindStringSubmatch(p.Action); r == nil {
		return errors.New("action failed to satisfy constraint, Member must satisfy regular expression pattern: cfc:[*]|cfc:[a-zA-Z]+|[*]")
	}

	if p.SourceAccount != "" {
		if r := api.RegularSourceAccount.FindStringSubmatch(p.SourceAccount); r == nil {
			return errors.New("sourceAccount failed to satisfy constraint, Member must satisfy regular expression pattern: [a-z0-9]{32}")
		}
	}

	// 兼容lambda sourceArn 字段
	if p.SourceArn != "" && p.Source == "" {
		p.Source = p.SourceArn
	}
	return nil
}
