package controllers

import (
	"fmt"
	"io/ioutil"
	"net/http"
	"strconv"
	"strings"

	"github.com/emicklei/go-restful"

	"icode.baidu.com/baidu/faas/kun/cmd/apiserver/filter"
	"icode.baidu.com/baidu/faas/kun/pkg/api"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/controllers/v1/cloudfunc"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/dao"
	apiErr "icode.baidu.com/baidu/faas/kun/pkg/apiserver/error"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/global"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/models"
	resp "icode.baidu.com/baidu/faas/kun/pkg/apiserver/models/function"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/service"
	kunErr "icode.baidu.com/baidu/faas/kun/pkg/error"
	"icode.baidu.com/baidu/faas/kun/pkg/middleware"
	"icode.baidu.com/baidu/faas/kun/pkg/rocketmq"
	"icode.baidu.com/baidu/faas/kun/pkg/server"
	"icode.baidu.com/baidu/faas/kun/pkg/server/endpoint"
	"icode.baidu.com/baidu/faas/kun/pkg/util/crypto"
	"icode.baidu.com/baidu/faas/kun/pkg/util/json"
)

type EventSourceRest struct {
	Path string
}

// APIs xxx
func (r EventSourceRest) APIs() []endpoint.ApiSingle {
	apis := []endpoint.ApiSingle{
		// find event-source-mappings by id, POST /2015-03-31/event-source-mappings/{UUID}
		{
			Verb:    "GET",
			Path:    "/event-source-mappings/{UUID}",
			Handler: server.WrapRestRouteFunc(r.find),
			Filters: []restful.FilterFunction{
				filter.IAMPermissionCheck(api.Resource, "", []string{"cfc:GetEventSourceMapping", filter.IamPermRead}),
			},
		},
		// update event-source-mappings by id, PUT /2015-03-31/event-source-mappings/{UUID}
		{
			Verb:    "PUT",
			Path:    "/event-source-mappings/{UUID}",
			Handler: server.WrapRestRouteFunc(r.update),
			Filters: []restful.FilterFunction{
				filter.IAMPermissionCheck(api.Resource, "", []string{"cfc:UpdateEventSourceMapping", filter.IamPermWrite}),
			},
		},
		// delete event-source-mappings by id, DELETE /2015-03-31/event-source-mappings/{UUID}
		{
			Verb:    "DELETE",
			Path:    "/event-source-mappings/{UUID}",
			Handler: server.WrapRestRouteFunc(r.delete),
			Filters: []restful.FilterFunction{
				filter.IAMPermissionCheck(api.Resource, "", []string{"cfc:DeleteEventSourceMapping", filter.IamPermWrite}),
			},
		},
		// create event-source-mappings, POST /2015-03-31/event-source-mappings
		{
			Verb:    "POST",
			Path:    "/event-source-mappings",
			Handler: server.WrapRestRouteFunc(r.create),
			Filters: []restful.FilterFunction{
				filter.IAMPermissionCheck(api.Resource, "", []string{"cfc:CreateEventSourceMapping", filter.IamPermWrite}),
			},
		},
		// list event-source-mappings, POST /2015-03-31/event-source-mappings
		{
			Verb:    "GET",
			Path:    "/event-source-mappings",
			Handler: server.WrapRestRouteFunc(r.findCondition),
			Filters: []restful.FilterFunction{
				filter.IAMPermissionCheck(api.Resource, "", []string{"cfc:ListEventSourceMappings", filter.IamPermList}),
			},
		},
		{
			Verb:    "GET",
			Path:    "/kafka/topic/list",
			Handler: server.WrapRestRouteFunc(r.getKafkaTopicList),
			Filters: []restful.FilterFunction{
				filter.IAMPermissionCheck(api.Resource, "", []string{"cfc:getKafkaTopicList", filter.IamPermList}),
			},
		},
		{
			Verb:    "GET",
			Path:    "/kafka/{clusterID}/topic/list",
			Handler: server.WrapRestRouteFunc(r.getKafkaTopicList),
			Filters: []restful.FilterFunction{
				filter.IAMPermissionCheck(api.Resource, "", []string{"cfc:getKafkaExclusiveTopicList", filter.IamPermList}),
			},
		},
		{
			Verb:    "GET",
			Path:    "/kafka/clusters/list",
			Handler: server.WrapRestRouteFunc(r.getKafkaClustersList),
			Filters: []restful.FilterFunction{
				filter.IAMPermissionCheck(api.Resource, "", []string{"cfc:getKafkaClustersList", filter.IamPermList}),
			},
		},
		{
			Verb:    "GET",
			Path:    "/kafka/cluster/{clusterID}",
			Handler: server.WrapRestRouteFunc(r.getKafkaCluster),
			Filters: []restful.FilterFunction{
				filter.IAMPermissionCheck(api.Resource, "", []string{"cfc:getKafkaCluster", filter.IamPermList}),
			},
		},
		{
			Verb:    "GET",
			Path:    "/rocketmq/clusters/list",
			Handler: server.WrapRestRouteFunc(r.getRocketmqClustersList),
			Filters: []restful.FilterFunction{
				filter.IAMPermissionCheck(api.Resource, "", []string{"cfc:getRocketmqClustersList", filter.IamPermList}),
			},
		},
		{
			Verb:    "GET",
			Path:    "/rocketmq/{clusterID}/topics",
			Handler: server.WrapRestRouteFunc(r.getRocketmqTopics),
			Filters: []restful.FilterFunction{
				filter.IAMPermissionCheck(api.Resource, "", []string{"cfc:getRocketmqTopics", filter.IamPermList}),
			},
		},
		{
			Verb:    "GET",
			Path:    "/rocketmq/{clusterID}/consumer-groups",
			Handler: server.WrapRestRouteFunc(r.getRocketmqConsumerGroups),
			Filters: []restful.FilterFunction{
				filter.IAMPermissionCheck(api.Resource, "", []string{"cfc:getRocketmqConsumerGroups", filter.IamPermList}),
			},
		},
	}
	return apis
}

// InsideAPIs xxx
func (r EventSourceRest) InsideAPIs() []endpoint.ApiSingle {
	apis := []endpoint.ApiSingle{
		// get invite link, GET /inside-v1/functions/{FunctionName}
		{
			Verb:    "PUT",
			Path:    "/event-source-mappings/disable/{UUID}",
			Handler: server.WrapRestRouteFunc(r.insideDisableAndSetReason),
			Filters: []restful.FilterFunction{
				middleware.SimpleAuthTokenVerify(),
			},
		},
		{
			Verb:    "GET",
			Path:    "/kafka/cluster/{clusterID}/{UID}/intranet-endpoints",
			Handler: server.WrapRestRouteFunc(r.getIntranetEndpoints),
			Filters: []restful.FilterFunction{
				middleware.SimpleAuthTokenVerify(),
			},
		},
		{
			Verb:    "GET",
			Path:    "/rocketmq/cluster/{clusterID}/{UID}",
			Handler: server.WrapRestRouteFunc(r.getRocketmqCluster),
			Filters: []restful.FilterFunction{
				middleware.SimpleAuthTokenVerify(),
			},
		},
		{
			Verb:    "GET",
			Path:    "/rocketmq/cluster/{clusterID}/{UID}/access-endpoints",
			Handler: server.WrapRestRouteFunc(r.getRocketmqEndpoint),
			Filters: []restful.FilterFunction{
				middleware.SimpleAuthTokenVerify(),
			},
		},
		{
			Verb:    "GET",
			Path:    "/rocketmq/cluster/{clusterID}/{UID}/consumer-groups/{groupName}",
			Handler: server.WrapRestRouteFunc(r.getRocketmqConsumerGroup),
			Filters: []restful.FilterFunction{
				middleware.SimpleAuthTokenVerify(),
			},
		},
	}
	return apis
}

type EventSourceUpdateReq struct {
	UUID                      string
	BatchSize                 int
	Enabled                   bool
	EventSourceBrn            string
	EventSourceArn            string
	Type                      string
	FunctionName              string
	StartingPosition          string
	StartingPositionTimestamp int64
	dao.DatahubConfig
}

// create create 函数用于创建事件源，参数为一个指向 server.Context 类型的指针，返回值不定义。
func (r *EventSourceRest) create(c *server.Context) {
	response := c.Response()
	user, err := cloudfunc.GetUser(c.Request())
	if err != nil {
		e := apiErr.NewUserNotFoundException("", err)
		c.WithErrorLog(e).WriteTo(response)
		return
	}

	a := &dao.FuncEventSource{}
	s, _ := ioutil.ReadAll(c.Request().Request.Body)

	if err = json.Unmarshal(s, a); err != nil {
		c.WithWarnLog(kunErr.NewInvalidRequestContentException("", err)).WriteTo(response)
		return
	}

	// 修正：rocketmq 类型最大 batch size 限制为 32
	maxBatchSize := 1000
	if a.Type == api.TypeEventSourceRocketmq {
		maxBatchSize = 32
	}
	if a.BatchSize < 1 || a.BatchSize > maxBatchSize {
		respErr := kunErr.NewInvalidParameterValueException("BatchSize is invalid", nil)
		resp.NewFailResp(respErr).WriteTo(response, respErr)
		return
	}

	if a.Type == api.TypeEventSourceExclusiveKafka {
		if len(a.ClusterID) == 0 {
			respErr := kunErr.NewInvalidParameterValueException("ClusterID is empty", nil)
			resp.NewFailResp(respErr).WriteTo(response, respErr)
			return
		}

		cluster, err := global.AC.Clients.BmsClient.GetCluster(user.Domain.ID, a.ClusterID)
		if err != nil {
			respErr := c.WithErrorLog(err)
			resp.NewFailResp(respErr).WriteTo(response, respErr)
			return
		}

		// 检查一下当前用户创建topic的cluster是否开启了kafka 产品间转储
		if !cluster.IntranetIpEnabled {
			respErr := kunErr.NewInvalidParameterValueException("IntranetIpEnabled was not open", nil)
			resp.NewFailResp(respErr).WriteTo(response, respErr)
			return
		}
	} else if a.Type == api.TypeEventSourceRocketmq {
		defer api.PanicCatcher()

		// *检查集群 acl 是否开启
		cluster, err := global.AC.Clients.RocketmqClient.GetClusterDetail(user.Domain.ID, a.ClusterID)
		if err != nil {
			c.Logger().Infof("[rocketmq] GetClusterDetail failed: %v", err)
			respErr := c.WithErrorLog(err)
			c.WithErrorLog(respErr).WriteTo(response)
			// resp.NewFailResp(respErr).WriteTo(response, respErr)
			return
		}
		if clusterBytes, err := json.Marshal(cluster); err == nil {
			c.Logger().Infof("[rocketmq] ClusterID: %s, Cluster: %s", a.ClusterID, string(clusterBytes))
		} else {
			c.Logger().Infof("[rocketmq] ClusterID: %s, Cluster: %+v (marshal failed: %v)", a.ClusterID, cluster, err)
		}

		// 新增：判断 intranetIpEnabled
		if cluster != nil && cluster.Provisioned != nil && !cluster.Provisioned.IntranetAccessEnabled {
			c.Logger().Infof("[rocketmq] ClusterID: %s, IntranetAccessEnabled was not open", a.ClusterID)
			respErr := apiErr.NewRocketmqNoIntranetAccessException("rocketmq 集群没有开通内网访问，请提交工单开通", nil)
			c.WithErrorLog(respErr).WriteTo(response)
			// resp.NewFailResp(respErr).WriteTo(response, respErr)
			return
		}
		if cluster == nil || cluster.Provisioned == nil || !cluster.Provisioned.AclEnabled {
			c.Logger().Infof("[rocketmq] ClusterID: %s, AclEnabled was not open", a.ClusterID)
		}

		// * 检查 topic、consumergroup 是否存在
		// EventSourceBrn 是 topic
		topic, err := global.AC.Clients.RocketmqClient.GetTopicDetail(user.Domain.ID, a.ClusterID, a.EventSourceBrn)
		if err != nil {
			c.Logger().Infof("[rocketmq] GetTopicDetail failed: %v", err)
			respErr := c.WithErrorLog(err)
			c.WithErrorLog(respErr).WriteTo(response)
			// resp.NewFailResp(respErr).WriteTo(response, respErr)
			return
		}
		if topicBytes, err := json.Marshal(topic); err == nil {
			c.Logger().Infof("[rocketmq] EventSourceBrn: %s, Topic: %s", a.EventSourceBrn, string(topicBytes))
		} else {
			c.Logger().Infof("[rocketmq] EventSourceBrn: %s, Topic: %+v (marshal failed: %v)", a.EventSourceBrn, topic, err)
		}

		cg, err := global.AC.Clients.RocketmqClient.GetConsumerGroupDetail(user.Domain.ID, a.ClusterID, a.ConsumerGroupId)
		if err != nil {
			c.Logger().Infof("[rocketmq] GetConsumerGroupDetail failed: %v", err)
			respErr := c.WithErrorLog(err)
			c.WithErrorLog(respErr).WriteTo(response)
			// resp.NewFailResp(respErr).WriteTo(response, respErr)
			return
		}
		c.Logger().Infof("[rocketmq] ConsumerGroupId: %s, ConsumerGroup: %+v", a.ConsumerGroupId, cg)

		// 新增逻辑：如果 StartingPosition=="LATEST"，查询消费组消费进度，若有分区为0则重置为最新
		if strings.ToUpper(a.StartingPosition) == "LATEST" {
			c.Logger().Infof("[rocketmq] QueryConsumerOffset: clusterID=%s, group=%s, topic=%s", a.ClusterID, a.ConsumerGroupId, a.EventSourceBrn)
			consumeStates, err := global.AC.Clients.RocketmqClient.QueryConsumerOffset(user.Domain.ID, a.ClusterID, a.ConsumerGroupId, a.EventSourceBrn)
			if err != nil {
				c.Logger().Warnf("[rocketmq] QueryConsumerOffset failed: %v", err)
				respErr := c.WithErrorLog(err)
				c.WithErrorLog(respErr).WriteTo(response)
				// resp.NewFailResp(respErr).WriteTo(response, respErr)
				return
			} else {
				c.Logger().Infof("[rocketmq] QueryConsumerOffset result: %+v", consumeStates)
				if len(consumeStates) == 0 {
					// 新消费组，consumeStates 为空，需先初始化 offset
					// 查询集群详情，判断 enableTLS
					enableTLS := false
					if err == nil && cluster != nil && cluster.Provisioned != nil && cluster.Provisioned.EncryptionInTransit != nil {
						for _, encryption := range cluster.Provisioned.EncryptionInTransit {
							if encryption == "SSL" {
								enableTLS = true
								break
							}
						}
					}
					c.Logger().Infof("[rocketmq] enableTLS for cluster %s: %v", a.ClusterID, enableTLS)
					// 查询 endpoint
					endpoint, err := global.AC.Clients.RocketmqClient.GetClusterEndPoint(user.Domain.ID, a.ClusterID)
					if err != nil {
						c.Logger().Warnf("[rocketmq] GetClusterEndPoint failed: %v", err)
						respErr := c.WithErrorLog(err)
						c.WithErrorLog(respErr).WriteTo(response)
						// resp.NewFailResp(respErr).WriteTo(response, respErr)
						return
					} else {
						protocol := "http"
						if enableTLS {
							protocol = "https"
						}
						brokers := []string{fmt.Sprintf("%s://%s", protocol, endpoint.Endpoint)}
						c.Logger().Infof("[rocketmq] EnsureGroupTopicOffsetInitialized: brokers=%v, group=%s, topic=%s, tls=%v", brokers, a.ConsumerGroupId, a.EventSourceBrn, enableTLS)
						err := rocketmq.EnsureGroupTopicOffsetInitialized(brokers, a.ConsumerGroupId, a.EventSourceBrn, a.UserName, a.Password, enableTLS)
						if err != nil {
							c.Logger().Warnf("[rocketmq] EnsureGroupTopicOffsetInitialized failed: %v", err)
							respErr := c.WithErrorLog(err)
							c.WithErrorLog(respErr).WriteTo(response)
							// resp.NewFailResp(respErr).WriteTo(response, respErr)
							return
						} else {
							c.Logger().Infof("[rocketmq] EnsureGroupTopicOffsetInitialized success for group %s topic %s", a.ConsumerGroupId, a.EventSourceBrn)
						}
						// 再 resetOffset
						ts := int64(-1)
						c.Logger().Infof("[rocketmq] ResetOffset: clusterID=%s, group=%s, topic=%s, timestamp=%d", a.ClusterID, a.ConsumerGroupId, a.EventSourceBrn, ts)
						err = global.AC.Clients.RocketmqClient.ResetOffset(user.Domain.ID, a.ClusterID, a.ConsumerGroupId, a.EventSourceBrn, ts)
						if err != nil {
							c.Logger().Warnf("[rocketmq] ResetOffset failed: %v", err)
							respErr := c.WithErrorLog(err)
							c.WithErrorLog(respErr).WriteTo(response)
							// resp.NewFailResp(respErr).WriteTo(response, respErr)
							return
						} else {
							c.Logger().Infof("[rocketmq] ResetOffset to latest for group %s topic %s success", a.ConsumerGroupId, a.EventSourceBrn)
						}
					}
				}
			}
		}

		// 用户名和密码可以是空的
		passwordCipher, err := crypto.EncryptToBase64([]byte(a.Password))
		if err != nil {
			respErr := kunErr.NewServiceException("Encrypt Password failed", err)
			c.WithErrorLog(respErr).WriteTo(response)
			// resp.NewFailResp(respErr).WriteTo(response, respErr)
			return
		}
		a.Password = passwordCipher
	}

	workspaceID := c.Request().Request.Header.Get(api.HeaderWorkspaceID)
	a.WorkspaceID = workspaceID
	if kerr := service.CreateOneEventSource(a, user); kerr != nil {
		c.WithErrorLog(kerr).WriteTo(response)
		return
	}
	a.UserName = ""
	a.Password = ""

	response.WriteHeaderAndEntity(http.StatusCreated, a)
}

// findCondition 查找条件，包括用户、函数名称等，返回事件源映射列表
// 参数c：*server.Context类型，请求上下文对象
func (r *EventSourceRest) findCondition(c *server.Context) {
	response := c.Response()
	markerStr := c.Request().QueryParameter("Marker")
	maxItemStr := c.Request().QueryParameter("MaxItems")
	functionName := c.Request().QueryParameter("FunctionName")

	user, err := cloudfunc.GetUser(c.Request())
	// err != nil会panic
	if err != nil {
		c.WithWarnLog(apiErr.NewUserNotFoundException("", err)).WriteTo(response)
		return
	}
	workspaceID := c.Request().Request.Header.Get(api.HeaderWorkspaceID)
	f, err := models.InitUserFunc(user.Domain.ID, functionName, "", workspaceID)
	if err != nil {
		c.WithErrorLog(apiErr.NewInitFuncMetaException(err.Error(), "", nil)).WriteTo(response)
		return
	}
	if f.Version == "" {
		f.Version = "$LATEST"
	}
	f.FunctionBrnInit()

	cond := &dao.FuncEventSource{}
	if functionName == "" {
		cond.Uid = user.Domain.ID
	} else {
		cond.FunctionBrn = f.FunctionBrn
	}

	db, count := dao.GetEventSourceCount(cond)

	var marker, maxItem int64
	var resMap = make(map[string]interface{})
	funcEventSourceSlice := make([]dao.FuncEventSource, 0)

	if markerStr != "" {
		marker, _ = strconv.ParseInt(markerStr, 10, 64)
		db = db.Offset(int(marker))
	}
	if maxItemStr != "" {
		maxItem, _ = strconv.ParseInt(maxItemStr, 10, 64)
		db = db.Limit(int(maxItem))

		if count > (maxItem + marker) {
			resMap["NextMarker"] = strconv.FormatInt((maxItem + marker), 10)
		}
	}

	db.Find(&funcEventSourceSlice)

	ml := make([]interface{}, 0)
	for _, v := range funcEventSourceSlice {
		v.DealEventSource()
		// 先判断认证模式
		authMode := ""
		if v.Type == api.TypeEventSourceRocketmq {
			if v.UserName != "" && v.Password != "" {
				authMode = "acl"
			} else {
				authMode = "none"
			}
		}
		v.UserName = ""
		v.Password = ""
		service.GetEventSourceLastProcessingResult(&v)
		item := map[string]interface{}{}
		b, _ := json.Marshal(v)
		_ = json.Unmarshal(b, &item)
		item["AuthMode"] = authMode
		ml = append(ml, item)
	}

	resMap["EventSourceMappings"] = ml
	response.WriteHeaderAndEntity(http.StatusOK, resMap)
}

// find find 函数用于查找事件源，参数为指向 server.Context 类型的指针，返回值不存在
func (r *EventSourceRest) find(c *server.Context) {
	response := c.Response()
	uuid := c.Request().PathParameter("UUID")
	a := &dao.FuncEventSource{}
	a.Uuid = uuid

	if kerr := dao.FindOneEventSource(a); kerr != nil {
		c.WithErrorLog(kerr).WriteTo(response)
		return
	}
	a.DealEventSource()
	// 先判断认证模式
	authMode := ""
	if a.Type == api.TypeEventSourceRocketmq {
		if a.UserName != "" && a.Password != "" {
			authMode = "acl"
		} else {
			authMode = "none"
		}
	}
	a.UserName = ""
	a.Password = ""
	service.GetEventSourceLastProcessingResult(a)
	item := map[string]interface{}{}
	b, _ := json.Marshal(a)
	_ = json.Unmarshal(b, &item)
	item["AuthMode"] = authMode
	response.WriteHeaderAndEntity(http.StatusOK, item)
}

// update update 更新事件源信息
//
// 参数：
//
//	c (*server.Context) - Context类型，请求上下文
//
// 返回值：
//
//	无
//
// 错误：
//
//	UserNotFoundException - 用户不存在异常
//	InvalidParameterValueException - 参数值无效异常
//	InvalidRequestContentException - 请求内容无效异常
//	KunServerError - 服务端错误
//
// 更新事件源信息，包括批量大小、是否启用等信息。
func (r *EventSourceRest) update(c *server.Context) {
	response := c.Response()
	user, err := cloudfunc.GetUser(c.Request())
	if err != nil {
		c.WithWarnLog(apiErr.NewUserNotFoundException("", err)).WriteTo(response)
		return
	}

	cond := &dao.FuncEventSource{}
	cond.Uuid = c.Request().PathParameter("UUID")
	cond.Uid = user.Domain.ID

	tmpEventSource := &dao.FuncEventSource{}
	update := &dao.FuncEventSource{}
	if err := c.Request().ReadEntity(tmpEventSource); err != nil {
		c.WithWarnLog(kunErr.NewInvalidRequestContentException("", err)).WriteTo(response)
		return
	}
	// 修正：rocketmq 类型最大 batch size 限制为 32
	maxBatchSize := 1000
	if tmpEventSource.Type == api.TypeEventSourceRocketmq {
		maxBatchSize = 32
	}
	if tmpEventSource.BatchSize < 1 || tmpEventSource.BatchSize > maxBatchSize {
		c.WithWarnLog(kunErr.NewInvalidParameterValueException("BatchSize is invalid", err)).WriteTo(response)
		return
	}
	update.BatchSize = tmpEventSource.BatchSize
	update.Enabled = tmpEventSource.Enabled
	update.FunctionName = tmpEventSource.FunctionName
	// add datahub config
	update.DatahubConfig = tmpEventSource.DatahubConfig
	update.EventSourceBrn = tmpEventSource.EventSourceBrn

	workspaceID := c.Request().Request.Header.Get(api.HeaderWorkspaceID)
	update.WorkspaceID = workspaceID

	find, kerr := service.UpdateEventSource(cond, update, user)
	if kerr != nil {
		c.WithErrorLog(kerr).WriteTo(response)
		return
	}
	find.DealEventSource()
	find.UserName = ""
	find.Password = ""
	service.GetEventSourceLastProcessingResult(find)

	response.WriteHeaderAndEntity(http.StatusOK, find)
}

func (r *EventSourceRest) delete(c *server.Context) {
	response := c.Response()
	uuid := c.Request().PathParameter("UUID")
	user, err := cloudfunc.GetUser(c.Request())
	if err != nil {
		e := apiErr.NewUserNotFoundException("", err)
		c.WithErrorLog(e).WriteTo(response)
		return
	}
	a := &dao.FuncEventSource{}
	a.Uuid = uuid
	a.Uid = user.Domain.ID

	if rKunErr := service.DeleteOneEventSource(a); rKunErr != nil {
		c.WithErrorLog(rKunErr).WriteTo(response)
		return
	}
	service.DelEventSourceLastProcessingResult(a.Uuid)
	response.WriteHeaderAndEntity(http.StatusNoContent, nil)
}

func (r *EventSourceRest) insideDisableAndSetReason(c *server.Context) {
	response := c.Response()
	cond := &dao.FuncEventSource{}
	cond.Uuid = c.Request().PathParameter("UUID")

	tmpEventSource := &dao.FuncEventSource{}
	update := &dao.FuncEventSource{}
	if err := c.Request().ReadEntity(tmpEventSource); err != nil {
		c.WithWarnLog(kunErr.NewInvalidRequestContentException("", err)).WriteTo(response)
		return
	}
	enable := false
	update.Enabled = &enable
	update.StateTransitionReason = tmpEventSource.StateTransitionReason

	find, kerr := service.InsideDisableAndSetReason(cond, update)
	if kerr != nil {
		c.WithErrorLog(kerr).WriteTo(response)
		return
	}
	find.DealEventSource()
	service.GetEventSourceLastProcessingResult(find)
	response.WriteHeaderAndEntity(http.StatusOK, find)
}

func (r *EventSourceRest) getKafkaTopicList(c *server.Context) {
	response := c.Response()
	clusterID := c.Request().PathParameter("clusterID")
	strings.TrimSpace(clusterID)
	user, err := cloudfunc.GetUser(c.Request())
	if err != nil {
		c.WithWarnLog(apiErr.NewUserNotFoundException("", err)).WriteTo(response)
		return
	}
	userID := user.Domain.ID
	topicList, err := global.AC.Clients.BmsClient.ListTopic(userID, clusterID)
	if err != nil {
		respErr := c.WithErrorLog(err)
		resp.NewFailResp(respErr).WriteTo(response, respErr)
		return
	}
	response.WriteHeaderAndEntity(http.StatusOK, resp.NewSuccessResp(topicList))
}

func (r *EventSourceRest) getKafkaCluster(c *server.Context) {
	response := c.Response()
	user, err := cloudfunc.GetUser(c.Request())
	if err != nil {
		c.WithWarnLog(apiErr.NewUserNotFoundException("", err)).WriteTo(response)
		return
	}
	userID := user.Domain.ID
	clusterID := c.Request().PathParameter("clusterID")
	cluster, err := global.AC.Clients.BmsClient.GetCluster(userID, clusterID)
	if err != nil {
		respErr := c.WithErrorLog(err)
		resp.NewFailResp(respErr).WriteTo(response, respErr)
		return
	}
	response.WriteHeaderAndEntity(http.StatusOK, resp.NewSuccessResp(cluster))
}

func (r *EventSourceRest) getKafkaClustersList(c *server.Context) {
	response := c.Response()
	user, err := cloudfunc.GetUser(c.Request())
	if err != nil {
		c.WithWarnLog(apiErr.NewUserNotFoundException("", err)).WriteTo(response)
		return
	}
	userID := user.Domain.ID
	clustersList, err := global.AC.Clients.BmsClient.ListClusters(userID)
	if err != nil {
		respErr := c.WithErrorLog(err)
		resp.NewFailResp(respErr).WriteTo(response, respErr)
		return
	}
	response.WriteHeaderAndEntity(http.StatusOK, resp.NewSuccessResp(clustersList))
}

func (r *EventSourceRest) getIntranetEndpoints(c *server.Context) {
	response := c.Response()
	clusterID := c.Request().PathParameter("clusterID")
	userID := c.Request().PathParameter("UID")
	intranetEndpoints, err := global.AC.Clients.BmsClient.GetClusterEndPoint(userID, clusterID)
	if err != nil {
		respErr := c.WithErrorLog(err)
		resp.NewFailResp(respErr).WriteTo(response, respErr)
		return
	}
	response.WriteHeaderAndEntity(http.StatusOK, resp.NewSuccessResp(intranetEndpoints.IntranetEndpoints.KafkaEndpoints))
}

// getRocketmqCluster 获取 RocketMQ 集群详情，参数为一个 server.Context 类型的指针，返回值为 void
func (r *EventSourceRest) getRocketmqCluster(c *server.Context) {
	response := c.Response()
	clusterID := c.Request().PathParameter("clusterID")
	userID := c.Request().PathParameter("UID")
	cluster, err := global.AC.Clients.RocketmqClient.GetClusterDetail(userID, clusterID)
	if err != nil {
		respErr := c.WithErrorLog(err)
		resp.NewFailResp(respErr).WriteTo(response, respErr)
		return
	}
	response.WriteHeaderAndEntity(http.StatusOK, cluster)
}

// getRocketmqEndpoint 获取Rocketmq集群的端点，并返回给客户端
// 参数c：*server.Context类型，包含了请求和响应相关信息
func (r *EventSourceRest) getRocketmqEndpoint(c *server.Context) {
	response := c.Response()
	clusterID := c.Request().PathParameter("clusterID")
	userID := c.Request().PathParameter("UID")
	endpoint, err := global.AC.Clients.RocketmqClient.GetClusterEndPoint(userID, clusterID)
	if err != nil {
		respErr := c.WithErrorLog(err)
		resp.NewFailResp(respErr).WriteTo(response, respErr)
		return
	}
	response.WriteHeaderAndEntity(http.StatusOK, endpoint)
}

// getRocketmqConsumerGroup 获取rocketmq消费组详情的接口函数
// 参数c：*server.Context类型，请求上下文对象
func (r *EventSourceRest) getRocketmqConsumerGroup(c *server.Context) {
	response := c.Response()
	clusterID := c.Request().PathParameter("clusterID")
	userID := c.Request().PathParameter("UID")
	groupName := c.Request().PathParameter("groupName")
	consumerGroup, err := global.AC.Clients.RocketmqClient.GetConsumerGroupDetail(userID, clusterID, groupName)
	if err != nil {
		respErr := c.WithErrorLog(err)
		resp.NewFailResp(respErr).WriteTo(response, respErr)
		return
	}
	response.WriteHeaderAndEntity(http.StatusOK, consumerGroup)
}

// 获取 RocketMQ 集群列表
func (r *EventSourceRest) getRocketmqClustersList(c *server.Context) {
	response := c.Response()
	user, err := cloudfunc.GetUser(c.Request())
	if err != nil {
		c.WithWarnLog(apiErr.NewUserNotFoundException("", err)).WriteTo(response)
		return
	}
	userID := user.Domain.ID
	clustersListResp, err := global.AC.Clients.RocketmqClient.ListClusters(userID)
	if err != nil {
		respErr := c.WithErrorLog(err)
		resp.NewFailResp(respErr).WriteTo(response, respErr)
		return
	}
	response.WriteHeaderAndEntity(http.StatusOK, resp.NewSuccessResp(clustersListResp.Clusters))
}

// 获取 RocketMQ 集群下的 topic 列表
func (r *EventSourceRest) getRocketmqTopics(c *server.Context) {
	response := c.Response()
	clusterID := c.Request().PathParameter("clusterID")
	user, err := cloudfunc.GetUser(c.Request())
	if err != nil {
		c.WithWarnLog(apiErr.NewUserNotFoundException("", err)).WriteTo(response)
		return
	}
	userID := user.Domain.ID
	topicListResp, err := global.AC.Clients.RocketmqClient.ListTopics(userID, clusterID)
	if err != nil {
		respErr := c.WithErrorLog(err)
		resp.NewFailResp(respErr).WriteTo(response, respErr)
		return
	}
	response.WriteHeaderAndEntity(http.StatusOK, resp.NewSuccessResp(topicListResp.Topics))
}

// 获取 RocketMQ 集群下的消费组列表
func (r *EventSourceRest) getRocketmqConsumerGroups(c *server.Context) {
	response := c.Response()
	clusterID := c.Request().PathParameter("clusterID")
	user, err := cloudfunc.GetUser(c.Request())
	if err != nil {
		c.WithWarnLog(apiErr.NewUserNotFoundException("", err)).WriteTo(response)
		return
	}
	userID := user.Domain.ID
	consumerGroupsResp, err := global.AC.Clients.RocketmqClient.ListConsumerGroups(userID, clusterID)
	if err != nil {
		respErr := c.WithErrorLog(err)
		resp.NewFailResp(respErr).WriteTo(response, respErr)
		return
	}
	response.WriteHeaderAndEntity(http.StatusOK, resp.NewSuccessResp(consumerGroupsResp.ConsumerGroups))
}
