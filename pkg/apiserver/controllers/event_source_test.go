package controllers

import (
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/stretchr/testify/assert"

	"icode.baidu.com/baidu/faas/kun/pkg/api"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/dao"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/dbengine"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/global"
	"icode.baidu.com/baidu/faas/kun/pkg/bce/rocketmq"
	"icode.baidu.com/baidu/faas/kun/pkg/server"
)

// TestCreateEventSource 测试函数CreateEventSource的功能，包括参数校验和数据库操作。
// 参数校验包括：
//   - FunctionName为空
//   - StartingPosition无效值（例如"LATEST1"）
//   - BatchSize无效值（例如0）
//
// 数据库操作包括：
//   - 事务开始、查询函数信息、执行插入语句、提交事务
//
// 返回值：无
func TestCreateEventSource(t *testing.T) {
	global.MockAC()
	api.EnableCheckBms = false

	cases := []struct {
		in_c         *server.Context
		out_HttpCode int
		sqlFunc      func()
	}{
		// functionName为空
		{
			in_c:         global.BuildNewKunCtx("POST", "/v1/event-source-mappings", `{"FunctionName":"","StartingPosition":"LATEST","EventSourceBrn":"c7ac82ae14ef42d1a4ffa3b2ececa17f__whx_topic","BatchSize":5,"Enabled":true}`, "uiduid", map[string]string{}),
			out_HttpCode: 400,
		},
		// StartingPosition invalid
		{
			in_c:         global.BuildNewKunCtx("POST", "/v1/event-source-mappings", `{"FunctionName":"brn:bce:cfc:bj:9c1c262dfbd8e59026414770fa02df3c:function:test0908_test-0908:$LATEST","StartingPosition":"LATEST1","EventSourceBrn":"c7ac82ae14ef42d1a4ffa3b2ececa17f__whx_topic","BatchSize":5,"Enabled":true}`, "uiduid", map[string]string{}),
			out_HttpCode: 400,
		},
		// BatchSize invalid
		{
			in_c:         global.BuildNewKunCtx("POST", "/v1/event-source-mappings", `{"FunctionName":"brn:bce:cfc:bj:9c1c262dfbd8e59026414770fa02df3c:function:test0908_test-0908:$LATEST","StartingPosition":"LATEST","EventSourceBrn":"c7ac82ae14ef42d1a4ffa3b2ececa17f__whx_topic","BatchSize":0,"Enabled":true}`, "uiduid", map[string]string{}),
			out_HttpCode: 400,
		},
		{
			in_c:         global.BuildNewKunCtx("POST", "/v1/event-source-mappings", `{"FunctionName":"brn:bce:cfc:bj:9c1c262dfbd8e59026414770fa02df3c:function:test0908_test-0908:$LATEST","StartingPosition":"LATEST","EventSourceBrn":"c7ac82ae14ef42d1a4ffa3b2ececa17f__whx_topic","BatchSize":5,"Enabled":true}`, "uiduid", map[string]string{}),
			out_HttpCode: 201,
			sqlFunc: func() {
				var m sqlmock.Sqlmock
				m, dbengine.Engine = global.MockDB()
				m.ExpectBegin()
				m.ExpectQuery(".*").WillReturnRows(global.GetRowsFunctions(global.GetTestFunc(1)))
				m.ExpectExec(".*").WillReturnResult(sqlmock.NewResult(1, 1))
				m.ExpectCommit()
			},
		},
		{
			in_c:         global.BuildNewKunCtx("POST", "/v1/event-source-mappings", `{"Enabled":true,"FunctionName":"helloyyy","EventSourceBrn":"xxx","StartingPosition":"","Type":"datahub_topic","BatchSize":1,"MetaHostEndpoint":"*************","MetaHostPort":2831,"ClusterName":"bigpipe_sandbox_new","PipeName":"dueros-tob-pipe_1","PipeletNum":1,"StartPoint":-1,"AclName":"duer-bigpipe","AclPassword":"$1$TpSQB/On$pp1ktaEg8a0ezYDLIWaMC."}`, "uiduid", map[string]string{}),
			out_HttpCode: 201,
			sqlFunc: func() {
				var m sqlmock.Sqlmock
				m, dbengine.Engine = global.MockDB()
				m.ExpectBegin()
				m.ExpectQuery(".*").WillReturnRows(global.GetRowsFunctions(global.GetTestFunc(1)))
				m.ExpectExec(".*").WillReturnResult(sqlmock.NewResult(1, 1))
				m.ExpectCommit()
			},
		},
		{
			in_c:         global.BuildNewKunCtx("POST", "/v1/event-source-mappings", `{"Enabled":true,"FunctionName":"helloyyy","EventSourceBrn":"xxx","StartingPosition":"","Type":"datahub_topic","BatchSize":1,"MetaHostEndpoint":"*************","MetaHostPort":2831,"ClusterName":"bigpipe_sandbox_new","PipeName":"dueros-tob-pipe,"PipeletNum":1000,"StartPoint":-1,"ACLName":"duer-bigpipe","ACLPassword":"$1$TpSQB/On$pp1ktaEg8a0ezYDLIWaMC."}`, "uiduid", map[string]string{}),
			out_HttpCode: 400,
			sqlFunc: func() {
				var m sqlmock.Sqlmock
				m, dbengine.Engine = global.MockDB()
				m.ExpectBegin()
				m.ExpectQuery(".*").WillReturnRows(global.GetRowsFunctions(global.GetTestFunc(1)))
				m.ExpectExec(".*").WillReturnResult(sqlmock.NewResult(1, 1))
				m.ExpectCommit()
			},
		},
		{
			in_c:         global.BuildNewKunCtx("POST", "/v1/event-source-mappings", `{"Enabled":true,"FunctionName":"helloyyy","EventSourceBrn":"xxx","StartingPosition":"","Type":"datahub_topic","BatchSize":2001,"MetaHostEndpoint":"*************","MetaHostPort":2831,"ClusterName":"bigpipe_sandbox_new","PipeName":"dueros-tob-pipe,"PipeletNum":1000,"StartPoint":-1,"ACLName":"duer-bigpipe","ACLPassword":"$1$TpSQB/On$pp1ktaEg8a0ezYDLIWaMC."}`, "uiduid", map[string]string{}),
			out_HttpCode: 400,
			sqlFunc: func() {
				var m sqlmock.Sqlmock
				m, dbengine.Engine = global.MockDB()
				m.ExpectBegin()
				m.ExpectQuery(".*").WillReturnRows(global.GetRowsFunctions(global.GetTestFunc(1)))
				m.ExpectExec(".*").WillReturnResult(sqlmock.NewResult(1, 1))
				m.ExpectCommit()
			},
		},
		{
			in_c:         global.BuildNewKunCtx("POST", "/v1/event-source-mappings", `{"FunctionName":"brn:bce:cfc:bj:9c1c262dfbd8e59026414770fa02df3c:function:test0908_test-0908:$LATEST","StartingPosition":"LATEST","EventSourceBrn":"c7ac82ae14ef42d1a4ffa3b2ececa17f__whx_topic","BatchSize":5,"Enabled":true}`, "uiduid", map[string]string{}),
			out_HttpCode: 201,
			sqlFunc: func() {
				var m sqlmock.Sqlmock
				m, dbengine.Engine = global.MockDB()
				m.ExpectBegin()
				m.ExpectQuery(".*").WillReturnRows(global.GetRowsFunctions(global.GetTestFunc(1)))
				m.ExpectExec(".*").WillReturnResult(sqlmock.NewResult(1, 1))
				m.ExpectCommit()
			},
		},
	}

	for _, tc := range cases {
		r := EventSourceRest{}
		if tc.sqlFunc != nil {
			tc.sqlFunc()
		}

		r.create(tc.in_c)
		assert.Equal(t, tc.out_HttpCode, tc.in_c.Response().StatusCode())
	}
}

// TestCreateEventSourceRocketmq 测试函数，用于创建 RocketMQ 事件源。
// 参数：
//   - t *testing.T: 必需，类型为 *testing.T，表示当前测试用例。
//
// 返回值：
//   - 无
func TestCreateEventSourceRocketmq(t *testing.T) {
	// 模拟 RocketMQ 集群详情
	cluster := &rocketmq.Cluster{
		ClusterID: "test-cluster-id",
		Name:      "test-cluster",
		Region:    "bj",
		State:     "ACTIVE",
		Provisioned: &rocketmq.Provisioned{
			AclEnabled: true,
		},
	}
	clusterDetailResponse := &rocketmq.ClusterDetailResponse{
		RequestID: "xxxx",
		Cluster:   cluster,
	}
	clusterJSON, _ := json.Marshal(clusterDetailResponse)

	// 模拟 RocketMQ topic 详情
	topic := &rocketmq.Topic{
		TopicName: "test-topic",
	}
	topicDetailResponse := &rocketmq.TopicDetailResponse{
		RequestID: "xxxx",
		Topic:     topic,
	}
	topicJSON, _ := json.Marshal(topicDetailResponse)

	// 模拟 RocketMQ consumer group 详情
	consumerGroup := &rocketmq.ConsumerGroup{
		GroupName:              "test-group",
		RetryMaxTimes:          5,
		ConsumeBroadcastEnable: true,
	}
	consumerGroupResponse := &rocketmq.ConsumerGroupResponse{
		RequestID:     "xxxx",
		ConsumerGroup: consumerGroup,
	}
	consumerGroupJSON, _ := json.Marshal(consumerGroupResponse)

	// 创建 HTTP 测试服务器
	ts := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		fmt.Println("Received request:", r.Method, r.URL.Path)
		switch r.URL.Path {
		case "/v1/clusters/rocketmq-POTvIerMW0WT0IktdI4o":
			w.WriteHeader(http.StatusOK)
			w.Write(clusterJSON)
		case "/v1/clusters/rocketmq-POTvIerMW0WT0IktdI4o/topics/test":
			w.WriteHeader(http.StatusOK)
			w.Write(topicJSON)
		case "/v1/clusters/rocketmq-POTvIerMW0WT0IktdI4o/consumer-groups/cfc":
			w.WriteHeader(http.StatusOK)
			w.Write(consumerGroupJSON)
		default:
			w.WriteHeader(http.StatusNotFound)
		}
	}))
	defer ts.Close()

	global.MockACWithRocketmq(ts.URL)

	cases := []struct {
		in_c         *server.Context
		out_HttpCode int
		sqlFunc      func()
	}{
		{
			in_c:         global.BuildNewKunCtx("POST", "/v1/event-source-mappings", `{"Type":"rocketmq","BatchSize":10,"FunctionName":"brn:bce:cfc:bj:1a2cbf55b97ac8a7c760c4177db4e17d:function:0606_rocketmq_test:$LATEST","ClusterID":"rocketmq-POTvIerMW0WT0IktdI4o","EventSourceBrn":"test","StartingPosition":"LATEST","Enabled":true,"ConsumerGroupId":"cfc","Username":"test","Password":"test"}`, "uiduid", map[string]string{}),
			out_HttpCode: 201,
			sqlFunc: func() {
				var m sqlmock.Sqlmock
				m, dbengine.Engine = global.MockDB()
				m.ExpectBegin()
				m.ExpectQuery(".*").WillReturnRows(global.GetRowsFunctions(global.GetTestFunc(1)))
				m.ExpectExec(".*").WillReturnResult(sqlmock.NewResult(1, 1))
				m.ExpectCommit()
			},
		},
	}

	for _, tc := range cases {
		r := EventSourceRest{}
		if tc.sqlFunc != nil {
			tc.sqlFunc()
		}

		r.create(tc.in_c)
		assert.Equal(t, tc.out_HttpCode, tc.in_c.Response().StatusCode())
	}
}

func TestFindEventSourceMappings(t *testing.T) {
	global.MockAC()

	cases := []struct {
		in_c         *server.Context
		out_HttpCode int
		sqlFunc      func()
	}{
		{
			in_c:         global.BuildNewKunCtx("GET", "/v1/event-source-mappings", "", "uiduid", map[string]string{}),
			out_HttpCode: 200,
			sqlFunc: func() {
				var m sqlmock.Sqlmock
				m, dbengine.Engine = global.MockDB()
				m.ExpectQuery(".*").WillReturnRows(global.GetRowsEventSource(global.GetTestEventSource(1)))
				m.ExpectQuery(".*").WillReturnRows(global.GetRowsEventSource(global.GetTestEventSource(1)))
			},
		},
	}

	for _, tc := range cases {
		r := EventSourceRest{}
		if tc.sqlFunc != nil {
			tc.sqlFunc()
		}

		r.findCondition(tc.in_c)
		assert.Equal(t, tc.out_HttpCode, tc.in_c.Response().StatusCode())
	}
}

func TestFindOneEventSource(t *testing.T) {
	global.MockAC()

	cases := []struct {
		in_c         *server.Context
		out_HttpCode int
		sqlFunc      func()
	}{
		{
			in_c:         global.BuildNewKunCtx("GET", "/v1/event-source-mappings/testeventsource", "", "uiduid", map[string]string{}),
			out_HttpCode: 200,
			sqlFunc: func() {
				var m sqlmock.Sqlmock
				m, dbengine.Engine = global.MockDB()
				m.ExpectQuery(".*").WillReturnRows(global.GetRowsEventSource(global.GetTestEventSource(1)))
			},
		},
	}

	for _, tc := range cases {
		r := EventSourceRest{}
		if tc.sqlFunc != nil {
			tc.sqlFunc()
		}

		r.find(tc.in_c)
		assert.Equal(t, tc.out_HttpCode, tc.in_c.Response().StatusCode())
	}
}

func TestUpdateEventSource(t *testing.T) {
	global.MockAC()
	api.EnableCheckBms = false

	cases := []struct {
		in_c         *server.Context
		out_HttpCode int
		sqlFunc      func()
	}{
		// BatchSize invalid
		{
			in_c:         global.BuildNewKunCtx("PUT", "/v1/event-source-mappings", `{"FunctionName":"brn:bce:cfc:bj:9c1c262dfbd8e59026414770fa02df3c:function:test0908_test-0908:$LATEST","StartingPosition":"LATEST","EventSourceBrn":"c7ac82ae14ef42d1a4ffa3b2ececa17f__whx_topic","BatchSize":0,"Enabled":true}`, "uiduid", map[string]string{}),
			out_HttpCode: 400,
		},
		{
			in_c:         global.BuildNewKunCtx("PUT", "/v1/event-source-mappings/test", `{"FunctionName":"brn:bce:cfc:bj:9c1c262dfbd8e59026414770fa02df3c:function:test0908_test-0908:$LATEST","StartingPosition":"LATEST","EventSourceBrn":"c7ac82ae14ef42d1a4ffa3b2ececa17f__whx_topic","BatchSize":5,"Enabled":true}`, "uiduid", map[string]string{}),
			out_HttpCode: 200,
			sqlFunc: func() {
				var m sqlmock.Sqlmock
				m, dbengine.Engine = global.MockDB()
				m.ExpectQuery(".*").WillReturnRows(global.GetRowsEventSource(global.GetTestEventSource(1)))
				m.ExpectQuery(".*").WillReturnRows(global.GetRowsFunctions(global.GetTestFunc(1)))
				m.ExpectExec("UPDATE").WillReturnResult(sqlmock.NewResult(1, 1))
				m.ExpectQuery(".*").WillReturnRows(global.GetRowsEventSource(global.GetTestEventSource(1)))
			},
		},
		{
			in_c:         global.BuildNewKunCtx("PUT", "/v1/event-source-mappings/test3", `{"FunctionName":"brn:bce:cfc:bj:9c1c262dfbd8e59026414770fa02df3c:function:test0908_test-0908:$LATEST","StartingPosition":"LATEST","EventSourceBrn":"c7ac82ae14ef42d1a4ffa3b2ececa17f__whx_topic","BatchSize":5,"Enabled":true}`, "uiduid", map[string]string{}),
			out_HttpCode: 200,
			sqlFunc: func() {
				var m sqlmock.Sqlmock
				m, dbengine.Engine = global.MockDB()
				m.ExpectQuery(".*").WillReturnRows(global.GetRowsEventSource(global.GetTestEventSource(1)))
				m.ExpectQuery(".*").WillReturnRows(global.GetRowsFunctions(global.GetTestFunc(1)))
				m.ExpectExec("UPDATE").WillReturnResult(sqlmock.NewResult(1, 1))
				m.ExpectQuery(".*").WillReturnRows(global.GetRowsEventSource(global.GetTestEventSource(1)))
			},
		},
		{
			in_c:         global.BuildNewKunCtx("PUT", "/v1/event-source-mappings/test3", `{"Type":"datahub_topic","BatchSize":1,"MetaHostEndpoint":"*************","MetaHostPort":2181,"ClusterName":"bigpipe_sandbox_new","PipeName":"dueros-tob-pipe","PipeletNum":1,"ACLName":"duer-bigpipe","StartPoint":-1,"ACLPassword":"acPvtNfyWG","Enabled":false}`, "uiduid", map[string]string{}),
			out_HttpCode: 200,
			sqlFunc: func() {
				var m sqlmock.Sqlmock
				m, dbengine.Engine = global.MockDB()
				m.ExpectQuery(".*").WillReturnRows(global.GetRowsEventSource(global.GetTestEventSource(1)))
				m.ExpectQuery(".*").WillReturnRows(global.GetRowsFunctions(global.GetTestFunc(1)))
				m.ExpectExec("UPDATE").WillReturnResult(sqlmock.NewResult(1, 1))
				m.ExpectQuery(".*").WillReturnRows(global.GetRowsEventSource(global.GetTestEventSource(1)))
			},
		},
		{
			in_c:         global.BuildNewKunCtx("PUT", "/v1/event-source-mappings/test3", `{"Type":"datahub_topic","BatchSize":1,"MetaHostEndpoint":"*************","MetaHostPort":2181,"ClusterName":"bigpipe_sandbox_new","PipeName":"dueros-tob-pipe","PipeletNum":1,"ACLName":"duer-bigpipe","StartPoint":-1,"ACLPassword":"acPvtNfyWG","Enabled":true}`, "uiduid", map[string]string{}),
			out_HttpCode: 200,
			sqlFunc: func() {
				var m sqlmock.Sqlmock
				m, dbengine.Engine = global.MockDB()
				m.ExpectQuery(".*").WillReturnRows(global.GetRowsEventSource(global.GetTestEventSource(1)))
				m.ExpectQuery(".*").WillReturnRows(global.GetRowsFunctions(global.GetTestFunc(1)))
				m.ExpectExec("UPDATE").WillReturnResult(sqlmock.NewResult(1, 1))
				m.ExpectQuery(".*").WillReturnRows(global.GetRowsEventSource(global.GetTestEventSource(1)))
			},
		},
		{
			in_c:         global.BuildNewKunCtx("PUT", "/v1/event-source-mappings/test3", `{"Type":"datahub_topic","BatchSize":2000,"MetaHostEndpoint":"*************","MetaHostPort":2181,"ClusterName":"bigpipe_sandbox_new","PipeName":"dueros-tob-pipe","PipeletNum":1,"ACLName":"duer-bigpipe","StartPoint":-1,"ACLPassword":"acPvtNfyWG","Enabled":true}`, "uiduid", map[string]string{}),
			out_HttpCode: 400,
			sqlFunc: func() {
				var m sqlmock.Sqlmock
				m, dbengine.Engine = global.MockDB()
				m.ExpectQuery(".*").WillReturnRows(global.GetRowsEventSource(global.GetTestEventSource(1)))
				m.ExpectQuery(".*").WillReturnRows(global.GetRowsFunctions(global.GetTestFunc(1)))
				m.ExpectExec("UPDATE").WillReturnResult(sqlmock.NewResult(1, 1))
				m.ExpectQuery(".*").WillReturnRows(global.GetRowsEventSource(global.GetTestEventSource(1)))
			},
		},
	}

	for _, tc := range cases {
		r := EventSourceRest{}
		if tc.sqlFunc != nil {
			tc.sqlFunc()
		}

		r.update(tc.in_c)
		assert.Equal(t, tc.out_HttpCode, tc.in_c.Response().StatusCode())
	}
}

func TestDeleteEventSource(t *testing.T) {
	global.MockAC()

	cases := []struct {
		in_c         *server.Context
		out_HttpCode int
		sqlFunc      func()
	}{
		{
			in_c:         global.BuildNewKunCtx("DELETE", "/v1/event-source-mappings/testeventsource", "", "uiduid", map[string]string{}),
			out_HttpCode: 204,
			sqlFunc: func() {
				var m sqlmock.Sqlmock
				m, dbengine.Engine = global.MockDB()
				m.ExpectExec("UPDATE").WillReturnResult(sqlmock.NewResult(1, 1))
			},
		},
	}

	for _, tc := range cases {
		r := EventSourceRest{}
		if tc.sqlFunc != nil {
			tc.sqlFunc()
		}

		r.delete(tc.in_c)
		assert.Equal(t, tc.out_HttpCode, tc.in_c.Response().StatusCode())
	}
}

func TestApis(t *testing.T) {
	r := EventSourceRest{}
	res := r.APIs()
	assert.Equal(t, 9, len(res))
}

func TestGetKafkaTopicList(t *testing.T) {
	global.MockAC()
	cases := []struct {
		in_c         *server.Context
		out_HttpCode int
	}{
		{
			in_c:         global.BuildNewKunCtx("GET", "/v1/kafka/topic/list", "", "uiduid", map[string]string{}),
			out_HttpCode: 200,
		},
	}
	for _, tc := range cases {
		r := EventSourceRest{}

		r.getKafkaTopicList(tc.in_c)
		assert.Equal(t, tc.out_HttpCode, tc.in_c.Response().StatusCode())
	}
}

func TestGetKafkaClusterList(t *testing.T) {
	global.MockAC()
	cases := []struct {
		in_c         *server.Context
		out_HttpCode int
	}{
		{
			in_c:         global.BuildNewKunCtx("GET", "/v1/kafka/cluster/list", "", "uiduid", map[string]string{}),
			out_HttpCode: 200,
		},
	}
	for _, tc := range cases {
		r := EventSourceRest{}

		r.getKafkaClustersList(tc.in_c)
		assert.Equal(t, tc.out_HttpCode, tc.in_c.Response().StatusCode())
	}
}

func TestGetKafkaCluster(t *testing.T) {
	global.MockAC()
	cases := []struct {
		in_c         *server.Context
		out_HttpCode int
		sqlFunc      func()
	}{
		{
			in_c:         global.BuildNewKunCtx("GET", "/v1/kafka/cluster/list/test_cluster_id", "", "uiduid", map[string]string{}),
			out_HttpCode: 500,
			sqlFunc: func() {
				var m sqlmock.Sqlmock
				m, dbengine.Engine = global.MockDB()
				eventSource := &dao.FuncEventSource{
					ClusterID: "test_cluster_id",
					Uuid:      "uiduid",
					Type:      "exclusive_kafka",
				}
				m.ExpectQuery(".*").WillReturnRows(global.GetTestCluster(eventSource))
			},
		},
		{
			in_c:         global.BuildNewKunCtx("GET", "/v1/kafka/cluster/list/test_cluster_id", "", "uiduid", map[string]string{}),
			out_HttpCode: 500,
			sqlFunc: func() {
				var m sqlmock.Sqlmock
				m, dbengine.Engine = global.MockDB()
				eventSource := &dao.FuncEventSource{
					ClusterID: "test_cluster_id",
					Uuid:      "uiduid",
					Type:      "bms",
				}
				m.ExpectQuery(".*").WillReturnRows(global.GetTestCluster(eventSource))
			},
		},
	}
	for _, tc := range cases {
		if tc.sqlFunc != nil {
			tc.sqlFunc()
		}

		r := EventSourceRest{}
		r.getKafkaCluster(tc.in_c)
		assert.Equal(t, tc.out_HttpCode, tc.in_c.Response().StatusCode())
	}
}

func TestGetIntranetEndpoints(t *testing.T) {
	global.MockAC()
	cases := []struct {
		in_c         *server.Context
		out_HttpCode int
	}{
		{
			in_c:         global.BuildNewKunCtx("GET", "/kafka/cluster/test_cluster_id/uid_uid/intranet-endpoints", "", "uiduid", map[string]string{}),
			out_HttpCode: 200,
		},
	}
	for _, tc := range cases {
		r := EventSourceRest{}
		r.getIntranetEndpoints(tc.in_c)
		assert.Equal(t, tc.out_HttpCode, tc.in_c.Response().StatusCode())
	}
}

// TestGetRocketmqCluster 测试函数，用于获取RocketMQ集群信息
// 参数t：*testing.T类型，表示测试对象
// 返回值：无
func TestGetRocketmqCluster(t *testing.T) {
	global.MockAC()
	cases := []struct {
		in_c         *server.Context
		out_HttpCode int
	}{
		{
			in_c:         global.BuildNewKunCtx("GET", "/rocketmq/cluster/test_cluster_id/uid_uid", "", "uiduid", map[string]string{}),
			out_HttpCode: 200,
		},
	}
	for _, tc := range cases {
		r := EventSourceRest{}
		r.getRocketmqCluster(tc.in_c)
		assert.Equal(t, tc.out_HttpCode, tc.in_c.Response().StatusCode())
	}
}

// TestGetRocketmqEndpoint 测试函数TestGetRocketmqEndpoint，该函数用于获取RocketMQ端点信息
// 参数t：*testing.T类型，表示测试对象
// 返回值：无
func TestGetRocketmqEndpoint(t *testing.T) {
	global.MockAC()
	cases := []struct {
		in_c         *server.Context
		out_HttpCode int
	}{
		{
			in_c:         global.BuildNewKunCtx("GET", "/rocketmq/cluster/test_cluster_id/uid_uid/access-endpoints", "", "uiduid", map[string]string{}),
			out_HttpCode: 500, // access-endpoints is empty
		},
	}
	for _, tc := range cases {
		r := EventSourceRest{}
		r.getRocketmqEndpoint(tc.in_c)
		assert.Equal(t, tc.out_HttpCode, tc.in_c.Response().StatusCode())
	}
}

// TestGetRocketmqConsumerGroup 测试函数TestGetRocketmqConsumerGroup，该函数用于获取RocketMQ消费者组信息
// 参数t：*testing.T类型，表示测试对象
// 返回值：无
func TestGetRocketmqConsumerGroup(t *testing.T) {
	global.MockAC()
	cases := []struct {
		in_c         *server.Context
		out_HttpCode int
	}{
		{
			in_c:         global.BuildNewKunCtx("GET", "/rocketmq/cluster/test_cluster_id/uid_uid/consumer-groups/test_group", "", "uiduid", map[string]string{}),
			out_HttpCode: 200,
		},
	}
	for _, tc := range cases {
		r := EventSourceRest{}
		r.getRocketmqConsumerGroup(tc.in_c)
		assert.Equal(t, tc.out_HttpCode, tc.in_c.Response().StatusCode())
	}
}

func TestGetRocketmqClustersList(t *testing.T) {
	global.MockAC()
	// mock response
	clusters := []*rocketmq.Cluster{
		{
			ClusterID: "rocketmq-1",
			Name:      "test-cluster-1",
			Region:    "bj",
			State:     "ACTIVE",
		},
	}
	global.AC.Clients.RocketmqClient = &mockRocketmqClient{
		clusters: clusters,
	}
	cases := []struct {
		in_c         *server.Context
		out_HttpCode int
	}{
		{
			in_c:         global.BuildNewKunCtx("GET", "/v1/rocketmq/clusters/list", "", "uiduid", map[string]string{}),
			out_HttpCode: 200,
		},
	}
	for _, tc := range cases {
		r := EventSourceRest{}
		r.getRocketmqClustersList(tc.in_c)
		assert.Equal(t, tc.out_HttpCode, tc.in_c.Response().StatusCode())
	}
}

func TestGetRocketmqTopics(t *testing.T) {
	global.MockAC()
	topics := []*rocketmq.Topic{
		{
			TopicName: "topic-1",
		},
	}
	global.AC.Clients.RocketmqClient = &mockRocketmqClient{
		topics: topics,
	}
	cases := []struct {
		in_c         *server.Context
		out_HttpCode int
	}{
		{
			in_c:         global.BuildNewKunCtx("GET", "/v1/rocketmq/rocketmq-1/topics", "", "uiduid", map[string]string{"clusterID": "rocketmq-1"}),
			out_HttpCode: 200,
		},
	}
	for _, tc := range cases {
		r := EventSourceRest{}
		r.getRocketmqTopics(tc.in_c)
		assert.Equal(t, tc.out_HttpCode, tc.in_c.Response().StatusCode())
	}
}

func TestGetRocketmqConsumerGroups(t *testing.T) {
	global.MockAC()
	consumerGroups := []*rocketmq.ConsumerGroup{
		{
			GroupName: "group-1",
		},
	}
	global.AC.Clients.RocketmqClient = &mockRocketmqClient{
		consumerGroups: consumerGroups,
	}
	cases := []struct {
		in_c         *server.Context
		out_HttpCode int
	}{
		{
			in_c:         global.BuildNewKunCtx("GET", "/v1/rocketmq/rocketmq-1/consumer-groups", "", "uiduid", map[string]string{"clusterID": "rocketmq-1"}),
			out_HttpCode: 200,
		},
	}
	for _, tc := range cases {
		r := EventSourceRest{}
		r.getRocketmqConsumerGroups(tc.in_c)
		assert.Equal(t, tc.out_HttpCode, tc.in_c.Response().StatusCode())
	}
}

// mockRocketmqClient implements the RocketmqControlInterface for testing
// Only the methods needed for these tests are implemented

type mockRocketmqClient struct {
	clusters       []*rocketmq.Cluster
	topics         []*rocketmq.Topic
	consumerGroups []*rocketmq.ConsumerGroup
}

func (m *mockRocketmqClient) ListClusters(uid string) (*rocketmq.ClusterListResponse, error) {
	return &rocketmq.ClusterListResponse{Clusters: m.clusters}, nil
}
func (m *mockRocketmqClient) ListTopics(uid, clusterID string) (*rocketmq.TopicListResponse, error) {
	return &rocketmq.TopicListResponse{Topics: m.topics}, nil
}
func (m *mockRocketmqClient) ListConsumerGroups(uid, clusterID string) (*rocketmq.ConsumerGroupListResponse, error) {
	return &rocketmq.ConsumerGroupListResponse{ConsumerGroups: m.consumerGroups}, nil
}

// The rest of the interface methods can panic or return nil for brevity
func (m *mockRocketmqClient) GetClusterDetail(uid, clusterID string) (*rocketmq.Cluster, error) {
	panic("not implemented")
}
func (m *mockRocketmqClient) GetClusterEndPoint(uid, clusterID string) (*rocketmq.AccessEndpoint, error) {
	panic("not implemented")
}
func (m *mockRocketmqClient) GetTopicDetail(uid, clusterID, topicName string) (*rocketmq.Topic, error) {
	panic("not implemented")
}
func (m *mockRocketmqClient) GetConsumerGroupDetail(uid, clusterID, groupName string) (*rocketmq.ConsumerGroup, error) {
	panic("not implemented")
}
