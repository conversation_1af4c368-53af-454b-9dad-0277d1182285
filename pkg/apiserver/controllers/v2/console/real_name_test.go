package console

import (
	"errors"
	"fmt"
	"testing"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/stretchr/testify/assert"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/dbengine"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/global"
	"icode.baidu.com/baidu/faas/kun/pkg/server"
)

func TestRealName(t *testing.T) {
	global.MockAC()

	cases := []struct {
		in_c         *server.Context
		out_HttpCode int
	}{
		{
			in_c:         global.BuildNewKunCtx("GET", "/v2/console/realNameQualification", "", "abc", map[string]string{}),
			out_HttpCode: 500,
		},
		{
			in_c:         global.BuildNewKunCtx("GET", "/v2/console/realNameQualification", "", "abc", map[string]string{}),
			out_HttpCode: 500,
		},
		{
			in_c:         global.BuildNewKunCtx("GET", "/v2/console/realNameQualification", "", "abc", map[string]string{}),
			out_HttpCode: 200,
		},
	}
	for i, tc := range cases {
		var m sqlmock.Sqlmock
		m, dbengine.Engine = global.MockDB()
		if i == 0 {
			m.ExpectExec(".*").WillReturnResult(sqlmock.NewErrorResult(errors.New("#1062 - Duplicate entry")))
		} else if i == 1 {
			m.ExpectQuery(".*").WillReturnRows(global.GetRowsRealName(nil))
		} else if i == 2 {
			m.ExpectQuery(".*").WillReturnRows(global.GetRowsRealName(global.GetTestRealName(1)))
		}
		c := ConsoleFunctionRest{}
		fmt.Println(i)
		c.consoleQueryRealName(tc.in_c)
		assert.Equal(t, tc.out_HttpCode, tc.in_c.Response().StatusCode())
	}
}
