package console

import (
	"fmt"
	"github.com/DATA-DOG/go-sqlmock"
	"github.com/stretchr/testify/assert"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/code"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/dbengine"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/global"
	"icode.baidu.com/baidu/faas/kun/pkg/server"
	"icode.baidu.com/baidu/faas/kun/pkg/util/base64"
	"os"
	"testing"
)

func TestConsoleUpdateCode(t *testing.T) {
	global.MockAC()
	code.MockCode()
	var symlinkZipFile = "UEsDBAoAAAAAAMSbjVDCVPv+DQAAAA0AAAAKABwAYmx1ZXByaW50c1VUCQADP02UXj9NlF51eAsAAQT2AQAABBQAAAAuLi9ibHVlcHJpbnRzUEsDBBQAAAAIAMOTmlDxG1zIfwAAAMMAAAAIABwAaW5kZXguanNVVAkAA61ipV6tYqVedXgLAAEE9gEAAAQUAAAAbY1NCsJADIX3c4owm7agvUCpa2/gQroYp0GLIZEklYp4d5niqrh7fO+vmg3BXKfsVRdweYi6tbfEI6FCD8lenKHGJ7LvIAs7LkUkokvK9wb6A7wDFMeEsCW51vGIRAInURpjszHXpb/wHFl4j8tkHodt4vdcsKLPyrB2wqcLX1BLAQIeAwoAAAAAAMSbjVDCVPv+DQAAAA0AAAAKABgAAAAAAAAAAADtoQAAAABibHVlcHJpbnRzVVQFAAM/TZRedXgLAAEE9gEAAAQUAAAAUEsBAh4DFAAAAAgAw5OaUPEbXMh/AAAAwwAAAAgAGAAAAAAAAQAAAKSBUQAAAGluZGV4LmpzVVQFAAOtYqVedXgLAAEE9gEAAAQUAAAAUEsFBgAAAAACAAIAngAAABIBAAAAAA=="
	byteArray, _ := base64.DecodeString(symlinkZipFile)
	bodyFile := string((byteArray))
	mockMutipartBody := "--__X_PAW_BOUNDARY__\n" +
		"Content-Disposition: form-data; name=\"file\"; filename=\"default_py.zip\"\n" +
		"Content-Type: application/zip\n\n" +
		bodyFile + "\n" +
		"--__X_PAW_BOUNDARY__\n" +
		"Content-Disposition: form-data; name=\"chunkTotal\"\n\n" +
		"1\n" +
		"--__X_PAW_BOUNDARY__\n" +
		"Content-Disposition: form-data; name=\"fileName\"\n\n" +
		"default_py.zip\n" +
		"--__X_PAW_BOUNDARY__\n" +
		"Content-Disposition: form-data; name=\"fileSize\"\n\n" +
		"606\n" +
		"--__X_PAW_BOUNDARY__\n" +
		"Content-Disposition: form-data; name=\"retryCount\"\n\n" +
		"1\n" +
		"--__X_PAW_BOUNDARY__\n" +
		"Content-Disposition: form-data; name=\"chunkIndex\"\n\n" +
		"0\n" +
		"--__X_PAW_BOUNDARY__\n" +
		"Content-Disposition: form-data; name=\"chunkSize\"\n\n" +
		"606\n" +
		"--__X_PAW_BOUNDARY__--"

	mockMutipartBodyNotChunk := "--__X_PAW_BOUNDARY__\n" +
		"Content-Disposition: form-data; name=\"file\"; filename=\"default_py.zip\"\n" +
		"Content-Type: application/zip\n\n" +
		bodyFile + "\n" +
		"--__X_PAW_BOUNDARY__--"

	chuckFile := string((byteArray))
	mockChunkFileBody1 := "--__X_PAW_BOUNDARY__\n" +
		"Content-Disposition: form-data; name=\"file\"; filename=\"test1.zip\"\n" +
		"Content-Type: application/zip\n\n" +
		chuckFile + "\n" +
		"--__X_PAW_BOUNDARY__\n" +
		"Content-Disposition: form-data; name=\"chunkTotal\"\n\n" +
		"2\n" +
		"--__X_PAW_BOUNDARY__\n" +
		"Content-Disposition: form-data; name=\"fileName\"\n\n" +
		"test.zip\n" +
		"--__X_PAW_BOUNDARY__\n" +
		"Content-Disposition: form-data; name=\"fileSize\"\n\n" +
		"908\n" +
		"--__X_PAW_BOUNDARY__\n" +
		"Content-Disposition: form-data; name=\"retryCount\"\n\n" +
		"1\n" +
		"--__X_PAW_BOUNDARY__\n" +
		"Content-Disposition: form-data; name=\"chunkIndex\"\n\n" +
		"0\n" +
		"--__X_PAW_BOUNDARY__\n" +
		"Content-Disposition: form-data; name=\"chunkSize\"\n\n" +
		"454\n" +
		"--__X_PAW_BOUNDARY__\n" +
		"Content-Disposition: form-data; name=\"chunkOver\"\n\n" +
		"false\n" +
		"--__X_PAW_BOUNDARY__--"

	mockChunkFileBody2 := "--__X_PAW_BOUNDARY__\n" +
		"Content-Disposition: form-data; name=\"file\"; filename=\"test2.zip\"\n" +
		"Content-Type: application/zip\n\n" +
		chuckFile + "\n" +
		"--__X_PAW_BOUNDARY__\n" +
		"Content-Disposition: form-data; name=\"chunkTotal\"\n\n" +
		"2\n" +
		"--__X_PAW_BOUNDARY__\n" +
		"Content-Disposition: form-data; name=\"fileName\"\n\n" +
		"test.zip\n" +
		"--__X_PAW_BOUNDARY__\n" +
		"Content-Disposition: form-data; name=\"fileSize\"\n\n" +
		"908\n" +
		"--__X_PAW_BOUNDARY__\n" +
		"Content-Disposition: form-data; name=\"retryCount\"\n\n" +
		"1\n" +
		"--__X_PAW_BOUNDARY__\n" +
		"Content-Disposition: form-data; name=\"chunkIndex\"\n\n" +
		"1\n" +
		"--__X_PAW_BOUNDARY__\n" +
		"Content-Disposition: form-data; name=\"chunkSize\"\n\n" +
		"454\n" +
		"--__X_PAW_BOUNDARY__\n" +
		"Content-Disposition: form-data; name=\"chunkOver\"\n\n" +
		"false\n" +
		"--__X_PAW_BOUNDARY__--"

	mockChunkFileOver := "--__X_PAW_BOUNDARY__\n" +
		"Content-Disposition: form-data; name=\"chunkTotal\"\n\n" +
		"2\n" +
		"--__X_PAW_BOUNDARY__\n" +
		"Content-Disposition: form-data; name=\"fileName\"\n\n" +
		"test.zip\n" +
		"--__X_PAW_BOUNDARY__\n" +
		"Content-Disposition: form-data; name=\"fileSize\"\n\n" +
		"908\n" +
		"--__X_PAW_BOUNDARY__\n" +
		"Content-Disposition: form-data; name=\"chunkOver\"\n\n" +
		"true\n" +
		"--__X_PAW_BOUNDARY__--"
	var m sqlmock.Sqlmock
	m, dbengine.Engine = global.MockDB()
	m.ExpectQuery("SELECT").WillReturnRows(global.GetRowsFunctions(global.GetTestFunc(1)))
	m.ExpectExec("UPDATE").WillReturnResult(sqlmock.NewResult(1, 1))
	m.ExpectQuery("SELECT").WillReturnRows(global.GetRowsFunctions(global.GetTestFunc(1)))
	m.ExpectQuery("SELECT").WillReturnRows(global.GetRowsFunctions(global.GetTestFunc(1)))
	m.ExpectQuery("SELECT").WillReturnRows(global.GetRowsFunctions(global.GetTestFunc(1)))
	m.ExpectExec("UPDATE").WillReturnResult(sqlmock.NewResult(1, 1))
	m.ExpectQuery("SELECT").WillReturnRows(global.GetRowsFunctions(global.GetTestFunc(1)))
	m.ExpectQuery("SELECT").WillReturnRows(global.GetRowsFunctions(global.GetTestFunc(1)))
	cases := []struct {
		in_c         *server.Context
		out_HttpCode int
		sqlFunc      func()
	}{
		{
			in_c: global.BuildNewKunCtxWithHeaders1("POST", "/v2/console/functions/myfunc/code", "err Json BODY", "uiduid", map[string]string{
				"FunctionName": "myfunc:$LATEST",
			}),
			out_HttpCode: 400,
		},
		{
			in_c:         global.BuildNewKunCtxWithHeaders1("POST", "/v2/console/functions/myfunc/code", mockMutipartBody, "", map[string]string{}),
			out_HttpCode: 400,
		},
		{
			in_c:         global.BuildNewKunCtxWithHeaders1("POST", "/v2/console/functions/myfunc/code", mockMutipartBody, "uiduid", map[string]string{}),
			out_HttpCode: 400,
		},
		{
			in_c: global.BuildNewKunCtxWithHeaders1("POST", "/v2/console/functions/myfunc/code?Qualifier=$LATEST", mockMutipartBodyNotChunk, "uid", map[string]string{
				"FunctionName": "test:$LATEST",
			}),
			out_HttpCode: 200,
			sqlFunc: func() {
			},
		},
		{
			in_c:         global.BuildNewKunCtxWithHeaders1("POST", "/v2/console/functions/myfunc/code?Qualifier=$LATEST", mockChunkFileBody1, "uiduid", map[string]string{"FunctionName": "myfunc:$LATEST"}),
			out_HttpCode: 200,
		},
		{
			in_c:         global.BuildNewKunCtxWithHeaders1("POST", "/v2/console/functions/myfunc/code?Qualifier=$LATEST", mockChunkFileBody2, "uiduid", map[string]string{"FunctionName": "myfunc:$LATEST"}),
			out_HttpCode: 200,
		},
		{
			in_c:         global.BuildNewKunCtxWithHeaders1("POST", "/v2/console/functions/myfunc/code?Qualifier=$LATEST", mockChunkFileOver, "uiduid", map[string]string{"FunctionName": "myfunc:$LATEST"}),
			out_HttpCode: 200,
			sqlFunc: func() {

			},
		},
	}
	for i, tc := range cases {
		c := ConsoleFunctionRest{}
		fmt.Println(i)
		c.consoleUpdateCode(tc.in_c)
		assert.Equal(t, tc.out_HttpCode, tc.in_c.Response().StatusCode())
	}
	os.RemoveAll("./chunktemp")
}
