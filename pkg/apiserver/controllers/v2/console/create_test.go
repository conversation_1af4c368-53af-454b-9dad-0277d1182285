package console

import (
	"testing"
	"time"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/stretchr/testify/assert"
	"icode.baidu.com/baidu/faas/kun/pkg/api"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/code"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/dao"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/dbengine"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/global"
	"icode.baidu.com/baidu/faas/kun/pkg/server"
	"icode.baidu.com/baidu/faas/kun/pkg/util/convert"
)

func TestConsoleNewFuncV2(t *testing.T) {
	global.MockAC()
	// code.MockCode()
	var m sqlmock.Sqlmock
	m, dbengine.Engine = global.MockDB()
	global.RegisterFaasRuntimeGovalidatorTag()

	mockBodyString := `{"FunctionName":"test2","ServiceName":"service", "MemorySize":128,"Description":"create from paw","Handler":"index.handler","Runtime":"python2","Timeout":20}`
	mockInvalidBodyString := `{"FunctionName":"test2","MemorySize":128,"Description":"create from paw","Handler":"index.handler","Runtime":"pyt?on2","Timeout":20}`
	req1 := "SELECT * FROM `functions`  WHERE (`functions`.`function_brn` = ?) AND (deleted_at IS NULL OR deleted_at='0000-00-00 00:00:00') ORDER BY `functions`.`id` ASC LIMIT 1"
	cases := []struct {
		in_c         *server.Context
		out_HttpCode int
	}{
		{
			in_c:         global.BuildNewKunCtx("POST", "/v2/console/functions", "err Json BODY", "uiduid", map[string]string{}),
			out_HttpCode: 400,
		},
		{
			//验证function memSize/Runtime错误
			in_c:         global.BuildNewKunCtx("POST", "/v2/console/functions", mockInvalidBodyString, "uiduid", map[string]string{}),
			out_HttpCode: 404,
		},
		{
			in_c:         global.BuildNewKunCtx("POST", "/v2/console/functions", mockBodyString, "", map[string]string{}),
			out_HttpCode: 404,
		},
		{
			in_c:         global.BuildNewKunCtx("POST", "/v2/console/functions", mockBodyString, "uiduid", map[string]string{}),
			out_HttpCode: 201,
		},
	}
	for i, tc := range cases {
		c := ConsoleFunctionRest{}
		if i == 3 {
			m.ExpectQuery("SELECT").WillReturnRows(global.GetRowsBlueprints(global.GetTestBlueprint(1)))
			m.ExpectQuery("SELECT").WillReturnRows(global.GetRowsRuntimeConfig(global.GetTestRuntimeConfig(1)))
			m.ExpectQuery("SELECT").WillReturnRows(global.GetRowsFunctions(global.GetTestFunc(1)))
			m.ExpectQuery(global.FixedFullRe(req1)).WillReturnRows(global.GetRowsFunctions(nil))
			m.ExpectQuery("SELECT").WillReturnRows(global.GetRowsService(global.GetTestService(1)))
			m.ExpectBegin()
			m.ExpectExec("^INSERT INTO (.+)").WillReturnResult(sqlmock.NewResult(0, 0))
			m.ExpectCommit()
		}

		c.consoleNewFuncV2(tc.in_c)
		assert.Equal(t, tc.out_HttpCode, tc.in_c.Response().StatusCode())
	}
}

func TestConsoleNewCbdFunc(t *testing.T) {
	global.MockAC()
	code.MockCode()
	var m sqlmock.Sqlmock
	m, dbengine.Engine = global.MockDB()
	global.RegisterFaasRuntimeGovalidatorTag()

	mockBodyString := `{"UUID":"f1eeb70e-dda5-11e8-9f8b-f2801f1b9fd1","FunctionName":"scohxjqugq_test","MemorySize":512,"Description":"create from paw for cbd","SourceTag":"cbd","Handler":"index.handler","Timeout":20,"LogType":"kafka","LogBosDir":"name=cbd&topic=test_topic"}`
	mockInvalidBodyString1 := `{"FunctionName":"scohxjqugq_test2","MemorySize":512,"Description":"create cbd function","SourceTag":"cbd","Handler":"index.handler","Timeout":20}`
	mockInvalidBodyString2 := `{"FunctionName":"scohxjqugq_test2","MemorySize":512,"Description":"create cbd function","SourceTag":"cas","Handler":"index.handler","Timeout":20,"Runtime":"nodejs8.5"}`
	req1 := "SELECT * FROM `functions`  WHERE (`functions`.`function_brn` = ?) AND (deleted_at IS NULL OR deleted_at='0000-00-00 00:00:00') ORDER BY `functions`.`id` ASC LIMIT 1"
	cases := []struct {
		in_c         *server.Context
		out_HttpCode int
		sqlFunc      func()
	}{
		{
			in_c:         global.BuildNewKunCtx("POST", "/v2/console/cbd/functions", "err Json BODY", "uiduid", map[string]string{}),
			out_HttpCode: 400,
			sqlFunc:      nil,
		},
		{
			//验证蓝图UUID为空错误且Runtime为空
			in_c:         global.BuildNewKunCtx("POST", "/v2/console/cbd/functions", mockInvalidBodyString1, "uiduid", map[string]string{}),
			out_HttpCode: 400,
			sqlFunc:      nil,
		},
		{
			//验证蓝图SourceTag错误
			in_c:         global.BuildNewKunCtx("POST", "/v2/console/cbd/functions", mockInvalidBodyString2, "uiduid", map[string]string{}),
			out_HttpCode: 400,
			sqlFunc:      nil,
		},
		{
			in_c:         global.BuildNewKunCtx("POST", "/v2/console/cbd/functions", mockBodyString, "", map[string]string{}),
			out_HttpCode: 400,
			sqlFunc: func() {
				m.ExpectQuery("SELECT").WillReturnRows(global.GetRowsBlueprints(global.GetTestBlueprint(1)))
			},
		},
		{
			in_c:         global.BuildNewKunCtx("POST", "/v2/console/cbd/functions", mockBodyString, "uiduid", map[string]string{}),
			out_HttpCode: 201,
			sqlFunc: func() {
				m.ExpectQuery("SELECT").WillReturnRows(global.GetRowsBlueprints(global.GetTestBlueprint(1)))
				m.ExpectQuery("SELECT").WillReturnRows(global.GetRowsRuntimeConfig(global.GetTestRuntimeConfig(1)))
				m.ExpectQuery("SELECT").WillReturnRows(global.GetRowsFunctions(global.GetTestFunc(1)))
				m.ExpectQuery("SELECT").WillReturnRows(global.GetRowsNetworkConfigs(global.GetTestNetworkConfig(1)))
				m.ExpectQuery(global.FixedFullRe(req1)).WillReturnRows(global.GetRowsFunctions(nil))
				m.ExpectQuery("SELECT").WillReturnRows(global.GetRowsService(global.GetTestService(1)))
				m.ExpectBegin()
				m.ExpectExec("^INSERT INTO (.+)").WillReturnResult(sqlmock.NewResult(0, 0))
				m.ExpectExec("^INSERT INTO (.+)").WillReturnResult(sqlmock.NewResult(0, 0))
				m.ExpectCommit()
			},
		},
	}
	for _, tc := range cases {
		c := ConsoleFunctionRest{}
		if tc.sqlFunc != nil {
			tc.sqlFunc()
		}
		c.consoleNewCbdFunc(tc.in_c)
		assert.Equal(t, tc.out_HttpCode, tc.in_c.Response().StatusCode())
	}
}

func TestConsoleNewCbdFunc2(t *testing.T) {
	global.MockAC()
	code.MockCode()
	var m sqlmock.Sqlmock
	m, dbengine.Engine = global.MockDB()
	global.RegisterFaasRuntimeGovalidatorTag()

	mockBodyString1 := `{"Runtime":"nodejs8.5","FunctionName":"scohxjqugq_test","MemorySize":512,"Description":"create from paw for cbd","SourceTag":"cbd","Handler":"index.handler","Timeout":20}`
	req1 := "SELECT * FROM `functions`  WHERE (`functions`.`function_brn` = ?) AND (deleted_at IS NULL OR deleted_at='0000-00-00 00:00:00') ORDER BY `functions`.`id` ASC LIMIT 1"
	cases := []struct {
		in_c         *server.Context
		out_HttpCode int
		sqlFunc      func()
	}{
		{
			in_c:         global.BuildNewKunCtx("POST", "/v2/console/cbd/functions", mockBodyString1, "uiduid", map[string]string{}),
			out_HttpCode: 201,
			sqlFunc: func() {
				m.ExpectQuery("SELECT").WillReturnRows(global.GetRowsBlueprints(global.GetTestBlueprint(1)))
				m.ExpectQuery("SELECT").WillReturnRows(global.GetRowsRuntimeConfig(global.GetTestRuntimeConfig(1)))
				m.ExpectQuery("SELECT").WillReturnRows(global.GetRowsFunctions(global.GetTestFunc(1)))
				m.ExpectQuery("SELECT").WillReturnRows(global.GetRowsNetworkConfigs(global.GetTestNetworkConfig(1)))
				m.ExpectQuery(global.FixedFullRe(req1)).WillReturnRows(global.GetRowsFunctions(nil))
				m.ExpectQuery("SELECT").WillReturnRows(global.GetRowsService(global.GetTestService(1)))
				m.ExpectBegin()
				m.ExpectExec("^INSERT INTO (.+)").WillReturnResult(sqlmock.NewResult(0, 0))
				m.ExpectExec("^INSERT INTO (.+)").WillReturnResult(sqlmock.NewResult(0, 0))
				m.ExpectCommit()
			},
		},
	}
	for _, tc := range cases {
		c := ConsoleFunctionRest{}
		if tc.sqlFunc != nil {
			tc.sqlFunc()
		}
		c.consoleNewCbdFunc(tc.in_c)
		assert.Equal(t, tc.out_HttpCode, tc.in_c.Response().StatusCode())
	}
}

// 创建自定义的mock blueprint数据，支持custom runtime
func getCustomRuntimeBlueprint() dao.Blueprint {
	description := "Custom Ubuntu 22.04 with Python 3.10"
	deployments := 1
	return dao.Blueprint{
		Id:             1,
		Uuid:           "custom-ubuntu2204-python3.10",
		Name:           "custom-ubuntu2204-python3.10",
		Description:    &description,
		KeywordsStr:    "custom,python3.10",
		BosObjKey:      "CustomBlueprintBosKey",
		Runtime:        "custom.ubuntu2204.python3.10.python3.10",
		Handler:        "index.handler",
		Version:        "1.0.0",
		Status:         "default",
		UpdatedAt:      time.Now(),
		CreatedAt:      time.Now(),
		EnvironmentStr: `{"Variables":{"PYTHONPATH":"/usr/local/lib/python3.10"}}`,
		LinksStr:       `{"docs":"https://docs.example.com"}`,
		Deployments:    &deployments,
		AuthorsStr:     "test-author",
	}
}

// 创建自定义的mock runtime config数据，支持custom runtime
func getCustomRuntimeConfig() dao.RuntimeConfig {
	return dao.RuntimeConfig{
		RuntimeConfiguration: api.RuntimeConfiguration{
			Name: "custom.ubuntu2204.python3.10",
		},
		DeprecatedAt: time.Date(2038, 1, 1, 1, 1, 1, 1, time.Local),
	}
}

// 创建自定义的mock service数据，支持custom runtime测试
func getCustomService() dao.Service {
	return dao.Service{
		Id:          1,
		Uid:         "uiduid",
		Region:      "bj",
		ServiceName: "service",
		ServiceDesc: convert.String("Custom service for testing"),
		ServiceConf: "custom service config",
		Status:      1,
		UpdatedAt:   time.Now(),
		CreatedAt:   time.Now(),
	}
}

// TestConsoleNewFuncV2_CustomRuntimeValidation 测试ConsoleNewFuncV2_CustomRuntimeValidation函数，包括custom runtime配置不完整、command为空、port为0以及langruntime缺失等场景。
// 该函数会对输入的请求参数进行验证，并返回相应的http状态码。如果是有效的custom runtime请求，则会创建一个新的函数并返回201状态码。
func TestConsoleNewFuncV2_CustomRuntimeValidation(t *testing.T) {
	global.MockAC()
	var m sqlmock.Sqlmock
	m, dbengine.Engine = global.MockDB()
	global.RegisterFaasRuntimeGovalidatorTag()

	// 手动添加custom runtime到RuntimeCache中
	global.AC.Cache.RuntimeCache["custom.ubuntu2204.python3.10"] = getCustomRuntimeConfig()

	// CustomRuntimeConfig缺失的测试用例
	mockCustomRuntimeNoConfig := `{"FunctionName":"test_custom","ServiceName":"service","MemorySize":128,"Description":"custom runtime test","Handler":"index.handler","Runtime":"custom.ubuntu2204.python3.10","Timeout":20}`

	// CustomRuntimeConfig存在但command为空的测试用例
	mockCustomRuntimeNoCommand := `{"FunctionName":"test_custom","ServiceName":"service","MemorySize":128,"Description":"custom runtime test","Handler":"index.handler","Runtime":"custom.ubuntu2204.python3.10","Timeout":20,"CustomRuntimeConfig":{"Port":8080,"Command":[],"Args":[]}}`

	// CustomRuntimeConfig存在但port为0的测试用例
	mockCustomRuntimeNoPort := `{"FunctionName":"test_custom","ServiceName":"service","MemorySize":128,"Description":"custom runtime test","Handler":"index.handler","Runtime":"custom.ubuntu2204.python3.10","Timeout":20,"CustomRuntimeConfig":{"Port":0,"Command":["/bin/sh"],"Args":[]}}`

	// LangRuntime缺失的测试用例
	mockCustomRuntimeNoLangRuntime := `{"FunctionName":"test_custom","ServiceName":"service","MemorySize":128,"Description":"custom runtime test","Handler":"index.handler","Runtime":"custom.ubuntu2204.python3.10","Timeout":20,"CustomRuntimeConfig":{"Port":8080,"Command":["/bin/sh"],"Args":[]}}`

	// 正常的custom runtime测试用例
	mockValidCustomRuntime := `{"FunctionName":"test_custom","ServiceName":"bpname_0","MemorySize":128,"Description":"custom runtime test","Handler":"index.handler","Runtime":"custom.ubuntu2204.python3.10","Timeout":20,"CustomRuntimeConfig":{"Port":8080,"Command":["/bin/sh"],"Args":[]},"LangRuntime":"python3.10"}`

	cases := []struct {
		name         string
		in_c         *server.Context
		out_HttpCode int
		sqlFunc      func()
	}{
		{
			name:         "custom runtime without CustomRuntimeConfig",
			in_c:         global.BuildNewKunCtx("POST", "/v2/console/functions", mockCustomRuntimeNoConfig, "uiduid", map[string]string{}),
			out_HttpCode: 400,
			sqlFunc:      nil,
		},
		{
			name:         "custom runtime with empty command",
			in_c:         global.BuildNewKunCtx("POST", "/v2/console/functions", mockCustomRuntimeNoCommand, "uiduid", map[string]string{}),
			out_HttpCode: 400,
			sqlFunc:      nil,
		},
		{
			name:         "custom runtime with port 0",
			in_c:         global.BuildNewKunCtx("POST", "/v2/console/functions", mockCustomRuntimeNoPort, "uiduid", map[string]string{}),
			out_HttpCode: 400,
			sqlFunc:      nil,
		},
		{
			name:         "custom runtime without LangRuntime",
			in_c:         global.BuildNewKunCtx("POST", "/v2/console/functions", mockCustomRuntimeNoLangRuntime, "uiduid", map[string]string{}),
			out_HttpCode: 400,
			sqlFunc:      nil,
		},
		{
			name:         "valid custom runtime",
			in_c:         global.BuildNewKunCtx("POST", "/v2/console/functions", mockValidCustomRuntime, "00457f0b-20d8-4f3d-8555-c233c7ebd495", map[string]string{}),
			out_HttpCode: 201,
			sqlFunc: func() {
				// 使用自定义的custom runtime blueprint数据
				customBlueprint := getCustomRuntimeBlueprint()
				m.ExpectQuery("SELECT").WillReturnRows(global.GetRowsBlueprints([]dao.Blueprint{customBlueprint}))
				m.ExpectQuery("SELECT").WillReturnRows(global.GetRowsRuntimeConfig([]dao.RuntimeConfig{getCustomRuntimeConfig()}))
				m.ExpectQuery("SELECT").WillReturnRows(global.GetRowsFunctions(global.GetTestFunc(1)))
				m.ExpectQuery("SELECT").WillReturnRows(global.GetRowsFunctions(nil))
				customService := dao.Service{
					Id:          1,
					Uid:         "00457f0b-20d8-4f3d-8555-c233c7ebd495",
					Region:      "bj",
					ServiceName: "bpname_0",
					ServiceDesc: convert.String("desc"),
					ServiceConf: "keywords_0",
					Status:      1,
					UpdatedAt:   time.Now(),
					CreatedAt:   time.Now(),
				}
				m.ExpectQuery("SELECT").WillReturnRows(global.GetRowsService([]dao.Service{customService}))
				m.ExpectQuery("SELECT").WillReturnRows(global.GetRowsService([]dao.Service{customService}))
				m.ExpectBegin()
				m.ExpectExec("^INSERT INTO (.+)").WillReturnResult(sqlmock.NewResult(0, 0))
				m.ExpectCommit()
			},
		},
	}

	for _, tc := range cases {
		t.Run(tc.name, func(t *testing.T) {
			c := ConsoleFunctionRest{}
			if tc.sqlFunc != nil {
				tc.sqlFunc()
			}
			c.consoleNewFuncV2(tc.in_c)

			// 添加调试信息
			if tc.name == "valid custom runtime" {
				t.Logf("Response Status: %d", tc.in_c.Response().StatusCode())
				// 注意：这里可能需要其他方式获取响应内容
			}

			assert.Equal(t, tc.out_HttpCode, tc.in_c.Response().StatusCode())
		})
	}
}

// TestConsoleNewFuncV2_BlueprintRuntimeConcatenation 测试ConsoleNewFuncV2_BlueprintRuntimeConcatenation函数，该函数是ConsoleFunctionRest结构体的一个方法，用于新增函数。
// 该函数会根据传入的runtime参数，判断是否为自定义runtime，如果是则会从RuntimeCache中获取对应的custom runtime配置并进行拼接，
// 然后将拼接后的blueprint runtime数据写入数据库。
// 该函数需要在全局环境中注册faas runtime govalidator tag，并且需要初始化dbengine和global.AC。
func TestConsoleNewFuncV2_BlueprintRuntimeConcatenation(t *testing.T) {
	global.MockAC()
	var m sqlmock.Sqlmock
	m, dbengine.Engine = global.MockDB()
	global.RegisterFaasRuntimeGovalidatorTag()

	// 手动添加custom runtime到RuntimeCache中
	global.AC.Cache.RuntimeCache["custom.ubuntu2204.python3.10"] = getCustomRuntimeConfig()

	// 测试custom runtime的blueprint runtime拼接逻辑
	mockCustomRuntime := `{"FunctionName":"test_custom","ServiceName":"bpname_0","MemorySize":128,"Description":"custom runtime test","Handler":"index.handler","Runtime":"custom.ubuntu2204.python3.10","Timeout":20,"CustomRuntimeConfig":{"Port":8080,"Command":["/bin/sh"],"Args":[]},"LangRuntime":"python3.10"}`

	c := ConsoleFunctionRest{}

	// 使用自定义的custom runtime blueprint数据
	customBlueprint := getCustomRuntimeBlueprint()
	m.ExpectQuery("SELECT").WillReturnRows(global.GetRowsBlueprints([]dao.Blueprint{customBlueprint}))
	m.ExpectQuery("SELECT").WillReturnRows(global.GetRowsRuntimeConfig([]dao.RuntimeConfig{getCustomRuntimeConfig()}))
	m.ExpectQuery("SELECT").WillReturnRows(global.GetRowsFunctions(global.GetTestFunc(1)))
	m.ExpectQuery("SELECT").WillReturnRows(global.GetRowsFunctions(nil))
	customService := dao.Service{
		Id:          1,
		Uid:         "00457f0b-20d8-4f3d-8555-c233c7ebd495",
		Region:      "bj",
		ServiceName: "bpname_0",
		ServiceDesc: convert.String("desc"),
		ServiceConf: "keywords_0",
		Status:      1,
		UpdatedAt:   time.Now(),
		CreatedAt:   time.Now(),
	}
	m.ExpectQuery("SELECT").WillReturnRows(global.GetRowsService([]dao.Service{customService}))
	m.ExpectQuery("SELECT").WillReturnRows(global.GetRowsService([]dao.Service{customService}))
	m.ExpectBegin()
	m.ExpectExec("^INSERT INTO (.+)").WillReturnResult(sqlmock.NewResult(0, 0))
	m.ExpectCommit()

	ctx := global.BuildNewKunCtx("POST", "/v2/console/functions", mockCustomRuntime, "00457f0b-20d8-4f3d-8555-c233c7ebd495", map[string]string{})
	c.consoleNewFuncV2(ctx)

	// 验证返回201状态码，表示创建成功
	assert.Equal(t, 201, ctx.Response().StatusCode())
}

// TestConsoleNewFuncV2_EnvironmentMerge 测试ConsoleNewFuncV2_EnvironmentMerge函数，该函数用于测试环境变量合并逻辑。
func TestConsoleNewFuncV2_EnvironmentMerge(t *testing.T) {
	global.MockAC()
	var m sqlmock.Sqlmock
	m, dbengine.Engine = global.MockDB()
	global.RegisterFaasRuntimeGovalidatorTag()

	// 手动添加custom runtime到RuntimeCache中
	global.AC.Cache.RuntimeCache["custom.ubuntu2204.python3.10"] = getCustomRuntimeConfig()

	// 测试环境变量合并逻辑
	mockCustomRuntimeWithEnv := `{"FunctionName":"test_custom","ServiceName":"bpname_0","MemorySize":128,"Description":"custom runtime test","Handler":"index.handler","Runtime":"custom.ubuntu2204.python3.10","Timeout":20,"CustomRuntimeConfig":{"Port":8080,"Command":["/bin/sh"],"Args":[]},"LangRuntime":"python3.10","Environment":{"Variables":{"USER_VAR":"user_value"}}}`

	c := ConsoleFunctionRest{}

	// 使用自定义的custom runtime blueprint数据
	customBlueprint := getCustomRuntimeBlueprint()
	m.ExpectQuery("SELECT").WillReturnRows(global.GetRowsBlueprints([]dao.Blueprint{customBlueprint}))
	m.ExpectQuery("SELECT").WillReturnRows(global.GetRowsRuntimeConfig([]dao.RuntimeConfig{getCustomRuntimeConfig()}))
	m.ExpectQuery("SELECT").WillReturnRows(global.GetRowsFunctions(global.GetTestFunc(1)))
	m.ExpectQuery("SELECT").WillReturnRows(global.GetRowsFunctions(nil))
	customService := dao.Service{
		Id:          1,
		Uid:         "00457f0b-20d8-4f3d-8555-c233c7ebd495",
		Region:      "bj",
		ServiceName: "bpname_0",
		ServiceDesc: convert.String("desc"),
		ServiceConf: "keywords_0",
		Status:      1,
		UpdatedAt:   time.Now(),
		CreatedAt:   time.Now(),
	}
	m.ExpectQuery("SELECT").WillReturnRows(global.GetRowsService([]dao.Service{customService}))
	m.ExpectQuery("SELECT").WillReturnRows(global.GetRowsService([]dao.Service{customService}))
	m.ExpectBegin()
	m.ExpectExec("^INSERT INTO (.+)").WillReturnResult(sqlmock.NewResult(0, 0))
	m.ExpectCommit()

	ctx := global.BuildNewKunCtx("POST", "/v2/console/functions", mockCustomRuntimeWithEnv, "00457f0b-20d8-4f3d-8555-c233c7ebd495", map[string]string{})
	c.consoleNewFuncV2(ctx)

	// 验证返回201状态码，表示创建成功
	assert.Equal(t, 201, ctx.Response().StatusCode())
}
