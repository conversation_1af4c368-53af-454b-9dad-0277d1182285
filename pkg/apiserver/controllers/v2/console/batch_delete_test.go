package console

import (
	"testing"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/stretchr/testify/assert"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/dbengine"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/global"
	apiServer "icode.baidu.com/baidu/faas/kun/pkg/apiserver/server"
)

func TestBatchDeleteFunc(t *testing.T) {
	global.MockAC()
	var m sqlmock.Sqlmock
	m, dbengine.Engine = global.MockDB()
	for i := 0; i < 3; i++ {
		m.ExpectQuery("SELECT").WillReturnRows(global.GetRowsFunctions(global.GetTestFunc(1)))
		m.ExpectQuery("SELECT").WillReturnRows(global.GetRowsAliases(nil))
		m.ExpectQuery("SELECT").WillReturnRows(global.GetRowsEventSource(nil))
		m.ExpectBegin()
		m.ExpectQuery("SELECT").WillReturnRows(global.GetRowsPolicy(nil))
		m.ExpectQuery("SELECT").WillReturnRows(global.GetRowsUserTestEvents(nil))
		m.ExpectExec("^DELETE (.+)").WillReturnResult(sqlmock.NewResult(0, 0))
		m.ExpectExec("^UPDATE (.+)").WillReturnResult(sqlmock.NewResult(0, 1))
		m.ExpectQuery("SELECT").WillReturnRows(global.GetRowsAliases(nil))
		m.ExpectExec("^DELETE (.+)").WillReturnResult(sqlmock.NewResult(0, 0))
		m.ExpectQuery("SELECT").WillReturnRows(global.GetRowsPolicy(nil))
		m.ExpectExec("^DELETE (.+)").WillReturnResult(sqlmock.NewResult(0, 0))
		m.ExpectCommit()
	}

	mockBodyString := `{"FuncsToDelete": [{"FunctionName": "test22"},{"FunctionName": "test23"},{"FunctionName": "abdcee"}]}`

	cases := []struct {
		in_c         *apiServer.ApiServerContext
		out_HttpCode int
	}{
		{
			in_c:         global.BuildNewApiServerCtx("POST", "/v2/console/batch-delete-functions", "", "", map[string]string{}),
			out_HttpCode: 400,
		},
		{
			in_c:         global.BuildNewApiServerCtx("POST", "/v2/console/batch-delete-functions", mockBodyString, "", map[string]string{}),
			out_HttpCode: 401,
		},
		{
			in_c:         global.BuildNewApiServerCtx("POST", "/v2/console/batch-delete-functions", mockBodyString, "uiduid", map[string]string{}),
			out_HttpCode: 200,
		},
	}
	for i, tc := range cases {
		c := ConsoleFunctionRest{}
		if i == 3 {
			mockDeleteOneFuncSQL(m, 3)
		}
		c.consoleBatchDelete(tc.in_c)
		assert.Equal(t, tc.out_HttpCode, tc.in_c.Response().StatusCode())
	}
}

func mockDeleteOneFuncSQL(m sqlmock.Sqlmock, funcNum int) {
	for i := 0; i < funcNum; i++ {
		m.ExpectQuery("SELECT").WillReturnRows(global.GetRowsFunctions(global.GetTestFunc(1)))
		m.ExpectQuery("SELECT").WillReturnRows(global.GetRowsAliases(nil))
		m.ExpectBegin()
		m.ExpectQuery("SELECT").WillReturnRows(global.GetRowsPolicy(nil))
		m.ExpectExec("^UPDATE (.+)").WillReturnResult(sqlmock.NewResult(0, 1))
		m.ExpectQuery("SELECT").WillReturnRows(global.GetRowsAliases(nil))
		m.ExpectExec("^DELETE (.+)").WillReturnResult(sqlmock.NewResult(0, 0))
		m.ExpectQuery("SELECT").WillReturnRows(global.GetRowsPolicy(nil))
		m.ExpectExec("^DELETE (.+)").WillReturnResult(sqlmock.NewResult(0, 0))
		m.ExpectCommit()
	}
}
