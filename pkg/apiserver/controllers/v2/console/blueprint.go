package console

import (
	"net/http"

	"icode.baidu.com/baidu/faas/kun/pkg/api"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/dao"
	apiErr "icode.baidu.com/baidu/faas/kun/pkg/apiserver/error"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/global"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/models"
	resp "icode.baidu.com/baidu/faas/kun/pkg/apiserver/models/function"
	kunErr "icode.baidu.com/baidu/faas/kun/pkg/error"
	"icode.baidu.com/baidu/faas/kun/pkg/server"
	"icode.baidu.com/baidu/faas/kun/pkg/util/json"
	"icode.baidu.com/baidu/faas/kun/pkg/util/logs"
)

type BlueprintRest struct {
	Path string
}

type ListBlueprintResp struct {
	Blueprints []interface{}
	PageNo     int64 `json:"pageNo"`
	PageSize   int64 `json:"pageSize"`
	Total      int64
}

// 根据搜索关键字获取蓝图列表
func (b BlueprintRest) list(c *server.Context) {
	response := c.Response()
	r := c.Request()
	ctx := global.BuildApiserverContext(c.Request(), c.Response(), "v2_blueprints_list")

	var (
		pageNo, pageSize, count         int64
		blueprints                      = new(ListBlueprintResp)
		blueprintsResSliceTmp, countTmp interface{}
		runtimeStr, name, tagStr        string
		tags                            = make([]string, 0)
		runtimes                        = make([]string, 0)
		err                             error
	)

	runtimeStr = r.QueryParameter("Runtime")
	name = r.QueryParameter("Name")
	tagStr = r.QueryParameter("Tags")
	pageNoStr := r.QueryParameter("pageNo")
	pageSizeStr := r.QueryParameter("pageSize")
	logs.V(6).Infof("request_name=%s, request_runtime=%s, request_tags=%s", name, runtimeStr, tagStr)

	if runtimeStr != "" {
		// Runtime为["python","nodejs"]格式的json串
		if err = json.Unmarshal([]byte(runtimeStr), &runtimes); err != nil {
			respErr := c.WithWarnLog(kunErr.NewInvalidParameterValueException("[param Runtime invalid]", err))
			resp.NewFailResp(respErr).WriteTo(response, respErr)
			return
		}
	}
	if tagStr != "" {
		// Tags为["dueros", "bot"]格式的json串
		if err = json.Unmarshal([]byte(tagStr), &tags); err != nil {
			respErr := c.WithWarnLog(kunErr.NewInvalidParameterValueException("[param Tags invalid]", err))
			resp.NewFailResp(respErr).WriteTo(response, respErr)
			return
		}
	}
	if pageNoStr == "" && pageSizeStr == "" {
		pageNoStr = "1"
		pageSizeStr = "10"
	}

	pageNo, pageSize, _, _, err = models.ParseParams(pageNoStr, pageSizeStr, "", "")
	if err != nil {
		respErr := c.WithWarnLog(kunErr.NewInvalidParameterValueException("[parse pageNo|pageSize fail]", err))
		resp.NewFailResp(respErr).WriteTo(response, respErr)
		return
	}

	condition := &models.ListBlueprintsCond{
		Name:     name,
		Runtimes: runtimes,
		Tags:     tags,
		PageNo:   pageNo,
		PageSize: pageSize,
		Status:   api.BlueprintOnline,
	}
	if blueprintsResSliceTmp, countTmp, err = ctx.Observer.NewStage(global.QueryDatabaseStage).ObserveObjects(models.ListBlueprintsWithConditions(condition)); err != nil {
		respErr := c.WithWarnLog(kunErr.NewServiceException("[Query database failed]", err))
		resp.NewFailResp(respErr).WriteTo(response, respErr)
		return
	}
	blueprintsResSlice := blueprintsResSliceTmp.(*[]dao.Blueprint)
	count = countTmp.(int64)
	blueprints.Blueprints = models.FormatBlueprints(blueprintsResSlice)
	blueprints.PageNo = pageNo
	blueprints.PageSize = pageSize
	blueprints.Total = count

	global.WriteSummary(ctx, api.LogNotToSummary)
	response.WriteHeaderAndEntity(http.StatusOK, resp.NewSuccessResp(blueprints))
}

// 获取蓝图详情，详情结果中包含蓝图代码
func (b BlueprintRest) find(c *server.Context) {
	response := c.Response()
	ctx := global.BuildApiserverContext(c.Request(), c.Response(), "v2_blueprints_find")
	uuid := c.Request().PathParameter("UUID")
	if uuid == "" {
		respErr := c.WithWarnLog(kunErr.NewInvalidParameterValueException("[params invalid, uuid is empty]", nil))
		resp.NewFailResp(respErr).WriteTo(response, respErr)
		return
	}
	logs.V(6).Infof("request_uuid=%s", uuid)

	var (
		resMap       = make(map[string]interface{})
		blueprint    *dao.Blueprint
		rawCode, url string
		err          error
	)

	if blueprint, rawCode, url, err = models.GetBlueprint(uuid, ctx); err != nil {
		respErr := c.WithWarnLog(apiErr.NewResourceNotFoundException("Get blueprint fail, blueprint not found", err))
		resp.NewFailResp(respErr).WriteTo(response, respErr)
		return
	}

	models.DealBlueprint(blueprint)
	resMap["Code"] = api.CodeStorage{Location: url}
	resMap["Blueprint"] = blueprint
	resMap["RawCode"] = rawCode

	global.WriteSummary(ctx, api.LogNotToSummary)
	response.WriteHeaderAndEntity(http.StatusOK, resp.NewSuccessResp(resMap))
}

func (b BlueprintRest) listKeywords(c *server.Context) {
	response := c.Response()
	ctx := global.BuildApiserverContext(c.Request(), c.Response(), "v2_blueprints_listKeywords")

	var (
		resMap                  = make(map[string]interface{})
		keywords, name, runtime []string
		err                     error
	)

	if keywords, name, runtime, err = models.GetBlueprintKeywords(ctx); err != nil {
		respErr := c.WithWarnLog(kunErr.NewServiceException("[Get blueprint keywords fail]", err))
		resp.NewFailResp(respErr).WriteTo(response, respErr)
		return
	}

	resMap["Keywords"] = keywords
	resMap["Name"] = name
	resMap["Runtime"] = runtime

	global.WriteSummary(ctx, api.LogNotToSummary)
	response.WriteHeaderAndEntity(http.StatusOK, resp.NewSuccessResp(resMap))
}

func (b BlueprintRest) getDefaultCode(c *server.Context) {
	response := c.Response()
	ctx := global.BuildApiserverContext(c.Request(), c.Response(), "v2_blueprints_getDefaultCode")

	runtime := c.Request().PathParameter("Runtime")
	var (
		resMap            = make(map[string]interface{})
		rawCode, url      string
		blueprint         *dao.Blueprint
		isExist, editable bool
		err               error
	)

	isExist, editable = models.CheckDefaultRuntime(runtime)
	if runtime == "" || !isExist {
		respErr := c.WithWarnLog(kunErr.NewInvalidParameterValueException("[params invalid, runtime invalid]", nil))
		resp.NewFailResp(respErr).WriteTo(response, respErr)
		return
	}

	if editable {
		bp := &dao.Blueprint{
			Runtime: runtime,
			Status:  api.BlueprintDefault,
		}
		// 查看默认蓝图是否存在
		if rkunErr := ctx.Observer.NewStage(global.FindBlueprintStage).Observe(dao.FindOneBlueprint(bp)); rkunErr != nil {
			respErr := c.WithWarnLog(apiErr.NewResourceNotFoundException("invalid runtime", rkunErr))
			resp.NewFailResp(respErr).WriteTo(response, respErr)
			return
		}

		// 获取默认蓝图代码
		if blueprint, rawCode, url, err = models.GetBlueprint(bp.Uuid, ctx); err != nil {
			respErr := c.WithWarnLog(kunErr.NewServiceException("[Get blueprint default code fail]", err))
			resp.NewFailResp(respErr).WriteTo(response, respErr)
			return
		}
		resMap["Handler"] = blueprint.Handler
		resMap["Code"] = api.CodeStorage{Location: url}
	} else {
		rawCode = "此类型Runtime不支持在线编辑"
	}

	resMap["RawCode"] = rawCode
	global.WriteSummary(ctx, api.LogNotToSummary)
	response.WriteHeaderAndEntity(http.StatusOK, resp.NewSuccessResp(resMap))
}
