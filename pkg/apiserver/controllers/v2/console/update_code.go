// Package console : console v2 api
package console

import (
	"io"
	"io/ioutil"
	"mime/multipart"
	"net/http"
	"os"
	"strconv"
	"sync"
	"time"

	"github.com/google/uuid"

	"icode.baidu.com/baidu/faas/kun/pkg/api"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/controllers/v1/cloudfunc"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/dao"
	apiErr "icode.baidu.com/baidu/faas/kun/pkg/apiserver/error"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/global"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/models"
	resp "icode.baidu.com/baidu/faas/kun/pkg/apiserver/models/function"
	kunErr "icode.baidu.com/baidu/faas/kun/pkg/error"
	"icode.baidu.com/baidu/faas/kun/pkg/server"
	"icode.baidu.com/baidu/faas/kun/pkg/util/convert"
)

const tmpFilePath = "./chunktemp/"

type Repay struct {
	StartTime  time.Time `json:"startTime"`
	EndTime    time.Time `json:"endTime"`
	ChunkSize  int64     `json:"chunkSize"`
	ChunkIndex int       `json:"chunkIndex"`
	Message    string    `json:"message"`
	RetryCount int       `json:"retryCount"`
}
type msg struct {
	Message string `json:"message"`
}

// UpdateCodeReq 更新函数代码
type UpdateCodeReq struct {
	ZipFile multipart.File
}

//console update code
func (f ConsoleFunctionRest) consoleUpdateCode(c *server.Context) {
	response := c.Response()
	r := c.Request()
	ctx := global.BuildApiserverContext(c.Request(), c.Response(), "consoleUpdateCode")
	var (
		updateFuncTmp, codeSha256, codeSize, codeId, squashFsSha256 interface{}
		updateFunc                                                  *dao.Function
		err                                                         error
	)
	if updateFuncTmp, err = ctx.Observer.NewStage(global.InitFunctionStage).ObserveObject(cloudfunc.InitFunction(c.Request())); err != nil {
		c.WithWarnLog(apiErr.NewInitFuncMetaException(err.Error(), "", nil)).WriteTo(response)
		return
	}
	updateFunc = updateFuncTmp.(*dao.Function)
	if updateFunc.Version != "$LATEST" {
		c.WithWarnLog(apiErr.
			NewInitFuncMetaException("only $LATEST version can be updated", "", nil)).
			WriteTo(response)
		return
	}
	updateFunc.Version = "$LATEST"
	updateFunc.CommitID = convert.String(uuid.New().String())
	updateFunc.FunctionBrnInit()

	ctx.Context.FunctionBrn = updateFunc.FunctionBrn
	condFunc := new(dao.Function)
	condFunc.FunctionBrn = updateFunc.FunctionBrn
	total := r.Request.FormValue("chunkTotal")
	byteArray := make([]byte, 0, 512)
	message := ""
	//兼容线上不分片接口，空说明没有分片，走原来的逻辑。1说明只有一个文件，也不进行分片
	if total == "" || total == "1" {
		file, _, err := r.Request.FormFile("file")
		if err != nil {
			c.WithWarnLog(kunErr.NewInvalidRequestContentException("", err)).WriteTo(response)
			return
		}
		defer file.Close()

		updateCodeReq := UpdateCodeReq{
			ZipFile: file,
		}

		byteArray, err = ioutil.ReadAll(updateCodeReq.ZipFile)
		message = "整个文件上传成功"
	} else {
		//分片上传文件:发送6个分片请求，分片接收完毕后，会收到前端的chunkOver字段进行合并
		startTime := time.Now()
		fileSize, retryCount, chunkSize, index, filePath, chunkOver, err := mergeChunk(c, updateFunc.FunctionBrn)
		endTime := time.Now()
		if err != nil {
			//分片文件上传错误
			message = "分片文件上传失败"
			Repays := newRepay(startTime, endTime, chunkSize, index, message, retryCount)
			response.WriteHeaderAndEntity(500, resp.NewFailResp(Repays))
			return
		} else if chunkOver {
			// 整个文件合并成功
			fi, err := os.Stat(filePath)
			if err != nil || fi.Size() != fileSize {
				message = "合并文件失败"
				msg := newMsg(message)
				response.WriteHeaderAndEntity(500, resp.NewFailResp(msg))
				return
			} else {
				message = "整个文件上传成功"
			}
		} else {
			//分片上传成功
			message = "分片文件上传成功"
			Repays := newRepay(startTime, endTime, chunkSize, index, message, retryCount)
			response.WriteHeaderAndEntity(200, resp.NewSuccessResp(Repays))
			return
		}

		file, err := os.Open(filePath)
		if err != nil {
			c.WithWarnLog(kunErr.NewInvalidRequestContentException("", err)).WriteTo(response)
			return
		}
		defer file.Close()
		//结束之后删除文件
		defer os.Remove(filePath)
		byteArray, err = ioutil.ReadAll(file)
	}

	if err != nil {
		c.WithWarnLog(kunErr.
			NewServiceException("read form-data into a byteArray failed", err)).
			WriteTo(response)
		return
	}

	//校验文件类型
	if filetype := http.DetectContentType(byteArray); filetype != "application/zip" {
		c.WithWarnLog(kunErr.
			NewInvalidParameterValueException("invalid code file type", nil)).
			WriteTo(response)
		return
	}

	if err = models.CheckCodeSize("update", updateFunc, byteArray); err != nil {
		c.WithWarnLog(err).WriteTo(response)
		return
	}

	//upload code to bos
	if codeSha256, squashFsSha256, codeSize, codeId, err = ctx.Observer.NewStage(global.UploadCodeStage).ObserveMoreObjects(global.AC.Clients.Code.FaasUploadCode(byteArray, updateFunc.FunctionName, updateFunc.Uid)); err != nil {
		err = models.OptimizeErrorCode("upload code to bos failed", err)
		c.WithErrorLog(err).WriteTo(response)
		return
	}
	updateFunc.CodeSha256 = codeSha256.(string)
	updateFunc.CodeSize = codeSize.(int32)
	updateFunc.CodeID = codeId.(string)
	updateFunc.SquashFsSha256 = squashFsSha256.(*string)

	if rkunErr := ctx.Observer.NewStage(global.UpdateFunctionStage).Observe(dao.UpdateFunc(*condFunc, updateFunc)); rkunErr != nil {
		c.WithErrorLog(rkunErr).WriteTo(response)
		return
	}

	defer global.WriteSummary(ctx, api.LogNotToSummary)
	if total == "" {
		//兼容线上接口
		response.WriteHeaderAndEntity(200, resp.NewSuccessResp(nil))
	} else {
		//最后一个分片上传完毕，并且没有错误
		msg := newMsg(message)
		response.WriteHeaderAndEntity(200, resp.NewSuccessResp(msg))
	}
}

// 合并文件
func mergeChunk(c *server.Context, brn string) (int64, int, int64, int, string, bool, error) {
	r := c.Request().Request
	//创建chunktemp文件夹
	if _, err := fileExists(tmpFilePath); err != nil {
		os.Mkdir(tmpFilePath, os.ModePerm)
	}
	//是否合并
	chunkOver := r.FormValue("chunkOver")
	//文件名
	fileName := r.FormValue("fileName")
	//分片总数
	chunkTotal := r.FormValue("chunkTotal")
	total, err := strconv.Atoi(chunkTotal)
	//文件大小
	fileSize := r.FormValue("fileSize")
	size, err := strconv.ParseInt(fileSize, 10, 64)
	over, _ := strconv.ParseBool(chunkOver)
	// 新文件创建，加上brn命令
	filePath := initFileName(tmpFilePath, brn, fileName)
	// 收到合并标志,进行分片合并成一个文件
	if over {
		var lock sync.WaitGroup
		// 新文件创建，加上brn命令
		fileBool, err := createFile(filePath)
		if !fileBool {
			return size, 0, 0, 0, filePath, false, err
		}
		// 读取文件片段 进行合并
		for i := 0; i < total; i++ {
			lock.Add(1)
			go mergeFile(c, i, filePath, &lock)
		}
		lock.Wait()
		return size, 0, 0, 0, filePath, true, nil
	}

	retryCount := r.FormValue("retryCount")
	retry, err := strconv.Atoi(retryCount)
	retry++
	chunkSize, index, err := chunkUpload(c, filePath)
	if err != nil {
		return size, retry, chunkSize, index, filePath, false, err
	}
	return size, retry, chunkSize, index, filePath, false, nil
}

// 合并切片文件
func mergeFile(c *server.Context, i int, filePath string, lock *sync.WaitGroup) {
	// 打开最终合并的文件
	file, err := os.OpenFile(filePath, os.O_CREATE|os.O_WRONLY, os.ModePerm)
	defer file.Close()
	defer lock.Done()
	if err != nil {
		err = models.OptimizeErrorCode("upload chunkfile failed", err)
		c.WithErrorLog(err)
		return
	}
	// 从第一个分片文件开始，设置偏移量
	fi, _ := os.Stat(filePath + "_0")
	chunkSize := fi.Size()
	// 设置文件写入偏移量
	file.Seek(chunkSize*int64(i), 0)
	//第i个分片文件
	iSize := strconv.Itoa(i)
	chunkFilePath := filePath + "_" + iSize
	chunkFileObj, err := os.Open(chunkFilePath)
	defer chunkFileObj.Close()
	if err != nil {
		err = models.OptimizeErrorCode("upload chunkfile failed", err)
		c.WithErrorLog(err)
		return
	}

	totalLen := 0
	// 写入数据
	data := make([]byte, 1024, 1024)
	for {
		tal, err := chunkFileObj.Read(data)
		if err == io.EOF {
			chunkFileObj.Close()
			err := os.Remove(chunkFilePath)
			if err != nil {
				err = models.OptimizeErrorCode("upload chunkfile failed", err)
				c.WithErrorLog(err)
				return
			}
			break
		}
		len, err := file.Write(data[:tal])
		if err != nil {
			err = models.OptimizeErrorCode("upload chunkfile failed", err)
			c.WithErrorLog(err)
			return
		}
		totalLen += len
	}
}

// 创建分片文件
func chunkUpload(c *server.Context, filePath string) (int64, int, error) {
	r := c.Request().Request
	chunkIndex := r.FormValue("chunkIndex")
	index, _ := strconv.Atoi(chunkIndex)
	upFile, _, err := r.FormFile("file")
	chunkFileSize := r.FormValue("chunkSize")
	chunkSize, _ := strconv.ParseInt(chunkFileSize, 10, 64)
	if err != nil {
		return chunkSize, index, err
	}

	// 新文件创建,以brn唯一标识
	filePath = filePath + "_" + chunkIndex
	fileBool, err := createFile(filePath)
	if !fileBool {
		return chunkSize, index, err
	}
	fi, _ := os.Stat(filePath)
	if fi.Size() < chunkSize {
		start := strconv.Itoa(int(fi.Size()))

		// 进行断点上传，打开之前上传文件
		file, err := os.OpenFile(filePath, os.O_CREATE|os.O_WRONLY, os.ModePerm)
		defer file.Close()
		if err != nil {
			return chunkSize, index, err
		}
		// 将数据写入文件
		count, _ := strconv.ParseInt(start, 10, 64)
		err = uploadFile(upFile, count, file, count)
		return chunkSize, index, err
	}
	return chunkSize, index, err
}

// 写入数据到分片文件
func uploadFile(upfile multipart.File, upSeek int64, file *os.File, fSeek int64) (error) {
	// 上传文件大小记录
	fileSzie := 0
	// 设置上传偏移量
	upfile.Seek(upSeek, 0)
	// 设置文件偏移量
	file.Seek(fSeek, 0)
	data := make([]byte, 1024, 1024)
	for {
		total, err := upfile.Read(data)
		if err == io.EOF {
			break
		}
		len, err := file.Write(data[:total])
		if err != nil {
			return err
		}
		fileSzie += len
	}
	return nil
}

// 创建文件
func createFile(filePath string) (bool, error) {
	fileBool, err := fileExists(filePath)
	if fileBool && err == nil {
		return true, nil
	} else {
		newFile, err := os.Create(filePath)
		defer newFile.Close()
		if err != nil {
			return false, err
		}
	}
	return true, nil
}

// 判断文件或文件夹是否存在
func fileExists(path string) (bool, error) {
	_, err := os.Stat(path)
	if err == nil {
		return true, nil
	}
	if os.IsNotExist(err) {
		return false, err
	}
	return false, err
}

func newRepay(startTime time.Time, endTime time.Time, chunkSize int64, chunkIndex int, message string, retryCount int) *Repay {
	return &Repay{
		StartTime:  startTime,
		EndTime:    endTime,
		ChunkSize:  chunkSize,
		ChunkIndex: chunkIndex,
		Message:    message,
		RetryCount: retryCount,
	}
}
func newMsg(message string) *msg {
	return &msg{
		Message: message,
	}
}

func initFileName(tmpFilePath string, brn string, fileName string) string {
	return tmpFilePath + brn + "_" + fileName
}
