package console

import (
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/DATA-DOG/go-sqlmock"
	bosSdk "github.com/baidubce/bce-sdk-go/services/bos"
	bosApi "github.com/baidubce/bce-sdk-go/services/bos/api"
	"github.com/stretchr/testify/assert"

	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/code"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/dbengine"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/global"
	"icode.baidu.com/baidu/faas/kun/pkg/server"
	"icode.baidu.com/baidu/faas/kun/pkg/util/json"
)

func newClientMock1(c *server.Context) (*bosSdk.Client, error) {
	ts := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
		bytes, _ := json.Marshal(bosApi.ListBucketsResult{})
		w.Write(bytes)
	}))
	b, _ := bosSdk.NewClient("abc", "abc", ts.URL)
	return b, nil
}
func newClientMock(c *server.Context) (*bosSdk.Client, error) {
	ts := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
		bytes := []byte{
			80, 75, 3, 4, 20, 0, 8, 0, 8, 0, 123, 137, 170, 76, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 13, 0, 16, 0, 100, 117, 101, 114, 111, 115, 98, 111, 116, 46, 116,
			120, 116, 85, 88, 12, 0, 217, 12, 244, 90, 217, 12, 244, 90, 246, 239, 186, 158, 75, 41, 77, 45, 202, 47, 6, 0, 80, 75, 7, 8, 177, 111, 113, 99, 8, 0, 0, 0,
			6, 0, 0, 0, 80, 75, 3, 4, 10, 0, 0, 0, 0, 0, 130, 137, 170, 76, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 9, 0, 16, 0, 95, 95, 77, 65, 67, 79, 83, 88, 47, 85, 88,
			12, 0, 227, 12, 244, 90, 227, 12, 244, 90, 246, 239, 186, 158, 80, 75, 3, 4, 20, 0, 8, 0, 8, 0, 123, 137, 170, 76, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 24, 0,
			16, 0, 95, 95, 77, 65, 67, 79, 83, 88, 47, 46, 95, 100, 117, 101, 114, 111, 115, 98, 111, 116, 46, 116, 120, 116, 85, 88, 12, 0, 217, 12, 244, 90, 217, 12, 244,
			90, 246, 239, 186, 158, 99, 96, 21, 99, 103, 96, 98, 96, 240, 77, 76, 86, 240, 15, 86, 136, 80, 128, 2, 144, 24, 3, 39, 16, 27, 49, 48, 48, 238, 0, 210, 64,
			62, 227, 43, 6, 162, 128, 99, 72, 72, 16, 132, 5, 210, 193, 104, 1, 100, 108, 66, 83, 194, 2, 21, 231, 103, 96, 16, 79, 206, 207, 213, 75, 44, 40, 200, 73, 213,
			11, 73, 173, 40, 113, 205, 75, 206, 79, 201, 204, 75, 135, 232, 119, 7, 18, 2, 12, 12, 82, 8, 53, 57, 137, 197, 37, 165, 197, 169, 41, 41, 137, 37, 169, 202, 1,
			193, 80, 123, 194, 129, 132, 22, 3, 131, 10, 66, 93, 110, 106, 73, 34, 80, 77, 162, 85, 124, 182, 175, 139, 103, 73, 106, 110, 104, 113, 106, 81, 72, 98, 122, 49,
			88, 125, 35, 144, 200, 100, 96, 48, 199, 162, 30, 168, 220, 39, 49, 41, 53, 39, 190, 188, 212, 40, 191, 194, 44, 181, 56, 53, 171, 180, 52, 167, 52, 199, 44, 183,
			176, 36, 173, 48, 41, 49, 49, 35, 23, 168, 185, 180, 36, 77, 215, 194, 218, 208, 216, 196, 200, 208, 220, 210, 194, 228, 18, 207, 151, 40, 144, 193, 157, 167, 98,
			69, 64, 116, 82, 65, 78, 102, 113, 137, 129, 193, 2, 14, 168, 3, 25, 161, 30, 135, 209, 48, 192, 249, 233, 136, 123, 83, 193, 165, 64, 193, 85, 223, 190, 218, 177,
			207, 205, 72, 255, 118, 87, 151, 113, 78, 188, 215, 234, 164, 169, 159, 52, 206, 243, 76, 125, 177, 233, 227, 35, 151, 143, 167, 79, 196, 156, 173, 158, 207, 230,
			155, 253, 213, 130, 173, 150, 231, 140, 128, 63, 155, 208, 85, 221, 210, 221, 29, 123, 31, 214, 197, 28, 241, 156, 51, 209, 123, 207, 177, 67, 154, 46, 210, 57, 7,
			254, 184, 253, 114, 191, 86, 49, 171, 113, 167, 197, 226, 159, 165, 158, 30, 49, 39, 230, 229, 29, 255, 156, 234, 188, 227, 211, 249, 107, 0, 80, 75, 7, 8, 211, 146,
			124, 33, 92, 1, 0, 0, 234, 1, 0, 0, 80, 75, 1, 2, 21, 3, 20, 0, 8, 0, 8, 0, 123, 137, 170, 76, 177, 111, 113, 99, 8, 0, 0, 0, 6, 0, 0, 0, 13, 0, 12, 0, 0, 0, 0, 0, 0,
			0, 0, 64, 164, 129, 0, 0, 0, 0, 100, 117, 101, 114, 111, 115, 98, 111, 116, 46, 116, 120, 116, 85, 88, 8, 0, 217, 12, 244, 90, 217, 12, 244, 90, 80, 75, 1, 2, 21, 3,
			10, 0, 0, 0, 0, 0, 130, 137, 170, 76, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 9, 0, 12, 0, 0, 0, 0, 0, 0, 0, 0, 64, 253, 65, 83, 0, 0, 0, 95, 95, 77, 65, 67, 79, 83, 88,
			47, 85, 88, 8, 0, 227, 12, 244, 90, 227, 12, 244, 90, 80, 75, 1, 2, 21, 3, 20, 0, 8, 0, 8, 0, 123, 137, 170, 76, 211, 146, 124, 33, 92, 1, 0, 0, 234, 1, 0, 0, 24, 0,
			12, 0, 0, 0, 0, 0, 0, 0, 0, 64, 164, 129, 138, 0, 0, 0, 95, 95, 77, 65, 67, 79, 83, 88, 47, 46, 95, 100, 117, 101, 114, 111, 115, 98, 111, 116, 46, 116, 120, 116, 85,
			88, 8, 0, 217, 12, 244, 90, 217, 12, 244, 90, 80, 75, 5, 6, 0, 0, 0, 0, 3, 0, 3, 0, 220, 0, 0, 0, 60, 2, 0, 0, 0, 0,
		}
		w.Write(bytes)
	}))

	b, _ := initBosClient(ts.URL)
	return b, nil
}

func bosManagerWrapperMock(bosClient *bosSdk.Client, bucketName string) code.ObjectStorageInterface {
	return &code.MockBos{}
}

func TestListBuckets(t *testing.T) {
	global.MockAC()
	Getter = newClientMock1

	cases := []struct {
		in_c         *server.Context
		out_HttpCode int
	}{
		{
			in_c:         global.BuildNewKunCtxWithHeaders1("GET", "/v2/console/functions/bos", "", "uiduid", map[string]string{}),
			out_HttpCode: 200,
		},
		{
			in_c:         global.BuildNewKunCtxWithHeaders1("GET", "/v2/console/functions/bos", "test body", "uiduid", map[string]string{}),
			out_HttpCode: 200,
		},
		{
			in_c: global.BuildNewKunCtxWithHeaders("GET", "/v2/console/functions/bos", "", "uiduid", map[string]string{}, map[string]string{
				"X-Test": "test",
			}),
			out_HttpCode: 200,
		},
		{
			in_c: global.BuildNewKunCtxWithHeaders("GET", "/v2/console/functions/bos", "test body", "uiduid", map[string]string{}, map[string]string{
				"X-Test": "test",
			}),
			out_HttpCode: 200,
		},
	}
	for _, tc := range cases {
		c := ConsoleFunctionRest{}
		c.listBuckets(tc.in_c)
		assert.Equal(t, tc.out_HttpCode, tc.in_c.Response().StatusCode())
	}
}

func TestConsoleUpdateCodeFromBos(t *testing.T) {
	global.MockAC()
	code.MockCode()
	Getter = newClientMock

	cases := []struct {
		in_c         *server.Context
		out_HttpCode int
		sqlFunc      func()
	}{
		{
			in_c: global.BuildNewKunCtxWithHeaders1("POST", "/v2/console/functions/bos/myfunc/code", "err Json BODY", "uiduid", map[string]string{
				"FunctionName": "myfunc:$LATEST",
			}),
			out_HttpCode: 400,
		},
		{
			in_c:         global.BuildNewKunCtxWithHeaders1("POST", "/v2/console/functions/bos/myfunc/code", `{"BosBucket":"hjj-bucket-test","BosObject":"index.py.zip"}`, "uiduid", map[string]string{}),
			out_HttpCode: 400,
		},
		{
			in_c:         global.BuildNewKunCtxWithHeaders1("POST", "/v2/console/functions/bos/myfunc/code", `{"BosBucket":"hjj-bucket-test","BosObject":"index.py.zip"}`, "", map[string]string{}),
			out_HttpCode: 400,
		},
		{
			in_c: global.BuildNewKunCtx("POST", "/v2/console/functions/bos/myfunc/code?Qualifier=$LATEST", `{"BosBucket":"hjj-bucket-test","BosObject":"index.py.zip"}`, "uiduid", map[string]string{
				"FunctionName": "myfunc:$LATEST",
			}),
			out_HttpCode: 200,
			sqlFunc: func() {
				var m sqlmock.Sqlmock
				m, dbengine.Engine = global.MockDB()
				m.ExpectQuery("SELECT").WillReturnRows(global.GetRowsFunctions(global.GetTestFunc(1)))
				m.ExpectExec("UPDATE").WillReturnResult(sqlmock.NewResult(1, 1))
				m.ExpectQuery("SELECT").WillReturnRows(global.GetRowsFunctions(global.GetTestFunc(1)))
				m.ExpectQuery("SELECT").WillReturnRows(global.GetRowsFunctions(global.GetTestFunc(1)))
			},
		},
		{
			in_c:         global.BuildNewKunCtx("POST", "/2/console/functions/bos/myfunc/code?Qualifier=$LATEST", `{"BosBucket":"test"}`, "uiduid", map[string]string{}),
			out_HttpCode: 400,
		},
		{
			in_c: global.BuildNewKunCtx("POST", "/v2/console/functions/bos/myfunc/code?Qualifier=$LATEST", `{"BosBucket":"","BosObject":""}`, "uiduid", map[string]string{
				"FunctionName": "myfunc:$LATEST",
			}),
			out_HttpCode: 400,
		},
	}
	for _, tc := range cases {
		c := ConsoleFunctionRest{}
		if tc.sqlFunc != nil {
			tc.sqlFunc()
		}
		c.consoleUpdateCodeFromBos(tc.in_c)
		assert.Equal(t, tc.out_HttpCode, tc.in_c.Response().StatusCode())
	}
}

func initBosClient(endpoint string) (*bosSdk.Client, error) {
	return bosSdk.NewClient("abc", "abc", endpoint)
}
