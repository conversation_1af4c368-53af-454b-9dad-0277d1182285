package console

import (
	"icode.baidu.com/baidu/faas/kun/pkg/api"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/global"
	"net/http"

	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/controllers/v1/cloudfunc"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/dao"
	apiErr "icode.baidu.com/baidu/faas/kun/pkg/apiserver/error"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/models"
	resp "icode.baidu.com/baidu/faas/kun/pkg/apiserver/models/function"
	apiServer "icode.baidu.com/baidu/faas/kun/pkg/apiserver/server"
	kunErr "icode.baidu.com/baidu/faas/kun/pkg/error"
	"icode.baidu.com/baidu/faas/kun/pkg/util/json"
)

// BatchDeleteFuncRequest 批量删除函数
type BatchDeleteFuncRequest struct {
	FuncsToDelete []dao.Function
}

// BatchDeleteError 批量删除函数错误
type BatchDeleteError struct {
	Cause        string `json:"Cause"`
	FunctionName string `json:"FunctionName"`
}

// console批量删除
func (f *ConsoleFunctionRest) consoleBatchDelete(c *apiServer.ApiServerContext) {
	//POST /v2/console/batch_delete_functions
	response := c.Response()
	r := c.Request().Request
	defer r.Body.Close()

	ctx := global.BuildApiserverContext(c.Request(), c.Response(), "consoleBatchDelete")
	decoder := json.NewDecoder(r.Body)
	funcsToDelete := new(BatchDeleteFuncRequest)
	if err := decoder.Decode(funcsToDelete); err != nil {
		respErr := c.WithWarnLog(kunErr.NewInvalidRequestContentException(err.Error(), nil))
		resp.NewFailResp(respErr).WriteTo(response, respErr)
		return
	}

	user, err := cloudfunc.GetUser(c.Request())
	if err != nil {
		respErr := c.WithWarnLog(apiErr.NewUserNotFoundException("", err))
		resp.NewFailResp(respErr).WriteTo(response, respErr)
		return
	}

	var faiList []interface{}
	for _, fn := range funcsToDelete.FuncsToDelete {
		var (
			functionTmp interface{}
			function    *dao.Function
			err         error
		)
		if functionTmp, err = ctx.Observer.NewStage(global.InitFunctionStage).ObserveObject(models.InitUserFunc(user.Domain.ID, fn.FunctionName, "", fn.WorkspaceID)); err != nil {
			respErr := c.WithErrorLog(apiErr.NewInitFuncMetaException(err.Error(), "", nil))
			resp.NewFailResp(respErr).WriteTo(response, respErr)
			return
		}
		function = functionTmp.(*dao.Function)

		if errInDelFunc := cloudfunc.DeleteOne(function, c, ctx); errInDelFunc != nil {
			deleteErr := c.WithErrorLog(errInDelFunc)
			faiList = append(faiList, BatchDeleteError{
				Cause:        deleteErr.Message,
				FunctionName: function.FunctionName,
			})
		}
	}

	if len(faiList) > 0 {
		failMsg := make(map[string]interface{})
		failMsg["faiList"] = faiList
		// TODO: respond properly according to the err above
		response.WriteHeaderAndEntity(http.StatusBadRequest, resp.NewFailResp(failMsg))
		return
	}

	defer global.WriteSummary(ctx, api.LogNotToSummary)
	response.WriteHeaderAndEntity(http.StatusOK, resp.NewSuccessResp(nil))
}
