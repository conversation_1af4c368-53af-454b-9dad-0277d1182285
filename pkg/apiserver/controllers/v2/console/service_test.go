package console

import (
	"bytes"
	"github.com/DATA-DOG/go-sqlmock"
	"github.com/emicklei/go-restful"
	"github.com/stretchr/testify/assert"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/code"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/dbengine"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/global"
	"icode.baidu.com/baidu/faas/kun/pkg/bce/iam"
	"icode.baidu.com/baidu/faas/kun/pkg/server"
	"icode.baidu.com/baidu/faas/kun/pkg/util/json"
	"net/http"
	"net/http/httptest"
	"strings"
	"testing"
)

func TestServiceList(t *testing.T) {
	global.MockAC()

	cases := []struct {
		in_c         *server.Context
		out_HttpCode int
	}{
		{
			in_c:         global.BuildNewKunCtx("GET", "/v1/service?pageFrom=serviceList", ``, "uid", map[string]string{}),
			out_HttpCode: 200,
		},
	}

	for _, tc := range cases {
		var m sqlmock.Sqlmock
		m, dbengine.Engine = global.MockDB()
		m.ExpectQuery(".*").WillReturnRows(global.GetRowsService(global.GetTestService(1)))
		m.ExpectQuery(".*").WillReturnRows(global.GetRowsService(global.GetTestService(2)))
		s := ServiceRest{}
		s.list(tc.in_c)
		assert.Equal(t, tc.out_HttpCode, tc.in_c.Response().StatusCode())
	}
}

func TestServiceFind(t *testing.T) {
	global.MockAC()
	code.MockCode()

	cases := []struct {
		in_c         *server.Context
		out_HttpCode int
	}{
		{
			in_c:         global.BuildNewKunCtx("GET", "/v1/service/serviceName/detail", ``, "uid", map[string]string{}),
			out_HttpCode: 200,
		},
	}
	for _, tc := range cases {
		var m sqlmock.Sqlmock
		m, dbengine.Engine = global.MockDB()
		m.ExpectQuery(".*").WillReturnRows(global.GetRowsService(global.GetTestService(1)))

		s := ServiceRest{}
		s.find(tc.in_c)
		assert.Equal(t, tc.out_HttpCode, tc.in_c.Response().StatusCode())
	}
}

func TestCreateService(t *testing.T) {
	global.MockAC()
	code.MockCode()
	global.RegisterFaasRuntimeGovalidatorTag()

	var m sqlmock.Sqlmock
	m, dbengine.Engine = global.MockDB()

	// dao.CreateService
	req3 := "INSERT INTO"
	m.ExpectExec(req3).
		WillReturnResult(sqlmock.NewResult(1, 1))

	cr := map[string]string{
		"ServiceName": "ServiceName",
		"ServiceDesc": "ServiceDesc",
		"ServiceConf": "ServiceConf",
		"region": "bj",
	}

	body := new(bytes.Buffer)
	json.NewEncoder(body).Encode(cr)
	bodyReader := strings.NewReader(body.String())
	request := httptest.NewRequest("POST", "/v1/service", bodyReader)
	request.Header.Add("Content-Type", restful.MIME_JSON)
	restReq := restful.NewRequest(request)

	domain := iam.Domain{
		ID:   "85ae1423f09f4660becb15d46402e9cd",
		Name: "PASSPORT:2500402428",
	}
	iamuser := iam.User{
		ID:     "85ae1423f09f4660becb15d46402e9cd",
		Name:   "PASSPORT:2500402428",
		Domain: &domain,
	}
	restReq.SetAttribute("User", &iamuser)

	response := httptest.NewRecorder()
	restRsp := restful.NewResponse(response)
	restRsp.SetRequestAccepts(restful.MIME_JSON)

	s := ServiceRest{}
	s.create(server.BuildContext(restReq, restRsp, ""))

	if response.Code != http.StatusCreated {
		t.Error(1)
	}
}

func TestUpdateService(t *testing.T) {
	global.MockAC()
	code.MockCode()
	global.RegisterFaasRuntimeGovalidatorTag()
	var m sqlmock.Sqlmock
	m, dbengine.Engine = global.MockDB()

	cases := []struct {
		in_c         *server.Context
		out_HttpCode int
		sqlFunc      func()
	}{
		{
			in_c:         global.BuildNewKunCtx("PUT", "/service/myservice", "err Json BODY", "uiduid", map[string]string{}),
			out_HttpCode: 400,
		},
		{
			in_c:         global.BuildNewKunCtx("PUT", "/service/myservice", `{"ServiceName":"name", "ServiceDesc":"desc", "Uid":"uid","ServiceConf":"", "Status":1}`, "uid", map[string]string{}),
			out_HttpCode: 200,
			sqlFunc: func() {
				m.ExpectQuery("SELECT").WillReturnRows(global.GetRowsService(global.GetTestService(1)))
				req1 := "SELECT * FROM `functions`  WHERE (`functions`.`uid` = ?) AND (deleted_at IS NULL OR deleted_at='0000-00-00 00:00:00') ORDER BY `functions`.`id` ASC LIMIT 1"
				m.ExpectQuery(global.FixedFullRe(req1)).
					WillReturnRows(global.GetRowsService(nil))
				m.ExpectExec("UPDATE").WillReturnResult(sqlmock.NewResult(1, 1))
			},
		},
		// deprecated runtime
		{
			in_c:         global.BuildNewKunCtx("PUT", "/service/myservice", `{"ServiceName":"name", "ServiceName":"desc"}`, "uid", map[string]string{}),
			out_HttpCode: 404,
			sqlFunc: func() {
				m.ExpectQuery("SELECT").WillReturnRows(global.GetRowsFunctions(nil))
			},
		},
	}
	for k, tc := range cases {
		f := ServiceRest{}
		if tc.sqlFunc != nil {
			tc.sqlFunc()
		}
		f.update(tc.in_c)
		assert.Equal(t, tc.out_HttpCode, tc.in_c.Response().StatusCode())
		t.Logf("k %d response %+v", k, tc.in_c.Response().ResponseWriter)
	}
}

func TestDeleteService(t *testing.T) {
	global.MockAC()
	code.MockCode()
	global.RegisterFaasRuntimeGovalidatorTag()
	var m sqlmock.Sqlmock
	m, dbengine.Engine = global.MockDB()

	cases := []struct {
		in_c         *server.Context
		out_HttpCode int
		sqlFunc      func()
	}{
		{
			in_c:         global.BuildNewKunCtx("DELETE", "/service/myservice", "err Json BODY", "uiduid", map[string]string{}),
			out_HttpCode: 404,
		},
		{
			in_c:         global.BuildNewKunCtx("DELETE", "/service/myservice", `{"ServiceName":"name", "ServiceName":"desc"}`, "uid", map[string]string{}),
			out_HttpCode: 200,
			sqlFunc: func() {
				m.ExpectQuery("SELECT").WillReturnRows(global.GetRowsService(global.GetTestService(1)))
				m.ExpectExec("UPDATE").WillReturnResult(sqlmock.NewResult(1, 1))
			},
		},
		// deprecated runtime
		{
			in_c:         global.BuildNewKunCtx("DELETE", "/service/myservice", `{"ServiceName":"name", "ServiceName":"desc"}`, "uid", map[string]string{}),
			out_HttpCode: 404,
			sqlFunc: func() {
				m.ExpectQuery("SELECT").WillReturnRows(global.GetRowsFunctions(nil))
			},
		},
	}
	for k, tc := range cases {
		f := ServiceRest{}
		if tc.sqlFunc != nil {
			tc.sqlFunc()
		}
		f.delete(tc.in_c)
		assert.Equal(t, tc.out_HttpCode, tc.in_c.Response().StatusCode())
		t.Logf("k %d response %+v", k, tc.in_c.Response().ResponseWriter)
	}
}
