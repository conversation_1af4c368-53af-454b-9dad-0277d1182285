package console

import (
	"errors"
	"testing"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/stretchr/testify/assert"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/dbengine"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/global"
	"icode.baidu.com/baidu/faas/kun/pkg/server"
)

func TestConsoleQueryLegal(t *testing.T) {
	global.MockAC()

	cases := []struct {
		in_c         *server.Context
		out_HttpCode int
	}{
		{
			in_c:         global.BuildNewKunCtx("GET", "/v2/console/legalStatusCheck", "", "abc", map[string]string{}),
			out_HttpCode: 500,
		},
		{
			in_c:         global.BuildNewKunCtx("GET", "/v2/console/legalStatusCheck", "", "abc", map[string]string{}),
			out_HttpCode: 200,
		},
		{
			in_c:         global.BuildNewKunCtx("GET", "/v2/console/legalStatusCheck", "", "abc", map[string]string{}),
			out_HttpCode: 200,
		},
		{
			in_c:         global.BuildNewKunCtx("GET", "/v2/console/legalStatusCheck", "", "abc", map[string]string{}),
			out_HttpCode: 200,
		},
	}
	for i, tc := range cases {
		var m sqlmock.Sqlmock
		m, dbengine.Engine = global.MockDB()
		if i == 0{
			m.ExpectExec(".*").WillReturnResult(sqlmock.NewErrorResult(errors.New("#1062 - Duplicate entry")))
		}else if i == 1 {
			m.ExpectQuery(".*").WillReturnRows(global.GetRowsLegal(nil))
		} else if i == 2 {
			m.ExpectQuery(".*").WillReturnRows(global.GetRowsLegal(global.GetTestLegalStatus(1)))
		}
		c := ConsoleFunctionRest{}
		c.consoleQueryLegal(tc.in_c)
		assert.Equal(t, tc.out_HttpCode, tc.in_c.Response().StatusCode())
	}
}
func TestConsoleNewLegal(t *testing.T) {
	global.MockAC()

	cases := []struct {
		in_c         *server.Context
		out_HttpCode int
	}{
		{
			in_c:         global.BuildNewKunCtx("POST", "/v2/console/increaseLegal", "", "abc", map[string]string{}),
			out_HttpCode: 500,
		},
		{
			in_c:         global.BuildNewKunCtx("POST", "/v2/console/increaseLegal", "", "abc", map[string]string{}),
			out_HttpCode: 201,
		},
		{
			in_c:         global.BuildNewKunCtx("POST", "/v2/console/increaseLegal", "", "abc", map[string]string{}),
			out_HttpCode: 201,
		},
		{
			in_c:         global.BuildNewKunCtx("POST", "/v2/console/increaseLegal", "", "abc", map[string]string{}),
			out_HttpCode: 201,
		},
	}
	for i, tc := range cases {
		var m sqlmock.Sqlmock
		m, dbengine.Engine = global.MockDB()
		if i == 0 {
			m.ExpectExec(".*").WillReturnResult(sqlmock.NewErrorResult(errors.New("#1062 - Duplicate entry")))
		} else if i == 1 {
			m.ExpectQuery(".*").WillReturnRows(global.GetRowsLegal(global.GetTestLegalStatus(1)))
			m.ExpectExec(".*").WillReturnResult(sqlmock.NewResult(1, 1))
		} else if i == 2 {
			m.ExpectQuery(".*").WillReturnRows(global.GetRowsLegal(nil))
			m.ExpectExec(".*").WillReturnResult(sqlmock.NewResult(1, 1))
		} else if i == 3 {
			m.ExpectExec(".*").WillReturnResult(sqlmock.NewResult(1, 1))
		}

		c := ConsoleFunctionRest{}
		c.consoleNewLegal(tc.in_c)
		assert.Equal(t, tc.out_HttpCode, tc.in_c.Response().StatusCode())
	}
}
