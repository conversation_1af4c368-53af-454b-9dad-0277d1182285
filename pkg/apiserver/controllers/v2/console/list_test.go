package console

import (
	"testing"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/stretchr/testify/assert"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/code"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/dbengine"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/global"
	"icode.baidu.com/baidu/faas/kun/pkg/server"
)

func TestFindByConditionV2(t *testing.T) {
	global.MockAC()
	code.MockCode()

	cases := []struct {
		in_c         *server.Context
		out_HttpCode int
	}{
		{
			in_c:         global.BuildNewKunCtx("GET", "/v2/console/functions", "", "", map[string]string{}),
			out_HttpCode: 400,
		},
		{
			in_c:         global.BuildNewKunCtx("GET", "/v2/console/functions?pageNo=a", ``, "uiduid", map[string]string{}),
			out_HttpCode: 400,
		},
		{
			in_c:         global.BuildNewKunCtx("GET", "/v2/console/functions", "", "uiduid", map[string]string{}),
			out_HttpCode: 200,
		},
		{
			in_c:         global.BuildNewKunCtx("GET", "/v2/console/functions?pageSize=1", ``, "uiduid", map[string]string{}),
			out_HttpCode: 200,
		},
		{
			in_c:         global.BuildNewKunCtx("GET", "/v2/console/functions?pageNo=1", ``, "uiduid", map[string]string{}),
			out_HttpCode: 200,
		},
		{
			in_c:         global.BuildNewKunCtx("GET", "/v2/console/functions?pageSize=10&pageNo=2", ``, "uiduid", map[string]string{}),
			out_HttpCode: 200,
		},
		{
			in_c:         global.BuildNewKunCtx("GET", "/v2/console/functions?runtime=python2", ``, "uiduid", map[string]string{}),
			out_HttpCode: 200,
		},
		{
			in_c:         global.BuildNewKunCtx("GET", "/v2/console/functions?orderBy=CodeSize&order=DESC", ``, "uiduid", map[string]string{}),
			out_HttpCode: 200,
		},
		{
			in_c:         global.BuildNewKunCtx("GET", "/v2/console/functions?searchFN=test", ``, "uiduid", map[string]string{}),
			out_HttpCode: 200,
		},
	}
	for _, tc := range cases {
		var m sqlmock.Sqlmock
		m, dbengine.Engine = global.MockDB()
		m.ExpectQuery(".*").WillReturnRows(global.GetRowsFunctions(global.GetTestFunc(1)))
		m.ExpectQuery(".*").WillReturnRows(global.GetRowsFunctions(global.GetTestFunc(2)))
		c := ConsoleFunctionRest{}

		c.findByConditionV2(tc.in_c)
		assert.Equal(t, tc.out_HttpCode, tc.in_c.Response().StatusCode())
	}
}

func TestFindByConditionCbd(t *testing.T) {
	global.MockAC()
	code.MockCode()

	cases := []struct {
		in_c         *server.Context
		out_HttpCode int
	}{
		{
			in_c:         global.BuildNewKunCtx("GET", "/v2/console/cbd/functions", "", "uiduid", map[string]string{}),
			out_HttpCode: 200,
		},
		{
			in_c:         global.BuildNewKunCtx("GET", "/v2/console/cbd/functions?FunctionNamePrefix=cdveotrcoq_", "", "", map[string]string{}),
			out_HttpCode: 400,
		},
		{
			in_c:         global.BuildNewKunCtx("GET", "/v2/console/cbd/functions?FunctionNamePrefix=cdveotrcoq_&pageSize=a", "", "uiduid", map[string]string{}),
			out_HttpCode: 400,
		},
		{
			in_c:         global.BuildNewKunCtx("GET", "/v2/console/cbd/functions?FunctionNamePrefix=cdveotrcoq_", "", "uiduid", map[string]string{}),
			out_HttpCode: 200,
		},
		{
			in_c:         global.BuildNewKunCtx("GET", "/v2/console/cbd/functions?FunctionNamePrefix=cdveotrcoq_&pageSize=1", ``, "uiduid", map[string]string{}),
			out_HttpCode: 200,
		},
		{
			in_c:         global.BuildNewKunCtx("GET", "/v2/console/cbd/functions?FunctionNamePrefix=cdveotrcoq_&pageNo=1", ``, "uiduid", map[string]string{}),
			out_HttpCode: 200,
		},
		{
			in_c:         global.BuildNewKunCtx("GET", "/v2/console/cbd/functions?FunctionNamePrefix=cdveotrcoq_&pageSize=10&pageNo=2", ``, "uiduid", map[string]string{}),
			out_HttpCode: 200,
		},
		{
			in_c:         global.BuildNewKunCtx("GET", "/v2/console/cbd/functions?FunctionNamePrefix=cdveotrcoq_&Marker=1&MaxItems=10", ``, "uiduid", map[string]string{}),
			out_HttpCode: 200,
		},
		{
			in_c:         global.BuildNewKunCtx("GET", "/v2/console/cbd/functions?FunctionNamePrefix=cdveotrcoq_&Filter=FunctionName:test,Description:test", ``, "uiduid", map[string]string{}),
			out_HttpCode: 200,
		},
		{
			in_c:         global.BuildNewKunCtx("GET", "/v2/console/cbd/functions?FunctionNamePrefix=cdveotrcoq_&Order=FunctionName,Description:desc", ``, "uiduid", map[string]string{}),
			out_HttpCode: 200,
		},
		{
			in_c:         global.BuildNewKunCtx("GET", "/v2/console/cbd/functions?FunctionNamePrefix=cdveotrcoq_&Order=FunctionName:asc,Description:desc", ``, "uiduid", map[string]string{}),
			out_HttpCode: 200,
		},
	}
	for _, tc := range cases {
		var m sqlmock.Sqlmock
		m, dbengine.Engine = global.MockDB()
		m.ExpectQuery(".*").WillReturnRows(global.GetRowsFunctions(global.GetTestFunc(1)))
		m.ExpectQuery(".*").WillReturnRows(global.GetRowsFunctions(global.GetTestFunc(2)))
		c := ConsoleFunctionRest{}
		c.findByConditionCbd(tc.in_c)
		assert.Equal(t, tc.out_HttpCode, tc.in_c.Response().StatusCode())
	}
}

func TestIsFunctionExist(t *testing.T) {
	global.MockAC()
	cases := []struct {
		in_c         *server.Context
		out_HttpCode int
	}{
		{
			in_c:         global.BuildNewKunCtx("GET", "/v2/console/functions/myFunc/is-exist", ``, "", map[string]string{}),
			out_HttpCode: 400,
		},
		{
			in_c:         global.BuildNewKunCtx("GET", "/v2/console/functions/myFunc/is-exist", ``, "uiduid", map[string]string{}),
			out_HttpCode: 200,
		},
	}

	for _, tc := range cases {
		var m sqlmock.Sqlmock
		m, dbengine.Engine = global.MockDB()
		m.ExpectQuery(".*").WillReturnRows(global.GetRowsFunctions(global.GetTestFunc(1)))
		c := ConsoleFunctionRest{}
		c.isFunctionExist(tc.in_c)
		assert.Equal(t, tc.out_HttpCode, tc.in_c.Response().StatusCode())
	}

}
