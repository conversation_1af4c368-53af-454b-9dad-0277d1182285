package console

import (
	"fmt"
	"net/http"
	"time"

	"github.com/asaskevich/govalidator"
	"github.com/emicklei/go-restful"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/controllers/v1/cloudfunc"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/dao"
	apiErr "icode.baidu.com/baidu/faas/kun/pkg/apiserver/error"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/models"
	resp "icode.baidu.com/baidu/faas/kun/pkg/apiserver/models/function"
	kunErr "icode.baidu.com/baidu/faas/kun/pkg/error"

	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/global"

	"icode.baidu.com/baidu/faas/kun/pkg/api"
	"icode.baidu.com/baidu/faas/kun/pkg/server"
)

type ServiceRest struct {
	Path string
}

// req body
type ServiceReq struct {
	dao.Service
}

func (f ServiceRest) list(c *server.Context) {
	response := c.Response()

	ctx := global.BuildApiserverContext(c.Request(), c.Response(), "serviceList")
	var newServiceTmp interface{}
	var err error
	if newServiceTmp, err = ctx.Observer.NewStage(global.InitServiceStage).ObserveObject(initService(c.Request())); err != nil {
		c.WithWarnLog(apiErr.NewInitFuncMetaException(err.Error(), "", err)).WriteTo(response)
		return
	}

	s := newServiceTmp.(*dao.Service)

	var (
		svsTmp interface{}
		resMap = make(map[string]interface{})
	)
	if svsTmp, err = ctx.Observer.NewStage(global.QueryDatabaseStage).ObserveObject(dao.ListServices(s)); err != nil {
		c.WithWarnLog(err).WriteTo(response)
		return
	}
	svs := svsTmp.(*[]dao.Service)
	sPage := c.Request().QueryParameter("pageFrom")
	if sPage == "serviceList" {
		var resObj interface{}
		if resObj, err = ctx.Observer.NewStage(global.QueryDatabaseStage).ObserveObject(dao.CountUserServiceFun(s.Uid, s.Region)); err != nil {
			c.WithWarnLog(err).WriteTo(response)
		}
		resMap["result"] = models.FormatServicesWithCountFun(svs, resObj)
	} else {
		resMap["result"] = models.FormatServices(svs)
	}
	defer global.WriteSummary(ctx, api.LogNotToSummary)

	response.WriteHeaderAndEntity(http.StatusOK, resp.NewSuccessResp(resMap))
}

func (f ServiceRest) find(c *server.Context) {
	response := c.Response()

	ctx := global.BuildApiserverContext(c.Request(), c.Response(), "serviceFind")
	var newServiceTmp interface{}
	var err error
	if newServiceTmp, err = ctx.Observer.NewStage(global.InitServiceStage).ObserveObject(initService(c.Request())); err != nil {
		c.WithWarnLog(apiErr.NewInitFuncMetaException(err.Error(), "", err)).WriteTo(response)
		return
	}
	s := newServiceTmp.(*dao.Service)

	var (
		svsTmp interface{}
	)
	if err := models.CheckPtrString(&s.ServiceName, 0, 50); err != nil {
		c.WithWarnLog(kunErr.NewInvalidParameterValueException("Server Name length is Illegal", err)).WriteTo(response)
		return
	}
	if svsTmp, err = ctx.Observer.NewStage(global.QueryDatabaseStage).ObserveObject(dao.ListServices(s)); err != nil {
		c.WithWarnLog(err).WriteTo(response)
		return
	}
	svs := svsTmp.(*[]dao.Service)
	svsRes := models.FormatServices(svs)

	defer global.WriteSummary(ctx, api.LogNotToSummary)

	if len(svsRes) > 0 {
		response.WriteHeaderAndEntity(http.StatusOK, resp.NewSuccessResp(svsRes[0]))
	}
	return
}

func (f ServiceRest) create(c *server.Context) {
	response := c.Response()
	serviceReq := new(ServiceReq)
	var err error
	var newServiceTmp interface{}
	if err = c.Request().ReadEntity(serviceReq); err != nil {
		c.WithWarnLog(kunErr.NewInvalidRequestContentException("", err)).WriteTo(response)
		return
	}

	ctx := global.BuildApiserverContext(c.Request(), c.Response(), "serviceCreate")

	if newServiceTmp, err = ctx.Observer.NewStage(global.InitServiceStage).ObserveObject(initService(c.Request())); err != nil {
		c.WithWarnLog(apiErr.NewInitFuncMetaException(err.Error(), "", err)).WriteTo(response)
		return
	}
	newService := newServiceTmp.(*dao.Service)
	newService.ServiceName = serviceReq.ServiceName

	// 加锁
	lock := fmt.Sprintf("%s_cfc_service", newService.Uid)
	acquired, err := global.AC.Clients.Redis.Redis().SetNX(lock, 1, 10*time.Second).Result()

	defer global.AC.Clients.Redis.Redis().Del(lock)

	if err != nil {
		c.WithWarnLog(kunErr.NewServiceException("something wrong when creating the service", nil)).WriteTo(response)
		return
	}

	if !acquired {
		c.WithWarnLog(kunErr.NewTooManyRequestsException("too many requests to create the services", nil)).WriteTo(response)
		return
	}

	if rkunErr := validateService("create", newService); rkunErr != nil {
		c.WithWarnLog(rkunErr).WriteTo(response)
		return
	}

	if rkunErr := validateService("create", newService); rkunErr != nil {
		c.WithWarnLog(rkunErr).WriteTo(response)
		return
	}

	//查找是否存在同服务名
	if rKunErr := ctx.Observer.NewStage(global.CheckServiceStage).Observe(dao.FindOneService(newService)); rKunErr == nil && newService.ServiceName != "" {
		c.WithWarnLog(apiErr.NewResourceConflictException("service exists : "+newService.ServiceName, nil)).WriteTo(response)
		return
	}

	newService.ServiceDesc = serviceReq.ServiceDesc
	newService.ServiceConf = serviceReq.ServiceConf

	if err := ctx.Observer.NewStage(global.CreateServiceStage).Observe(dao.CreateService(newService)); err != nil {
		c.WithWarnLog(err).WriteTo(response)
	}
	response.WriteHeaderAndEntity(http.StatusCreated, resp.NewSuccessResp(newService))
}

func (f ServiceRest) update(c *server.Context) {
	response := c.Response()
	serviceReq := new(ServiceReq)
	var err error
	var newServiceTmp interface{}
	if err = c.Request().ReadEntity(serviceReq); err != nil {
		c.WithWarnLog(kunErr.NewInvalidRequestContentException("", err)).WriteTo(response)
		return
	}

	ctx := global.BuildApiserverContext(c.Request(), c.Response(), "serviceUpdate")

	if newServiceTmp, err = ctx.Observer.NewStage(global.InitServiceStage).ObserveObject(initService(c.Request())); err != nil {
		c.WithWarnLog(apiErr.NewInitFuncMetaException(err.Error(), "", err)).WriteTo(response)
		return
	}
	udtService := newServiceTmp.(*dao.Service)

	condService := new(dao.Service)
	condService.ServiceName = udtService.ServiceName
	condService.Uid = udtService.Uid
	condService.Region = udtService.Region

	//查找是否存在服务
	if rKunErr := ctx.Observer.NewStage(global.CheckServiceStage).Observe(dao.FindOneService(condService)); rKunErr != nil {
		c.WithWarnLog(apiErr.NewServiceNotFoundException("service not exists : "+condService.ServiceName, nil)).WriteTo(response)
		return
	}

	if rkunErr := validateService("update", udtService); rkunErr != nil {
		c.WithWarnLog(rkunErr).WriteTo(response)
		return
	}

	udtService.ServiceDesc = serviceReq.ServiceDesc
	udtService.ServiceConf = serviceReq.ServiceConf

	if err := ctx.Observer.NewStage(global.UpdateServiceStage).Observe(dao.UpdateService(*condService, udtService)); err != nil {
		c.WithWarnLog(err).WriteTo(response)
	}
	response.WriteHeaderAndEntity(http.StatusOK, resp.NewSuccessResp(udtService))
}

func (f ServiceRest) delete(c *server.Context) {
	response := c.Response()
	var err error
	var newServiceTmp interface{}

	ctx := global.BuildApiserverContext(c.Request(), c.Response(), "serviceCreate")

	if newServiceTmp, err = ctx.Observer.NewStage(global.InitServiceStage).ObserveObject(initService(c.Request())); err != nil {
		c.WithWarnLog(apiErr.NewInitFuncMetaException(err.Error(), "", err)).WriteTo(response)
		return
	}
	delService := newServiceTmp.(*dao.Service)

	if err := models.CheckPtrString(&delService.ServiceName, 0, 50); err != nil {
		c.WithWarnLog(kunErr.NewInvalidParameterValueException("Server Name length is Illegal", err)).WriteTo(response)
		return
	}

	condService := new(dao.Service)
	condService.ServiceName = delService.ServiceName
	condService.Uid = delService.Uid
	condService.Region = delService.Region
	//查找是否存在服务
	if rKunErr := ctx.Observer.NewStage(global.CheckServiceStage).Observe(dao.FindOneService(condService)); rKunErr != nil {
		c.WithWarnLog(apiErr.NewServiceNotFoundException("service not exists : "+delService.ServiceName, nil)).WriteTo(response)
		return
	}

	//查找是否存在函数
	findFunc := new(dao.Function)
	findFunc.ServiceName = delService.ServiceName
	findFunc.Uid = delService.Uid
	findFunc.Version = "$LATEST"
	findFunc.SourceTag = ""
	findFunc.Region = delService.Region
	if rKunErr := ctx.Observer.NewStage(global.CheckFunctionStage).Observe(dao.FindOneFunc(findFunc)); rKunErr == nil && findFunc.FunctionName != "" {
		c.WithWarnLog(apiErr.DeleteServiceNotEmpty("service function exists : "+findFunc.ServiceName, nil)).WriteTo(response)
		return
	}

	delService.Status = api.ServiceDelete
	if err := ctx.Observer.NewStage(global.UpdateServiceStage).Observe(dao.UpdateService(*condService, delService)); err != nil {
		c.WithWarnLog(err).WriteTo(response)
	}
	response.WriteHeaderAndEntity(http.StatusOK, resp.NewSuccessResp(delService))
}

func initService(rest *restful.Request) (*dao.Service, error) {
	//可选

	sName := rest.PathParameter("ServiceName")
	if sName == "" {
		sName = rest.QueryParameter("serviceName")
	}

	user, err := cloudfunc.GetUser(rest)
	if err != nil {
		return nil, err
	}

	return models.InitUserService(user.Domain.ID, sName)
}

func validateService(vtype string, serviceReq *dao.Service) error {
	if _, err := govalidator.ValidateStruct(serviceReq); err != nil {
		return kunErr.NewInvalidParameterValueException("Validate Struct Failed", err)
	} else if err := models.CheckPtrString(serviceReq.ServiceDesc, 0, 300); err != nil {
		return kunErr.NewInvalidParameterValueException("Description length is Illegal", err)
	} else if err := models.CheckPtrString(&serviceReq.ServiceName, 0, 50); err != nil {
		return kunErr.NewInvalidParameterValueException("Server Name length is Illegal", err)
	}

	if vtype == "create" {
		var total int
		total, err := dao.CountUserService(serviceReq.Uid, serviceReq.Region)
		if total >= api.MaxServicePerUser {
			return apiErr.CreateServiceExceedMaximum("Maximum Service for per user is "+fmt.Sprint(api.MaxServicePerUser), err)
		}
	}
	return nil

}
