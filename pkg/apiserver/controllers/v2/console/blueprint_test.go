package console

import (
	"crypto/tls"
	"net/http"
	"testing"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/stretchr/testify/assert"

	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/code"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/dbengine"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/global"
	"icode.baidu.com/baidu/faas/kun/pkg/server"
)

func TestList(t *testing.T) {
	global.MockAC()
	cases := []struct {
		in_c         *server.Context
		out_HttpCode int
	}{
		{
			in_c:         global.BuildNewKunCtx("GET", "/v2/blueprints", ``, "uiduid", map[string]string{}),
			out_HttpCode: 200,
		},
		{
			in_c:         global.BuildNewKunCtx("GET", "/v2/blueprints?pageNo=a", ``, "uiduid", map[string]string{}),
			out_HttpCode: 400,
		},
		{
			in_c:         global.BuildNewKunCtx("GET", "/v2/blueprints?Runtime=[\"python\",\"nodejs\"\\]&Tags=[\"bot\",\"python\"]&Name=bp", ``, "uiduid", map[string]string{}),
			out_HttpCode: 400,
		},
		{
			in_c:         global.BuildNewKunCtx("GET", "/v2/blueprints?Runtime=[\"python\",\"nodejs\"]&Tags=[\"bot\",\"python\"\\],&Name=bp", ``, "uiduid", map[string]string{}),
			out_HttpCode: 400,
		},
		{
			in_c:         global.BuildNewKunCtx("GET", "/v2/blueprints", ``, "uiduid", map[string]string{}),
			out_HttpCode: 500,
		},
		{
			in_c:         global.BuildNewKunCtx("GET", "/v2/blueprints?Runtime=[\"python\",\"nodejs\"]&Tags=[\"bot\",\"python\"]&Name=bp", ``, "uiduid", map[string]string{}),
			out_HttpCode: 200,
		},
	}

	for i, tc := range cases {
		var m sqlmock.Sqlmock
		m, dbengine.Engine = global.MockDB()
		if i == 0 || i == 5 {
			m.ExpectQuery(".*").WillReturnRows(global.GetRowsBlueprints(global.GetTestBlueprint(1)))
			m.ExpectQuery(".*").WillReturnRows(global.GetRowsBlueprints(global.GetTestBlueprint(2)))
		}
		b := BlueprintRest{}
		b.list(tc.in_c)
		assert.Equal(t, tc.out_HttpCode, tc.in_c.Response().StatusCode())
	}
}

func TestFind(t *testing.T) {
	global.MockAC()
	code.MockCode()

	cases := []struct {
		in_c         *server.Context
		out_HttpCode int
	}{
		{
			in_c:         global.BuildNewKunCtx("GET", "/v2/blueprints", ``, "uiduid", map[string]string{"UUID": "00457f0b-20d8-4f3d-8555-c233c7ebd495"}),
			out_HttpCode: 200,
		},
		{
			// UUID为空
			in_c:         global.BuildNewKunCtx("GET", "/v2/blueprints", ``, "uiduid", map[string]string{"UUID": ""}),
			out_HttpCode: 400,
		},
		{
			// get blueprint fail
			in_c:         global.BuildNewKunCtx("GET", "/v2/blueprints", ``, "uiduid", map[string]string{"UUID": "00457f0b-20d8-4f3d-8555-c233c7ebd495"}),
			out_HttpCode: 404,
		},
	}
	for i, tc := range cases {
		var m sqlmock.Sqlmock
		m, dbengine.Engine = global.MockDB()
		if i == 0 {
			m.ExpectQuery(".*").WillReturnRows(global.GetRowsBlueprints(global.GetTestBlueprint(1)))
		}

		b := BlueprintRest{}
		tlsClientConfig := http.DefaultTransport.(*http.Transport).TLSClientConfig
		http.DefaultTransport.(*http.Transport).TLSClientConfig = &tls.Config{InsecureSkipVerify: true}
		b.find(tc.in_c)

		http.DefaultTransport.(*http.Transport).TLSClientConfig = tlsClientConfig
		assert.Equal(t, tc.out_HttpCode, tc.in_c.Response().StatusCode())
	}
}

func TestListKeywords(t *testing.T) {
	global.MockAC()

	cases := []struct {
		in_c         *server.Context
		out_HttpCode int
	}{
		{
			in_c:         global.BuildNewKunCtx("GET", "/v2/keywords", ``, "uiduid", map[string]string{}),
			out_HttpCode: 500,
		},
		{
			in_c:         global.BuildNewKunCtx("GET", "/v2/keywords", ``, "uiduid", map[string]string{}),
			out_HttpCode: 200,
		},
	}

	for i, tc := range cases {
		var m sqlmock.Sqlmock
		m, dbengine.Engine = global.MockDB()
		if i == 1 {
			m.ExpectQuery(".*").WillReturnRows(global.GetRowsBlueprints(global.GetTestBlueprint(1)))
		}
		b := BlueprintRest{}
		b.listKeywords(tc.in_c)
		assert.Equal(t, tc.out_HttpCode, tc.in_c.Response().StatusCode())
	}
}

func TestGetDefaultCode(t *testing.T) {
	global.MockAC()
	code.MockCode()

	cases := []struct {
		in_c         *server.Context
		out_HttpCode int
	}{
		{
			in_c:         global.BuildNewKunCtx("GET", "/v2/default/blueprint", ``, "uiduid", map[string]string{"Runtime": "python2"}),
			out_HttpCode: 200,
		},
		{
			// Runtime为空
			in_c:         global.BuildNewKunCtx("GET", "/v2/default/blueprint", ``, "uiduid", map[string]string{}),
			out_HttpCode: 400,
		},
		{
			// 蓝图为空
			in_c:         global.BuildNewKunCtx("GET", "/v2/default/blueprint", ``, "uiduid", map[string]string{"Runtime": "python2"}),
			out_HttpCode: 404,
		},
		{
			// 蓝图为空
			in_c:         global.BuildNewKunCtx("GET", "/v2/default/blueprint", ``, "uiduid", map[string]string{"Runtime": "python2"}),
			out_HttpCode: 500,
		},
		{
			in_c:         global.BuildNewKunCtx("GET", "/v2/default/blueprint", ``, "", map[string]string{"Runtime": "java8"}),
			out_HttpCode: 200,
		},
	}
	for i, tc := range cases {
		var m sqlmock.Sqlmock
		m, dbengine.Engine = global.MockDB()
		if i != 2 && i != 3 {
			m.ExpectQuery(".*").WillReturnRows(global.GetRowsBlueprints(global.GetTestBlueprint(1)))
			m.ExpectQuery(".*").WillReturnRows(global.GetRowsBlueprints(global.GetTestBlueprint(1)))
		} else if i == 3 {
			m.ExpectQuery(".*").WillReturnRows(global.GetRowsBlueprints(global.GetTestBlueprint(1)))
		}
		b := BlueprintRest{}
		b.getDefaultCode(tc.in_c)
		assert.Equal(t, tc.out_HttpCode, tc.in_c.Response().StatusCode())
	}
}
