package console

import (
	"icode.baidu.com/baidu/faas/kun/pkg/api"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/dao"
	apiErr "icode.baidu.com/baidu/faas/kun/pkg/apiserver/error"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/global"
	resp "icode.baidu.com/baidu/faas/kun/pkg/apiserver/models/function"
	"icode.baidu.com/baidu/faas/kun/pkg/bce/iam"
	"icode.baidu.com/baidu/faas/kun/pkg/server"
	"net/http"
)

//新增法务签约
func (f ConsoleFunctionRest) consoleNewLegal(c *server.Context) {
	response := c.Response()
	r := c.Request().Request
	defer r.Body.Close()
	ctx := global.BuildApiserverContext(c.Request(), c.Response(), "consoleNewLegal")
	requestUser, _ := c.Request().Attribute("User").(*iam.User)
	accountId := requestUser.Domain.ID
	var resMap = make(map[string]interface{})
	resMap["signed"] = true
	status, err := dao.GetLegal(global.AC.Clients.Redis, accountId)
	if err != nil {
		c.WithErrorLog(err)
	} else if status == 1 {
		//redis查到用户已签约
		response.WriteHeaderAndEntity(http.StatusCreated, resp.NewSuccessResp(resMap))
		return
	}
	newLegalStatus := new(dao.LegalStatus)
	newLegalStatus.AccountId = accountId
	//数据库兜底查询，用户已签约
	legalTemp, err := ctx.Observer.NewStage(global.QueryLegal).ObserveObject(dao.FindLegal(newLegalStatus))
	if err != nil || legalTemp == nil {
		respErr := c.WithWarnLog(apiErr.CreateLegalException("invalid LegalStatus", err))
		resp.NewFailResp(respErr).WriteTo(response, respErr)
		return
	}
	legalStatus := legalTemp.(*[]dao.LegalStatus)
	if len(*legalStatus) == 0 {
		//用户记录未存在，新增记录
		newLegalStatus.Status = 1
		if rkunErr := ctx.Observer.NewStage(global.CreateLegal).Observe(dao.CreateLegal(newLegalStatus)); rkunErr != nil {
			respErr := c.WithWarnLog(apiErr.CreateLegalException("invalid LegalStatus", rkunErr))
			resp.NewFailResp(respErr).WriteTo(response, respErr)
			return
		}
	} else if (*legalStatus)[0].Status != 1 {
		//用户记录已存在,未签约，更新记录
		newLegalStatus.Status = 1
		newLegalStatus.Id = (*legalStatus)[0].Id
		newLegalStatus.CreatedAt = (*legalStatus)[0].CreatedAt
		newLegalStatus.RealNameStatus = (*legalStatus)[0].RealNameStatus
		if rkunErr := ctx.Observer.NewStage(global.UpdateLegal).Observe(dao.UpdateLegal(&(*legalStatus)[0], newLegalStatus)); rkunErr != nil {
			respErr := c.WithWarnLog(apiErr.UpdateLegalException("Update user lagal status error", rkunErr))
			resp.NewFailResp(respErr).WriteTo(response, respErr)
			return
		}
	}

	//redis 设置key
	err = dao.SetLegal(global.AC.Clients.Redis, accountId, 1)
	if err != nil {
		c.WithErrorLog(err)
	}
	defer global.WriteSummary(ctx, api.LogNotToSummary)
	response.WriteHeaderAndEntity(http.StatusCreated, resp.NewSuccessResp(resMap))
}

//查询法务签约
func (f ConsoleFunctionRest) consoleQueryLegal(c *server.Context) {
	response := c.Response()
	r := c.Request().Request
	defer r.Body.Close()
	ctx := global.BuildApiserverContext(c.Request(), c.Response(), "consoleQueryLegal")
	requestUser, _ := c.Request().Attribute("User").(*iam.User)
	var resMap = make(map[string]interface{})
	resMap["open"] = true
	accountId := requestUser.Domain.ID
	status, err := dao.GetLegal(global.AC.Clients.Redis, accountId)
	if err != nil {
		c.WithErrorLog(err)
	} else if status == 1 {
		//redis查到用户已签约
		resMap["open"] = false
		response.WriteHeaderAndEntity(http.StatusOK, resp.NewSuccessResp(resMap))
		return
	}
	newLegalStatus := new(dao.LegalStatus)
	newLegalStatus.AccountId = accountId
	newLegalStatus.Status = 1
	legalTemp, err := ctx.Observer.NewStage(global.QueryLegal).ObserveObject(dao.FindLegal(newLegalStatus))
	if err != nil || legalTemp == nil {
		respErr := c.WithWarnLog(apiErr.NewLegalNotFoundException("can not found LegalStatus", err))
		resp.NewFailResp(respErr).WriteTo(response, respErr)
		return
	}
	legalStatus := legalTemp.(*[]dao.LegalStatus)
	//用户已签约
	if len(*legalStatus) == 1 {
		resMap["open"] = false
		//如果是从数据库查出来，加入到redis
		err = dao.SetLegal(global.AC.Clients.Redis, accountId, 1)
		if err != nil {
			c.WithErrorLog(err)
		}
	}
	defer global.WriteSummary(ctx, api.LogNotToSummary)
	response.WriteHeaderAndEntity(http.StatusOK, resp.NewSuccessResp(resMap))
}
