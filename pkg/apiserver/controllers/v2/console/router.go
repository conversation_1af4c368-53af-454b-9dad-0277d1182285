package console

import (
	"github.com/emicklei/go-restful"
	"icode.baidu.com/baidu/faas/kun/pkg/api"

	"icode.baidu.com/baidu/faas/kun/cmd/apiserver/filter"
	apiServer "icode.baidu.com/baidu/faas/kun/pkg/apiserver/server"
	"icode.baidu.com/baidu/faas/kun/pkg/server"
	"icode.baidu.com/baidu/faas/kun/pkg/server/endpoint"
)

// ConsoleFunctionRest xxx
type ConsoleFunctionRest struct{}

// ConsoleAPIs xxx Version2
func (f ConsoleFunctionRest) ConsoleV2APIs() []endpoint.ApiSingle {
	apis := []endpoint.ApiSingle{
		// create function, POST /v2/console/functions
		endpoint.ApiSingle{
			Verb:    "POST",
			Path:    "/console/functions",
			Handler: server.WrapRestRouteFunc(f.consoleNewFuncV2),
			Filters: []restful.FilterFunction{
				filter.IAMPermissionCheck(api.Resource, "", []string{"cfc:CreateFunctions", filter.IamPermWrite}),
				filter.RealNameCheck,
				filter.BillingCheck,
				filter.CheckCFCWhiteList(),
			},
		},
		endpoint.ApiSingle{
			Verb:    "GET",
			Path:    "/console/legalStatusCheck",
			Handler: server.WrapRestRouteFunc(f.consoleQueryLegal),
			Filters: []restful.FilterFunction{
				filter.IAMPermissionCheck(api.Resource, "", []string{"cfc:QuerylegalStatus", filter.IamPermWrite}),
			},
		},
		endpoint.ApiSingle{
			Verb:    "GET",
			Path:    "/console/realNameQualification",
			Handler: server.WrapRestRouteFunc(f.consoleQueryRealName),
			Filters: []restful.FilterFunction{
				filter.IAMPermissionCheck(api.Resource, "", []string{"cfc:QueryRealNameQualification", filter.IamPermWrite}),
			},
		},
		endpoint.ApiSingle{
			Verb:    "POST",
			Path:    "/console/increaseLegal",
			Handler: server.WrapRestRouteFunc(f.consoleNewLegal),
			Filters: []restful.FilterFunction{
				filter.IAMPermissionCheck(api.Resource, "", []string{"cfc:CreateLegal", filter.IamPermWrite}),
			},
		},
		// update function code, POST /v2/console/functions/{FunctionName}
		endpoint.ApiSingle{
			Verb:    "POST",
			Path:    "/console/functions/{FunctionName}/code",
			Handler: server.WrapRestRouteFunc(f.consoleUpdateCode),
			Filters: []restful.FilterFunction{
				filter.IAMPermissionCheck(api.Resource, "", []string{"cfc:UpdateFunctionCode", filter.IamPermWrite}),
			},
		},
		// list function, GET /v2/functions
		endpoint.ApiSingle{
			Verb:    "GET",
			Path:    "/console/functions",
			Handler: server.WrapRestRouteFunc(f.findByConditionV2),
			Filters: []restful.FilterFunction{
				filter.IAMPermissionCheck(api.Resource, "", []string{"cfc:ListFunctions", filter.IamPermList}),
				server.WrapRestFilterFunction(filter.ValidatorQueryParameter),
			},
		},
		// list user buckets GET /console/functions/bos
		endpoint.ApiSingle{
			Verb:    "GET",
			Path:    "/console/functions/bos",
			Handler: server.WrapRestRouteFunc(f.listBuckets),
			Filters: []restful.FilterFunction{
				filter.IAMPermissionCheck("*", "", []string{"cfc:ListBuckets", filter.IamPermList}),
			},
		},
		// download code from user BOS POST /console/functions/bos/{FunctionNames}/code
		endpoint.ApiSingle{
			Verb:    "POST",
			Path:    "/console/functions/{FunctionName}/code-bos",
			Handler: server.WrapRestRouteFunc(f.consoleUpdateCodeFromBos),
			Filters: []restful.FilterFunction{
				filter.IAMPermissionCheck("*", "", []string{"cfc:UpdateFunctionCode", filter.IamPermWrite}),
			},
		},
		//batch delete functions, POST /v2/console/batch_delete_functions
		endpoint.ApiSingle{
			Verb:    "POST",
			Path:    "/console/batch-delete-functions",
			Handler: apiServer.WrapRestRouteFunc(f.consoleBatchDelete),
			Filters: []restful.FilterFunction{
				filter.IAMPermissionCheck(api.Resource, "", []string{"cfc:DeleteFunction", filter.IamPermWrite}),
			},
		},
		// create cbd function, POST /v2/console/cbd/functions
		endpoint.ApiSingle{
			Verb:    "POST",
			Path:    "/console/cbd/functions",
			Handler: server.WrapRestRouteFunc(f.consoleNewCbdFunc),
			Filters: []restful.FilterFunction{
				filter.IAMPermissionCheck("*", "", []string{"cfc:CreateFunctions", filter.IamPermWrite}),
				filter.BillingCheck,
				filter.CheckCFCWhiteList(),
			},
		},
		// list cbd function, GET /v2/functions
		endpoint.ApiSingle{
			Verb:    "GET",
			Path:    "/console/cbd/functions",
			Handler: server.WrapRestRouteFunc(f.findByConditionCbd),
			Filters: []restful.FilterFunction{
				filter.IAMPermissionCheck("*", "", []string{"cfc:ListFunctions", filter.IamPermList}),
				server.WrapRestFilterFunction(filter.ValidatorCbdQueryParameter),
			},
		},

		// check function , GET /v2/console/functions/{FunctionName}/is-exist
		endpoint.ApiSingle{
			Verb:    "GET",
			Path:    "/console/functions/{FunctionName}/is-exist",
			Handler: server.WrapRestRouteFunc(f.isFunctionExist),
			Filters: []restful.FilterFunction{
				filter.IAMPermissionCheck("*", "", []string{"cfc:IsFunctionExist", filter.IamPermWrite}),
			},
		},
	}
	return apis
}

func (b BlueprintRest) ConsoleAPIs() []endpoint.ApiSingle {
	apis := []endpoint.ApiSingle{
		// 高级蓝图功能
		// list blueprints, GET /v2/blueprints
		endpoint.ApiSingle{
			Verb:    "GET",
			Path:    "/blueprints",
			Handler: server.WrapRestRouteFunc(b.list),
			Filters: []restful.FilterFunction{
				filter.IAMPermissionCheck("*", "", []string{"cfc:ListBlueprints", filter.IamPermList}),
			},
		},
		// get blueprints, GET /v2/blueprints/{UUID}
		endpoint.ApiSingle{
			Verb:    "GET",
			Path:    "/blueprints/{UUID}",
			Handler: server.WrapRestRouteFunc(b.find),
			Filters: []restful.FilterFunction{
				filter.IAMPermissionCheck("*", "", []string{"cfc:FindBlueprint", filter.IamPermList}),
			},
		},
		// get blueprints, GET /v2/keywords
		endpoint.ApiSingle{
			Verb:    "GET",
			Path:    "/keywords",
			Handler: server.WrapRestRouteFunc(b.listKeywords),
			Filters: []restful.FilterFunction{
				filter.IAMPermissionCheck("*", "", []string{"cfc:ListBlueprintKeywords", filter.IamPermList}),
			},
		},
		// get default function code, GET /v2/default/blueprint/{Runtime}
		endpoint.ApiSingle{
			Verb:    "GET",
			Path:    "/default/blueprint/{Runtime}",
			Handler: server.WrapRestRouteFunc(b.getDefaultCode),
			Filters: []restful.FilterFunction{
				filter.IAMPermissionCheck("*", "", []string{"cfc:GetDefaultCode", filter.IamPermList}),
			},
		},
	}
	return apis
}

// PublicAPIs xxx
func (s ServiceRest) ConsoleAPIs() []endpoint.ApiSingle {
	apis := []endpoint.ApiSingle{

		endpoint.ApiSingle{
			Verb:    "GET",
			Path:    "/service",
			Handler: server.WrapRestRouteFunc(s.list),
			Filters: []restful.FilterFunction{
				filter.IAMPermissionCheck(api.Resource, "", []string{"cfc:ListService", filter.IamPermList}),
			},
		},
		endpoint.ApiSingle{
			Verb:    "GET",
			Path:    "/service/detail",
			Handler: server.WrapRestRouteFunc(s.find),
			Filters: []restful.FilterFunction{
				filter.IAMPermissionCheck(api.Resource, "", []string{"cfc:FindService", filter.IamPermRead}),
			},
		},
		endpoint.ApiSingle{
			Verb:    "POST",
			Path:    "/service",
			Handler: server.WrapRestRouteFunc(s.create),
			Filters: []restful.FilterFunction{
				filter.IAMPermissionCheck(api.Resource, "", []string{"cfc:CreateService", filter.IamPermWrite}),
				server.WrapRestFilterFunction(filter.ValidatorQueryParameter),
			},
		},
		endpoint.ApiSingle{
			Verb:    "PUT",
			Path:    "/service",
			Handler: server.WrapRestRouteFunc(s.update),
			Filters: []restful.FilterFunction{
				filter.IAMPermissionCheck(api.Resource, "", []string{"cfc:updateService", filter.IamPermWrite}),
				server.WrapRestFilterFunction(filter.ValidatorQueryParameter),
			},
		},
		endpoint.ApiSingle{
			Verb:    "DELETE",
			Path:    "/service",
			Handler: server.WrapRestRouteFunc(s.delete),
			Filters: []restful.FilterFunction{
				filter.IAMPermissionCheck(api.Resource, "", []string{"cfc:DeleteService", filter.IamPermWrite}),
			},
		},
	}
	return apis
}
