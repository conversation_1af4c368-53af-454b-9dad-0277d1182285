package console

import (
	"net/http"

	"github.com/google/uuid"

	"icode.baidu.com/baidu/faas/kun/pkg/api"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/controllers/v1/cloudfunc"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/dao"
	apiErr "icode.baidu.com/baidu/faas/kun/pkg/apiserver/error"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/global"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/models"
	resp "icode.baidu.com/baidu/faas/kun/pkg/apiserver/models/function"
	"icode.baidu.com/baidu/faas/kun/pkg/bce/bos"
	"icode.baidu.com/baidu/faas/kun/pkg/server"
	"icode.baidu.com/baidu/faas/kun/pkg/util/convert"
)

/*
*	Request Body格式
*	{
*		BosBucket : "bucketKey",
*		BosObject :	"objectKey"
*	}
 */
type consoleRequest struct {
	BosBucket string
	BosObject string
}

// bos.NewClient封装
var Getter = bos.NewClient

/*
*	bucket列表函数
*	GET方法用于获取用户bos中bucket列表
 */

func (f ConsoleFunctionRest) listBuckets(c *server.Context) {
	response := c.Response()
	ctx := global.BuildApiserverContext(c.Request(), c.Response(), "listBuckets")
	bosClient, err := Getter(c)
	if err != nil {
		c.WithWarnLog(apiErr.NewInitBosClientException(err.Error(), "", nil)).WriteTo(response)
		return
	}

	bucketList, err := bosClient.ListBuckets()
	if err != nil {
		c.WithWarnLog(err).WriteTo(response)
		return
	}
	// 根据X-Region信息过滤buckets
	var list []string
	location := c.Request().HeaderParameter("X-Region")
	for _, b := range bucketList.Buckets {
		if b.Location == location {
			list = append(list, b.Name)
		}
	}
	defer global.WriteSummary(ctx, api.LogNotToSummary)
	response.WriteHeaderAndEntity(http.StatusOK, resp.NewSuccessResp(list))
}

/*
*	从用户BOS下载代码，并且上传到CFC BOS
*	Request Body格式
*	{
*		BosBucket:  "bucketKey",
*		BosObject:	"objectKey"
*	}
*	Response格式
*	{
*		success: "true",
*		result:	null
*	}
 */
func (f ConsoleFunctionRest) consoleUpdateCodeFromBos(c *server.Context) {
	response := c.Response()

	ctx := global.BuildApiserverContext(c.Request(), c.Response(), "consoleUpdateCodeFromBos")
	var (
		updateFuncTmp, codeSha256, codeSize, codeId, squashFsSha256 interface{}
		updateFunc                                                  *dao.Function
		err                                                         error
	)

	if updateFuncTmp, err = ctx.Observer.NewStage(global.InitFunctionStage).ObserveObject(cloudfunc.InitFunction(c.Request())); err != nil {
		c.WithWarnLog(apiErr.NewInitFuncMetaException(err.Error(), "", nil)).WriteTo(response)
		return
	}
	updateFunc = updateFuncTmp.(*dao.Function)
	if updateFunc.Version != "$LATEST" {
		c.WithWarnLog(apiErr.NewInitFuncMetaException("only $LATEST version can be updated", "", nil)).WriteTo(response)
		return
	}

	updateFunc.Version = "$LATEST"
	updateFunc.CommitID = convert.String(uuid.New().String())
	updateFunc.FunctionBrnInit()
	ctx.Context.FunctionBrn = updateFunc.FunctionBrn
	condFunc := new(dao.Function)
	condFunc.FunctionBrn = updateFunc.FunctionBrn

	req := consoleRequest{}
	c.Request().ReadEntity(&req)

	// 请求中必须同时包含bucketKey以及objectKey
	if len(req.BosBucket) == 0 || len(req.BosObject) == 0 {
		c.WithWarnLog(apiErr.NewMissingParametersException("BosBucket or BosObject is empty", nil)).WriteTo(response)
		return
	}

	bosClient, err := Getter(c)
	if err != nil {
		c.WithWarnLog(apiErr.NewInitBosClientException("", "Init User BOS client failed", nil)).WriteTo(response)
		return
	}

	byteArray, err := cloudfunc.DownloadCodeFromUserBos(bosClient, req.BosBucket, req.BosObject)
	if err != nil {
		c.WithWarnLog(err).WriteTo(response)
		return
	}

	if err = models.CheckCodeSize("update", updateFunc, byteArray); err != nil {
		c.WithWarnLog(err).WriteTo(response)
		return
	}
	//upload code to bos
	if codeSha256, squashFsSha256, codeSize, codeId, err = ctx.Observer.NewStage(global.UploadCodeStage).
		ObserveMoreObjects(global.AC.Clients.Code.FaasUploadCode(byteArray, updateFunc.FunctionName, updateFunc.Uid)); err != nil {
		err = models.OptimizeErrorCode("upload code to bos from user bos failed", err)
		c.WithErrorLog(err).WriteTo(response)
		return
	}
	updateFunc.CodeSha256 = codeSha256.(string)
	updateFunc.CodeSize = codeSize.(int32)
	updateFunc.CodeID = codeId.(string)
	updateFunc.SquashFsSha256 = squashFsSha256.(*string)
	if rkunErr := ctx.Observer.NewStage(global.UpdateFunctionStage).Observe(dao.UpdateFunc(*condFunc, updateFunc)); rkunErr != nil {
		c.WithErrorLog(rkunErr).WriteTo(response)
		return
	}
	defer global.WriteSummary(ctx, api.LogNotToSummary)
	response.WriteHeaderAndEntity(200, resp.NewSuccessResp(nil))
}
