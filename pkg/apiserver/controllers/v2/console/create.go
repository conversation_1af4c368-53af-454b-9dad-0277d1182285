package console

import (
	"net/http"
	"strings"

	"icode.baidu.com/baidu/faas/kun/pkg/api"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/controllers/v1/cloudfunc"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/dao"
	apiErr "icode.baidu.com/baidu/faas/kun/pkg/apiserver/error"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/global"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/models"
	resp "icode.baidu.com/baidu/faas/kun/pkg/apiserver/models/function"
	kunErr "icode.baidu.com/baidu/faas/kun/pkg/error"
	"icode.baidu.com/baidu/faas/kun/pkg/server"
	"icode.baidu.com/baidu/faas/kun/pkg/util/json"
	"icode.baidu.com/baidu/faas/kun/pkg/util/logs"
)

// consoleNewFuncV2 创建新函数v2版本，包含自定义运行时和官方运行时。
// 参数：
//
//	c *server.Context - 请求上下文对象，包含了请求和响应对象等信息。
//
// 返回值：
//
//	void - 无返回值，直接操作响应对象。
func (f ConsoleFunctionRest) consoleNewFuncV2(c *server.Context) {
	response := c.Response()
	r := c.Request().Request
	defer r.Body.Close()
	logs.Infof("consoleNewFuncV2 Request {}", c.Request())
	ctx := global.BuildApiserverContext(c.Request(), c.Response(), "consoleNewFuncV2")
	decoder := json.NewDecoder(r.Body) // decoder把json解析模板套到Function结构体上
	newFunction := new(dao.Function)
	if err := decoder.Decode(newFunction); err != nil {
		respErr := c.WithWarnLog(kunErr.NewInvalidRequestContentException(err.Error(), nil))
		resp.NewFailResp(respErr).WriteTo(response, respErr)
		return
	}

	// 如果是自定义运行时，需要设置CustomRuntimeConfig
	if strings.HasPrefix(newFunction.Runtime, "custom.") {
		if newFunction.CustomRuntimeConfig == nil || newFunction.CustomRuntimeConfig.Command == nil ||
			len(newFunction.CustomRuntimeConfig.Command) == 0 || newFunction.CustomRuntimeConfig.Port <= 0 {
			respErr := c.WithWarnLog(kunErr.NewInvalidParameterValueException("CustomRuntimeConfig param is required for custom runtime", nil))
			resp.NewFailResp(respErr).WriteTo(response, respErr)
			return
		}
	}

	// 函数代码从蓝图获取
	bp := new(dao.Blueprint)
	if strings.HasPrefix(newFunction.Runtime, "custom.") {
		// 如果是自定义运行时，需要设置lang_runtime
		if newFunction.LangRuntime == "" {
			respErr := c.WithWarnLog(kunErr.NewInvalidParameterValueException("LangRuntime param is required for custom runtime", nil))
			resp.NewFailResp(respErr).WriteTo(response, respErr)
			return
		}
		bp.Runtime = newFunction.Runtime + "." + newFunction.LangRuntime
	} else {
		bp.Runtime = newFunction.Runtime
	}
	bp.Status = api.BlueprintDefault
	if rkunErr := ctx.Observer.NewStage(global.FindBlueprintStage).Observe(dao.FindOneBlueprint(bp)); rkunErr != nil {
		respErr := c.WithWarnLog(apiErr.NewResourceNotFoundException("invalid runtime", rkunErr))
		resp.NewFailResp(respErr).WriteTo(response, respErr)
		return
	}

	//set default handler
	newFunction.Handler = bp.Handler
	// 添加蓝图默认的官方layer
	newFunction.LayerList = models.GetLayers(bp.LayersStr)
	// 添加蓝图默认的环境变量
	if len(bp.EnvironmentStr) > 0 {
		json.Unmarshal([]byte(bp.EnvironmentStr), &(bp.Environment))
		if newFunction.Environment != nil {
			// 抛弃用户定义的且蓝图默认环境变量中存在的变量，不能覆盖蓝图默认环境变量
			for key, val := range newFunction.Environment.Variables {
				if _, ok := bp.Environment.Variables[key]; !ok {
					bp.Environment.Variables[key] = val
				}
			}
		}
		newFunction.Environment = bp.Environment
	}

	var (
		byteArrayTmp interface{}
	)
	byteArrayTmp, _ = ctx.Observer.NewStage(global.GetBytesFromBosStage).ObserveObject(global.AC.Clients.Code.FaasGetBytesFromCodeStorage(bp.BosObjKey))
	byteArray := byteArrayTmp.([]byte)

	workspaceID := c.Request().Request.Header.Get(api.HeaderWorkspaceID)
	if workspaceID != "" {
		newFunction.WorkspaceID = workspaceID
		newFunction.FunctionName = workspaceID + "_" + newFunction.FunctionName
	}
	NewFunc, rKerr := cloudfunc.CreateNewFunction(c, *newFunction, byteArray, ctx) //创建函数需要函数基本配置信息以及函数代码（byte类型）
	if rKerr != nil {
		respErr := c.WithErrorLog(rKerr)
		resp.NewFailResp(respErr).WriteTo(response, respErr)
		return
	}

	NewFunc.DealResFunction()

	var resMap = make(map[string]interface{})
	resMap["result"] = NewFunc

	defer global.WriteSummary(ctx, api.LogNotToSummary)
	response.WriteHeaderAndEntity(http.StatusCreated, resp.NewSuccessResp(resMap))
}

// 小程序云创建函数
func (f ConsoleFunctionRest) consoleNewCbdFunc(c *server.Context) {
	response := c.Response()
	r := c.Request().Request
	defer r.Body.Close()

	ctx := global.BuildApiserverContext(c.Request(), c.Response(), "consoleNewCbdFunc")

	decoder := json.NewDecoder(r.Body)
	newFunctionRequest := new(models.CreateCBDFunctionRequest)
	if err := decoder.Decode(newFunctionRequest); err != nil {
		c.WithWarnLog(kunErr.NewInvalidRequestContentException(err.Error(), nil)).WriteTo(response)
		return
	}

	// 蓝图UUID和Runtime不能同时为空
	if newFunctionRequest.UUID == "" && newFunctionRequest.Runtime == "" {
		c.WithWarnLog(kunErr.NewInvalidParameterValueException("params error, blueprint UUID and Runtime are empty", nil)).WriteTo(response)
		return
	}

	newFunction := newFunctionRequest.Function
	//判断函数SourceTag
	if newFunction.SourceTag != api.CbdSourceTag {
		c.WithWarnLog(kunErr.NewInvalidParameterValueException("function's params error, sourceTag should be cbd", nil)).WriteTo(response)
		return
	}

	// cbd UUID不为空时,根据蓝图UUID查询蓝图信息; UUID为空时,根据Runtime查询蓝图信息
	bp := new(dao.Blueprint)
	if newFunctionRequest.UUID != "" {
		bp.Uuid = newFunctionRequest.UUID
	} else {
		bp.Runtime = newFunctionRequest.Runtime
		bp.Status = api.BlueprintDefault
	}

	if rkunErr := ctx.Observer.NewStage(global.FindBlueprintStage).Observe(dao.FindOneBlueprint(bp)); rkunErr != nil {
		c.WithWarnLog(apiErr.NewResourceNotFoundException("invalid blueprint", rkunErr)).WriteTo(response)
		return
	}

	//set default handler
	if newFunction.Handler == "" {
		newFunction.Handler = bp.Handler
	}
	// set default runtime
	if newFunction.Runtime == "" {
		newFunction.Runtime = bp.Runtime
	}

	// 添加蓝图默认的官方layer
	newFunction.LayerList = models.GetLayers(bp.LayersStr)

	var byteArrayTmp interface{}
	byteArrayTmp, _ = ctx.Observer.NewStage(global.GetBytesFromBosStage).ObserveObject(global.AC.Clients.Code.FaasGetBytesFromCodeStorage(bp.BosObjKey))
	byteArray := byteArrayTmp.([]byte)

	NewFunc, rKerr := cloudfunc.CreateNewFunction(c, newFunction, byteArray, ctx)
	if rKerr != nil {
		c.WithErrorLog(rKerr).WriteTo(response)
		return
	}
	NewFunc.DealResFunction()

	var resMap = make(map[string]interface{})
	resMap["result"] = NewFunc

	defer global.WriteSummary(ctx, api.LogNotToSummary)
	response.WriteHeaderAndEntity(http.StatusCreated, resp.NewSuccessResp(resMap))
}
