package console

import (
	"icode.baidu.com/baidu/faas/kun/pkg/api"
	apiErr "icode.baidu.com/baidu/faas/kun/pkg/apiserver/error"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/global"
	resp "icode.baidu.com/baidu/faas/kun/pkg/apiserver/models/function"
	"icode.baidu.com/baidu/faas/kun/pkg/bce/iam"
	"icode.baidu.com/baidu/faas/kun/pkg/server"
	"net/http"
)

//查询实名认证状态
func (f ConsoleFunctionRest) consoleQueryRealName(c *server.Context) {
	response := c.Response()
	r := c.Request().Request
	defer r.Body.Close()
	ctx := global.BuildApiserverContext(c.Request(), c.Response(), "consoleQueryLegal")
	requestUser, _ := c.Request().Attribute("User").(*iam.User)
	accountId := requestUser.Domain.ID
	requestId := r.Header.Get(api.HeaderXRequestID)
	var resMap = make(map[string]interface{})
	realNameStatus, err := global.QueryRealName(accountId, requestId)
	if err != nil {
		respErr := c.WithWarnLog(apiErr.NewRealNameQualificationException("query real name qualification error", err))
		resp.NewFailResp(respErr).WriteTo(response, respErr)
		return
	}
	resMap["realNameStatus"] = realNameStatus
	response.WriteHeaderAndEntity(http.StatusOK, resp.NewSuccessResp(resMap))
	defer global.WriteSummary(ctx, api.LogNotToSummary)
}
