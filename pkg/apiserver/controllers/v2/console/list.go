package console

import (
	"net/http"
	"strconv"

	"github.com/asaskevich/govalidator"

	"icode.baidu.com/baidu/faas/kun/pkg/api"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/controllers/v1/cloudfunc"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/dao"
	apiErr "icode.baidu.com/baidu/faas/kun/pkg/apiserver/error"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/global"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/models"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/models/function"
	resp "icode.baidu.com/baidu/faas/kun/pkg/apiserver/models/function"
	kunErr "icode.baidu.com/baidu/faas/kun/pkg/error"
	"icode.baidu.com/baidu/faas/kun/pkg/server"
)

func (f *ConsoleFunctionRest) findByConditionV2(c *server.Context) {
	response := c.Response()
	r := c.Request()

	ctx := global.BuildApiserverContext(c.Request(), c.Response(), "findByConditionV2")
	version := r.QueryParameter("FunctionVersion")
	//bce api 支持分页
	pageNoStr := r.QueryParameter("pageNo")
	pageSizeStr := r.QueryParameter("pageSize")

	//bce api 支持单字段排序、functionName模糊检索
	orderBy := r.QueryParameter("orderBy")
	sort := r.QueryParameter("order")
	searchFN := r.QueryParameter("searchFN")
	serviceName := r.QueryParameter("serviceName")
	//bce api 支持rumtime过滤
	runtime := r.QueryParameter("runtime")

	if searchFN != "" && (!api.RxFunctionName.MatchString(searchFN) || !govalidator.IsByteLength(searchFN, 0, 128)) {
		c.WithWarnLog(kunErr.
			NewInvalidParameterValueException("searchFN ParameterValueException", nil))
		ml := make([]interface{}, 0)
		response.WriteHeaderAndEntity(http.StatusOK, resp.NewSuccessResp(ml))
		return
	}

	var (
		findFuncTmp, functionResSliceTmp, countTmp interface{}
		findFunc                                   *dao.Function
		pageNo, pageSize, count                    int64
		resMap                                     = make(map[string]interface{})
		err                                        error
	)
	if findFuncTmp, err = ctx.Observer.NewStage(global.InitFunctionStage).ObserveObject(cloudfunc.InitFunction(r)); err != nil {
		respErr := c.WithWarnLog(apiErr.NewInitFuncMetaException(err.Error(), "", nil))
		resp.NewFailResp(respErr).WriteTo(response, respErr)
		return
	}
	findFunc = findFuncTmp.(*dao.Function)

	switch version {
	case "":
		findFunc.Version = "$LATEST"
	case "ALL":
		findFunc.Version = ""
	default:
		findFunc.Version = version
	}

	if orderBy == "" {
		orderBy = "CreatedAt"
	}
	if sort == "" {
		sort = "DESC"
	}
	if pageNoStr == "" && pageSizeStr == "" {
		pageNoStr = "1"
		pageSizeStr = "10"
	}

	//分页
	pageNo, pageSize, _, _, err = models.ParseParams(pageNoStr, pageSizeStr, "", "")
	if err != nil {
		respErr := c.WithWarnLog(kunErr.NewInvalidParameterValueException("[parse pageNo|pageSize|Marker|MaxItems fail]", err))
		resp.NewFailResp(respErr).WriteTo(response, respErr)
		return
	}

	condition := &function.FunctionListCond{
		Version:     version,
		PageNo:      pageNo,
		PageSize:    pageSize,
		OrderBy:     orderBy,
		Sort:        sort,
		SearchFN:    searchFN,
		Runtime:     runtime,
		ServiceName: serviceName,
	}

	if _, ok := r.Request.Header[api.HeaderWorkspaceID]; ok {
		workspaceID := r.Request.Header.Get(api.HeaderWorkspaceID)
		condition.WorkspaceID = &workspaceID
	}

	if functionResSliceTmp, countTmp, err = ctx.Observer.NewStage(global.ListFunctionsStage).ObserveObjects(function.ListFunctionWithConditions(findFunc, condition)); err != nil {
		respErr := c.WithWarnLog(kunErr.NewServiceException("[Query database failed]", err))
		resp.NewFailResp(respErr).WriteTo(response, respErr)
		return
	}
	functionResSlice := functionResSliceTmp.([]dao.Function)
	count = countTmp.(int64)

	if searchFN != "" {
		resMap["searchFN"] = searchFN
	}
	if runtime != "" {
		resMap["runtime"] = runtime
	}
	if sort != "" {
		resMap["order"] = sort
	}
	if orderBy != "" {
		resMap["orderBy"] = orderBy
	}

	resMap["pageNo"] = pageNo
	resMap["pageSize"] = pageSize
	resMap["result"] = models.FormatFunctionRes(functionResSlice)
	resMap["totalCount"] = count

	defer global.WriteSummary(ctx, api.LogNotToSummary)
	response.WriteHeaderAndEntity(http.StatusOK, resp.NewSuccessResp(resMap))
}

//获取小程序云相关函数列表
func (f *ConsoleFunctionRest) findByConditionCbd(c *server.Context) {
	response := c.Response()
	r := c.Request()

	ctx := global.BuildApiserverContext(c.Request(), c.Response(), "findByConditionCbd")
	funcPrefix := r.QueryParameter("FunctionNamePrefix")

	markerStr := r.QueryParameter("Marker")
	maxItemStr := r.QueryParameter("MaxItems")
	version := r.QueryParameter("FunctionVersion")

	//支持分页
	pageStr := r.QueryParameter("pageNo")
	pageSizeStr := r.QueryParameter("pageSize")

	filter := r.QueryParameter("Filter")
	order := r.QueryParameter("Order")

	var (
		pageNo, pageSize, marker, maxItem, count   int64
		resMap                                     = make(map[string]interface{})
		description                                string
		findFuncTmp, functionResSliceTmp, countTmp interface{}
		findFunc                                   *dao.Function
		err                                        error
	)

	//解析过滤条件
	functionName, description := api.ParseCBDFilter(filter)
	searchFN := funcPrefix + functionName
	//检查参数
	if (searchFN == "") || (searchFN != "" && (!api.RxFunctionName.MatchString(searchFN) || !govalidator.IsByteLength(searchFN, 0, 128))) {
		c.WithWarnLog(kunErr.
			NewInvalidParameterValueException("searchFN ParameterValueException", nil))
		funcRes := make([]interface{}, 0)
		response.WriteHeaderAndEntity(http.StatusOK, resp.NewSuccessResp(funcRes))
		return
	}

	if findFuncTmp, err = ctx.Observer.NewStage(global.InitFunctionStage).ObserveObject(cloudfunc.InitFunction(r)); err != nil {
		c.WithWarnLog(apiErr.NewInitFuncMetaException(err.Error(), "", nil)).WriteTo(response)
		return
	}
	findFunc = findFuncTmp.(*dao.Function)
	switch version {
	case "":
		findFunc.Version = "$LATEST"
	case "ALL":
		findFunc.Version = ""
	default:
		findFunc.Version = version
	}
	//解析Order
	orderMap := api.ParseCBDOrder(order)

	pageNo, pageSize, marker, maxItem, err = models.ParseParams(pageStr, pageSizeStr, markerStr, maxItemStr)
	if err != nil {
		c.WithWarnLog(kunErr.NewInvalidParameterValueException("[parse pageNo|pageSize|Marker|MaxItems fail]", err)).WriteTo(response)
		return
	}

	condition := &function.CBDFunctionListCond{
		PageNo:      pageNo,
		PageSize:    pageSize,
		Order:       orderMap,
		SearchFN:    searchFN,
		FuncPrefix:  funcPrefix,
		FuncName:    functionName,
		Description: description,
		Marker:      marker,
		MaxItems:    maxItem,
		SourceTag:   api.CbdSourceTag,
	}
	if functionResSliceTmp, countTmp, err = ctx.Observer.NewStage(global.ListFunctionsStage).ObserveObjects(function.ListCbdFunctionWithCondition(findFunc, condition)); err != nil {
		c.WithWarnLog(kunErr.NewServiceException("[Query database failed]", err)).WriteTo(response)
		return
	}
	functionResSlice := functionResSliceTmp.([]dao.Function)
	count = countTmp.(int64)

	resMap["Functions"] = models.FormatFunctionRes(functionResSlice)
	resMap["Total"] = count
	if count > (maxItem + marker) {
		resMap["NextMarker"] = strconv.FormatInt((maxItem + marker), 10)
	}

	defer global.WriteSummary(ctx, api.LogNotToSummary)
	response.WriteHeaderAndEntity(http.StatusOK, resp.NewSuccessResp(resMap))
}

func (f *ConsoleFunctionRest) isFunctionExist(c *server.Context) {
	response := c.Response()

	ctx := global.BuildApiserverContext(c.Request(), c.Response(), "isFunctionExist")
	defer global.WriteSummary(ctx, api.LogNotToSummary)
	var (
		findFuncTmp interface{}
		findFunc    *dao.Function
		resMap      = make(map[string]interface{})
		err         error
	)
	// 初始化
	if findFuncTmp, err = ctx.Observer.NewStage(global.InitFunctionStage).ObserveObject(cloudfunc.InitFunction(c.Request())); err != nil {
		c.WithWarnLog(apiErr.NewInitFuncMetaException(err.Error(), "", nil)).WriteTo(response)
		return
	}
	findFunc = findFuncTmp.(*dao.Function)

	ctx.Observer.NewStage(global.FindOneFunctionStage).Observe(dao.FindOneFunc(findFunc))
	resMap["isExist"] = false

	if findFunc.FunctionBrn != "" {
		resMap["isExist"] = true
	}

	response.WriteHeaderAndEntity(http.StatusOK, resMap)
}
