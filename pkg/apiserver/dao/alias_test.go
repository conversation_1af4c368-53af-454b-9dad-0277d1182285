package dao_test

import (
	"errors"
	"fmt"
	"github.com/DATA-DOG/go-sqlmock"
	"github.com/stretchr/testify/assert"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/dao"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/dbengine"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/global"
	kunErr "icode.baidu.com/baidu/faas/kun/pkg/error"
	"testing"
)

//函数function dao相关test
func TestAliasSelectOne(t *testing.T) {
	var m sqlmock.Sqlmock
	m, dbengine.Engine = NewDB()
	exp := global.GetTestAlias(1)
	req := "SELECT * FROM `aliases` WHERE (`aliases`.`alias_brn` = ?)"
	m.ExpectQuery(FixedFullRe(req)).
		WillReturnRows(global.GetRowsAliases(exp))

	act := &dao.Alias{
		AliasBrn: "test_brn",
	}

	err := dao.FindOneAlias(act)
	assert.Equal(t, exp[0], *act)
	assert.Nil(t, err)
}

func TestFindAlias(t *testing.T) {
	var m sqlmock.Sqlmock
	m, dbengine.Engine = NewDB()
	exp := global.GetTestAlias(1)

	m.ExpectQuery(FixedFullRe("SELECT * FROM `aliases`")).
		WillReturnRows(global.GetRowsAliases(exp))
	act, _ := dao.FindAlias(nil)

	assert.Equal(t, exp, *act)
}
func TestDeleteAlias(t *testing.T) {
	var m sqlmock.Sqlmock
	m, dbengine.Engine = NewDB()
	act := &dao.Alias{
		Uid:          "123456",
		FunctionName: "sjf",
		AliasBrn:     "af",
	}

	m.ExpectBegin()
	m.ExpectQuery(".*").WillReturnRows(global.GetRowsAliases(global.GetTestAlias(1)))
	m.ExpectExec(".*").WillReturnResult(sqlmock.NewResult(0, 1))
	m.ExpectQuery(".*").WillReturnRows(global.GetRowsPolicy(global.GetTestPolicy(1)))
	m.ExpectExec(".*").WillReturnResult(sqlmock.NewResult(0, 1))
	m.ExpectCommit()

	tx := dbengine.Engine.Begin()
	err := dao.DeleteAlias(act, tx)
	tx.Commit()
	assert.Nil(t, err)
}

func TestUpdateAlias(t *testing.T) {
	var m sqlmock.Sqlmock
	m, dbengine.Engine = NewDB()

	cond := &dao.Alias{
		AliasBrn: "a_brn",
	}

	act := &dao.Alias{
		Uid: "123456",
	}

	req := "UPDATE `aliases` SET `uid` = ? WHERE (`aliases`.`alias_brn` = ?)"
	m.ExpectExec(FixedFullRe(req)).
		WithArgs(act.Uid, cond.AliasBrn).
		WillReturnResult(sqlmock.NewResult(0, 1))
	m.ExpectQuery(".*").WillReturnRows(global.GetRowsAliases(global.GetTestAlias(1)))
	m.ExpectQuery(".*").WillReturnRows(global.GetRowsAliases(global.GetTestAlias(1)))
	_, err := dao.UpdateAlias(*cond, act)
	assert.Nil(t, err)
}

func TestAliasSelectOneFailed(t *testing.T) {
	_, dbengine.Engine = NewDB()
	act := &dao.Alias{
		AliasBrn: "test_brn",
	}

	dbengine.Engine.AddError(errors.New("kk"))
	err := dao.FindOneAlias(act)
	cause := fmt.Sprintf("[select Alias failed][Alias: %v]", act)
	e := kunErr.NewServiceException(cause, errors.New("kk"))
	assert.Equal(t, e, err.(kunErr.FinalError))
}

func TestGetAliasCount(t *testing.T) {
	var m sqlmock.Sqlmock
	m, dbengine.Engine = NewDB()
	act := &dao.Alias{
		AliasBrn: "alias_brn_1",
	}

	exp := global.GetTestAlias(1)

	m.ExpectQuery(".*").
		WillReturnRows(global.GetRowsAliases(exp))

	db, count := dao.GetAliasCount(act)

	m.ExpectQuery(".*").WillReturnRows(global.GetRowsAliases(exp))

	slice := make([]dao.Alias, 0)
	db.Find(&slice)

	assert.Equal(t, exp, slice)
	assert.Equal(t, 0, int(count))
}
