package dao_test

import (
	"github.com/DATA-DOG/go-sqlmock"
	"github.com/stretchr/testify/assert"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/dao"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/dbengine"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/global"
	"testing"
	"time"
)

func TestGetUserEvent(t *testing.T) {
	var m sqlmock.Sqlmock
	m, dbengine.Engine = NewDB()

	exp := global.GetUserTestEvents(2)

	m.ExpectQuery(FixedFullRe("SELECT * FROM `user_test_events`")).WillReturnRows(global.GetRowsUserTestEvents(exp))
	ret, _ := dao.GetUserEvent(nil)
	assert.Equal(t, exp, ret)
}

func TestGetOneUserEvent(t *testing.T) {
	var m sqlmock.Sqlmock
	m, dbengine.Engine = NewDB()

	exp := global.GetUserTestEvents(2)

	m.ExpectQuery(FixedFullRe("SELECT * FROM `user_test_events`")).WillReturnRows(global.GetRowsUserTestEvents(exp))
	err := dao.GetOneUserEvent(nil)
	assert.NotNil(t, err)
}

func TestUpdateUserEvent(t *testing.T) {
	var m sqlmock.Sqlmock
	m, dbengine.Engine = NewDB()

	cond := &dao.UserTestEvent{
		Uuid: "abc123def",
	}
	act := &dao.UserTestEvent{
		Event: "helloworld",
	}

	updateSql := "UPDATE `user_test_events` SET `event` = ? WHERE (`user_test_events`.`uuid` = ?)"
	m.ExpectExec(FixedFullRe(updateSql)).WithArgs(act.Event, cond.Uuid).WillReturnResult(sqlmock.NewResult(0, 1))

	err := dao.UpdateUserEvent(*cond, act)
	assert.Nil(t, err)
}

func TestDeleteUserEvent(t *testing.T) {
	var m sqlmock.Sqlmock
	m, dbengine.Engine = NewDB()

	cond := &dao.UserTestEvent{
		Uuid: "abc123def",
	}

	delSql := "DELETE FROM `user_test_events` WHERE (`user_test_events`.`uuid` = ?)"

	m.ExpectExec(FixedFullRe(delSql)).
		WithArgs(cond.Uuid).
		WillReturnResult(sqlmock.NewResult(0, 1))

	err := dao.DeleteUserEvent(cond)
	assert.Nil(t, err)
}

func TestGetUserEventCount(t *testing.T) {
	var m sqlmock.Sqlmock
	m, dbengine.Engine = NewDB()

	cond := &dao.UserTestEvent{
		Uid:          "123",
		FunctionName: "myFunc",
	}

	exp := global.GetUserTestEvents(2)

	m.ExpectQuery(".*").WillReturnRows(global.GetRowsUserTestEvents(exp))

	count, _ := dao.GetUserEventCount(cond)

	assert.Equal(t, 0, count)
}

func TestCreateUserEvent(t *testing.T) {
	var m sqlmock.Sqlmock
	m, dbengine.Engine = NewDB()

	act := &dao.UserTestEvent{
		Uuid:         "abc123def",
		Uid:          "123",
		FunctionName: "myFunc",
		Title:        "test3",
		Event:        "{}",
		CreatedAt:    time.Now(),
		UpdatedAt:    time.Now(),
	}

	req := "INSERT INTO `user_test_events` (`uuid`,`uid`,`function_name`,`title`,`event`,`created_at`,`updated_at`) VALUES (?,?,?,?,?,?,?)"
	m.ExpectExec(FixedFullRe(req)).
		WillReturnResult(sqlmock.NewResult(1, 1))

	err := dao.CreateUserEvent(act)
	assert.Nil(t, err)
}

func TestIsUserEventExist(t *testing.T) {
	var m sqlmock.Sqlmock
	m, dbengine.Engine = NewDB()

	exp := global.GetUserTestEvents(0)
	m.ExpectQuery(".*").
		WillReturnRows(global.GetRowsUserTestEvents(exp))
	cond := &dao.UserTestEvent{
		Uid:          "123",
		FunctionName: "myFunc",
		Title:        "test1",
	}

	err := dao.IsUserEventExist(cond)
	assert.NotNil(t, err)
}

func TestCreateStandardEvent(t *testing.T) {
	var m sqlmock.Sqlmock
	m, dbengine.Engine = NewDB()

	act := &dao.StandardTestEvent{
		Title:     "HelloWorld模版",
		Type:      "general",
		Event:     "{}",
		Status:    1,
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}

	req := "INSERT INTO `standard_test_events` (`title`,`type`,`event`,`status`,`created_at`,`updated_at`) VALUES (?,?,?,?,?,?)"
	m.ExpectExec(FixedFullRe(req)).
		WillReturnResult(sqlmock.NewResult(1, 1))
	err := dao.CreateStandardEvent(act)

	assert.Nil(t, err)
}

func TestIsStandardEventExist(t *testing.T) {
	var m sqlmock.Sqlmock
	m, dbengine.Engine = NewDB()

	exp := global.GetStandardTestEvents(0)
	m.ExpectQuery(".*").
		WillReturnRows(global.GetRowsStandardTestEvents(exp))
	cond := &dao.StandardTestEvent{
		Title: "helloworld模版",
	}

	err := dao.IsStandardEventExist(cond)
	assert.NotNil(t, err)

}

func TestGetStandardEvent(t *testing.T) {
	var m sqlmock.Sqlmock
	m, dbengine.Engine = NewDB()
	exp := global.GetStandardTestEvents(2)

	m.ExpectQuery(FixedFullRe("SELECT * FROM `standard_test_events`")).WillReturnRows(global.GetRowsStandardTestEvents(exp))
	ret, _ := dao.GetStandardEvent(nil)
	assert.Equal(t, exp, ret)

}

func TestGetOneStandardEvent(t *testing.T) {
	var m sqlmock.Sqlmock
	m, dbengine.Engine = NewDB()
	exp := global.GetStandardTestEvents(2)

	m.ExpectQuery(FixedFullRe("SELECT * FROM `standard_test_events`")).WillReturnRows(global.GetRowsStandardTestEvents(exp))
	err := dao.GetOneStandardEvent(nil)
	assert.NotNil(t, err)

}

func TestUpdateStandardEvent(t *testing.T) {
	var m sqlmock.Sqlmock
	m, dbengine.Engine = NewDB()

	cond := &dao.StandardTestEvent{
		ID: 1,
	}
	act := &dao.StandardTestEvent{
		Title: "测试",
		Event: "{}",
	}

	updateSql := "UPDATE `standard_test_events` SET `event` = ?, `title` = ? WHERE (`standard_test_events`.`id` = ?)"
	m.ExpectExec(FixedFullRe(updateSql)).WithArgs(act.Event, act.Title, cond.ID).WillReturnResult(sqlmock.NewResult(0, 1))

	err := dao.UpdateStandardEvent(cond, act)
	assert.Nil(t, err)
}

func TestDeleteStandardEvent(t *testing.T) {
	var m sqlmock.Sqlmock
	m, dbengine.Engine = NewDB()

	cond := &dao.StandardTestEvent{
		ID: 1,
	}

	delSql := "DELETE FROM `standard_test_events` WHERE (`standard_test_events`.`id` = ?)"

	m.ExpectExec(FixedFullRe(delSql)).
		WithArgs(cond.ID).
		WillReturnResult(sqlmock.NewResult(0, 1))

	err := dao.DeleteStandardEvent(cond)
	assert.NotNil(t, err)
}
