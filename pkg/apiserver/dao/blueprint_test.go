package dao_test

import (
	"errors"
	"fmt"
	"github.com/DATA-DOG/go-sqlmock"
	"github.com/stretchr/testify/assert"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/dao"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/dbengine"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/global"
	kunErr "icode.baidu.com/baidu/faas/kun/pkg/error"
	"testing"
)

//blueprint dao相关test

func TestListBlueprint(t *testing.T) {
	var m sqlmock.Sqlmock
	m, dbengine.Engine = NewDB()
	exp := global.GetTestBlueprint(1)

	req := "SELECT * FROM `blueprints`"
	m.ExpectQuery(FixedFullRe(req)).
		WillReturnRows(global.GetRowsBlueprints(exp))
	act, _ := dao.ListBlueprints(nil)

	assert.Equal(t, exp[0].Uuid, (*act)[0].Uuid)
}

func TestListBlueprintError(t *testing.T) {
	_, dbengine.Engine = NewDB()

	dbengine.Engine.AddError(errors.New(""))
	_, err := dao.ListBlueprints(nil)

	cause := fmt.Sprintf("[ListBlueprints failed]")
	e := kunErr.NewServiceException(cause, errors.New(""))
	assert.Equal(t, e, err.(kunErr.FinalError))
}

func TestListBlueprintInside(t *testing.T) {
	var m sqlmock.Sqlmock
	m, dbengine.Engine = NewDB()
	exp := global.GetTestBlueprint(1)

	req := "SELECT * FROM `blueprints` ORDER BY `name`"
	m.ExpectQuery(FixedFullRe(req)).
		WillReturnRows(global.GetRowsBlueprints(exp))
	act, _ := dao.ListBlueprintsInside(nil)

	assert.Equal(t, exp[0].Uuid, (*act)[0].Uuid)
}

func TestListBlueprintInsideError(t *testing.T) {
	_, dbengine.Engine = NewDB()
	dbengine.Engine.AddError(errors.New(""))

	act := &dao.Blueprint{
		Uuid: "123456",
	}
	_, err := dao.ListBlueprintsInside(act)

	cause := fmt.Sprintf("[ListBlueprintsInside failed]")
	e := kunErr.NewServiceException(cause, errors.New(""))
	assert.Equal(t, e, err.(kunErr.FinalError))
}

func TestBlueprintSelectOne(t *testing.T) {
	var m sqlmock.Sqlmock
	m, dbengine.Engine = NewDB()

	req := "SELECT * FROM `blueprints` WHERE (`blueprints`.`name` = ?)"
	m.ExpectQuery(FixedFullRe(req)).
		WillReturnRows(global.GetRowsBlueprints(nil))

	act := &dao.Blueprint{
		Name: "test_blueprint",
	}
	err := dao.FindOneBlueprint(act)
	assert.NotNil(t, err)
}

func TestBlueprintSelectOneError(t *testing.T) {
	_, dbengine.Engine = NewDB()
	act := &dao.Blueprint{
		Uuid: "123456",
	}
	dbengine.Engine.AddError(errors.New(""))
	err := dao.FindOneBlueprint(act)

	cause := fmt.Sprintf("[FindBlueprint failed][Blueprint: %v]", act)
	e := kunErr.NewServiceException(cause, errors.New(""))
	assert.Equal(t, e, err.(kunErr.FinalError))
}

func TestUpdateBlueprint(t *testing.T) {
	var m sqlmock.Sqlmock
	m, dbengine.Engine = NewDB()

	cond := &dao.Blueprint{
		Uuid: "123456",
	}

	act := &dao.Blueprint{
		KeywordsStr: "new keywords",
	}

	req := "UPDATE `blueprints` SET `keywords` = ? WHERE (`blueprints`.`uuid` = ?)"
	m.ExpectExec(FixedFullRe(req)).
		WithArgs(act.KeywordsStr, cond.Uuid).
		WillReturnResult(sqlmock.NewResult(0, 1))

	err := dao.UpdateBlueprint(*cond, act)
	assert.Nil(t, err)
}

func TestDeleteBlueprint(t *testing.T) {
	var m sqlmock.Sqlmock
	m, dbengine.Engine = NewDB()

	act := &dao.Blueprint{
		Uuid: "123456",
	}
	req := "DELETE FROM `blueprints` WHERE (`blueprints`.`uuid` = ?)"

	m.ExpectExec(FixedFullRe(req)).
		WithArgs(act.Uuid).
		WillReturnResult(sqlmock.NewResult(0, 1))

	err := dao.DeleteBlueprint(act)
	assert.Nil(t, err)
}

func TestCreateBlueprint(t *testing.T) {
	var m sqlmock.Sqlmock
	m, dbengine.Engine = NewDB()

	act := &dao.Blueprint{
		Uuid: "123456",
	}
	req := "INSERT INTO `blueprints` (`uuid`,`name`,`keywords`,`bos_obj_key`,`runtime`,`handler`,`version`,`status`,`code_update_type`,`updated_at`,`created_at`) VALUES (?,?,?,?,?,?,?,?,?,?,?)"

	m.ExpectExec(FixedFullRe(req)).
		WillReturnResult(sqlmock.NewResult(1, 1))

	err := dao.CreateBlueprint(act)
	assert.NotNil(t, err)
}

func TestGetBlueprintCount(t *testing.T) {
	var m sqlmock.Sqlmock
	m, dbengine.Engine = NewDB()

	tag := "python"
	queries := make([]dao.BlueprintQuery, 0)
	queries = append(queries, dao.BlueprintQuery{
		Query:     "blueprints.name like ?",
		Condition: "%" + tag + "%",
	})

	m.ExpectQuery(".*").WillReturnRows(global.GetRowsBlueprints(global.GetTestBlueprint(1)))
	_, count := dao.GetBlueprintCount(queries...)
	assert.Equal(t, int64(0), count)
}
