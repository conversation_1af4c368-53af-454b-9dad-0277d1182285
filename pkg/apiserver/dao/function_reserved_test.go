package dao_test

import (
	"testing"
	"time"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/stretchr/testify/assert"

	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/dao"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/dbengine"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/global"
)

func TestDBCreateFunctionReserved(t *testing.T) {
	var m sqlmock.Sqlmock
	m, dbengine.Engine = NewDB()

	fr := &dao.FunctionReserved{
		Uuid: "1234",
	}
	req := "INSERT INTO `function_reserveds` (`uuid`) VALUES (?)"
	m.ExpectExec(FixedFullRe(req)).
		WillReturnResult(sqlmock.NewResult(1, 1))
	err := dao.DBCreateFunctionReserved(fr)
	assert.NotNil(t, err)
}

func TestDBFindFunctionReserved(t *testing.T) {
	var m sqlmock.Sqlmock
	m, dbengine.Engine = NewDB()
	exp := global.GetTestFunctionReserveds(1)
	req := "SELECT * FROM `function_reserveds` WHERE (`function_reserveds`.`uuid` = ?) AND (deleted_at IS NULL OR deleted_at='0000-00-00 00:00:00') ORDER BY `function_reserveds`.`id` ASC LIMIT 1"
	m.ExpectQuery(FixedFullRe(req)).
		WillReturnRows(global.GetRowsFunctionReserveds(exp))

	fr := &dao.FunctionReserved{
		Uuid: "1234",
	}
	err := dao.DBFindFunctionReserved(fr, false)
	assert.Equal(t, exp[0], *fr)
	assert.Nil(t, err)
}

func TestDBListFunctionReserved(t *testing.T) {
	var m sqlmock.Sqlmock
	m, dbengine.Engine = NewDB()
	exp := global.GetTestFunctionReserveds(2)
	m.ExpectQuery(FixedFullRe("SELECT * FROM `function_reserveds` WHERE (deleted_at IS NULL OR deleted_at='0000-00-00 00:00:00')")).
		WillReturnRows(global.GetRowsFunctionReserveds(exp))
	act, _ := dao.DBListFunctionReserved(nil)
	assert.Equal(t, exp, act)
}

func TestInsideListFunctionReserved(t *testing.T) {
	var m sqlmock.Sqlmock
	m, dbengine.Engine = NewDB()
	exp := global.GetTestFunctionReserveds(2)
	m.ExpectQuery(FixedFullRe("SELECT * FROM `function_reserveds` WHERE (deleted_at IS NULL OR deleted_at='0000-00-00 00:00:00')")).
		WillReturnRows(global.GetRowsFunctionReserveds(exp))
	act, _ := dao.DBListFunctionReserved(nil)
	assert.Equal(t, exp, act)
}

func TestDBUpdateFunctionReserved(t *testing.T) {
	var m sqlmock.Sqlmock
	var err error
	m, dbengine.Engine = NewDB()

	//_, m, err = sqlmock.New(sqlmock.QueryMatcherOption(sqlmock.QueryMatcherRegexp))


	cond := &dao.FunctionReserved{
		Uuid: "1234",
	}

	fr := &dao.FunctionReserved{
		ReservedCount:1,
		Status:2,
		ReservedTs: time.Now().Unix(),
		//RealCount:1,
	}
	req := "UPDATE `function_reserveds` SET `reserved_count` = ?, `status`=?, `reserved_ts`=? WHERE (`function_reserveds`.`uuid` = ?) AND (deleted_at IS NULL OR deleted_at='0000-00-00 00:00:00')"

	m.ExpectExec(FixedFullRe(req)).
		WithArgs(fr.ReservedCount, fr.Status, fr.ReservedTs, cond.Uuid).
		WillReturnResult(sqlmock.NewResult(0, 1))
	err = dao.DBUpdateFunctionReserved(cond, fr, false)
	assert.NotNil(t, err)
}

func TestDBDeleteFunctionReserved(t *testing.T) {
	var m sqlmock.Sqlmock
	var err error
	m, dbengine.Engine = NewDB()

	cond := &dao.FunctionReserved{
		Uuid:"1234",
		Status: 3,
	}

	req := "UPDATE `function_reserveds` SET `status`=? WHERE (`function_reserveds`.`uuid` = ?) AND (deleted_at IS NULL OR deleted_at='0000-00-00 00:00:00')"
	m.ExpectExec(FixedFullRe(req)).
		WithArgs(cond.Status, cond.Uuid).
		WillReturnResult(sqlmock.NewResult(0, 1))
	err = dao.DBDeleteFunctionReserved(cond, false)
	assert.NotNil(t, err)
}
