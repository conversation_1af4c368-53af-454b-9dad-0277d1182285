package dao

import (
	"fmt"

	"github.com/jinzhu/gorm"
	"icode.baidu.com/baidu/faas/kun/pkg/api"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/dbengine"
	apiErr "icode.baidu.com/baidu/faas/kun/pkg/apiserver/error"
	kunErr "icode.baidu.com/baidu/faas/kun/pkg/error"
)

// ListPolicies xxx
func ListPolicies(pol *Policy) (*[]Policy, error) {
	PolicySlice := make([]Policy, 0)
	db := dbengine.DBInstance().Where(pol).Find(&PolicySlice)

	if db.Error != nil {
		cause := "[ListPolicies failed]"
		e := kunErr.NewServiceException(cause, db.Error)
		return nil, e
	}
	return &PolicySlice, nil
}

func FindOnePolicy(pol *Policy) error {
	db := dbengine.DBInstance().Where(pol).First(pol)

	if db.RecordNotFound() == true {
		cause := fmt.Sprintf("[can not find policy][condtion: %v]", pol)
		return apiErr.NewResourceNotFoundException(cause, nil)
	}
	if err := db.Error; err != nil {
		cause := fmt.Sprintf("[select failed][condtion: %v][err: %v]", pol, err.Error())
		return kunErr.NewServiceException(cause, nil)
	}

	return nil
}

// CreatePolicy xxx
func CreatePolicy(pol *Policy, tx *gorm.DB) error {
	if tx == nil {
		tx = dbengine.DBInstance()
	}
	db := tx.Create(pol)
	if db.Error != nil {
		cause := fmt.Sprintf("[CreatePolicy failed][Policy: %v]", pol)
		return kunErr.NewServiceException(cause, db.Error)
	}
	notifyEtcd(api.PolicyCacheMethodCreate, *pol)
	return nil
}

// UpdatePolicy xxx
func UpdatePolicy(pol Policy, updatePolicy *Policy, tx *gorm.DB) error {
	if tx == nil {
		tx = dbengine.DBInstance()
	}
	db := tx.Model(pol).Where(pol).Update(updatePolicy)
	if db.Error != nil {
		cause := fmt.Sprintf("[UpdatePolicy failed][Policy: %v]", pol)
		return kunErr.NewServiceException(cause, db.Error)
	}
	notifyEtcd(api.PolicyCacheMethodUpdate, pol)
	return nil
}

// DeletePolicy xxx
func DeletePolicy(pol *Policy, tx *gorm.DB) error {
	if tx == nil {
		tx = dbengine.DBInstance()
	}

	policyList, err := ListPolicies(pol)
	if err != nil {
		cause := fmt.Sprintf("[DeletePolicy failed][Policy: %v]", pol)
		return kunErr.NewServiceException(cause, err)
	}

	db := tx.Where(pol).Delete(pol)
	if db.Error != nil {
		cause := fmt.Sprintf("[DeletePolicy failed][Policy: %v]", pol)
		return kunErr.NewServiceException(cause, db.Error)
	}

	notifyEtcd(api.PolicyCacheMethodDelete, *policyList...)

	return nil
}

func notifyEtcd(method string, policyList ...Policy) {
	keyMap := map[string]bool{}
	pcMsg := api.NewPolicyCacheNotifyMsg()

	for _, v := range policyList {
		key := v.Resource + "/" + v.PrincipalService

		if _, exist := keyMap[key]; !exist {
			pcMsg.AddInfo(key, method)
			keyMap[key] = true
		}
	}

	dbengine.NotifyEtcdWhenChange(api.PolicyCacheEtcdPrefix, "policy", pcMsg)
}
