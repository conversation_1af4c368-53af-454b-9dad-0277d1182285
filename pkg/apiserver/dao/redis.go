package dao

import (
	"fmt"
	"time"

	"icode.baidu.com/baidu/faas/kun/pkg/rpc/redis"
)

const (
	//一个月
	KeyTimeOut = 2626560
)

// 设置 key
func SetLegal(redisClient *redis.Client, accountId string, status int) error {
	key := fmt.Sprintf("%s:accountId-%s", redisClient.KeyPrefix(), accountId)
	return redisClient.Redis().Set(key, status, KeyTimeOut*time.Second).Err()
}

// 获取 key
func GetLegal(redisClient *redis.Client, accountId string) (int, error) {
	key := fmt.Sprintf("%s:accountId-%s", redisClient.KeyPrefix(), accountId)
	v, err := redisClient.Redis().Get(key).Int()
	if err != nil {
		return 0, nil
	}
	return v, err
}
