package dao

import (
	"fmt"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/dbengine"
	apiErr "icode.baidu.com/baidu/faas/kun/pkg/apiserver/error"
	kunErr "icode.baidu.com/baidu/faas/kun/pkg/error"
)

func IsUserEventExist(e *UserTestEvent) error {
	db := dbengine.DBInstance().Where(e).First(e)

	if err := db.Error; err != nil {
		cause := fmt.Sprintf("[select failed][condtion: %v]", e)
		return kunErr.NewServiceException(cause, err)
	}
	return nil
}

func CreateUserEvent(e *UserTestEvent) error {
	if err := dbengine.DBInstance().Create(e).Error; err != nil {
		cause := fmt.Sprintf("[Create user test event Failed][user event: %v]", e)
		return kunErr.NewServiceException(cause, err)
	}
	return nil
}

func GetUserEvent(e *UserTestEvent) ([]UserTestEvent, error) {
	eventResSlice := make([]UserTestEvent, 0)
	db := dbengine.DBInstance().Where(e).Find(&eventResSlice)
	if db.RecordNotFound() == true {
		return eventResSlice, nil
	}

	if err := db.Error; err != nil {
		cause := fmt.Sprintf("[select failed][condtion: %v]", e)
		return eventResSlice, kunErr.NewServiceException(cause, err)
	}

	return eventResSlice, nil
}

func GetOneUserEvent(e *UserTestEvent) error {
	db := dbengine.DBInstance().Where(e).Find(&e)
	if db.RecordNotFound() == true {
		cause := fmt.Sprintf("[user test event not found][condition:%v]", e)
		return apiErr.NewTestEventNotFoundException(cause, nil)
	}
	if err := db.Error; err != nil {
		cause := fmt.Sprintf("[select failed][condtion: %v]", e)
		return kunErr.NewServiceException(cause, err)
	}
	return nil
}

func GetUserEventCount(e *UserTestEvent) (count int, err error) {
	db := dbengine.DBInstance().Model(UserTestEvent{}).Where(e).Count(&count)
	if err := db.Error; err != nil {
		cause := fmt.Sprintf("[select failed][condtion: %v]", e)
		return count, kunErr.NewServiceException(cause, err)
	}

	return count, nil
}

func UpdateUserEvent(updateCondition UserTestEvent, e *UserTestEvent) error {
	db := dbengine.DBInstance().Model(e).Where(updateCondition).Update(e)
	if err := db.Error; err != nil {
		cause := fmt.Sprintf("[update user test event failed][condtion: %v][updateEvent: %v]", updateCondition, e)
		return kunErr.NewServiceException(cause, err)
	}

	return nil
}

func DeleteUserEvent(e *UserTestEvent) error {
	db := dbengine.DBInstance().Model(e).Where(e).Delete(e)
	if err := db.Error; err != nil {
		cause := fmt.Sprintf("[delete user test event failed][event: %v]", e)
		return kunErr.NewServiceException(cause, err)
	}

	return nil
}

func IsStandardEventExist(e *StandardTestEvent) error {
	db := dbengine.DBInstance().Where(e).First(e)

	if err := db.Error; err != nil {
		cause := fmt.Sprintf("[select failed][condtion: %v]", e)
		return kunErr.NewServiceException(cause, err)
	}
	return nil
}

func CreateStandardEvent(e *StandardTestEvent) error {
	if err := dbengine.DBInstance().Create(e).Error; err != nil {
		cause := fmt.Sprintf("[Create standard test event Failed][standard event: %v]", e)
		return kunErr.NewServiceException(cause, err)
	}
	return nil
}

func GetStandardEvent(e *StandardTestEvent) ([]StandardTestEvent, error) {
	eventResSlice := make([]StandardTestEvent, 0)
	db := dbengine.DBInstance().Where(e).Find(&eventResSlice)

	if db.RecordNotFound() == true {
		cause := fmt.Sprintf("[standard test event is empty][condition:%v]", e)
		return eventResSlice, apiErr.NewTestEventNotFoundException(cause, nil)
	}

	if err := db.Error; err != nil {
		cause := fmt.Sprintf("[select failed][condtion: %v]", e)
		return eventResSlice, kunErr.NewServiceException(cause, err)
	}

	return eventResSlice, nil
}

func GetOneStandardEvent(e *StandardTestEvent) error {
	db := dbengine.DBInstance().Where(e).Find(&e)

	if db.RecordNotFound() == true {
		cause := fmt.Sprintf("[standard test event not found][condition:%v]", e)
		return apiErr.NewTestEventNotFoundException(cause, nil)
	}

	if err := db.Error; err != nil {
		cause := fmt.Sprintf("[select failed][condtion: %v]", e)
		return kunErr.NewServiceException(cause, err)
	}

	return nil
}

func UpdateStandardEvent(updateCondition *StandardTestEvent, e *StandardTestEvent) error {
	db := dbengine.DBInstance().Model(e).Where(updateCondition).Update(e)
	if err := db.Error; err != nil {
		cause := fmt.Sprintf("[update standard test event failed][condtion: %v][updateEvent: %v]", updateCondition, e)
		return kunErr.NewServiceException(cause, err)
	}

	return nil
}

func DeleteStandardEvent(e *StandardTestEvent) error {
	db := dbengine.DBInstance().Model(e).Where(e).Delete(e)
	if err := db.Error; err != nil {
		cause := fmt.Sprintf("[delete standard test event failed][event: %v]", e)
		return kunErr.NewServiceException(cause, err)
	}

	return nil
}
