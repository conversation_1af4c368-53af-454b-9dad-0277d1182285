package dao

import (
	"fmt"
	"github.com/jinzhu/gorm"
	"icode.baidu.com/baidu/faas/kun/pkg/api"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/dbengine"
	apiErr "icode.baidu.com/baidu/faas/kun/pkg/apiserver/error"
	kunErr "icode.baidu.com/baidu/faas/kun/pkg/error"
)

func CreateRes(r *BillingResource) error {
	if err := dbengine.DBInstance().Create(r).Error; err != nil {
		cause := fmt.Sprintf("[Create billing resource Failed][resource: %v]", r)
		return apiErr.NewResourceConflictException(cause, err)
	}
	return nil
}

func FindOneRes(r *BillingResource) error {
	db := dbengine.DBInstance().Where(r).First(r)
	if db.RecordNotFound() == true {
		return nil
	}
	if err := db.Error; err != nil {
		cause := fmt.Sprintf("[select failed][condtion: %v][err: %v]", r, err.Error())
		return kunErr.NewServiceException(cause, nil)
	}
	return nil
}

func UpdateRes(updateCondition BillingResource, r *BillingResource) error {
	//写入etcd key
	db := dbengine.DBInstance().Model(r).Where(updateCondition).Unscoped().Update(r)
	if err := db.Error; err != nil {
		cause := fmt.Sprintf("[update resource failed][condtion: %v][updateFunc: %v][err: %s]", updateCondition, r, err.Error())
		return kunErr.NewServiceException(cause, err)
	}
	fcMsg := api.NewBillingCacheNotifyMsg().AddInfo(updateCondition.AccountId, api.BillingCacheMethodUpdate, r.ResourceState)
	dbengine.NotifyEtcdWhenChange(api.BillingCacheEtcdPrefix, updateCondition.AccountId, fcMsg)
	return nil
}

func FindPrepayRes(r *BillingPrepayResource) error {
	db := dbengine.DBInstance().Where(r).First(r)
	if db.RecordNotFound() == true {
		return nil
	}
	if err := db.Error; err != nil {
		cause := fmt.Sprintf("[Find billing prepay resource Failed][condtion: %v][err: %v]", r, err.Error())
		return kunErr.NewServiceException(cause, nil)
	}
	return nil
}

func CreatePrepayRes(r *BillingPrepayResource) error {
	if err := dbengine.DBInstance().Create(r).Error; err != nil {
		cause := fmt.Sprintf("[Create billing prepay resource Failed][resource: %v]", r)
		return kunErr.NewServiceException(cause, err)
	}
	return nil
}

func UpdatePrepayRes(updateCondition BillingPrepayResource, r *BillingPrepayResource) error {
	db := dbengine.DBInstance().Model(r).Where(updateCondition).Unscoped().Update(r)
	if err := db.Error; err != nil {
		cause := fmt.Sprintf("[Update billing prepay resource failed][condtion: %v][resource: %v][err: %s]", updateCondition, r, err.Error())
		return kunErr.NewServiceException(cause, err)
	}
	return nil
}

func CreateAutoRenewRule(r *AutoRenewRule) error {
	if err := dbengine.DBInstance().Create(r).Error; err != nil {
		cause := fmt.Sprintf("[Create autoRenew rule Failed][rule: %v]", r)
		return kunErr.NewServiceException(cause, err)
	}
	return nil
}

func FindAutoRenewRule(r *AutoRenewRule) error {
	db := dbengine.DBInstance().Where(r).First(r)
	if db.RecordNotFound() == true {
		return nil
	}
	if err := db.Error; err != nil {
		cause := fmt.Sprintf("[Find autoRenew rule Failed][condtion: %v][err: %v]", r, err.Error())
		return kunErr.NewServiceException(cause, nil)
	}
	return nil
}

func UpdateAutoRenewRule(updateCondition AutoRenewRule, r *AutoRenewRule) error {
	db := dbengine.DBInstance().Model(r).Where(updateCondition).Unscoped().Update(r)
	if err := db.Error; err != nil {
		cause := fmt.Sprintf("[Update autoRenew rule failed][condtion: %v][resource: %v][err: %s]", updateCondition, r, err.Error())
		return kunErr.NewServiceException(cause, err)
	}
	return nil
}

func DeleteAutoRenewRule(r *AutoRenewRule) error {
	db := dbengine.DBInstance().Model(r).Where(*r).Unscoped()
	r.RenewState = 4
	if err := db.Update(r).Error; err != nil {
		cause := fmt.Sprintf("[Delete autoRenew rule failed][resource: %v][err: %s]", r, err.Error())
		return kunErr.NewServiceException(cause, err)
	}
	return nil
}

func BatchAutoRenewRules(accountId string, orderIds []string) (rules []AutoRenewRule, err error) {
	db := dbengine.DBInstance().Model(AutoRenewRule{}).Where("account_id = ? and order_id in (?)", accountId, orderIds)
	db = db.Unscoped()
	err = db.Find(&rules).Error
	if err == gorm.ErrRecordNotFound {
		return rules, nil
	}
	return
}
