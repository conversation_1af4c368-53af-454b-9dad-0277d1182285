package dao_test

import (
	"context"
	"errors"
	"fmt"
	"github.com/DATA-DOG/go-sqlmock"
	"github.com/stretchr/testify/assert"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/dao"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/dbengine"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/global"
	kunErr "icode.baidu.com/baidu/faas/kun/pkg/error"
	"testing"
)

//函数function dao相关test
func TestFunctionSelectOne(t *testing.T) {
	var m sqlmock.Sqlmock
	m, dbengine.Engine = NewDB()
	exp := global.GetTestFunc(1)
	req := "SELECT * FROM `functions` WHERE (`functions`.`function_brn` = ?) AND (deleted_at IS NULL OR deleted_at='0000-00-00 00:00:00') ORDER BY `functions`.`id` ASC LIMIT 1"
	m.ExpectQuery(FixedFullRe(req)).
		WillReturnRows(global.GetRowsFunctions(exp))

	act := &dao.Function{
		FunctionBrn: "test_brn",
	}

	err := dao.FindOneFunc(act)
	assert.Equal(t, exp[0], *act)
	assert.Nil(t, err)
}

func TestFunctionSelectOneWithTrace(t *testing.T) {
	var m sqlmock.Sqlmock
	m, dbengine.Engine = NewDB()
	exp := global.GetTestFunc(1)
	req := "SELECT * FROM `functions` WHERE (`functions`.`function_brn` = ?) AND (deleted_at IS NULL OR deleted_at='0000-00-00 00:00:00') ORDER BY `functions`.`id` ASC LIMIT 1"
	m.ExpectQuery(FixedFullRe(req)).
		WillReturnRows(global.GetRowsFunctions(exp))

	act := &dao.Function{
		FunctionBrn: "test_brn",
	}

	err := dao.FindOneFunc(act)
	assert.Equal(t, exp[0], *act)
	assert.Nil(t, err)
}

func TestFindFunctions(t *testing.T) {
	var m sqlmock.Sqlmock
	m, dbengine.Engine = NewDB()
	exp := global.GetTestFunc(2)

	m.ExpectQuery(FixedFullRe("SELECT * FROM `functions` WHERE (deleted_at IS NULL OR deleted_at='0000-00-00 00:00:00')")).
		WillReturnRows(global.GetRowsFunctions(exp))
	act, _ := dao.FindFunc(nil)

	assert.Equal(t, exp, *act)
}

func TestUpdateFunc(t *testing.T) {
	var m sqlmock.Sqlmock
	m, dbengine.Engine = NewDB()

	cond := &dao.Function{
		FunctionBrn: "f_brn",
	}

	act := &dao.Function{
		Uid: "123456",
	}

	req := "UPDATE `functions` SET `uid` = ? WHERE (`functions`.`function_brn` = ?) AND (deleted_at IS NULL OR deleted_at='0000-00-00 00:00:00')"
	m.ExpectExec(FixedFullRe(req)).
		WithArgs(act.Uid, cond.FunctionBrn).
		WillReturnResult(sqlmock.NewResult(0, 1))

	err := dao.UpdateFunc(*cond, act)
	assert.Nil(t, err)
}

func TestFunctionSelectOneFailed(t *testing.T) {
	_, dbengine.Engine = NewDB()
	act := &dao.Function{
		FunctionBrn: "test_brn",
	}

	dbengine.Engine.AddError(errors.New(""))
	err := dao.FindOneFunc(act)
	cause := fmt.Sprintf("[select failed][condtion: %v][err: %v]", act, "")
	e := kunErr.NewServiceException(cause, nil)
	assert.Equal(t, e, err)
}

func TestGetFuncCount(t *testing.T) {
	var m sqlmock.Sqlmock
	m, dbengine.Engine = NewDB()
	act := &dao.Function{
		FunctionBrn: "function_brn_1",
	}

	exp := global.GetTestFunc(2)

	m.ExpectQuery(".*").
		WillReturnRows(global.GetRowsFunctions(exp))

	db, count := dao.GetFuncCount(act)

	m.ExpectQuery(".*").
		WillReturnRows(global.GetRowsFunctions(exp))

	functionResSlice := make([]dao.Function, 0)
	db.Find(&functionResSlice)

	assert.Equal(t, exp, functionResSlice)
	assert.Equal(t, 0, int(count))
}

func TestGetAllFunc(t *testing.T) {
	var m sqlmock.Sqlmock
	m, dbengine.Engine = NewDB()
	exp := global.GetTestFunc(2)

	m.ExpectQuery(FixedFullRe("SELECT * FROM `functions` WHERE (`functions`.`uid` = ?)")).WillReturnRows(global.GetRowsFunctions(exp))

	res, err := dao.FindAllFuncs(&dao.Function{Uid: "123"})

	assert.Equal(t, exp, *res)
	assert.Equal(t, err, nil)
}

func TestCountUserReservedConcurrency(t *testing.T){
	var m sqlmock.Sqlmock
	m, dbengine.Engine = NewDB()
	//exp := global.GetTestFunc(2)
    uid := "xxx"
	req := "SELECT SUM(concurrency) AS sum FROM functions WHERE `uid` = ? AND `version` = '$LATEST' AND `concurrency` > 0 AND (deleted_at IS NULL OR deleted_at='0000-00-00 00:00:00')"
	m.ExpectExec(FixedFullRe(req)).
		WithArgs(uid).
		WillReturnResult(sqlmock.NewResult(0, 1))
	_, err := dao.CountUserReservedConcurrency(context.TODO(), uid)
	assert.Nil(t, err)
}

func TestCountUserServiceFun(t *testing.T){
	var m sqlmock.Sqlmock
	m, dbengine.Engine = NewDB()
	//exp := global.GetTestFunc(2)
	uid := "xxx"
	region := "bj"
	req := "SELECT service_name, count(1) as fun_count  FROM functions WHERE `uid` = ? AND `region` = ? AND `version` = '$LATEST' " +
		"AND `service_name` != '' AND `source_tag` = '' AND (deleted_at IS NULL OR deleted_at='0000-00-00 00:00:00') group by service_name"
	m.ExpectExec(FixedFullRe(req)).
		WithArgs(uid).
		WillReturnResult(sqlmock.NewResult(0, 1))
	_, err := dao.CountUserServiceFun(uid, region)
	assert.NotNil(t, err)
}

func TestFindOneReservedFuncFail(t *testing.T) {
	_, dbengine.Engine = NewDB()
	act := &dao.Function{
		FunctionBrn: "test_brn",
	}

	dbengine.Engine.AddError(errors.New(""))
	err := dao.FindOneReservedFunc(act)
	cause := fmt.Sprintf("[select failed][condtion: %v][err: %v]", act, "")
	e := kunErr.NewServiceException(cause, nil)
	assert.Equal(t, e, err)
}

func TestFindOneReservedFunc(t *testing.T) {
	var m sqlmock.Sqlmock
	m, dbengine.Engine = NewDB()
	exp := global.GetTestFunc(1)
	req := "SELECT * FROM `functions` WHERE (`functions`.`function_brn` = ?) ORDER BY `functions`.`id` ASC LIMIT 1"
	m.ExpectQuery(FixedFullRe(req)).
		WillReturnRows(global.GetRowsFunctions(exp))

	act := &dao.Function{
		FunctionBrn: "test_brn",
	}

	err := dao.FindOneReservedFunc(act)
	assert.Equal(t, exp[0], *act)
	assert.Nil(t, err)
}