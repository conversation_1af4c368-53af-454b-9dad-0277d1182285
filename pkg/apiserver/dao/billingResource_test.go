package dao_test

import (
	"github.com/DATA-DOG/go-sqlmock"
	"github.com/stretchr/testify/assert"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/dao"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/dbengine"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/global"
	"testing"
)

func TestBillingResSelectOne(t *testing.T) {
	//正常查询
	var m sqlmock.Sqlmock
	m, dbengine.Engine = NewDB()
	exp := global.GetTestBillingRes(1)
	req := "SELECT * FROM `billing_resources` WHERE (`billing_resources`.`account_id` = ?)  ORDER BY `billing_resources`.`id` ASC LIMIT 1"
	m.ExpectQuery(FixedFullRe(req)).
		WillReturnRows(global.GetRowsBillingRes(exp))

	act := &dao.BillingResource{
		AccountId: "abc",
	}
	err := dao.FindOneRes(act)
	assert.Equal(t, exp[0], *act)
	assert.Nil(t, err)
	//空
	exp = global.GetTestBillingRes(0)
	req = "SELECT * FROM `billing_resources` WHERE (`billing_resources`.`account_id` = ?)  ORDER BY `billing_resources`.`id` ASC LIMIT 1"
	m.ExpectQuery(FixedFullRe(req)).
		WillReturnRows(global.GetRowsBillingRes(exp))

	act = &dao.BillingResource{
		AccountId: "abc",
	}
	err = dao.FindOneRes(act)
	assert.Equal(t, exp[0], *act)
	assert.Nil(t, err)
}

func TestUpdateRes(t *testing.T) {
	var m sqlmock.Sqlmock
	m, dbengine.Engine = NewDB()

	cond := &dao.BillingResource{
		AccountId: "abc",
	}

	act := &dao.BillingResource{
		ResourceState: "RUNNING",
	}

	req := "UPDATE `billing_resources` SET `resource_state` = ? WHERE (`billing_resources`.`account_id` = ?)"
	m.ExpectExec(FixedFullRe(req)).
		WithArgs(act.ResourceState, cond.AccountId).
		WillReturnResult(sqlmock.NewResult(0, 1))

	err := dao.UpdateRes(*cond, act)
	assert.Nil(t, err)
}

func TestCreateRes(t *testing.T) {
	var m sqlmock.Sqlmock
	m, dbengine.Engine = NewDB()

	pol := &dao.BillingResource{
		AccountId: "abc",
	}
	req := "INSERT INTO `billing_resources` (`account_id`,`resource_state`,`create_state`,`updated_at`,`created_at`) VALUES (?,?,?,?,?)"

	m.ExpectExec(FixedFullRe(req)).
		WillReturnResult(sqlmock.NewResult(1, 1))

	err := dao.CreateRes(pol)
	assert.Nil(t, err)
}
