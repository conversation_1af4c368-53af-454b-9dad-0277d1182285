package dao

import (
	"fmt"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/dbengine"
	apiErr "icode.baidu.com/baidu/faas/kun/pkg/apiserver/error"
	kunErr "icode.baidu.com/baidu/faas/kun/pkg/error"
)

func CreateFeatureActivation(f *FeatureActivation) error {
	if err := dbengine.DBInstance().Create(f).Error; err != nil {
		cause := fmt.Sprintf("[create user feature activation Failed][condition: %v]", f)
		return kunErr.NewServiceException(cause, err)
	}
	return nil
}

func UpdateFeatureActivationStatus(updateCondition FeatureActivation, status int) error {
	db := dbengine.DBInstance().Model(&FeatureActivation{}).Select("status").Where(updateCondition).Updates(map[string]interface{}{"status": status})
	if err := db.Error; err != nil {
		cause := fmt.Sprintf("[update user feature activation Failed][condtion: %v][expect status: %v]", updateCondition, status)
		return kunErr.NewServiceException(cause, err)
	}
	return nil
}

func FindOneFeatureActivation(f *FeatureActivation) error {
	db := dbengine.DBInstance().Where(f).First(f)
	if db.RecordNotFound() == true {
		cause := fmt.Sprintf("[can not find any activation][condtion: %v]", f)
		return apiErr.NewResourceNotFoundException(cause, nil)
	}
	if err := db.Error; err != nil {
		cause := fmt.Sprintf("[select failed][condtion: %v][err: %v]", f, err.Error())
		return kunErr.NewServiceException(cause, nil)
	}
	return nil
}

func FindFeatureActivation(f *FeatureActivation) ([]FeatureActivation, error) {
	resList := make([]FeatureActivation, 0)
	db := dbengine.DBInstance().Where(f).Find(&resList)

	if len(resList) == 0 {
		cause := fmt.Sprintf("[can not find any activation][condition: %v]", f)
		return resList, apiErr.NewResourceNotFoundException(cause, nil)
	}

	if err := db.Error; err != nil {
		cause := fmt.Sprintf("[select failed][condtion: %v][err: %v]", f, err.Error())
		return resList, kunErr.NewServiceException(cause, nil)
	}
	return resList, nil
}
