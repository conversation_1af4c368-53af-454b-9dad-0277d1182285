package dao

import (
	"fmt"
	"github.com/jinzhu/gorm"
	"strconv"
	"time"

	"icode.baidu.com/baidu/faas/kun/pkg/api"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/dbengine"
	apiErr "icode.baidu.com/baidu/faas/kun/pkg/apiserver/error"
	kunErr "icode.baidu.com/baidu/faas/kun/pkg/error"
)

func DBCreateFunctionReserved(r *FunctionReserved) error {
	if err := dbengine.DBInstance().Unscoped().Create(r).Error; err != nil {
		cause := fmt.Sprintf("create function reserved failed, function: %+v", r)
		return kunErr.NewServiceException(cause, err)
	}

	// 写入etcd key
	frMsg := api.NewFunctionReservedNotifyMsg().AddInfo(r.Uuid, api.FunctionReservedCreate)
	dbengine.NotifyEtcdWhenChange(api.FunctionReservedEtcdPrefix, r.Uuid, frMsg)
	return nil
}

func DBFindFunctionReserved(r *FunctionReserved, inside bool) error {
	var db *gorm.DB
	// 内部接口不用排除已删除的预留实例
	if inside {
		db = dbengine.DBInstance().Where(r).Unscoped().First(&r)
	} else {
		db = dbengine.DBInstance().Where(r).Unscoped().Scopes(ScopeRudDeletedAt).First(&r)
	}
	if db.RecordNotFound() == true {
		cause := fmt.Sprintf("function reserved not found, brn: %v", r.FunctionBrn)
		return apiErr.NewResourceNotFoundException(cause, nil)
	}
	if err := db.Error; err != nil {
		cause := fmt.Sprintf("select failed brn: %v", r.FunctionBrn)
		return kunErr.NewServiceException(cause, err)
	}
	return nil
}

func DBListFunctionReserved(r *FunctionReserved) ([]FunctionReserved,error) {
	reserveds := make([]FunctionReserved, 0)

	db := dbengine.DBInstance().Where(r).Unscoped().Scopes(ScopeRudDeletedAt).Find(&reserveds)

	if db.RecordNotFound() == true {
		cause := fmt.Sprintf("function reserved not found, brn: %v", r.FunctionBrn)
		return nil, apiErr.NewResourceNotFoundException(cause, nil)
	}
	if err := db.Error; err != nil {
		cause := fmt.Sprintf("select failed brn: %v", r.FunctionBrn)
		return nil, kunErr.NewServiceException(cause, err)
	}
	return reserveds, nil
}

func InsideListFunctionReserved(r *FunctionReserved) ([]*FunctionReserved, error) {
	reserveds := make([]*FunctionReserved, 0)
	queries := make([]FunctionQuery, 0)
	var orderBy = "UpdatedAt"
	var sort = "DESC"

	if r.Status == api.ReservedDeleted{
		queries = append(queries, FunctionQuery{
			Query: "function_reserveds.status = ? ",
			Condition: strconv.Itoa(api.ReservedDeleted),
		})
	} else {
		sort = "ASC"
		queries = append(queries, FunctionQuery{
			Query: "function_reserveds.status != ? ",
			Condition: strconv.Itoa(api.ReservedDeleted),
		})
	}

	if r.ReservedTs > 0 {
		queries = append(queries, FunctionQuery{
			Query: "function_reserveds.reserved_ts > ? ",
			Condition: strconv.FormatInt(r.ReservedTs, 10),
		})
	}

	db := dbengine.DBInstance().Model(FunctionReserved{}).Where(FunctionReserved{})
	if len(queries) > 0 {
		for _, v := range queries {
			db = db.Where(v.Query, v.Condition)
		}
	}
	db = db.Order(fmt.Sprintf("%s %s", gorm.ToDBName(orderBy), sort))
	db = db.Unscoped().Find(&reserveds)
	if db.RecordNotFound() == true {
		cause := fmt.Sprintf("function reserved not found")
		return nil, apiErr.NewResourceNotFoundException(cause, nil)
	}
	if err := db.Error; err != nil {
		cause := fmt.Sprintf("select failed ")
		return nil, kunErr.NewServiceException(cause, err)
	}
	return reserveds, nil
}

func DBUpdateFunctionReserved(cond *FunctionReserved, r *FunctionReserved, inside bool) error {
	var db *gorm.DB
	updateMap := make(map[string]interface{})
	if !inside {
		// 外部更新涉及ReservedCount
		updateMap["reserved_count"] = r.ReservedCount
	} else {
		// 内部更新涉及RealCount
		updateMap["real_count"] = r.RealCount
	}
	updateMap["reserved_ts"] = r.ReservedTs
	updateMap["status"] = r.Status

	if inside {
		db = dbengine.DBInstance().Model(r).Where(cond).Unscoped()
	} else {
		// 外部更新，不能更新已删除记录
		db = dbengine.DBInstance().Model(r).Where(cond).Unscoped().Scopes(ScopeRudDeletedAt)
	}

	err := db.Update(updateMap).Error
	if err != nil {
		cause := fmt.Sprintf("update reserved failed. condition: %+v, update reserved: %+v", cond, r)
		return kunErr.NewServiceException(cause, err)
	}

	if !inside {
		// 写入etcd key
		frMsg := api.NewFunctionReservedNotifyMsg().AddInfo(cond.Uuid, api.FunctionReservedUpdate)
		dbengine.NotifyEtcdWhenChange(api.FunctionReservedEtcdPrefix, cond.Uuid, frMsg)
	}
	return nil
}

func DBDeleteFunctionReserved(cond *FunctionReserved, inside bool) error {
	var db *gorm.DB
	if inside {
		//  系统删除预留实例pod后将预留实例状态改成已删除
		db = dbengine.DBInstance().Model(cond).Where(*cond).Unscoped()
		cond.Status = api.ReservedDeleted
	} else {
		db = dbengine.DBInstance().Model(cond).Where(*cond).Unscoped().Scopes(ScopeRudDeletedAt)
		cond.Status = api.ReservedDeleting // 状态改成删除中，等待预留实例真正删除时，更新status
	}

	// 注意: gorm.DB更新字段时，会忽略0值，nil，false等
	updateMap := make(map[string]interface{})
	updateMap["reserved_count"] = 0
	updateMap["deleted_at"] = time.Now()
	updateMap["reserved_ts"] = time.Now().Unix()
	updateMap["status"] = cond.Status
	err := db.Update(updateMap).Error
	if err != nil {
		cause := fmt.Sprintf("delete reserved failed, brn: %v", cond.FunctionBrn)
		return kunErr.NewServiceException(cause, err)
	}

	if !inside {
		// 写入etcd key
		frMsg := api.NewFunctionReservedNotifyMsg().AddInfo(cond.Uuid, api.FunctionReservedDelete)
		dbengine.NotifyEtcdWhenChange(api.FunctionReservedEtcdPrefix, cond.Uuid, frMsg)
	}
	return nil
}

