package dao

import (
	"fmt"
	"github.com/jinzhu/gorm"
	omgErr "icode.baidu.com/baidu/faas/kun/cmd/omg/error"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/dbengine"
	apiErr "icode.baidu.com/baidu/faas/kun/pkg/apiserver/error"
	kunErr "icode.baidu.com/baidu/faas/kun/pkg/error"
)

func ListBlueprints(bp *Blueprint) (*[]Blueprint, error) {
	bpsResSlice := make([]Blueprint, 0)
	db := dbengine.DBInstance().Where(bp).Find(&bpsResSlice)

	if db.Error != nil {
		cause := fmt.Sprintf("[ListBlueprints failed]")
		e := kunErr.NewServiceException(cause, db.Error)
		return nil, e
	}
	return &bpsResSlice, nil
}

func ListBlueprintsInside(bp *Blueprint) (*[]Blueprint, error) {
	bpsResSlice := make([]Blueprint, 0)
	db := dbengine.DBInstance().Where(bp).Order("name").Find(&bpsResSlice)

	if db.Error != nil {
		cause := fmt.Sprintf("[ListBlueprintsInside failed]")
		e := kunErr.NewServiceException(cause, db.Error)
		return nil, e
	}
	return &bpsResSlice, nil
}

func FindOneBlueprint(bp *Blueprint) error {
	db := dbengine.DBInstance().Where(bp).Find(bp)
	if db.RecordNotFound() == true {
		cause := fmt.Sprintf("[FindBlueprint failed, not found][Blueprint: %v]", bp)
		return apiErr.NewResourceNotFoundException(cause, nil)
	}

	if db.Error != nil {
		cause := fmt.Sprintf("[FindBlueprint failed][Blueprint: %v]", bp)
		return kunErr.NewServiceException(cause, db.Error)
	}
	return nil
}

func CreateBlueprint(bp *Blueprint) error {
	db := dbengine.DBInstance().Create(bp)
	if db.Error != nil {
		cause := fmt.Sprintf("[CreateBlueprint failed][Blueprint: %v]", bp)
		return omgErr.NewSqlIOException(cause, db.Error)
	}
	return nil
}

func UpdateBlueprint(updateCondition Blueprint, bp *Blueprint) error {
	db := dbengine.DBInstance().Model(bp).Where(updateCondition).Updates(bp)
	if db.Error != nil {
		cause := fmt.Sprintf("[UpdateBlueprint failed][Blueprint: %v]", bp)
		return omgErr.NewSqlIOException(cause, db.Error)
	}
	return nil
}

func DeleteBlueprint(bp *Blueprint) error {
	db := dbengine.DBInstance().Where(bp).Delete(bp)
	if db.Error != nil {
		cause := fmt.Sprintf("[DeleteBlueprint failed][Blueprint: %v]", bp)
		return omgErr.NewSqlIOException(cause, db.Error)
	}
	return nil
}

type BlueprintQuery struct {
	Query     string
	Condition string
}

func GetBlueprintCount(args ...BlueprintQuery) (*gorm.DB, int64) {
	var count int64
	db := dbengine.DBInstance().Model(Blueprint{})
	if len(args) > 0 {
		for _, v := range args {
			db = db.Where(v.Query, v.Condition)
		}
	}
	db.Count(&count)
	return db, count
}
