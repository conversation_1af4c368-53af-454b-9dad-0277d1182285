package dao

import (
	"fmt"

	"github.com/jinzhu/gorm"
	"icode.baidu.com/baidu/faas/kun/pkg/api"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/dbengine"
	apiErr "icode.baidu.com/baidu/faas/kun/pkg/apiserver/error"
	kunErr "icode.baidu.com/baidu/faas/kun/pkg/error"
	"icode.baidu.com/baidu/faas/kun/pkg/util/logs"
)

func CreateAlias(a *Alias) error {

	if err := dbengine.DBInstance().Create(a).Error; err != nil {
		cause := fmt.Sprintf("[Create Alias Failed][Alias: %v]", a)
		return apiErr.NewResourceConflictException(cause, err)
	}
	return nil

}

func UpdateAlias(updateCondition Alias, a *Alias) (*Alias, error) {

	db := dbengine.DBInstance().Model(a).Where(updateCondition).Update(a)

	if db.Error != nil {
		cause := fmt.Sprintf("[Update Alias Failed][updateCondition: %v][Alias: %v][err: %s]", updateCondition, a, db.Error.Error())
		e := kunErr.NewServiceException(cause, db.Error)
		return nil, e
	}

	aliasFind := &Alias{}
	dbengine.DBInstance().Where(updateCondition).First(aliasFind)
	logs.V(6).Infof("UpdateAlias dbengine.NotifyEtcdWhenFunctionChange key(%s)value(%s)", aliasFind.AliasBrn, aliasFind.Name)
	dbengine.NotifyEtcdWhenChange(api.FunctionCacheEtcdPrefix, aliasFind.AliasBrn, api.NewFunctionCacheNotifyMsg().
		AddInfo(aliasFind.AliasBrn, aliasFind.Name, api.FunctionCacheMethodUpdate))

	var aliasList *[]Alias
	var err error
	acMsg := api.NewAliasCacheNotifyMsg()
	if aliasList, err = FindAlias(&updateCondition); err != nil {
		return nil, err
	}

	for _, v := range *aliasList {
		acMsg.AddInfo(v.AliasBrn, api.AliasCacheMethodUpdate)
	}

	dbengine.NotifyEtcdWhenChange(api.AliasCacheEtcdPrefix, a.AliasBrn, acMsg)
	return aliasFind, nil
}

func DeleteAlias(a *Alias, tx *gorm.DB) error {
	if tx == nil {
		tx = dbengine.DBInstance()
	}
	var aliasList *[]Alias
	var err error
	fcMsg := api.NewFunctionCacheNotifyMsg()
	acMsg := api.NewAliasCacheNotifyMsg()
	if aliasList, err = FindAlias(a); err != nil {
		return err
	}

	for _, v := range *aliasList {
		fcMsg.AddInfo(v.AliasBrn, v.Name, api.FunctionCacheMethodDelete)
		acMsg.AddInfo(v.AliasBrn, api.AliasCacheMethodDelete)
	}

	err = tx.Where(a).Delete(a).Error
	if err != nil {
		cause := fmt.Sprintf("[delete Alias failed][Alias: %v][err: %s]", a, err.Error())
		e := kunErr.NewServiceException(cause, err)
		return e
	}

	for _, v := range *aliasList {
		if err = DeletePolicy(&Policy{FunctionUid: v.Uid, FunctionName: v.FunctionName, Resource: v.AliasBrn}, tx); err != nil {
			return err
		}
	}

	dbengine.NotifyEtcdWhenChange(api.FunctionCacheEtcdPrefix, a.FunctionName, fcMsg)
	dbengine.NotifyEtcdWhenChange(api.AliasCacheEtcdPrefix, a.AliasBrn, acMsg)
	return nil
}

func FindOneAlias(a *Alias) error {

	db := dbengine.DBInstance().Where(a).Find(a)
	if db.RecordNotFound() == true {
		cause := fmt.Sprintf("[select Alias not found][Alias: %v]", a)
		return apiErr.NewResourceNotFoundException(cause, nil)
	}
	if err := db.Error; err != nil {
		cause := fmt.Sprintf("[select Alias failed][Alias: %v]", a)
		return kunErr.NewServiceException(cause, err)
	}

	return nil
}

func FindAlias(a *Alias) (*[]Alias, error) {
	aliasResSlice := make([]Alias, 0)

	db := dbengine.DBInstance().Where(a).Find(&aliasResSlice)

	if db.Error != nil {
		cause := fmt.Sprintf("[findAlias Failed][Alias: %v]", a)
		return nil, kunErr.NewServiceException(cause, db.Error)
	}

	return &aliasResSlice, nil

}

func GetAliasCount(f *Alias) (*gorm.DB, int64) {
	var count int64
	db := dbengine.DBInstance().Model(Alias{}).Where(f)
	db.Count(&count)
	return db, count
}
