package dao

import (
	"github.com/jinzhu/gorm"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/dbengine"
)

type FunctionQuery struct {
	Query     string
	Condition string
}

func GetFuncCount(f *Function, args ...FunctionQuery) (*gorm.DB, int64) {
	var count int64
	db := dbengine.DBInstance().Model(Function{}).Where(f)
	if len(args) > 0 {
		for _, v := range args {
			db = db.Where(v.Query, v.Condition)
		}
	}
	db = db.Unscoped().Scopes(ScopeRudDeletedAt)
	db.Count(&count)
	return db, count
}
