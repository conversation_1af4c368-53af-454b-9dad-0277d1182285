package dao

import (
	"fmt"
	"time"

	"github.com/jinzhu/gorm"

	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/dbengine"
	apiErr "icode.baidu.com/baidu/faas/kun/pkg/apiserver/error"
	kunErr "icode.baidu.com/baidu/faas/kun/pkg/error"
	"icode.baidu.com/baidu/faas/kun/pkg/util/json"
)

func CreateEventSource(tx *gorm.DB, a *FuncEventSource) error {

	if err := tx.Create(a).Error; err != nil {
		cause := fmt.Sprintf("[Create FuncEventSource Failed][FuncEventSource: %v]", a)
		return apiErr.NewResourceConflictException(cause, err)
	}

	return nil

}

func FindOneEventSource(a *FuncEventSource) error {

	db := dbengine.DBInstance().Unscoped().Scopes(ScopeRudDeletedAt).Where(a).Find(a)
	if db.RecordNotFound() == true {
		cause := fmt.Sprintf("[select FuncEventSource not found][FuncEventSource: %v]", a)
		return apiErr.NewResourceNotFoundException(cause, nil)
	}
	if err := db.Error; err != nil {
		cause := fmt.Sprintf("[select FuncEventSource failed][FuncEventSource: %v]", a)
		return apiErr.NewResourceConflictException(cause, err)
	}

	if a.DatahubConfigStr != nil && len(*a.DatahubConfigStr) > 0 {
		json.Unmarshal([]byte(*a.DatahubConfigStr), &a.DatahubConfig)
	}
	return nil
}

func FindEventSource(a *FuncEventSource) (*[]FuncEventSource, error) {

	resSlice := make([]FuncEventSource, 0)
	db := dbengine.DBInstance().Where(a)
	db = db.Unscoped().Scopes(ScopeRudDeletedAt)
	db = db.Find(&resSlice)
	if db.Error != nil {
		cause := fmt.Sprintf("[select FuncEventSource failed][FuncEventSource: %v]", a)
		return nil, apiErr.NewResourceConflictException(cause, db.Error)
	}
	for i := range resSlice {
		if resSlice[i].DatahubConfigStr != nil && len(*(resSlice[i].DatahubConfigStr)) > 0 {
			json.Unmarshal([]byte(*(resSlice[i].DatahubConfigStr)), &(resSlice[i]).DatahubConfig)
		}
	}
	return &resSlice, nil
}

func FindEventSourceByUpdateAtForMqhub(a *FuncEventSource, t time.Time) (*[]FuncEventSource, error) {

	resSlice := make([]FuncEventSource, 0)
	db := dbengine.DBInstance().Where(a).Where("updated_at > ?", t)
	db = db.Unscoped()
	db = db.Find(&resSlice)
	if db.Error != nil {
		cause := fmt.Sprintf("[select FuncEventSource failed][FuncEventSource: %v]", a)
		return nil, apiErr.NewResourceConflictException(cause, db.Error)
	}
	for i := range resSlice {
		if resSlice[i].DatahubConfigStr != nil && len(*(resSlice[i].DatahubConfigStr)) > 0 {
			json.Unmarshal([]byte(*(resSlice[i].DatahubConfigStr)), &(resSlice[i]).DatahubConfig)
		}
	}
	return &resSlice, nil
}

func GetEventSourceCount(f *FuncEventSource) (*gorm.DB, int64) {
	var count int64
	db := dbengine.DBInstance().Model(FuncEventSource{}).Where(f)
	db = db.Unscoped().Scopes(ScopeRudDeletedAt)
	db.Count(&count)
	return db, count
}

func UpdateEventSource(updateCondition FuncEventSource, a *FuncEventSource) (*FuncEventSource, error) {

	db := dbengine.DBInstance().Unscoped().Scopes(ScopeRudDeletedAt).Model(a).Where(updateCondition).Update(a)

	if db.Error != nil {
		cause := fmt.Sprintf("[Update FuncEventSource Failed][updateCondition: %v][FuncEventSource: %v][err: %s]", updateCondition, a, db.Error.Error())
		return nil, apiErr.NewResourceConflictException(cause, db.Error)
	}

	find := &FuncEventSource{}
	dbengine.DBInstance().Unscoped().Scopes(ScopeRudDeletedAt).Where(updateCondition).First(find)

	if find.DatahubConfigStr != nil && len(*find.DatahubConfigStr) > 0 {
		json.Unmarshal([]byte(*find.DatahubConfigStr), &find.DatahubConfig)
	}
	return find, nil
}

func DeleteEventSource(tx *gorm.DB, f *FuncEventSource) error {
	db := tx.Model(f).Where(*f).Unscoped().Scopes(ScopeRudDeletedAt)
	f.DeletedAt = time.Now()
	b := false
	f.Enabled = &b
	err := db.Update(f).Error
	if err != nil {
		cause := fmt.Sprintf("[delete FuncEventSource failed][FuncEventSource: %v][err: %s]", f, err.Error())
		e := kunErr.NewServiceException(cause, err)
		return &e
	}
	return nil
}
