package dao

/**
 * Created Date: Wednesday, September 13th 2017, 2:30:39 pm
 * Author: hefan<PERSON>hi
 * Copyright (c) 2017 Baidu.Inc
 *
 */

import (
	"time"

	"icode.baidu.com/baidu/faas/kun/pkg/api"
)

const (
	TypeDuerOSBot = "dueros-bot"
)

// function
type Function struct {
	Id                     uint `json:"-"`
	Uid                    string
	Description            *string `gorm:"default:''"`
	FunctionBrn            string
	Region                 string
	Timeout                *int
	EnvironmentStr         string  `gorm:"column:environment" json:"-"`
	VpcConfigStr           *string `gorm:"column:vpc_config" json:"-"`
	VersionDesc            *string `gorm:"default:''"`
	UpdatedAt              time.Time
	CreatedAt              time.Time `json:"-"`
	DeletedAt              time.Time `gorm:"column:deleted_at;DEFAULT:0000-00-00 00:00:00" json:"-"`
	LastModified           time.Time `gorm:"-" sql:"-"`
	SourceTag              string    `gorm:"column:source_tag"` // 函数来源，小程序云创建 SourceTag=cbd
	LayersStr              *string   `gorm:"column:layers" json:"-"`
	AsyncInvokeConfigStr   *string   `gorm:"column:async_invoke_config" json:"-"`   // 异步调用配置
	CFSConfigStr           *string   `gorm:"column:cfs_config" json:"-"`            // cfs配置
	BlueprintTag           string    `gorm:"column:blueprint_tag"`                  // 函数的蓝图来源，通过蓝图创建的函数会写入蓝图名称
	CustomRuntimeConfigStr string    `gorm:"column:custom_runtime_config" json:"-"` // 自定义运行时启动命令/端口配置，创建函数从runtime_configs中拉取

	api.FunctionConfig
}

type BillingResource struct {
	Id            uint `json:"-"`
	AccountId     string
	ResourceState string
	CreateState   string
	UpdatedAt     time.Time
	CreatedAt     time.Time
}

// db  LegalStatus
type LegalStatus struct {
	Id             uint `json:"-"`
	AccountId      string
	Status         int
	UpdatedAt      time.Time
	CreatedAt      time.Time
	RealNameStatus int
}

// TableName 解决gorm表名映射
func (LegalStatus) TableName() string {
	return "legal_status"
}

// db res
type Alias struct {
	Id                      uint   `json:"-"`
	AliasBrn                string `gorm:"column:alias_brn"`
	AliasArn                string `gorm:"-" sql:"-"`
	FunctionName            string
	FunctionVersion         string  `valid:"optional,matches(^\\$LATEST|([0-9]+)$),runelength(1|32)"`
	Name                    string  `valid:"optional,matches(^[a-zA-Z0-9-_]+$),runelength(1|64)"`
	Description             *string `gorm:"default:''"`
	Uid                     string
	UpdatedAt               time.Time
	CreatedAt               time.Time
	AdditionalVersion       *string  `gorm:"default:''"`
	AdditionalVersionWeight *float64 `gorm:"default:'0'"`
}

// db res
type Blueprint struct {
	Id             uint    `json:"-"`
	Uuid           string  `json:"UUID"`
	Name           string  `gorm:"size:255"`
	Description    *string `gorm:"default:''"`
	KeywordsStr    string  `gorm:"column:keywords;size:255" json:"-"`
	BosObjKey      string  `gorm:"size:255"`
	Runtime        string
	Handler        string
	Version        string
	EnvironmentStr string `gorm:"column:environment" json:"-"`
	Status         string
	Extra          string `gorm:"column:extra" json:"-"`
	LinksStr       string `gorm:"column:links" json:"-"`
	AuthorsStr     string `gorm:"column:authors" json:"-"`
	CodeUpdateType string `json:"Type,omitempty"`
	LayersStr      string `gorm:"column:layers" json:"-"`
	Deployments    *int   `gorm:"column:deployments"`
	UpdatedAt      time.Time
	CreatedAt      time.Time
	BlueprintConfig
}

type BlueprintConfig struct {
	Environment *api.Environment  `gorm:"-" sql:"-"`
	MemorySize  *int              `gorm:"-" sql:"-"`
	Timeout     *int              `gorm:"-" sql:"-"`
	TriggerType string            `gorm:"-" sql:"-"`
	Keywords    []string          `gorm:"-" sql:"-"`
	Links       map[string]string `gorm:"-" sql:"-"`
	Authors     []string          `gorm:"-" sql:"-"`
	Layers      []string          `gorm:"-" json:"Layers"`
}

// db res
type RuntimeConfig struct {
	api.RuntimeConfiguration
	Id           uint   `gorm:"column:id" json:"-"`
	ArgStr       string `gorm:"column:argstr" json:"-"`
	KataArgStr   string `gorm:"column:kata_arg_str" json:"-"`
	Editable     bool   `gorm:"column:editable" json:"-"`    // 函数是否可在console在线编辑
	DefaultExt   string `gorm:"column:default_ext" json:"-"` // 默认后缀
	DeprecatedAt time.Time
}

type FuncEventSource struct {
	Id                        uint       `json:"-"`
	Uuid                      string     `json:"UUID"`
	Uid                       string     `json:"-"`
	BatchSize                 int        //  一次最多消费多少条消息
	Enabled                   *bool      `gorm:"DEFAULT:true" json:"Enabled,omitempty" `
	FunctionBrn               string     //  绑定的function brn
	EventSourceBrn            string     //  topic名/pipeName, 需要保证function
	FunctionArn               string     `gorm:"-" sql:"-" `
	EventSourceArn            string     `gorm:"-" sql:"-" `
	Type                      string     `json:"Type,omitempty"` // 类型 bms/datahub_topic/datahub_queue
	FunctionName              string     `gorm:"-" json:"FunctionName,omitempty"`
	StartingPosition          string     `gorm:"column:starting_position" json:"StartingPosition,omitempty"` // kalfka topic 起始位置
	StartingPositionTimestamp *time.Time `json:"StartingPositionTimestamp,omitempty"`
	StateTransitionReason     string     // 状态变更原因
	UpdatedAt                 time.Time  `json:"-"`
	CreatedAt                 time.Time  `json:"-"`
	DeletedAt                 time.Time  `gorm:"column:deleted_at;DEFAULT:0000-00-00 00:00:00" json:"-"`

	DatahubConfig
	DatahubConfigStr     *string   `gorm:"column:datahub_config" json:"-"`
	State                string    `json:"State" gorm:"-"`                // aws 状态
	LastProcessingResult string    `json:"LastProcessingResult" gorm:"-"` // aws 最后执行结果
	LastModified         time.Time `gorm:"-" sql:"-"`
	WorkspaceID          string    `gorm:"-" sql:""-" json:"WorkspaceID,omitempty"`
	ClusterID            string    `gorm:"column:cluster_id" json:"clusterID"` //  kafka集群Id
	ConsumerGroupId      string    `gorm:"column:consumer_group_id" json:"ConsumerGroupId,omitempty"`
	UserName             string    `gorm:"column:user_name" json:"UserName,omitempty"` //  rocket集群 ACL 用户名
	Password             string    `gorm:"column:password" json:"Password,omitempty"`  //  rocket集群 ACL 密码
}

type DatahubConfig struct {
	MetaHostEndpoint string `gorm:"-" json:"MetaHostEndpoint,omitempty"` // MetaHost endpoint
	MetaHostPort     int    `gorm:"-" json:"MetaHostPort,omitempty"`     // MetaHost port
	ClusterName      string `gorm:"-" json:"ClusterName,omitempty"`      // 集群名
	PipeName         string `gorm:"-" json:"PipeName,omitempty"`         // pipe名
	PipeletNum       uint32 `gorm:"-" json:"PipeletNum,omitempty"`       // 订阅PipiletNum
	StartPoint       int64  `gorm:"-" json:"StartPoint,omitempty"`       // 起始订阅点  正常情况下id为正整数, 2个特殊的点 -1: 表示pipelet内的最新一条消息；-2: 表示pipelet内最旧的一条消息
	AclName          string `gorm:"-" json:"ACLName,omitempty"`          // ACL name
	AclPassword      string `gorm:"-" json:"ACLPassword,omitempty"`      // ACL passwd
}

/*
	type DatahubConfig struct {
		MetaHostEndpoint string `gorm:"column:meta_host_endpoint" json:"MetaHostEndpoint,omitempty"` // MetaHost endpoint
		MetaHostPort     int    `gorm:"column:meta_host_port" json:"MetaHostPort,omitempty"`         // MetaHost port
		ClusterName      string `gorm:"column:cluster_name" json:"ClusterName,omitempty"`            // 集群名
		PipeName         string `gorm:"column:pipe_name" json:"PipeName,omitempty"`                  // pipe名
		PipeletNum       uint32 `gorm:"column:pipelet_num" json:"PipeletNum,omitempty"`              // 订阅PipiletNum
		StartPoint       int64  `gorm:"column:start_point" json:"StartPoint,omitempty"`              // 起始订阅点  正常情况下id为正整数, 2个特殊的点 -1: 表示pipelet内的最新一条消息；-2: 表示pipelet内最旧的一条消息
		AclName          string `gorm:"column:acl_name" json:"ACLName,omitempty"`                    // ACL name
		AclPassword      string `gorm:"column:acl_password" json:"ACLPassword,omitempty"`            // ACL passwd
	}
*/
func (FuncEventSource) TableName() string {
	return "event_source_mapping"
}

// AccountLimit xxx
type AccountLimit struct {
	Id  int
	Uid string
	api.AccountLimit
}

// Policy xxx
type Policy struct {
	ID               uint `json:"-"`
	FunctionUid      string
	FunctionName     string
	StatementId      string
	Effect           string
	Action           string
	Resource         string
	PrincipalService string `json:"Principal"`
	Source           string
	SourceAccount    string
	CreatedAt        time.Time
	api.PolicyConfig
}

// UserTestEvent xxx
type UserTestEvent struct {
	ID           int64  `json:"-"`
	Uuid         string `json:"UUID"`
	Uid          string
	FunctionName string
	Title        string `gorm:"size:255"`
	Event        string
	CreatedAt    time.Time
	UpdatedAt    time.Time
}

// StandardTestEvent xxx
type StandardTestEvent struct {
	ID        int
	Title     string `gorm:"size:255"`
	Type      string
	Event     string
	Status    int
	CreatedAt time.Time
	UpdatedAt time.Time
}

type FeatureActivation struct {
	ID        int
	Uid       string
	Type      int
	Status    int
	UpdatedAt time.Time
	CreatedAt time.Time
}

// function reserved
type FunctionReserved struct {
	Id              uint   `json:"-"`
	Uuid            string `json:"UUID"`
	Uid             string
	CommitID        *string   `gorm:"column:commit_id" json:"CommitId"`
	FunctionVersion string    `gorm:"column:function_version" json:"FunctionVersion"`
	FunctionName    string    `gorm:"column:function_name" json:"FunctionName"`
	FunctionBrn     string    `gorm:"column:function_brn" json:"FunctionBrn"`
	MemorySize      int       `gorm:"column:memory_size" json:"MemorySize"`       // 函数内存
	ReservedCount   int       `gorm:"column:reserved_count" json:"ReservedCount"` // 预留实例个数
	RealCount       int       `gorm:"column:real_count"`                          // 实际成功预留实例个数
	Status          int       `gorm:"column:status" json:"Status"`                // 预留实例状态，0-失败，1-创建中，2-成功
	ReservedTs      int64     `gorm:"column:reserved_ts" json:"ReservedTs"`       // 上次预留更新时间
	UpdatedAt       time.Time `json:"-"`
	CreatedAt       time.Time `json:"-"`
	DeletedAt       time.Time `gorm:"column:deleted_at;DEFAULT:0000-00-00 00:00:00" json:"-"`
}

// db res
type Service struct {
	Id          uint `json:"-"`
	Uid         string
	ServiceName string  `valid:"optional,matches(^[a-zA-Z0-9-_]+$),runelength(1|50)"`
	ServiceDesc *string `gorm:"default:''"`
	ServiceConf string
	Region      string
	Status      int
	UpdatedAt   time.Time
	CreatedAt   time.Time

	ServiceConfig
}

type ServiceConfig struct {
	ServiceConfig map[string]string `gorm:"-" json:"ServiceConfig"`
}

type CountServiceFunResObj struct {
	ResMap map[string]int
}

type ServiceWithFun struct {
	Service
	FuncCount int
}

// NetworkConfig 函数 vpc、dns 等网络配置
type NetworkConfig struct {
	Id        uint      `json:"-"`
	UpdatedAt time.Time `json:"-"`
	CreatedAt time.Time `json:"-"`
	api.NetworkConfig
}

// BillingPrepayResource 预付费资源绑定信息表
type BillingPrepayResource struct {
	Id            int64     `json:"-"`
	AccountId     string    `gorm:"column:account_id" json:"accountId"`
	Region        string    `gorm:"column:region" json:"region"`
	OrderId       string    `gorm:"column:order_id" json:"orderId"`
	ResourceType  string    `gorm:"column:resource_type" json:"resourceType"`
	ResourceState string    `gorm:"column:resource_state" json:"resourceState"`
	ResourceId    string    `gorm:"column:resource_id" json:"resourceId"`
	OrderState    string    `gorm:"column:order_state" json:"orderState"`
	ProductType   string    `gorm:"column:product_type" json:"productType"`
	TimeUnit      string    `gorm:"column:time_unit" json:"timeUnit"`
	UpdatedAt     time.Time `gorm:"column:updated_at;DEFAULT:0000-00-00 00:00:00" json:"updatedAt"`
	CreatedAt     time.Time `gorm:"column:created_at;DEFAULT:0000-00-00 00:00:00" json:"createdAt"`
}

// AutoRenewRule 自动续费规则
type AutoRenewRule struct {
	Id            int64     `json:"-"`
	AccountId     string    `gorm:"column:account_id" json:"accountId"`
	Region        string    `gorm:"column:region" json:"region"`
	OrderId       string    `gorm:"column:order_id" json:"orderId"`
	ServiceId     string    `gorm:"column:service_id" json:"serviceId"`          // 资源Ids，对应量包packageName
	RenewState    int       `gorm:"column:renew_state" json:"renewState"`        // 自动续费规则创建状态(1:待创建，2：创建中，3：创建完成，4：已删除)
	RenewTimeUnit string    `gorm:"column:renew_time_unit" json:"renewTimeUnit"` // 每次的续费周期，相当于续费单位，支持month，year
	RenewTime     int       `gorm:"column:renew_time" json:"renewTime"`          // 每次续费时长
	CreatedAt     time.Time `gorm:"column:created_at" json:"createdAt"`          // 自动续费规则创建时间
	UpdatedAt     time.Time `gorm:"column:updated_at" json:"updatedAt"`          // 自动续费规则更新时间
}
