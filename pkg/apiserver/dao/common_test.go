package dao_test

import (
	"database/sql/driver"
	"fmt"
	"log"
	"regexp"
	"testing"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/jinzhu/gorm"
)

type TestQueryFunc func(t *testing.T, m sqlmock.Sqlmock, db *gorm.DB)

func FixedFullRe(s string) string {
	return fmt.Sprintf("^%s$", regexp.QuoteMeta(s))
}

func NewDB() (sqlmock.Sqlmock, *gorm.DB) {
	db, mock, err := sqlmock.New()
	if err != nil {
		log.Fatalf("can't create sqlmock: %s", err)
	}

	gormDB, gerr := gorm.Open("mysql", db)
	if gerr != nil {
		log.Fatalf("can't open gorm connection: %s", err)
	}
	gormDB.LogMode(true)

	return mock, gormDB.Set("gorm:update_column", true)
}

func GetRowWithFields(fields []driver.Value) *sqlmock.Rows {
	fieldNames := []string{}
	for i := range fields {
		fieldNames = append(fieldNames, fmt.Sprintf("f%d", i))
	}

	return sqlmock.NewRows(fieldNames).AddRow(fields...)
}

func CheckMock(t *testing.T, mock sqlmock.Sqlmock) {
	if err := mock.ExpectationsWereMet(); err != nil {
		t.Errorf("there were unfulfilled expections: %s", err)
	}
}
