package dao_test

import (
	"testing"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/stretchr/testify/assert"

	"icode.baidu.com/baidu/faas/kun/pkg/api"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/code"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/dao"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/dbengine"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/global"
	"icode.baidu.com/baidu/faas/kun/pkg/util/convert"
)

func TestGenerateFuncBrn(t *testing.T) {
	exp := dao.Function{
		Region:      "bj",
		Uid:         "7777",
		FunctionBrn: "brn:bce:cfc:bj:cb691571fad8810e85f59a4ab1c1fd6a:function:woshifunction:2",
		FunctionConfig: api.FunctionConfig{
			FunctionName: "woshifunction",
			Version:      "2",
		},
	}
	f := new(dao.Function)
	f.Version = "2"
	f.Uid = "7777"
	f.Region = "bj"
	f.FunctionName = "woshifunction"
	f.FunctionBrnInit()
	assert.Equal(t, exp, *f)
}

func TestCreateFunctionPre(t *testing.T) {
	e := new(api.Environment)
	exp := dao.Function{
		FunctionConfig: api.FunctionConfig{
			MemorySize:                   convert.Int(128),
			Environment:                  e,
			LogType:                      "",
			Role:                         convert.String(""),
			ReservedConcurrentExecutions: convert.Int(0),
			PodConcurrentQuota:           &api.DefaultPodConcurrentQuota,
		},
		Description:          convert.String(""),
		VersionDesc:          convert.String(""),
		Timeout:              convert.Int(8),
		EnvironmentStr:       `{"Variables":{}}`,
		VpcConfigStr:         convert.String(""),
		AsyncInvokeConfigStr: convert.String(""),
		CFSConfigStr:         convert.String(""),
	}
	f := new(dao.Function)

	f.Environment = e
	f.PodConcurrentQuota = nil
	f.CreateFunctionPre()
	f.PodConcurrentQuota = convert.Int(0)
	f.CFSConfig = &api.CFSConfig{}
	f.CreateFunctionPre()
	assert.Equal(t, exp.Description, f.Description)
}

// TestCreateFunctionPre_CustomRuntime 测试函数CreateFunctionPre_CustomRuntime，该函数用于在创建函数之前对其进行处理。
// 该函数会将custom runtime配置设置为默认最大并发度，并将其序列化成字符串。
// 参数：t *testing.T - 类型为*testing.T的指针，表示当前测试用例的上下文信息。
func TestCreateFunctionPre_CustomRuntime(t *testing.T) {
	e := new(api.Environment)
	f := new(dao.Function)
	f.Environment = e
	f.Runtime = "custom.ubuntu2204.python3.10"
	f.PodConcurrentQuota = nil

	// 设置custom runtime配置
	customConfig := &api.CustomRuntimeConfig{
		Port:    8080,
		Command: []string{"/bin/sh"},
		Args:    []string{"-c", "echo 'hello'"},
	}
	f.CustomRuntimeConfig = customConfig

	f.CreateFunctionPre()

	// 验证custom runtime的并发度被设置为默认最大值
	assert.Equal(t, api.DefaultPodConcurrentQuotaMax, *f.PodConcurrentQuota)

	// 验证custom runtime配置被序列化到字符串
	assert.Contains(t, f.CustomRuntimeConfigStr, `"port":8080`)
	assert.Contains(t, f.CustomRuntimeConfigStr, `"command":["/bin/sh"]`)
	assert.Contains(t, f.CustomRuntimeConfigStr, `"args":["-c","echo 'hello'"]`)
}

// TestCreateFunctionPre_NonCustomRuntime 测试函数CreateFunctionPre_NonCustomRuntime，用于验证非自定义运行时的情况下，并发度和custom runtime配置字符串是否正确设置。
func TestCreateFunctionPre_NonCustomRuntime(t *testing.T) {
	e := new(api.Environment)
	f := new(dao.Function)
	f.Environment = e
	f.Runtime = "nodejs8.5" // 非custom runtime
	f.PodConcurrentQuota = convert.Int(5)

	// 设置custom runtime配置（非custom runtime不应该处理）
	customConfig := &api.CustomRuntimeConfig{
		Port:    8080,
		Command: []string{"/bin/sh"},
	}
	f.CustomRuntimeConfig = customConfig

	f.CreateFunctionPre()

	// 验证非custom runtime的并发度保持不变
	assert.Equal(t, 5, *f.PodConcurrentQuota)

	// 验证custom runtime配置字符串为空（非custom runtime不会设置这个字段）
	assert.Equal(t, "", f.CustomRuntimeConfigStr)
}

func TestCreateRemoteFSFunctionPre(t *testing.T) {
	e := new(api.Environment)
	exp := dao.Function{
		FunctionConfig: api.FunctionConfig{
			MemorySize:                   convert.Int(128),
			Environment:                  e,
			LogType:                      "",
			Role:                         convert.String(""),
			ReservedConcurrentExecutions: convert.Int(0),
			PodConcurrentQuota:           convert.Int(0),
		},
		Description:          convert.String(""),
		VersionDesc:          convert.String(""),
		Timeout:              convert.Int(8),
		EnvironmentStr:       `{"Variables":{}}`,
		VpcConfigStr:         convert.String(""),
		AsyncInvokeConfigStr: convert.String(""),
		CFSConfigStr:         convert.String(""),
	}
	f := new(dao.Function)

	f.Environment = e
	f.CreateFunctionPre()
	assert.Equal(t, exp.Description, f.Description)
}

// TestUpdateFunctionPre_CustomRuntime t: *testing.T 类型，用于存放测试结果
// 返回值：无返回值
func TestUpdateFunctionPre_CustomRuntime(t *testing.T) {
	f := new(dao.Function)
	f.Runtime = "custom.ubuntu2204.python3.10"

	// 设置custom runtime配置
	customConfig := &api.CustomRuntimeConfig{
		Port:    9000,
		Command: []string{"/usr/bin/python3"},
		Args:    []string{"app.py"},
	}
	f.CustomRuntimeConfig = customConfig

	f.UpdateFunctionPre()

	// 验证custom runtime配置被序列化到字符串
	assert.Contains(t, f.CustomRuntimeConfigStr, `"port":9000`)
	assert.Contains(t, f.CustomRuntimeConfigStr, `"command":["/usr/bin/python3"]`)
	assert.Contains(t, f.CustomRuntimeConfigStr, `"args":["app.py"]`)
}

// TestUpdateFunctionPre_NonCustomRuntime 测试函数UpdateFunctionPre_NonCustomRuntime，用于验证非自定义运行时的情况下，是否正确处理custom runtime配置
func TestUpdateFunctionPre_NonCustomRuntime(t *testing.T) {
	f := new(dao.Function)
	f.Runtime = "nodejs8.5" // 非custom runtime

	// 设置custom runtime配置（非custom runtime不应该处理）
	customConfig := &api.CustomRuntimeConfig{
		Port:    9000,
		Command: []string{"/usr/bin/python3"},
	}
	f.CustomRuntimeConfig = customConfig

	f.UpdateFunctionPre()

	// 验证custom runtime配置字符串不为空（UpdateFunctionPre会处理所有非nil的CustomRuntimeConfig）
	// 根据代码逻辑，UpdateFunctionPre会处理所有非nil的CustomRuntimeConfig，不管runtime类型
	assert.Contains(t, f.CustomRuntimeConfigStr, `"port":9000`)
	assert.Contains(t, f.CustomRuntimeConfigStr, `"command":["/usr/bin/python3"]`)
}

func TestGetLayerSampleList(t *testing.T) {
	layerStr := "brn:bce:cfc:bj:640c8817bd1de2928d47256dd0620ce5:layer:consoletestlayer1:2"
	global.MockAC()
	code.MockCode()
	var m sqlmock.Sqlmock
	m, dbengine.Engine = global.MockDB()
	m.ExpectQuery("SELECT").WillReturnRows(global.GetRowsLayer(global.GetTestLayers(1)))
	f := new(dao.Function)
	layers := f.GetLayerSampleList(layerStr)
	assert.Equal(t, len(layers), 1)
}

func TestDealResFunction(t *testing.T) {
	f := new(dao.Function)
	f.Version = "2"
	f.Uid = "7777"
	f.Region = "bj"
	f.FunctionName = "test_woshifunction"
	f.WorkspaceID = "test"

	name := "woshifunction"
	f.DealResFunction()
	assert.Equal(t, name, f.FunctionName)
}

// TestDealResFunction_CustomRuntime 测试函数DealResFunction的自定义运行时功能，包括反序列化custom runtime配置字符串
func TestDealResFunction_CustomRuntime(t *testing.T) {
	f := new(dao.Function)
	f.Version = "2"
	f.Uid = "7777"
	f.Region = "bj"
	f.FunctionName = "test_woshifunction"
	f.WorkspaceID = "test"

	// 设置custom runtime配置字符串
	f.CustomRuntimeConfigStr = `{"port":8080,"command":["/bin/sh"],"args":["-c","echo 'hello'"]}`

	f.DealResFunction()

	// 验证custom runtime配置被正确反序列化
	assert.NotNil(t, f.CustomRuntimeConfig)
	assert.Equal(t, 8080, f.CustomRuntimeConfig.Port)
	assert.Equal(t, []string{"/bin/sh"}, f.CustomRuntimeConfig.Command)
	assert.Equal(t, []string{"-c", "echo 'hello'"}, f.CustomRuntimeConfig.Args)
}

// TestDealResFunction_EmptyCustomRuntime 测试函数DealResFunction_EmptyCustomRuntime，该函数用于处理Function结构体中的custom runtime配置字段。
// 该函数接受一个*dao.Function类型的参数，并返回void。
// 该函数在处理完成后，应该将custom runtime配置字段转换为对应的结构体，并且验证其值是否正确。
func TestDealResFunction_EmptyCustomRuntime(t *testing.T) {
	f := new(dao.Function)
	f.Version = "2"
	f.Uid = "7777"
	f.Region = "bj"
	f.FunctionName = "test_woshifunction"
	f.WorkspaceID = "test"

	// 设置空的custom runtime配置字符串
	f.CustomRuntimeConfigStr = ""

	f.DealResFunction()

	// 验证custom runtime配置为nil（空字符串反序列化后为nil）
	// 注意：json.Unmarshal空字符串到结构体指针会创建一个空的结构体，不是nil
	assert.NotNil(t, f.CustomRuntimeConfig)
	assert.Equal(t, 0, f.CustomRuntimeConfig.Port)
	assert.Nil(t, f.CustomRuntimeConfig.Command)
	assert.Nil(t, f.CustomRuntimeConfig.Args)
}
