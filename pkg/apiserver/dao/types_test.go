package dao_test

import (
	"testing"
	"time"

	"github.com/asaskevich/govalidator"
	"github.com/stretchr/testify/assert"
	// "github.com/stretchr/testify/assert"

	"icode.baidu.com/baidu/faas/kun/pkg/api"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/dao"
	"icode.baidu.com/baidu/faas/kun/pkg/util/convert"
)

func TestValidFunctionConfig(t *testing.T) {
	f := dao.Function{
		FunctionConfig: api.FunctionConfig{
			FunctionName: "aaa",
			Handler:      "index.hand",
			Version:      "$LATEST",
			Runtime:      "nodejs6.11",
			MemorySize:   convert.Int(12),
		},
		Timeout: convert.Int(12),
	}

	result, err := govalidator.ValidateStruct(f)
	if err != nil {
		println("error: " + err.Error())
	}
	println(result)

	// t.Errorf("cc")

	// assert.Equal(t, exp, *f)
}

// TestFuncEventSource 测试 FuncEventSource 的功能，包括字段值的验证。
// 参数 t *testing.T：表示当前正在运行的单元测试。
// 返回值 bool：表示是否通过了测试，true 表示通过，false 表示未通过。
func TestFuncEventSource(t *testing.T) {
	// 创建一个 FuncEventSource 实例
	now := time.Now()
	enabled := true
	eventSource := &dao.FuncEventSource{
		Uuid:                      "test-uuid",
		Uid:                       "test-uid",
		BatchSize:                 10,
		Enabled:                   &enabled,
		FunctionBrn:               "brn:bce:cfc:bj:test-function-brn",
		EventSourceBrn:            "test-event-source-brn",
		FunctionArn:               "brn:bce:cfc:bj:test-function-arn",
		EventSourceArn:            "test-event-source-arn",
		Type:                      "RocketMQ",
		FunctionName:              "test-function-name",
		StateTransitionReason:     "test-reason",
		StartingPosition:          "LATEST",
		StartingPositionTimestamp: &now,
		ClusterID:                 "test-cluster-id",
		UserName:                  "test-user",
		Password:                  "test-password",
		LastModified:              now,
	}

	// 验证字段值
	assert.Equal(t, "test-uuid", eventSource.Uuid)
	assert.Equal(t, "test-uid", eventSource.Uid)
	assert.Equal(t, 10, eventSource.BatchSize)
	assert.Equal(t, &enabled, eventSource.Enabled)
	assert.Equal(t, "brn:bce:cfc:bj:test-function-brn", eventSource.FunctionBrn)
	assert.Equal(t, "test-event-source-brn", eventSource.EventSourceBrn)
	assert.Equal(t, "brn:bce:cfc:bj:test-function-arn", eventSource.FunctionArn)
	assert.Equal(t, "test-event-source-arn", eventSource.EventSourceArn)
	assert.Equal(t, "RocketMQ", eventSource.Type)
	assert.Equal(t, "test-function-name", eventSource.FunctionName)
	assert.Equal(t, "test-reason", eventSource.StateTransitionReason)
	assert.Equal(t, "LATEST", eventSource.StartingPosition)
	assert.Equal(t, &now, eventSource.StartingPositionTimestamp)
	assert.Equal(t, "test-cluster-id", eventSource.ClusterID)
	assert.Equal(t, "test-user", eventSource.UserName)
	assert.Equal(t, "test-password", eventSource.Password)
	assert.Equal(t, now, eventSource.LastModified)
}
