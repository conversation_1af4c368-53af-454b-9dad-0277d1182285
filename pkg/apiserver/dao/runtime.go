package dao

import (
	"fmt"

	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/dbengine"
	apiErr "icode.baidu.com/baidu/faas/kun/pkg/apiserver/error"
	kunErr "icode.baidu.com/baidu/faas/kun/pkg/error"
)

func FindRuntimeConf(r *RuntimeConfig) error {

	db := dbengine.DBInstance().Where(r).First(r)

	if db.RecordNotFound() == true {
		cause := fmt.Sprintf("[can not find any runtime][RuntimeName:%s]", r.Name)
		return apiErr.NewResourceNotFoundException(cause, nil)
	}

	if err := db.Error; err != nil {
		cause := fmt.Sprintf("[select runtime faied][RuntimeName:%s]", r.Name)
		return kunErr.NewServiceException(cause, err)
	}
	return nil
}

func ListRuntimeConf() ([]RuntimeConfig, error) {

	slice := make([]RuntimeConfig, 0)
	db := dbengine.DBInstance().Find(&slice)

	if db.Error != nil {
		cause := fmt.Sprintf("[ListRuntimeConfig failed]")
		e := kunErr.NewServiceException(cause, db.Error)
		return nil, e
	}
	return slice, nil
}
