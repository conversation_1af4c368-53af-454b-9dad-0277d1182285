package dao_test

import (
	"github.com/DATA-DOG/go-sqlmock"
	"github.com/stretchr/testify/assert"
	"icode.baidu.com/baidu/faas/kun/pkg/api"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/dao"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/dbengine"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/global"
	"testing"
	"time"
)

func TestCreateFeatureActivation(t *testing.T) {
	var m sqlmock.Sqlmock
	m, dbengine.Engine = NewDB()

	f := &dao.FeatureActivation{
		ID:        1,
		Uid:       "uid-1",
		Type:      api.FeatureTypeCfcEdge,
		Status:    api.UserFeatureActivated,
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}

	req := "INSERT INTO `feature_activations` (`id`,`uid`,`type`,`status`,`updated_at`,`created_at`) VALUES (?,?,?,?,?,?)"
	m.ExpectExec(FixedFullRe(req)).
		WillReturnResult(sqlmock.NewResult(1, 1))
	err := dao.CreateFeatureActivation(f)

	assert.Nil(t, err)
}

func TestGetFeatureActivation(t *testing.T) {
	var m sqlmock.Sqlmock
	m, dbengine.Engine = NewDB()

	exp := global.GetFeatureActivation(2)

	m.ExpectQuery(FixedFullRe("SELECT * FROM `feature_activations`")).WillReturnRows(global.GetRowsFeatureActivation(exp))
	ret, _ := dao.FindFeatureActivation(nil)
	assert.Equal(t, exp, ret)
}

func TestFindOneFeatureActivation(t *testing.T) {
	var m sqlmock.Sqlmock
	m, dbengine.Engine = NewDB()

	exp := global.GetFeatureActivation(1)
	m.ExpectQuery(FixedFullRe("SELECT * FROM `feature_activations` WHERE (`feature_activations`.`uid` = ?) ORDER BY `feature_activations`.`id` ASC LIMIT 1")).WillReturnRows(global.GetRowsFeatureActivation(exp))
	err := dao.FindOneFeatureActivation(&dao.FeatureActivation{Uid: "uid-0"})
	assert.Nil(t, err)
}

func TestUpdateFeatureActivation(t *testing.T) {
	var m sqlmock.Sqlmock
	m, dbengine.Engine = NewDB()

	cond := dao.FeatureActivation{
		Uid:    "uiduid",
		Type:   api.FeatureTypeCfcEdge,
		Status: api.UserFeatureActivated,
	}

	updateSql := "UPDATE `feature_activations` SET `status` = ? WHERE (`feature_activations`.`uid` = ?) AND (`feature_activations`.`type` = ?) AND (`feature_activations`.`status` = ?)"
	m.ExpectExec(FixedFullRe(updateSql)).WithArgs(api.UserFeatureInactive, cond.Uid, cond.Type, cond.Status).WillReturnResult(sqlmock.NewResult(0, 1))

	err := dao.UpdateFeatureActivationStatus(cond, api.UserFeatureInactive)
	assert.Nil(t, err)
}
