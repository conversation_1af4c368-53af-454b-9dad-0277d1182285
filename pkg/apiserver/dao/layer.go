package dao

import (
	"fmt"
	"time"

	"github.com/jinzhu/gorm"

	"icode.baidu.com/baidu/faas/kun/pkg/api"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/dbengine"
	apiErr "icode.baidu.com/baidu/faas/kun/pkg/apiserver/error"
	kunErr "icode.baidu.com/baidu/faas/kun/pkg/error"
)

func CreateLayer(layer *api.Layer) error {
	if err := dbengine.DBInstance().Create(layer).Error; err != nil {
		cause := fmt.Sprintf("[Create layer Failed][layer: %v]", layer)
		return kunErr.NewServiceException(cause, err)
	}
	return nil
}

func FindOneLayer(layer *api.Layer) error {
	db := dbengine.DBInstance().Unscoped().Scopes(ScopeRudDeletedAt).Where(layer).Find(layer)
	if db.RecordNotFound() == true {
		cause := fmt.Sprintf("[select layer not found][layer: %v]", layer)
		return apiErr.NewResourceNotFoundException(cause, nil)
	}
	if err := db.Error; err != nil {
		cause := fmt.Sprintf("[select layer failed][layer: %v]", layer)
		return apiErr.NewResourceConflictException(cause, err)
	}
	return nil
}

func FindLayers(layer *api.Layer) (*[]api.Layer, error) {

	resSlice := make([]api.Layer, 0)
	db := dbengine.DBInstance().Where(layer)
	db = db.Unscoped().Scopes(ScopeRudDeletedAt)
	db = db.Find(&resSlice)
	if db.RecordNotFound() == true {
		cause := fmt.Sprintf("[select layer not found][layer: %v]", layer)
		return nil, apiErr.NewResourceNotFoundException(cause, nil)
	}
	if db.Error != nil {
		cause := fmt.Sprintf("[select Layer failed][Layer: %v]", layer)
		return nil, apiErr.NewResourceConflictException(cause, db.Error)
	}
	return &resSlice, nil
}

func DeleteLayer(tx *gorm.DB, layer *api.Layer) error {
	db := tx.Model(layer).Where(*layer).Unscoped().Scopes(ScopeRudDeletedAt)
	err := db.Update(&api.Layer{DeletedAt: time.Now()}).Error
	if err != nil {
		cause := fmt.Sprintf("[delete Layer failed][Layer: %v][err: %s]", layer, err.Error())
		e := kunErr.NewServiceException(cause, err)
		return &e
	}
	return nil
}

//获取下一个版本号
func GetLayerPreVersion(layer *api.Layer) (int64, error) {
	data := new(api.Layer)
	data.Uid = layer.Uid
	data.LayerName = layer.LayerName
	err := dbengine.DBInstance().Unscoped().Where(data).Last(data).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			// 版本号1开始
			return 1, nil
		}
		return 0, err
	}
	return data.Version + 1, nil
}

//获取最后一个版本的Layer
func GetLatestLayer(layer *api.Layer) (*api.Layer, error) {
	data := new(api.Layer)
	data.Uid = layer.Uid
	data.LayerName = layer.LayerName
	data.Enabled = layer.Enabled
	data.IsVended = layer.IsVended
	err := dbengine.DBInstance().Unscoped().Scopes(ScopeRudDeletedAt).Where(data).Last(data).Error
	if err != nil {
		return nil, err
	}
	return data, nil
}
