package dao_test

import (
	"github.com/DATA-DOG/go-sqlmock"
	"github.com/stretchr/testify/assert"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/dao"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/dbengine"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/global"
	"testing"
)

func TestListServices(t *testing.T) {
	var m sqlmock.Sqlmock
	m, dbengine.Engine = NewDB()
	exp := global.GetTestService(2)

	m.ExpectQuery(FixedFullRe("SELECT * FROM `services` ORDER BY created_at desc")).
		WillReturnRows(global.GetRowsService(exp))
	act, _ := dao.ListServices(nil)

	assert.Equal(t, exp, *act)
}
func TestFindOneService(t *testing.T) {
	var m sqlmock.Sqlmock
	m, dbengine.Engine = NewDB()
	exp := global.GetTestService(1)
	req := "SELECT * FROM `services` WHERE (`services`.`service_name` = ?) AND (`status` = ) ORDER BY `services`.`id` ASC LIMIT 1 "
	m.ExpectQuery(FixedFullRe(req)).
		WillReturnRows(global.GetRowsService(exp))

	act := &dao.Service{
		ServiceName: "test_name",
	}

	err := dao.FindOneService(act)
	assert.NotNil(t, err)
}

func TestCreateService(t *testing.T) {
	var m sqlmock.Sqlmock
	m, dbengine.Engine = NewDB()

	act := &dao.Service{
		Uid: "123456",
		ServiceName: "test_name",
	}

	req3 := "INSERT INTO"
	m.ExpectExec(req3).
		WillReturnResult(sqlmock.NewResult(1, 1))

	err := dao.CreateService(act)
	assert.Nil(t, err)
}

func TestUpdateService(t *testing.T) {
	var m sqlmock.Sqlmock
	m, dbengine.Engine = NewDB()

	cond := &dao.Service{
		ServiceName: "test_name",
	}

	act := &dao.Service{
		Uid: "123456",
	}

	req := "UPDATE `services` SET `uid` = ? WHERE (`services`.`service_name` = ?)"
	m.ExpectExec(FixedFullRe(req)).
		WithArgs(act.Uid, cond.ServiceName).
		WillReturnResult(sqlmock.NewResult(0, 1))

	err := dao.UpdateService(*cond, act)
	assert.Nil(t, err)
}

