package dao_test

import (
	"fmt"
	"testing"
	"time"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/stretchr/testify/assert"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/dao"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/dbengine"
)

//Policy dao相关test

func TestListPolicies(t *testing.T) {
	var m sqlmock.Sqlmock
	m, dbengine.Engine = NewDB()
	exp := getTestPolicy(2)

	req := "SELECT * FROM `policies` WHERE (`policies`.`resource` = ?)"
	m.ExpectQuery(FixedFullRe(req)).
		WillReturnRows(getRowsPolicy(exp))

	pol := &dao.Policy{
		Resource: "brn:bce:cfc:bj:640c8817bd1de2928d47256dd0620ce5:function:hello:$LATEST",
	}
	act, _ := dao.ListPolicies(pol)

	assert.Equal(t, exp, *act)
}

func TestFindOnePolicy(t *testing.T) {
	var m sqlmock.Sqlmock
	m, dbengine.Engine = NewDB()
	exp := getTestPolicy(1)

	m.ExpectQuery(".*").
		WillReturnRows(getRowsPolicy(exp))

	pol := &dao.Policy{
		Resource:    "brn:bce:cfc:bj:640c8817bd1de2928d47256dd0620ce5:function:hello:$LATEST",
		StatementId: "123",
	}
	err := dao.FindOnePolicy(pol)

	assert.Nil(t, err)
}

func TestFindOnePolicyFailed(t *testing.T) {
	var m sqlmock.Sqlmock
	m, dbengine.Engine = NewDB()
	exp := getTestPolicy(0)

	m.ExpectQuery(".*").
		WillReturnRows(getRowsPolicy(exp))

	pol := &dao.Policy{
		Resource:    "brn:bce:cfc:bj:640c8817bd1de2928d47256dd0620ce5:function:hello:$LATEST",
		StatementId: "123",
	}
	err := dao.FindOnePolicy(pol)

	assert.NotNil(t, err)
}

func TestCreatePolicy(t *testing.T) {
	var m sqlmock.Sqlmock
	m, dbengine.Engine = NewDB()

	pol := &dao.Policy{
		StatementId: "123456",
	}
	req := "INSERT INTO `policies` (`function_uid`,`function_name`,`statement_id`,`effect`,`action`,`resource`,`principal_service`,`source`,`source_account`,`created_at`) VALUES (?,?,?,?,?,?,?,?,?,?)"

	m.ExpectExec(FixedFullRe(req)).
		WillReturnResult(sqlmock.NewResult(1, 1))

	err := dao.CreatePolicy(pol, nil)
	assert.Nil(t, err)
}
func TestUpdatePolicy(t *testing.T) {
	var m sqlmock.Sqlmock
	m, dbengine.Engine = NewDB()

	pol := &dao.Policy{
		Resource:     "helloResource",
		FunctionName: "hello",
		Source:       "api-gateway/GWGP-gahfajttJwg/apitrigger123",
	}
	updatePolicy := &dao.Policy{
		Resource:     "testResource",
		FunctionName: "test",
		Source:       "api-gateway/GWGP-gahfajttJwg/apitrigger123",
	}

	m.ExpectExec("^UPDATE (.+)").
		WillReturnResult(sqlmock.NewResult(0, 1))

	err := dao.UpdatePolicy(*pol, updatePolicy, dbengine.Engine)
	assert.Nil(t, err)
}

func TestDeletePolicy(t *testing.T) {
	var m sqlmock.Sqlmock
	m, dbengine.Engine = NewDB()

	act := &dao.Policy{
		StatementId: "123456",
	}
	req := "DELETE FROM `policies` WHERE (`policies`.`statement_id` = ?)"

	exp := getTestPolicy(2)

	m.ExpectQuery(".*").
		WillReturnRows(getRowsPolicy(exp))

	m.ExpectExec(FixedFullRe(req)).
		WithArgs(act.StatementId).
		WillReturnResult(sqlmock.NewResult(0, 1))

	err := dao.DeletePolicy(act, dbengine.Engine)
	assert.Nil(t, err)
}

func getTestPolicy(n int) (ret []dao.Policy) {
	for i := 0; i < n; i++ {
		tmp := dao.Policy{
			ID:               uint(i),
			FunctionUid:      fmt.Sprintf("8555-c233c7ebd495-%d", i),
			FunctionName:     fmt.Sprintf("function_name_%d", i),
			StatementId:      fmt.Sprintf("%d-4f3d-8555-c233c7ebd495", i),
			Effect:           "Allow",
			Action:           "cfc:InvokeFunction",
			Resource:         "brn:bce:cfc:bj:640c8817bd1de2928d47256dd0620ce5:function:hello:$LATEST",
			PrincipalService: "bos.baidubce.com",
			Source:           fmt.Sprintf("brn:bce:bos:bj:640c8817bd1de2928d47256dd0620ce5:test-bucket-%d", i),
			SourceAccount:    "",
			CreatedAt:        time.Now(),
		}
		ret = append(ret, tmp)
	}
	return
}

func getRowsPolicy(pol []dao.Policy) *sqlmock.Rows {
	var fieldNames = []string{"id", "function_uid", "function_name", "statement_id", "effect", "action", "resource", "principal_service", "source", "source_account", "created_at"}
	rows := sqlmock.NewRows(fieldNames)
	for _, po := range pol {
		rows = rows.AddRow(po.ID, po.FunctionUid, po.FunctionName, po.StatementId, po.Effect, po.Action, po.Resource, po.PrincipalService, po.Source, po.SourceAccount, po.CreatedAt)
	}
	return rows
}
