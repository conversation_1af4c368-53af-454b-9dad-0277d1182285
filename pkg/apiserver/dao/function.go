package dao

import (
	"context"
	"errors"
	"fmt"
	"time"

	"github.com/jinzhu/gorm"

	"icode.baidu.com/baidu/faas/kun/pkg/api"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/dbengine"
	apiErr "icode.baidu.com/baidu/faas/kun/pkg/apiserver/error"
	"icode.baidu.com/baidu/faas/kun/pkg/bce/brn"
	kunErr "icode.baidu.com/baidu/faas/kun/pkg/error"
	"icode.baidu.com/baidu/faas/kun/pkg/util/logs"
)

func ScopeRudDeletedAt(db *gorm.DB) *gorm.DB {
	return db.Where("deleted_at IS NULL OR deleted_at='0000-00-00 00:00:00'")
}

func CreateFunc(tx *gorm.DB, f *Function) error {
	if tx == nil {
		tx = dbengine.DBInstance()
	}
	if err := tx.Unscoped().Create(f).Error; err != nil {
		// tx.Rollback()
		cause := fmt.Sprintf("[Create function Failed][function: %v]", f)
		return kunErr.NewServiceException(cause, err)
	}
	return nil
}

//func FindOneFuncWithTrace(ctx context.Context, f *Function) error {
//	//_, span := trace.SpanFromContext(ctx).Tracer().Start(ctx, "findOneFunc")
//	err := FindOneFunc(f)
//	//if err != nil {
//	//	span.SetStatus(codes.Internal, err.Error())
//	//}
//	//defer span.End()
//	return err
//}

func FindOneFunc(f *Function) error {
	db := dbengine.DBInstance().Unscoped().Where(f).Scopes(ScopeRudDeletedAt).First(f)

	if db.RecordNotFound() == true {
		cause := fmt.Sprintf("[can not find any function][condtion: %v]", f)
		return apiErr.NewResourceNotFoundException(cause, nil)
	}
	if err := db.Error; err != nil {
		cause := fmt.Sprintf("[select failed][condtion: %v][err: %v]", f, err.Error())
		return kunErr.NewServiceException(cause, nil)
	}

	return nil
}

func FindOneReservedFunc(f *Function) error {
	db := dbengine.DBInstance().Unscoped().Where(f).First(f)

	if db.RecordNotFound() == true {
		cause := fmt.Sprintf("[can not find any function][condtion: %v]", f)
		return apiErr.NewResourceNotFoundException(cause, nil)
	}
	if err := db.Error; err != nil {
		cause := fmt.Sprintf("[select failed][condtion: %v][err: %v]", f, err.Error())
		return kunErr.NewServiceException(cause, nil)
	}

	return nil
}
func FindFunc(f *Function) (*[]Function, error) {

	functionResSlice := make([]Function, 0)
	db := dbengine.DBInstance().Where(f)
	db = db.Unscoped().Scopes(ScopeRudDeletedAt)
	db = db.Find(&functionResSlice)

	return &functionResSlice, db.Error

}

func UpdateFunc(updateCondition Function, f *Function) error {
	db := dbengine.DBInstance().Model(f).Where(updateCondition).Unscoped().Scopes(ScopeRudDeletedAt).Update(f)
	if err := db.Error; err != nil {
		cause := fmt.Sprintf("[update func failed][condtion: %v][updateFunc: %v][err: %s]", updateCondition, f, err.Error())
		return kunErr.NewServiceException(cause, err)
	}
	// 写入etcd key
	fcMsg := api.NewFunctionCacheNotifyMsg().AddInfo(f.FunctionBrn, f.Version, api.FunctionCacheMethodUpdate)
	a := &Alias{FunctionName: f.FunctionName, Uid: f.Uid, FunctionVersion: "$LATEST"}
	addEtcdAliasInfo(f, a, fcMsg)
	dbengine.NotifyEtcdWhenChange(api.FunctionCacheEtcdPrefix, f.FunctionBrn, fcMsg)
	return nil
}

func DeleteFunc(tx *gorm.DB, f *Function) error {
	// function := Function{FunctionName: functionName, Uid: uid}
	if tx == nil {
		return kunErr.NewServiceException("delete function failed", errors.New("no transaction found for further DB actions"))
	}

	db := tx.Model(f).Where(*f).Unscoped().Scopes(ScopeRudDeletedAt)
	f.DeletedAt = time.Now()
	err := db.Update(f).Error
	if err != nil {
		cause := fmt.Sprintf("[delete function failed][Function: %v][err: %s]", f, err.Error())
		e := kunErr.NewServiceException(cause, err)
		return &e
	}
	if kunErr := DeleteAlias(&Alias{Uid: f.Uid, FunctionName: f.FunctionName, FunctionVersion: f.Version}, tx); kunErr != nil {
		return kunErr
	}

	funcBrn := ""
	if f.Version != "" {
		funcBrn = brn.GenerateFuncBrnString(f.Region, f.Uid, f.FunctionName, f.Version)
	}
	if kunErr := DeletePolicy(&Policy{FunctionUid: f.Uid, FunctionName: f.FunctionName, Resource: funcBrn}, tx); kunErr != nil {
		return kunErr
	}

	return nil
}

func UpdateConcurrency(updateCondition Function, f *Function) error {
	db := dbengine.DBInstance().Model(f).Where(updateCondition).Unscoped().Scopes(ScopeRudDeletedAt).Update(f)
	if err := db.Error; err != nil {
		cause := fmt.Sprintf("[update func concurrency failed][condtion: %v][updateFunc: %v][err: %s]", updateCondition, f, err.Error())
		return kunErr.NewServiceException(cause, err)
	}

	// 写入etcd key
	fcMsg := api.NewFunctionCacheNotifyMsg()
	a := &Alias{FunctionName: updateCondition.FunctionName, Uid: updateCondition.Uid}
	addEtcdAliasInfo(&updateCondition, a, fcMsg)
	addEtcdVersionInfo(&updateCondition, fcMsg)
	dbengine.NotifyEtcdWhenChange(api.FunctionCacheEtcdPrefix, updateCondition.FunctionBrn, fcMsg)

	return nil
}

func addEtcdVersionInfo(f *Function, fcMsg *api.FunctionCacheNotifyMsg) {
	condFunc := &Function{Uid: f.Uid}
	condFunc.FunctionName = f.FunctionName
	fSlice, _ := FindFunc(condFunc)

	for _, f := range *fSlice {
		logs.V(6).Infof("UpdateFunc dbengine.NotifyEtcdWhenFunctionChange key(%s)value(%s)", f.FunctionBrn, f.Version)
		fcMsg.AddInfo(f.FunctionBrn, f.Version, api.FunctionCacheMethodUpdate)
	}
}

func addEtcdAliasInfo(f *Function, a *Alias, fcMsg *api.FunctionCacheNotifyMsg) {
	aSlice := make([]Alias, 0)
	dbengine.DBInstance().Model(&Alias{}).Where(a).Find(&aSlice)
	for _, v := range aSlice {
		logs.V(6).Infof("UpdateFunc dbengine.NotifyEtcdWhenFunctionChange key(%s)value(%s)", v.AliasBrn, v.Name)
		fcMsg.AddInfo(v.AliasBrn, v.Name, api.FunctionCacheMethodUpdate)
	}
}

type countConcurrencyRes struct {
	Sum int
}

func CountUserReservedConcurrency(ctx context.Context, uid string) (int, error) {
	//_, span := trace.SpanFromContext(ctx).Tracer().Start(ctx, "countUserReservedConcurrency")
	//defer span.End()

	var res countConcurrencyRes

	db := dbengine.DBInstance()
	db.Raw("SELECT SUM(concurrency) AS sum FROM functions WHERE `uid` = ? AND `version` = '$LATEST' AND `concurrency` > 0 AND (deleted_at IS NULL OR deleted_at='0000-00-00 00:00:00')", uid).
		Scan(&res)
	if db.Error != nil {
		//span.SetStatus(codes.Internal, db.Error.Error())
	}
	return res.Sum, db.Error
}

// FindAllFuncs 将已 delete 的函数一并拿出来
func FindAllFuncs(f *Function) (*[]Function, error) {
	functionResSlice := make([]Function, 0)
	db := dbengine.DBInstance().Unscoped().Where(f).Find(&functionResSlice)
	return &functionResSlice, db.Error

}

type countServiceFunRes struct {
	ServiceName string
	FunCount    int
}

func CountUserServiceFun(uid string, region string) (*CountServiceFunResObj, error) {
	var res countServiceFunRes
	resObj := new(CountServiceFunResObj)
	db := dbengine.DBInstance()
	rows, err := db.Raw("SELECT service_name, count(1) as fun_count  FROM functions WHERE `uid` = ? AND `region` = ? AND `version` = '$LATEST' "+
		"AND `service_name` != '' AND `source_tag` = '' AND (deleted_at IS NULL OR deleted_at='0000-00-00 00:00:00') group by service_name", uid, region).Rows()
	if err != nil {
		return resObj, err
	}
	defer rows.Close()
	resMap := make(map[string]int)
	for rows.Next() {
		db.ScanRows(rows, &res)
		resMap[res.ServiceName] = res.FunCount
	}
	resObj.ResMap = resMap
	return resObj, err
}
