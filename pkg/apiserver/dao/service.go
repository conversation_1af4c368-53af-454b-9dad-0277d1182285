package dao

import (
	"fmt"
	"github.com/jinzhu/gorm"
	omgErr "icode.baidu.com/baidu/faas/kun/cmd/omg/error"
	"icode.baidu.com/baidu/faas/kun/pkg/api"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/dbengine"
	apiErr "icode.baidu.com/baidu/faas/kun/pkg/apiserver/error"
	kunErr "icode.baidu.com/baidu/faas/kun/pkg/error"
)

func ScopeStatusOnline(db *gorm.DB) *gorm.DB {
	return db.Where("`status` = '1'")
}

func ListServices(s *Service) (*[]Service, error) {
	sResSlice := make([]Service, 0)
	db := dbengine.DBInstance().Where(s).Order("created_at desc").Find(&sResSlice)

	if db.Error != nil {
		cause := fmt.Sprintf("[ListSerives failed]")
		e := kunErr.NewServiceException(cause, db.Error)
		return nil, e
	}
	return &sResSlice, nil
}

func FindOneService(s *Service) error {
	db := dbengine.DBInstance().Where(s).Scopes(ScopeStatusOnline).First(s)
	if db.RecordNotFound() == true {
		cause := fmt.Sprintf("[FindService failed, not found][Service: %v]", s)
		return apiErr.NewResourceNotFoundException(cause, nil)
	}

	if db.Error != nil {
		cause := fmt.Sprintf("[FindService failed][Service: %v]", s)
		return kunErr.NewServiceException(cause, db.Error)
	}
	return nil
}

type ServiceQuery struct {
	Query     string
	Condition string
}

func CreateService(f *Service) error {
	if err := dbengine.DBInstance().Unscoped().Create(f).Error; err != nil {
		cause := fmt.Sprintf("[Create service Failed][service: %v]", f)
		return kunErr.NewServiceException(cause, err)
	}
	return nil
}


func UpdateService(updateCondition Service, s *Service) error {
	db := dbengine.DBInstance().Model(s).Where(updateCondition).Updates(s)
	if db.Error != nil {
		cause := fmt.Sprintf("[UpdateService failed][Service: %v]", s)
		return omgErr.NewSqlIOException(cause, db.Error)
	}
	return nil
}
type countUserServiceRes struct {
	Total int
}

func CountUserService(uid string, region string) (int, error) {
	var res countUserServiceRes
	db := dbengine.DBInstance()
	db.Raw("SELECT count(1) as total  FROM services WHERE `uid` = ? AND `region` = ? AND status = ?", uid, region, api.ServiceOnline).Scan(&res)
	if db.Error != nil {
		return 0, db.Error
	}
	return res.Total, db.Error
}