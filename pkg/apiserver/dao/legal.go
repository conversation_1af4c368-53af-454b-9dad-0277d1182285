package dao

import (
	"fmt"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/dbengine"
	kunErr "icode.baidu.com/baidu/faas/kun/pkg/error"
)

func CreateLegal(legalStatus *LegalStatus) error {
	if err := dbengine.DBInstance().Create(legalStatus).Error; err != nil {
		cause := fmt.Sprintf("[Create legalStatus Failed][legalStatus: %v]", legalStatus)
		return kunErr.NewServiceException(cause, err)
	}
	return nil
}

func UpdateLegal(legalStatus *LegalStatus, newLegalStatus *LegalStatus) error {
	if err := dbengine.DBInstance().Model(newLegalStatus).Where(legalStatus).Update(newLegalStatus).Error; err != nil {
		cause := fmt.Sprintf("[Update legalStatus Failed][legalStatus: %v]", legalStatus)
		return kunErr.NewServiceException(cause, err)
	}
	return nil
}

func FindLegal(legalStatus *LegalStatus) (*[]LegalStatus, error) {
	legalResSlice := make([]LegalStatus, 0)
	db := dbengine.DBInstance().Where(legalStatus)
	db = db.Find(&legalResSlice)
	return &legalResSlice, db.Error
}
