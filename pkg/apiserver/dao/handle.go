package dao

import (
	"strings"

	"icode.baidu.com/baidu/faas/kun/pkg/api"
	"icode.baidu.com/baidu/faas/kun/pkg/bce/brn"
	"icode.baidu.com/baidu/faas/kun/pkg/util/convert"
	"icode.baidu.com/baidu/faas/kun/pkg/util/json"
)

// 创建function初始化
func (f *Function) CreateFunctionPre() {
	if f.MemorySize == nil {
		f.MemorySize = convert.Int(128)
	}
	if f.Timeout == nil {
		f.Timeout = convert.Int(8)
	}
	if f.Description == nil {
		f.Description = convert.String("")
	}
	if f.VersionDesc == nil {
		f.VersionDesc = convert.String("")
	}
	if f.Role == nil {
		f.Role = convert.String("")
	}
	if f.ReservedConcurrentExecutions == nil {
		f.ReservedConcurrentExecutions = convert.Int(0)
	}
	if f.PodConcurrentQuota == nil || *f.PodConcurrentQuota == 0 {
        f.PodConcurrentQuota = new(int)
        *f.PodConcurrentQuota = api.DefaultPodConcurrentQuota
        // f.PodConcurrentQuota = &api.DefaultPodConcurrentQuota
        // 判断 mcp 运行时函数才设置为 DefaultPodConcurrentQuotaMax
        if strings.Contains(f.Runtime, "mcp") {
            *f.PodConcurrentQuota = api.DefaultPodConcurrentQuotaMax
        }
        // 当前自定义运行时的函数默认并发度设置为10
        if strings.HasPrefix(f.Runtime, "custom.") {
            *f.PodConcurrentQuota = api.DefaultPodConcurrentQuotaMax
        }
    }
	tmpStr, _ := json.Marshal(f.Environment)
	f.EnvironmentStr = string(tmpStr)
	if f.Environment == nil || len(f.Environment.Variables) == 0 || len(f.EnvironmentStr) == 0 {
		f.EnvironmentStr = `{"Variables":{}}`
	}

	// function 表里的 vpc str 不再需要，置为空
	f.VpcConfigStr = convert.String("")
	f.FormatAsyncInvokeConfig()
	f.FormatCFSConfig()

	// 如果是自定义运行时，需要设置custom_runtime_config，保存到函数表
	if strings.HasPrefix(f.Runtime, "custom.") {
		customRuntimeConfig, _ := json.Marshal(f.CustomRuntimeConfig)
		f.CustomRuntimeConfigStr = string(customRuntimeConfig)
	}
}

/*
*
CFS配置格式化
*/
func (f *Function) FormatCFSConfig() {
	if f.CFSConfig != nil {
		if f.CFSConfig.RemotePath == nil {
			f.CFSConfig.RemotePath = convert.String(api.DefaulfRemotePath)
		}
		if f.CFSConfig.LocalPath == nil {
			f.CFSConfig.LocalPath = convert.String(api.DefaultLocalPath)
		}
		str, _ := json.Marshal(f.CFSConfig)
		cfsConfigStr := string(str)

		f.CFSConfigStr = &cfsConfigStr
	} else {
		f.CFSConfigStr = convert.String("")
	}
}

func (f *Function) FormatAsyncInvokeConfig() {
	if f.AsyncInvokeConfig != nil {
		if f.AsyncInvokeConfig.MaxRetryIntervalInSeconds == nil {
			f.AsyncInvokeConfig.MaxRetryIntervalInSeconds = convert.Int64(api.DefaultMaxRetryIntervalInSeconds)
		}
		if f.AsyncInvokeConfig.MaxRetryAttempts == nil {
			f.AsyncInvokeConfig.MaxRetryAttempts = convert.Int(api.MaxRetryAttempts)
		}
		asyncConfTmpStr, _ := json.Marshal(f.AsyncInvokeConfig)
		asyncConf := string(asyncConfTmpStr)
		f.AsyncInvokeConfigStr = &asyncConf
	} else {
		f.AsyncInvokeConfigStr = convert.String("")
	}
}

// UpdateFunctionPre UpdateFunctionPre 函数用于在更新函数之前进行一些预处理操作，包括：
// 1. 将 Environment 字段转换为字符串格式，并删除空值；
// 2. 更新自定义运行时相关配置；
// 3. 格式化异步调用配置和容器存储服务（Container File System）配置。
func (f *Function) UpdateFunctionPre() {
	if f.Environment != nil {
		tmpStr, _ := json.Marshal(f.Environment)
		f.EnvironmentStr = string(tmpStr)
		if len(f.Environment.Variables) == 0 || len(f.EnvironmentStr) == 0 {
			f.EnvironmentStr = `{"Variables":{}}`
		}
	}
	// 更新自定义运行时相关配置
	if f.CustomRuntimeConfig != nil {
		customRuntimeConfig, _ := json.Marshal(f.CustomRuntimeConfig)
		f.CustomRuntimeConfigStr = string(customRuntimeConfig)
	}

	f.FormatAsyncInvokeConfig()
	f.FormatCFSConfig()
}

// 请求返回处理function
func (f *Function) DealResFunction() {
	// 展示时函数名称中去掉workspaceID
	if f.WorkspaceID != "" {
		index := strings.Index(f.FunctionName, "_")
		f.FunctionName = f.FunctionName[index+1:]
	}

	json.Unmarshal([]byte(f.EnvironmentStr), &(f.Environment))
	// decode自定义运行时配置
	json.Unmarshal([]byte(f.CustomRuntimeConfigStr), &(f.CustomRuntimeConfig))
	f.FunctionArn = f.FunctionBrn
	f.LastModified = f.UpdatedAt

	// 仅会出现在读取旧数据时，LogType会出现为空
	// 若函数LogType为空，那么设置值为none
	if f.LogType == "" {
		f.LogType = "none"
		f.LogBosDir = ""
		f.BlsLogSet = ""
	}

	if f.VpcConfigStr != nil && len(*f.VpcConfigStr) > 0 {
		json.Unmarshal([]byte(*f.VpcConfigStr), &(f.VpcConfig))
	}

	if f.DeadLetterTopic != nil && *f.DeadLetterTopic == "" {
		f.DeadLetterTopic = nil
	}

	if f.LayersStr != nil {
		f.LayerList = f.GetLayerSampleList(*f.LayersStr)
	}

	if f.AsyncInvokeConfigStr != nil {
		json.Unmarshal([]byte(*f.AsyncInvokeConfigStr), &(f.AsyncInvokeConfig))
	}

	if f.CFSConfigStr != nil {
		json.Unmarshal([]byte(*f.CFSConfigStr), &(f.CFSConfig))
	}
}

func (f *Function) FunctionBrnInit() {
	f.FunctionBrn = brn.GenerateFuncBrnString(f.Region, f.Uid, f.FunctionName, f.Version)
}

// ==============runtime===========

func (m *RuntimeConfig) DealRuntimeConfiguration() {
	json.Unmarshal([]byte(m.ArgStr), &(m.Args))
	json.Unmarshal([]byte(m.KataArgStr), &(m.KataArgs))
}

// ==============alias==============

func (m *Alias) DealAlias() {
	m.AliasArn = m.AliasBrn
}

// ======event source ============

func (m *FuncEventSource) DealEventSource() {
	m.FunctionArn = m.FunctionBrn
	m.EventSourceArn = m.EventSourceBrn
	m.LastModified = m.UpdatedAt

	// 只有 bms、datahub_topic 类型才清空 StartingPosition，其他类型（如 rocketmq、专享kafka）保留
	if m.Type == api.TypeEventSourceBms || m.Type == api.TypeEventSourceDatahubTopic {
		m.StartingPosition = ""
	}
	m.StartingPositionTimestamp = nil
	m.Uid = ""
	if *m.Enabled {
		m.State = "Enabled"
	} else {
		m.State = "Disabled"
	}
	if m.DatahubConfigStr != nil && len(*m.DatahubConfigStr) > 0 {
		json.Unmarshal([]byte(*m.DatahubConfigStr), &m.DatahubConfig)
	}
	m.Enabled = nil
}

// ====== policy ============

func (m *Policy) DealPolicy() {
	if m.Source == "" && m.SourceArn != "" {
		m.Source = m.SourceArn
	}

	m.ConditionMap = make(map[string]interface{})
	if m.Source != "" {
		m.ConditionMap["ArnLike"] = map[string]string{
			"AWS:SourceArn": m.Source,
		}

		m.ConditionMap["SourceLike"] = map[string]string{
			"BCE:Source": m.Source,
		}
	}
	if m.SourceAccount != "" {
		m.ConditionMap["StringEquals"] = map[string]string{
			"AWS:SourceAccount": m.SourceAccount,
			"BCE:SourceAccount": m.SourceAccount,
		}
	}

	m.PrincipalMap = map[string]string{
		"Service": m.PrincipalService,
	}
}

func (f *Function) GetLayerSampleList(layerBrnStr string) []*api.LayerSample {
	sampleList := make([]*api.LayerSample, 0)
	for _, v := range strings.Split(layerBrnStr, ",") {
		layerBrn, err := brn.ParseLayerBrn(v)
		if err != nil {
			continue
		}
		layer := &api.Layer{
			Brn:     layerBrn.ShortBrn(),
			Version: layerBrn.LayerVersion,
		}

		err = FindOneLayer(layer)
		if err != nil {
			continue
		}
		sampleList = append(sampleList, &api.LayerSample{
			Brn:         layerBrn.String(),
			CodeSize:    layer.CodeSize,
			Description: layer.Description,
			Version:     layer.Version,
			LayerName:   layer.LayerName,
			Layer:       layer,
		})
	}
	return sampleList
}
