package dao

import (
	"fmt"

	"github.com/jinzhu/gorm"

	"icode.baidu.com/baidu/faas/kun/pkg/api"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/dbengine"
	apiErr "icode.baidu.com/baidu/faas/kun/pkg/apiserver/error"
	kunErr "icode.baidu.com/baidu/faas/kun/pkg/error"
)

func CreateNetworkConfig(tx *gorm.DB, a *NetworkConfig) error {
	if tx == nil {
		tx = dbengine.DBInstance()
	}

	if err := tx.Create(a).Error; err != nil {
		tx.Rollback()
		cause := fmt.Sprintf("create network config failed: %v", a)
		return apiErr.NewResourceConflictException(cause, err)
	}
	return nil
}

// UpdateNetworkConfig 优先更新 network_config 表，后面更新函数表时，由 function.go 内的实现去更新缓存
// 通过 etcd 对 proxyctrl 的通知放到上一层去做
func UpdateNetworkConfig(tx *gorm.DB, updateCond NetworkConfig, a *NetworkConfig) error {
	if tx == nil {
		tx = dbengine.DBInstance()
	}

	db := tx.Model(a).Where(updateCond).Update(a)
	if db.Error != nil {
		cause := fmt.Sprintf("update %s network config failed, err: %s", updateCond.Brn, db.Error.Error())
		e := kunErr.NewServiceException(cause, db.Error)
		return e
	}

	// fcMsg := api.NewFunctionCacheNotifyMsg().AddInfo(a.Brn, a.Version, api.FunctionCacheMethodUpdate)
	// dbengine.NotifyEtcdWhenChange(api.FunctionCacheEtcdPrefix, f.FunctionBrn, fcMsg)
	return nil
}

// DeleteNetworkConfig 删除某个配置
func DeleteNetworkConfig(tx *gorm.DB, a *NetworkConfig) error {
	if tx == nil {
		tx = dbengine.DBInstance()
	}

	if err := tx.Where(a).Delete(a).Error; err != nil {
		cause := fmt.Sprintf("delete %s network config failed, err: %s", a.Brn, err.Error())
		e := kunErr.NewServiceException(cause, err)
		return e
	}

	return nil
}

// FindNetworkConfig 查找某个配置
func FindOneNetworkConfig(cond *NetworkConfig) (*NetworkConfig, error) {
	db := dbengine.DBInstance().Where(cond).Find(cond)
	if db.RecordNotFound() {
		return nil, nil
	}

	if err := db.Error; err != nil {
		cause := fmt.Sprintf("find %s network config failed, err: %v", cond.Brn, db.Error)
		return nil, kunErr.NewServiceException(cause, err)
	}
	return cond, nil
}

// FindNetworkConfig 根据vpc配置查找函数列表
func FindNetworkConfigs(cond *NetworkConfig) ([]NetworkConfig, error) {
	res := make([]NetworkConfig, 0)
	db := dbengine.DBInstance().Where(cond).Find(&res)
	return res, db.Error
}

// ListVpcConfigs 列出 network config
func ListVpcConfigs() ([]*api.NetworkConfig, error) {
	db := dbengine.DBInstance()

	rawSql := `SELECT uid, brn, vpc_config_str, vpc_cidr, proxy_internal_ip, proxy_floating_ip, status, enable_slave FROM network_configs`
	rows, err := db.Raw(rawSql).Rows()
	if err != nil {
		return nil, kunErr.NewServiceException(fmt.Sprintf("list vpc configs failed, err: %v", db.Error), err)
	}
	defer rows.Close()

	ret := make([]*api.NetworkConfig, 0)
	for rows.Next() {
		value := api.NetworkConfig{}
		db.ScanRows(rows, &value)
		ret = append(ret, &value)
	}
	return ret, nil
}
