package dao_test

import (
	"errors"
	"fmt"
	"log"
	"testing"

	"github.com/DATA-DOG/go-sqlmock"
	"icode.baidu.com/baidu/faas/kun/pkg/api"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/dao"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/dbengine"
	kunErr "icode.baidu.com/baidu/faas/kun/pkg/error"

	_ "github.com/jinzhu/gorm/dialects/mysql"
	"github.com/stretchr/testify/assert"
)

func getRowsRuntimes(as []dao.RuntimeConfig) *sqlmock.Rows {
	var fieldNames = []string{"id", "name", "bin", "path", "argstr"}
	rows := sqlmock.NewRows(fieldNames)
	for _, a := range as {
		rows = rows.AddRow(a.Id, a.<PERSON>, a.Bin, a.<PERSON>, a.ArgStr)
	}
	return rows
}
func getTestRuntime(n int) (ret []dao.RuntimeConfig) {
	for i := 0; i < n; i++ {
		u := dao.RuntimeConfig{
			ArgStr: fmt.Sprintf("argstr_%d", i),
			Id:     uint(i),
			RuntimeConfiguration: api.RuntimeConfiguration{
				Name: fmt.Sprintf("name_%d", i),
				Bin:  fmt.Sprintf("bin_%d", i),
				Path: fmt.Sprintf("path_%d", i),
			},
		}
		ret = append(ret, u)
	}
	return
}

func TestRuntimeSelectOne(t *testing.T) {
	var m sqlmock.Sqlmock
	m, dbengine.Engine = NewDB()

	exp := getTestRuntime(1)
	req := "SELECT * FROM `runtime_configs` WHERE (`runtime_configs`.`name` = ?) ORDER BY `runtime_configs`.`id` ASC LIMIT 1"
	m.ExpectQuery(FixedFullRe(req)).
		WillReturnRows(getRowsRuntimes(exp))

	runtime := new(dao.RuntimeConfig)
	runtime.Name = "name_0"

	err := dao.FindRuntimeConf(runtime)
	assert.Equal(t, exp[0], *runtime)
	assert.Nil(t, err)

}

func TestRuntimeSelectOneFailed(t *testing.T) {
	var m sqlmock.Sqlmock
	m, dbengine.Engine = NewDB()

	expUsers := getTestRuntime(1)
	req := "SELECT * FROM `runtime_configs` WHERE (`runtime_configs`.`name` = ?) ORDER BY `runtime_configs`.`id` ASC LIMIT 1"
	m.ExpectQuery(FixedFullRe(req)).
		WillReturnRows(getRowsRuntimes(expUsers))

	runtime := new(dao.RuntimeConfig)
	runtime.Name = "name_0"

	dbengine.Engine.AddError(errors.New(""))
	err := dao.FindRuntimeConf(runtime)

	cause := fmt.Sprintf("[select runtime faied][RuntimeName:%s]", runtime.Name)
	e := kunErr.NewServiceException(cause, errors.New(""))
	assert.Equal(t, e, err)

}

func TestFindRuntimeConf(t *testing.T) {
	var m sqlmock.Sqlmock
	m, dbengine.Engine = NewDB()
	expUsers := getTestRuntime(1)
	log.Println(expUsers)

	m.ExpectQuery(FixedFullRe("SELECT * FROM `runtime_configs`")).
		WillReturnRows(getRowsRuntimes(expUsers))
	var runtimes []dao.RuntimeConfig
	_ = dbengine.Engine.Model(&dao.RuntimeConfig{}).Find(&runtimes).Error

	assert.Equal(t, expUsers, runtimes)
}
