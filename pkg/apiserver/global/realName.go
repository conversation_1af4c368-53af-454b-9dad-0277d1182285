package global

import (
	"bytes"
	"net/http"
	"net/url"

	"icode.baidu.com/baidu/faas/kun/pkg/api"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/dao"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/order"
	"icode.baidu.com/baidu/faas/kun/pkg/util/json"
)

const realNameQualificationStatus = "PASS"

func QueryRealName(accountId string, requestId string) (string, error) {
	//先查询数据库
	newLegalStatus := new(dao.LegalStatus)
	newLegalStatus.AccountId = accountId
	legalStatus, err := dao.FindLegal(newLegalStatus)
	if err != nil {
		return "", err
	}
	if len(*legalStatus) == 1 && (*legalStatus)[0].RealNameStatus == 1 {
		//数据库查询到实名认证信息
		return realNameQualificationStatus, nil
	}
	//请求用户实名认证接口，获取实名信息
	realNameQualification, err := queryRealNameQualification(accountId, requestId)
	if err != nil {
		return "", err
	}
	//用户实名认证通过
	if realNameQualification.Status == realNameQualificationStatus {
		newLegalStatus = new(dao.LegalStatus)
		newLegalStatus.AccountId = accountId
		newLegalStatus.RealNameStatus = 1
		//数据库中未存
		if len(*legalStatus) == 0 {
			if rkunErr := dao.CreateLegal(newLegalStatus); rkunErr != nil {
				return realNameQualificationStatus, err
			}
		} else {
			newLegalStatus.Id = (*legalStatus)[0].Id
			newLegalStatus.CreatedAt = (*legalStatus)[0].CreatedAt
			newLegalStatus.Status = (*legalStatus)[0].Status
			//数据库已存，更新实名认证状态
			if rkunErr := dao.UpdateLegal(&(*legalStatus)[0], newLegalStatus); rkunErr != nil {
				return realNameQualificationStatus, err
			}
		}
		return realNameQualificationStatus, nil
	}
	//实名认证未通过,返回实名状态
	return realNameQualification.Status, nil
}

//请求用户实名认证接口
func queryRealNameQualification(accountId string, requestId string) (*api.RealNameQualification, error) {
	body := new(bytes.Buffer)
	url, _ := getUrlPathWithParams(AC.Config.RealNameQualificationEndpoint+"/qualify/v2/qualification/realname", map[string]string{"accountId": accountId})
	req, err := http.NewRequest(http.MethodGet, url, body)
	if err != nil {
		return nil, err
	}
	rsp, err := order.HttpRequestForService(req, requestId, AC.Clients.Iam)
	if err != nil {
		return nil, err
	}
	realNameQualification := &api.RealNameQualification{}
	json.NewDecoder(rsp.Body).Decode(realNameQualification)
	return realNameQualification, nil
}

//url格式化
func getUrlPathWithParams(urlPath string, paramMap map[string]string) (urlPathNew string, err error) {
	params := url.Values{}
	var parseURL *url.URL
	parseURL, err = url.Parse(urlPath)
	if err != nil {
		return "", err
	}
	if paramMap != nil {
		for k, v := range paramMap {
			params.Set(k, v)
		}
	}
	parseURL.RawQuery = params.Encode()
	urlPathNew = parseURL.String()
	return urlPathNew, nil
}
