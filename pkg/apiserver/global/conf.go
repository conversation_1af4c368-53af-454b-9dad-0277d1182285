package global

import (
	"encoding/json"
	"fmt"
	"time"

	"github.com/asaskevich/govalidator"
	"github.com/jinzhu/gorm"

	"icode.baidu.com/baidu/faas/kun/cmd/apiserver/options"
	"icode.baidu.com/baidu/faas/kun/pkg/api"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/code"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/dao"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/dbengine"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/squashfs"
	"icode.baidu.com/baidu/faas/kun/pkg/bce/apigateway"
	"icode.baidu.com/baidu/faas/kun/pkg/bce/bms"
	"icode.baidu.com/baidu/faas/kun/pkg/bce/cdn"
	"icode.baidu.com/baidu/faas/kun/pkg/bce/cfs"
	"icode.baidu.com/baidu/faas/kun/pkg/bce/iam"
	"icode.baidu.com/baidu/faas/kun/pkg/bce/kunbns"
	"icode.baidu.com/baidu/faas/kun/pkg/bce/opscenter"
	"icode.baidu.com/baidu/faas/kun/pkg/bce/rocketmq"
	"icode.baidu.com/baidu/faas/kun/pkg/bce/sts_credential"
	"icode.baidu.com/baidu/faas/kun/pkg/bce/vpc"
	"icode.baidu.com/baidu/faas/kun/pkg/etcd"
	proxy "icode.baidu.com/baidu/faas/kun/pkg/proxyctrl/client"
	"icode.baidu.com/baidu/faas/kun/pkg/rpc/db"
	"icode.baidu.com/baidu/faas/kun/pkg/rpc/redis"
	"icode.baidu.com/baidu/faas/kun/pkg/util/logs"
	"icode.baidu.com/baidu/faas/kun/pkg/whitelist"
)

var AC *ApiserverCore

type ApiserverCore struct {
	Clients *Clients
	Config  *Config
	Cache   *Cache
	Metrics *logs.MetricGroup
}

type Clients struct {
	Etcd           etcd.EtcdInterface
	Mysql          *gorm.DB
	Redis          *redis.Client
	Iam            iam.ClientInterface
	BmsClient      bms.BMSControlInterface
	Ops            opscenter.OpsCenterInterface
	Cdn            cdn.Dialer
	Vpc            vpc.Interface
	ApiGateway     apigateway.ApigatewayController
	Code           code.CodeManagerInterface
	Squash         squashfs.SquashFsClient
	CFS            cfs.CFSInterface
	BNS            kunbns.Client
	Proxy          proxy.Interface
	RocketmqClient rocketmq.RocketmqControlInterface
	// Bls        bls.BlsManagerz
}

type Config struct {
	Region                        string // apiserver运行的地域、CFC服务部署的地域。用于brn生成\校验;IAM校验
	ServiceName                   string // 服务名称，用于IAM校验
	WhiteListEndpoint             string // 白名单地址
	EnableCheckWhiteList          bool   // 是否开启白名单
	BosEndpoint                   string // 用于建立bos触发器、上传用户代码、日志
	BillingCatalogEndpoint        string // 新开账单地址
	BillingEndpoint               string // 账单服务地址
	BillingPackageEndpoint        string // 用量包服务地址
	BillingChargeEndpoint         string // 账单计费资源地址
	RealNameQualificationEndpoint string // 用户实名认证地址
	EnableCheckBilling            bool   // 是否开启账单服务
	EnableCheckDuedgeTrigger      bool   // 是否检查已创建duedge触发器
	SourceWhiteList               *whitelist.WhiteList
	OpsCenterEndpoint             string
	BmsCertificateSN              string // 百度消息服务 超级证书
	BmsAccountID                  string // 百度消息服务超级证书所有者用户ID
	BmsEndPoint                   string // 百度消息服务 endpoint
	EnableSquashFs                bool   // 是否开启SquashFs
	MakeSquashFsCodeSize          int32
	MakeSquashFsFileNum           int
	SquashFsToolsPath             string
	MkSquashFsOptions             []string
	BlsEndpoint                   string   // bls地址，用于上传函数日志
	MaindataBNS                   string   // 主服务BNS name
	BillingOrderRenewEndpoint     string   // 自动续费服务地址
	VpcPermissionWhiteList        []string // 允许配置Vpc功能的Uid列表
	CodeConfiguration             *options.CodeConfiguration
	RocketmqEndpoint              string   // RocketMQ服务地址
}

type Cache struct {
	StsCache     sts_credential.Cache
	RuntimeCache map[string]dao.RuntimeConfig

	// InternalUsers
	InternalUserCache map[string]struct{}
}

// NewApiserverCore 新建一个ApiserverCore，包含了所有的客户端和配置信息。
// 参数：
//
//	runOptions *options.ApiServerOptions - 运行时选项，包括etcd、mysql、redis等客户端的配置信息以及服务名称等。
//	stopCh <-chan struct{} - 用于关闭同步goroutine的通道。
//
// 返回值：
//
//	error - 如果创建失败，则返回错误；否则返回nil。
func NewApiserverCore(runOptions *options.ApiServerOptions, stopCh <-chan struct{}) error {
	// 初始化etcd
	etcdClient := dbengine.NewEtcdClient(runOptions.EtcdOptions, true)
	// 初始化mysql连接池
	mysqlEngine, err := db.NewMysqlEngine(runOptions.DbConfiguration)
	if err != nil {
		return err
	}
	iamClient, err := iam.CreateIAMClient(runOptions.IAMConfiguration.IAMConfPath)
	if err != nil {
		return err
	}
	wl, err := whitelist.NewWhiteList(runOptions.SourceWhiteListOptions)
	if err != nil {
		return err
	}
	stsCache := sts_credential.NewLocalCredentialCache()
	opscli, err := opscenter.NewOpsCenter(iamClient, runOptions.OpsCenterEndpoint)
	if err != nil {
		return err
	}

	// blsCli := bls.NewBlsClient(runOptions.BlsEndpoint, api.StsRoleName, stsCache, iamClient)
	cdnCli := cdn.NewClient(runOptions.CdnEndpoint, api.StsRoleName, stsCache, iamClient)
	vpcCli := vpc.NewClient(runOptions.VpcEndpoint, api.StsRoleName, stsCache, iamClient)
	apiGatewayCli := apigateway.NewClient(runOptions.ApiGatewayEndpoint, api.StsRoleName, stsCache, iamClient)
	sqfsCli := squashfs.NewSquashFsClient(runOptions.SquashFsOptions)
	codeCli, err := code.NewCodeManager(runOptions.CodeConfiguration, sqfsCli)
	CFSCli := cfs.NewClient(runOptions.CFSEndpoint, api.StsRoleName, stsCache, iamClient)
	if err != nil {
		return err
	}
	mg := NewApiserverMetrics()

	// 单实例对并发最大限制
	api.DefaultPodConcurrentQuotaMax = runOptions.PodConcurrentQuotaMax

	AC = &ApiserverCore{
		Clients: &Clients{
			Etcd:           etcdClient,
			Mysql:          mysqlEngine,
			Redis:          redis.NewClient(runOptions.GenericRedisOptions),
			Iam:            iamClient,
			BmsClient:      bms.NewBMSControl(runOptions.BmsEndPoint, iamClient, stsCache, api.StsRoleName),
			Ops:            opscli,
			Cdn:            cdnCli,
			Vpc:            vpcCli,
			ApiGateway:     apiGatewayCli,
			Code:           codeCli,
			Squash:         *sqfsCli,
			CFS:            CFSCli,
			Proxy:          proxy.NewClient(runOptions.ProxyCtrlEndpoint),
			BNS:            kunbns.NewRRClient(),
			RocketmqClient: rocketmq.NewRocketmqControl(runOptions.RocketmqEndpoint, iamClient, stsCache, api.StsRoleName),
			// Bls:        blsCli,
		},
		Config: &Config{
			Region:                        runOptions.Region,
			ServiceName:                   "bce:cfc",
			WhiteListEndpoint:             fmt.Sprintf("http://%s", runOptions.WhiteListEndpoint),
			EnableCheckWhiteList:          runOptions.EnableCheckWhiteList,
			BosEndpoint:                   runOptions.CodeConfiguration.Endpoint,
			BillingCatalogEndpoint:        fmt.Sprintf("http://%s", runOptions.BillingCatalogEndpoint),
			BillingEndpoint:               fmt.Sprintf("http://%s", runOptions.BillingEndpoint),
			BillingPackageEndpoint:        fmt.Sprintf("http://%s", runOptions.BillingPackageEndpoint),
			BillingChargeEndpoint:         fmt.Sprintf("http://%s", runOptions.BillingChargeEndpoint),
			RealNameQualificationEndpoint: fmt.Sprintf("http://%s", runOptions.RealNameQualificationEndpoint),
			EnableCheckBilling:            runOptions.EnableCheckBilling,
			EnableCheckDuedgeTrigger:      runOptions.EnableCheckDuedgeTrigger,
			EnableSquashFs:                runOptions.SquashFsOptions.EnableSquashFs,
			SourceWhiteList:               wl,
			BmsCertificateSN:              runOptions.BmsCertificateSN,
			BmsAccountID:                  runOptions.BmsAccountID,
			MakeSquashFsCodeSize:          runOptions.SquashFsOptions.MakeSquashFsCodeSize,
			MakeSquashFsFileNum:           runOptions.SquashFsOptions.MakeSquashFsFileNum,
			SquashFsToolsPath:             runOptions.SquashFsOptions.SquashFsToolsPath,
			MkSquashFsOptions:             runOptions.SquashFsOptions.MkSquashFsOptions,
			BlsEndpoint:                   fmt.Sprintf("http://%s", runOptions.BlsEndpoint),
			MaindataBNS:                   runOptions.MaindataBNS,
			BillingOrderRenewEndpoint:     fmt.Sprintf("http://%s", runOptions.BillingOrderRenewEndpoint),
			VpcPermissionWhiteList:        runOptions.VpcPermissionWhiteList,
			CodeConfiguration:             runOptions.CodeConfiguration,
		},
		Cache: &Cache{
			StsCache: stsCache,
		},
		Metrics: mg,
	}
	if runOptions.IAMConfiguration.EnableInf {
		AC.Config.ServiceName = "luoshu:cfc"
	}
	runtimes, err := ReadRuntimeConfig()
	if err != nil {
		return err
	}
	AC.Cache.RuntimeCache = runtimes

	internalUsers, err := GetInternalUsers()
	if err != nil {
		return err
	}
	AC.Cache.InternalUserCache = internalUsers

	go AC.syncGoroutine(stopCh)
	return nil
}

func (core *ApiserverCore) syncGoroutine(stopCh <-chan struct{}) {
	for {
		select {
		case <-stopCh:
			return
		case <-time.After(api.RuntimeExpireTime):
			runtimes, err := ReadRuntimeConfig()
			if err != nil {
				logs.Errorf("sync runtime configs err=%s", err.Error())
				continue
			}
			AC.Cache.RuntimeCache = runtimes
		case <-time.After(api.InternalUserExpireTime):
			internelUsers, err := GetInternalUsers()
			if err != nil {
				logs.Errorf("sync internal users err=%s", err.Error())
				continue
			}
			AC.Cache.InternalUserCache = internelUsers
		}
	}
}

func ReadRuntimeConfig() (map[string]dao.RuntimeConfig, error) {
	runtimes, err := dao.ListRuntimeConf()
	if err != nil {
		return nil, err
	}

	runtimeCache := make(map[string]dao.RuntimeConfig)
	for _, r := range runtimes {
		runtimeCache[r.Name] = r
	}
	return runtimeCache, nil
}

// 注册自定义govalidator tag
func RegisterFaasRuntimeGovalidatorTag() {
	govalidator.TagMap["cfc_runtime"] = govalidator.Validator(func(str string) bool {
		_, ok := AC.Cache.RuntimeCache[str]
		return ok
	})
}

// 从ETCD中同步百度内部AccountID到内存
func GetInternalUsers() (map[string]struct{}, error) {
	res, err := AC.Clients.Etcd.Get(api.InternalUserPrefix)
	if err != nil {
		return nil, err
	}
	internalUserCache := make(map[string]struct{})
	if res.Count == 0 {
		return internalUserCache, nil
	}
	output := api.GetInternalUsersOutput{}
	if err := json.Unmarshal(res.Kvs[0].Value, &output); err != nil {
		return nil, err
	}

	for _, id := range output.AccountIds {
		internalUserCache[id] = struct{}{}
	}
	return internalUserCache, nil
}

// GetEndpointFromBNS 根据BNS名获取节点endpoint
func GetEndpointFromBNS(bnsName string, bnsClient kunbns.Client) (string, error) {
	endpoint, err := bnsClient.GetEndpoint(bnsName)
	if err != nil {
		return "", err
	}
	return "http://" + endpoint, nil
}
