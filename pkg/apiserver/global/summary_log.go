package global

import (
	"flag"
	"fmt"
	"go.uber.org/zap"
	"icode.baidu.com/baidu/faas/kun/pkg/util/bytefmt"
	"icode.baidu.com/baidu/faas/kun/pkg/util/logs"
	"io"
	"os"
	"path/filepath"
	"time"
)

var (
	program = filepath.Base(os.Args[0])
	enabled bool
	logdir  string
)

//InitMetrics xxx
func InitMetrics(enableMetrics bool) {
	if l := flag.Lookup("log_dir"); l != nil {
		logdir = l.Value.String()
	}
	enabled = enableMetrics && (len(logdir) > 0)
}

// NewFileWriter 创建一个 summary 打印至文件的 writer
func NewFileWriter() io.Writer {
	rotater := logs.NewRotate(logdir, program, "SUMMARY", logs.SetRotateSize(bytefmt.Gigabyte))
	writer := logs.NewDiodeWriter(rotater, 1000, 10*time.Millisecond, func(missed int) {
		fmt.Printf("Logger droped %d messages", missed)
	})
	return writer
}

// WriteSummary 根据 Config 配置，将 summary 写入 .INFO 或 .SUMMARY 文件
func WriteSummary(ctx *ApiserverContext, seperate bool) {
	fileds := []zap.Field{
		zap.String("msg", "accesslog"),
		zap.String("method", ctx.Method),
		zap.Object("context", ctx.Context),
	}

	if seperate {
		logs.NewSummaryLogger().With(fileds...).Info("summary", zap.Object("cost", ctx.Observer))
	} else {
		ctx.Logger.With(fileds...).Info("summary", zap.Object("cost", ctx.Observer))
	}
}
