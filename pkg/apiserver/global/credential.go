package global

import (
	"errors"

	"icode.baidu.com/baidu/faas/kun/pkg/api"
	"icode.baidu.com/baidu/faas/kun/pkg/bce/iam"
	"icode.baidu.com/baidu/faas/kun/pkg/bce/sts_credential"
	"icode.baidu.com/baidu/faas/kun/pkg/server"
)

// GetCredential Get the Credential according to userid
func GetCredential(c *server.Context) (*sts_credential.StsCredential, error) {
	role := api.StsRoleName

	r := c.Request().Attribute("User")
	if r == nil {
		return nil, errors.New("User not found")
	}
	user := r.(*iam.User)
	userid := user.Domain.ID

	var err error
	credential := AC.Cache.StsCache.Get(userid, role)
	if credential == nil {
		// 调用sts服务assumeRole
		credential, err = AC.Clients.Iam.AssumeRole(userid, role, c.Request().HeaderParameter(api.HeaderXRequestID), 3600)
		if err != nil {
			c.Logger().Warnf("assumeRole failed.", err.Error())
			return nil, err
		}

		AC.Cache.StsCache.Set(userid, role, credential)
	}
	return credential, nil
}
