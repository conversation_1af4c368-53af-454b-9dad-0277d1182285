package global

import (
	"strings"

	"icode.baidu.com/baidu/faas/kun/pkg/util/logs"
)

const (
	CheckParamsStage                   = "check_params"
	InitFunctionStage                  = "init_function"
	CheckFunctionStage                 = "check_function"
	UploadCodeStage                    = "upload_code"
	UploadLayerStage                   = "upload_layer"
	DownloadCodeStage                  = "download_code"
	QueryDatabaseStage                 = "query_database"
	PublishFunctionStage               = "publish_function"
	ListAliasesStage                   = "list_aliases"
	ListFunctionsStage                 = "list_functions"
	DeleteRelationsStage               = "delete_relations"
	DeleteTestEventsStage              = "delete_testevents"
	DeleteNetworkConfigStage           = "delete_networkconfig"
	DeleteFunctionStage                = "delete_function"
	ListPolicyStage                    = "list_policy"
	CountReservedConcurrencyStage      = "count_reserved_concurrency"
	GetRawCodeStage                    = "get_rawcode"
	ListEventSourceStage               = "list_event_source"
	FindOneFunctionStage               = "find_one_function"
	CreateFunctionStage                = "create_function"
	UpdateFunctionStage                = "update_function"
	FindFeatureActivationListStage     = "find_feature_activation_list"
	CreateFeatureActivationStage       = "create_feature_activation"
	UpdateFeatureActivationStatusStage = "update_feature_activation_status"
	CreateAliasStage                   = "create_alias"
	CheckAliasStage                    = "check_alias"
	UpdateAliasStage                   = "update_alias"
	FindOneAliasStage                  = "find_one_alias"
	DeleteAliasStage                   = "delete_alias"
	GetNextVersionStage                = "get_next_version"
	ListVersionsStage                  = "list_versions"
	CheckTesteventStage                = "check_testevent"
	CountUserTesteventStage            = "count_user_testevent"
	ValidateConcurrencyStage           = "validate_concurrency"
	CheckPolicyStage                   = "check_policy_input"
	CreatePolicyStage                  = "create_policy"
	FindOnePolicyStage                 = "find_one_policy"
	DeletePolicyStage                  = "delete_policy"
	OTECheckStage                      = "OTE_check"
	GetBytesFromBosStage               = "get_bytes_from_bos"
	UpdateDuerOSCodeStage              = "update_duerOS_code"
	GeneratePresignedUrlStage          = "generate_presigned_url"
	GetRelationsStage                  = "get_relations"
	CheckIfNeedCreatePolicyStage       = "check_if_need_create_policy"
	CheckIfNeedDeletePolicyStage       = "check_if_need_delete_policy"
	CreateRelationStage                = "create_relation"
	DeleteRelationStage                = "delete_relation"
	UpdateRelationStage                = "update_relation"
	FindBlueprintStage                 = "find_blueprint"
	AuthorizationSuper                 = "check_authorization_super"
	InitServiceStage                   = "init_service"
	CheckServiceStage                  = "check_service"
	CreateServiceStage                 = "create_service"
	UpdateServiceStage                 = "update_service"
	CheckAsyncConfigStage              = "check_async_config"
	ValidateCFSConfigStage             = "validate_cfs_config"
	CreateLegal                        = "create_legal"
	QueryLegal                         = "query_legal"
	UpdateLegal                        = "update_legal"
)

var (
	stagesMap = map[string][]float64{
		CheckParamsStage:                   latencyBucketsPermissions,
		InitFunctionStage:                  latencyBucketsPermissions,
		CheckFunctionStage:                 latencyBucketsPermissions,
		UploadCodeStage:                    latencyBucketsUploadCode,
		DownloadCodeStage:                  latencyBucketsUploadCode,
		UploadLayerStage:                   latencyBucketsUploadCode,
		QueryDatabaseStage:                 latencyBucketsPermissions,
		PublishFunctionStage:               latencyBucketsPermissions,
		ListAliasesStage:                   latencyBucketsPermissions,
		ListFunctionsStage:                 latencyBucketsPermissions,
		DeleteRelationsStage:               latencyBucketsPermissions,
		DeleteTestEventsStage:              latencyBucketsPermissions,
		DeleteFunctionStage:                latencyBucketsPermissions,
		ListPolicyStage:                    latencyBucketsPermissions,
		CountReservedConcurrencyStage:      latencyBucketsPermissions,
		GetRawCodeStage:                    latencyBucketsPermissions,
		ListEventSourceStage:               latencyBucketsPermissions,
		FindOneFunctionStage:               latencyBucketsPermissions,
		CreateFunctionStage:                latencyBucketsPermissions,
		UpdateFunctionStage:                latencyBucketsPermissions,
		FindFeatureActivationListStage:     latencyBucketsPermissions,
		CreateFeatureActivationStage:       latencyBucketsPermissions,
		UpdateFeatureActivationStatusStage: latencyBucketsPermissions,
		CreateAliasStage:                   latencyBucketsPermissions,
		CheckAliasStage:                    latencyBucketsPermissions,
		UpdateAliasStage:                   latencyBucketsPermissions,
		FindOneAliasStage:                  latencyBucketsPermissions,
		DeleteAliasStage:                   latencyBucketsPermissions,
		GetNextVersionStage:                latencyBucketsPermissions,
		ListVersionsStage:                  latencyBucketsPermissions,
		CheckTesteventStage:                latencyBucketsPermissions,
		CountUserTesteventStage:            latencyBucketsPermissions,
		ValidateConcurrencyStage:           latencyBucketsPermissions,
		CheckPolicyStage:                   latencyBucketsPermissions,
		CreatePolicyStage:                  latencyBucketsPermissions,
		FindOnePolicyStage:                 latencyBucketsPermissions,
		DeletePolicyStage:                  latencyBucketsPermissions,
		OTECheckStage:                      latencyBucketsPermissions,
		GetBytesFromBosStage:               latencyBucketsPermissions,
		UpdateDuerOSCodeStage:              latencyBucketsPermissions,
		GeneratePresignedUrlStage:          latencyBucketsPermissions,
		GetRelationsStage:                  latencyBucketsPermissions,
		CheckIfNeedCreatePolicyStage:       latencyBucketsPermissions,
		CreateRelationStage:                latencyBucketsPermissions,
		DeleteRelationStage:                latencyBucketsPermissions,
		DeleteNetworkConfigStage:           latencyBucketsPermissions,
		CheckIfNeedDeletePolicyStage:       latencyBucketsPermissions,
		UpdateRelationStage:                latencyBucketsPermissions,
		FindBlueprintStage:                 latencyBucketsPermissions,
		AuthorizationSuper:                 latencyBucketsPermissions,
		InitServiceStage:                   latencyBucketsPermissions,
		CheckServiceStage:                  latencyBucketsPermissions,
		CreateServiceStage:                 latencyBucketsPermissions,
		UpdateServiceStage:                 latencyBucketsPermissions,
		CheckAsyncConfigStage:              latencyBucketsPermissions,
	}
)

var (
	namespace = "apiserver"
	subsystem = "apiserver"
)

var (
	// Permission latency bucket
	// t<1ms is dueros; t=4ms is avg of fast iam; t=30ms is avg of slow iam;
	// 40ms<t<100ms is long tail; t>100ms is unacceptable
	latencyBucketsPermissions = []float64{1000, 3000, 4000, 5000, 20000, 35000, 50000, 100000}

	// upload latency bucket
	// t<1ms means hit local cache; t=9ms is avg of rpc call
	// t=20ms~50ms is long tail; t>100ms is unacceptable
	latencyBucketsUploadCode = []float64{1000, 8000, 9000, 10000, 20000, 50000, 100000}
)

func getMetricConfig(stage string, template string, buckets []float64) *logs.MetricConfig {
	return &logs.MetricConfig{
		MetricType:   logs.MetricTypeHistogram,
		Name:         stage,
		Labels:       []string{},
		HelpTemplate: template,
		Buckets:      buckets,
	}
}

func getTemplate(stage string) string {
	template := ""
	stageSlice := strings.Split(stage, "_")
	for _, v := range stageSlice {
		template = template + v + " "
	}
	template = template + "stage %s"
	return template
}

func NewApiserverMetrics() *logs.MetricGroup {
	mg := logs.NewMetricGroup(namespace, subsystem, false)
	for k, v := range stagesMap {
		template := getTemplate(k)
		mc := getMetricConfig(k, template, v)
		mg.AddMetric(mc)
	}
	return mg
}
