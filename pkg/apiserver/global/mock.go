package global

import (
	"fmt"
	"log"
	"net/http"
	"net/http/httptest"
	"path"
	"regexp"
	"runtime"
	"strings"
	"time"
	"unsafe"

	sqlmock "github.com/DATA-DOG/go-sqlmock"
	"github.com/emicklei/go-restful"
	"github.com/jinzhu/gorm"

	apiserverOptions "icode.baidu.com/baidu/faas/kun/cmd/apiserver/options"
	"icode.baidu.com/baidu/faas/kun/pkg/api"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/code"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/dao"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/dbengine"
	apiServer "icode.baidu.com/baidu/faas/kun/pkg/apiserver/server"
	"icode.baidu.com/baidu/faas/kun/pkg/bce/apigateway"
	"icode.baidu.com/baidu/faas/kun/pkg/bce/bms"
	"icode.baidu.com/baidu/faas/kun/pkg/bce/brn"
	"icode.baidu.com/baidu/faas/kun/pkg/bce/cdn"
	"icode.baidu.com/baidu/faas/kun/pkg/bce/cfs"
	"icode.baidu.com/baidu/faas/kun/pkg/bce/iam"
	"icode.baidu.com/baidu/faas/kun/pkg/bce/opscenter"
	"icode.baidu.com/baidu/faas/kun/pkg/bce/rocketmq"
	"icode.baidu.com/baidu/faas/kun/pkg/bce/sts_credential"
	"icode.baidu.com/baidu/faas/kun/pkg/bce/vpc"
	crontabMock "icode.baidu.com/baidu/faas/kun/pkg/crontab/dao"
	"icode.baidu.com/baidu/faas/kun/pkg/etcd/mock"
	redisMock "icode.baidu.com/baidu/faas/kun/pkg/rpc/redis/mock"
	"icode.baidu.com/baidu/faas/kun/pkg/server"
	"icode.baidu.com/baidu/faas/kun/pkg/util/convert"
	"icode.baidu.com/baidu/faas/kun/pkg/util/json"
	"icode.baidu.com/baidu/faas/kun/pkg/whitelist"
)

// MockAC MockAC 生成一个 ApiserverCore 类型的对象，包含了所有需要的客户端和配置信息。
// 该函数会修改全局变量 AC，并将其设置为新创建的 ApiserverCore 对象。
// 参数：无
// 返回值：无
func MockAC() {
	s := apiserverOptions.NewApiServerOptions()
	_, filename, _, _ := runtime.Caller(1) // 当前函数的上一个函数调用信息
	tmpPathArr := strings.Split(path.Join(path.Dir(filename)), "/kun/")

	s.IAMConfiguration.IAMConfPath = tmpPathArr[0] + "/kun/pkg/bce/iam/mock.yaml"
	s.CodeConfiguration.Endpoint = "http://bos-sandbox-bj.baidu-int.com"

	iamClient, _ := iam.CreateIAMClient(s.IAMConfiguration.IAMConfPath)
	opsClient, _ := opscenter.NewOpsCenter(iamClient, "http://user-config.internal-qasandbox.baidu-int.com:8690")
	opsClient = opscenter.NewNoopClient()
	swl, _ := whitelist.NewWhiteList(&whitelist.WhiteListOptions{
		SourceWhiteList: []string{"duedge:85ae1423f09f4660becb15d46402e9cd", "ote:c7ac82ae14ef42d1a4ffa3b2ececa17f"}})
	stsCache := sts_credential.NewLocalCredentialCache()
	topicListJson, _ := json.Marshal(bms.TopicList{})
	ts := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
		w.Write([]byte(topicListJson))
	}))
	bmsClient := bms.NewBMSControl(ts.URL, iamClient, stsCache, api.StsRoleName)
	// mock rocketmq client
	rocketmqClient := rocketmq.NewRocketmqControl(ts.URL, iamClient, stsCache, api.StsRoleName)

	mockRedis := redisMock.NewMockRedisClient()
	redisClient := mockRedis.Fake()

	AC = &ApiserverCore{
		Clients: &Clients{
			Etcd: new(mock.EtcdMockClient),
			// Mysql: dbengine.DBInstance(),
			Iam:            iamClient,
			Cdn:            cdn.MockClient(),
			ApiGateway:     apigateway.NewMockClient(),
			Ops:            opsClient,
			BmsClient:      bmsClient,
			RocketmqClient: rocketmqClient,
			Code:           code.MockCode(),
			Redis:          redisClient,
			Vpc:            vpc.MockClient(),
			CFS:            cfs.MockClient(),
		},
		Config: &Config{
			Region:               "bj",
			ServiceName:          "bce:cfc",
			WhiteListEndpoint:    "http://user-config.internal-qasandbox.baidu-int.com:8690",
			EnableCheckWhiteList: false,
			BosEndpoint:          s.CodeConfiguration.Endpoint,
			SourceWhiteList:      swl,
			MakeSquashFsCodeSize: 5242880,
			MakeSquashFsFileNum:  0,
			BmsCertificateSN:     "73d1af3f69ec46d98d9f494d7b8b7005",
			BmsAccountID:         "c7ac82ae14ef42d1a4ffa3b2ececa17f",
			BmsEndPoint:          "10.74.230.49:8222",
			BlsEndpoint:          s.BlsEndpoint,
		},
		Cache: &Cache{
			StsCache: sts_credential.NewLocalCredentialCache(),
			RuntimeCache: map[string]dao.RuntimeConfig{
				"python2": {
					Editable: true,
				},
				"python3": {
					Editable: true,
				},
				"nodejs8.5": {
					Editable: true,
				},
				"nodejs6.11": {
					Editable: true,
				},
				"java8": {
					Editable: false,
				},
				"golang": {
					Editable: false,
				},
			},
			InternalUserCache: map[string]struct{}{
				"userID1": {},
			},
		},
	}
}

// MockACWithRocketmq func MockACWithRocketmq(rocketUrl string)
// 参数：
//
//	rocketUrl (string) - RocketMQ服务器地址，必填项
//
// 返回值：
//
//	nil - 无返回值
func MockACWithRocketmq(rocketUrl string) {
	s := apiserverOptions.NewApiServerOptions()
	_, filename, _, _ := runtime.Caller(1) // 当前函数的上一个函数调用信息
	tmpPathArr := strings.Split(path.Join(path.Dir(filename)), "/kun/")

	s.IAMConfiguration.IAMConfPath = tmpPathArr[0] + "/kun/pkg/bce/iam/mock.yaml"
	s.CodeConfiguration.Endpoint = "http://bos-sandbox-bj.baidu-int.com"

	iamClient, _ := iam.CreateIAMClient(s.IAMConfiguration.IAMConfPath)
	opsClient, _ := opscenter.NewOpsCenter(iamClient, "http://user-config.internal-qasandbox.baidu-int.com:8690")
	opsClient = opscenter.NewNoopClient()
	swl, _ := whitelist.NewWhiteList(&whitelist.WhiteListOptions{
		SourceWhiteList: []string{"duedge:85ae1423f09f4660becb15d46402e9cd", "ote:c7ac82ae14ef42d1a4ffa3b2ececa17f"}})
	stsCache := sts_credential.NewLocalCredentialCache()
	topicListJson, _ := json.Marshal(bms.TopicList{})
	ts := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
		w.Write([]byte(topicListJson))
	}))
	bmsClient := bms.NewBMSControl(ts.URL, iamClient, stsCache, api.StsRoleName)
	// mock rocketmq client
	rocketmqClient := rocketmq.NewRocketmqControl(rocketUrl, iamClient, stsCache, api.StsRoleName)

	mockRedis := redisMock.NewMockRedisClient()
	redisClient := mockRedis.Fake()

	AC = &ApiserverCore{
		Clients: &Clients{
			Etcd: new(mock.EtcdMockClient),
			// Mysql: dbengine.DBInstance(),
			Iam:            iamClient,
			Cdn:            cdn.MockClient(),
			ApiGateway:     apigateway.NewMockClient(),
			Ops:            opsClient,
			BmsClient:      bmsClient,
			RocketmqClient: rocketmqClient,
			Code:           code.MockCode(),
			Redis:          redisClient,
			Vpc:            vpc.MockClient(),
			CFS:            cfs.MockClient(),
		},
		Config: &Config{
			Region:               "bj",
			ServiceName:          "bce:cfc",
			WhiteListEndpoint:    "http://user-config.internal-qasandbox.baidu-int.com:8690",
			EnableCheckWhiteList: false,
			BosEndpoint:          s.CodeConfiguration.Endpoint,
			SourceWhiteList:      swl,
			MakeSquashFsCodeSize: 5242880,
			MakeSquashFsFileNum:  0,
			BmsCertificateSN:     "73d1af3f69ec46d98d9f494d7b8b7005",
			BmsAccountID:         "c7ac82ae14ef42d1a4ffa3b2ececa17f",
			BmsEndPoint:          "10.74.230.49:8222",
			BlsEndpoint:          s.BlsEndpoint,
		},
		Cache: &Cache{
			StsCache: sts_credential.NewLocalCredentialCache(),
			RuntimeCache: map[string]dao.RuntimeConfig{
				"python2": {
					Editable: true,
				},
				"python3": {
					Editable: true,
				},
				"nodejs8.5": {
					Editable: true,
				},
				"nodejs6.11": {
					Editable: true,
				},
				"java8": {
					Editable: false,
				},
				"golang": {
					Editable: false,
				},
			},
			InternalUserCache: map[string]struct{}{
				"userID1": {},
			},
		},
	}
}

func MockDB() (sqlmock.Sqlmock, *gorm.DB) {
	db, mock, err := sqlmock.New()
	if err != nil {
		log.Fatalf("can't create sqlmock: %s", err)
	}

	gormDB, gerr := gorm.Open("mysql", db)
	if gerr != nil {
		log.Fatalf("can't open gorm connection: %s", err)
	}
	gormDB.LogMode(true)
	gormDB = gormDB.Set("gorm:update_column", true)
	dbengine.Engine = gormDB
	return mock, gormDB
}

func BuildNewKunCtx(method, uri, body, uid string, pathMapV map[string]string) *server.Context {
	request := httptest.NewRequest(method, uri, strings.NewReader(body))
	restReq := restful.NewRequest(request)
	restRsp := restful.NewResponse(httptest.NewRecorder())
	restReq.Request.Header.Set("Content-Type", "application/json")
	restReq.Request.Header.Set("X-CFC-Workspace-Id", "test")
	restRsp.SetRequestAccepts(restful.MIME_JSON)
	var pathMap *map[string]string = (*map[string]string)(unsafe.Pointer(uintptr(unsafe.Pointer(restReq)) + uintptr(unsafe.Sizeof(restReq.Request))))
	*pathMap = pathMapV

	if uid != "" {
		restReq.SetAttribute("User", &iam.User{
			Domain: &iam.Domain{
				ID: uid,
			},
		})
	}
	return server.BuildContext(restReq, restRsp, "")
}

func BuildNewApiServerCtx(method, uri, body, uid string, pathMapV map[string]string) *apiServer.ApiServerContext {
	request := httptest.NewRequest(method, uri, strings.NewReader(body))
	restReq := restful.NewRequest(request)
	restRsp := restful.NewResponse(httptest.NewRecorder())
	restReq.Request.Header.Set("Content-Type", "application/json")
	restRsp.SetRequestAccepts(restful.MIME_JSON)
	var pathMap *map[string]string = (*map[string]string)(unsafe.Pointer(uintptr(unsafe.Pointer(restReq)) + uintptr(unsafe.Sizeof(restReq.Request))))
	*pathMap = pathMapV

	if uid != "" {
		restReq.SetAttribute("User", &iam.User{
			Domain: &iam.Domain{
				ID: uid,
			},
		})
	}
	return apiServer.BuildContext(restReq, restRsp)
}

func BuildNewKunCtxWithHeaders(method, uri, body, uid string, pathMapV map[string]string, headers map[string]string) *server.Context {
	request := httptest.NewRequest(method, uri, strings.NewReader(body))
	restReq := restful.NewRequest(request)
	restRsp := restful.NewResponse(httptest.NewRecorder())
	restReq.Request.Header.Set("Content-Type", "application/json")
	for k, v := range headers {
		restReq.Request.Header.Set(k, v)
	}
	restRsp.SetRequestAccepts(restful.MIME_JSON)
	var pathMap *map[string]string = (*map[string]string)(unsafe.Pointer(uintptr(unsafe.Pointer(restReq)) + uintptr(unsafe.Sizeof(restReq.Request))))
	*pathMap = pathMapV

	if uid != "" {
		restReq.SetAttribute("User", &iam.User{
			Domain: &iam.Domain{
				ID: uid,
			},
		})
	}
	return server.BuildContext(restReq, restRsp, "")
}

func BuildNewKunCtxWithHeaders1(method, uri, body, uid string, pathMapV map[string]string) *server.Context {
	request := httptest.NewRequest(method, uri, strings.NewReader(body))
	restReq := restful.NewRequest(request)
	restRsp := restful.NewResponse(httptest.NewRecorder())
	restReq.Request.Header.Set("Content-Type", "multipart/form-data; charset=utf-8; boundary=__X_PAW_BOUNDARY__")
	restRsp.SetRequestAccepts(restful.MIME_JSON)
	var pathMap *map[string]string = (*map[string]string)(unsafe.Pointer(uintptr(unsafe.Pointer(restReq)) + uintptr(unsafe.Sizeof(restReq.Request))))
	*pathMap = pathMapV

	if uid != "" {
		restReq.SetAttribute("User", &iam.User{
			Domain: &iam.Domain{
				ID: uid,
			},
		})
	}
	return server.BuildContext(restReq, restRsp, "")
}

func GetTestFunctionReserveds(n int) (res []dao.FunctionReserved) {
	for i := 0; i < n; i++ {
		r := dao.FunctionReserved{
			Id:              uint(i),
			Uuid:            "uiduid",
			Uid:             "123456",
			CommitID:        convert.String(fmt.Sprintf("commit_id_%d", i)),
			FunctionVersion: fmt.Sprintf("%d", i),
			FunctionName:    "fname",
			FunctionBrn:     fmt.Sprintf("brn:bce:cfc:bj:640c8817bd1de2928d47256dd0620ce5:function:test_%d:1", i),
			MemorySize:      128,
			ReservedCount:   i,
			RealCount:       i,
			Status:          2,
			ReservedTs:      time.Now().Unix(),
			UpdatedAt:       time.Now(),
			CreatedAt:       time.Now(),
		}
		res = append(res, r)
	}
	return
}

func GetRowsFunctionReserveds(reserveds []dao.FunctionReserved) *sqlmock.Rows {
	var fieldNames = []string{"id", "uid", "uuid", "commit_id", "function_version", "function_name", "function_brn", "memory_size", "reserved_count", "real_count", "status", "reserved_ts", "updated_at", "created_at"}
	rows := sqlmock.NewRows(fieldNames)
	if reserveds != nil {
		for _, r := range reserveds {
			rows = rows.AddRow(r.Id, r.Uid, r.Uuid, r.CommitID, r.FunctionVersion, r.FunctionName, r.FunctionBrn, r.MemorySize, r.ReservedCount, r.RealCount, r.Status, r.ReservedTs, r.UpdatedAt, r.CreatedAt)
		}
	}
	return rows
}

func GetTestAlias(n int) (ret []dao.Alias) {
	for i := 0; i < n; i++ {
		u := dao.Alias{
			Id:              uint(i),
			AliasBrn:        fmt.Sprintf("brn:bce:cfc:bj:640c8817bd1de2928d47256dd0620ce5:function:test_%d:alias_name", i),
			FunctionName:    "",
			FunctionVersion: "5",
			Name:            "alias",
			Description:     convert.String(fmt.Sprintf("alias_desc_%d", i)),
			Uid:             "123456",
			UpdatedAt:       time.Now(),
			CreatedAt:       time.Now(),
		}
		ret = append(ret, u)
	}
	return
}

func GetRowsAliases(aliases []dao.Alias) *sqlmock.Rows {
	var fieldNames = []string{"id", "alias_brn", "function_name", "function_version", "name", "description", "uid", "updated_at", "created_at"}
	rows := sqlmock.NewRows(fieldNames)
	if aliases != nil {
		for _, a := range aliases {
			rows = rows.AddRow(a.Id, a.AliasBrn, a.FunctionName, a.FunctionVersion, a.Name, a.Description, a.Uid, a.UpdatedAt, a.CreatedAt)
		}
	}
	return rows
}

func GetTestFunc(n int) (ret []dao.Function) {
	for i := 0; i < n; i++ {
		deadletterTopic := "c7ac82ae14ef42d1a4ffa3b2ececa17f__dead_letter"
		u := dao.Function{
			Id:             uint(i),
			Uid:            fmt.Sprintf("uid_%d", i),
			Description:    convert.String(fmt.Sprintf("description_%d", i)),
			FunctionBrn:    fmt.Sprintf("function_brn_%d", i),
			Region:         fmt.Sprintf("region_%d", i),
			Timeout:        convert.Int(i),
			EnvironmentStr: fmt.Sprintf(`{"Variables":{"k%d":"v%d"}}`, i, i),
			VersionDesc:    convert.String(fmt.Sprintf("version_desc_%d", i)),
			UpdatedAt:      time.Now(),
			CreatedAt:      time.Now(),
			DeletedAt:      time.Now(),

			FunctionConfig: api.FunctionConfig{
				CodeSha256:                   "cNBGAjstP9SgRKYdf8I3ang/4dPWdcrTUYzpSD4bD8s=",
				CodeSize:                     int32(i),
				FunctionName:                 "fname",
				Handler:                      "index.handler",
				Version:                      fmt.Sprintf("%d", i),
				Runtime:                      "nodejs6.11",
				MemorySize:                   convert.Int(128),
				CommitID:                     convert.String(fmt.Sprintf("commit_id_%d", i)),
				LogType:                      "bos",
				CodeID:                       "5afb2719-b470-4bce-a9d8-11379f8c57ce",
				ReservedConcurrentExecutions: convert.Int(i),
				DeadLetterTopic:              &deadletterTopic,
				WorkspaceID:                  "testtest",
			},
		}
		ret = append(ret, u)
	}
	return
}

func GetTestBanFunc(n int) (ret []dao.Function) {
	for i := 0; i < n; i++ {
		deadletterTopic := "c7ac82ae14ef42d1a4ffa3b2ececa17f__dead_letter"
		u := dao.Function{
			Id:             uint(i),
			Uid:            fmt.Sprintf("uid_%d", i),
			Description:    convert.String(fmt.Sprintf("description_%d", i)),
			FunctionBrn:    fmt.Sprintf("function_brn_%d", i),
			Region:         fmt.Sprintf("region_%d", i),
			Timeout:        convert.Int(3),
			EnvironmentStr: fmt.Sprintf(`{"Variables":{"k%d":"v%d"}}`, i, i),
			VersionDesc:    convert.String(fmt.Sprintf("version_desc_%d", i)),
			UpdatedAt:      time.Now(),
			CreatedAt:      time.Now(),
			DeletedAt:      time.Now(),

			FunctionConfig: api.FunctionConfig{
				CodeSha256:                   "cNBGAjstP9SgRKYdf8I3ang/4dPWdcrTUYzpSD4bD8s=",
				CodeSize:                     int32(i),
				FunctionName:                 "fname",
				Handler:                      "index.handler",
				Version:                      fmt.Sprintf("%d", i),
				Runtime:                      "nodejs6.11",
				MemorySize:                   convert.Int(512),
				CommitID:                     convert.String(fmt.Sprintf("commit_id_%d", i)),
				LogType:                      "bos",
				CodeID:                       "5afb2719-b470-4bce-a9d8-11379f8c57ce",
				ReservedConcurrentExecutions: convert.Int(i),
				DeadLetterTopic:              &deadletterTopic,
				WorkspaceID:                  "testtest",
				Ban:                          convert.Bool(false),
				LimitMemorySize:              convert.Int(0),
				LimitTimeout:                 convert.Int(0),
				LimitMaxRetryAttempts:        convert.Int(0),
			},
		}
		ret = append(ret, u)
	}
	return
}

func GetTestNetworkConfig(n int) (ret []dao.NetworkConfig) {
	vpcConfig := &api.VpcConfig{
		VpcID:            "vpc-123",
		SubnetIDs:        []string{"sbn-123"},
		SecurityGroupIDs: []string{"g-123"},
	}

	vpcBytes, _ := json.Marshal(vpcConfig)
	vpcStr := string(vpcBytes)

	for i := 0; i < n; i++ {
		u := dao.NetworkConfig{
			Id:        uint(i),
			UpdatedAt: time.Now(),
			CreatedAt: time.Now(),
			NetworkConfig: api.NetworkConfig{
				Uid:             fmt.Sprintf("uid_%d", i),
				Brn:             fmt.Sprintf("function_brn_%d", i),
				FunctionName:    "fname",
				VpcConfig:       vpcConfig,
				VpcConfigStr:    &vpcStr,
				ProxyInternalIP: convert.String("***********"),
				ProxyFloatingIP: convert.String("***********"),
				EnableSlave:     false,
			},
		}
		ret = append(ret, u)
	}
	return
}

func GetTestLegalStatus(n int) (ret []dao.LegalStatus) {
	for i := 0; i < n; i++ {
		u := dao.LegalStatus{
			Id:        uint(i),
			AccountId: fmt.Sprintf("uid_%d", i),
			Status:    1,
			UpdatedAt: time.Time{},
			CreatedAt: time.Time{},
		}
		ret = append(ret, u)
	}
	return
}

func GetTestRealName(n int) (ret []dao.LegalStatus) {
	for i := 0; i < n; i++ {
		u := dao.LegalStatus{
			Id:             uint(i),
			AccountId:      fmt.Sprintf("uid_%d", i),
			Status:         1,
			RealNameStatus: 1,
			UpdatedAt:      time.Time{},
			CreatedAt:      time.Time{},
		}
		ret = append(ret, u)
	}
	return
}

func GetRowsFunctions(as []dao.Function) *sqlmock.Rows {
	var fieldNames = []string{"id", "commit_id", "function_name", "uid", "code_sha256", "code_size", "description", "region", "memory_size", "function_brn", "handler", "runtime", "timeout", "version", "version_desc", "environment", "concurrency", "updated_at", "created_at", "deleted_at", "log_type", "code_id", "dead_letter_topic", "workspace_id"}
	rows := sqlmock.NewRows(fieldNames)
	for _, a := range as {
		rows = rows.AddRow(a.Id, a.CommitID, a.FunctionName, a.Uid, a.CodeSha256, a.CodeSize, a.Description, a.Region, a.MemorySize, a.FunctionBrn, a.Handler, a.Runtime, a.Timeout, a.Version, a.VersionDesc, a.EnvironmentStr, a.ReservedConcurrentExecutions, a.UpdatedAt, a.CreatedAt, a.DeletedAt, a.LogType, a.CodeID, a.DeadLetterTopic, a.WorkspaceID)
	}
	return rows
}

func GetRowsNetworkConfigs(as []dao.NetworkConfig) *sqlmock.Rows {
	var fieldNames = []string{"id", "created_at", "updated_at", "uid", "function_name", "brn", "vpc_config_str", "status", "vpc_cidr", "proxy_internal_ip", "proxy_floating_ip", "enable_slave"}
	rows := sqlmock.NewRows(fieldNames)
	for _, a := range as {
		rows = rows.AddRow(a.Id, a.CreatedAt, a.UpdatedAt, a.Uid, a.FunctionName, a.Brn, a.VpcConfigStr, a.Status, a.VpcCidr, a.ProxyInternalIP, a.ProxyFloatingIP, a.EnableSlave)
	}
	return rows
}

func GetRowsLegal(as []dao.LegalStatus) *sqlmock.Rows {
	var fieldNames = []string{"id", "account_id", "status", "update_at", "create_at"}
	rows := sqlmock.NewRows(fieldNames)
	for _, a := range as {
		rows = rows.AddRow(a.Id, a.AccountId, a.Status, a.UpdatedAt, a.CreatedAt)
	}
	return rows
}

func GetRowsRealName(as []dao.LegalStatus) *sqlmock.Rows {
	var fieldNames = []string{"id", "account_id", "status", "update_at", "create_at", "real_name_status"}
	rows := sqlmock.NewRows(fieldNames)
	for _, a := range as {
		rows = rows.AddRow(a.Id, a.AccountId, a.Status, a.UpdatedAt, a.CreatedAt, a.RealNameStatus)
	}
	return rows
}

func NewLegal(as []dao.LegalStatus) *sqlmock.Rows {
	var fieldNames = []string{"id", "account_id", "status", "update_at", "create_at"}
	rows := sqlmock.NewRows(fieldNames)
	for _, a := range as {
		rows = rows.AddRow(a.Id, a.AccountId, a.Status, a.UpdatedAt, a.CreatedAt)
	}
	return rows
}

func GetRowsPolicy(pol []dao.Policy) *sqlmock.Rows {
	var fieldNames = []string{"id", "statement_id", "effect", "action", "resource", "principal_service", "source", "source_account", "created_at"}
	rows := sqlmock.NewRows(fieldNames)
	for _, po := range pol {
		rows = rows.AddRow(po.ID, po.StatementId, po.Effect, po.Action, po.Resource, po.PrincipalService, po.Source, po.SourceAccount, po.CreatedAt)
	}
	return rows
}

func GetRowsCronRule(pol []crontabMock.CrontabRule) *sqlmock.Rows {
	var fieldNames = []string{"id", "uuid", "uid", "enabled", "name", "input", "function_name", "brn", "schedule_expression", "original_time", "updated_at", "created_at", "deleted_at"}
	rows := sqlmock.NewRows(fieldNames)
	for _, po := range pol {
		rows = rows.AddRow(po.ID, po.UUID, po.Uid, po.Enabled, po.Name, po.Input, po.FunctionName, po.Brn, po.ScheduleExpression, po.OriginalTime, po.UpdatedAt, po.CreatedAt, po.DeletedAt)
	}
	return rows
}

func GetRowsUserTestEvents(events []dao.UserTestEvent) *sqlmock.Rows {
	var fieldNames = []string{"id", "uuid", "uid", "function_name", "title", "event", "created_at", "updated_at"}
	rows := sqlmock.NewRows(fieldNames)
	for _, event := range events {
		rows = rows.AddRow(event.ID, event.Uuid, event.Uid, event.FunctionName, event.Title, event.Event, event.CreatedAt, event.UpdatedAt)
	}
	return rows
}

func GetUserTestEvents(n int) (ret []dao.UserTestEvent) {
	for i := 0; i < n; i++ {
		e := dao.UserTestEvent{
			ID:           int64(i),
			Uuid:         fmt.Sprintf("uuid_%d", i),
			Uid:          fmt.Sprintf("uid_%d", i),
			FunctionName: fmt.Sprintf("function_name_%d", i),
			Title:        fmt.Sprintf("title_%d", i),
			Event:        fmt.Sprintf("event_%d", i),
			CreatedAt:    time.Now(),
			UpdatedAt:    time.Now(),
		}
		ret = append(ret, e)
	}
	return
}

func GetRowsStandardTestEvents(events []dao.StandardTestEvent) *sqlmock.Rows {
	var fieldNames = []string{"id", "title", "type", "event", "status", "created_at", "updated_at"}
	rows := sqlmock.NewRows(fieldNames)
	for _, event := range events {
		rows = rows.AddRow(event.ID, event.Title, event.Type, event.Event, event.Status, event.CreatedAt, event.UpdatedAt)
	}
	return rows
}

func GetStandardTestEvents(n int) (ret []dao.StandardTestEvent) {
	for i := 0; i < n; i++ {
		e := dao.StandardTestEvent{
			ID:        i,
			Title:     fmt.Sprintf("title_%d", i),
			Type:      fmt.Sprintf("type_%d", i),
			Event:     fmt.Sprintf("event_%d", i),
			Status:    1,
			CreatedAt: time.Now(),
			UpdatedAt: time.Now(),
		}
		ret = append(ret, e)
	}
	return
}

func GetRowsFeatureActivation(events []dao.FeatureActivation) *sqlmock.Rows {
	var fieldNames = []string{"id", "uid", "type", "status", "updated_at", "created_at"}
	rows := sqlmock.NewRows(fieldNames)
	for _, event := range events {
		rows = rows.AddRow(event.ID, event.Uid, event.Type, event.Status, event.UpdatedAt, event.CreatedAt)
	}
	return rows
}

func GetFeatureActivation(n int) (ret []dao.FeatureActivation) {
	for i := 0; i < n; i++ {
		f := dao.FeatureActivation{
			ID:        i,
			Uid:       fmt.Sprintf("uid-%d", i),
			Type:      api.FeatureTypeCfcEdge,
			Status:    api.UserFeatureActivated,
			CreatedAt: time.Now(),
			UpdatedAt: time.Now(),
		}
		ret = append(ret, f)
	}
	return
}

func GetRowsBlueprints(bps []dao.Blueprint) *sqlmock.Rows {
	var fieldNames = []string{"id", "uuid", "name", "description", "keywords", "bos_obj_key", "runtime", "handler", "version", "status", "updated_at", "created_at", "environment", "extra", "links", "deployments", "authors", "layers"}
	rows := sqlmock.NewRows(fieldNames)
	for _, bp := range bps {
		rows = rows.AddRow(bp.Id, bp.Uuid, bp.Name, bp.Description, bp.KeywordsStr, bp.BosObjKey, bp.Runtime, bp.Handler, bp.Version, bp.Status, bp.UpdatedAt, bp.CreatedAt, bp.Environment, bp.Extra, bp.LinksStr, bp.Deployments, bp.AuthorsStr, bp.LayersStr)
	}
	return rows
}

func GetTestBlueprint(n int) (ret []dao.Blueprint) {
	for i := 0; i < n; i++ {
		tmp := dao.Blueprint{
			Id:             uint(i),
			Uuid:           fmt.Sprintf("%d0457f0b-20d8-4f3d-8555-c233c7ebd495", i),
			Name:           fmt.Sprintf("bpname_%d", i),
			Description:    convert.String(fmt.Sprintf("description_%d", i)),
			KeywordsStr:    fmt.Sprintf("keywords_%d", i),
			BosObjKey:      fmt.Sprintf("BlueprintBosKey_%d", i),
			Runtime:        "nodejs6.11",
			Handler:        fmt.Sprintf("index.handler%d", i),
			Version:        "1.0.0",
			Status:         "online",
			UpdatedAt:      time.Now(),
			CreatedAt:      time.Now(),
			EnvironmentStr: fmt.Sprintf(`{"Variables":{"k%d":"v%d"}}`, i, i),
			LinksStr:       fmt.Sprintf(`{"k%d":"v%d"}`, i, i),
			Deployments:    convert.Int(i),
			AuthorsStr:     fmt.Sprintf("authors_%d", i),
		}
		extraMap := make(map[string]interface{})
		extraMap["MemorySize"] = int64(128)
		extraMap["Timeout"] = int64(1)
		extraMap["TriggerType"] = "http"
		extra, _ := json.Marshal(extraMap)
		tmp.Extra = string(extra)
		ret = append(ret, tmp)
	}
	return
}

func GetTestPolicy(n int) (ret []dao.Policy) {
	for i := 0; i < n; i++ {
		tmp := dao.Policy{
			ID:               uint(i),
			StatementId:      fmt.Sprintf("%d-4f3d-8555-c233c7ebd495", i),
			Effect:           "Allow",
			Action:           "cfc:InvokeFunction",
			Resource:         "brn:bce:cfc:bj:640c8817bd1de2928d47256dd0620ce5:function:hello:$LATEST",
			PrincipalService: "dueros.baidu.com",
			Source:           "",
			SourceAccount:    "",
			CreatedAt:        time.Now(),
		}
		ret = append(ret, tmp)
	}
	return
}

func GetBlsTestPolicy(n int) (ret []dao.Policy) {
	for i := 0; i < n; i++ {
		tmp := dao.Policy{
			ID:               uint(i),
			StatementId:      fmt.Sprintf("%d-4f3d-8555-c233c7ebd495", i),
			Effect:           "Allow",
			Action:           "cfc:InvokeFunction",
			Resource:         "brn:bce:cfc:bj:640c8817bd1de2928d47256dd0620ce5:function:test-bls:$LATEST",
			PrincipalService: "bls-log.baidubce.com",
			Source:           "bls/testlogrecord/testlogStreamName/blstest",
			SourceAccount:    "",
			CreatedAt:        time.Now(),
		}
		ret = append(ret, tmp)
	}
	return
}

func GetCronRuleTestPolicy(n int) (ret []crontabMock.CrontabRule) {
	input := "{\"ak\":\"\",\"host\":\"bls-log.bj.baidubce.com\",\"logStreamName\":\"42f6fbc2cd374bfcb80d9967370fd8ff_testflow111\",\"logrecord\":\"xflow_history\",\"sk\":\"\",\"taskInterval\":\"60\",\"token\":\"\"}"
	for i := 0; i < n; i++ {
		tmp := crontabMock.CrontabRule{
			ID:                 uint(i),
			Brn:                "brn:bce:cfc:bj:640c8817bd1de2928d47256dd0620ce5:function:test-bls:$LATEST",
			UUID:               "f6b3b5a0-4cdb-4072-a089-bdd46df4d6b4",
			ScheduleExpression: "rate(1 minute)",
			CreatedAt:          time.Now(),
			Input:              &input,
		}
		ret = append(ret, tmp)
	}
	return
}

func GetApiGatewayPolicy(n int) (ret []dao.Policy) {
	for i := 0; i < n; i++ {
		tmp := dao.Policy{
			ID:               uint(i),
			StatementId:      fmt.Sprintf("%d-4f3d-8555-c233c7ebd495", i),
			Effect:           "Allow",
			Action:           "cfc:InvokeFunction",
			Resource:         "brn:bce:cfc:bj:640c8817bd1de2928d47256dd0620ce5:function:hello:$LATEST",
			PrincipalService: "dueros.baidu.com",
			Source:           "api-gateway/GWGP-LqsMHhpNwET/test",
			SourceAccount:    "",
			CreatedAt:        time.Now(),
		}
		ret = append(ret, tmp)
	}
	return
}

func GetRowsBillingPrepayRes(as []dao.BillingPrepayResource) *sqlmock.Rows {
	var fieldNames = []string{"id", "account_id", "region", "order_id", "resource_type", "resource_state", "resource_id", "order_state", "product_type", "time_unit", "updated_at", "created_at"}
	rows := sqlmock.NewRows(fieldNames)
	for _, a := range as {
		rows = rows.AddRow(a.Id, a.AccountId, a.Region, a.OrderId, a.ResourceType, a.ResourceState, a.ResourceId, a.OrderState, a.ProductType, a.TimeUnit, a.UpdatedAt, a.CreatedAt)
	}
	return rows
}

func GetTestBillingPrepayResCreating(n int) (ret []dao.BillingPrepayResource) {
	for i := 0; i < n; i++ {
		u := dao.BillingPrepayResource{
			Id:            int64(i),
			AccountId:     "c7ac82ae14ef42d1a4ffa3b2ececa17f",
			OrderId:       fmt.Sprintf("order_%d", i),
			ResourceType:  "FunctionCallCount",
			ResourceState: "INIT",
			ResourceId:    "83349aca2e0a43ca8e6f32b3a619d4cf",
			OrderState:    "CREATINT",
			ProductType:   "prepay",
			TimeUnit:      "month",
			UpdatedAt:     time.Now(),
			CreatedAt:     time.Now(),
		}
		ret = append(ret, u)
	}
	return
}

func GetRowsBillingRes(as []dao.BillingResource) *sqlmock.Rows {
	var fieldNames = []string{"id", "account_id", "resource_state", "create_state", "updated_at", "created_at"}
	rows := sqlmock.NewRows(fieldNames)
	for _, a := range as {
		rows = rows.AddRow(a.Id, a.AccountId, a.ResourceState, a.CreateState, a.UpdatedAt, a.CreatedAt)
	}
	return rows
}

func GetTestBillingRes(n int) (ret []dao.BillingResource) {
	for i := 0; i < n; i++ {
		u := dao.BillingResource{
			Id:            uint(i),
			AccountId:     "c7ac82ae14ef42d1a4ffa3b2ececa17f",
			ResourceState: "RUNNING",
			CreateState:   "CREATED",
			UpdatedAt:     time.Now(),
			CreatedAt:     time.Now(),
		}
		ret = append(ret, u)
	}
	if n == 0 {
		u := dao.BillingResource{}
		ret = append(ret, u)
	}
	return
}

func GetTestBillingResCreating(n int) (ret []dao.BillingResource) {
	for i := 0; i < n; i++ {
		u := dao.BillingResource{
			Id:            uint(i),
			AccountId:     "c7ac82ae14ef42d1a4ffa3b2ececa17f",
			ResourceState: "",
			CreateState:   "CREATINT",
			UpdatedAt:     time.Now(),
			CreatedAt:     time.Now(),
		}
		ret = append(ret, u)
	}
	return
}

func FixedFullRe(s string) string {
	return fmt.Sprintf("^%s$", regexp.QuoteMeta(s))
}

func GetTestEventSource(n int) (ret []dao.FuncEventSource) {
	enable := true
	curTime := time.Now()
	for i := 0; i < n; i++ {
		u := dao.FuncEventSource{
			Id:                        uint(i),
			Uuid:                      fmt.Sprintf("brn:bce:cfc:bj:640c8817bd1de2928d47256dd0620ce5:function:test_%d:alias_name", i),
			BatchSize:                 int(i),
			Enabled:                   &enable,
			FunctionBrn:               fmt.Sprintf("function_brn_%d", i),
			EventSourceBrn:            fmt.Sprintf("event_source_brn_%d", i),
			FunctionArn:               fmt.Sprintf("function_arn_%d", i),
			EventSourceArn:            fmt.Sprintf("event_source_arn_%d", i),
			Type:                      fmt.Sprintf("type_%d", i),
			FunctionName:              fmt.Sprintf("function_name_%d", i),
			StartingPosition:          fmt.Sprintf("StartingPosition_%d", i),
			StartingPositionTimestamp: &curTime,
			StateTransitionReason:     fmt.Sprintf("state_transition_reason_%d", i),
			Uid:                       "123456",
			UpdatedAt:                 time.Now(),
			CreatedAt:                 time.Now(),
			DeletedAt:                 time.Now(),
			State:                     fmt.Sprintf("state_%d", i),
			LastProcessingResult:      fmt.Sprintf("LastProcessingResult_%d", i),
			LastModified:              time.Now(),
		}
		ret = append(ret, u)
	}
	return
}

func GetRowsEventSource(eventSourceSlice []dao.FuncEventSource) *sqlmock.Rows {
	var fieldNames = []string{"id", "uuid", "uid", "batch_size", "enabled", "function_brn", "function_arn", "event_source_brn", "event_source_arn", "type", "function_name", "start_position", "start_position_timestamp", "start_transition_reason", "updated_at", "created_at", "deleted_at", "state", "last_processing_result", "last_modified"}
	rows := sqlmock.NewRows(fieldNames)
	if eventSourceSlice != nil {
		for _, a := range eventSourceSlice {
			rows = rows.AddRow(a.Id, a.Uuid, a.Uid, a.BatchSize, a.Enabled, a.FunctionBrn, a.FunctionArn, a.EventSourceBrn, a.EventSourceArn, a.Type, a.FunctionName, a.StartingPosition, a.StartingPositionTimestamp, a.StateTransitionReason, a.UpdatedAt, a.CreatedAt, a.DeletedAt, a.State, a.LastProcessingResult, a.LastModified)
		}
	}
	return rows
}

func GetTestLayers(n int) (ret []api.Layer) {
	enable := true
	for i := 0; i < n; i++ {
		layerName := fmt.Sprintf("layerName%d", i)
		uid := "c7ac82ae14ef42d1a4ffa3b2ececa17f"
		layerBrn := brn.GenerateLayerBrn("bj", uid, layerName)
		u := api.Layer{
			Id:                   uint(i),
			Uid:                  "c7ac82ae14ef42d1a4ffa3b2ececa17f",
			CodeSha256:           "n5o+gRbN8lrBumjLUdrfGYzPJbybvI9q1c8LIHieE9o=",
			CodeId:               "a9757e08-0090-4d36-bbca-c0b4a9d3eb95",
			CodeSize:             3883,
			SquashFsSha256:       "dPOIzlT3bZdUmSeO+5TcSREdG2kMjtUNQo/e1OT8MoU=",
			UncompressedCodeSize: 12966,
			CompatibleRuntimeStr: "python2",
			Description:          "desc",
			LicenseInfo:          "license info",
			LayerName:            fmt.Sprintf("layerName%d", i),
			Version:              1,
			Brn:                  layerBrn.String(),
			IsVended:             &enable,
			Enabled:              &enable,
			CreatedAt:            time.Now(),
		}
		ret = append(ret, u)
	}
	return
}

func GetRowsLayer(layerSlice []api.Layer) *sqlmock.Rows {
	var fieldNames = []string{"id", "uid", "code_sha256", "squashfs_sha256", "code_id", "code_size", "uncompressed_code_size", "compatible_runtime", "description", "license_info", "layer_name", "version", "brn", "is_vended", "enabled", "created_at", "deleted_at"}
	rows := sqlmock.NewRows(fieldNames)
	if layerSlice != nil {
		for _, a := range layerSlice {
			rows = rows.AddRow(a.Id, a.Uid, a.CodeSha256, a.SquashFsSha256, a.CodeId, a.CodeSize, a.UncompressedCodeSize, a.CompatibleRuntimeStr, a.Description, a.LicenseInfo, a.LayerName, a.Version, a.Brn, a.IsVended, a.Enabled, a.CreatedAt, a.DeletedAt)
		}
	}
	return rows
}

func GetCount(num int) *sqlmock.Rows {
	var fieldNames = []string{"count(*)"}
	rows := sqlmock.NewRows(fieldNames)
	rows = rows.AddRow(num)
	return rows
}

func GetRowsRuntimeConfig(runtimeRonfigs []dao.RuntimeConfig) *sqlmock.Rows {
	var fieldNames = []string{"id", "name", "editale", "default_ext", "bin", "path", "sqfs_path", "argstr", "deprecated_at"}
	rows := sqlmock.NewRows(fieldNames)
	for _, rc := range runtimeRonfigs {
		rows = rows.AddRow(rc.Id, rc.Name, rc.Editable, rc.DefaultExt, rc.Bin, rc.Path, rc.SqfsPath, rc.ArgStr, rc.DeprecatedAt)
	}

	return rows
}

func GetTestRuntimeConfig(n int) (ret []dao.RuntimeConfig) {
	for i := 0; i < n; i++ {
		rc := dao.RuntimeConfig{
			RuntimeConfiguration: api.RuntimeConfiguration{
				Name: "nodejs8.5",
			},
			DeprecatedAt: time.Date(2038, 1, 1, 1, 1, 1, 1, time.Local),
		}

		ret = append(ret, rc)
	}
	return
}

func GetRowsService(svs []dao.Service) *sqlmock.Rows {
	var fieldNames = []string{"id", "uid", "region", "service_name", "service_desc", "service_conf", "status", "updated_at", "created_at"}
	rows := sqlmock.NewRows(fieldNames)
	for _, s := range svs {
		rows = rows.AddRow(s.Id, s.Uid, s.Region, s.ServiceName, s.ServiceDesc, s.ServiceConf, s.Status, s.UpdatedAt, s.CreatedAt)
	}
	return rows
}

func GetTestService(n int) (ret []dao.Service) {
	for i := 0; i < n; i++ {
		tmp := dao.Service{
			Id:          uint(i),
			Uid:         fmt.Sprintf("%d0457f0b-20d8-4f3d-8555-c233c7ebd495", i),
			Region:      fmt.Sprintf("region_%d", i),
			ServiceName: fmt.Sprintf("bpname_%d", i),
			ServiceDesc: convert.String(fmt.Sprintf("description_%d", i)),
			ServiceConf: fmt.Sprintf("keywords_%d", i),
			Status:      1,
			UpdatedAt:   time.Now(),
			CreatedAt:   time.Now(),
		}
		ret = append(ret, tmp)
	}
	return
}

func GetTestCluster(eventSource *dao.FuncEventSource) *sqlmock.Rows {
	var fieldNames = []string{
		"id", "uuid", "uid",
		"batch_size", "enabled", "function_brn",
		"function_arn", "event_source_brn", "event_source_arn",
		"type", "function_name", "start_position",
		"start_position_timestamp", "start_transition_reason",
		"updated_at", "created_at", "deleted_at", "state",
		"last_processing_result", "last_modified",
		"ClusterID",
	}

	rows := sqlmock.NewRows(fieldNames)
	rows = rows.AddRow(
		eventSource.Id, eventSource.Uuid, eventSource.Uid, eventSource.BatchSize,
		eventSource.Enabled, eventSource.FunctionBrn, eventSource.FunctionArn,
		eventSource.EventSourceBrn, eventSource.EventSourceArn, eventSource.Type,
		eventSource.FunctionName, eventSource.StartingPosition, eventSource.StartingPositionTimestamp,
		eventSource.StateTransitionReason, eventSource.UpdatedAt, eventSource.CreatedAt,
		eventSource.DeletedAt, eventSource.State, eventSource.LastProcessingResult,
		eventSource.LastModified, eventSource.ClusterID,
	)
	return rows
}
