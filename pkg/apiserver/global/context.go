package global

import (
	"context"

	"github.com/emicklei/go-restful"
	"go.uber.org/zap/zapcore"
	"icode.baidu.com/baidu/faas/kun/pkg/api"
	"icode.baidu.com/baidu/faas/kun/pkg/util/logs"
)

//ApiserverContext xxx
type ApiserverContext struct {
	Request     *restful.Request
	Response    *restful.Response
	Context     *Context
	Method      string
	Logger      *logs.Logger
	Observer    *logs.Observer
	SpanContext context.Context
}

type Context struct {
	RequestID   string
	FunctionBrn string
}

//BuildApiserverContext xxx
func BuildApiserverContext(request *restful.Request, response *restful.Response, method string) *ApiserverContext {
	requestID := request.HeaderParameter(api.HeaderXRequestID)
	appname := request.HeaderParameter(api.AppNameKey)
	ctx := &Context{
		RequestID: requestID,
	}
	apiserverContext := &ApiserverContext{
		Request:     request,
		Response:    response,
		Context:     ctx,
		Method:      method,
		Logger:      logs.NewLogger().V(2).With<PERSON>ield("request_id", requestID).WithField(api.AppNameKey, appname),
		Observer:    logs.NewObserver(),
		SpanContext: context.Background(),
	}
	apiserverContext.Observer.SetMetrics(AC.Metrics)
	return apiserverContext
}

func (ctx *Context) MarshalLogObject(enc zapcore.ObjectEncoder) error {
	enc.AddString("RequestID", ctx.RequestID)
	enc.AddString("FunctionBrn", ctx.FunctionBrn)
	return nil
}
