package order

import (
	"icode.baidu.com/baidu/faas/kun/pkg/api"
	"icode.baidu.com/baidu/faas/kun/pkg/bce/iam"
)

type NoopOrder struct{}

func NewNoopOrderManager() OrderInterface {
	return &NoopOrder{}
}

func (NoopOrder) QueryOrder(orderId string, requestId string, billingEndpoint string, iamClient iam.ClientInterface) (*api.QueryOrderResponce, error) {
	return &api.QueryOrderResponce{
		Uuid:           "",
		OrderType:      "",
		AccountId:      "noopuser",
		UserId:         "noopuser",
		ServiceType:    "CFC",
		ProductType:    "",
		SubProductType: "",
		Status:         "CREATED",
	}, nil
}
func (NoopOrder) QueryOrderDetail(orderId string, requestId string, billingEndpoint string, iamClient iam.ClientInterface) (*api.OrderDetail, error) {
	return nil, nil
}
func (NoopOrder) QueryOrderList(accountId string, requestId string, billingEndpoint string, iamClient iam.ClientInterface) (*api.QueryOrderListResponse, error) {
	queryOrderListRes := &api.QueryOrderListResponse{}
	orderListRes := api.OrderListResponse{
		Uuid:        "",
		ResourceIds: []string{"noop-resource"},
		Status:      "CREATED",
		AccountId:   "noopuser",
	}
	item := api.OrderItems{
		Region: "on-premise",
	}

	orderListRes.Items = append(orderListRes.Items, item)
	queryOrderListRes.Orders = append(queryOrderListRes.Orders, orderListRes)

	return queryOrderListRes, nil
}

func (NoopOrder) UpdateOrder(orderId string, resourceId string, requestId string, billingEndpoint string, iamClient iam.ClientInterface) error {
	return nil
}

func (NoopOrder) CreateOrder(iamClient iam.ClientInterface, requestId string, region string, billingCatalogEndpoint string, accountId string) (error, *api.NewOrderResponce) {
	return nil, &api.NewOrderResponce{
		OrderId: "",
	}
}
func (NoopOrder) CreatePackageOrder(couponId string, iamClient iam.ClientInterface, requestId string, region string, billingCatalogEndpoint string, accountId string, count int, flavor api.FlavorInfo) (error, string) {
	return nil, ""
}
func (NoopOrder) QueryPackageStatus(accountId string, orderId string, requestId string, billingPackageEndpoint string, iamClient iam.ClientInterface) ([]api.PackageInfoResponse, error) {
	return nil, nil
}
func (NoopOrder) CreatePackageResource(billingPackageEndpoint string, billingEndpoint string, iamClient iam.ClientInterface, requestId string, orderId string) ([]api.PackageInfoResponse, error) {
	return nil, nil
}
func (NoopOrder) UpdateOrderStatus(orderId string, resourceId []api.PackageInfoResponse, orderStatus string, packageStatus string, requestId string, billingEndpoint string, iamClient iam.ClientInterface) error {
	return nil
}
func (NoopOrder) QueryPackageInfo(accountId string, queryPackageInfo *api.QueryPackageInfo, requestId string, billingPackageEndpoint string, iamClient iam.ClientInterface) (*api.QueryPackageInfoResponse, error) {
	return nil, nil
}
func (NoopOrder) UpdateOrderToFail(orderId string, requestId string, billingEndpoint string, iamClient iam.ClientInterface) error {
	return nil
}

func (NoopOrder) CreateAutoRenew(requestId string, autoRenewReq *api.CreateAutoRenewRequest, billingOrderRenewEndpoint string, iamClient iam.ClientInterface) error {
	return nil
}

func (NoopOrder) QueryAutoRenew(requestId string, accountId string, serviceId string, region string, billingOrderRenewEndpoint string, iamClient iam.ClientInterface) ([]api.QueryAutoRenewResponse, error) {
	return nil, nil
}

func (NoopOrder) DeleteAutoRenew(accountId string, requestId string, ruleId string, billingOrderRenewEndpoint string, iamClient iam.ClientInterface) error {
	return nil
}

func (NoopOrder) BatchQueryAutoRenew(requestId string, resources []api.AutoRenewResource, billingOrderRenewEndpoint string, iamClient iam.ClientInterface) ([]api.QueryAutoRenewResponse, error) {
	return nil, nil
}
