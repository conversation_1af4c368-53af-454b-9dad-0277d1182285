package order

import (
	"fmt"
	"testing"
	"time"

	"github.com/google/uuid"

	"icode.baidu.com/baidu/faas/kun/pkg/api"
	"icode.baidu.com/baidu/faas/kun/pkg/bce/iam"
)

func TestQueryBillingCharge(t *testing.T) {
	NewBillingChargeClient()

	iamClient, _ := iam.CreateIAMClient("./test.conf")
	// example
	/*
			{
		     "accountId":"dd7c75c82c504c169a72cf6638a863e6",
		     "serviceName":"BOS",
		     "region":"su",
		     "chargeItem":"StandardIAStorageBytes",
		       "startTime":"2020-03-31T16:00:00Z",
		     "endTime":"2020-04-30T16:00:00Z",
		     "usageMeta": {
		       "minuteReducer": "SumReducer",
		       "dayReducer": "SumReducer",
		       "monthReducer": "SumReducer",
			   "removeZero":"false"
		        }
		     }
	*/

	/*
		FunctionCallCount
		[{"amount":*********,"unit":"Count","timeSpan":{"startTime":"2021-05-31T16:00:00Z","endTime":"2021-06-30T16:00:00Z"}}]
		FunctionRunTimeCount
		[{"amount":*********.*****************,"unit":"Count","timeSpan":{"startTime":"2021-05-31T16:00:00Z","endTime":"2021-06-30T16:00:00Z"}}]
		PublicDataTransfer
		[{"amount":***********,"unit":"Bytes","timeSpan":{"startTime":"2021-05-31T16:00:00Z","endTime":"2021-06-30T16:00:00Z"}}]

	*/
	accountID := "8ba8fe2d049948d88d04918b78ba820c"
	defaultUsageMeta := api.UsageMeta{
		MinuteReducer: "SumReducer",
		DayReducer:    "SumReducer",
		MonthReducer:  "SumReducer",
		RemoveZero:    false,
	}

	req := api.BillingResourceQueryRequest{
		AccountId:   accountID,
		ServiceName: "CFC",
		Region:      "bj",
		ChargeItem:  "FunctionRunTimeCount",
		StartTime:   "2021-06-01T16:00:00Z",
		EndTime:     "2021-06-10T16:00:00Z",
		UsageMeta:   defaultUsageMeta,
	}
	endpoint := "http://billingsvr.bce-billing.baidu-int.com"
	reqid, _ := uuid.NewUUID()
	_, err := BillingChargeClient.QueryBillingResourceUsage(&req, reqid.String(), endpoint, iamClient)
	if err != nil {
		t.Errorf("%s", err)
	}
}

func TestTime(t *testing.T) {
	now := time.Now()
	currentYear, currentMonth, _ := now.Date()
	currentLocation := now.Location()

	firstOfMonth := time.Date(currentYear, currentMonth, 1, 0, 0, 0, 0, currentLocation).UTC()
	fmt.Println(firstOfMonth.Format(time.RFC3339))

}
