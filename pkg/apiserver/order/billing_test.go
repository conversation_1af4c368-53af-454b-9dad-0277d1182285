package order

import (
	"log"
	"path"
	"runtime"
	"strings"
	"testing"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/jinzhu/gorm"
	"github.com/stretchr/testify/assert"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/dbengine"
	"icode.baidu.com/baidu/faas/kun/pkg/bce/iam"
)

func TestNewOrder(t *testing.T) {
	//QueryOrderList CreateRes
	//QueryOrderList CreateOrder UpdateOrder CreateRes
	MockOrderManager()

	var m sqlmock.Sqlmock
	m, dbengine.Engine = MockDB()
	_, filename, _, _ := runtime.Caller(1)
	tmpPathArr := strings.Split(path.Join(path.Dir(filename)), "/kun/")

	iamClient, _ := iam.CreateIAMClient(tmpPathArr[0] + "/kun/pkg/bce/iam/mock.yaml")
	m.ExpectExec(".*").WillReturnResult(sqlmock.NewResult(1, 1))
	err := CreateOrder("abc", "111", "", "", iamClient, "bj")
	assert.Equal(t, nil, err)
}

func MockDB() (sqlmock.Sqlmock, *gorm.DB) {
	db, mock, err := sqlmock.New()
	if err != nil {
		log.Fatalf("can't create sqlmock: %s", err)
	}

	gormDB, gerr := gorm.Open("mysql", db)
	if gerr != nil {
		log.Fatalf("can't open gorm connection: %s", err)
	}
	gormDB.LogMode(true)
	gormDB = gormDB.Set("gorm:update_column", true)
	dbengine.Engine = gormDB
	return mock, gormDB
}
