package order

import (
	"bytes"
	"encoding/json"
	"fmt"
	"net/http"

	"icode.baidu.com/baidu/faas/kun/pkg/api"
	"icode.baidu.com/baidu/faas/kun/pkg/bce/iam"
)

var BillingChargeClient BillingChargeServiceInterface

func NewBillingChargeClient() BillingChargeServiceInterface {
	BillingChargeClient = NewBillingCharge()
	return BillingChargeClient
}

// BillingChargeServiceInterface 计费资源请求 http://wiki.baidu.com/pages/viewpage.action?pageId=1124114433
type (
	BillingChargeServiceInterface interface {
		QueryBillingResourceUsage(resourceUsageReq *api.BillingResourceQueryRequest, requestId string, billingChargeServiceEndpoint string, iamClient iam.ClientInterface) (*api.BillingResourceQueryResponce, error)
	}
	BillingCharge struct {
	}
)

// NewOrderManager 创建Order管理器
func NewBillingCharge() *BillingCharge {
	return &BillingCharge{}
}

func (b *BillingCharge) QueryBillingResourceUsage(resourceUsageReq *api.BillingResourceQueryRequest, requestId string, billingChargeServiceEndpoint string, iamClient iam.ClientInterface) (*api.BillingResourceQueryResponce, error) {

	body := new(bytes.Buffer)
	err := json.NewEncoder(body).Encode(resourceUsageReq)
	if err != nil {
		return nil, err
	}
	req, err := http.NewRequest(http.MethodPost, billingChargeServiceEndpoint+"/v1/resourceusage/usage/data/trail", body)
	if err != nil {
		return nil, err
	}
	rsp, err := HttpRequestForService(req, requestId, iamClient)
	if err != nil {
		return nil, err
	}
	if rsp == nil {
		return nil, fmt.Errorf("billingCharge response is nil, requestID: %s", requestId)
	}
	defer rsp.Body.Close()
	if rsp.StatusCode != http.StatusOK {
		resp := &api.OrderResp{}
		json.NewDecoder(rsp.Body).Decode(resp)
		err = fmt.Errorf("query user's order faild. requestId: %s, code: %s, message: %s", resp.RequestID, resp.Code, resp.Message)
		return nil, err
	}
	resp := &api.BillingResourceQueryResponce{}
	json.NewDecoder(rsp.Body).Decode(resp)
	return resp, nil
}
