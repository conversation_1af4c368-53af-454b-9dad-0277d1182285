/**
 * Created Date: Wednesday, September 13th 2017, 2:33:05 pm
 * Author: hefangshi
 * Copyright (c) 2017 Baidu.Inc
 *
 */

package order

import (
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/dao"
	"icode.baidu.com/baidu/faas/kun/pkg/bce/iam"
	"icode.baidu.com/baidu/faas/kun/pkg/util/logs"
)

func CreateOrder(accountId string, requestId string, billingEndpoint string, billingCatalogEndpoint string, iamClient iam.ClientInterface, region string) error {
	//为订单绑定和更新资源id时，资源id为用户的accountid,资源服务方会为资源生成一个新的id，如果用户用资源id生成的话，其他的失效订单后续处理会直接废弃。
	resourceId := accountId
	//查询用户CFC下的CREATED状态的账单 如果region和当前region相同，则已经绑定资源。
	queryOrderListResponse, err := OrderManager.QueryOrderList(accountId, requestId, billingEndpoint, iamClient)
	if err != nil {
		logs.Errorf("QueryOrderList fail, requestId, %v, err: %v", requestId, err)
		return err
	}
	alreadyCreated := false
	for i := 0; i < len(queryOrderListResponse.Orders); i++ {
		if queryOrderListResponse.Orders[i].Status == "CREATED" {
			for j := 0; j < len(queryOrderListResponse.Orders[i].ResourceIds); j++ {
				if queryOrderListResponse.Orders[i].Items[0].Region == region {
					alreadyCreated = true
					break
				}
			}
		}
	}
	//已经绑定了资源，则创建数据库记录，退出
	if alreadyCreated {
		billingResource := &dao.BillingResource{
			AccountId:     accountId,
			ResourceState: "RUNNING",
			CreateState:   "CREATED",
		}
		if err = dao.CreateRes(billingResource); err != nil {
			logs.Errorf("billingResource is exist CreateBillingResource fail, requestId, %v, err: %v", requestId, err)
			return err
		}
		return nil
	}
	//创建订单
	err, resp := OrderManager.CreateOrder(iamClient, requestId, region, billingCatalogEndpoint, accountId)
	if err != nil {
		logs.Errorf("CreateOrder fail, requestId, %v, err: %v", requestId, err)
		return err
	}
	//更新绑定订单
	err = OrderManager.UpdateOrder(resp.OrderId, resourceId, requestId, billingEndpoint, iamClient)
	if err != nil {
		logs.Errorf("UpdateOrder fail, requestId, %v, err: %v", requestId, err)
		return err
	}
	//新建数据库记录
	billingResource := &dao.BillingResource{
		AccountId:     accountId,
		ResourceState: "RUNNING",
		CreateState:   "CREATED",
	}
	if err = dao.CreateRes(billingResource); err != nil {
		logs.Errorf("billingResource is not exist CreateBillingResource fail, requestId, %v, err: %v", requestId, err)
		return err
	}
	return nil
}
