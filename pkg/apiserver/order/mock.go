package order

import (
	"encoding/json"
	"errors"

	"icode.baidu.com/baidu/faas/kun/pkg/api"
	"icode.baidu.com/baidu/faas/kun/pkg/bce/iam"
)

type MockOrder struct{}

func MockOrderManager() {
	OrderManager = &MockOrder{}
}

func (b *MockOrder) QueryOrder(orderId string, requestId string, billingEndpoint string, iamClient iam.ClientInterface) (*api.QueryOrderResponce, error) {
	if orderId == "1" {
		//准备创建
		resp := &api.QueryOrderResponce{
			Uuid:           "123",
			OrderType:      "NEW",
			AccountId:      "abc",
			UserId:         "abc",
			ServiceType:    "CFC",
			ProductType:    "postpay",
			SubProductType: "",
			Status:         "READY_FOR_CREATE",
		}
		return resp, nil
	} else if orderId == "2" {
		//已经创建好
		resp := &api.QueryOrderResponce{
			Uuid:           "123",
			OrderType:      "NEW",
			AccountId:      "abc",
			UserId:         "abc",
			ServiceType:    "CFC",
			ProductType:    "postpay",
			SubProductType: "",
			Status:         "CREATED",
		}
		return resp, nil
	} else if orderId == "3" {
		//量包READY_FOR_CREATE状态
		resp := &api.QueryOrderResponce{
			Uuid:           "123",
			OrderType:      "NEW",
			AccountId:      "1",
			UserId:         "1",
			ServiceType:    "CFC",
			ProductType:    "prepay",
			SubProductType: "",
			Status:         "READY_FOR_CREATE",
		}
		return resp, nil
	} else if orderId == "4" {
		//量包CREATING状态
		resp := &api.QueryOrderResponce{
			Uuid:           "123",
			OrderType:      "NEW",
			AccountId:      "1",
			UserId:         "1",
			ServiceType:    "CFC",
			ProductType:    "prepay",
			SubProductType: "",
			Status:         "CREATING",
		}
		return resp, nil
	} else if orderId == "5" {
		//量包CREATING状态
		resp := &api.QueryOrderResponce{
			Uuid:           "123",
			OrderType:      "NEW",
			AccountId:      "1",
			UserId:         "1",
			ServiceType:    "CFC",
			ProductType:    "prepay",
			SubProductType: "",
			Status:         "CREATING",
		}
		return resp, nil
	}
	return nil, nil
}
func (b *MockOrder) QueryOrderDetail(orderId string, requestId string, billingEndpoint string, iamClient iam.ClientInterface) (*api.OrderDetail, error) {
	if orderId == "1" {
		flavor1 := api.FlavorDetail{Name: "count"}
		flavor2 := api.FlavorDetail{Name: "subServiceType"}
		return &api.OrderDetail{ProductType: "prepay", Items: []api.Items{api.Items{Flavor: []api.FlavorDetail{flavor1, flavor2}}}}, nil
	} else if orderId == "2" {
		flavor1 := api.FlavorDetail{Name: "count"}
		flavor2 := api.FlavorDetail{Name: "subServiceType"}
		return &api.OrderDetail{ProductType: "postpay", Items: []api.Items{api.Items{Flavor: []api.FlavorDetail{flavor1, flavor2}}}}, nil
	} else if orderId == "3" {
		flavor1 := api.FlavorDetail{Name: "count"}
		flavor2 := api.FlavorDetail{Name: "subServiceType"}
		return &api.OrderDetail{ProductType: "postpay", Items: []api.Items{api.Items{Flavor: []api.FlavorDetail{flavor2, flavor1}}}}, nil
	} else if orderId == "4" {
		return nil, errors.New("失败")
	}
	return nil, nil
}
func (b *MockOrder) QueryOrderList(accountId string, requestId string, billingEndpoint string, iamClient iam.ClientInterface) (*api.QueryOrderListResponse, error) {
	if accountId == "abc" {
		resp := &api.QueryOrderListResponse{
			Size:  1,
			Begin: 1,
			Orders: []api.OrderListResponse{
				{
					Uuid:        "123",
					ResourceIds: []string{"abc"},
					Status:      "CREATED",
					AccountId:   "abc",
					Items: []api.OrderItems{
						{
							Region: "bj",
						},
					},
				},
			},
		}
		return resp, nil
	} else {
		resp := &api.QueryOrderListResponse{}
		return resp, nil
	}
}

func (b *MockOrder) UpdateOrder(orderId string, resourceId string, requestId string, billingEndpoint string, iamClient iam.ClientInterface) error {
	if orderId == "4" {
		return errors.New("更新失败")
	}
	return nil
}

func (b *MockOrder) CreateOrder(iamClient iam.ClientInterface, requestId string, region string, billingCatalogEndpoint string, accountId string) (error, *api.NewOrderResponce) {
	resp := &api.NewOrderResponce{
		OrderId: "123",
	}
	return nil, resp
}

func (b *MockOrder) CreatePackageOrder(couponId string, iamClient iam.ClientInterface, requestId string, region string, billingCatalogEndpoint string, accountId string, count int, flavor api.FlavorInfo) (error, string) {
	if count == 1 {
		return nil, "1"
	} else if count == 2 {
		return nil, ""
	} else {
		return errors.New("创建订单失败"), "3"
	}
}

func (b *MockOrder) QueryPackageStatus(accountId string, orderId string, requestId string, billingPackageEndpoint string, iamClient iam.ClientInterface) ([]api.PackageInfoResponse, error) {
	if orderId == "1" {
		return nil, nil
	} else if orderId == "3" {
		return []api.PackageInfoResponse{api.PackageInfoResponse{Status: "INITIAL", PackageName: "1"}}, nil
	} else {
		return []api.PackageInfoResponse{api.PackageInfoResponse{Status: "CREATED", PackageName: "1"}}, nil
	}
}

func (b *MockOrder) QueryPackageInfo(accountId string, queryPackageInfo *api.QueryPackageInfo, requestId string, billingPackageEndpoint string, iamClient iam.ClientInterface) (*api.QueryPackageInfoResponse, error) {
	if queryPackageInfo.Order == "desc" {
		return nil, errors.New("失败")
	}
	return &api.QueryPackageInfoResponse{TotalCount: 2}, nil
}
func (b *MockOrder) CreatePackageResource(billingPackageEndpoint string, billingEndpoint string, iamClient iam.ClientInterface, requestId string, orderId string) ([]api.PackageInfoResponse, error) {
	if orderId == "1" || orderId == "2" || orderId == "3" {
		return nil, nil
	} else {
		return nil, errors.New("失败")
	}
}
func (b *MockOrder) UpdateOrderStatus(orderId string, resourceId []api.PackageInfoResponse, orderStatus string, packageStatus string, requestId string, billingEndpoint string, iamClient iam.ClientInterface) error {
	if orderId == "1" || orderId == "2" || orderId == "3" || orderId == "4" {
		return nil
	} else {
		return errors.New("失败")
	}
}
func (b *MockOrder) UpdateOrderToFail(orderId string, requestId string, billingEndpoint string, iamClient iam.ClientInterface) error {
	if orderId == "1" {
		return nil
	} else if orderId == "2" {
		return nil
	} else {
		return nil
	}
}

func (b *MockOrder) CreateAutoRenew(requestId string, autoRenewReq *api.CreateAutoRenewRequest, billingOrderRenewEndpoint string, iamClient iam.ClientInterface) error {
	return nil
}

func (b *MockOrder) QueryAutoRenew(requestId string, accountId string, serviceId string, region string, billingOrderRenewEndpoint string, iamClient iam.ClientInterface) ([]api.QueryAutoRenewResponse, error) {
	return nil, nil
}

func (b *MockOrder) DeleteAutoRenew(accountId string, requestId string, ruleId string, billingOrderRenewEndpoint string, iamClient iam.ClientInterface) error {
	return nil
}

func (b *MockOrder) BatchQueryAutoRenew(requestId string, resources []api.AutoRenewResource, billingOrderRenewEndpoint string, iamClient iam.ClientInterface) ([]api.QueryAutoRenewResponse, error) {
	return nil, nil
}

type MockBillingChargeClient struct{}

func NewMockBillingChargeClient() {
	BillingChargeClient = &MockBillingChargeClient{}
}
func (b *MockBillingChargeClient) QueryBillingResourceUsage(resourceUsageReq *api.BillingResourceQueryRequest, requestId string, billingChargeServiceEndpoint string, iamClient iam.ClientInterface) (*api.BillingResourceQueryResponce, error) {
	if resourceUsageReq.AccountId == "abc" {
		body := []byte(`[{"amount":*********.*****************,"unit":"Count","timeSpan":{"startTime":"2021-05-31T16:00:00Z","endTime":"2021-06-30T16:00:00Z"}}]`)

		resp := &api.BillingResourceQueryResponce{}
		_ = json.Unmarshal(body, resp)
		return resp, nil

	} else {
		resp := &api.BillingResourceQueryResponce{}
		return resp, nil
	}
}
