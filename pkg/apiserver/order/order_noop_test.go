package order

import (
	"icode.baidu.com/baidu/faas/kun/pkg/api"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestNewNoopOrderManager(t *testing.T) {
	noopOrderManager := NewNoopOrderManager()

	assert.NotPanics(t, func() {
		noopOrderManager.QueryOrder("", "", "", nil)
		noopOrderManager.QueryOrderList("", "", "", nil)
		noopOrderManager.UpdateOrder("", "", "", "", nil)
		noopOrderManager.CreateOrder(nil, "", "", "", "")
		noopOrderManager.UpdateOrderToFail("", "", "", nil)
		noopOrderManager.CreatePackageOrder("", nil, "", "", "", "", 0, api.FlavorInfo{})
		noopOrderManager.CreatePackageResource("", "", nil, "", "")
		noopOrderManager.QueryPackageInfo("", nil, "", "", nil)
	})
}
