/**
 * Created Date: Wednesday, September 13th 2017, 2:33:05 pm
 * Author: hefan<PERSON>hi
 * Copyright (c) 2017 Baidu.Inc
 *
 */

package order

import (
	"bytes"
	"encoding/json"
	"errors"
	"fmt"
	"io/ioutil"
	"net/http"
	"strings"
	"time"

	"icode.baidu.com/baidu/faas/kun/pkg/api"
	"icode.baidu.com/baidu/faas/kun/pkg/bce/auth"
	"icode.baidu.com/baidu/faas/kun/pkg/bce/iam"
	"icode.baidu.com/baidu/faas/kun/pkg/bce/sts_credential"
	"icode.baidu.com/baidu/faas/kun/pkg/util/logs"
)

const (
	FunctionCallCountFreeType     = "FunctionCallCountFree"
	PublicDataTransferFreeType    = "PublicDataTransferFree"
	FunctionRunTimeCountFreeType  = "FunctionRunTimeCountFree"
	FunctionCallCountFreeValue    = "1000000"
	PublicDataTransferFreeValue   = "1gi"
	FunctionRunTimeCountFreeValue = "400000"

	FunctionCallCountFreeValueTotal    = "3000000"
	PublicDataTransferFreeValueTotal   = "3G"
	FunctionRunTimeCountFreeValueTotal = "1200000"

	PackageTypeName         = "subServiceType"
	PackageTimeUnitMonth    = "month"
	PackageTimeUnitYear     = "year"
	PackageSpecNameCount    = "count"
	PackageSpecNameCapacity = "capacity"

	FunctionCallCountType    = "FunctionCallCount"
	PublicDataTransferType   = "PublicDataTransfer"
	FunctionRunTimeCountType = "FunctionRunTimeCount"
)

var FunctionCallCountValues = []string{"1000000", "5000000", "10000000", "100000000", "1000000000"}
var FunctionRunTimeCountValues = []string{"100000", "500000", "1000000", "10000000", "100000000", "1000000000"}
var PublicDataTransferValues = []string{"1G", "5G", "10G", "100G", "1000G"}

var OrderManager OrderInterface

func NewOrderManager() OrderInterface {
	OrderManager = NewOrder()
	return OrderManager
}

// OrderInterface order请求管理器
type (
	OrderInterface interface {
		QueryOrder(orderId string, requestId string, billingEndpoint string, iamClient iam.ClientInterface) (*api.QueryOrderResponce, error)
		QueryOrderDetail(orderId string, requestId string, billingEndpoint string, iamClient iam.ClientInterface) (*api.OrderDetail, error)
		QueryPackageStatus(accountId string, orderId string, requestId string, billingPackageEndpoint string, iamClient iam.ClientInterface) ([]api.PackageInfoResponse, error)
		QueryOrderList(accountId string, requestId string, billingEndpoint string, iamClient iam.ClientInterface) (*api.QueryOrderListResponse, error)
		UpdateOrder(orderId string, resourceId string, requestId string, billingEndpoint string, iamClient iam.ClientInterface) error
		UpdateOrderStatus(orderId string, resourceId []api.PackageInfoResponse, orderStatus string, packageStatus string, requestId string, billingEndpoint string, iamClient iam.ClientInterface) error
		CreateOrder(iamClient iam.ClientInterface, requestId string, region string, billingCatalogEndpoint string, accountId string) (error, *api.NewOrderResponce)
		UpdateOrderToFail(orderId string, requestId string, billingEndpoint string, iamClient iam.ClientInterface) error
		CreatePackageOrder(couponId string, iamClient iam.ClientInterface, requestId string, region string, billingCatalogEndpoint string, accountId string, count int, flavor api.FlavorInfo) (error, string)
		CreatePackageResource(billingPackageEndpoint string, billingEndpoint string, iamClient iam.ClientInterface, requestId string, orderId string) ([]api.PackageInfoResponse, error)
		QueryPackageInfo(accountId string, queryPackageInfo *api.QueryPackageInfo, requestId string, billingPackageEndpoint string, iamClient iam.ClientInterface) (*api.QueryPackageInfoResponse, error)
		CreateAutoRenew(requestId string, autoRenewReq *api.CreateAutoRenewRequest, billingOrderRenewEndpoint string, iamClient iam.ClientInterface) error
		QueryAutoRenew(requestId string, accountId string, serviceId string, region string, billingOrderRenewEndpoint string, iamClient iam.ClientInterface) ([]api.QueryAutoRenewResponse, error)
		DeleteAutoRenew(accountId string, requestId string, ruleId string, billingOrderRenewEndpoint string, iamClient iam.ClientInterface) error
		BatchQueryAutoRenew(requestId string, resources []api.AutoRenewResource, billingOrderRenewEndpoint string, iamClient iam.ClientInterface) ([]api.QueryAutoRenewResponse, error)
	}
	Order struct {
	}
)

// NewOrderManager 创建Order管理器
func NewOrder() *Order {
	return &Order{}
}

func (b *Order) QueryOrder(orderId string, requestId string, billingEndpoint string, iamClient iam.ClientInterface) (*api.QueryOrderResponce, error) {
	body := new(bytes.Buffer)
	req, err := http.NewRequest(http.MethodGet, billingEndpoint+"/orders/"+orderId, body)
	rsp, err := HttpRequestForService(req, requestId, iamClient)
	if err != nil {
		return nil, err
	}
	defer rsp.Body.Close()
	if rsp.StatusCode != http.StatusOK {
		resp := &api.OrderResp{}
		json.NewDecoder(rsp.Body).Decode(resp)
		err = errors.New(fmt.Sprintf("query user's order faild. requestId: %s, code: %s, message: %s", resp.RequestID, resp.Code, resp.Message))
		return nil, err
	}
	resp := &api.QueryOrderResponce{}
	err = json.NewDecoder(rsp.Body).Decode(resp)
	if err != nil {
		return nil, err
	}
	return resp, nil
}

func (b *Order) QueryOrderDetail(orderId string, requestId string, billingEndpoint string, iamClient iam.ClientInterface) (*api.OrderDetail, error) {
	body := new(bytes.Buffer)
	req, err := http.NewRequest(http.MethodGet, billingEndpoint+"/orders/"+orderId, body)
	rsp, err := HttpRequestForService(req, requestId, iamClient)
	if err != nil {
		return nil, err
	}
	defer rsp.Body.Close()
	if rsp.StatusCode != http.StatusOK {
		resp := &api.OrderResp{}
		err := json.NewDecoder(rsp.Body).Decode(resp)
		if err != nil {
			logs.Errorf("err:%v ", err)
			return nil, err
		}
		err = errors.New(fmt.Sprintf("query user's orderDetail faild. requestId: %s, code: %s, message: %s", resp.RequestID, resp.Code, resp.Message))
		return nil, err
	}
	resp := &api.OrderDetail{}
	err = json.NewDecoder(rsp.Body).Decode(resp)
	if err != nil {
		logs.Errorf("err:%v ", err)
		return nil, err
	}
	return resp, nil
}
func (b *Order) QueryPackageStatus(accountId string, orderId string, requestId string, billingPackageEndpoint string, iamClient iam.ClientInterface) ([]api.PackageInfoResponse, error) {
	packageInfoRequest := &api.PackageInfoRequest{
		AccountId: accountId,
		OrderIds:  []string{orderId},
	}
	body := new(bytes.Buffer)
	err := json.NewEncoder(body).Encode(packageInfoRequest)
	req, err := http.NewRequest(http.MethodPost, billingPackageEndpoint+"/package/queryPackageName", body)
	rsp, err := HttpRequestForService(req, requestId, iamClient)
	if rsp.StatusCode != http.StatusOK {
		resp := &api.OrderResp{}
		json.NewDecoder(rsp.Body).Decode(resp)
		err = errors.New(fmt.Sprintf("query user's order faild. requestId: %s, code: %s, message: %s", resp.RequestID, resp.Code, resp.Message))
		return nil, err
	}
	var jsonObj []interface{}
	rspBody, _ := ioutil.ReadAll(rsp.Body)
	json.Unmarshal(rspBody, &jsonObj)
	packageInfoResponse := []api.PackageInfoResponse{}
	for i := 0; i < len(jsonObj); i++ {
		ans := jsonObj[i].(map[string]interface{})
		var status string
		var packageName string
		for k, v := range ans {
			switch v := v.(type) {
			case string:
				if k == "status" {
					status = v
				} else if k == "packageName" {
					packageName = v
				}
			}
		}
		packageInfoResponse = append(packageInfoResponse, api.PackageInfoResponse{Status: status, PackageName: packageName})
	}
	defer rsp.Body.Close()
	return packageInfoResponse, nil
}

func (b *Order) QueryPackageInfo(accountId string, queryPackageInfo *api.QueryPackageInfo, requestId string, billingPackageEndpoint string, iamClient iam.ClientInterface) (*api.QueryPackageInfoResponse, error) {
	body := new(bytes.Buffer)
	if queryPackageInfo.Status != "" {
		queryPackageInfoRequest := api.QueryPackageInfoRequest{
			AccountId:   accountId,
			ServiceType: queryPackageInfo.ServiceType,
			Region:      queryPackageInfo.Region,
			PageNo:      queryPackageInfo.PageNo,
			PageSize:    queryPackageInfo.PageSize,
			Status:      queryPackageInfo.Status,
		}
		if len(queryPackageInfo.PackageType) > 0 {
			queryPackageInfoRequest.PackageType = queryPackageInfo.PackageType
		} else {
			queryPackageInfoRequest.PackageType = []string{"FunctionCallCount", "PublicDataTransfer", "FunctionRunTimeCount"}
		}
		if queryPackageInfo.OrderBy != "" {
			queryPackageInfoRequest.OrderBy = queryPackageInfo.OrderBy
		} else {
			queryPackageInfoRequest.OrderBy = "active_time"
		}
		if queryPackageInfo.Order != "" {
			queryPackageInfoRequest.Order = queryPackageInfo.Order
		} else {
			queryPackageInfoRequest.Order = "desc"
		}
		err := json.NewEncoder(body).Encode(queryPackageInfoRequest)
		if err != nil {
			return nil, err
		}
	} else {
		queryPackageInfoRequestAllStatus := api.QueryPackageInfoRequestAllStatus{
			AccountId:   accountId,
			ServiceType: queryPackageInfo.ServiceType,
			Region:      queryPackageInfo.Region,
			PageNo:      queryPackageInfo.PageNo,
			PageSize:    queryPackageInfo.PageSize,
		}
		if len(queryPackageInfo.PackageType) > 0 {
			queryPackageInfoRequestAllStatus.PackageType = queryPackageInfo.PackageType
		} else {
			queryPackageInfoRequestAllStatus.PackageType = []string{"FunctionCallCount", "PublicDataTransfer", "FunctionRunTimeCount"}
		}
		if queryPackageInfo.OrderBy != "" {
			queryPackageInfoRequestAllStatus.OrderBy = queryPackageInfo.OrderBy
		} else {
			queryPackageInfoRequestAllStatus.OrderBy = "active_time"
		}
		if queryPackageInfo.Order != "" {
			queryPackageInfoRequestAllStatus.Order = queryPackageInfo.Order
		} else {
			queryPackageInfoRequestAllStatus.Order = "desc"
		}
		err := json.NewEncoder(body).Encode(queryPackageInfoRequestAllStatus)
		if err != nil {
			return nil, err
		}
	}
	req, err := http.NewRequest(http.MethodPost, billingPackageEndpoint+"/package/getByServiceTypeV3", body)
	rsp, err := HttpRequestForService(req, requestId, iamClient)
	if err != nil {
		logs.Errorf("err:%v ", err)
		return nil, err
	}
	defer rsp.Body.Close()
	if rsp.StatusCode != http.StatusOK {
		resp := &api.OrderResp{}
		json.NewDecoder(rsp.Body).Decode(resp)
		err = errors.New(fmt.Sprintf("query packageInfo faild. requestId: %s, code: %s, message: %s", resp.RequestID, resp.Code, resp.Message))
		return nil, err
	}
	resp := &api.QueryPackageInfoResponse{}
	json.NewDecoder(rsp.Body).Decode(resp)
	return resp, nil
}

func (b *Order) QueryOrderList(accountId string, requestId string, billingEndpoint string, iamClient iam.ClientInterface) (*api.QueryOrderListResponse, error) {
	//查询用户是否有创建成功的后付费订单
	orderListResquest := api.OrderListResquest{
		ServiceType: "CFC",
		AccountId:   accountId,
		Status:      "CREATED",
		ProductType: "postpay",
	}
	body := new(bytes.Buffer)
	err := json.NewEncoder(body).Encode(orderListResquest)
	req, err := http.NewRequest(http.MethodPost, billingEndpoint+"/orders?queryList", body)
	rsp, err := HttpRequestForService(req, requestId, iamClient)
	if err != nil {
		return nil, err
	}
	defer rsp.Body.Close()
	if rsp.StatusCode != http.StatusOK {
		resp := &api.OrderResp{}
		json.NewDecoder(rsp.Body).Decode(resp)
		err = errors.New(fmt.Sprintf("query user's orderlist faild. requestId: %s, code: %s, message: %s", resp.RequestID, resp.Code, resp.Message))
		return nil, err
	}
	resp := &api.QueryOrderListResponse{}
	json.NewDecoder(rsp.Body).Decode(resp)
	return resp, nil
}

func (b *Order) UpdateOrder(orderId string, resourceId string, requestId string, billingEndpoint string, iamClient iam.ClientInterface) error {
	updateOrderResquest := &api.UpdateOrderResquest{
		Status: "CREATED",
	}
	resources := api.Resources{
		Id:     resourceId,
		Key:    "cfc",
		Status: "RUNNING",
	}
	updateOrderResquest.Resources = append(updateOrderResquest.Resources, resources)
	body := new(bytes.Buffer)
	err := json.NewEncoder(body).Encode(updateOrderResquest)
	if err != nil {
		return err
	}
	req, err := http.NewRequest(http.MethodPut, billingEndpoint+"/orders/"+orderId, body)
	rsp, err := HttpRequestForService(req, requestId, iamClient)
	if err != nil {
		return err
	}
	defer rsp.Body.Close()
	if rsp.StatusCode != http.StatusOK {
		resp := &api.OrderResp{}
		json.NewDecoder(rsp.Body).Decode(resp)
		err = errors.New(fmt.Sprintf("update order faild. requestId: %s, code: %s, message: %s", resp.RequestID, resp.Code, resp.Message))
		return err
	}
	return nil
}

func (b *Order) UpdateOrderStatus(orderId string, packageInfo []api.PackageInfoResponse, orderStatus string, packageStatus string, requestId string, billingEndpoint string, iamClient iam.ClientInterface) error {
	updateOrderResquest := &api.UpdateOrderResquest{
		Status: orderStatus,
	}
	resources := []api.Resources{}
	for i := 0; i < len(packageInfo); i++ {
		resourcesTmp := api.Resources{
			Id:     packageInfo[i].PackageName,
			Key:    "cfc",
			Status: packageStatus,
		}
		resources = append(resources, resourcesTmp)
	}
	updateOrderResquest.Resources = resources
	body := new(bytes.Buffer)
	err := json.NewEncoder(body).Encode(updateOrderResquest)
	if err != nil {
		return err
	}
	req, err := http.NewRequest(http.MethodPut, billingEndpoint+"/orders/"+orderId, body)
	rsp, err := HttpRequestForService(req, requestId, iamClient)
	if err != nil {
		return err
	}
	defer rsp.Body.Close()
	if rsp.StatusCode != http.StatusOK {
		resp := &api.OrderResp{}
		json.NewDecoder(rsp.Body).Decode(resp)
		err = errors.New(fmt.Sprintf("update order faild. requestId: %s, code: %s, message: %s", resp.RequestID, resp.Code, resp.Message))
		return err
	}
	return nil
}

func (b *Order) CreateOrder(iamClient iam.ClientInterface, requestId string, region string, billingCatalogEndpoint string, accountId string) (error, *api.NewOrderResponce) {
	if iamClient == nil {
		err := errors.New("iam client is not on service")
		return err, nil
	}
	var stsRoleName = api.StsRoleName
	if stsRoleName == "" {
		err := errors.New("stsRoleName is nill")
		return err, nil
	}
	credential, err := iamClient.AssumeRole(accountId, stsRoleName, requestId, 3600)
	if err != nil {
		err := errors.New(fmt.Sprintf("StsProcessor: assumeRole failed.err:%s", err.Error()))
		return err, nil
	}
	item := api.OrderItemModel{
		ServiceType:   "CFC",
		ProductType:   "postpay",
		Count:         1,
		Key:           "cfc",
		Flavor:        []api.Flavor{},        // 产品的配置, 将影响订单的价格。默认为空
		PaymentMethod: []api.PaymentMethod{}, //默认为空
	}
	orderDetail := api.CreateOrderRequest{
		Region:        region,
		OrderType:     "NEW",
		PaymentMethod: []api.PaymentMethod{},
	}
	orderDetail.Items = append(orderDetail.Items, item)
	body := new(bytes.Buffer)
	err = json.NewEncoder(body).Encode(orderDetail)
	if err != nil {
		return err, nil
	}
	req, err := http.NewRequest(http.MethodPost, billingCatalogEndpoint+"/order", body)
	rsp, err := HttpRequestForUser(credential, req, requestId)
	if err != nil {
		return err, nil
	}
	defer rsp.Body.Close()
	if rsp.StatusCode != http.StatusOK {
		resp := &api.OrderResp{}
		json.NewDecoder(rsp.Body).Decode(resp)
		var err error
		if resp.Code == "InsufficientBalance" {
			err = errors.New("您的账户存在欠款，暂时无法创建函数，请您充值后重试。")
		} else {
		}
		return err, nil
	}
	resp := new(api.NewOrderResponce)
	json.NewDecoder(rsp.Body).Decode(resp)
	if resp.OrderId == "" {
		err := errors.New("create order failed. orderId is nil")
		return err, nil
	}
	return nil, resp
}

func (b *Order) CreatePackageResource(billingPackageEndpoint string, billingEndpoint string, iamClient iam.ClientInterface, requestId string, orderId string) ([]api.PackageInfoResponse, error) {
	//查询订单，获取order作为创建用量包参数
	body := new(bytes.Buffer)
	req, err := http.NewRequest(http.MethodGet, billingEndpoint+"/orders/"+orderId, body)
	rsp, err := HttpRequestForService(req, requestId, iamClient)
	if err != nil {
		logs.Errorf("err:%v ", err)
		return nil, err
	}
	defer rsp.Body.Close()
	if rsp.StatusCode != http.StatusOK {
		resp := &api.OrderResp{}
		json.NewDecoder(rsp.Body).Decode(resp)
		logs.Errorf("err:%v ", err)
		return nil, err
	}
	//创建用量包
	req, err = http.NewRequest(http.MethodPost, billingPackageEndpoint+"/package/create", rsp.Body)
	rsp, err = HttpRequestForService(req, requestId, iamClient)
	if rsp.StatusCode != http.StatusOK {
		resp := &api.OrderResp{}
		err = json.NewDecoder(rsp.Body).Decode(resp)
		if err != nil {
			return nil, err
		}
		logs.Errorf("err:%v", err)
		return nil, err
	}
	var jsonObj []interface{}
	rspBody, _ := ioutil.ReadAll(rsp.Body)
	json.Unmarshal(rspBody, &jsonObj)
	packageInfoResponse := []api.PackageInfoResponse{}
	for i := 0; i < len(jsonObj); i++ {
		ans := jsonObj[i].(map[string]interface{})
		var status string
		var packageName string
		for k, v := range ans {
			switch v := v.(type) {
			case string:
				if k == "status" {
					status = v
				} else if k == "packageName" {
					packageName = v
				}
			}
		}
		packageInfoResponse = append(packageInfoResponse, api.PackageInfoResponse{Status: status, PackageName: packageName})
	}
	return packageInfoResponse, nil
}

func (b *Order) CreatePackageOrder(couponId string, iamClient iam.ClientInterface, requestId string, region string, billingCatalogEndpoint string, accountId string, count int, flavorInfo api.FlavorInfo) (error, string) {
	// 创建量包时添加参数校验，包括量包类型的名称(typeName)、量包类型取值(typeValue)、资源包规格名称(specName)、资源包规格取值(specValue)、量包周期(timeUnit)
	if err := packageParameterCheck(flavorInfo); err != nil {
		return err, ""
	}

	if iamClient == nil {
		err := errors.New("iam client is not on service")
		return err, ""
	}
	var stsRoleName = api.StsRoleName
	if stsRoleName == "" {
		err := errors.New("stsRoleName is nill")
		return err, ""
	}
	credential, err := iamClient.AssumeRole(accountId, stsRoleName, requestId, 3600)
	if err != nil {
		err := errors.New(fmt.Sprintf("StsProcessor: assumeRole failed.err:%s", err.Error()))
		return err, ""
	}
	flavorRes := []api.Flavor{}
	flavor1 := api.Flavor{
		Name:  flavorInfo.TypeName,
		Value: flavorInfo.TypeValue,
		Scale: flavorInfo.TypeScale}
	flavorRes = append(flavorRes, flavor1)
	flavor2 := api.Flavor{
		Name:  flavorInfo.SpecName,
		Value: flavorInfo.SpecValue,
		Scale: flavorInfo.Specscale}
	flavorRes = append(flavorRes, flavor2)

	// 免费资源包订单下的是分段定额包
	if flavorInfo.PackageType == "free" {
		freeFlavors := []api.Flavor{
			{
				Name:  "packageProperty", // 量包属性名称，seg表示分段量包
				Value: "seg",
				Scale: 1,
			},
			{
				Name:  "packageUnit", // 量包时间分段单位
				Value: "month",
				Scale: 1,
			},
			{
				Name:  "packageCount", // 量包分段数量
				Value: "3",
				Scale: 1,
			},
		}
		switch flavorInfo.TypeValue {
		case FunctionCallCountFreeType:
			flavor := []api.Flavor{
				{
					Name:  "deductPolicy", // 分段量包抵扣策略
					Value: FunctionCallCountFreeType,
				},
				{
					Name:  "capacityPerUnit", // 分段量包容量大小
					Value: FunctionCallCountFreeValue,
					Scale: 1,
				},
			}
			freeFlavors = append(freeFlavors, flavor...)
		case PublicDataTransferFreeType:
			flavor := []api.Flavor{
				{
					Name:  "deductPolicy", // 分段量包抵扣策略
					Value: PublicDataTransferFreeType,
				},
				{
					Name:  "capacityPerUnit", // 分段量包容量大小
					Value: PublicDataTransferFreeValue,
					Scale: 1,
				},
			}

			freeFlavors = append(freeFlavors, flavor...)
		case FunctionRunTimeCountFreeType:
			flavor := []api.Flavor{
				{
					Name:  "deductPolicy", // 分段量包抵扣策略
					Value: FunctionRunTimeCountFreeType,
				},
				{
					Name:  "capacityPerUnit", // 分段量包容量大小
					Value: FunctionRunTimeCountFreeValue,
					Scale: 1,
				},
			}
			freeFlavors = append(freeFlavors, flavor...)
		}
		flavorRes = append(flavorRes, freeFlavors...)
	}

	paymentMethodTemp := api.PaymentMethod{}
	paymentMethod := []api.PaymentMethod{}
	//id不为空串，说明使用代金劵
	if couponId != "" {
		paymentMethodTemp.Type = "coupon"
		paymentMethodTemp.Values = []string{couponId}
		paymentMethod = []api.PaymentMethod{paymentMethodTemp}
	}

	// 资源包周期，默认为YEAR
	timeUnit := "YEAR"
	itemTime := 1
	if flavorInfo.TimeUnit != "" {
		timeUnit = flavorInfo.TimeUnit
	}

	// 免费资源包的购买时长为3
	if flavorInfo.PackageType == "free" {
		itemTime = 3
	}

	// Extra字段添加免费资源包和自动续费标识，后续可能会用到
	extra := map[string]interface{}{
		"packageType": flavorInfo.PackageType,
		"autoRenew":   flavorInfo.AutoRenew,
	}
	extraJson, _ := json.Marshal(extra)

	item := api.OrderPackageItemModel{
		ServiceType:    "CFC",
		ProductType:    "prepay",
		SubProductType: "package",
		Count:          count,
		Key:            "cfc",
		Time:           itemTime,
		TimeUnit:       timeUnit,
		Extra:          string(extraJson),
		Flavor:         flavorRes,     // 产品的配置
		PaymentMethod:  paymentMethod, //代金劵配置
		PurchaseOrder:  0,
	}
	orderDetail := api.CreatePackageOrderRequest{
		Region:        region,
		OrderType:     "NEW",
		PaymentMethod: []api.PaymentMethod{},
	}
	orderDetail.Items = append(orderDetail.Items, item)
	body := new(bytes.Buffer)
	err = json.NewEncoder(body).Encode(orderDetail)
	if err != nil {
		return err, ""
	}
	//创建订单
	req, err := http.NewRequest(http.MethodPost, billingCatalogEndpoint+"/order", body)
	rsp, err := HttpRequestForUser(credential, req, requestId)
	if err != nil {
		return err, ""
	}
	defer rsp.Body.Close()
	if rsp.StatusCode != http.StatusOK {
		resp := &api.OrderResp{}
		json.NewDecoder(rsp.Body).Decode(resp)
		err := errors.New(fmt.Sprintf("create order failed. requestId: %s, code: %s, message: %s", resp.RequestID, resp.Code, resp.Message))
		logs.Errorf("err:%v ", err)
		return err, ""
	}
	resp := new(api.NewOrderResponce)
	json.NewDecoder(rsp.Body).Decode(resp)
	orderId := resp.OrderId
	if orderId == "" {
		err := errors.New("create order failed. orderId is nil")
		logs.Errorf("err:%v ", err)
		return err, ""
	}

	return nil, orderId
}

// packageParameterCheck 量包参数校验
func packageParameterCheck(flavorInfo api.FlavorInfo) (err error) {
	if flavorInfo.TypeName != PackageTypeName {
		err = errors.New("typeName invalid")
		return
	}
	if flavorInfo.TimeUnit != "" && strings.ToLower(flavorInfo.TimeUnit) != PackageTimeUnitMonth && strings.ToLower(flavorInfo.TimeUnit) != PackageTimeUnitYear {
		err = errors.New("timeUnit invalid")
		return
	}

	validSpecValue := func(specValue string, specValues []string) bool {
		for _, s := range specValues {
			if specValue == s {
				return true
			}
		}
		return false
	}

	switch flavorInfo.TypeValue {
	case FunctionCallCountType:
		if flavorInfo.SpecName != PackageSpecNameCount {
			err = errors.New("FunctionCallCount: spec name invalid")
			return
		}
		if !validSpecValue(flavorInfo.SpecValue, FunctionCallCountValues) {
			err = errors.New("FunctionCallCount: spec value invalid")
			return
		}
	case FunctionRunTimeCountType:
		if flavorInfo.SpecName != PackageSpecNameCount {
			err = errors.New("FunctionRunTimeCount: spec name invalid")
			return
		}
		if !validSpecValue(flavorInfo.SpecValue, FunctionRunTimeCountValues) {
			err = errors.New("FunctionRunTimeCount: spec value invalid")
			return
		}
	case PublicDataTransferType:
		if flavorInfo.SpecName != PackageSpecNameCapacity {
			err = errors.New("PublicDataTransfer: spec name invalid")
			return
		}
		if !validSpecValue(flavorInfo.SpecValue, PublicDataTransferValues) {
			err = errors.New("PublicDataTransfer: spec value invalid")
			return
		}
	case FunctionCallCountFreeType:
		if flavorInfo.SpecName != PackageSpecNameCount {
			err = errors.New("FunctionCallCountFree: spec name invalid")
			return
		}
		if flavorInfo.SpecValue != FunctionCallCountFreeValueTotal {
			err = errors.New("FunctionCallCountFree: spec value invalid")
			return
		}
	case FunctionRunTimeCountFreeType:
		if flavorInfo.SpecName != PackageSpecNameCount {
			err = errors.New("FunctionRuntimeCountFree: spec name invalid")
			return
		}
		if flavorInfo.SpecValue != FunctionRunTimeCountFreeValueTotal {
			err = errors.New("FunctionRuntimeCountFree: spec value invalid")
			return
		}
	case PublicDataTransferFreeType:
		if flavorInfo.SpecName != PackageSpecNameCapacity {
			err = errors.New("PublicDataTransferFree: spec name invalid")
			return
		}
		if flavorInfo.SpecValue != PublicDataTransferFreeValueTotal {
			err = errors.New("PublicDataTransferFree: spec value invalid")
			return
		}
	default:
		err = errors.New("type value invalid")
	}

	return
}

func (b *Order) UpdateOrderToFail(orderId string, requestId string, billingEndpoint string, iamClient iam.ClientInterface) error {
	updateOrderResquest := &api.UpdateOrderResquest{
		Status: "CREATE_FAILED",
	}
	body := new(bytes.Buffer)
	err := json.NewEncoder(body).Encode(updateOrderResquest)
	if err != nil {
		return err
	}
	req, err := http.NewRequest(http.MethodPut, billingEndpoint+"/orders/"+orderId, body)
	rsp, err := HttpRequestForService(req, requestId, iamClient)
	if err != nil {
		return err
	}
	defer rsp.Body.Close()
	if rsp.StatusCode != http.StatusOK {
		resp := &api.OrderResp{}
		json.NewDecoder(rsp.Body).Decode(resp)
		err = errors.New(fmt.Sprintf("update order to CREATE_FAILED faild. requestId: %s, code: %s, message: %s", resp.RequestID, resp.Code, resp.Message))
		return err
	}
	return nil
}

func HttpRequestForUser(credential *sts_credential.StsCredential, req *http.Request, requestId string) (*http.Response, error) {
	req.Header.Set("content-type", "application/json")
	req.Header.Set("x-bce-security-token", credential.SessionToken)
	req.Header.Set("x-bce-sub-account-id", credential.UserId)
	req.Header.Set("x-bce-date", time.Now().Format(time.RFC3339))
	req.Header.Set("x-bce-request-id", requestId)
	req.Header.Set("Host", req.Host)
	auth := auth.NewBceAuth(credential.AccessKeyId, credential.AccessKeySecret)
	signature := auth.NewSigner().
		Method(req.Method).
		Path(req.URL.Path).
		Headers(req.Header).
		WithSignedHeader().
		Expire(3600).
		GetSign()
	req.Header.Set("Authorization", signature)
	client := &http.Client{}
	rsp, err := client.Do(req)
	return rsp, err
}

func HttpRequestForService(req *http.Request, requestId string, iamClient iam.ClientInterface) (*http.Response, error) {
	if iamClient == nil {
		err := errors.New("global iam client is not on service")
		return nil, err
	}
	req.Header.Set("content-type", "application/json")
	req.Header.Set("x-bce-date", time.Now().Format(time.RFC3339))
	req.Header.Set("x-bce-request-id", requestId)
	req.Header.Set("Host", req.Host)
	auth := auth.NewBceAuth(iamClient.AccessKey(), iamClient.SecretKey())
	signature := auth.NewSigner().
		Params(req.URL.Query()).
		Method(req.Method).
		Path(req.URL.Path).
		Headers(req.Header).
		WithSignedHeader().
		Expire(3600).
		GetSign()
	req.Header.Set("Authorization", signature)
	client := &http.Client{}
	rsp, err := client.Do(req)
	return rsp, err
}

// CreateAutoRenew 创建自动续费
func (b *Order) CreateAutoRenew(requestId string, autoRenewReq *api.CreateAutoRenewRequest, billingOrderRenewEndpoint string, iamClient iam.ClientInterface) error {
	if iamClient == nil {
		err := errors.New("iam client is not on service")
		return err
	}
	var stsRoleName = api.StsRoleName
	if stsRoleName == "" {
		err := errors.New("stsRoleName is nil")
		return err
	}
	credential, err := iamClient.AssumeRole(autoRenewReq.AccountId, stsRoleName, requestId, 3600)
	if err != nil {
		err = errors.New(fmt.Sprintf("StsProcessor: assumeRole failed.err:%s", err.Error()))
		return err
	}
	body := new(bytes.Buffer)
	err = json.NewEncoder(body).Encode(autoRenewReq)
	if err != nil {
		return err
	}

	req, err := http.NewRequest(http.MethodPost, billingOrderRenewEndpoint+"/v1/auto-renew/rule", body)
	rsp, err := HttpRequestForUser(credential, req, requestId)
	if err != nil {
		logs.Errorf("err:%v ", err)
		return err
	}
	defer rsp.Body.Close()

	if rsp.StatusCode != http.StatusOK {
		resp := &api.OrderResp{}
		json.NewDecoder(rsp.Body).Decode(resp)
		err := errors.New(fmt.Sprintf("create autoRenew rule failed. requestId: %s, code: %s, message: %s", resp.RequestID, resp.Code, resp.Message))
		logs.Errorf("err:%v ", err)
		return err
	}
	return nil
}

// QueryAutoRenew 查询自动续费规则
func (b *Order) QueryAutoRenew(requestId string, accountId string, serviceId string, region string, billingOrderRenewEndpoint string, iamClient iam.ClientInterface) ([]api.QueryAutoRenewResponse, error) {
	body := new(bytes.Buffer)
	req, _ := http.NewRequest(http.MethodGet, billingOrderRenewEndpoint+"/v1/auto-renew/rule", body)
	queryParams := req.URL.Query()
	queryParams.Add("serviceType", "CFC")
	queryParams.Add("accountId", accountId)
	queryParams.Add("region", region)
	queryParams.Add("serviceId", serviceId)
	req.URL.RawQuery = queryParams.Encode()
	rsp, err := HttpRequestForService(req, requestId, iamClient)
	if err != nil {
		return nil, err
	}
	defer rsp.Body.Close()
	if rsp.StatusCode != http.StatusOK {
		resp := &api.OrderResp{}
		json.NewDecoder(rsp.Body).Decode(resp)
		err := errors.New(fmt.Sprintf("query autoRenew rule failed. requestId: %s, code: %s, message: %s", resp.RequestID, resp.Code, resp.Message))
		logs.Errorf("err:%v ", err)
		return nil, err
	}

	var resp []api.QueryAutoRenewResponse
	json.NewDecoder(rsp.Body).Decode(&resp)
	return resp, nil
}

func (b *Order) DeleteAutoRenew(accountId string, requestId string, ruleId string, billingOrderRenewEndpoint string, iamClient iam.ClientInterface) error {
	if iamClient == nil {
		err := errors.New("iam client is not on service")
		return err
	}
	var stsRoleName = api.StsRoleName
	if stsRoleName == "" {
		err := errors.New("stsRoleName is nil")
		return err
	}
	credential, err := iamClient.AssumeRole(accountId, stsRoleName, requestId, 3600)
	if err != nil {
		err = errors.New(fmt.Sprintf("StsProcessor: assumeRole failed.err:%s", err.Error()))
		return err
	}
	body := new(bytes.Buffer)
	req, _ := http.NewRequest(http.MethodDelete, billingOrderRenewEndpoint+"/v1/auto-renew/rule/"+ruleId, body)
	rsp, err := HttpRequestForUser(credential, req, requestId)
	if err != nil {
		return err
	}

	defer rsp.Body.Close()
	if rsp.StatusCode != http.StatusOK {
		resp := &api.OrderResp{}
		json.NewDecoder(rsp.Body).Decode(resp)
		err := errors.New(fmt.Sprintf("delete autoRenew rule failed. requestId: %s, code: %s, message: %s", resp.RequestID, resp.Code, resp.Message))
		logs.Errorf("err:%v ", err)
		return err
	}
	return nil
}

func (b *Order) BatchQueryAutoRenew(requestId string, resources []api.AutoRenewResource, billingOrderRenewEndpoint string, iamClient iam.ClientInterface) ([]api.QueryAutoRenewResponse, error) {
	body := new(bytes.Buffer)
	err := json.NewEncoder(body).Encode(resources)
	if err != nil {
		return nil, err
	}
	req, _ := http.NewRequest(http.MethodPost, billingOrderRenewEndpoint+"/v1/auto-renew/rule/batch/status", body)
	rsp, err := HttpRequestForService(req, requestId, iamClient)
	if err != nil {
		return nil, err
	}

	defer rsp.Body.Close()
	if rsp.StatusCode != http.StatusOK {
		resp := &api.OrderResp{}
		json.NewDecoder(rsp.Body).Decode(resp)
		err := errors.New(fmt.Sprintf("batch query autoRenew rule failed. requestId: %s, code: %s, message: %s", resp.RequestID, resp.Code, resp.Message))
		logs.Errorf("err:%v ", err)
		return nil, err
	}

	var resp []api.QueryAutoRenewResponse
	json.NewDecoder(rsp.Body).Decode(&resp)
	return resp, nil
}
