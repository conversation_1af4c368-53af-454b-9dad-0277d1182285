package dbengine

import (
	"github.com/jinzhu/gorm"
	_ "github.com/jinzhu/gorm/dialects/mysql"
	"icode.baidu.com/baidu/faas/kun/pkg/rpc/db"
	"icode.baidu.com/baidu/faas/kun/pkg/util/logs"
)

var Engine *gorm.DB
var DbConf *db.DbConfig

func DBInstance() *gorm.DB {
	var err error
	if Engine == nil {
		if Engine, err = db.NewMysqlEngine(DbConf); err != nil {
			logs.Errorf("err happend when NewDbEngine, err: %s, db conf: %v", err.Error(), DbConf)
		}
	}
	return Engine
}

func DBTransaction() *gorm.DB {
	return DBInstance().Begin()
}
