package dbengine

import (
	"icode.baidu.com/baidu/faas/kun/pkg/etcd"
	"icode.baidu.com/baidu/faas/kun/pkg/util/logs"
)

var (
	EtcdConf       *etcd.Options
	EtcdNotifier   *etcd.Notifier
)

func NewEtcdClient(opt *etcd.Options, enableEtcd bool) etcd.EtcdInterface{
	if enableEtcd {
		EtcdConf = opt
		EtcdClient := etcd.NewClient()
		err := EtcdClient.StartClient(opt)
		if err != nil {
			logs.Exitf("start etcd client error: %v", err)
		}

		EtcdNotifier, err = etcd.NewNotifierWithClient(EtcdClient, "")
		if err != nil {
			logs.Exitf("start etcd notifier error: %v", err)
		}
		return EtcdClient
	} else {
		return &etcd.Client{}
	}
}

func NotifyEtcdWhenChange(etcdPrefix, key string, msg interface{}) {
	if msg == nil || EtcdNotifier == nil {
		return
	}
	err := EtcdNotifier.Notify(etcdPrefix, key, msg)
	if err != nil {
		logs.Errorf("functionCache Notifier err(%v)", err)
		return
	}
	logs.V(6).Infof("NotifyEtcdWhenChange etcdPrefix (%s) key(%s)", etcdPrefix, key)
}
