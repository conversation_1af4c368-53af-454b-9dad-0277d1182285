package service

import (
	"crypto/sha256"
	"fmt"
	"io/ioutil"
	"os"
	"path/filepath"
	"strconv"
	"strings"

	bosSdk "github.com/baidubce/bce-sdk-go/services/bos"
	"github.com/google/uuid"
	"github.com/jinzhu/gorm"

	"icode.baidu.com/baidu/faas/kun/pkg/api"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/code"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/dao"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/dbengine"
	apiErr "icode.baidu.com/baidu/faas/kun/pkg/apiserver/error"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/global"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/models"
	"icode.baidu.com/baidu/faas/kun/pkg/bce/bos"
	bceBrn "icode.baidu.com/baidu/faas/kun/pkg/bce/brn"
	kunErr "icode.baidu.com/baidu/faas/kun/pkg/error"
	"icode.baidu.com/baidu/faas/kun/pkg/server"
	"icode.baidu.com/baidu/faas/kun/pkg/util/base64"
	"icode.baidu.com/baidu/faas/kun/pkg/util/zip"
)

// PublishLayer 发布一个层，包括创建层、上传代码和生成squashfs。如果是从迁移中心来的层，则需要从CFC code storage中获取层代码。
// 参数：
//
//	uid (string) - 用户ID
//	input (*api.PublishLayerVersionInput) - 发布层请求参数结构体指针，包括兼容运行时列表、描述信息、许可证信息、层名称等字段
//	c (server.Context) - 服务器上下文对象，包含日志记录器等功能
//
// 返回值：
//
//	output (api.PublishLayerVersionOutput) - 发布层版本输出结构体，包括兼容运行时列表、代码SHA256校验和、代码大小、位置、创建日期、描述信息、层BRN、层版本BRN、许可证信息、版本号等字段
//	error (error) - 错误信息，如果没有错误则为nil
func PublishLayer(uid string, input *api.PublishLayerVersionInput, c *server.Context) (*api.PublishLayerVersionOutput, error) {
	var byteArray []byte

	layer := &api.Layer{
		Uid:                  uid,
		CodeId:               uuid.New().String(),
		CompatibleRuntimeStr: strings.Join(input.CompatibleRuntimes, ","),
		Description:          input.Description,
		LicenseInfo:          input.LicenseInfo,
		LayerName:            input.LayerName,
	}

	// 版本号处理
	if input.SourceTag == api.CFCMigrationSourceTag {
		layer.Version = input.Version
	} else {
		version, err := dao.GetLayerPreVersion(layer)
		if err != nil {
			return nil, kunErr.NewServiceException("get layer version failed", err)
		}
		layer.Version = version
	}

	layerBrn := bceBrn.GenerateLayerBrn(global.AC.Config.Region, uid, input.LayerName)
	layer.Brn = layerBrn.String()

	// 从迁移中心来的layer
	if len(input.Content.ZipFileBytes) > 0 {
		if input.Content.BosObject != "" || input.Content.BosBucket != "" {
			return nil, apiErr.NewTooManyParametersException("Please do not provide other parameters when providing a ZipFile", nil)
		}
		byteArray = input.Content.ZipFileBytes
	} else {
		if input.Content.BosObject == "" || input.Content.BosBucket == "" {
			return nil, apiErr.NewMissingParametersException("Invalid Layer Version content specified", nil)
		}

		if input.SourceTag == api.CFCMigrationSourceTag {
			// 迁移中心来的layer, 从CFC code storage中取得layer code
			codeConfig := global.AC.Config.CodeConfiguration
			cfcBosClient, err := bosSdk.NewClient(codeConfig.AccessKey, codeConfig.SecretAccessKey, codeConfig.MigrationSericeBosEndpoint)
			if err != nil {
				return nil, apiErr.NewInitBosClientException("", "Init CFC BOS client failed from cfc-migration", err)
			}
			meta, err := cfcBosClient.GetObjectMeta(input.Content.BosBucket, input.Content.BosObject)
			if err != nil {
				return nil, apiErr.NewInitBosClientException("", "Get BOS object meta failed from cfc-migration", err)
			}
			if meta.ContentLength > int64(api.DefaultLayerCodeSizeLimitZipped) {
				return nil, apiErr.NewCodeStorageExceededException("zipped code size too large from cfc-migration", err)
			}
			object, err := cfcBosClient.GetObject(input.Content.BosBucket, input.Content.BosObject, nil)
			if err != nil {
				return nil, apiErr.NewObjectNotFoundException("code not found from cfc-migration", err)
			}
			if err != nil {
				return nil, apiErr.NewObjectNotFoundException("get code object error from cfc-migration", err)
			}
			byteArray, err = ioutil.ReadAll(object.Body)
			if err != nil {
				return nil, apiErr.NewObjectNotFoundException("", nil)
			}
		} else {
			userBosClient, err := bos.NewClient(c)
			if err != nil {
				return nil, apiErr.NewInitBosClientException("", "Init User BOS client failed", nil)
			}
			meta, err := userBosClient.GetObjectMeta(input.Content.BosBucket, input.Content.BosObject)
			if err != nil {
				return nil, apiErr.NewInitBosClientException("", "Get BOS object meta failed", nil)
			}
			if meta.ContentLength > int64(api.DefaultLayerCodeSizeLimitZipped) {
				return nil, apiErr.NewCodeStorageExceededException("zipped code size too large", nil)
			}
			object, err := userBosClient.GetObject(input.Content.BosBucket, input.Content.BosObject, nil)
			if err != nil {
				return nil, apiErr.NewObjectNotFoundException("", nil)
			}
			byteArray, err = ioutil.ReadAll(object.Body)
			if err != nil {
				return nil, apiErr.NewObjectNotFoundException("", nil)
			}
		}
	}

	// check code size
	err := models.CheckCodeSizeLayer("create", layer, byteArray)
	if err != nil {
		return nil, err
	}
	// upload layer zip to BOS
	layer.CodeSha256, layer.CodeSize, layer.CodeId, err = global.AC.Clients.Code.FaasUploadLayerCode(byteArray, layer.LayerName, layer.Uid)
	if err != nil {
		return nil, kunErr.NewServiceException("upload code failed", err)
	}

	squashFsKey := code.GetLayerSquashFsKey(uid, layer.LayerName, layer.CodeId)
	layer.SquashFsSha256, err = global.AC.Clients.Code.FaasUploadLayerSqfs(byteArray, int32(layer.CodeSize), squashFsKey)
	if err != nil {
		// 删除已经上传的layer
		removeErr := global.AC.Clients.Code.FaasRemoveLayerCode(layer.LayerName, layer.Uid, layer.CodeId)
		if c != nil {
			c.Logger().Errorf("remove layer code error %v", removeErr)
		}
		e := models.OptimizeErrorCode("upload layer squashfs failed", err)
		return nil, e
	}
	err = dao.CreateLayer(layer)
	if err != nil {
		// 删除已经上传的layer
		removeErr := global.AC.Clients.Code.FaasRemoveLayerCode(layer.LayerName, layer.Uid, layer.CodeId)
		if c != nil {
			c.Logger().Errorf("remove layer code error %v", removeErr)
		}
		return nil, err
	}
	objectKey := global.AC.Clients.Code.FaasLayerDownloadUrl(layer.LayerName, layer.Uid, layer.CodeId)

	output := &api.PublishLayerVersionOutput{
		CompatibleRuntimes: input.CompatibleRuntimes,
		Content: &api.LayerVersionContentOutput{
			CodeSha256: layer.CodeSha256,
			CodeSize:   layer.CodeSize,
			Location:   objectKey,
		},
		CreatedDate:     layer.CreatedAt.String(),
		Description:     layer.Description,
		LayerBrn:        layer.Brn,
		LayerVersionBrn: bceBrn.GenerateLayerVersionBrn(global.AC.Config.Region, uid, input.LayerName, layer.Version).String(),
		LicenseInfo:     layer.LicenseInfo,
		Version:         layer.Version,
	}
	return output, nil
}

// 通用获取layers
func listLayers(layer *api.Layer, input *api.ListCondition, query map[string]string, getLatestVersion bool) (*api.ListLayersReturn, error) {

	var (
		db            *gorm.DB
		count         int64
		layerResSlice = make([]*api.Layer, 0)
	)

	output := &api.ListLayersReturn{
		Layers: make([]*api.Layer, 0),
	}

	db = dbengine.DBInstance().Unscoped().Scopes(dao.ScopeRudDeletedAt).Model(api.Layer{}).Where(layer)
	if getLatestVersion {
		db = db.Group("uid, layer_name")
	}
	for k, v := range query {
		db = db.Where(k, v)
	}
	db.Count(&count)

	if input.PageNo != 0 || input.PageSize != 0 {
		db = db.Offset(int((input.PageNo - 1) * input.PageSize)).Limit(int(input.PageSize))
	} else {
		if input.Marker != 0 {
			db = db.Offset(input.Marker)
		}
		if input.MaxItems != 0 {
			db = db.Limit(input.MaxItems)
		}
	}

	if count == 0 {
		return output, nil
	}

	err := db.Find(&layerResSlice).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return output, apiErr.NewObjectNotFoundException("", err)
		}
		return nil, err
	}
	output.Total = count
	output.NextMarker = strconv.FormatInt(input.MaxItems+input.Marker, 10)
	if getLatestVersion {
		for k, v := range layerResSlice {
			latestLayer, err := dao.GetLatestLayer(v)
			if err != nil {
				return nil, err
			}
			layerResSlice[k] = latestLayer
		}
	}
	output.Layers = layerResSlice
	return output, nil
}

// 删除layer
func DeleteLayerVersion(layer *api.Layer) error {
	err := dao.FindOneLayer(layer)
	if err != nil {
		return err
	}
	// 删除数据
	err = dao.DeleteLayer(dbengine.DBInstance(), layer)
	if err != nil {
		return err
	}
	// 删除存储
	_ = global.AC.Clients.Code.FaasRemoveLayerCode(layer.Uid, layer.LayerName, layer.CodeId)
	return nil
}

// 删除layer
func DeleteLayer(layer *api.Layer) error {
	layers, err := dao.FindLayers(layer)
	if err != nil {
		return err
	}
	// 删除数据
	err = dao.DeleteLayer(dbengine.DBInstance(), layer)
	if err != nil {
		return err
	}
	// 删除存储
	for _, v := range *layers {
		_ = global.AC.Clients.Code.FaasRemoveLayerCode(v.Uid, v.LayerName, v.CodeId)
	}
	return nil
}

// 通过brn 获取layer
func GetLayerVersionByBrn(uid string, brn bceBrn.LayerBRN) (*api.GetLayerVersionOutput, error) {
	layer := &api.Layer{
		Uid:       uid,
		LayerName: brn.LayerName,
		Version:   brn.LayerVersion,
	}

	err := dao.FindOneLayer(layer)
	if err != nil {
		return nil, err
	}

	layer.Complement()

	objectKey := global.AC.Clients.Code.FaasLayerDownloadUrl(layer.LayerName, layer.Uid, layer.CodeId)

	output := &api.GetLayerVersionOutput{
		CompatibleRuntimes: layer.CompatibleRuntime,
		Content: &api.LayerVersionContentOutput{
			CodeSha256: layer.CodeSha256,
			CodeSize:   layer.CodeSize,
			Location:   objectKey,
		},
		CreatedDate:     layer.CreatedAt.String(),
		Description:     layer.Description,
		LayerBrn:        layer.Brn,
		LayerVersionBrn: brn.String(),
		LicenseInfo:     layer.LicenseInfo,
		Version:         layer.Version,
	}

	return output, nil

}

// 获取layer列表
func ListLayers(uid string, input *api.ListLayersInput, getVendedLayers bool) (*api.ListLayersOutput, error) {

	output := &api.ListLayersOutput{
		Layers:   make([]*api.LayersListItem, 0),
		PageSize: input.PageSize,
		PageNo:   input.PageNo,
	}

	layer := &api.Layer{
		Uid: uid,
	}

	// 获取官方(IsVended=true)的layer
	if getVendedLayers {
		layer.Uid = ""
		layer.IsVended = &getVendedLayers
		layer.Enabled = &getVendedLayers
	} else {
		// 用户获取自己的层列表时，排除官方层
		isVended := false
		layer.IsVended = &isVended
	}

	query := make(map[string]string)
	if input.CompatibleRuntime != "" {
		query["compatible_runtime like ?"] = "%" + input.CompatibleRuntime + "%"
	}

	layersOutPut, err := listLayers(layer, input.ListCondition, query, true)
	if err != nil {
		return nil, err
	}

	for _, v := range layersOutPut.Layers {
		v.Complement()
		layersListItem := &api.LayersListItem{
			LatestMatchingVersion: &api.LayerVersionsListItem{
				CompatibleRuntimes: v.CompatibleRuntime,
				CreatedDate:        v.CreatedAt.String(),
				Description:        v.Description,
				LayerVersionBrn:    bceBrn.GenerateLayerVersionBrn(global.AC.Config.Region, v.Uid, v.LayerName, v.Version).String(),
				LicenseInfo:        v.LicenseInfo,
				Version:            v.Version,
			},
			LayerBrn:  v.Brn,
			LayerName: v.LayerName,
		}
		output.Layers = append(output.Layers, layersListItem)
	}
	output.NextMarker = layersOutPut.NextMarker
	output.Total = layersOutPut.Total
	return output, nil
}

// 获取layer version 列表，指定了layerName的
func ListLayerVersions(uid string, input *api.ListLayerVersionsInput, getVendedLayers bool) (*api.ListLayerVersionsOutput, error) {

	output := &api.ListLayerVersionsOutput{
		LayerVersions: make([]*api.LayerVersionsListItem, 0),
		PageNo:        input.PageNo,
		PageSize:      input.PageSize,
	}

	layer := &api.Layer{
		Uid:       uid,
		LayerName: input.LayerName,
	}
	if getVendedLayers {
		layer.Uid = ""
		layer.IsVended = &getVendedLayers
		layer.Enabled = &getVendedLayers
	} else {
		// 用户获取自己的层版本列表时，排除官方层
		isVended := false
		layer.IsVended = &isVended
	}

	query := make(map[string]string)
	if input.CompatibleRuntime != "" {
		query["compatible_runtime like ?"] = "%" + input.CompatibleRuntime + "%"
	}

	layersOutPut, err := listLayers(layer, input.ListCondition, query, false)
	if err != nil {
		return nil, err
	}

	for _, v := range layersOutPut.Layers {
		v.Complement()
		layersListItem := &api.LayerVersionsListItem{
			CompatibleRuntimes: v.CompatibleRuntime,
			CreatedDate:        v.CreatedAt.String(),
			Description:        v.Description,
			LayerVersionBrn:    bceBrn.GenerateLayerVersionBrn(global.AC.Config.Region, v.Uid, v.LayerName, v.Version).String(),
			LicenseInfo:        v.LicenseInfo,
			Version:            v.Version,
		}
		output.LayerVersions = append(output.LayerVersions, layersListItem)
	}
	if len(output.LayerVersions) == 0 {
		return nil, apiErr.NewObjectNotFoundException("", nil)
	}
	output.NextMarker = layersOutPut.NextMarker
	output.Total = layersOutPut.Total
	return output, nil
}

func ValidateCompatibleRuntimeList(runtimeList []string) error {
	err := GetCompatibleRuntimesError()
	length := len(runtimeList)
	if length > 5 {
		return err
	}
	for k, v := range runtimeList {
		if _, ok := global.AC.Cache.RuntimeCache[v]; !ok {
			return err
		}
		for i := k + 1; i < length; i++ {
			if v == runtimeList[i] {
				return err
			}
		}
	}
	return nil
}

func GetCompatibleRuntimesError() error {
	return kunErr.NewInvalidParameterValueException(fmt.Sprintf("CompatibleRuntimes Member must satisfy enum value set: %v", getRuntimesList()), nil)
}

func ValidateCompatibleRuntime(str string) error {
	if str == "" {
		return nil
	}
	if _, ok := global.AC.Cache.RuntimeCache[str]; !ok {
		return kunErr.NewInvalidParameterValueException(fmt.Sprintf("CompatibleRuntim Member must satisfy enum value set: %v", getRuntimesList()), nil)
	}
	return nil
}

func getRuntimesList() []string {
	runtimes := make([]string, 0)
	for _, v := range global.AC.Cache.RuntimeCache {
		runtimes = append(runtimes, v.Name)
	}
	return runtimes
}

// TODO function 代码的解压大小加入计算
func CheckFunctionLayers(uid string, functionCodeSize int64, layerInterfaceList []*api.LayerSample) ([]*api.LayerSample, []string, error) {
	outLayerSamples := make([]*api.LayerSample, 0)
	outLayerStrList := make([]string, 0)
	totalSize := functionCodeSize
	length := len(layerInterfaceList)
	if length == 0 {
		return nil, nil, nil
	}
	if length > 5 {
		return nil, nil, kunErr.NewInvalidParameterValueException("Cannot reference more than 5 layers.", nil)
	}

	layerBrnList := make([]bceBrn.LayerBRN, 0)
	for _, v := range layerInterfaceList {
		layerBrn, err := bceBrn.ParseLayerBrn(v.Brn)
		if err != nil {
			return nil, nil, kunErr.NewInvalidParameterValueException(fmt.Sprintf("layer %s invalid", v.Brn), nil)
		}
		layerBrnList = append(layerBrnList, layerBrn)
	}

	for k, v := range layerBrnList {
		for i := k + 1; i < length; i++ {
			if v.LayerName == layerBrnList[i].LayerName {
				err := kunErr.NewInvalidParameterValueException(fmt.Sprintf("layer %s %s is repetition", v.String(), layerBrnList[k].String()), nil)
				return nil, nil, err
			}
		}
	}

	for _, layerBrn := range layerBrnList {
		layer := &api.Layer{
			Brn:     layerBrn.ShortBrn(),
			Version: layerBrn.LayerVersion,
		}
		err := dao.FindOneLayer(layer)
		if err != nil {
			return nil, nil, apiErr.NewResourceNotFoundException(fmt.Sprintf("layer %s not found", layerBrn.String()), nil)
		}
		if layer.Uid != uid && !*layer.IsVended {
			return nil, nil, apiErr.NewResourceNotFoundException(fmt.Sprintf("layer %s not found", layerBrn.String()), nil)
		}
		totalSize += layer.UncompressedCodeSize
		if totalSize > int64(api.DefaultCodeSizeLimitUnzipped) {
			return nil, nil, apiErr.NewCodeStorageExceededException("Layers consume more than the available size of 262144000 bytes", nil)
		}
		outLayerSamples = append(outLayerSamples, &api.LayerSample{
			Brn:      layerBrn.String(),
			CodeSize: layer.CodeSize,
			Layer:    layer,
		})
		outLayerStrList = append(outLayerStrList, layerBrn.String())
	}
	return outLayerSamples, outLayerStrList, nil
}

// 制作layer squash fs 镜像
func UploadFunctionLayersSquashFs(uid, functionName string, layers []*api.LayerSample) (codeSha256 *string, err error) {
	// 如果layers只有一个，直接使用layer里的squashfs地址 copy到function的存储
	var layersKey string
	if len(layers) == 1 {
		layer := layers[0].Layer
		layersKey = code.GetFunctionLayersSquashFsKey(uid, functionName, layer.SquashFsSha256)
		sourceResource := code.GetLayerSquashFsKey(layer.Uid, layer.LayerName, layer.CodeId)
		err = global.AC.Clients.Code.BasicCopyObject(layersKey, sourceResource)
		if err != nil {
			return
		}
		codeSha256 = &layer.SquashFsSha256
		return
	}
	// 生成临时目录
	tmpDir, err := ioutil.TempDir("", "apiserver_func_layer_squashfs")
	if err != nil {
		return
	}
	// 删除目录
	defer os.RemoveAll(tmpDir)
	targetDir := filepath.Join(tmpDir, "squashfs_dir")
	targetFileName := filepath.Join(tmpDir, "target.sqfs")

	// 按照顺序下载解压
	for _, layerSample := range layers {
		layerKey := code.GetLayerKey(layerSample.Layer.Uid, layerSample.Layer.LayerName, layerSample.Layer.CodeId)
		// 下载 解压
		byteArray, err := global.AC.Clients.Code.FaasGetBytesFromCodeStorage(layerKey)
		if err != nil {
			return nil, err
		}
		err = zip.UnzipFromBytes(targetDir, byteArray, int64(len(byteArray)))
		if err != nil {
			return nil, err
		}
	}
	// mksquashfs 需要 squashfs-tools
	err = global.AC.Clients.Squash.MkSquashFs(targetDir, targetFileName)
	if err != nil {
		return
	}
	b, err := ioutil.ReadFile(targetFileName)
	if err != nil {
		panic(err)
	}
	hasher := sha256.New()
	hasher.Write(b)
	shaByte := hasher.Sum(nil)
	tmpCodeSha256 := base64.EncodeToString(shaByte)
	layersKey = code.GetFunctionLayersSquashFsKey(uid, functionName, tmpCodeSha256)
	// 3. 上传镜像到bos
	if _, err = global.AC.Clients.Code.Upload(b, layersKey); err != nil {
		return
	}
	codeSha256 = &tmpCodeSha256
	return codeSha256, nil
}
