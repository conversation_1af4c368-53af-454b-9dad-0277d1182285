package service

import (
	"errors"
	"testing"

	sqlmock "github.com/DATA-DOG/go-sqlmock"
	"github.com/jinzhu/gorm"
	"github.com/stretchr/testify/assert"

	"icode.baidu.com/baidu/faas/kun/pkg/api"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/code"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/dbengine"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/global"
	bceBrn "icode.baidu.com/baidu/faas/kun/pkg/bce/brn"
	"icode.baidu.com/baidu/faas/kun/pkg/util/base64"
)

func TestValidateValidateCompatibleRuntimeList(t *testing.T) {
	global.MockAC()

	cases := []struct {
		runtimes []string
		err      error
	}{
		{
			runtimes: []string{"python2"},
			err:      nil,
		},
		{
			runtimes: []string{"python2,python2"},
			err:      GetCompatibleRuntimesError(),
		},
		{
			runtimes: []string{"python22"},
			err:      GetCompatibleRuntimesError(),
		},
		{
			runtimes: []string{"python2,python3,python3,python3,python3,python3,"},
			err:      GetCompatibleRuntimesError(),
		},
	}
	for _, tc := range cases {
		err := ValidateCompatibleRuntimeList(tc.runtimes)
		assert.Equal(t, tc.err, err)
	}
}

func TestListLayers(t *testing.T) {
	global.MockAC()
	code.MockCode()
	var m sqlmock.Sqlmock
	m, dbengine.Engine = global.MockDB()
	cases := []struct {
		name     string
		input    *api.ListLayersInput
		errIsNil bool
		sqlFunc  func()
	}{
		{
			name: "list",
			input: &api.ListLayersInput{
				CompatibleRuntime: "python2",
				ListCondition: &api.ListCondition{
					PageNo:   1,
					PageSize: 10,
				},
			},
			errIsNil: true,
			sqlFunc: func() {
				m.ExpectQuery("count").WillReturnRows(global.GetCount(1))
				m.ExpectQuery("SELECT").WillReturnRows(global.GetRowsLayer(global.GetTestLayers(1)))
				m.ExpectQuery("SELECT").WillReturnRows(global.GetRowsLayer(global.GetTestLayers(1)))
			},
		},
		{
			name: "list count0",
			input: &api.ListLayersInput{
				CompatibleRuntime: "python2",
				ListCondition: &api.ListCondition{
					PageNo:   1,
					PageSize: 10,
				},
			},
			errIsNil: true,
			sqlFunc: func() {
				m.ExpectQuery("count").WillReturnRows(global.GetCount(0))
			},
		},
		{
			name: "list not found",
			input: &api.ListLayersInput{
				CompatibleRuntime: "python2",
				ListCondition: &api.ListCondition{
					PageNo:   1,
					PageSize: 10,
				},
			},
			errIsNil: false,
			sqlFunc: func() {
				m.ExpectQuery("count").WillReturnRows(global.GetCount(1))
				m.ExpectQuery("SELECT").WillReturnError(gorm.ErrRecordNotFound)
			},
		},
		{
			name: "list not found1",
			input: &api.ListLayersInput{
				CompatibleRuntime: "python2",
				ListCondition: &api.ListCondition{
					PageNo:   1,
					PageSize: 10,
				},
			},
			errIsNil: false,
			sqlFunc: func() {
				m.ExpectQuery("count").WillReturnRows(global.GetCount(1))
				m.ExpectQuery("SELECT").WillReturnRows(global.GetRowsLayer(global.GetTestLayers(1)))
				m.ExpectQuery("SELECT").WillReturnError(gorm.ErrRecordNotFound)
			},
		},
	}
	for _, tc := range cases {
		if tc.sqlFunc != nil {
			tc.sqlFunc()
		}
		_, err := ListLayers("uid", tc.input, true)
		var isPass bool
		if tc.errIsNil {
			isPass = assert.Equal(t, nil, err)
		} else {
			isPass = assert.NotEqual(t, nil, err)
		}
		if !isPass {
			t.Logf("tc name err %s", tc.name)
		}
	}
}

// TestListLayersExcludeVended 测试函数，用于在测试用户获取自己的层列表时排除官方层
// 参数：t *testing.T - 单元测试对象指针，表示当前执行的单元测试
func TestListLayersExcludeVended(t *testing.T) {
	global.MockAC()
	code.MockCode()
	var m sqlmock.Sqlmock
	m, dbengine.Engine = global.MockDB()

	// 测试用户获取自己的层列表时排除官方层
	input := &api.ListLayersInput{
		CompatibleRuntime: "python2",
		ListCondition: &api.ListCondition{
			PageNo:   1,
			PageSize: 10,
		},
	}

	// 模拟查询条件包含 is_vended = false
	m.ExpectQuery("count").WillReturnRows(global.GetCount(1))
	m.ExpectQuery("SELECT").WillReturnRows(global.GetRowsLayer(global.GetTestLayers(1)))
	m.ExpectQuery("SELECT").WillReturnRows(global.GetRowsLayer(global.GetTestLayers(1)))

	_, err := ListLayers("uid", input, false) // getVendedLayers = false
	assert.Equal(t, nil, err)

	// 验证SQL查询中包含了 is_vended = false 的条件
	if err := m.ExpectationsWereMet(); err != nil {
		t.Errorf("SQL expectations were not met: %v", err)
	}
}

func TestListLayerVersions(t *testing.T) {
	global.MockAC()
	code.MockCode()
	var m sqlmock.Sqlmock
	m, dbengine.Engine = global.MockDB()
	cases := []struct {
		name     string
		input    *api.ListLayerVersionsInput
		errIsNil bool
		sqlFunc  func()
	}{
		{
			name: "list",
			input: &api.ListLayerVersionsInput{
				CompatibleRuntime: "python2",
				ListCondition: &api.ListCondition{
					PageNo:   1,
					PageSize: 10,
				},
			},
			errIsNil: true,
			sqlFunc: func() {
				m.ExpectQuery("count").WillReturnRows(global.GetCount(1))
				m.ExpectQuery("SELECT").WillReturnRows(global.GetRowsLayer(global.GetTestLayers(1)))
			},
		},
		{
			name: "list count0",
			input: &api.ListLayerVersionsInput{
				CompatibleRuntime: "python2",
				ListCondition: &api.ListCondition{
					PageNo:   1,
					PageSize: 10,
				},
			},
			errIsNil: false,
			sqlFunc: func() {
				m.ExpectQuery("count").WillReturnRows(global.GetCount(0))
			},
		},
		{
			name: "list not found",
			input: &api.ListLayerVersionsInput{
				CompatibleRuntime: "python2",
				ListCondition: &api.ListCondition{
					PageNo:   1,
					PageSize: 10,
				},
			},
			errIsNil: false,
			sqlFunc: func() {
				m.ExpectQuery("count").WillReturnRows(global.GetCount(1))
				m.ExpectQuery("SELECT").WillReturnError(gorm.ErrRecordNotFound)
			},
		},
	}
	for _, tc := range cases {
		if tc.sqlFunc != nil {
			tc.sqlFunc()
		}
		_, err := ListLayerVersions("uid", tc.input, true)
		var isPass bool
		if tc.errIsNil {
			isPass = assert.Equal(t, nil, err)
		} else {
			isPass = assert.NotEqual(t, nil, err)
		}
		if !isPass {
			t.Logf("tc name err %s", tc.name)
		}
	}
}

// TestListLayerVersionsExcludeVended 测试用户获取自己的层版本列表时排除官方层，并返回结果
// 参数：t *testing.T - 单元测试对象指针，用于处理断言和记录错误信息
// 返回值：nil - 无返回值，只是用于执行断言和验证SQL查询
func TestListLayerVersionsExcludeVended(t *testing.T) {
	global.MockAC()
	code.MockCode()
	var m sqlmock.Sqlmock
	m, dbengine.Engine = global.MockDB()

	// 测试用户获取自己的层版本列表时排除官方层
	input := &api.ListLayerVersionsInput{
		CompatibleRuntime: "python2",
		LayerName:         "testLayer",
		ListCondition: &api.ListCondition{
			PageNo:   1,
			PageSize: 10,
		},
	}

	// 模拟查询条件包含 is_vended = false
	m.ExpectQuery("count").WillReturnRows(global.GetCount(1))
	m.ExpectQuery("SELECT").WillReturnRows(global.GetRowsLayer(global.GetTestLayers(1)))

	_, err := ListLayerVersions("uid", input, false) // getVendedLayers = false
	assert.Equal(t, nil, err)

	// 验证SQL查询中包含了 is_vended = false 的条件
	if err := m.ExpectationsWereMet(); err != nil {
		t.Errorf("SQL expectations were not met: %v", err)
	}
}

func TestDeleteLayerVersion(t *testing.T) {
	global.MockAC()
	code.MockCode()
	var m sqlmock.Sqlmock
	m, dbengine.Engine = global.MockDB()
	cases := []struct {
		name      string
		LayerName string
		Version   int64
		errIsNil  bool
		sqlFunc   func()
	}{
		{
			name:      "delete not found",
			LayerName: "layer1",
			Version:   1,
			errIsNil:  false,
			sqlFunc: func() {
				m.ExpectQuery("SELECT").WillReturnError(gorm.ErrRecordNotFound)
			},
		},
		{
			name:      "delete success",
			LayerName: "layer1",
			Version:   1,
			errIsNil:  true,
			sqlFunc: func() {
				m.ExpectQuery("SELECT").WillReturnRows(global.GetRowsLayer(global.GetTestLayers(1)))
				m.ExpectExec("UPDATE").WillReturnResult(sqlmock.NewResult(1, 1))
			},
		},
		{
			name:      "delete update err",
			LayerName: "layer1",
			Version:   1,
			errIsNil:  false,
			sqlFunc: func() {
				m.ExpectQuery("SELECT").WillReturnRows(global.GetRowsLayer(global.GetTestLayers(1)))
				m.ExpectExec("UPDATE").WillReturnResult(sqlmock.NewErrorResult(errors.New("err")))
			},
		},
	}
	for _, tc := range cases {
		if tc.sqlFunc != nil {
			tc.sqlFunc()
		}
		err := DeleteLayerVersion(&api.Layer{LayerName: tc.LayerName, Version: tc.Version})
		var isPass bool
		if tc.errIsNil {
			isPass = assert.Equal(t, nil, err)
		} else {
			isPass = assert.NotEqual(t, nil, err)
		}
		if !isPass {
			t.Logf("tc name %s", tc.name)
		}
	}
}

func TestDeleteLayer(t *testing.T) {
	global.MockAC()
	code.MockCode()
	var m sqlmock.Sqlmock
	m, dbengine.Engine = global.MockDB()
	cases := []struct {
		name      string
		LayerName string
		errIsNil  bool
		sqlFunc   func()
	}{
		{
			name:      "delete not found",
			LayerName: "layer1",
			errIsNil:  false,
			sqlFunc: func() {
				m.ExpectQuery("SELECT").WillReturnError(gorm.ErrRecordNotFound)
			},
		},
		{
			name:      "delete success",
			LayerName: "layer1",
			errIsNil:  true,
			sqlFunc: func() {
				m.ExpectQuery("SELECT").WillReturnRows(global.GetRowsLayer(global.GetTestLayers(1)))
				m.ExpectExec("UPDATE").WillReturnResult(sqlmock.NewResult(1, 1))
			},
		},
		{
			name:      "delete update err",
			LayerName: "layer1",
			errIsNil:  false,
			sqlFunc: func() {
				m.ExpectQuery("SELECT").WillReturnRows(global.GetRowsLayer(global.GetTestLayers(1)))
				m.ExpectExec("UPDATE").WillReturnResult(sqlmock.NewErrorResult(errors.New("err")))
			},
		},
	}
	for _, tc := range cases {
		if tc.sqlFunc != nil {
			tc.sqlFunc()
		}
		err := DeleteLayer(&api.Layer{LayerName: tc.LayerName})
		var isPass bool
		if tc.errIsNil {
			isPass = assert.Equal(t, nil, err)
		} else {
			isPass = assert.NotEqual(t, nil, err)
		}
		if !isPass {
			t.Logf("tc name %s", tc.name)
		}
	}
}

func TestGetLayerVersionByBrn(t *testing.T) {
	global.MockAC()
	code.MockCode()
	var m sqlmock.Sqlmock
	m, dbengine.Engine = global.MockDB()
	cases := []struct {
		name     string
		brn      bceBrn.LayerBRN
		errIsNil bool
		sqlFunc  func()
	}{
		{
			name:     "not found",
			brn:      bceBrn.GenerateLayerVersionBrn("bj", "uid", "layer1", 1),
			errIsNil: false,
			sqlFunc: func() {
				m.ExpectQuery("SELECT").WillReturnError(gorm.ErrRecordNotFound)
			},
		},
		{
			name:     "success",
			brn:      bceBrn.GenerateLayerVersionBrn("bj", "uid", "layer1", 1),
			errIsNil: true,
			sqlFunc: func() {
				m.ExpectQuery("SELECT").WillReturnRows(global.GetRowsLayer(global.GetTestLayers(1)))
			},
		},
	}
	for _, tc := range cases {
		if tc.sqlFunc != nil {
			tc.sqlFunc()
		}
		_, err := GetLayerVersionByBrn("uid", tc.brn)
		var isPass bool
		if tc.errIsNil {
			isPass = assert.Equal(t, nil, err)
		} else {
			isPass = assert.NotEqual(t, nil, err)
		}
		if !isPass {
			t.Logf("tc name %s", tc.name)
		}
	}
}

func TestPublishLayer(t *testing.T) {
	//
	global.MockAC()
	code.MockCode()
	global.RegisterFaasRuntimeGovalidatorTag()
	var m sqlmock.Sqlmock
	m, dbengine.Engine = global.MockDB()
	zipFileBytes, _ := base64.DecodeString(zipFile)
	symlinkZipFileBytes, _ := base64.DecodeString(symlinkZipFile)
	cases := []struct {
		name     string
		input    *api.PublishLayerVersionInput
		errIsNil bool
		sqlFunc  func()
	}{
		{
			name: "success",
			input: &api.PublishLayerVersionInput{
				CompatibleRuntimes: []string{"nodejs8"},
				Content: &api.LayerVersionContentInput{
					BosBucket:    "",
					BosObject:    "",
					ZipFileBytes: zipFileBytes,
				},
				Description: "desc",
				LayerName:   "LayerName",
				LicenseInfo: "",
			},
			errIsNil: true,
			sqlFunc: func() {
				m.ExpectQuery("SELECT").WillReturnError(gorm.ErrRecordNotFound)
				m.ExpectQuery("SELECT").WillReturnRows(global.GetRowsFunctions(global.GetTestFunc(1)))
				m.ExpectQuery("SELECT").WillReturnRows(global.GetRowsLayer(global.GetTestLayers(1)))
				m.ExpectExec("INSERT").WillReturnResult(sqlmock.NewResult(1, 1))
			},
		},
		{
			name: "bos args",
			input: &api.PublishLayerVersionInput{
				CompatibleRuntimes: []string{"nodejs8"},
				Content: &api.LayerVersionContentInput{
					BosBucket:    "b",
					BosObject:    "b",
					ZipFileBytes: zipFileBytes,
				},
				Description: "desc",
				LayerName:   "LayerName",
				LicenseInfo: "",
			},
			errIsNil: false,
			sqlFunc: func() {
				m.ExpectQuery("SELECT").WillReturnError(gorm.ErrRecordNotFound)
			},
		},
		{
			name: "create layer err",
			input: &api.PublishLayerVersionInput{
				CompatibleRuntimes: []string{"nodejs8"},
				Content: &api.LayerVersionContentInput{
					BosBucket:    "",
					BosObject:    "",
					ZipFileBytes: zipFileBytes,
				},
				Description: "desc",
				LayerName:   "LayerName",
				LicenseInfo: "",
			},
			errIsNil: false,
			sqlFunc: func() {
				m.ExpectQuery("SELECT").WillReturnError(gorm.ErrRecordNotFound)
				m.ExpectQuery("SELECT").WillReturnRows(global.GetRowsFunctions(global.GetTestFunc(1)))
				m.ExpectQuery("SELECT").WillReturnRows(global.GetRowsLayer(global.GetTestLayers(1)))
				m.ExpectExec("INSERT").WillReturnError(errors.New("some err"))
			},
		},
		{
			name: "create layer err1",
			input: &api.PublishLayerVersionInput{
				CompatibleRuntimes: []string{"nodejs8"},
				Content: &api.LayerVersionContentInput{
					BosBucket: "bucket",
					BosObject: "",
				},
				Description: "desc",
				LayerName:   "LayerName",
				LicenseInfo: "",
			},
			errIsNil: false,
			sqlFunc: func() {
				m.ExpectQuery("SELECT").WillReturnError(gorm.ErrRecordNotFound)
			},
		},
		{
			name: "create layer symlink err",
			input: &api.PublishLayerVersionInput{
				CompatibleRuntimes: []string{"nodejs10"},
				Content: &api.LayerVersionContentInput{
					BosBucket:    "",
					BosObject:    "",
					ZipFileBytes: symlinkZipFileBytes,
				},
				Description: "desc",
				LayerName:   "LayerName",
				LicenseInfo: "",
			},
			errIsNil: false,
			sqlFunc: func() {
				m.ExpectQuery("SELECT").WillReturnError(gorm.ErrRecordNotFound)
				m.ExpectQuery("SELECT").WillReturnRows(global.GetRowsFunctions(global.GetTestFunc(1)))
				m.ExpectQuery("SELECT").WillReturnRows(global.GetRowsLayer(global.GetTestLayers(1)))
			},
		},
	}
	for _, tc := range cases {
		if tc.sqlFunc != nil {
			tc.sqlFunc()
		}
		_, err := PublishLayer("uid", tc.input, nil)
		var isPass bool
		if tc.errIsNil {
			isPass = assert.Equal(t, nil, err)
		} else {
			isPass = assert.NotEqual(t, nil, err)
		}
		if !isPass {
			t.Logf("tc name %s", tc.name)
		}
		if err != nil {
			t.Logf("%s response %v", tc.name, err)
		}
	}
}

func TestValidateCompatibleRuntime(t *testing.T) {
	global.MockAC()
	code.MockCode()
	cases := []struct {
		name     string
		runtime  string
		errIsNil bool
	}{
		{
			name:     "err",
			runtime:  "nodejs",
			errIsNil: false,
		},
		{
			name:     "success",
			runtime:  "nodejs8.5",
			errIsNil: true,
		},
		{
			name:     "success",
			runtime:  "",
			errIsNil: true,
		},
	}
	for _, tc := range cases {
		err := ValidateCompatibleRuntime(tc.runtime)
		var isPass bool
		if tc.errIsNil {
			isPass = assert.Equal(t, nil, err)
		} else {
			isPass = assert.NotEqual(t, nil, err)
		}
		if !isPass {
			t.Logf("tc name %s", tc.name)
		}
		if err != nil {
			t.Logf("%s err %v", tc.name, err)
		}
	}
}

func TestCheckFunctionLayers(t *testing.T) {
	global.MockAC()
	code.MockCode()
	var m sqlmock.Sqlmock
	m, dbengine.Engine = global.MockDB()
	cases := []struct {
		name                  string
		uid                   string
		functionCodeSize      int64
		layerBrnInterfaceList []*api.LayerSample
		errIsNil              bool
		sqlFunc               func()
	}{
		{
			name:                  "nil",
			uid:                   "uid",
			functionCodeSize:      123,
			layerBrnInterfaceList: []*api.LayerSample{},
			errIsNil:              true,
		},
		{
			name:                  "err layer num",
			uid:                   "uid",
			functionCodeSize:      123,
			layerBrnInterfaceList: []*api.LayerSample{{Brn: "1"}, {Brn: "2"}, {Brn: "3"}, {Brn: "4"}, {Brn: "5"}, {Brn: "6"}},
			errIsNil:              false,
		},
		{
			name:                  "err layer interface int",
			uid:                   "uid",
			functionCodeSize:      123,
			layerBrnInterfaceList: []*api.LayerSample{{Brn: "1"}},
			errIsNil:              false,
		},
		{
			name:                  "err layer brn",
			uid:                   "uid",
			functionCodeSize:      123,
			layerBrnInterfaceList: []*api.LayerSample{{Brn: "cfc:zzzz:640c8817bd1de2928d47256dd0620ce5:layer:consoletestlayer1:2"}},
			errIsNil:              false,
		},
		{
			name:                  "err layer brn repetition",
			uid:                   "uid",
			functionCodeSize:      123,
			layerBrnInterfaceList: []*api.LayerSample{{Brn: "brn:bce:cfc:bj:640c8817bd1de2928d47256dd0620ce5:layer:consoletestlayer1:2"}, {Brn: "brn:bce:cfc:bj:640c8817bd1de2928d47256dd0620ce5:layer:consoletestlayer1:3"}},
			errIsNil:              false,
		},
		{
			name:                  "success",
			uid:                   "uid",
			functionCodeSize:      123,
			layerBrnInterfaceList: []*api.LayerSample{{Brn: "brn:bce:cfc:bj:640c8817bd1de2928d47256dd0620ce5:layer:consoletestlayer1:2"}},
			errIsNil:              true,
			sqlFunc: func() {
				m.ExpectQuery("SELECT").WillReturnRows(global.GetRowsLayer(global.GetTestLayers(1)))
			},
		},
		{
			name:                  "success",
			uid:                   "uid",
			functionCodeSize:      250 * 1024 * 1024,
			layerBrnInterfaceList: []*api.LayerSample{{Brn: "brn:bce:cfc:bj:640c8817bd1de2928d47256dd0620ce5:layer:consoletestlayer1:2"}},
			errIsNil:              true,
			sqlFunc: func() {
				m.ExpectQuery("SELECT").WillReturnRows(global.GetRowsLayer(global.GetTestLayers(1)))
			},
		},
	}
	for i, tc := range cases {
		if tc.sqlFunc != nil {
			tc.sqlFunc()
		}
		_, _, err := CheckFunctionLayers(tc.uid, tc.functionCodeSize, tc.layerBrnInterfaceList)
		var isPass bool
		if tc.errIsNil {
			isPass = assert.Equal(t, nil, err)
		} else {
			isPass = assert.NotEqual(t, nil, err)
		}
		if !isPass {
			t.Logf("tc name %s", tc.name)
			t.Logf("tc cases %d", i)
		}
		if err != nil {
			t.Logf("%s err %v", tc.name, err)
		}
	}
}

var zipFile = "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"
var symlinkZipFile = "UEsDBAoAAAAAAMSbjVDCVPv+DQAAAA0AAAAKABwAYmx1ZXByaW50c1VUCQADP02UXj9NlF51eAsAAQT2AQAABBQAAAAuLi9ibHVlcHJpbnRzUEsDBBQAAAAIAMOTmlDxG1zIfwAAAMMAAAAIABwAaW5kZXguanNVVAkAA61ipV6tYqVedXgLAAEE9gEAAAQUAAAAbY1NCsJADIX3c4owm7agvUCpa2/gQroYp0GLIZEklYp4d5niqrh7fO+vmg3BXKfsVRdweYi6tbfEI6FCD8lenKHGJ7LvIAs7LkUkokvK9wb6A7wDFMeEsCW51vGIRAInURpjszHXpb/wHFl4j8tkHodt4vdcsKLPyrB2wqcLX1BLAQIeAwoAAAAAAMSbjVDCVPv+DQAAAA0AAAAKABgAAAAAAAAAAADtoQAAAABibHVlcHJpbnRzVVQFAAM/TZRedXgLAAEE9gEAAAQUAAAAUEsBAh4DFAAAAAgAw5OaUPEbXMh/AAAAwwAAAAgAGAAAAAAAAQAAAKSBUQAAAGluZGV4LmpzVVQFAAOtYqVedXgLAAEE9gEAAAQUAAAAUEsFBgAAAAACAAIAngAAABIBAAAAAA=="
