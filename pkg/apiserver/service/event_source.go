package service

import (
	"encoding/base64"
	"fmt"
	"time"

	"strings"

	"github.com/google/uuid"
	"github.com/jinzhu/gorm"

	"icode.baidu.com/baidu/faas/kun/pkg/api"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/dao"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/dbengine"
	apiErr "icode.baidu.com/baidu/faas/kun/pkg/apiserver/error"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/global"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/models"
	"icode.baidu.com/baidu/faas/kun/pkg/bce/bms"
	"icode.baidu.com/baidu/faas/kun/pkg/bce/iam"
	kunErr "icode.baidu.com/baidu/faas/kun/pkg/error"
	"icode.baidu.com/baidu/faas/kun/pkg/util/convert"
	"icode.baidu.com/baidu/faas/kun/pkg/util/json"
)

func CreateOneEventSource(a *dao.FuncEventSource, user *iam.User) error {
	tx := dbengine.DBTransaction()

	if kerr := newOneEventSource(tx, a, user); kerr != nil {
		tx.Rollback()
		return kerr
	}

	tx.Commit()
	a.DealEventSource()
	notifyList := api.NewBMSNotifyMsg()
	notifyList.AddInfo(a.Uuid, api.MethodCreate)
	dbengine.NotifyEtcdWhenChange(api.EventSourceEtcdPrefix, a.Uuid, notifyList)
	return nil
}

func DeleteOneEventSource(a *dao.FuncEventSource) error {
	err := dao.DeleteEventSource(dbengine.DBInstance(), a)
	if err != nil {
		return err
	}
	// 通知
	notifyList := api.NewBMSNotifyMsg()
	notifyList.AddInfo(a.Uuid, api.MethodDelete)
	dbengine.NotifyEtcdWhenChange(api.EventSourceEtcdPrefix, a.Uuid, notifyList)
	return nil
}

func newOneEventSource(tx *gorm.DB, a *dao.FuncEventSource, user *iam.User) error {

	switch a.Type {
	case api.TypeEventSourceDatahubTopic:
		err := checkDatahubTopicEventSource(a, user)
		if err != nil {
			return err
		}
	default:
		err := checkKafkaEventSource(a, user)
		if err != nil {
			return err
		}
	}

	// --- 唯一性检查 begin ---
	var existList *[]dao.FuncEventSource
	var err error
	zeroTime := a.DeletedAt
	if zeroTime.IsZero() {
		zeroTime, _ = time.Parse("2006-01-02 15:04:05", "0000-00-00 00:00:00")
	}
	switch a.Type {
	case api.TypeEventSourceRocketmq:
		existList, err = dao.FindEventSource(&dao.FuncEventSource{
			ClusterID:       a.ClusterID,
			ConsumerGroupId: a.ConsumerGroupId,
			DeletedAt:       zeroTime,
		})
	case api.TypeEventSourceExclusiveKafka:
		existList, err = dao.FindEventSource(&dao.FuncEventSource{
			FunctionBrn:    a.FunctionBrn,
			EventSourceBrn: a.EventSourceBrn,
			ClusterID:      a.ClusterID,
			DeletedAt:      zeroTime,
		})
	default:
		existList, err = dao.FindEventSource(&dao.FuncEventSource{
			FunctionBrn:    a.FunctionBrn,
			EventSourceBrn: a.EventSourceBrn,
			DeletedAt:      zeroTime,
		})
	}
	if err != nil {
		return err
	}
	if existList != nil && len(*existList) > 0 {
		if a.Type == api.TypeEventSourceRocketmq {
			return apiErr.NewRocetmqConsumerConflictException("RocketMQ同一集群同一个消费组已存在触发器", nil)
		}
		return apiErr.NewResourceConflictException("event source already exists", nil)
	}
	// --- 唯一性检查 end ---

	if kerr := dao.CreateEventSource(tx, a); kerr != nil {
		return kerr
	}
	return nil
}

// check Kafka EventSource
func checkKafkaEventSource(a *dao.FuncEventSource, user *iam.User) error {
	if a.FunctionName == "" || a.EventSourceBrn == "" ||
		a.StartingPosition == "" {
		return kunErr.NewInvalidParameterValueException("FunctionName or EventSourceBrn or"+
			" StartingPosition is empty", nil)
	}

	switch a.StartingPosition {
	case api.StartingPositionLatest, api.StartingPositionTriHorizon:
	default:
		return kunErr.NewInvalidParameterValueException("StartingPosition is invalid", nil)
	}

	if a.BatchSize < 1 || a.BatchSize > 1000 {
		return kunErr.NewInvalidParameterValueException("BatchSize is invalid", nil)
	}

	//生成function brn
	f, err := models.InitUserFunc(user.Domain.ID, a.FunctionName, "", a.WorkspaceID)
	if err != nil {
		return apiErr.NewInitFuncMetaException(err.Error(), "", nil)
	}
	if f.Version == "" {
		f.Version = "$LATEST"
	}
	f.FunctionBrnInit()
	// 检查 functionBrn 是否存在
	if err = dao.FindOneFunc(f); err != nil {
		return apiErr.NewResourceNotFoundException("function not exists : "+f.FunctionName, nil)
	}
	// 检查是否需要bms证书检查
	if a.Type == api.TypeEventSourceBms {
		if api.EnableCheckBms {
			authorization := &bms.SuperAuthorization{
				TopicOwnerAccountID: f.Uid,
				Authorization: &bms.Authorization{
					//TopicOperation: "Read",
					AccountId:     global.AC.Config.BmsAccountID,
					Topic:         a.EventSourceBrn,
					CertificateSN: global.AC.Config.BmsCertificateSN,
				},
			}
			err = global.AC.Clients.BmsClient.AuthorizationSuper(authorization, api.TopicReadOperation)
			if err != nil {
				return apiErr.NewResourceNotFoundException(fmt.Sprintf("bms kafka topic not exists: [eventsource: %v] [error: %v]", a.EventSourceBrn, err), err)
			}
		}
	}

	a.FunctionBrn = f.FunctionBrn
	a.Uuid = uuid.New().String()
	a.Uid = user.Domain.ID
	a.DatahubConfigStr = convert.String("")
	// 支持专享版 kafka exclusive_kafka type，不再统一赋值bms
	// a.Type = api.TypeEventSourceBms
	return nil
}

func checkDatahubTopicEventSourceConfig(a *dao.FuncEventSource) error {
	if a.FunctionName == "" {
		return kunErr.NewInvalidParameterValueException("FunctionName  is empty", nil)
	}
	if a.DatahubConfig.MetaHostEndpoint == "" || a.DatahubConfig.ClusterName == "" || a.DatahubConfig.PipeName == "" {
		return kunErr.NewInvalidParameterValueException("MetaHostEndpoint or ClusterName "+
			" PipeName or is empty", nil)
	}

	if a.DatahubConfig.AclName == "" || a.DatahubConfig.AclPassword == "" {
		return kunErr.NewInvalidParameterValueException("ACLName or ACLPassword is empty", nil)
	}

	if a.DatahubConfig.PipeletNum < 1 || a.DatahubConfig.PipeletNum > 100 {
		return kunErr.NewInvalidParameterValueException("PipeletNum is invalid", nil)
	}

	if a.BatchSize < 1 || a.BatchSize > 1000 {
		return kunErr.NewInvalidParameterValueException("BatchSize is invalid", nil)
	}
	return nil

}
func checkDatahubTopicEventSource(a *dao.FuncEventSource, user *iam.User) error {

	err := checkDatahubTopicEventSourceConfig(a)
	if err != nil {
		return err
	}
	// 取值可以是最新一条/最老一条/某个 大于0的订阅点
	switch a.DatahubConfig.StartPoint {
	case api.DatahubTopicStartPointLatest, api.DatahubTopicStartPointOldest:
	default:
		if a.DatahubConfig.StartPoint < int64(0) {
			return kunErr.NewInvalidParameterValueException("StartingPosition is invalid", nil)
		}
	}

	//生成function brn
	f, err := models.InitUserFunc(user.Domain.ID, a.FunctionName, "", a.WorkspaceID)
	if err != nil {
		return apiErr.NewInitFuncMetaException(err.Error(), "", nil)
	}
	if f.Version == "" {
		f.Version = "$LATEST"
	}
	f.FunctionBrnInit()
	// 检查 functionBrn 是否存在
	if err = dao.FindOneFunc(f); err != nil {
		return apiErr.NewResourceNotFoundException("function not exists : "+f.FunctionName, nil)
	}
	a.FunctionBrn = f.FunctionBrn
	a.Uuid = uuid.New().String()
	a.Uid = user.Domain.ID
	a.EventSourceBrn = fmt.Sprintf("%s|%d|%s|%s|%d|%d", a.DatahubConfig.MetaHostEndpoint,
		a.DatahubConfig.MetaHostPort, a.DatahubConfig.ClusterName, a.DatahubConfig.PipeName,
		a.DatahubConfig.PipeletNum, a.DatahubConfig.StartPoint)

	a.MetaHostEndpoint = a.DatahubConfig.MetaHostEndpoint
	a.MetaHostPort = a.DatahubConfig.MetaHostPort
	a.ClusterName = a.DatahubConfig.ClusterName
	a.PipeName = a.DatahubConfig.PipeName
	a.PipeletNum = a.DatahubConfig.PipeletNum
	a.StartPoint = a.DatahubConfig.StartPoint
	a.AclName = a.DatahubConfig.AclName
	a.AclPassword = a.DatahubConfig.AclPassword

	tmpStr, _ := json.Marshal(&a.DatahubConfig)
	a.DatahubConfigStr = convert.String(string(tmpStr))

	return nil
}

func UpdateEventSource(cond, update *dao.FuncEventSource, user *iam.User) (find *dao.FuncEventSource, err error) {
	// 查询eventsource
	es := &dao.FuncEventSource{}
	es.Uuid = cond.Uuid
	if kerr := dao.FindOneEventSource(es); kerr != nil {
		return nil, kerr
	}
	f := &dao.Function{}
	if update.FunctionName != "" {
		f, err = models.InitUserFunc(user.Domain.ID, update.FunctionName, "", update.WorkspaceID)
		if err != nil {
			return nil, apiErr.NewInitFuncMetaException(err.Error(), "", nil)
		}
		if f.Version == "" {
			f.Version = "$LATEST"
		}
		f.FunctionBrnInit()
		update.FunctionBrn = f.FunctionBrn
	} else {
		f.Uid = user.Domain.ID
		f.FunctionBrn = es.FunctionBrn
	}

	// check brn
	if err = dao.FindOneFunc(f); err != nil {
		return nil, apiErr.NewResourceNotFoundException("function not exists : "+f.FunctionName, nil)
	}
	// check bms topic
	switch es.Type {
	case api.TypeEventSourceBms:
		// 检查是否需要进行bms证书检查
		if api.EnableCheckBms {
			authorization := &bms.SuperAuthorization{
				TopicOwnerAccountID: f.Uid,
				Authorization: &bms.Authorization{
					//TopicOperation: "Read",
					AccountId:     global.AC.Config.BmsAccountID,
					Topic:         es.EventSourceBrn,
					CertificateSN: global.AC.Config.BmsCertificateSN,
				},
			}
			err := global.AC.Clients.BmsClient.AuthorizationSuper(authorization, api.TopicReadOperation)
			if err != nil {
				return nil, apiErr.NewResourceNotFoundException("bms kafka topic not exists : "+es.EventSourceBrn, err)
			}
		}
		update.DatahubConfigStr = convert.String("")
	case api.TypeEventSourceDatahubTopic:
		//
		// meta endpoint /port/clusterName不允许修改，因为需要重置client，比较复杂
		update.FunctionName = f.FunctionName
		err := checkDatahubTopicEventSourceConfig(update)
		if err != nil {
			return nil, err
		}
		update.EventSourceBrn = fmt.Sprintf("%s|%d|%s|%s|%d|%d", update.DatahubConfig.MetaHostEndpoint,
			update.DatahubConfig.MetaHostPort, update.DatahubConfig.ClusterName,
			update.DatahubConfig.PipeName, update.DatahubConfig.PipeletNum, update.DatahubConfig.StartPoint)
		tmpStr, _ := json.Marshal(update.DatahubConfig)
		update.DatahubConfigStr = convert.String(string(tmpStr))
	}

	find, err = dao.UpdateEventSource(*cond, update)
	if err != nil {
		return nil, err
	}
	notifyList := api.NewBMSNotifyMsg()
	notifyList.AddInfo(find.Uuid, api.MethodUpdate)
	dbengine.NotifyEtcdWhenChange(api.EventSourceEtcdPrefix, find.Uuid, notifyList)
	return find, err
}

func GetEventSourceLastProcessingResult(es *dao.FuncEventSource) {
	if es.Uuid == "" {
		return
	}
	rd := global.AC.Clients.Redis.Redis()
	redisKey := fmt.Sprintf("%s:event_source_mapping:%s:result", global.AC.Clients.Redis.KeyPrefix(), es.Uuid)
	s, err := rd.Get(redisKey).Result()
	if err != nil || s == "" {
		return
	}
	l := strings.SplitN(s, "^", 2)
	if len(l) != 2 {
		return
	}
	b, _ := base64.StdEncoding.DecodeString(l[1])
	result := string(b)
	es.LastProcessingResult = result
	if result != "OK" {
		es.StateTransitionReason = "Unrecoverable error"
	} else {
		es.StateTransitionReason = "User action"
	}
}

// 这里这些不知道在干嘛
func DelEventSourceLastProcessingResult(UUID string) {
	rd := global.AC.Clients.Redis.Redis()
	redisKey := fmt.Sprintf("%s:event_source_mapping:%s:result", global.AC.Clients.Redis.KeyPrefix(), UUID)
	_, err := rd.Del(redisKey).Result()
	if err != nil {

	}
}

func InsideDisableAndSetReason(cond, update *dao.FuncEventSource) (find *dao.FuncEventSource, err error) {
	// 查询eventsource
	es := &dao.FuncEventSource{}
	es.Uuid = cond.Uuid
	if kerr := dao.FindOneEventSource(es); kerr != nil {
		return nil, kerr
	}
	find, err = dao.UpdateEventSource(*cond, update)
	if err != nil {
		return nil, err
	}
	notifyList := api.NewBMSNotifyMsg()
	notifyList.AddInfo(find.Uuid, api.MethodUpdate)
	dbengine.NotifyEtcdWhenChange(api.EventSourceEtcdPrefix, find.Uuid, notifyList)
	return find, err
}
