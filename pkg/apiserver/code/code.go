package code

import (
	"crypto/sha256"
	"fmt"
	"io"
	"io/ioutil"
	"os"
	"path/filepath"
	"strings"
	"time"

	"github.com/google/uuid"

	"icode.baidu.com/baidu/faas/kun/cmd/apiserver/options"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/squashfs"
	"icode.baidu.com/baidu/faas/kun/pkg/util/base64"
	"icode.baidu.com/baidu/faas/kun/pkg/util/logs"
	"icode.baidu.com/baidu/faas/kun/pkg/util/zip"
)

type (
	CodeManagerInterface interface {
		FaasGetCodeObject(functionName string, uid string, codeSha256, codeID string) ([]byte, error)
		FaasGetBytesFromCodeStorage(objectKey string) ([]byte, error)
		FaasRemoveLayerCode(LayerName, uid, codeID string) (err error)
		FaasUploadCode(byteArray []byte, functionName string, uid string) (codeSha256 string, squashFsSha256 *string, codeSize int32, codeID string, err error)
		FaasUploadLayerCode(byteArray []byte, LayerName string, uid string) (codeSha256 string, codeSize int64, codeID string, err error)
		FaasUploadCodeSqfs(byteArray []byte, codeSize int32, squashFsKey string) (codeSha256 string, err error)
		FaasUploadLayerSqfs(byteArray []byte, codeSize int32, layerSquashFsKey string) (codeSha256 string, err error)
		FaasUploadBlueprintZip(byteArray []byte, fileName string) (string, error)

		FaasCodeDownloadUrl(functionName string, uid string, codeSha256, codeID string) (string, error)
		FaasFunctionLayersSquashFsDownloadUrl(functionName string, uid string, layerSha256 string) string
		FaasLayerDownloadUrl(layerName string, uid string, codeID string) string
		FaasSquashFsCodeDownloadUrl(functionName string, uid string, codeID string) (string, error)
		FaasBpCodeDownloadUrl(bosObjKey string) string

		ObjectStorageInterface
	}

	ObjectStorageInterface interface {
		GeneratePresignedUrl(string) string
		GetObject(objectKey string) (io.ReadCloser, error)
		ListObjects(*ListObjectsArgs) (*ListObjectsResult, error)
		Upload([]byte, string) (string, error)
		DeleteObject(string) error
		DeleteMultiObjects(keys DeleteMultipleObjectKeys) error
		BasicCopyObject(object, srcObject string) error
		CopyObjectFromTrash(string) error
	}
)

type CodeManager struct {
	Squash *squashfs.SquashFsClient
	ObjectStorageInterface
}

func NewCodeManager(c *options.CodeConfiguration, squash *squashfs.SquashFsClient) (m CodeManagerInterface, err error) {
	var storage ObjectStorageInterface
	if c.StorageType == "" {
		c.StorageType = "bos"
	}
	if c.StorageType == "bos" {
		storage, err = NewBosManager(c)
	} else if c.StorageType == "minio" {
		storage, err = NewMinioClient(c)
	} else {
		return nil, fmt.Errorf("storage type invalid: %s", c.StorageType)
	}
	if err != nil {
		return nil, err
	}
	manager := CodeManager{
		ObjectStorageInterface: storage,
		Squash:                 squash,
	}
	return &manager, nil
}

func (cm *CodeManager) SetStorage(storage ObjectStorageInterface) {
	cm.ObjectStorageInterface = storage
}

func (cm *CodeManager) FaasUploadCodeSqfs(byteArray []byte, codeSize int32, squashFsKey string) (codeSha256 string, err error) {
	return cm.faasUploadCodeSqfs(byteArray, codeSize, squashFsKey, "apiserver_squashfs")
}

func (cm *CodeManager) FaasUploadLayerSqfs(byteArray []byte, codeSize int32, layerSquashFsKey string) (codeSha256 string, err error) {
	return cm.faasUploadCodeSqfs(byteArray, codeSize, layerSquashFsKey, "apiserver_layer_squashfs")
}

func (cm *CodeManager) FaasUploadCode(byteArray []byte, functionName string, uid string) (codeSha256 string, squashFsSha256 *string, codeSize int32, codeID string, err error) {
	meta := generateCodeMeta(byteArray)
	codeSha256 = meta["CodeSha256"].(string)
	codeSize = int32(meta["CodeSize"].(int64))
	codeID = uuid.New().String()

	unzipFileNum, err := zip.GetUnzipFileNum(byteArray, int64(codeSize))
	if err != nil {
		logs.V(4).Errorf("GetUnzipFileNum failed, err: %s", err)
		return
	}

	newObjectKey := GetFuncZipKey(uid, functionName, codeID)
	if _, err = cm.ObjectStorageInterface.Upload(byteArray, newObjectKey); err != nil {
		logs.V(4).Errorf("Upload code failed, err: %s", err)
		return
	}
	var squashFsSha256Str string
	if codeSize > cm.Squash.GetSquashFsCodeSize() || unzipFileNum > cm.Squash.GetSquashFsFileNum() {
		squashFsKey := GetFuncSquashfsKey(uid, functionName, codeID)
		squashFsSha256Str, err = cm.FaasUploadCodeSqfs(byteArray, codeSize, squashFsKey)
		if err != nil {
			logs.V(4).Errorf("Upload code sqfs failed, err: %s", err)
			return
		}
	}
	squashFsSha256 = &squashFsSha256Str
	return
}

func (cm *CodeManager) FaasUploadLayerCode(byteArray []byte, LayerName string, uid string) (codeSha256 string, codeSize int64, codeID string, err error) {
	meta := generateCodeMeta(byteArray)
	codeSha256 = meta["CodeSha256"].(string)
	codeSize = meta["CodeSize"].(int64)
	codeID = uuid.New().String()

	objectKey := GetLayerKey(uid, LayerName, codeID)
	if _, err = cm.ObjectStorageInterface.Upload(byteArray, objectKey); err != nil {
		return
	}
	return
}

func (cm *CodeManager) FaasRemoveLayerCode(LayerName, uid, codeID string) (err error) {
	objectKey := GetLayerKey(uid, LayerName, codeID)
	sqfsKey := GetLayerSquashFsKey(uid, LayerName, codeID)
	err = cm.ObjectStorageInterface.DeleteObject(objectKey)
	if err != nil {
		return err
	}
	return cm.ObjectStorageInterface.DeleteObject(sqfsKey)
}

func (cm *CodeManager) FaasGetBytesFromCodeStorage(objectKey string) ([]byte, error) {
	o, err := cm.ObjectStorageInterface.GetObject(objectKey)
	if err != nil {
		return nil, err
	}
	defer o.Close()
	return ioutil.ReadAll(o)
}

func (cm *CodeManager) FaasCodeDownloadUrl(functionName string, uid string, codeSha256, codeID string) (string, error) {
	objectKey := GetFuncZipKey(uid, functionName, codeID)
	return cm.ObjectStorageInterface.GeneratePresignedUrl(objectKey), nil
}

func (cm *CodeManager) FaasLayerDownloadUrl(layerName string, uid string, codeID string) string {
	objectKey := GetLayerKey(uid, layerName, codeID)
	return cm.ObjectStorageInterface.GeneratePresignedUrl(objectKey)
}

func (cm *CodeManager) FaasFunctionLayersSquashFsDownloadUrl(functionName string, uid string, layerSha256 string) string {
	objectKey := GetFunctionLayersSquashFsKey(uid, functionName, layerSha256)
	return cm.ObjectStorageInterface.GeneratePresignedUrl(objectKey)
}

func (cm *CodeManager) FaasGetCodeObject(functionName string, uid string, codeSha256, codeID string) ([]byte, error) {
	objectKey := GetFuncZipKey(uid, functionName, codeID)
	return cm.FaasGetBytesFromCodeStorage(objectKey)
}

func (cm *CodeManager) FaasUploadBlueprintZip(byteArray []byte, fileName string) (string, error) {
	prefix := strings.Split(fileName, ".")[0]
	timeUnix := uint64(time.Now().Unix())
	objectKey := fmt.Sprintf("blueprints/%s_%d.zip", prefix, timeUnix)
	if _, err := cm.ObjectStorageInterface.Upload(byteArray, objectKey); err != nil {
		return "", err
	}
	return objectKey, nil
}

func (cm *CodeManager) FaasSquashFsCodeDownloadUrl(functionName string, uid string, codeID string) (string, error) {
	objectKey := GetFuncSquashfsKey(uid, functionName, codeID)
	return cm.ObjectStorageInterface.GeneratePresignedUrl(objectKey), nil
}

// 根据蓝图bosObjKey获取下载zip包url
func (cm *CodeManager) FaasBpCodeDownloadUrl(bosObjKey string) string {
	return cm.ObjectStorageInterface.GeneratePresignedUrl(bosObjKey)
}

func (cm *CodeManager) faasUploadCodeSqfs(byteArray []byte, codeSize int32, squashFsKey, tmpDirPrefix string) (codeSha256 string, err error) {
	// 生成临时目录
	tmpDir, err := ioutil.TempDir("", tmpDirPrefix)
	if err != nil {
		return
	}
	// 删除目录
	defer os.RemoveAll(tmpDir)
	targetDir := filepath.Join(tmpDir, "squashfs_dir")
	targetFileName := filepath.Join(tmpDir, "target.sqfs")
	// 1. 加压文件到目录
	err = zip.UnzipFromBytes(targetDir, byteArray, int64(codeSize))
	if err != nil {
		logs.V(4).Errorf("Unzip code failed, err: %s", err)
		return
	}
	// 2.mksquashfs 需要 squashfs-tools
	err = cm.Squash.MkSquashFs(targetDir, targetFileName)
	if err != nil {
		logs.V(4).Errorf("mksquashfs failed, err: %s", err)
		return
	}
	f, err := ioutil.ReadFile(targetFileName)
	if err != nil {
		panic(err)
	}
	hasher := sha256.New()
	hasher.Write(f)
	shaByte := hasher.Sum(nil)
	codeSha256 = base64.EncodeToString(shaByte)
	// 3. 上传镜像到bos
	if _, err = cm.ObjectStorageInterface.Upload(f, squashFsKey); err != nil {
		logs.V(4).Errorf("Upload code sqfs failed, err: %s", err)
		return
	}
	return
}
