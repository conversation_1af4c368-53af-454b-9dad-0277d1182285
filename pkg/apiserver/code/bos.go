/**
 * Created Date: Wednesday, September 13th 2017, 2:33:05 pm
 * Author: hefan<PERSON>hi
 * Copyright (c) 2017 Baidu.Inc
 *
 */

package code

import (
	"errors"
	"io"

	"github.com/baidubce/bce-sdk-go/http"

	"github.com/baidubce/bce-sdk-go/bce"
	"github.com/baidubce/bce-sdk-go/services/bos"
	"github.com/baidubce/bce-sdk-go/services/bos/api"

	"icode.baidu.com/baidu/faas/kun/cmd/apiserver/options"
)

// BosManager BOS文件管理
type (
	BosManager struct {
		Bucket string
		Client *bos.Client
	}
)

const ExpirationPeriodInSeconds = 600

// NewBosManager 创建BOS管理器
func NewBosManager(c *options.CodeConfiguration) (*BosManager, error) {
	bosClient, err := bos.NewClient(c.<PERSON>, c<PERSON>, c.<PERSON>point)
	if err != nil {
		return nil, err
	}
	return &BosManager{
		Bucket: c.<PERSON>,
		Client: bosClient,
	}, nil
}

func (b *BosManager) GeneratePresignedUrl(objectKey string) string {
	//返回的uri: https://faasbj.bj.bcebos.com/c7ac82ae14ef42d1a4ffa3b2ececa17f/test_hello_****
	return b.Client.GeneratePresignedUrl(b.Bucket, objectKey, ExpirationPeriodInSeconds, http.GET, nil, nil)
}

func (b *BosManager) Upload(byteArray []byte, objectKey string) (string, error) {
	return b.Client.PutObjectFromBytes(b.Bucket, objectKey, byteArray, nil)
}

func (b *BosManager) GetObject(objectKey string) (io.ReadCloser, error) {
	o, err := b.Client.BasicGetObject(b.Bucket, objectKey)
	if err != nil {
		return nil, err
	}
	return o.Body, nil
}

func (b *BosManager) GetObjectMeta(objectKey string) (*api.GetObjectMetaResult, error) {
	return b.Client.GetObjectMeta(b.Bucket, objectKey)
}

func (b *BosManager) ListObjects(args *ListObjectsArgs) (*ListObjectsResult, error) {
	var bosArgs api.ListObjectsArgs
	if args != nil {
		bosArgs = api.ListObjectsArgs(*args)
	}
	res, err := b.Client.ListObjects(b.Bucket, &bosArgs)
	if err != nil {
		return nil, err
	}
	listRes := b.WrapperListObjectResult(res)
	return listRes, nil
}

func (b *BosManager) ListBuckets() (*api.ListBucketsResult, error) {
	return b.Client.ListBuckets()
}

func (b *BosManager) DeleteObject(objectKey string) error {
	return b.Client.DeleteObject(b.Bucket, objectKey)
}

func (b *BosManager) DeleteMultiObjects(keys DeleteMultipleObjectKeys) error {
	// 全部删除成功时 res = nil, err = io.EOF
	// 部分删除成功时 res != nil, err = nil
	objs := make([]api.DeleteObjectArgs, 0)
	for _, k := range keys {
		objs = append(objs, api.DeleteObjectArgs{k})
	}
	bosArgs := &api.DeleteMultipleObjectsArgs{
		Objects: objs,
	}
	res, err := b.Client.DeleteMultipleObjectsFromStruct(b.Bucket, bosArgs)
	if err == io.EOF && res == nil {
		return nil
	}
	if err == nil {
		err = errors.New("delete multi objects failed")
	}
	return err
}

func (b *BosManager) CopyObjectFromTrash(desObject string) error {
	srcObject := ".trash/" + desObject
	if _, err := b.Client.CopyObject(b.Bucket, desObject, b.Bucket, srcObject, nil); err != nil {
		return err
	}
	return nil
}

func (b *BosManager) BasicCopyObject(object, srcObject string) error {
	if _, err := b.Client.BasicCopyObject(b.Bucket, object, b.Bucket, srcObject); err != nil {
		return err
	}
	return nil
}

func (b *BosManager) GetConfig() *bce.BceClientConfiguration {
	return b.Client.Config
}

func (b *BosManager) WrapperListObjectResult(res *api.ListObjectsResult) *ListObjectsResult {
	listRes := ListObjectsResult{
		Name:        res.Name,
		Prefix:      res.Prefix,
		Delimiter:   res.Delimiter,
		Marker:      res.Marker,
		NextMarker:  res.NextMarker,
		MaxKeys:     res.MaxKeys,
		IsTruncated: res.IsTruncated,
	}
	for _, prefix := range res.CommonPrefixes {
		listRes.CommonPrefixes = append(listRes.CommonPrefixes, CommonPrefix(prefix))
	}
	for _, content := range res.Contents {
		listRes.Contents = append(listRes.Contents, *b.WrapperObjectInfo(&content))
	}

	return &listRes
}

func (b *BosManager) WrapperObjectInfo(res *api.ObjectSummaryType) *ObjectInfo {
	obj := ObjectInfo{
		LastModified: res.LastModified,
		ETag:         res.ETag,
		Size:         res.Size,
		StorageClass: res.StorageClass,
		Owner:        *b.WrapperOwner(&res.Owner),
	}
	return &obj
}

func (b *BosManager) WrapperOwner(res *api.OwnerType) *Owner {
	owner := Owner{
		ID:          res.Id,
		DisplayName: res.DisplayName,
	}
	return &owner
}
