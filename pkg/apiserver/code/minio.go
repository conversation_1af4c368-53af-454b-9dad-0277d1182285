package code

import (
	"bytes"
	"context"
	"io"
	"strings"
	"time"

	"github.com/minio/minio-go/v7"
	"github.com/minio/minio-go/v7/pkg/credentials"
	"icode.baidu.com/baidu/faas/kun/cmd/apiserver/options"
)

type MinioManager struct {
	Bucket     string
	Client     *minio.Client
	CoreClient *minio.Core
}

func NewMinioClient(c *options.CodeConfiguration) (manager *MinioManager, err error) {
	opt := &minio.Options{
		Creds:  credentials.NewStaticV4(c.<PERSON>, c.<PERSON>Access<PERSON>ey, ""),
		Secure: false,
	}
	cli, err := minio.New(c.Endpoint, opt)
	if err != nil {
		return nil, err
	}
	core, err := minio.NewCore(c.Endpoint, opt)
	if err != nil {
		return nil, err
	}
	return &MinioManager{
		Bucket:     c.Buck<PERSON>,
		Client:     cli,
		CoreClient: core,
	}, nil
}

func (m *MinioManager) GeneratePresignedUrl(objectKey string) string {
	url, err := m.Client.PresignedGetObject(context.Background(), m.<PERSON>, objectKey, ExpirationPeriodInSeconds*time.Second, nil)
	if err != nil {
		return ""
	}
	return url.String()
}

func (m *MinioManager) GetObject(objectKey string) (io.ReadCloser, error) {
	o, err := m.Client.GetObject(context.Background(), m.Bucket, objectKey, minio.GetObjectOptions{})
	if err != nil {
		return nil, err
	}
	return o, nil
}

func (m *MinioManager) ListObjects(args *ListObjectsArgs) (*ListObjectsResult, error) {
	if args == nil {
		args = &ListObjectsArgs{}
	}
	objLists, err := m.CoreClient.ListObjects(m.Bucket, args.Prefix, args.Marker, args.Delimiter, args.MaxKeys)
	if err != nil {
		return nil, err
	}
	return m.WrapperListObjectResult(&objLists), nil
}

func (m *MinioManager) Upload(byteArray []byte, objectKey string) (string, error) {
	buf := bytes.NewBuffer(byteArray)
	info, err := m.Client.PutObject(context.Background(), m.Bucket, objectKey, buf, int64(len(byteArray)), minio.PutObjectOptions{})
	if err != nil {
		return "", err
	}
	return strings.Trim(info.ETag, "\""), nil
}

func (m *MinioManager) DeleteObject(objectKey string) error {
	return m.Client.RemoveObject(context.Background(), m.Bucket, objectKey, minio.RemoveObjectOptions{})
}

func (m *MinioManager) DeleteMultiObjects(keys DeleteMultipleObjectKeys) error {
	objectsCh := make(chan minio.ObjectInfo, 0)
	go func() {
		defer close(objectsCh)
		for _, k := range keys {
			objectsCh <- minio.ObjectInfo{
				Key: k,
			}
		}
	}()
	removeErrors := m.Client.RemoveObjects(context.Background(), m.Bucket, objectsCh, minio.RemoveObjectsOptions{})
	for e := range removeErrors {
		if e.Err != nil {
			return e.Err
		}
	}
	return nil
}

func (m *MinioManager) BasicCopyObject(desObject, srcObject string) error {
	if _, err := m.Client.CopyObject(context.Background(),
		minio.CopyDestOptions{Bucket: m.Bucket, Object: desObject},
		minio.CopySrcOptions{Bucket: m.Bucket, Object: srcObject}); err != nil {
		return err
	}
	return nil
}

func (m *MinioManager) CopyObjectFromTrash(desObject string) error {
	srcObject := ".trash/" + desObject
	if _, err := m.Client.CopyObject(context.Background(),
		minio.CopyDestOptions{Bucket: m.Bucket, Object: desObject},
		minio.CopySrcOptions{Bucket: m.Bucket, Object: srcObject}); err != nil {
		return err
	}
	return nil
}

func (m *MinioManager) WrapperListObjectResult(res *minio.ListBucketResult) *ListObjectsResult {
	listRes := ListObjectsResult{
		Name:        res.Name,
		Prefix:      res.Prefix,
		Delimiter:   res.Delimiter,
		Marker:      res.Marker,
		NextMarker:  res.NextMarker,
		MaxKeys:     int(res.MaxKeys),
		IsTruncated: res.IsTruncated,
	}
	for _, prefix := range res.CommonPrefixes {
		listRes.CommonPrefixes = append(listRes.CommonPrefixes, CommonPrefix(prefix))
	}
	for _, content := range res.Contents {
		listRes.Contents = append(listRes.Contents, *m.WrapperObjectInfo(&content))
	}

	return &listRes
}

func (m *MinioManager) WrapperObjectInfo(res *minio.ObjectInfo) *ObjectInfo {
	obj := ObjectInfo{
		LastModified: res.LastModified.String(),
		ETag:         res.ETag,
		Size:         int(res.Size),
		StorageClass: res.StorageClass,
		Owner:        *m.WrapperOwner(&res.Owner),
	}
	return &obj
}

func (m *MinioManager) WrapperOwner(res *minio.Owner) *Owner {
	owner := Owner{
		ID:          res.ID,
		DisplayName: res.DisplayName,
	}
	return &owner
}
