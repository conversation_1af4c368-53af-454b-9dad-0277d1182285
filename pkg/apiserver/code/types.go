package code

type DeleteMultipleObjectKeys = []string

type ListObjectsArgs struct {
	Delimiter string `json:"delimiter"`
	Marker    string `json:"marker"`
	MaxKeys   int    `json:"maxKeys"`
	Prefix    string `json:"prefix"`
}

type ListObjectsResult struct {
	Name           string         `json:"name"`
	Prefix         string         `json:"prefix"`
	Delimiter      string         `json:"delimiter"`
	Marker         string         `json:"marker"`
	NextMarker     string         `json:"nextMarker,omitempty"`
	MaxKeys        int            `json:"maxKeys"`
	IsTruncated    bool           `json:"isTruncated"`
	Contents       []ObjectInfo   `json:"contents"`
	CommonPrefixes []CommonPrefix `json:"commonPrefixes"`
}

type ObjectInfo struct {
	Key          string `json:"key"`
	LastModified string `json:"lastModified"`
	ETag         string `json:"eTag"`
	Size         int    `json:"size"`
	StorageClass string `json:"storageClass"`
	Owner        Owner  `json:"owner"`
}

type CommonPrefix struct {
	Prefix string `json:"prefix"`
}

// Owner name.
type Owner struct {
	DisplayName string
	ID          string
}
