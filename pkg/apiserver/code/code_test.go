package code

import (
	"crypto/sha256"
	"encoding/base64"
	"testing"

	"github.com/stretchr/testify/assert"

	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/squashfs"

	"icode.baidu.com/baidu/faas/kun/cmd/apiserver/options"
)

func TestNewBosM(t *testing.T) {
	NewCodeManager(options.NewCodeOptions(), squashfs.NewSquashFsClient(squashfs.NewSquashFsOptions()))
}

func TestFaasUploadCode(t *testing.T) {
	cm := MockCode()
	codeSha256, _, codeSize, _, _ := cm.FaasUploadCode(MockBytes, "fname", "123")
	assert.Equal(t, int32(len(MockBytes)), codeSize)
	hasher := sha256.New()
	hasher.Write(MockBytes)
	shaByte := hasher.Sum(nil)
	assert.Equal(t, base64.StdEncoding.EncodeToString(shaByte), codeSha256)
}

func TestFaasUploadLayerCode(t *testing.T) {
	cm := MockCode()
	codeSha256, codeSize, _, _ := cm.FaasUploadLayerCode(MockBytes, "fname", "123")
	assert.Equal(t, int64(len(MockBytes)), codeSize)
	hasher := sha256.New()
	hasher.Write(MockBytes)
	shaByte := hasher.Sum(nil)
	assert.Equal(t, base64.StdEncoding.EncodeToString(shaByte), codeSha256)
}

func TestFaasRemoveLayerCode(t *testing.T) {
	cm := MockCode()
	err := cm.FaasRemoveLayerCode("layer", "fname", "123")
	assert.Nil(t, err)
}

func TestFaasCodeDownloadUrl(t *testing.T) {
	cm := MockCode()
	hasher := sha256.New()
	hasher.Write(MockBytes)
	shaByte := hasher.Sum(nil)
	_, err := cm.FaasCodeDownloadUrl("fname", "123", base64.StdEncoding.EncodeToString(shaByte), "456")
	assert.Equal(t, nil, err)
}

func TestFaasSquashFsCodeDownloadUrl(t *testing.T) {
	cm := MockCode()
	hasher := sha256.New()
	hasher.Write(MockBytes)
	shaByte := hasher.Sum(nil)
	_, err := cm.FaasSquashFsCodeDownloadUrl("fname", "123", base64.StdEncoding.EncodeToString(shaByte))
	assert.Equal(t, nil, err)

}
func TestFaasUploadBlueprintZip(t *testing.T) {
	cm := MockCode()
	_, err := cm.FaasUploadBlueprintZip(MockBytes, "test.a")
	assert.Equal(t, nil, err)
}

func TestFaasGetCodeObject(t *testing.T) {
	cm := MockCode()
	hasher := sha256.New()
	hasher.Write(MockBytes)
	shaByte := hasher.Sum(nil)
	_, err := cm.FaasGetCodeObject("fname", "123", base64.StdEncoding.EncodeToString(shaByte), "456")
	assert.Equal(t, nil, err)
}

func TestFaasBpCodeDownloadUrl(t *testing.T) {
	cm := MockCode()
	url := cm.FaasBpCodeDownloadUrl("123/fname_456.zip")
	assert.NotEqual(t, url, "")
}

func TestFaasLayerDownloadUrl(t *testing.T) {
	cm := MockCode()
	url := cm.FaasLayerDownloadUrl("layerName", "uid", "codeID")
	assert.NotEqual(t, url, "")
}

func TestFaasFunctionLayersSquashFsDownloadUrl(t *testing.T) {
	cm := MockCode()
	url := cm.FaasFunctionLayersSquashFsDownloadUrl("functionName", "uid", "sha256")
	assert.NotEqual(t, url, "")
}
