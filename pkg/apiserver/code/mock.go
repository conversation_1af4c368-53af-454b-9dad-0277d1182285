package code

import (
	"bytes"
	"errors"
	"fmt"
	"io"
	"io/ioutil"
	"net/http"
	"net/http/httptest"
	"net/url"
	"strconv"
	"sync"

	"github.com/baidubce/bce-sdk-go/bce"
	"github.com/baidubce/bce-sdk-go/services/bos/api"
	"github.com/google/uuid"
	"github.com/gorilla/mux"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/squashfs"
	"icode.baidu.com/baidu/faas/kun/pkg/util/zip"
)

var (
	mockBos *MockBos
	mu      sync.Mutex
)

func MockCode() CodeManagerInterface {
	code := NewMockBos()
	manager := CodeManager{
		ObjectStorageInterface: code,
		Squash: squashfs.NewSquashFsClient(&squashfs.SquashFsOptions{
			MakeSquashFsCodeSize: 5242880,
			MakeSquashFsFileNum:  0,
		}),
	}
	mockManager := MockCodeManager{
		Info:        "lz",
		CodeManager: manager,
	}
	return &mockManager
}

type MockCodeManager struct {
	Info string
	CodeManager
}

func (m *MockCodeManager) FaasUploadCode(byteArray []byte, functionName string, uid string) (codeSha256 string, squashFsSha256 *string, codeSize int32, codeID string, err error) {
	meta := generateCodeMeta(byteArray)
	codeSha256 = meta["CodeSha256"].(string)
	codeSize = int32(meta["CodeSize"].(int64))
	codeID = uuid.New().String()

	zip.GetUnzipFileNum(byteArray, int64(codeSize))
	if err != nil {
		return
	}

	newObjectKey := GetFuncZipKey(uid, functionName, codeID)
	if _, err = m.ObjectStorageInterface.Upload(byteArray, newObjectKey); err != nil {
		return
	}
	var squashFsSha256Str = ""
	squashFsSha256 = &squashFsSha256Str
	return
}

func (m *MockCodeManager) FaasUploadCodeSqfs(byteArray []byte, codeSize int32, squashFsKey string) (codeSha256 string, err error) {
	return "", nil
}
func (m *MockCodeManager) FaasUploadLayerSqfs(byteArray []byte, codeSize int32, layerSquashFsKey string) (codeSha256 string, err error) {
	return "", nil
}

// func (m *MockCodeManager) FaasUploadCodeSqfs(byteArray []byte, codeSize int32, squashFsKey string) {
// 	return
// }

type MockBos struct {
	port int
}

func NewMockBos() *MockBos {
	mu.Lock()
	defer mu.Unlock()
	if mockBos != nil {
		return mockBos
	}
	router := newCodeMockRouter()
	testserver := httptest.NewServer(router)
	baseURL, _ := url.Parse(testserver.URL)
	port, _ := strconv.Atoi(baseURL.Port())
	mockBos = &MockBos{port: port}
	return mockBos
}

func (b *MockBos) GeneratePresignedUrl(objectKey string) string {
	return fmt.Sprintf("http://127.0.0.1:%d/download/zipfile", b.port)
}

func (b *MockBos) GetObject(objectKey string) (io.ReadCloser, error) {
	return ioutil.NopCloser(bytes.NewBuffer(MockBytes)), nil
}

func (b *MockBos) ListObjects(*ListObjectsArgs) (*ListObjectsResult, error) {
	return &ListObjectsResult{
		Name:        "faastest",
		Prefix:      "",
		Delimiter:   "/",
		Marker:      "",
		NextMarker:  "",
		MaxKeys:     1000,
		IsTruncated: false,
		Contents: []ObjectInfo{
			{
				Key:          "uid_0/fname_5afb2719-b470-4bce-a9d8-11379f8c57ce.zip",
				LastModified: "2019-03-12T17:50:30Z",
			},
			{
				Key:          "uid_0/",
				LastModified: "2019-03-12T17:50:30Z",
			},
			{
				Key:          "uid_0/fname_456.zip",
				LastModified: "2019-03-12T17:50:30Z",
			},
		},
		CommonPrefixes: []CommonPrefix{
			{Prefix: "c7ac82ae14ef42d1a4ffa3b2ececa17f"},
			{Prefix: "611f366b29f64d04ad2ed130d32835d2"},
			{Prefix: "blueprints"},
			{Prefix: ".trash"},
			{Prefix: "611f3xxxxx-12344ad2ed130d32835d2"},
		},
	}, nil
}

func (b *MockBos) Upload(byteArray []byte, objectKey string) (string, error) {
	return "", nil
}

func (b *MockBos) DeleteObject(objectKey string) error {
	return nil
}

func (b *MockBos) DeleteMultiObjects(keys DeleteMultipleObjectKeys) error {
	return nil
}

func (b *MockBos) BasicCopyObject(object, srcObject string) error {
	return nil
}

func (b *MockBos) CopyObjectFromTrash(object string) error {
	return nil
}

func (b *MockBos) GetConfig() *bce.BceClientConfiguration {
	return &bce.BceClientConfiguration{
		Region:   "bj",
		Endpoint: "127.0.0.1",
	}
}

func (b *MockBos) GetObjectMeta(objectKey string) (*api.GetObjectMetaResult, error) {
	return &api.GetObjectMetaResult{}, nil
}

func (b *MockBos) ListBuckets() (*api.ListBucketsResult, error) {
	return &api.ListBucketsResult{}, nil
}

func newCodeMockRouter() *mux.Router {

	router := mux.NewRouter()
	var routers = []struct {
		Verb string
		Path string
		f    func(http.ResponseWriter, *http.Request)
	}{
		{
			Verb: "GET",
			Path: "/download/zipfile",
			f: func(writer http.ResponseWriter, request *http.Request) {
				writer.WriteHeader(http.StatusOK)
				writer.Write(MockBytes)
			},
		},
	}

	for _, v := range routers {
		router.HandleFunc(v.Path, v.f).Methods(v.Verb).Name(v.Path)
	}

	return router
}

// MockBytes 一个简单zip文件的字节码
var MockBytes = []byte{
	80, 75, 3, 4, 20, 0, 8, 0, 8, 0, 123, 137, 170, 76, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 13, 0, 16, 0, 100, 117, 101, 114, 111, 115, 98, 111, 116, 46, 116,
	120, 116, 85, 88, 12, 0, 217, 12, 244, 90, 217, 12, 244, 90, 246, 239, 186, 158, 75, 41, 77, 45, 202, 47, 6, 0, 80, 75, 7, 8, 177, 111, 113, 99, 8, 0, 0, 0,
	6, 0, 0, 0, 80, 75, 3, 4, 10, 0, 0, 0, 0, 0, 130, 137, 170, 76, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 9, 0, 16, 0, 95, 95, 77, 65, 67, 79, 83, 88, 47, 85, 88,
	12, 0, 227, 12, 244, 90, 227, 12, 244, 90, 246, 239, 186, 158, 80, 75, 3, 4, 20, 0, 8, 0, 8, 0, 123, 137, 170, 76, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 24, 0,
	16, 0, 95, 95, 77, 65, 67, 79, 83, 88, 47, 46, 95, 100, 117, 101, 114, 111, 115, 98, 111, 116, 46, 116, 120, 116, 85, 88, 12, 0, 217, 12, 244, 90, 217, 12, 244,
	90, 246, 239, 186, 158, 99, 96, 21, 99, 103, 96, 98, 96, 240, 77, 76, 86, 240, 15, 86, 136, 80, 128, 2, 144, 24, 3, 39, 16, 27, 49, 48, 48, 238, 0, 210, 64,
	62, 227, 43, 6, 162, 128, 99, 72, 72, 16, 132, 5, 210, 193, 104, 1, 100, 108, 66, 83, 194, 2, 21, 231, 103, 96, 16, 79, 206, 207, 213, 75, 44, 40, 200, 73, 213,
	11, 73, 173, 40, 113, 205, 75, 206, 79, 201, 204, 75, 135, 232, 119, 7, 18, 2, 12, 12, 82, 8, 53, 57, 137, 197, 37, 165, 197, 169, 41, 41, 137, 37, 169, 202, 1,
	193, 80, 123, 194, 129, 132, 22, 3, 131, 10, 66, 93, 110, 106, 73, 34, 80, 77, 162, 85, 124, 182, 175, 139, 103, 73, 106, 110, 104, 113, 106, 81, 72, 98, 122, 49,
	88, 125, 35, 144, 200, 100, 96, 48, 199, 162, 30, 168, 220, 39, 49, 41, 53, 39, 190, 188, 212, 40, 191, 194, 44, 181, 56, 53, 171, 180, 52, 167, 52, 199, 44, 183,
	176, 36, 173, 48, 41, 49, 49, 35, 23, 168, 185, 180, 36, 77, 215, 194, 218, 208, 216, 196, 200, 208, 220, 210, 194, 228, 18, 207, 151, 40, 144, 193, 157, 167, 98,
	69, 64, 116, 82, 65, 78, 102, 113, 137, 129, 193, 2, 14, 168, 3, 25, 161, 30, 135, 209, 48, 192, 249, 233, 136, 123, 83, 193, 165, 64, 193, 85, 223, 190, 218, 177,
	207, 205, 72, 255, 118, 87, 151, 113, 78, 188, 215, 234, 164, 169, 159, 52, 206, 243, 76, 125, 177, 233, 227, 35, 151, 143, 167, 79, 196, 156, 173, 158, 207, 230,
	155, 253, 213, 130, 173, 150, 231, 140, 128, 63, 155, 208, 85, 221, 210, 221, 29, 123, 31, 214, 197, 28, 241, 156, 51, 209, 123, 207, 177, 67, 154, 46, 210, 57, 7,
	254, 184, 253, 114, 191, 86, 49, 171, 113, 167, 197, 226, 159, 165, 158, 30, 49, 39, 230, 229, 29, 255, 156, 234, 188, 227, 211, 249, 107, 0, 80, 75, 7, 8, 211, 146,
	124, 33, 92, 1, 0, 0, 234, 1, 0, 0, 80, 75, 1, 2, 21, 3, 20, 0, 8, 0, 8, 0, 123, 137, 170, 76, 177, 111, 113, 99, 8, 0, 0, 0, 6, 0, 0, 0, 13, 0, 12, 0, 0, 0, 0, 0, 0,
	0, 0, 64, 164, 129, 0, 0, 0, 0, 100, 117, 101, 114, 111, 115, 98, 111, 116, 46, 116, 120, 116, 85, 88, 8, 0, 217, 12, 244, 90, 217, 12, 244, 90, 80, 75, 1, 2, 21, 3,
	10, 0, 0, 0, 0, 0, 130, 137, 170, 76, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 9, 0, 12, 0, 0, 0, 0, 0, 0, 0, 0, 64, 253, 65, 83, 0, 0, 0, 95, 95, 77, 65, 67, 79, 83, 88,
	47, 85, 88, 8, 0, 227, 12, 244, 90, 227, 12, 244, 90, 80, 75, 1, 2, 21, 3, 20, 0, 8, 0, 8, 0, 123, 137, 170, 76, 211, 146, 124, 33, 92, 1, 0, 0, 234, 1, 0, 0, 24, 0,
	12, 0, 0, 0, 0, 0, 0, 0, 0, 64, 164, 129, 138, 0, 0, 0, 95, 95, 77, 65, 67, 79, 83, 88, 47, 46, 95, 100, 117, 101, 114, 111, 115, 98, 111, 116, 46, 116, 120, 116, 85,
	88, 8, 0, 217, 12, 244, 90, 217, 12, 244, 90, 80, 75, 5, 6, 0, 0, 0, 0, 3, 0, 3, 0, 220, 0, 0, 0, 60, 2, 0, 0, 0, 0,
}

func MockFaultyCode() CodeManagerInterface {
	bos := NewMockFaultyBos()
	manager := &CodeManager{
		Squash:                 squashfs.NewSquashFsClient(squashfs.NewSquashFsOptions()),
		ObjectStorageInterface: bos,
	}
	return manager
}

type MockFaultyBos struct {
	port int
}

func NewMockFaultyBos() *MockFaultyBos {
	return &MockFaultyBos{
		port: 8888,
	}
}

func (b *MockFaultyBos) GeneratePresignedUrl(objectKey string) string {
	return fmt.Sprintf("http://127.0.0.1:%d/download/zipfile", b.port)
}

func (b *MockFaultyBos) GetObject(objectKey string) (io.ReadCloser, error) {
	return nil, errors.New("err")
}

func (b *MockFaultyBos) ListObjects(*ListObjectsArgs) (*ListObjectsResult, error) {
	return nil, errors.New("err")
}

func (b *MockFaultyBos) Upload(byteArray []byte, objectKey string) (string, error) {
	return "", errors.New("err")
}

func (b *MockFaultyBos) DeleteObject(objectKey string) error {
	return errors.New("err")
}

func (b *MockFaultyBos) DeleteMultiObjects(DeleteMultipleObjectKeys) error {
	return errors.New("err")
}

func (b *MockFaultyBos) BasicCopyObject(object, srcObject string) error {
	return errors.New("err")
}
func (b *MockFaultyBos) CopyObjectFromTrash(object string) error {
	return errors.New("err")
}

func (b *MockFaultyBos) GetObjectMeta(objectKey string) (*api.GetObjectMetaResult, error) {
	return nil, errors.New("err")
}

func (b *MockFaultyBos) ListBuckets() (*api.ListBucketsResult, error) {
	return nil, errors.New("err")
}

func (b *MockFaultyBos) GetConfig() *bce.BceClientConfiguration {
	return &bce.BceClientConfiguration{
		Region:   "bj",
		Endpoint: "127.0.0.1",
	}
}
