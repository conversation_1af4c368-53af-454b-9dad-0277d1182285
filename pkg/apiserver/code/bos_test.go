package code

import (
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/baidubce/bce-sdk-go/bce"
	"github.com/baidubce/bce-sdk-go/services/bos/api"
	"github.com/stretchr/testify/assert"

	"icode.baidu.com/baidu/faas/kun/cmd/apiserver/options"
)

func TestGeneratePresignedUrl(t *testing.T) {
	ts := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
		w.Write([]byte("json result"))
	}))

	b := initBosManager(ts.URL)
	url := b.GeneratePresignedUrl("key")
	assert.NotEqual(t, "", url)
}

func TestGetObject(t *testing.T) {
	ts := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.Write<PERSON>eader(http.StatusOK)
		w.Write([]byte("json result"))
	}))

	b := initBosManager(ts.URL)
	_, err := b.GetObject("key")
	assert.Nil(t, err)
}

func TestGetObjectMeta(t *testing.T) {
	ts := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
		w.Write([]byte("json result"))
	}))

	b := initBosManager(ts.URL)
	_, err := b.GetObjectMeta("key")
	assert.Nil(t, err)
}

func TestUpload(t *testing.T) {
	ts := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
		w.Write([]byte("json result"))
	}))

	b := initBosManager(ts.URL)
	_, err := b.Upload([]byte("bytes"), "key")
	assert.Nil(t, err)
}

func TestDelete(t *testing.T) {
	ts := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
		w.Write([]byte("json result"))
	}))

	b := initBosManager(ts.URL)
	err := b.DeleteObject("key")
	assert.Nil(t, err)
}

func TestGetConfig(t *testing.T) {
	ts := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		cfg := &bce.BceClientConfiguration{
			Region: "bj",
		}
		bytes, _ := json.Marshal(cfg)
		w.WriteHeader(http.StatusOK)
		w.Write([]byte(bytes))
	}))

	b := initBosManager(ts.URL)
	cfg := b.GetConfig()
	assert.Equal(t, "bj", cfg.Region)
}

func TestListObjects(t *testing.T) {
	res := api.ListObjectsResult{
		Name:        "faastest",
		Prefix:      "",
		Delimiter:   "/",
		Marker:      "",
		NextMarker:  "",
		MaxKeys:     1000,
		IsTruncated: false,
		Contents: []api.ObjectSummaryType{
			api.ObjectSummaryType{
				Key:          "uid_0/fname_5afb2719-b470-4bce-a9d8-11379f8c57ce.zip",
				LastModified: "2019-03-12T17:50:30Z",
			},
			api.ObjectSummaryType{
				Key:          "uid_0/",
				LastModified: "2019-03-12T17:50:30Z",
			},
			api.ObjectSummaryType{
				Key:          "uid_0/fname_456.zip",
				LastModified: "2019-03-12T17:50:30Z",
			},
		},
		CommonPrefixes: []api.PrefixType{
			api.PrefixType{Prefix: "c7ac82ae14ef42d1a4ffa3b2ececa17f"},
			api.PrefixType{Prefix: "611f366b29f64d04ad2ed130d32835d2"},
			api.PrefixType{Prefix: "blueprints"},
			api.PrefixType{Prefix: ".trash"},
			api.PrefixType{Prefix: "611f3xxxxx-12344ad2ed130d32835d2"},
		},
	}
	ts := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
		bytes, _ := json.Marshal(&res)
		w.Write(bytes)
	}))

	b := initBosManager(ts.URL)
	list, err := b.ListObjects(nil)
	t.Logf("list = %+v", list)
	assert.Nil(t, err)
	assert.NotNil(t, list.Contents)
	assert.NotNil(t, list.CommonPrefixes)
}

func TestListBuckets(t *testing.T) {
	ts := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
		bytes, _ := json.Marshal(&api.ListBucketsResult{})
		w.Write(bytes)
	}))

	b := initBosManager(ts.URL)
	_, err := b.ListBuckets()
	assert.Nil(t, err)
}

// TestDeleteMultiObjects 测试DeleteMultiObjects函数
// 参数：*testing.T - 类型为指针，表示测试对象，用于存储测试结果和错误信息
// 返回值：无返回值
func TestDeleteMultiObjects(t *testing.T) {
	ts := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
	}))

	b := initBosManager(ts.URL)
	res := b.DeleteMultiObjects(nil)
	assert.NotNil(t, res)
	assert.Contains(t, res.Error(), "delete multi objects failed")
}

func TestCopyObject(t *testing.T) {
	ts := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
		bytes, _ := json.Marshal(&api.CopyObjectResult{})
		w.Write(bytes)
	}))

	b := initBosManager(ts.URL)
	err := b.BasicCopyObject("obj", "srcObj")
	assert.Nil(t, err)
}

func TestCopyObjectFromTrash(t *testing.T) {
	ts := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
		bytes, _ := json.Marshal(&api.CopyObjectResult{})
		w.Write(bytes)
	}))

	b := initBosManager(ts.URL)
	err := b.CopyObjectFromTrash("key")
	assert.Nil(t, err)
}

func initBosManager(endpoint string) *BosManager {
	manager, _ := NewBosManager(
		&options.CodeConfiguration{
			Endpoint:        endpoint,
			Bucket:          "bucket",
			AccessKey:       "abc",
			SecretAccessKey: "abc",
		})
	return manager
}
