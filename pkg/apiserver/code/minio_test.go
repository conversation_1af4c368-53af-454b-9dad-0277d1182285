package code

import (
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"net/url"
	"testing"

	"github.com/minio/minio-go"

	"icode.baidu.com/baidu/faas/kun/cmd/apiserver/options"
)

func TestMinioManager_GeneratePresignedUrl(t *testing.T) {
	ts := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
	}))

	b := initMinioManager(ts.URL)
	b.GeneratePresignedUrl("key")
}

func TestMinioManager_GetObject(t *testing.T) {
	ts := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
		w.Write([]byte("json result"))
	}))

	b := initMinioManager(ts.URL)
	b.GetObject("key")
}

func TestMinioManager_Upload(t *testing.T) {
	ts := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
	}))

	b := initMinioManager(ts.URL)
	b.Upload([]byte("bytes"), "key")
}

func TestMinioManager_Delete(t *testing.T) {
	ts := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
		w.Write([]byte("json result"))
	}))

	b := initMinioManager(ts.URL)
	b.DeleteObject("key")
}

func TestMinioManager_ListObjects(t *testing.T) {
	ts := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
		bytes, _ := json.Marshal(&minio.ListBucketResult{})
		w.Write(bytes)
	}))

	b := initMinioManager(ts.URL)
	b.ListObjects(nil)
}

func TestMinioManager_DeleteMultiObjects(t *testing.T) {
	ts := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
	}))

	b := initMinioManager(ts.URL)
	b.DeleteMultiObjects(nil)
}

func TestMinioManager_CopyObject(t *testing.T) {
	ts := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
	}))

	b := initMinioManager(ts.URL)
	b.BasicCopyObject("obj", "srcObj")
}

func TestMinioManager_CopyObjectFromTrash(t *testing.T) {
	ts := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
	}))

	b := initMinioManager(ts.URL)
	b.CopyObjectFromTrash("key")
}

func initMinioManager(endpoint string) *MinioManager {
	urls, _ := url.Parse(endpoint)
	manager, err := NewMinioClient(
		&options.CodeConfiguration{
			Endpoint:        urls.Host,
			Bucket:          "bucket",
			AccessKey:       "abc",
			SecretAccessKey: "abc",
		})
	if err != nil {
		fmt.Printf("err = %+v", err)
	}
	return manager
}
