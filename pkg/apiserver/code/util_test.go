package code

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestGenerateCodeMeta(t *testing.T) {
	meta := generateCodeMeta([]byte("xxxxxxxxxxxxxxxxx"))
	t.Log(meta)
}

func TestGetFuncZipKey(t *testing.T) {
	MockCode()
	k := GetFuncZipKey("123", "fname", "456")
	assert.Equal(t, k, "123/fname_456.zip")
}
func TestGetFunctionLayersSquashFsKey(t *testing.T) {
	MockCode()
	k := GetFunctionLayersSquashFsKey("uid", "functionName", "dPOIzlT3bZdUmSeO+5TcSREdG2kMjt//Qo/e1OT8MoU=")
	assert.Equal(t, "uid/layer_func_functionName_dPOIzlT3bZdUmSeO+5TcSREdG2kMjt--Qo-e1OT8MoU=.sqfs", k)
}

func TestGetLayerSquashFsKey(t *testing.T) {
	MockCode()
	k := GetLayerSquashFsKey("uid", "layerName", "codeID")
	assert.Equal(t, k, "uid/layer/layerName_codeID.sqfs")
}

func TestGetFuncSquashfsKey(t *testing.T) {
	MockCode()
	k := GetFuncSquashfsKey("uid", "funcName", "codeID")
	t.Log(k)
	assert.Equal(t, k, "uid/funcName_codeID.sqfs")
}
func TestGetLayerKey(t *testing.T) {
	MockCode()
	k := GetLayerKey("uid", "layerName", "codeID")
	assert.Equal(t, k, "uid/layer/layerName_codeID.zip")
}
