package code

import (
	"crypto/sha256"
	"fmt"
	"strings"

	"icode.baidu.com/baidu/faas/kun/pkg/util/base64"
)

func generateCodeMeta(byteArray []byte) (meta map[string]interface{}) {
	meta = make(map[string]interface{})
	hasher := sha256.New()
	hasher.Write(byteArray)
	shaByte := hasher.Sum(nil)
	meta["CodeSha256"] = base64.EncodeToString(shaByte)
	meta["CodeSize"] = int64(len(byteArray))
	meta["x_sha256"] = fmt.Sprintf("%x", shaByte)
	return meta
}

func GetFuncZipKey(uid, functionName, codeID string) string {
	return fmt.Sprintf("%s/%s_%s.zip", uid, functionName, codeID)
}

func GetFuncSquashfsKey(uid, functionName, codeID string) string {
	return fmt.Sprintf("%s/%s_%s.sqfs", uid, functionName, codeID)
}

func GetLayerKey(uid, layerName, codeID string) string {
	return fmt.Sprintf("%s/layer/%s_%s.zip", uid, layerName, codeID)
}

func GetLayerSquashFsKey(uid, layerName, codeID string) string {
	return fmt.Sprintf("%s/layer/%s_%s.sqfs", uid, layerName, codeID)
}

func GetFunctionLayersSquashFsKey(uid, functionName, layerSha256 string) string {
	sha := strings.Map(func(r rune) rune {
		if r == '/' {
			return '-'
		}
		return r
	}, layerSha256)

	return fmt.Sprintf("%s/layer_func_%s_%s.sqfs", uid, functionName, sha)
}
