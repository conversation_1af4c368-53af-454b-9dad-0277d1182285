package maindata

import (
	"bytes"
	"encoding/json"
	"errors"
	"fmt"
	"io/ioutil"
	"net/http"

	"icode.baidu.com/baidu/faas/kun/pkg/api"
	"icode.baidu.com/baidu/faas/kun/pkg/bce/iam"
	"icode.baidu.com/baidu/faas/kun/pkg/util/logs"
)

var MaindataManager MaindataInterface

type Maindata struct{}

func NewMaindata() *Maindata {
	return &Maindata{}
}

type MaindataInterface interface {
	CreateNewFreeUser(accountId string, requestId string, MaindataEndpoint string, iamClient iam.ClientInterface) (*api.FreeNewUser, error)
	UpdateNewFreeUser(accountId string, requestId string, region string, freepackageOrders string, MaindataEndpoint string, iamClient iam.ClientInterface) (*api.FreeNewUser, error)
	QueryNewFreeUser(accountId string, requestId string, MaindataEndpoint string, iamClient iam.ClientInterface) (*api.FreeNewUser, error)
	DeleteNewFreeUser(accountId string, requestId string, MaindataEndpoint string, iamClient iam.ClientInterface) error
}

func NewMaindataManager() MaindataInterface {
	MaindataManager = NewMaindata()
	return MaindataManager
}

// CreateNewFreeUser 创建免费新用户
func (d *Maindata) CreateNewFreeUser(accountId string, requestId string, MaindataEndpoint string, iamClient iam.ClientInterface) (*api.FreeNewUser, error) {
	body := new(bytes.Buffer)
	req, err := http.NewRequest(http.MethodPost, MaindataEndpoint+"/v1/maindata/newusers/"+accountId, body)
	req.Header.Set(api.HeaderXAuthToken, "cfc-auth-2018")
	client := &http.Client{}
	rsp, err := client.Do(req)
	if err != nil {
		logs.Errorf("create free new user failed, requestId: %s. err: %v", requestId, err)
		return nil, err
	}
	defer rsp.Body.Close()
	if rsp.StatusCode != http.StatusCreated {
		resp := &api.MaindataResp{}
		json.NewDecoder(rsp.Body).Decode(resp)
		err = errors.New(fmt.Sprintf("create free new user failed. requestId: %s, code: %d, message: %s", requestId, rsp.StatusCode, resp.Message))
		return nil, err
	}

	res := new(api.FreeNewUser)
	rspBody, _ := ioutil.ReadAll(rsp.Body)
	json.Unmarshal(rspBody, &res)
	return res, nil
}

func (d *Maindata) UpdateNewFreeUser(accountId string, requestId string, region string, freepackageOrders string, MaindataEndpoint string, iamClient iam.ClientInterface) (*api.FreeNewUser, error) {
	updateReq := &api.UpdateFreeNewUserRequest{
		Region:            region,
		FreepackageOrders: freepackageOrders,
	}
	body := new(bytes.Buffer)
	err := json.NewEncoder(body).Encode(updateReq)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest(http.MethodPut, MaindataEndpoint+"/v1/maindata/newusers/"+accountId, body)
	req.Header.Set(api.HeaderXAuthToken, "cfc-auth-2018")
	client := &http.Client{}
	rsp, err := client.Do(req)
	if err != nil {
		logs.Errorf("update free new user failed, requestId: %s. err: %v", requestId, err)
		return nil, err
	}
	defer rsp.Body.Close()

	if rsp.StatusCode != http.StatusResetContent {
		resp := &api.MaindataResp{}
		json.NewDecoder(rsp.Body).Decode(resp)
		err = errors.New(fmt.Sprintf("update free new user failed. requestId: %s, code: %d, message: %s", requestId, rsp.StatusCode, resp.Message))
		return nil, err
	}

	res := new(api.FreeNewUser)
	rspBody, _ := ioutil.ReadAll(rsp.Body)
	json.Unmarshal(rspBody, &res)
	return res, nil
}

func (d *Maindata) QueryNewFreeUser(accountId string, requestId string, MaindataEndpoint string, iamClient iam.ClientInterface) (*api.FreeNewUser, error) {
	body := new(bytes.Buffer)
	req, err := http.NewRequest(http.MethodGet, MaindataEndpoint+"/v1/maindata/newusers/"+accountId, body)
	req.Header.Set(api.HeaderXAuthToken, "cfc-auth-2018")
	client := &http.Client{}
	rsp, err := client.Do(req)
	if err != nil {
		logs.Errorf("query free new user failed, requestId: %s. err: %v", requestId, err)
		return nil, err
	}
	defer rsp.Body.Close()
	if rsp.StatusCode != http.StatusOK && rsp.StatusCode != http.StatusNotFound {
		resp := &api.MaindataResp{}
		json.NewDecoder(rsp.Body).Decode(resp)
		err = errors.New(fmt.Sprintf("query free new user failed. requestId: %s, code: %d, message: %s", requestId, rsp.StatusCode, resp.Message))
		return nil, err
	}
	// 老用户不存在，直接返回
	if rsp.StatusCode == http.StatusNotFound {
		return nil, nil
	}
	resp := &api.FreeNewUser{}

	err = json.NewDecoder(rsp.Body).Decode(resp)
	if err != nil {
		return nil, err
	}
	return resp, nil
}

func (d *Maindata) DeleteNewFreeUser(accountId string, requestId string, MaindataEndpoint string, iamClient iam.ClientInterface) error {
	body := new(bytes.Buffer)
	req, err := http.NewRequest(http.MethodDelete, MaindataEndpoint+"/v1/maindata/newusers/"+accountId, body)
	req.Header.Set(api.HeaderXAuthToken, "cfc-auth-2018")
	client := &http.Client{}
	rsp, err := client.Do(req)
	if err != nil {
		logs.Errorf("delete free new user failed, requestId: %s. err: %v", requestId, err)
		return err
	}
	defer rsp.Body.Close()
	if rsp.StatusCode != http.StatusOK {
		resp := &api.MaindataResp{}
		json.NewDecoder(rsp.Body).Decode(resp)
		err = errors.New(fmt.Sprintf("delete free new user failed. requestId: %s, code: %d, message: %s", requestId, rsp.StatusCode, resp.Message))
		return err
	}
	return nil
}
