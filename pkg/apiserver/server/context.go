package server

import (
	"github.com/emicklei/go-restful"
	"github.com/jinzhu/gorm"
	genericServer "icode.baidu.com/baidu/faas/kun/pkg/server"
)

type ApiServerContext struct {
	genericServer.Context
	DbTransaction *gorm.DB
}

func BuildContext(request *restful.Request, response *restful.Response) *ApiServerContext {
	return &ApiServerContext{
		Context:       *genericServer.BuildContext(request, response, ""),
		DbTransaction: nil,
	}
}

func WrapRestRouteFunc(h func(*ApiServerContext)) restful.RouteFunction {
	return func(request *restful.Request, response *restful.Response) {
		c := BuildContext(request, response)
		h(c)
	}
}
