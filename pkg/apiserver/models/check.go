package models

import (
	"archive/zip"
	"bytes"
	"encoding/json"
	"errors"
	"fmt"
	"regexp"
	"strconv"
	"strings"

	restful "github.com/emicklei/go-restful"

	"icode.baidu.com/baidu/faas/kun/pkg/api"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/dao"
	apiErr "icode.baidu.com/baidu/faas/kun/pkg/apiserver/error"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/global"
	"icode.baidu.com/baidu/faas/kun/pkg/bce/vpc"
	kunErr "icode.baidu.com/baidu/faas/kun/pkg/error"
	"icode.baidu.com/baidu/faas/kun/pkg/util/bytefmt"
)

const (
	StrLenIllegal          = "the length of %s is illegal, must in %d~%d"
	IntLenIllegal          = "the size of %d is illegal, must in %d~%d"
	AliasNameIllegal       = "Alias Name Must Match (?!^[0-9]+$)([a-zA-Z0-9-_]+)"
	MultipleIllegal        = "the length of %d is illegal, must is Multiple of %d"
	EnvLenIllegal          = "the size of Environment is too large, must smaller than 4 KB"
	ZippedCodeSizeExceed   = "the size of zipped code size is too large, must smaller than 150 MB"
	UnzippedCodeSizeExceed = "the size of unzipped code size is too large, must smaller than 250 MB"
	TotalCodeSizeExceed    = "you have exceeded the maximum account function total code size limit, which is %s"
)

var (
	regPureDigit = regexp.MustCompile(`^([0-9]+)$`)
)

func CheckPtrString(s *string, min, max int) error {
	if s == nil {
		return nil
	}
	l := len(*s)

	if l < min || l > max {
		errStr := fmt.Sprintf(StrLenIllegal, *s, min, max)
		return errors.New(errStr)
	}
	return nil
}

func CheckPtrIntSize(s *int, min, max int) error {
	if s == nil {
		return nil
	}

	if *s < min || *s > max {
		errStr := fmt.Sprintf(IntLenIllegal, *s, min, max)
		return errors.New(errStr)
	}
	return nil
}

func CheckFunctionTimeout(s *int, min, max, maxConfigTimeout int) error {
	if s == nil {
		return nil
	}

	if maxConfigTimeout > max {
		if *s < min || *s > maxConfigTimeout {
			errStr := fmt.Sprintf(IntLenIllegal, *s, min, maxConfigTimeout)
			return errors.New(errStr)
		}
	} else {
		if *s < min || *s > max {
			errStr := fmt.Sprintf(IntLenIllegal, *s, min, max)
			return errors.New(errStr)
		}
	}
	return nil
}

func CheckPodConcurrentQuota(s *int, max int) error {
	if s == nil || *s == 0 {
		s = &api.DefaultPodConcurrentQuotaMax
		return nil
	}

	if *s > max || *s < 0 {
		errStr := fmt.Sprintf("you have exceeded the maximum account pod concurrent quota limit, which is %d", max)
		return errors.New(errStr)
	}
	return nil
}

// 校验是否为某个值的倍数
func CheckMultiple(s *int, base int) (int, error) {
	if s == nil {
		return 0, nil
	}
	if *s%base == 0 {
		return *s / base, nil
	}
	return 0, fmt.Errorf(MultipleIllegal, *s, base)
}

func CheckAliasName(n string) error {
	if regPureDigit.MatchString(n) {
		return errors.New(AliasNameIllegal)
	}

	return nil
}

func Max(x, y int64) int64 {
	if x > y {
		return x
	}
	return y
}

func Min(x, y int64) int64 {
	if x < y {
		return x
	}
	return y
}

func CheckVersionAndAlias(req *restful.Request) bool {
	fName := req.PathParameter("FunctionName")
	res := strings.Split(fName, ":")

	qualifier := req.QueryParameter("Qualifier")
	return len(res) == 1 && len(qualifier) == 0
}

// 环境变量 < 4 KB
func CheckEnvSize(envStr string) error {
	if len(envStr) > int(api.DefaultEnvStrSizeLimit) {
		return errors.New(EnvLenIllegal)
	}
	return nil
}

/*
解压前的大小 < 150 MB
解压后的大小 < 500 MB
账户函数和层总大小 < 75 GB
*/
func CheckCodeSize(vtype string, value interface{}, codeZipFile []byte) error {
	zipfileSize := len(codeZipFile)
	if zipfileSize == 0 {
		return nil
	}

	if err := CheckZippedSize(zipfileSize); err != nil {
		return err
	}

	uncompressedCodeSize, err := GetUncompressedCodeSize(codeZipFile, zipfileSize)
	if err != nil {
		return err
	}

	if err := CheckUnzippedSize(uncompressedCodeSize); err != nil {
		return err
	}

	var (
		uid string
		f   *dao.Function
	)
	switch value.(type) {
	case *dao.Function:
		f = value.(*dao.Function)
		uid = f.Uid
	case *api.Layer:
		layer := value.(*api.Layer)
		uid = layer.Uid
		layer.UncompressedCodeSize = int64(uncompressedCodeSize)

	default:
		return kunErr.NewServiceException("check zip file error", err)
	}

	if err := CheckTotalSize(vtype, uid, f, zipfileSize); err != nil {
		return err
	}

	return nil
}

/*
解压前的大小 < 150 MB
解压后的大小 < 500 MB
账户函数和层总大小 < 75 GB
*/
func CheckCodeSizeLayer(vtype string, value interface{}, codeZipFile []byte) error {
	zipfileSize := len(codeZipFile)
	if zipfileSize == 0 {
		return nil
	}
	if zipfileSize > int(api.DefaultLayerCodeSizeLimitZipped) {
		return apiErr.NewCodeStorageExceededException("zipped code size too large", nil)
	}

	uncompressedCodeSize, err := GetUncompressedCodeSize(codeZipFile, zipfileSize)
	if err != nil {
		return err
	}

	if err := CheckUnzippedSize(uncompressedCodeSize); err != nil {
		return err
	}

	layer := value.(*api.Layer)
	uid := layer.Uid
	layer.UncompressedCodeSize = int64(uncompressedCodeSize)

	if err := CheckTotalSize(vtype, uid, nil, zipfileSize); err != nil {
		return err
	}

	return nil
}

func CheckZippedSize(zipfileSize int) error {
	if zipfileSize > int(api.DefaultCodeSizeLimitZipped) {
		return apiErr.NewCodeStorageExceededException("", errors.New(ZippedCodeSizeExceed))
	}
	return nil
}

func GetUncompressedCodeSize(byteArray []byte, zipfileSize int) (uint64, error) {
	var uncompressedCodeSize uint64
	zipReader, err := zip.NewReader(bytes.NewReader(byteArray), int64(zipfileSize))
	if err != nil {
		return 0, apiErr.NewInitFuncMetaException("read function zip file failed", "", err)
	}

	for _, f := range zipReader.File {
		uncompressedCodeSize += f.UncompressedSize64
	}
	return uncompressedCodeSize, nil
}

func CheckUnzippedSize(uncompressedCodeSize uint64) error {
	if uncompressedCodeSize > api.DefaultCodeSizeLimitUnzipped {
		return apiErr.NewCodeStorageExceededException(UnzippedCodeSizeExceed, nil)
	}
	return nil
}

// 根据vtype区别计算已有codesize
func CheckTotalSize(vtype string, uid string, reqFunc *dao.Function, zipfileSize int) error {
	if zipfileSize == 0 {
		return nil
	}

	funcs, err := dao.FindFunc(&dao.Function{Uid: uid})
	if err != nil {
		return kunErr.NewServiceException("", err)
	}

	var sum uint64
	if vtype == "create" {
		for _, f := range *funcs {
			sum += uint64(f.CodeSize)
		}
	} else {
		for _, f := range *funcs {
			if reqFunc != nil && f.FunctionBrn != reqFunc.FunctionBrn {
				sum += uint64(f.CodeSize)
			}
		}
	}

	layers, _ := dao.FindLayers(&api.Layer{Uid: uid})
	if layers != nil {
		for _, v := range *layers {
			sum += uint64(v.CodeSize)
		}
	}

	if sum+uint64(zipfileSize) > api.DefaultTotalCodeSizeLimit {
		limit := bytefmt.ByteSize(api.DefaultTotalCodeSizeLimit)
		if limit[len(limit)-1] != 'B' {
			limit += "B"
		}
		return apiErr.NewCodeStorageExceededException(
			fmt.Sprintf(TotalCodeSizeExceed, limit), nil)
	}

	return nil
}

func ParseParams(pageStr, pageSizeStr, markerStr, maxItemStr string) (int64, int64, int64, int64, error) {
	var (
		pageNo, pageSize, marker, maxItem int64
		err                               error
	)
	if pageStr != "" || pageSizeStr != "" {
		if pageStr == "" {
			pageNo = 1
		} else {
			pageNo, err = strconv.ParseInt(pageStr, 10, 64)
			if err != nil {
				return pageNo, pageSize, marker, maxItem, err
			}
		}
		pageNo = Max(1, pageNo)
		if pageSizeStr == "" {
			pageSize = 10
		} else {
			pageSize, err = strconv.ParseInt(pageSizeStr, 10, 64)
			if err != nil {
				return pageNo, pageSize, marker, maxItem, err
			}
		}
		pageSize = Max(1, pageSize)
		pageSize = Min(100, pageSize)
	} else {
		if markerStr != "" {
			marker, err = strconv.ParseInt(markerStr, 10, 64)
			if err != nil {
				return pageNo, pageSize, marker, maxItem, err
			}
			marker = Max(0, marker)
		}

		if maxItemStr != "" {
			maxItem, err = strconv.ParseInt(maxItemStr, 10, 64)
			if err != nil {
				return pageNo, pageSize, marker, maxItem, err
			}
			maxItem = Max(1, maxItem)
			maxItem = Min(50, maxItem)
		}
	}
	return pageNo, pageSize, marker, maxItem, nil
}

// ValidateCFSConfig 验证函数的 CFS 配置是否正确，包括 FsId、SubnetID 等参数的校验。
// 如果不需要使用 CFS，则将 CFSConfig 置空。
// 返回值：error - 如果配置错误，返回非 nil 错误；nil - 配置正确
func ValidateCFSConfig(f *dao.Function) error {
	if f.CFSConfig == nil || (f.CFSConfig.FsId == nil && f.CFSConfig.SubnetID == nil) {
		// 关闭该功能后，置空所有属性
		f.CFSConfig = nil
		return nil
	}

	// 绑定cfs之前，必须先绑定VPC（从 network_configs 表查）
	netCfg, err := dao.FindOneNetworkConfig(&dao.NetworkConfig{
		NetworkConfig: api.NetworkConfig{
			Brn: f.FunctionBrn,
		},
	})
	if err != nil {
		return kunErr.NewInvalidParameterValueException("查询 VPC 配置失败: "+err.Error(), err)
	}
	if netCfg == nil || netCfg.VpcConfigStr == nil || *netCfg.VpcConfigStr == "" {
		return kunErr.NewInvalidParameterValueException("请先为函数配置 VPC，再配置 CFS", nil)
	}
	vpcConfig := &api.VpcConfig{}
	if err := json.Unmarshal([]byte(*netCfg.VpcConfigStr), vpcConfig); err != nil {
		return kunErr.NewInvalidParameterValueException("VPC 配置解析失败，请检查 VPC 配置", err)
	}
	if vpcConfig.VpcID == "" || len(vpcConfig.SubnetIDs) == 0 || len(vpcConfig.SecurityGroupIDs) == 0 {
		return kunErr.NewInvalidParameterValueException("请先为函数配置 VPC，再配置 CFS", nil)
	}

	if f.CFSConfig.FsId == nil || f.CFSConfig.SubnetID == nil {
		return errors.New("the FsId domain and SubnetID should not empty")
	}

	// 验证获取到的CFS和待绑定的SubnetID是否一致，同时获取挂载信息Domain
	cfsInfo, err := global.AC.Clients.CFS.GetCFSInfo(f.Uid, *f.CFSConfig.FsId, *f.CFSConfig.SubnetID)
	if err != nil {
		return err
	}

	f.CFSConfig.Domain = &cfsInfo.MountTargetList[0].Domain
	f.CFSConfig.Ovip = &cfsInfo.MountTargetList[0].Ovip
	f.CFSConfig.VpcId = &cfsInfo.VpcId

	f.FormatCFSConfig()

	// 验证地址是否合法
	if f.CFSConfig.RemotePath == nil || len(*f.CFSConfig.RemotePath) == 0 {
		f.CFSConfig.RemotePath = &api.DefaulfRemotePath
	} else if len(*f.CFSConfig.RemotePath) > 255 {
		return errors.New("the length of remote path must be less than 255")
	}

	if f.CFSConfig.LocalPath == nil || len(*f.CFSConfig.LocalPath) == 0 {
		f.CFSConfig.LocalPath = &api.DefaultLocalPath
	} else if len(*f.CFSConfig.LocalPath) > 255 {
		return errors.New("the length of local path must be less than 255")
	}
	return nil
}

// ValidateVpcConfig 验证用户 vpc 参数
func ValidateVpcConfig(vtype string, f *dao.Function) error {
	if f.VpcConfig == nil {
		return nil
	}

	// update 时允许传空数组，表示删除 vpc 配置
	if f.VpcConfig.VpcID == "" &&
		len(f.VpcConfig.SubnetIDs) == 0 &&
		len(f.VpcConfig.SecurityGroupIDs) == 0 &&
		vtype == "update" {
		return nil
	}

	if f.VpcConfig.VpcID == "" {
		return errors.New("vpcId can not be empty")
	}
	if len(f.VpcConfig.SubnetIDs) == 0 || len(f.VpcConfig.SecurityGroupIDs) == 0 {
		return errors.New("subnet or securityGroup can not be empty")
	}

	if len(f.VpcConfig.SubnetIDs) > 16 {
		return errors.New("the number of subnet must be less than 16")
	}

	if len(f.VpcConfig.SecurityGroupIDs) > 5 {
		return errors.New("the number of securityGroup must be less than 5")
	}

	// 验证子网
	vpcID := f.VpcConfig.VpcID
	for _, sbn := range f.VpcConfig.SubnetIDs {
		res, err := global.AC.Clients.Vpc.GetSubnet(f.Uid, sbn)
		if err != nil {
			return fmt.Errorf("subnet %s does not exist", sbn)
		}

		if vpcID != res.Subnet.VpcID {
			return errors.New("subnets does not belong to the same VPC")
		}
	}

	// 验证安全组
	userSecGrps, err := getUserSecGroups(f.Uid, vpcID)
	if err != nil {
		return err
	}

	for _, sg := range f.VpcConfig.SecurityGroupIDs {
		if _, found := userSecGrps[sg]; !found {
			return fmt.Errorf("security group %s does not exist in vpc %s", sg, vpcID)
		}
	}

	return nil
}

// 安全组查询接口返回列表
func getUserSecGroups(uid, vpcID string) (secGrps map[string]bool, err error) {
	var marker string
	var res *vpc.ListSecGroupRes

	secGrps = make(map[string]bool)

	for {
		if res != nil {
			marker = res.NextMarker
		}

		res, err = global.AC.Clients.Vpc.ListSecGroups(uid, vpcID, marker)
		if err != nil {
			return nil, kunErr.NewServiceException("get security groups failed", err)
		}

		for _, s := range res.SecurityGroups {
			secGrps[s.ID] = true
		}

		if res.IsTruncated == false {
			break
		}
	}
	return
}

func GetListConditionFromRequest(request *restful.Request) (*api.ListCondition, error) {
	markerStr := request.QueryParameter("Marker")
	maxItemsStr := request.QueryParameter("MaxItems")
	pageNoStr := request.QueryParameter("pageNo")
	pageSizeStr := request.QueryParameter("pageSize")
	cond := &api.ListCondition{}
	var err error
	// pageNo, pageSize, marker, maxItem
	cond.PageNo, cond.PageSize, cond.Marker, cond.MaxItems, err = ParseParams(pageNoStr, pageSizeStr,
		markerStr, maxItemsStr)
	if err != nil {
		return nil, err
	}
	return cond, nil
}
