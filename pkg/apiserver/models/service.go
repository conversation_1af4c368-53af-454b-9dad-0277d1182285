package models

import (
	"errors"
	"icode.baidu.com/baidu/faas/kun/pkg/api"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/dao"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/global"
	"icode.baidu.com/baidu/faas/kun/pkg/util/json"
)



func FormatServices(svs *[]dao.Service) []interface{} {
	formatService := make([]interface{}, 0)
	for _, s := range *svs {
		dealService(&s)
		formatService = append(formatService, s)
	}
	return formatService
}

func FormatServicesWithCountFun(svs *[]dao.Service, sbj interface{}) []interface{} {
	formatService := make([]interface{}, 0)
	var service interface{}
	for _, s := range *svs {
		service = dealServiceWithMap(&s, sbj)
		formatService = append(formatService, service)

	}
	return formatService
}

func dealServiceWithMap(s *dao.Service, sbj interface{}) *dao.ServiceWithFun{
	sj := sbj.(*dao.CountServiceFunResObj)
	service := new(dao.ServiceWithFun)
	service.FuncCount = sj.ResMap[s.ServiceName]
	service.Uid = s.Uid
	service.ServiceName = s.ServiceName
	service.ServiceDesc = s.ServiceDesc
	service.ServiceConf = s.ServiceConf
	service.CreatedAt = s.CreatedAt
	service.UpdatedAt = s.UpdatedAt
	service.Status = s.Status
	json.Unmarshal([]byte(service.ServiceConf), &(service.ServiceConfig))
	return service

}

func dealService(s *dao.Service) {
	json.Unmarshal([]byte(s.ServiceConf), &(s.ServiceConfig))
}


func InitUserService(uid, sName string) (*dao.Service, error) {
	s := new(dao.Service)
	if uid == "" {
		return nil, errors.New("User not found")
	}
	s.Uid = uid
	s.ServiceName = sName
	//必须
	s.Region = global.AC.Config.Region
	s.Status = api.ServiceOnline
	return s, nil
}