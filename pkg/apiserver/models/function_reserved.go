package models

import (
	"context"
	"icode.baidu.com/baidu/faas/kun/pkg/api"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/dao"
	apiErr "icode.baidu.com/baidu/faas/kun/pkg/apiserver/error"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/global"
	kunErr "icode.baidu.com/baidu/faas/kun/pkg/error"
	"icode.baidu.com/baidu/faas/kun/pkg/util/logs"
)

// 从etcd中获取预留实例配置规格列表
func GetMaxReservedNum() int64 {
	c := global.AC.Clients.Etcd
	rc, err := c.GetReservedConfig("function_reserved")
	if err != nil {
		logs.V(6).Warnf("get reserved pod billingReport from etcd fail, err: %v", err)
	}
	if rc != nil && rc.MaxReservedNum != 0 {
		return rc.MaxReservedNum
	}
	// 如果没有配置，默认40
	return 40
}

func CkeckFunction(f *dao.Function) error {
	if err := dao.FindOneFunc(f); err != nil {
		return apiErr.NewResourceNotFoundException("function not found", nil)
	}

	// 函数版本不能为$LATEST
	if f.Version == "$LATEST" {
		return kunErr.NewInvalidParameterValueException("function is LATEST", nil)
	}

	return nil
}

func CheckReservedCount(f *dao.Function, reservedCount int) error {
	if reservedCount <= 0 {
		return kunErr.NewInvalidParameterValueException("reserved count is invalid", nil)
	}

	// 检查预留实例个数是否符合规定
	maxReservedNum := GetMaxReservedNum()

	if int(maxReservedNum) < reservedCount {
		return kunErr.NewInvalidParameterValueException("reserved count is more than max reserved num", nil)
	}

	// 检查预留实例个数是否超过函数或账户级别预留并发
	if f.ReservedConcurrentExecutions != nil && (*f.ReservedConcurrentExecutions > 0 && *f.ReservedConcurrentExecutions < reservedCount) {
		return kunErr.NewInvalidParameterValueException("reserved count is larger than function reserved concurrency", nil)
	}

	// 累加已预留的额度
	reservedConcurrencySum, err := GetReservedSum(f)
	if err != nil {
		return err
	}

	// 获取账户额度
	accountConcurrency, err := global.AC.Clients.Ops.GetQuota(context.TODO(), f.Uid, "user_concurrency")
	if err != nil {
		return kunErr.NewServiceException("get quota from opscenter failed", err)
	}

	// 计算是否超额
	if reservedConcurrencySum+reservedCount+api.DefaultAccountUnreservedConcurrencyMinimum > accountConcurrency {
		return kunErr.NewInvalidParameterValueException("reserved count is larger than account concurrency", nil)
	}

	return nil
}

func GetReservedSum(f *dao.Function) (sum int, err error) {
	findCond := &dao.Function{Uid: f.Uid}
	findCond.Version = "$LATEST"
	found := false

	funcs, err := dao.FindFunc(findCond)
	if err != nil {
		return
	}

	for _, fc := range *funcs {
		if fc.FunctionName == f.FunctionName {
			found = true
			continue
		}
		sum += *fc.ReservedConcurrentExecutions
	}

	if found == false {
		err = apiErr.NewResourceNotFoundException("function not found", err)
	}
	return
}

func CopyFunctionReserved(s *dao.FunctionReserved, d *dao.FunctionReserved) {
	d.FunctionVersion = s.FunctionVersion
	d.FunctionBrn = s.FunctionBrn
	d.FunctionName = s.FunctionName
	d.Uuid = s.Uuid
	d.MemorySize = s.MemorySize
}

func CopyFunctionInfo(reserved *dao.FunctionReserved, f *dao.Function) {
	reserved.FunctionName = f.FunctionName
	reserved.FunctionVersion = f.Version
	reserved.MemorySize = *f.MemorySize
	reserved.CommitID = f.CommitID
}
