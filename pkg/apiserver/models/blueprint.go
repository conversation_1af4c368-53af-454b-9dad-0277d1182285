package models

import (
	"fmt"
	"strings"

	"github.com/jinzhu/gorm"

	"icode.baidu.com/baidu/faas/kun/pkg/api"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/dao"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/global"
	bceBrn "icode.baidu.com/baidu/faas/kun/pkg/bce/brn"
	"icode.baidu.com/baidu/faas/kun/pkg/console"
	"icode.baidu.com/baidu/faas/kun/pkg/util/json"
	"icode.baidu.com/baidu/faas/kun/pkg/util/strtool"
)

const (
	DefalutBpMemorySize = 128
	DefaultBpTimeout    = 3
)

type ListBlueprintsCond struct {
	Name     string
	Runtimes []string
	Tags     []string
	PageNo   int64
	PageSize int64
	Status   string
}

func ListBlueprintsWithConditions(condition *ListBlueprintsCond) (*[]dao.Blueprint, int64, error) {
	var (
		db                 *gorm.DB
		count              int64
		blueprintsResSlice = make([]dao.Blueprint, 0)
		queries            = make([]dao.BlueprintQuery, 0)
	)

	if len(condition.Runtimes) > 0 {
		query := ""
		for k, rt := range condition.Runtimes {
			if k < len(condition.Runtimes)-1 {
				query = query + fmt.Sprintf("blueprints.runtime = '%s'  or ", rt)
			} else {
				queries = append(queries, dao.BlueprintQuery{
					Query:     query + "blueprints.runtime = ? ",
					Condition: rt,
				})
			}
		}
	}
	if condition.Name != "" {
		queries = append(queries, dao.BlueprintQuery{
			Query:     "blueprints.name like ?",
			Condition: "%" + condition.Name + "%",
		})
	}
	if len(condition.Tags) > 0 {
		for _, tag := range condition.Tags {
			queries = append(queries, dao.BlueprintQuery{
				Query:     "blueprints.keywords like ?",
				Condition: "%" + tag + "%",
			})
		}
	}
	if condition.Status != "" {
		queries = append(queries, dao.BlueprintQuery{
			Query:     "blueprints.status = ?",
			Condition: condition.Status,
		})
	}

	db, count = dao.GetBlueprintCount(queries...)
	db = db.Offset(int((condition.PageNo - 1) * condition.PageSize)).Limit(int(condition.PageSize))

	err := db.Find(&blueprintsResSlice).Error
	if err == gorm.ErrRecordNotFound {
		return &blueprintsResSlice, count, nil
	}
	return &blueprintsResSlice, count, err
}

func GetBlueprint(uuid string, ctx *global.ApiserverContext) (*dao.Blueprint, string, string, error) {
	var (
		bp           = new(dao.Blueprint)
		rawCodeTmp   interface{}
		rawCode, url string
		err          error
	)

	bp.Uuid = uuid
	// 获取蓝图信息
	if err = ctx.Observer.NewStage(global.QueryDatabaseStage).Observe(dao.FindOneBlueprint(bp)); err != nil {
		return bp, "", "", err
	}

	// 获取蓝图zip下载url
	url = global.AC.Clients.Code.FaasBpCodeDownloadUrl(bp.BosObjKey)

	//根据url获取蓝图RawCode
	if rawCodeTmp, err = ctx.Observer.NewStage(global.GetRawCodeStage).ObserveObject(console.GetRawCode(url, bp.Handler, bp.Runtime)); err != nil {
		return bp, "", rawCodeTmp.(string), err
	}
	rawCode = rawCodeTmp.(string)

	return bp, rawCode, url, nil
}

func GetBlueprintKeywords(ctx *global.ApiserverContext) ([]string, []string, []string, error) {
	var (
		bpsTmp                  interface{}
		keywords, name, runtime []string
		err                     error
	)
	bp := &dao.Blueprint{
		Status: api.BlueprintOnline,
	}
	if bpsTmp, err = ctx.Observer.NewStage(global.QueryDatabaseStage).ObserveObject(dao.ListBlueprints(bp)); err != nil {
		return nil, nil, nil, err
	}
	bps := bpsTmp.(*[]dao.Blueprint)
	for _, bp := range *bps {
		DealBlueprint(&bp)
		name = append(name, bp.Name)
		runtime = append(runtime, bp.Runtime)
		keywords = append(keywords, bp.Keywords...)
	}
	strtool.Deduplication(&name)
	strtool.Deduplication(&runtime)
	strtool.Deduplication(&keywords)
	return keywords, name, runtime, nil
}

func FormatBlueprints(bps *[]dao.Blueprint) []interface{} {
	formatBlueprint := make([]interface{}, 0)
	for _, bp := range *bps {
		DealBlueprint(&bp)
		formatBlueprint = append(formatBlueprint, bp)
	}
	return formatBlueprint
}

func DealBlueprint(bp *dao.Blueprint) {
	json.Unmarshal([]byte(bp.EnvironmentStr), &(bp.Environment))
	json.Unmarshal([]byte(bp.LinksStr), &(bp.Links))
	json.Unmarshal([]byte(bp.KeywordsStr), &bp.Keywords)
	json.Unmarshal([]byte(bp.AuthorsStr), &bp.Authors)
	bp.Layers = DealLayers(bp.LayersStr)

	extraMap := make(map[string]interface{})
	json.Unmarshal([]byte(bp.Extra), &extraMap)
	memorySize := DefalutBpMemorySize
	if mem, ok := extraMap["MemorySize"]; ok {
		memorySize = int(mem.(int64))
	}
	bp.MemorySize = &memorySize
	timeout := DefaultBpTimeout
	if t, ok := extraMap["Timeout"]; ok {
		timeout = int(t.(int64))
	}
	bp.Timeout = &timeout
	if trig, ok := extraMap["TriggerType"]; ok {
		bp.TriggerType = trig.(string)
	}
}

func DealLayers(layersStr string) []string {
	layers := make([]string, 0)
	if layersStr == "" {
		return layers
	}
	layersBrns := strings.Split(layersStr, ",")
	for _, brn := range layersBrns {
		layerBrn, err := bceBrn.ParseLayerBrn(brn)
		if err != nil {
			continue
		}
		layers = append(layers, layerBrn.LayerName)
	}
	return layers
}

func CheckLayers(layersStr string) error {
	layersBrns := strings.Split(layersStr, ",")
	if len(layersBrns) > 5 {
		return fmt.Errorf("too many layers, layers must less than 5")
	}
	return nil
}

func CheckDefaultRuntime(runtime string) (bool, bool) {
	if rt, ok := global.AC.Cache.RuntimeCache[runtime]; ok {
		return true, rt.Editable
	}
	return false, false
}

func GetLayers(layersStr string) []*api.LayerSample {
	layerList := make([]*api.LayerSample, 0)
	if layersStr != "" {
		layersBrns := strings.Split(layersStr, ",")
		if len(layersBrns) > 0 {
			for _, brn := range layersBrns {
				layer := &api.LayerSample{
					Brn: brn,
				}
				layerList = append(layerList, layer)
			}
		}
	}
	return layerList
}
