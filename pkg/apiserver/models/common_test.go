package models

import (
	"errors"
	"testing"

	"github.com/stretchr/testify/assert"

	apiErr "icode.baidu.com/baidu/faas/kun/pkg/apiserver/error"
	kunErr "icode.baidu.com/baidu/faas/kun/pkg/error"
)

func TestDealError(t *testing.T) {
	cases := []struct {
		in_cause string
		in_err error
		out_err error
	} {
		{
			in_cause: "update file failed",
			in_err: errors.New("unzip cross border"),
			out_err: apiErr.NewInvalidZipFileException("unzip cross border", nil),
		},
		{
			in_cause: "update file failed",
			in_err: errors.New("update file failed"),
			out_err: kunErr.NewServiceException("update file failed", errors.New("update file failed")),
		},
		{
			in_cause: "update file failed",
			in_err: kunErr.NewServiceException("update file failed", errors.New("update file failed")),
			out_err:kunErr.NewServiceException("update file failed", errors.New("update file failed")),
		},
	}

	for _, tc := range cases {
		err := OptimizeErrorCode(tc.in_cause, tc.in_err)
		assert.Equal(t, tc.out_err, err)
	}
}

