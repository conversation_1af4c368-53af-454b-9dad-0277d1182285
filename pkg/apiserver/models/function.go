package models

import (
	"errors"
	"strconv"

	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/dao"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/dbengine"
	"icode.baidu.com/baidu/faas/kun/pkg/bce/brn"
)

// publish func CommitId保持不变
func PublishNewFunc(oldF dao.Function, newVersion string) (*dao.Function, error) {
	copyFP := &oldF
	copyFP.Id = uint(0)
	copyFP.Version = newVersion
	copyFP.FunctionBrnInit()
	if err := dao.CreateFunc(nil, copyFP); err != nil {
		return nil, err
	}
	return copyFP, nil
}

// 获取最高版本号、最高版本commit_id
func GetPreVersion(uid string, region, fName string) (uint64, error) {
	f := new(dao.Function)
	f.Uid = uid
	f.Region = region
	f.FunctionName = fName
	err := dbengine.DBInstance().Unscoped().Where(f).Last(f).Error

	if err != nil {
		return 0, err
	}
	// 计算下个版本
	preVersionNum, _ := strconv.ParseUint(f.Version, 10, 64)
	return preVersionNum, nil
}

// 计算下个版本号
func GetNextVersion(uid string, region, fName string) (string, error) {
	preVersionNum, err := GetPreVersion(uid, region, fName)
	if err != nil {
		return "", err
	}
	nextVersionStr := strconv.FormatUint(preVersionNum+1, 10)

	return nextVersionStr, nil
}

func GetVersionByAlias(aliasName, uid, region, functionName string) (version string, err error) {
	if RegAlias.MatchString(aliasName) {
		aliasBrn := brn.GenerateFuncBrnString(region, uid, functionName, aliasName)
		aliasRes := &dao.Alias{AliasBrn: aliasBrn}

		if rkunErr := dao.FindOneAlias(aliasRes); rkunErr != nil {
			return "", rkunErr
		}
		version = aliasRes.FunctionVersion
	} else {
		return "", errors.New("aliasName not match Regexp")
	}

	return version, nil
}

func FormatFunctionRes(functionResSlice []dao.Function) []interface{} {
	functions := make([]interface{}, 0)
	for _, functionRes := range functionResSlice {
		functionRes.DealResFunction()
		functions = append(functions, functionRes)
	}

	return functions
}

type CreateCBDFunctionRequest struct {
	UUID string // 蓝图UUID
	dao.Function
}
