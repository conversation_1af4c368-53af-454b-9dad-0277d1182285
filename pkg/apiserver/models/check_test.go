package models_test

import (
	"errors"
	"fmt"
	"testing"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/google/uuid"
	"icode.baidu.com/baidu/faas/kun/pkg/api"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/code"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/dao"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/dbengine"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/global"
	"icode.baidu.com/baidu/faas/kun/pkg/util/base64"

	"github.com/stretchr/testify/assert"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/models"
	"icode.baidu.com/baidu/faas/kun/pkg/util/convert"
)

func TestValidateCFSConfig(t *testing.T) {

	global.MockAC()

	function := &dao.Function{}
	function.Uid = "abc"
	function.CFSConfig = &api.CFSConfig{}
	models.ValidateCFSConfig(function)

	function.VpcConfig = &api.VpcConfig{}
	models.ValidateCFSConfig(function)

	function.CFSConfig = &api.CFSConfig{}
	function.VpcConfig.SecurityGroupIDs = []string{"abc"}
	function.VpcConfig.SubnetIDs = []string{"abc"}
	function.CFSConfig.SubnetID = convert.String("abc")
	models.ValidateCFSConfig(function)

	function.CFSConfig.FsId = convert.String("fsid")
	models.ValidateCFSConfig(function)

	function.Uid = ""
	models.ValidateCFSConfig(function)

}

func TestCheckPodConcurrentQuota(t *testing.T) {
	s := convert.Int(0)
	i := -1
	models.CheckPodConcurrentQuota(s, i)
	i = 0
	models.CheckPodConcurrentQuota(s, i)
}

func TestCheckMultiple(t *testing.T) {
	cases := []struct {
		in_s         *int
		in_base      int
		out_multiple int
		out_err      error
	}{
		{
			in_s:         nil,
			in_base:      10,
			out_multiple: 0,
			out_err:      nil,
		},
		{
			in_s:         convert.Int(0),
			in_base:      10,
			out_multiple: 0,
			out_err:      nil,
		},
		{
			in_s:         convert.Int(10),
			in_base:      5,
			out_multiple: 2,
			out_err:      nil,
		},
		{
			in_s:         convert.Int(10),
			in_base:      3,
			out_multiple: 0,
			out_err:      errors.New(fmt.Sprintf(models.MultipleIllegal, 10, 3)),
		},
	}
	for _, tc := range cases {
		m, err := models.CheckMultiple(tc.in_s, tc.in_base)

		assert.Equal(t, tc.out_multiple, m)
		assert.Equal(t, tc.out_err, err)
	}
}

func TestCheckPtrString(t *testing.T) {
	cases := []struct {
		in_s    *string
		in_min  int
		in_max  int
		out_err error
	}{
		{
			in_s:    nil,
			in_min:  0,
			in_max:  0,
			out_err: nil,
		},
		{
			in_s:    convert.String("testkkk"),
			in_min:  1,
			in_max:  5,
			out_err: errors.New(fmt.Sprintf(models.StrLenIllegal, "testkkk", 1, 5)),
		},
		{
			in_s:    convert.String("testkkk"),
			in_min:  1,
			in_max:  10,
			out_err: nil,
		},
	}
	for _, tc := range cases {
		err := models.CheckPtrString(tc.in_s, tc.in_min, tc.in_max)
		assert.Equal(t, tc.out_err, err)
	}
}

func TestCheckPtrIntSize(t *testing.T) {
	cases := []struct {
		in_s    *int
		in_min  int
		in_max  int
		out_err error
	}{
		{
			in_s:    nil,
			in_min:  0,
			in_max:  0,
			out_err: nil,
		},
		{
			in_s:    convert.Int(100),
			in_min:  0,
			in_max:  99,
			out_err: errors.New(fmt.Sprintf(models.IntLenIllegal, 100, 0, 99)),
		},
		{
			in_s:    convert.Int(100),
			in_min:  1,
			in_max:  100,
			out_err: nil,
		},
	}
	for _, tc := range cases {
		err := models.CheckPtrIntSize(tc.in_s, tc.in_min, tc.in_max)
		assert.Equal(t, tc.out_err, err)
	}
}

func TestCheckFunctionTimeout(t *testing.T) {
	cases := []struct {
		in_s       *int
		in_min     int
		in_max     int
		in_confMax int
		out_err    error
	}{
		{
			in_s:       nil,
			in_min:     0,
			in_max:     0,
			in_confMax: 0,
			out_err:    nil,
		},
		{
			in_s:       convert.Int(100),
			in_min:     0,
			in_max:     99,
			in_confMax: 10,
			out_err:    errors.New(fmt.Sprintf(models.IntLenIllegal, 100, 0, 99)),
		},
		{
			in_s:       convert.Int(102),
			in_min:     1,
			in_max:     100,
			in_confMax: 101,
			out_err:    errors.New(fmt.Sprintf(models.IntLenIllegal, 102, 1, 101)),
		},
		{
			in_s:       convert.Int(100),
			in_min:     1,
			in_max:     100,
			in_confMax: 101,
			out_err:    nil,
		},
	}
	for _, tc := range cases {
		err := models.CheckFunctionTimeout(tc.in_s, tc.in_min, tc.in_max, tc.in_confMax)
		assert.Equal(t, tc.out_err, err)
	}
}

func TestCheckAliasName(t *testing.T) {
	cases := []struct {
		in_s    string
		out_err error
	}{
		{
			in_s:    "jk1234",
			out_err: nil,
		},
		{
			in_s:    "",
			out_err: nil,
		},
		{
			in_s:    "1234",
			out_err: errors.New(fmt.Sprintf(models.AliasNameIllegal)),
		},
	}
	for _, tc := range cases {
		err := models.CheckAliasName(tc.in_s)
		assert.Equal(t, tc.out_err, err)
	}
}

func TestCheckCodeSizeLayer(t *testing.T) {
	global.MockAC()
	code.MockCode()
	zipFile := "UEsDBBQACAgIAIumvE4AAAAAAAAAAAAAAAAIAAAAaW5kZXgucHkVjMsOgjAQAO9N+g8rXNTYVEHk8TMG6EJJmi2pW5C/t85xMpn8pOMn6GEhjbTBerD1JEUO6qpg9GahuYPIk2r+RgqDE9iejMNwxg2Jb6kixi9fOikgEZBjIMgKwv1t0Tn/KMpn9aqb9t4PYxrMNvsBUEsHCGs/OTtwAAAAdwAAAFBLAQIUABQACAgIAIumvE5rPzk7cAAAAHcAAAAIAAAAAAAAAAAAAAAAAAAAAABpbmRleC5weVBLBQYAAAAAAQABADYAAACmAAAAAAA="
	byteArray, _ := base64.DecodeString(zipFile)
	layer := &api.Layer{
		Uid:                  "uid",
		CodeId:               uuid.New().String(),
		CompatibleRuntimeStr: "python",
		Description:          "desc",
		LicenseInfo:          "lice",
		LayerName:            "layer",
	}
	var m sqlmock.Sqlmock
	m, dbengine.Engine = global.MockDB()
	cases := []struct {
		in_type      string
		in_layer     *api.Layer
		in_byteArray []byte
		out_err      error
		sqlFunc      func()
	}{
		{
			in_type:      "create",
			in_byteArray: nil,
			in_layer:     layer,
			out_err:      nil,
			sqlFunc: func() {
			},
		},
		{
			in_type:      "create",
			in_byteArray: byteArray,
			in_layer:     layer,
			out_err:      nil,
			sqlFunc: func() {
			},
		},
		{
			in_type:      "update",
			in_byteArray: byteArray,
			in_layer:     layer,
			out_err:      nil,
			sqlFunc: func() {
			},
		},
	}
	for i, tc := range cases {
		fmt.Println(i)
		m.ExpectQuery(".*").WillReturnRows(global.GetRowsFunctions(global.GetTestFunc(1)))
		m.ExpectQuery(".*").WillReturnRows(global.GetRowsFunctions(global.GetTestFunc(1)))
		err := models.CheckCodeSizeLayer(tc.in_type, tc.in_layer, tc.in_byteArray)
		assert.Equal(t, tc.out_err, err)

	}
}

func TestMax(t *testing.T) {
	s := models.Max(10, 11)
	assert.Equal(t, int64(11), s)
	s = models.Max(11, 10)
	assert.Equal(t, int64(11), s)
}

func TestMin(t *testing.T) {
	s := models.Min(10, 11)
	assert.Equal(t, int64(10), s)
	s = models.Min(11, 10)
	assert.Equal(t, int64(10), s)
}

func TestParseParams(t *testing.T) {
	cases := []struct {
		pageStr     string
		pageSizeStr string
		markerStr   string
		maxItemStr  string
		out_err     error
	}{
		{
			pageStr:     "",
			pageSizeStr: "",
			markerStr:   "",
			maxItemStr:  "",
			out_err:     nil,
		},
		{
			pageStr:     "1",
			pageSizeStr: "2",
			markerStr:   "10",
			maxItemStr:  "1",
			out_err:     nil,
		},
		{
			pageStr:     "1",
			pageSizeStr: "101",
			markerStr:   "10",
			maxItemStr:  "0",
			out_err:     nil,
		},
		{
			pageStr:     "1",
			pageSizeStr: "10",
			markerStr:   "10",
			maxItemStr:  "10000",
			out_err:     nil,
		},
	}

	for _, tc := range cases {
		_, _, _, _, err := models.ParseParams(tc.pageStr, tc.pageSizeStr, tc.markerStr, tc.maxItemStr)
		assert.Equal(t, tc.out_err, err)
	}
}
