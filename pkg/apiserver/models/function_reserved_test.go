package models

import (
	"fmt"
	"github.com/DATA-DOG/go-sqlmock"
	"github.com/stretchr/testify/assert"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/dao"
	"icode.baidu.com/baidu/faas/kun/pkg/util/convert"

	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/dbengine"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/global"
	kunErr "icode.baidu.com/baidu/faas/kun/pkg/error"
	"testing"
)

func TestGetMaxReservedNum(t *testing.T) {
	global.MockAC()
	maxReservedNum := GetMaxReservedNum()
	fmt.Println(maxReservedNum)
}

func TestCkeckFunction(t *testing.T) {
	global.MockAC()
	var m sqlmock.Sqlmock
	m, dbengine.Engine = global.MockDB()
	m.ExpectQuery("SELECT").WillReturnRows(global.GetRowsFunctions(global.GetTestFunc(1)))
	f := &dao.Function{
		Uid: "uid_0",
	}
	err := CkeckFunction(f)
	assert.Nil(t, err)

}

func TestCheckReservedCount(t *testing.T) {
	global.MockAC()

	f := &dao.Function{
		Uid: "uid_0",
	}
	f.FunctionName = "fname"
	f.MemorySize = convert.Int(128)

	f1 := &dao.Function{
		Uid: "uid",
	}
	f1.ReservedConcurrentExecutions = convert.Int(1)
	cases := []struct {
		function *dao.Function
		count    int
		sqlFunc  func()
		err      error
	}{
		{
			function: f,
			count:    0,
			err:      kunErr.NewInvalidParameterValueException("reserved count is invalid", nil),
		},
		{
			function: f1,
			count:    2,
			err:      kunErr.NewInvalidParameterValueException("reserved count is larger than function reserved concurrency", nil),
		},

		{
			function: f1,
			count:    41,
			err:      kunErr.NewInvalidParameterValueException("reserved count is more than max reserved num", nil),
		},
		{
			function: f,
			count:    1,
			sqlFunc: func() {
				var m sqlmock.Sqlmock
				m, dbengine.Engine = global.MockDB()
				m.ExpectQuery("SELECT").WillReturnRows(global.GetRowsFunctions(global.GetTestFunc(1)))
			},
			err: nil,
		},
	}

	for _, tc := range cases {
		if tc.sqlFunc != nil {
			tc.sqlFunc()
		}

		err := CheckReservedCount(tc.function, tc.count)
		assert.Equal(t, tc.err, err)

	}

}

func TestGetReservedSum(t *testing.T) {
	global.MockAC()
	f := &dao.Function{
		Uid: "uid_0",
	}

	var m sqlmock.Sqlmock
	m, dbengine.Engine = global.MockDB()
	m.ExpectQuery("SELECT").WillReturnRows(global.GetRowsFunctions(global.GetTestFunc(1)))

	_, err := GetReservedSum(f)
	assert.NotNil(t, err)
	f.FunctionName = "fname"
	m.ExpectQuery("SELECT").WillReturnRows(global.GetRowsFunctions(global.GetTestFunc(1)))
	_, err = GetReservedSum(f)
	assert.Nil(t, err)
}

func TestCopyFunctionReserved(t *testing.T) {
	s := &dao.FunctionReserved{
		FunctionVersion: "v1",
		FunctionBrn:     "brn",
		FunctionName:    "fname",
		Uuid:            "uuid",
		MemorySize:      128,
	}
	d := &dao.FunctionReserved{
		FunctionVersion: "v1",
		FunctionBrn:     "brn",
		FunctionName:    "fname-1",
		Uuid:            "uuid",
		MemorySize:      128,
	}
	CopyFunctionReserved(s, d)
}

func TestCopyFunctionInfo(t *testing.T) {
	r := &dao.FunctionReserved{}
	f := &dao.Function{
		FunctionBrn: "brn",
	}
	f.FunctionName = "fname"
	f.Version = "1"
	f.MemorySize = convert.Int(128)
	f.CommitID = convert.String("commitid")
	CopyFunctionInfo(r, f)
}
