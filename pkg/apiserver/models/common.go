package models

import (
	apiErr "icode.baidu.com/baidu/faas/kun/pkg/apiserver/error"
	kunErr "icode.baidu.com/baidu/faas/kun/pkg/error"
)

// deal error code error code optimization
func OptimizeErrorCode(cause string, err error) error {
	_, ok := (err).(kunErr.FinalError)
	if !ok {
		if err.Error() == "unzip cross border" || err.Error() == "archive and target path is not absolute" {
			err = apiErr.NewInvalidZipFileException(err.Error(), nil)
		} else {
			err = kunErr.NewServiceException(cause, err)
		}
	}
	return err
}
