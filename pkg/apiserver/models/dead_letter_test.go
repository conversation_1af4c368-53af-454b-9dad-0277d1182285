package models

import (
	"testing"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/stretchr/testify/assert"

	"icode.baidu.com/baidu/faas/kun/pkg/api"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/dao"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/dbengine"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/global"
)

func TestCheckDeadLetterTopic(t *testing.T) {
	global.MockAC()

	myTopic := "myTopic"
	inputFunc := new(dao.Function)
	inputFunc.Uid = "uiduid"
	inputFunc.DeadLetterTopic = &myTopic

	err := CheckDeadLetterTopic(inputFunc, "Write")
	assert.Nil(t, err)
}

func TestCheckAsyncInvokeConfig(t *testing.T) {
	global.MockAC()
	var m sqlmock.Sqlmock
	m, dbengine.Engine = global.MockDB()
	inputFunc := new(dao.Function)
	inputFunc.Uid = "uiduid"
	inputFunc.FunctionBrn = "brn:bce:cfc:bj:640c8817bd1de2928d47256dd0620ce5:function:test_1:$LATEST"
	maxRetryAttempts := 4
	asyncConf := api.AsyncInvokeConfig{
		MaxRetryAttempts: &maxRetryAttempts,
	}

	maxRetryIntervalInSeconds := int64(43201)
	asyncConf1 := api.AsyncInvokeConfig{
		MaxRetryIntervalInSeconds: &maxRetryIntervalInSeconds,
	}

	dc2 := &api.DestinationConfig{
		Type: api.DestinationTypeForKafka,
		Destination: "myTopic",
	}

	asyncConf2 := api.AsyncInvokeConfig{
		OnSuccess:dc2,
		OnFailure:dc2,
	}

	dc3 := &api.DestinationConfig{
		Type: api.DestinationTypeForCfc,
		Destination: inputFunc.FunctionBrn,
	}
	asyncConf3 := api.AsyncInvokeConfig{
		OnSuccess:dc3,
	}

	dc4 := &api.DestinationConfig{
		Type: api.DestinationTypeForCfc,
		Destination: "brn:bce:cfc:bj:640c8817bd1de2928d47256dd0620ce5",
	}
	asyncConf4 := api.AsyncInvokeConfig{
		OnSuccess:dc4,
	}

	dc5 := &api.DestinationConfig{
		Type: api.DestinationTypeForCfc,
		Destination: "brn:bce:cfc:bj:640c8817bd1de2928d47256dd0620ce5:function:test:1",
	}
	asyncConf5 := api.AsyncInvokeConfig{
		OnSuccess:dc5,
	}

	dc6 := &api.DestinationConfig{
		Type: api.DestinationTypeForCfc,
		Destination: "brn:bce:cfc:bj:640c8817bd1de2928d47256dd0620ce5:function:test:alias_1",
	}
	asyncConf6 := api.AsyncInvokeConfig{
		OnSuccess:dc6,
	}

	cases := []struct{
		funct *dao.Function
		asyncConf *api.AsyncInvokeConfig
		sqlFunc      func()
	}{
		{
			funct:inputFunc,
			asyncConf:&asyncConf,
		},
		{
			funct:inputFunc,
			asyncConf:&asyncConf1,
		},
		{
			funct:inputFunc,
			asyncConf:&asyncConf2,
		},
		{
			funct:inputFunc,
			asyncConf:&asyncConf3,
		},
		{
			funct:inputFunc,
			asyncConf:&asyncConf4,
		},
		{
			funct:inputFunc,
			asyncConf:&asyncConf5,
			sqlFunc: func() {
				m.ExpectQuery("SELECT").WillReturnRows(global.GetRowsFunctions(global.GetTestFunc(1)))
			},
		},
		{
			funct:inputFunc,
			asyncConf:&asyncConf6,
			sqlFunc: func() {
				m.ExpectQuery("SELECT").WillReturnRows(global.GetRowsAliases(global.GetTestAlias(1)))
			},
		},
	}

	for i, tc := range cases {
		tc.funct.AsyncInvokeConfig = tc.asyncConf
		err := CheckAsyncInvokeConfig(tc.funct)
		if tc.sqlFunc != nil {
			tc.sqlFunc()
		}
		if i == 2 || i == 6{
			assert.Nil(t, err)
			continue
		}
		if err == nil {
			t.Fatal("err")
		}
	}

}
