package models

import (
	"crypto/tls"
	"net/http"
	"testing"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/stretchr/testify/assert"

	"icode.baidu.com/baidu/faas/kun/pkg/api"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/code"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/dao"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/dbengine"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/global"
	"icode.baidu.com/baidu/faas/kun/pkg/util/json"
)

func TestListBlueprintsWithConditions(t *testing.T) {
	var m sqlmock.Sqlmock
	m, dbengine.Engine = global.MockDB()

	cond1 := &ListBlueprintsCond{
		Status: "online",
	}
	cond2 := &ListBlueprintsCond{
		Tags:   []string{"python"},
		Status: "online",
	}
	cases := []struct {
		in_cond *ListBlueprintsCond
		out_err error
	}{
		{
			in_cond: cond1,
			out_err: nil,
		},
		{
			in_cond: cond2,
			out_err: nil,
		},
	}
	for _, tc := range cases {
		m.ExpectQuery(".*").WillReturnRows(global.GetRowsBlueprints(global.GetTestBlueprint(1)))
		m.ExpectQuery(".*").WillReturnRows(global.GetRowsBlueprints(global.GetTestBlueprint(2)))
		_, _, err := ListBlueprintsWithConditions(tc.in_cond)
		assert.Equal(t, tc.out_err, err)
	}
}

func TestGetBlueprint(t *testing.T) {
	global.MockAC()
	code.MockCode()

	var m sqlmock.Sqlmock
	m, dbengine.Engine = global.MockDB()
	m.ExpectQuery(".*").WillReturnRows(global.GetRowsBlueprints(global.GetTestBlueprint(1)))

	uuid := "b7cc4498-dd9a-11e8-9f8b-f2801f1b9fd1"
	c := global.BuildNewKunCtx("GET", "/v1/blueprints", "", "uiduid", map[string]string{})
	in_ctx := global.BuildApiserverContext(c.Request(), c.Response(), "getBlueprints")

	tlsClientConfig := http.DefaultTransport.(*http.Transport).TLSClientConfig
	http.DefaultTransport.(*http.Transport).TLSClientConfig = &tls.Config{InsecureSkipVerify: true}

	_, _, _, err := GetBlueprint(uuid, in_ctx)

	http.DefaultTransport.(*http.Transport).TLSClientConfig = tlsClientConfig
	assert.Equal(t, nil, err)
}

func TestGetBlueprintKeywords(t *testing.T) {
	global.MockAC()

	var m sqlmock.Sqlmock
	m, dbengine.Engine = global.MockDB()
	m.ExpectQuery(".*").WillReturnRows(global.GetRowsBlueprints(global.GetTestBlueprint(1)))

	c := global.BuildNewKunCtx("GET", "/v1/blueprints", "", "uiduid", map[string]string{})
	in_ctx := global.BuildApiserverContext(c.Request(), c.Response(), "getBlueprints")

	_, _, _, err := GetBlueprintKeywords(in_ctx)
	assert.Equal(t, nil, err)
}

func TestFormatBlueprints(t *testing.T) {
	bps := make([]dao.Blueprint, 0)
	environment := api.Environment{
		Variables: make(map[string]string),
	}
	envStr, _ := json.Marshal(environment)
	extraMap := make(map[string]interface{})
	extraMap["MemorySize"] = 128
	extraMap["Timeout"] = 8
	extraMap["TriggerType"] = "http"
	extraStr, _ := json.Marshal(extraMap)
	bp := &dao.Blueprint{
		Name:           "blue",
		Uuid:           "uuid",
		EnvironmentStr: string(envStr),
		Extra:          string(extraStr),
	}
	bps = append(bps, *bp)
	bpsRes := FormatBlueprints(&bps)
	assert.Equal(t, 1, len(bpsRes))
}

func TestDealBlueprint(t *testing.T) {
	environment := api.Environment{
		Variables: make(map[string]string),
	}
	envStr, _ := json.Marshal(environment)
	extraMap := make(map[string]interface{})
	extraMap["MemorySize"] = 128
	extraMap["Timeout"] = 8
	extraMap["TriggerType"] = "http"
	extraStr, _ := json.Marshal(extraMap)
	bp := &dao.Blueprint{
		Name:           "blue",
		Uuid:           "uuid",
		EnvironmentStr: string(envStr),
		Extra:          string(extraStr),
		LayersStr:      "brn:bce:cfc:bj:640c8817bd1de2928d47256dd0620ce5:layer:consoletestlayer1:2",
	}
	DealBlueprint(bp)
	assert.Equal(t, 128, *bp.MemorySize)
}

func TestCheckDefaultRuntime(t *testing.T) {
	global.MockAC()

	cases := []struct {
		in_runtime string
		out        bool
	}{
		{
			in_runtime: "python2",
			out:        true,
		},
		{
			in_runtime: "java8",
			out:        false,
		},
	}
	for _, tc := range cases {
		_, editable := CheckDefaultRuntime(tc.in_runtime)
		assert.Equal(t, tc.out, editable)
	}
}

func TestDealLayers(t *testing.T) {
	cases := []struct {
		layersStr string
		isTrue    bool
	}{
		{
			layersStr: "",
			isTrue:    false,
		},
		{
			layersStr: "brn:bce:cfc:bj:640c8817bd1de2928d47256dd0620ce5:layer:consoletestlayer1:2,brn:bce:cfc:bj:640c8817bd1de2928d47256dd0620ce5:layer:console",
			isTrue:    true,
		},
	}
	for _, tc := range cases {
		layers := DealLayers(tc.layersStr)
		assert.Equal(t, tc.isTrue, len(layers) > 0)
	}
}

func TestCheckLayers(t *testing.T) {
	cases := []struct {
		layersStr string
		isTrue    bool
	}{
		{
			layersStr: "aaa,bbb",
			isTrue:    true,
		},
		{
			layersStr: "aa,bb,cc,dd,ee,ff",
			isTrue:    false,
		},
	}

	for _, tc := range cases {
		res := CheckLayers(tc.layersStr)
		assert.Equal(t, tc.isTrue, res == nil)
	}
}

func TestGetLayers(t *testing.T) {
	layersStr := "aaa,bbb,cccc"
	layers := GetLayers(layersStr)
	assert.Equal(t, 3, len(layers))
}
