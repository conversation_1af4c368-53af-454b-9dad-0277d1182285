package models_test

import (
	"errors"
	"fmt"
	"net/http"
	"strings"
	"testing"
	"time"

	"github.com/DATA-DOG/go-sqlmock"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/dbengine"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/global"

	"github.com/stretchr/testify/assert"

	"icode.baidu.com/baidu/faas/kun/pkg/api"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/dao"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/models"
	"icode.baidu.com/baidu/faas/kun/pkg/bce/brn"
)

func buildRestRequest(method, region, body string, uid, fName, qual string) *http.Request {
	endpoint := "https://cfc." + region + ".baidu.com/v1"
	reader := strings.NewReader(body)
	req, _ := http.NewRequest(method, endpoint, reader)

	req.Header.Add("FunctionName", fName)
	req.Header.Add("Qualifier", qual)
	req.Header.Add("BCE-FAAS-UID", uid)
	req.Header.Add("Content-Type", "application/json")
	req.Header.Add("Content-Length", fmt.Sprintf("%d", len(body)))

	return req
}

func getRowsAliases(aliases []dao.Alias) *sqlmock.Rows {
	var fieldNames = []string{"id", "alias_brn", "function_name", "function_version", "name", "description", "uid", "updated_at", "created_at"}
	rows := sqlmock.NewRows(fieldNames)
	if aliases != nil {
		for _, a := range aliases {
			rows = rows.AddRow(a.Id, a.AliasBrn, a.FunctionName, a.FunctionVersion, a.Name, a.Description, a.Uid, a.UpdatedAt, a.CreatedAt)
		}
	}
	return rows
}

func TestFunctionInit(t *testing.T) {
	global.MockAC()
	var m sqlmock.Sqlmock
	m, dbengine.Engine = global.MockDB()
	m.ExpectQuery("SELECT").WillReturnRows(getRowsAliases([]dao.Alias{
		{
			Id:              1,
			AliasBrn:        "",
			AliasArn:        "",
			FunctionName:    "woshifunction",
			FunctionVersion: "3",
			Name:            "alias",
			Description:     nil,
			Uid:             "13",
			UpdatedAt:       time.Time{},
			CreatedAt:       time.Time{},
		},
	}))
	m.ExpectQuery("SELECT").WillReturnRows(getRowsAliases([]dao.Alias{
		{
			Id:              1,
			AliasBrn:        "",
			AliasArn:        "",
			FunctionName:    "woshifunction",
			FunctionVersion: "3",
			Name:            "alias",
			Description:     nil,
			Uid:             "7777",
			UpdatedAt:       time.Time{},
			CreatedAt:       time.Time{},
		},
	}))
	m.ExpectQuery("SELECT").WillReturnRows(getRowsAliases([]dao.Alias{
		{
			Id:              1,
			AliasBrn:        "",
			AliasArn:        "",
			FunctionName:    "woshifunction",
			FunctionVersion: "3",
			Name:            "alias",
			Description:     nil,
			Uid:             "7777",
			UpdatedAt:       time.Time{},
			CreatedAt:       time.Time{},
		},
	}))

	cases := []struct {
		input_s string
		input_r *http.Request
		err     error
		act_f   dao.Function
	}{
		{
			input_r: buildRestRequest("GET", "bj", `{"key":"val"}`, "", "", ""),
			err:     errors.New("User not found"),
		},
		{
			input_r: buildRestRequest("GET", "bj", `{"key":"val"}`, "11", "9090!:!err-fname", ""),
			err:     brn.RegNotMatchErr,
		},
		{
			input_r: buildRestRequest("GET", "bj", `{"key":"val"}`, "13", "brn:!:!err-fname", ""),
			err:     brn.RegNotMatchErr,
		},
		{
			input_r: buildRestRequest("GET", "bj", `{"key":"val"}`, "7777", "cb691571fad8810e85f59a4ab1c1fd6a:opop", ""),
			err:     nil,
		},
		{
			input_r: buildRestRequest("GET", "bj", `{"key":"val"}`, "7777", "cb691571fad8810e85f59a4ab1c1fd6a:opop", "klklk|xcc"),
			err:     errors.New("Qualifier not match Regexp"),
		},
		{
			input_r: buildRestRequest("GET", "bj", `{"key":"val"}`, "7777", "cb691571fad8810e85f59a4ab1c1fd6a:woshifunction", ""),
			err:     nil,
			act_f: dao.Function{
				Region: "bj",
				Uid:    "7777",
				FunctionConfig: api.FunctionConfig{
					FunctionName: "woshifunction",
				},
			},
		},
		{
			input_r: buildRestRequest("GET", "bj", `{"key":"val"}`, "13", "brn:bce:cfc:bj:13:function:woshifunction:2:test", ""),
			err:     brn.RegNotMatchErr,
		},
		{
			input_r: buildRestRequest("GET", "bj", `{"key":"val"}`, "7777", "brn:bce:cfc:bj:cb691571fad8810e85f59a4ab1c1fd6a:function:woshifunction", ""),
			err:     nil,
			act_f: dao.Function{
				Region: "bj",
				Uid:    "7777",
				FunctionConfig: api.FunctionConfig{
					FunctionName: "woshifunction",
					Version:      "",
				},
			},
		},
		{
			input_r: buildRestRequest("GET", "bj", `{"key":"val"}`, "7777", "brn:bce:cfc:bj:cb691571fad8810e85f59a4ab1c1fd6a:function:woshifunction:$LATEST", ""),
			err:     nil,
			act_f: dao.Function{
				Region: "bj",
				Uid:    "7777",
				FunctionConfig: api.FunctionConfig{
					FunctionName: "woshifunction",
					Version:      "$LATEST",
				},
			},
		},
		{
			input_r: buildRestRequest("GET", "bj", `{"key":"val"}`, "7777", "brn:bce:cfc:bj:cb691571fad8810e85f59a4ab1c1fd6a:function:woshifunction:3", "2"),
			err:     errors.New("the derived qualifier(version) from the function brn does not match the specified qualifier(version)"),
		},
		{
			input_r: buildRestRequest("GET", "bj", `{"key":"val"}`, "7777", "brn:bce:cfc:bj:cb691571fad8810e85f59a4ab1c1fd6a:function:woshifunction:3", "3"),
			err:     nil,
			act_f: dao.Function{
				Region: "bj",
				Uid:    "7777",
				FunctionConfig: api.FunctionConfig{
					FunctionName: "woshifunction",
					Version:      "3",
				},
			},
		},
		{
			input_r: buildRestRequest("GET", "bj", `{"key":"val"}`, "7777", "brn:bce:cfc:bj:cb691571fad8810e85f59a4ab1c1fd6a:function:woshifunction", "3"),
			err:     nil,
			act_f: dao.Function{
				Region: "bj",
				Uid:    "7777",
				FunctionConfig: api.FunctionConfig{
					FunctionName: "woshifunction",
					Version:      "3",
				},
			},
		},
		{
			input_r: buildRestRequest("GET", "bj", `{"key":"val"}`, "7777", "cb691571fad8810e85f59a4ab1c1fd6a:woshifunction", "3"),
			err:     nil,
			act_f: dao.Function{
				Region: "bj",
				Uid:    "7777",
				FunctionConfig: api.FunctionConfig{
					FunctionName: "woshifunction",
					Version:      "3",
				},
			},
		},
		{
			input_r: buildRestRequest("GET", "bj", `{"key":"val"}`, "13", "woshifunction", "3"),
			err:     nil,
			act_f: dao.Function{
				Region: "bj",
				Uid:    "13",
				FunctionConfig: api.FunctionConfig{
					FunctionName: "woshifunction",
					Version:      "3",
				},
			},
		},
		{
			input_r: buildRestRequest("GET", "bj", `{"key":"val"}`, "13", "woshifunction", "alias"),
			err:     nil,
			act_f: dao.Function{
				Region: "bj",
				Uid:    "13",
				FunctionConfig: api.FunctionConfig{
					FunctionName: "woshifunction",
					Version:      "3",
				},
			},
		},
		{
			input_r: buildRestRequest("GET", "bj", `{"key":"val"}`, "7777", "brn:bce:cfc:bj:cb691571fad8810e85f59a4ab1c1fd6a:function:woshifunction:4", "alias"),
			err:     errors.New("the derived qualifier(version) from the function brn does not match the specified qualifier(version)"),
			act_f: dao.Function{
				Region: "bj",
				Uid:    "7777",
				FunctionConfig: api.FunctionConfig{
					FunctionName: "woshifunction",
					Version:      "3",
				},
			},
		},
		{
			input_r: buildRestRequest("GET", "bj", `{"key":"val"}`, "7777", "brn:bce:cfc:bj:cb691571fad8810e85f59a4ab1c1fd6a:function:woshifunction:alias", "alias"),
			err:     nil,
			act_f: dao.Function{
				Region: "bj",
				Uid:    "7777",
				FunctionConfig: api.FunctionConfig{
					FunctionName: "woshifunction",
					Version:      "3",
				},
			},
		},
		{
			input_r: buildRestRequest("GET", "bj", `{"key":"val"}`, "7777", "brn:bce:cfc:bj:cb691571fad8810e85f59a4ab1c1fd6a:function:woshifunction:alias", ""),
			err:     nil,
			act_f: dao.Function{
				Region: "bj",
				Uid:    "7777",
				FunctionConfig: api.FunctionConfig{
					FunctionName: "woshifunction",
					Version:      "3",
				},
			},
		},
		{
			input_r: buildRestRequest("GET", "bj", `{"key":"val"}`, "7777", "cb691571fad8810e85f59a4ab1c1fd6a:woshifunction:alias", "3"),
			err:     errors.New("the derived qualifier(version) from the function brn does not match the specified qualifier(version)"),
			act_f: dao.Function{
				Region: "bj",
				Uid:    "7777",
				FunctionConfig: api.FunctionConfig{
					FunctionName: "woshifunction",
					Version:      "3",
				},
			},
		},
	}

	for _, tc := range cases {
		t.Run(tc.input_s, func(t *testing.T) {
			u := tc.input_r.Header.Get("Bce-Faas-Uid")
			f, err := models.InitUserFunc(u, tc.input_r.Header.Get("FunctionName"), tc.input_r.Header.Get("Qualifier"), "test")

			if err == nil && tc.err != nil {
				t.Errorf("Expected err to be %v, but got nil", tc.err)
			} else if err != nil && tc.err == nil {
				t.Errorf("Expected err to be nil, but got %v", err)
			} else if err != nil && tc.err != nil && err.Error() != tc.err.Error() {
				t.Errorf("Expected err to be %v, but got %v", tc.err, err)
			}

			if err == nil && tc.err != nil {
				assert.Equal(t, tc.act_f, *f)
			}

		})
	}
}
