package function

import (
	"github.com/jinzhu/gorm"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/dao"
)

type AliasesCond struct {
	Marker   int64
	MaxItems int64
}

func ListAliasesByCondition(alias *dao.<PERSON><PERSON>, condition *AliasesCond) ([]dao.<PERSON>as, int64, error) {
	var (
		db              *gorm.DB
		count           int64
		aliasesResSlice = make([]dao.<PERSON>as, 0)
	)

	db, count = dao.GetAliasCount(alias)

	if condition.Marker != 0 {
		db = db.Offset(condition.Marker)
	}
	if condition.MaxItems != 0 {
		db = db.Limit(condition.MaxItems)
	}

	err := db.Find(&aliasesResSlice).Error
	if err == gorm.ErrRecordNotFound {
		return aliasesResSlice, count, nil
	}

	return aliasesResSlice, count, err
}
