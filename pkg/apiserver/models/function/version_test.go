package function

import (
	"testing"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/stretchr/testify/assert"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/dao"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/dbengine"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/global"
)

func TestCreateNewVersion(t *testing.T) {
	global.MockAC()
	global.RegisterFaasRuntimeGovalidatorTag()

	function := &dao.Function{
		Uid: "uiduid",
	}
	function.FunctionName = "test"

	desc := "test1"
	cases := []struct {
		in_function   *dao.Function
		in_codeSha256 string
		in_desc       *string
		in_ctx        *global.ApiserverContext
		out_err       error
	}{
		{
			in_function:   function,
			in_codeSha256: "cNBGAjstP9SgRKYdf8I3ang/4dPWdcrTUYzpSD4bD8s=",
			in_desc:       &desc,
			out_err:       nil,
		},
		{
			in_function:   function,
			in_codeSha256: "",
			in_desc:       &desc,
			out_err:       nil,
		},
	}

	for _, tc := range cases {
		var m sqlmock.Sqlmock
		m, dbengine.Engine = global.MockDB()
		m.ExpectQuery(".*").WillReturnRows(global.GetRowsFunctions(global.GetTestFunc(1)))
		m.ExpectQuery(".*").WillReturnRows(global.GetRowsFunctions(global.GetTestFunc(2)))
		m.ExpectQuery(".*").WillReturnRows(global.GetRowsNetworkConfigs(global.GetTestNetworkConfig(2)))
		m.ExpectBegin()
		m.ExpectExec(".*").WillReturnResult(sqlmock.NewResult(1, 1))
		m.ExpectExec(".*").WillReturnResult(sqlmock.NewResult(1, 1))
		m.ExpectCommit()
		findReq := FunctionVersionReq{
			CodeSha256:  tc.in_codeSha256,
			Description: tc.in_desc,
		}
		c := global.BuildNewKunCtx("POST", "/v1/functions/myfunc/versions", "{}", "uiduid", map[string]string{})
		tc.in_ctx = global.BuildApiserverContext(c.Request(), c.Response(), "version")
		_, err := CreateNewVersion(findReq, tc.in_function, tc.in_ctx)
		assert.Equal(t, tc.out_err, err)
	}

}

func TestListFunctionVersions(t *testing.T) {
	global.RegisterFaasRuntimeGovalidatorTag()
	function := &dao.Function{
		Uid: "uiduid",
	}
	function.FunctionName = "test"
	cond := &VersionListCond{
		SearchFN: "test",
		Marker:   1,
		MaxItems: 10,
	}
	cases := []struct {
		in_function *dao.Function
		in_cond     *VersionListCond
		out_err     error
	}{
		{
			in_function: function,
			in_cond:     cond,
			out_err:     nil,
		},
	}

	for _, tc := range cases {
		var m sqlmock.Sqlmock
		m, dbengine.Engine = global.MockDB()
		m.ExpectQuery(".*").WillReturnRows(global.GetRowsFunctions(global.GetTestFunc(1)))
		m.ExpectQuery(".*").WillReturnRows(global.GetRowsFunctions(global.GetTestFunc(1)))
		_, _, err := ListFunctionVersions(tc.in_function, tc.in_cond)
		assert.Equal(t, tc.out_err, err)
	}
}
