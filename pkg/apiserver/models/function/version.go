package function

import (
	"fmt"

	"github.com/asaskevich/govalidator"
	"github.com/jinzhu/gorm"

	"icode.baidu.com/baidu/faas/kun/pkg/api"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/dao"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/dbengine"
	apiErr "icode.baidu.com/baidu/faas/kun/pkg/apiserver/error"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/global"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/models"
	kunErr "icode.baidu.com/baidu/faas/kun/pkg/error"
)

type VersionListCond struct {
	SearchFN string
	Marker   int64
	MaxItems int64
}

// publish version request
type FunctionVersionReq struct {
	CodeSha256  string
	Description *string
}

func CreateNewVersion(funcVerReq FunctionVersionReq, initFunction *dao.Function, ctx *global.ApiserverContext) (*dao.Function, error) {
	funcRes := new(dao.Function)

	var (
		nextVerTmp interface{}
		err        error
	)
	// 计算下一个版本
	if nextVerTmp, err = ctx.Observer.NewStage(global.GetNextVersionStage).ObserveObject(models.GetNextVersion(initFunction.Uid, initFunction.Region, initFunction.FunctionName)); err != nil {
		cause := fmt.Sprintf("err happend when GetNextVersion, %v", err)
		return funcRes, apiErr.NewResourceNotFoundException(cause, nil)
	}

	// 获取$LATEST版本代码
	fLatest := new(dao.Function)
	fLatest.Version = "$LATEST"
	fLatest.Uid = initFunction.Uid
	fLatest.FunctionName = initFunction.FunctionName
	fLatest.WorkspaceID = initFunction.WorkspaceID

	if err := ctx.Observer.NewStage(global.FindOneFunctionStage).Observe(dao.FindOneFunc(fLatest)); err != nil {
		cause := fmt.Sprintf("can not find any function, %v", err)
		return funcRes, apiErr.NewResourceNotFoundException(cause, nil)
	}

	if funcVerReq.CodeSha256 != "" {
		if fLatest.CodeSha256 != funcVerReq.CodeSha256 {
			cause := fmt.Sprintf("check CodeSha256 failed, [req sha256:%s][last version sha256:%s]", funcVerReq.CodeSha256, fLatest.CodeSha256)
			return funcRes, kunErr.NewInvalidParameterValueException(cause, nil)
		}
	}

	newFuncData := *fLatest
	newFunc := &newFuncData
	newFunc.Id = uint(0)
	newFunc.Version = nextVerTmp.(string)
	newFunc.VersionDesc = funcVerReq.Description
	newFunc.ReservedConcurrentExecutions = fLatest.ReservedConcurrentExecutions
	newFunc.FunctionBrnInit()

	networkCfg, err := dao.FindOneNetworkConfig(&dao.NetworkConfig{
		NetworkConfig: api.NetworkConfig{
			Brn: fLatest.FunctionBrn,
		},
	})
	if err != nil {
		return nil, err
	}

	tx := dbengine.DBTransaction()
	if networkCfg != nil {
		// $latest 有网络配置，拷贝给新版本再创建一个
		versionNetworkCfg := *networkCfg
		versionNetworkCfg.Brn = newFunc.FunctionBrn
		versionNetworkCfg.Id = 0
		if err = dao.CreateNetworkConfig(tx, &versionNetworkCfg); err != nil {
			return nil, err
		}
	}

	if _, err := govalidator.ValidateStruct(newFunc); err != nil {
		tx.Rollback()
		cause := fmt.Sprintf("Validate failed err : %v", err)
		return funcRes, kunErr.NewInvalidParameterValueException(cause, nil)
	} else if err := models.CheckPtrString(newFunc.VersionDesc, 0, 125); err != nil {
		tx.Rollback()
		cause := fmt.Sprintf("Description length is Illegal err :%v", err)
		return funcRes, kunErr.NewInvalidParameterValueException(cause, nil)
	}

	if rKunErr := ctx.Observer.NewStage(global.CreateFunctionStage).Observe(dao.CreateFunc(tx, newFunc)); rKunErr != nil {
		tx.Rollback()
		return funcRes, rKunErr
	}
	tx.Commit()
	newFunc.DealResFunction()
	ctx.Context.FunctionBrn = newFunc.FunctionBrn
	return newFunc, nil
}

func ListFunctionVersions(findFunc *dao.Function, condition *VersionListCond) ([]dao.Function, int64, error) {
	var (
		db               *gorm.DB
		count            int64
		functionResSlice = make([]dao.Function, 0)
		queries          = make([]dao.FunctionQuery, 0)
	)
	if condition.SearchFN != "" {
		queries = append(queries, dao.FunctionQuery{
			Query:     "functions.function_name = ?",
			Condition: condition.SearchFN,
		})
	}
	db, count = dao.GetFuncCount(findFunc, queries...)
	if condition.Marker != 0 {
		db = db.Offset(condition.Marker)
	}
	if condition.MaxItems != 0 {
		db = db.Limit(condition.MaxItems)
	}
	err := db.Order("id asc").Find(&functionResSlice).Error
	if err == gorm.ErrRecordNotFound {
		return functionResSlice, count, nil
	}

	return functionResSlice, count, err
}
