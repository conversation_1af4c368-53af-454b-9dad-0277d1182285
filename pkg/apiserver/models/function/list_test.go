package function

import (
	"github.com/DATA-DOG/go-sqlmock"
	"github.com/stretchr/testify/assert"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/dao"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/dbengine"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/global"
	"testing"
)

func TestListFunctionWithConditions(t *testing.T) {
	function := &dao.Function{
		Uid: "uiduid",
	}
	cond1 := &FunctionListCond{
		SearchFN: "test",
		PageNo:   1,
		PageSize: 10,
		OrderBy:  "CreatedAt",
		Sort:     "DESC",
		Runtime:  "python2",
	}
	cond2 := &FunctionListCond{
		SearchFN: "test",
		Marker:   1,
		MaxItems: 10,
		OrderBy:  "CreatedAt",
		Sort:     "DESC",
		Runtime:  "python2",
	}
	cases := []struct {
		in_function *dao.Function
		in_cond     *FunctionListCond
		out_err     error
	}{
		{
			in_function: function,
			in_cond:     cond1,
			out_err:     nil,
		},
		{
			in_function: function,
			in_cond:     cond2,
			out_err:     nil,
		},
	}

	for _, tc := range cases {
		var m sqlmock.Sqlmock
		m, dbengine.Engine = global.MockDB()
		m.ExpectQuery(".*").WillReturnRows(global.GetRowsFunctions(global.GetTestFunc(1)))
		m.ExpectQuery(".*").WillReturnRows(global.GetRowsFunctions(global.GetTestFunc(2)))
		_, _, err := ListFunctionWithConditions(tc.in_function, tc.in_cond)
		assert.Equal(t, tc.out_err, err)
	}
}

func TestListCbdFunctionWithCondition(t *testing.T) {
	function := &dao.Function{
		Uid: "uiduid",
	}

	cond1 := &CBDFunctionListCond{
		PageNo:      1,
		PageSize:    10,
		Order:       map[string]string{"FunctionName": "desc"},
		SearchFN:    "test",
		Description: "test1",
		SourceTag:   "cbd",
	}
	cond2 := &CBDFunctionListCond{
		Order:       map[string]string{"Description": "asc"},
		SearchFN:    "test",
		Description: "test",
		Marker:      1,
		MaxItems:    10,
		SourceTag:   "cbd",
	}

	cases := []struct {
		in_function *dao.Function
		in_cond     *CBDFunctionListCond
		out_err     error
	}{
		{
			in_function: function,
			in_cond:     cond1,
			out_err:     nil,
		},
		{
			in_function: function,
			in_cond:     cond2,
			out_err:     nil,
		},
	}
	for _, tc := range cases {
		var m sqlmock.Sqlmock
		m, dbengine.Engine = global.MockDB()
		m.ExpectQuery(".*").WillReturnRows(global.GetRowsFunctions(global.GetTestFunc(1)))
		m.ExpectQuery(".*").WillReturnRows(global.GetRowsFunctions(global.GetTestFunc(2)))
		_, _, err := ListCbdFunctionWithCondition(tc.in_function, tc.in_cond)
		assert.Equal(t, tc.out_err, err)
	}
}
