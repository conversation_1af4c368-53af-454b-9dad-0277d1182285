// Package function : console v2 api 中所需结构
package function

import (
	"icode.baidu.com/baidu/faas/kun/pkg/api"
	kunErr "icode.baidu.com/baidu/faas/kun/pkg/error"
	"icode.baidu.com/baidu/faas/kun/pkg/util/json"
	"net/http"
)

// SuccessResp 成功响应体结构
type SuccessResp struct {
	Success bool        `json:"success"`
	Result  interface{} `json:"result"`
}

// FailResp 失败响应体结构
type FailResp struct {
	Success bool        `json:"success"`
	Message interface{} `json:"message"`
}

// NewSuccessResp 生成成功响应体结构
func NewSuccessResp(result interface{}) *SuccessResp {
	return &SuccessResp{
		Success: true,
		Result:  result,
	}
}

// NewFailResp 生成失败响应体结构
func NewFailResp(failure interface{}) *FailResp {
	var message = make(map[string]interface{})
	message["global"] = failure
	return &FailResp{
		Success: false,
		Message: message,
	}
}

// WriteTo 将失败写回response
func (respBody *FailResp) WriteTo(w http.ResponseWriter, err kunErr.FinalError) {
	w.Header().Set("Content-Type", "application/json")
	w.Header().Set("X-Amzn-ErrorType", string(err.Code))
	w.Header().Set(api.HeaderXBceErrorType, string(err.Code))

	w.WriteHeader(err.Status)

	b, _ := json.Marshal(respBody)
	w.Write(b)
}
