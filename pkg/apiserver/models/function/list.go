package function

import (
	"fmt"
	"strings"

	"github.com/jinzhu/gorm"

	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/dao"
)

type FunctionListCond struct {
	Version      string
	PageNo       int64
	PageSize     int64
	Marker       int64
	MaxItems     int64
	OrderBy      string
	Sort         string
	SearchFN     string
	Runtime      string
	WorkspaceID  *string
	ServiceName  string
	BlueprintTag string
}

func ListFunctionWithConditions(findFunc *dao.Function, condition *FunctionListCond) ([]dao.Function, int64, error) {
	var (
		db               *gorm.DB
		count            int64
		functionResSlice = make([]dao.Function, 0)
		queries          = make([]dao.FunctionQuery, 0)
	)

	if condition.SearchFN != "" {
		queries = append(queries, dao.FunctionQuery{
			Query:     "functions.function_name like ?",
			Condition: "%" + condition.SearchFN + "%",
		})
	}

	if condition.Runtime != "" {
		// 判断 runtime 是否以 "$" 开头
		if strings.HasPrefix(condition.Runtime, "$") {
			queries = append(queries, dao.FunctionQuery{
				Query:     "functions.runtime like ?",
				Condition: "%" + condition.Runtime[1:] + "%",
			})
		} else {
			queries = append(queries, dao.FunctionQuery{
				Query:     "functions.runtime = ?",
				Condition: condition.Runtime,
			})
		}
	}

	// 兼容云上CFC
	if condition.WorkspaceID != nil {
		queries = append(queries, dao.FunctionQuery{
			Query:     "functions.workspace_id = ?",
			Condition: *condition.WorkspaceID,
		})
	}

	if condition.ServiceName != "" {
		queries = append(queries, dao.FunctionQuery{
			Query:     "functions.service_name = ?",
			Condition: condition.ServiceName,
		})
	}

	if condition.BlueprintTag != "" {
		queries = append(queries, dao.FunctionQuery{
			Query:     "functions.blueprint_tag = ?",
			Condition: condition.BlueprintTag,
		})
	}

	db, count = dao.GetFuncCount(findFunc, queries...)
	db = db.Order(fmt.Sprintf("%s %s", gorm.ToDBName(condition.OrderBy), condition.Sort))
	if condition.PageNo != 0 || condition.PageSize != 0 {
		db = db.Offset(int((condition.PageNo - 1) * condition.PageSize)).Limit(int(condition.PageSize))
	} else {
		if condition.Marker != 0 {
			db = db.Offset(condition.Marker)
		}
		if condition.MaxItems != 0 {
			db = db.Limit(condition.MaxItems)
		}
	}

	err := db.Find(&functionResSlice).Error
	if err == gorm.ErrRecordNotFound {
		return functionResSlice, count, nil
	}

	return functionResSlice, count, err
}

type CBDFunctionListCond struct {
	PageNo      int64
	PageSize    int64
	Order       map[string]string
	SearchFN    string
	FuncPrefix  string
	FuncName    string
	Description string
	Marker      int64
	MaxItems    int64
	SourceTag   string
}

func ListCbdFunctionWithCondition(findFunc *dao.Function, condition *CBDFunctionListCond) ([]dao.Function, int64, error) {
	var (
		db               *gorm.DB
		count            int64
		functionResSlice = make([]dao.Function, 0)
		queries          = make([]dao.FunctionQuery, 0)
		order            string
	)

	if condition.SearchFN != "" {
		queries = append(queries, dao.FunctionQuery{
			Query:     "functions.function_name like ?",
			Condition: condition.FuncPrefix + "%" + condition.FuncName + "%",
		})
	}

	if condition.Description != "" {
		queries = append(queries, dao.FunctionQuery{
			Query:     "functions.description like ?",
			Condition: "%" + condition.Description + "%",
		})
	}

	if condition.SourceTag != "" {
		queries = append(queries, dao.FunctionQuery{
			Query:     "functions.source_tag = ?",
			Condition: condition.SourceTag,
		})
	}

	db, count = dao.GetFuncCount(findFunc, queries...)

	for k, v := range condition.Order {
		order = order + fmt.Sprintf("%s %s,", gorm.ToDBName(k), v)
	}
	order = strings.Trim(order, ",")
	db = db.Order(order)

	if condition.PageNo != 0 || condition.PageSize != 0 {
		db = db.Offset(int((condition.PageNo - 1) * condition.PageSize)).Limit(int(condition.PageSize))
	} else {
		if condition.Marker != 0 {
			db = db.Offset(condition.Marker)
		}
		if condition.MaxItems != 0 {
			db = db.Limit(condition.MaxItems)
		}
	}

	err := db.Find(&functionResSlice).Error
	if err == gorm.ErrRecordNotFound {
		return functionResSlice, count, nil
	}

	return functionResSlice, count, nil
}
