package function

import (
	"github.com/DATA-DOG/go-sqlmock"
	"github.com/stretchr/testify/assert"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/dao"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/dbengine"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/global"
	"testing"
)

func TestListAliasesByCondition(t *testing.T) {
	alias := &dao.Alias{
		FunctionName: "test",
		Uid:          "uiduid",
	}
	cases := []struct {
		in_alias    *dao.Alias
		in_marker   int64
		in_maxItems int64
		out_err     error
	}{
		{
			in_alias:    alias,
			in_marker:   1,
			in_maxItems: 10,
			out_err:     nil,
		},
		{
			in_alias:    alias,
			in_maxItems: 10,
			out_err:     nil,
		},
		{
			in_alias:  alias,
			in_marker: 1,
			out_err:   nil,
		},
		{
			in_alias: alias,
			out_err:  nil,
		},
	}

	for _, tc := range cases {
		var m sqlmock.Sqlmock
		m, dbengine.Engine = global.MockDB()
		m.ExpectQuery(".*").WillReturnRows(global.GetRowsAliases(global.GetTestAlias(1)))
		m.ExpectQuery(".*").WillReturnRows(global.GetRowsAliases(global.GetTestAlias(1)))
		cond := &AliasesCond{
			Marker:   tc.in_marker,
			MaxItems: tc.in_maxItems,
		}

		_, _, err := ListAliasesByCondition(tc.in_alias, cond)
		assert.Equal(t, tc.out_err, err)
	}
}
