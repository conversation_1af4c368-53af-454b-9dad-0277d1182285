package models

import (
	"errors"
	"regexp"

	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/dao"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/global"
	"icode.baidu.com/baidu/faas/kun/pkg/bce/brn"
)

var (
	RegAlias = regexp.MustCompile("^[a-zA-Z0-9-_]+$")
)

func InitUserFunc(uid, fName, qualifier, workspaceID string) (*dao.Function, error) {
	f := new(dao.Function)
	//必须
	f.Region = global.AC.Config.Region

	if uid == "" {
		return nil, errors.New("User not found")
	}

	f.Uid = uid
	f.WorkspaceID = workspaceID

	//命中则必然有f.FunctionName
	if fName != "" {
		//开始处理fName
		hashedAccountId := brn.Md5BceUid(uid)
		functionName, version, alias, err := brn.DealFName(hashedAccountId, fName, workspaceID)
		if err != nil {
			return nil, err
		}
		f.FunctionName = functionName

		if qualifier != "" {
			var t string
			var err error
			if t, err = brn.JudgeQualifier(qualifier); err != nil {
				return nil, err
			}

			if version == "" && alias == "" {
				if t == "version" {
					f.Version = qualifier
				} else if t == "alias" {
					if version, err := GetVersionByAlias(qualifier, f.Uid, f.Region, f.FunctionName); err != nil {
						return nil, err
					} else {
						f.Version = version
					}
				}
			} else if version != "" {
				if t == "version" && qualifier == version {
					f.Version = qualifier
				} else {
					return nil, errors.New("the derived qualifier(version) from the function brn does not match the specified qualifier(version)")
				}
			} else if alias != "" {
				if t == "alias" && qualifier == alias {
					if version, err := GetVersionByAlias(qualifier, f.Uid, f.Region, f.FunctionName); err != nil {
						return nil, err
					} else {
						f.Version = version
					}
				} else {
					return nil, errors.New("the derived qualifier(version) from the function brn does not match the specified qualifier(version)")
				}
			} //理论上version和alias不可能同时不为空
		} else {
			if version != "" {
				f.Version = version
			} else if alias != "" {
				if version, err := GetVersionByAlias(alias, f.Uid, f.Region, f.FunctionName); err != nil {
					return nil, err
				} else {
					f.Version = version
				}
			}
		}
	}
	return f, nil
}
