package models

import (
	"strings"

	"icode.baidu.com/baidu/faas/kun/pkg/api"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/dao"
	apiErr "icode.baidu.com/baidu/faas/kun/pkg/apiserver/error"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/global"
	"icode.baidu.com/baidu/faas/kun/pkg/bce/bms"
	"icode.baidu.com/baidu/faas/kun/pkg/bce/brn"
	kunErr "icode.baidu.com/baidu/faas/kun/pkg/error"
)

func CheckDeadLetterTopic(inputFunc *dao.Function, topicOperation string) error {
	authorization := &bms.SuperAuthorization{
		TopicOwnerAccountID: inputFunc.Uid,
		Authorization: &bms.Authorization{
			AccountId:     global.AC.Config.BmsAccountID,
			Topic:         *inputFunc.DeadLetterTopic,
			CertificateSN: global.AC.Config.BmsCertificateSN,
		},
	}
	err := global.AC.Clients.BmsClient.AuthorizationSuper(authorization, topicOperation)
	return err
}

func CheckAsyncInvokeConfig(inputFunc *dao.Function) error {
	if inputFunc.AsyncInvokeConfig != nil {
		// 最大失败重试次数0-3
		if inputFunc.AsyncInvokeConfig.MaxRetryAttempts != nil && (*inputFunc.AsyncInvokeConfig.MaxRetryAttempts < 0 || *inputFunc.AsyncInvokeConfig.MaxRetryAttempts > api.MaxRetryAttempts) {
			return kunErr.NewInvalidParameterValueException("params invalid, max retry attempts invalid", nil)
		}
		// 消息最大保留时间限制0-12小时
		if inputFunc.AsyncInvokeConfig.MaxRetryIntervalInSeconds != nil && (*inputFunc.AsyncInvokeConfig.MaxRetryIntervalInSeconds < 0 || *inputFunc.AsyncInvokeConfig.MaxRetryIntervalInSeconds > api.MaxRetryIntervalInSeconds) {
			return kunErr.NewInvalidParameterValueException("params invalid, max retry interval invalid", nil)
		}
		// 检查目标服务
		if inputFunc.AsyncInvokeConfig.OnSuccess != nil {
			if err := CheckDestinationConfig(inputFunc.AsyncInvokeConfig.OnSuccess, inputFunc); err != nil {
				return err
			}
		}
		if inputFunc.AsyncInvokeConfig.OnFailure != nil {
			if err := CheckDestinationConfig(inputFunc.AsyncInvokeConfig.OnFailure, inputFunc); err != nil {
				return err
			}
		}
	}
	return nil
}

func CheckDestinationConfig(des *api.DestinationConfig, f *dao.Function) error {
	var err error
	if des.Type == api.DestinationTypeForKafka {
		// 查看CFC超级证书是否有topic的写权限
		authorization := &bms.SuperAuthorization{
			TopicOwnerAccountID: f.Uid,
			Authorization: &bms.Authorization{
				AccountId:     global.AC.Config.BmsAccountID,
				Topic:         des.Destination,
				CertificateSN: global.AC.Config.BmsCertificateSN,
			},
		}
		err = global.AC.Clients.BmsClient.AuthorizationSuper(authorization, api.TopicWriteOperation)
		if err != nil {
			err = kunErr.NewInvalidParameterValueException("params invalid, bms topic invalid, err : ", err)
		}
	} else if des.Type == api.DestinationTypeForCfc {
		// 查看函数brn是否存在
		err = CheckFunctionExist(des.Destination, f)
	}
	return err
}

func CheckFunctionExist(funcBrn string, f *dao.Function) error {
	var err error

	// 检查目标函数是否为原函数
	if funcBrn == f.FunctionBrn {
		return kunErr.NewInvalidParameterValueException("invalid function brn, des function cannot be the same one", nil)
	}

	// 检查目标函数是否存在
	parseRes, _ := brn.Parse(funcBrn)
	resource := strings.Split(parseRes.Resource, ":")
	if len(resource) != 3 {
		return kunErr.NewInvalidParameterValueException("invalid function brn", nil)
	}

	if !api.RegVersion.MatchString(resource[2]) {
		err = dao.FindOneAlias(&dao.Alias{AliasBrn: funcBrn, Uid: f.Uid})
	} else {
		err = dao.FindOneFunc(&dao.Function{FunctionBrn: funcBrn, Uid: f.Uid})
	}

	if err != nil {
		return apiErr.NewObjectNotFoundException("function doesn't exist", err)
	}

	return nil
}
