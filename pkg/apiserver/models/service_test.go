package models_test

import (
	"fmt"
	"testing"

	"github.com/stretchr/testify/assert"

	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/dao"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/models"
)

func TestFormatServices(t *testing.T) {
	serviceSlice := make([]dao.Service, 0)
	for i := 0; i < 2; i++ {
		service := &dao.Service{}
		service.ServiceName = fmt.Sprintf("test_%d", i)
		serviceSlice = append(serviceSlice, *service)
	}
	resSlice := models.FormatServices(&serviceSlice)
	for _, service := range resSlice {
		tmpService := service.(dao.Service)
		assert.Equal(t, tmpService.ServiceName, tmpService.ServiceName)
	}
}

func TestFormatServicesWithCountFun(t *testing.T) {
	serviceSlice := make([]dao.Service, 0)
	for i := 0; i < 2; i++ {
		service := &dao.Service{}
		service.ServiceName = fmt.Sprintf("test_%d", i)
		serviceSlice = append(serviceSlice, *service)
	}
	var resObj = new(dao.CountServiceFunResObj)
	resSlice := models.FormatServicesWithCountFun(&serviceSlice, resObj)
	for _, service := range resSlice {
		tmpService := service.(*dao.ServiceWithFun)
		assert.Equal(t, tmpService.ServiceName, tmpService.ServiceName)
	}
}