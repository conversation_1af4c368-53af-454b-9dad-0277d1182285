package models_test

import (
	"errors"
	"fmt"
	"testing"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/stretchr/testify/assert"

	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/dao"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/dbengine"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/global"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/models"
)

func TestFormatFunctionRes(t *testing.T) {
	funcSlice := make([]dao.Function, 0)
	for i := 0; i < 2; i++ {
		function := &dao.Function{
			FunctionBrn: fmt.Sprintf("function_brn_%d", i),
		}
		function.FunctionName = fmt.Sprintf("test_%d", i)
		funcSlice = append(funcSlice, *function)
	}
	resSlice := models.FormatFunctionRes(funcSlice)
	for _, function := range resSlice {
		tmpFunc := function.(dao.Function)
		assert.Equal(t, tmpFunc.FunctionArn, tmpFunc.FunctionBrn)
	}
}

func TestGetVersionByAlias(t *testing.T) {
	cases := []struct {
		in_aliasNme string
		in_uid      string
		in_region   string
		in_funcName string
		out_err     error
	}{
		{
			in_aliasNme: "test_1",
			in_uid:      "uiduid",
			in_region:   "bj",
			in_funcName: "test",
			out_err:     nil,
		},
		{
			in_aliasNme: "#12a",
			in_uid:      "uiduid",
			in_region:   "bj",
			in_funcName: "test",
			out_err:     errors.New("aliasName not match Regexp"),
		},
	}

	for _, tc := range cases {
		var m sqlmock.Sqlmock
		m, dbengine.Engine = global.MockDB()
		m.ExpectQuery(".*").WillReturnRows(global.GetRowsAliases(global.GetTestAlias(1)))
		_, err := models.GetVersionByAlias(tc.in_aliasNme, tc.in_uid, tc.in_region, tc.in_funcName)
		assert.Equal(t, tc.out_err, err)
	}
}

func TestGetPreVersion(t *testing.T) {
	cases := []struct {
		in_uid    string
		in_region string
		in_fName  string
		out_err   error
	}{
		{
			in_uid:    "uiduid",
			in_region: "bj",
			in_fName:  "test",
			out_err:   nil,
		},
	}
	for _, tc := range cases {
		var m sqlmock.Sqlmock
		m, dbengine.Engine = global.MockDB()
		m.ExpectQuery(".*").WillReturnRows(global.GetRowsFunctions(global.GetTestFunc(1)))
		_, err := models.GetPreVersion(tc.in_uid, tc.in_region, tc.in_fName)
		assert.Equal(t, tc.out_err, err)
	}
}

func TestGetNextVersion(t *testing.T) {
	cases := []struct {
		in_uid    string
		in_region string
		in_fName  string
		out_err   error
	}{
		{
			in_uid:    "uiduid",
			in_region: "bj",
			in_fName:  "test",
			out_err:   nil,
		},
	}
	for _, tc := range cases {
		var m sqlmock.Sqlmock
		m, dbengine.Engine = global.MockDB()
		m.ExpectQuery(".*").WillReturnRows(global.GetRowsFunctions(global.GetTestFunc(1)))
		_, err := models.GetNextVersion(tc.in_uid, tc.in_region, tc.in_fName)
		assert.Equal(t, tc.out_err, err)
	}
}

func TestPublishNewFunc(t *testing.T) {
	global.MockAC()
	global.RegisterFaasRuntimeGovalidatorTag()
	function := &dao.Function{
		Uid:         "uiduid",
		FunctionBrn: "function_brn",
	}
	function.FunctionName = "test"
	cases := []struct {
		in_func    dao.Function
		in_version string
		out_err    error
	}{
		{
			in_func:    *function,
			in_version: "1",
			out_err:    nil,
		},
	}

	for _, tc := range cases {
		var m sqlmock.Sqlmock
		m, dbengine.Engine = global.MockDB()
		m.ExpectExec(".*").WillReturnResult(sqlmock.NewResult(1, 1))
		_, err := models.PublishNewFunc(tc.in_func, tc.in_version)
		assert.Equal(t, tc.out_err, err)
	}
}
