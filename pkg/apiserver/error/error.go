package error

import (
	"net/http"

	kunErr "icode.baidu.com/baidu/faas/kun/pkg/error"
)

const (
	// CodeStorageExceededException = You have exceeded your maximum total code size per account
	// Refer: http://docs.aws.amazon.com/lambda/latest/dg/limits.html
	//
	//   | Item                                                            | Default limit  |
	//   | --------------------------------------------------------------- | -------------- |
	//   | Function deployment package size (compressed .zip/.jar file)    | 50 MB          |
	//   | Total size of all the deployment packages that can be uploaded  | 75GB           |
	//   |     per region                                                  |                |
	//   | Size of code/dependencies that you can zip into a deployment    | 250MB          |
	//   |     package (uncompressed .zip/.jar size). Note that            |                |
	//   |     Each function receives an additional 500MB of               |                |
	//   |     non-persistent disk space in its own /tmp directory.        |                |
	//   |     The /tmp directory can be used for loading additional       |                |
	//   |     resources like dependency libraries or data sets during     |                |
	//   |     function initialization.                                    |                |
	//   | Total size of environment variables set                         | 4 KB           |
	//
	// Functions that exceed any of the limits listed in the previous limits tables will fail
	// with an exceeded limits exception. These limits are fixed and cannot be changed at this time.
	// For example, if you receive the exception CodeStorageExceededException or an error message
	// similar to "Code storage limit exceeded", you need to reduce the size of your code storage.
	CodeStorageExceededException kunErr.ErrorType = "CodeStorageExceededException"

	// InvalidZipFileException = could not unzip the function zip file.
	InvalidZipFileException kunErr.ErrorType = "InvalidZipFileException"

	// ResourceConflictException = the resource already exists.
	ResourceConflictException kunErr.ErrorType = "ResourceConflictException"

	ResourceNotAccessException kunErr.ErrorType = "ResourceNotAccessException"

	RocketmqConsumerConflictException kunErr.ErrorType = "RocketmqConsumerConflictException"

	// ResourceNotFoundException = The resource (for example, a function or access policy
	// statement) specified in the request does not exist.
	ResourceNotFoundException kunErr.ErrorType = "ResourceNotFoundException"
	UserNotFoundException     kunErr.ErrorType = "UserNotFoundException"

	RealNameQualificationException kunErr.ErrorType = "RealNameQualificationException"
	UpdateLegalStatusException     kunErr.ErrorType = "UpdateLegalStatusException"

	QueryOrderErrException  kunErr.ErrorType = "QueryOrderErrException"
	UpdateOrderErrException kunErr.ErrorType = "UpdateOrderErrException"

	//LogContentLengthExceededException = the content length of the request log is too large
	LogContentLengthExceededException kunErr.ErrorType = "LogContentLengthExceededException"

	//TestEventConflictException = the test event already exists
	TestEventConflictException kunErr.ErrorType = "TestEventConflictException"

	//TestEventStorageExceededException = test event limit exceeded
	TestEventStorageExceededException kunErr.ErrorType = "TestEventStorageExceededException"

	//TestEventNotFoundException = test event not found
	TestEventNotFoundException kunErr.ErrorType = "TestEventNotFoundException"

	BlsAccessDeniedException kunErr.ErrorType = "BlsAccessDeniedException"

	BlsNoIndexRequestException kunErr.ErrorType = "BlsNoIndexRequestException"
	LogStoreNotFoundException  kunErr.ErrorType = "LogStoreNotFoundException"
	BlsQueryInvalidException   kunErr.ErrorType = "BlsQueryInvalidException"

	BosAccessDeniedException kunErr.ErrorType = "BosAccessDeniedException"

	OjbectTooLargeException kunErr.ErrorType = "ObjectTooLargeException"

	ObjectMetaNotFoundException kunErr.ErrorType = "ObjectMetaNotFoundException"

	ObjectNotFoundException kunErr.ErrorType = "ObjectNotFoundException"

	ServiceNotFoundException kunErr.ErrorType = "ServiceNotFoundException"

	ServiceNotEmptyException kunErr.ErrorType = "ServiceNotEmptyException"

	ServiceExceedMaximumException kunErr.ErrorType = "ServiceExceedMaximumException"
)

// NewCodeStorageExceededException creates a CodeStorageExceededException
// basic usage:
//
//	if (err != nil) {
//	    return NewCodeStorageExceededException(cause, err).WriteTo(w)
//	}
//
// The function will automatically write log using glog
// HTTP status code is StatusBadRequest (400)
func NewCodeStorageExceededException(cause string, lasterr error) kunErr.FinalError {
	return kunErr.NewGenericException(kunErr.BasicError{
		Code:    CodeStorageExceededException,
		Cause:   cause,
		Message: "You have exceeded your maximum total code size per account",
		Status:  http.StatusBadRequest,
	}, lasterr)
}

// NewResourceConflictException creates a ResourceConflictException.
// HTTP status code is StatusConflict (409)
func NewResourceConflictException(cause string, lasterr error) kunErr.FinalError {
	return kunErr.NewGenericException(kunErr.BasicError{
		Code:    ResourceConflictException,
		Cause:   cause,
		Message: "The resource already exists",
		Status:  http.StatusConflict,
	}, lasterr)
}

func NewRocetmqConsumerConflictException(cause string, lasterr error) kunErr.FinalError {
	return kunErr.NewGenericException(kunErr.BasicError{
		Code:    RocketmqConsumerConflictException,
		Cause:   cause,
		Message: "Rocketmq consumer group already exists",
		Status:  http.StatusConflict,
	}, lasterr)
}

func NewRocketmqNoIntranetAccessException(cause string, lasterr error) kunErr.FinalError {
	return kunErr.NewGenericException(kunErr.BasicError{
		Code:    ResourceNotAccessException,
		Cause:   cause,
		Message: "rocketmq no intranet access",
		Status:  http.StatusConflict,
	}, lasterr)
}

// NewResourceNotFoundException creates a ResourceNotFoundException
// HTTP status code is StatusNotFound (404)
func NewResourceNotFoundException(cause string, lasterr error) kunErr.FinalError {
	return kunErr.NewGenericException(kunErr.BasicError{
		Code:    ResourceNotFoundException,
		Cause:   cause,
		Message: "The resource specified in the request does not exist",
		Status:  http.StatusNotFound,
	}, lasterr)
}

func NewLegalNotFoundException(cause string, lasterr error) kunErr.FinalError {
	return kunErr.NewGenericException(kunErr.BasicError{
		Code:    ResourceNotFoundException,
		Cause:   cause,
		Message: "create use legal error",
		Status:  http.StatusInternalServerError,
	}, lasterr)
}

func CreateLegalException(cause string, lasterr error) kunErr.FinalError {
	return kunErr.NewGenericException(kunErr.BasicError{
		Code:    ResourceNotFoundException,
		Cause:   cause,
		Message: "The use legal does not exist",
		Status:  http.StatusInternalServerError,
	}, lasterr)
}

func UpdateLegalException(cause string, lasterr error) kunErr.FinalError {
	return kunErr.NewGenericException(kunErr.BasicError{
		Code:    UpdateLegalStatusException,
		Cause:   cause,
		Message: "Update use legal error",
		Status:  http.StatusInternalServerError,
	}, lasterr)
}

func CreateRealNameQualificationException(cause string, lasterr error) kunErr.FinalError {
	return kunErr.NewGenericException(kunErr.BasicError{
		Code:    RealNameQualificationException,
		Cause:   cause,
		Message: "create real name qualification error",
		Status:  http.StatusInternalServerError,
	}, lasterr)
}

func UpdateRealNameQualificationException(cause string, lasterr error) kunErr.FinalError {
	return kunErr.NewGenericException(kunErr.BasicError{
		Code:    RealNameQualificationException,
		Cause:   cause,
		Message: "update real name qualification error",
		Status:  http.StatusInternalServerError,
	}, lasterr)
}

func NewRealNameQualificationException(cause string, lasterr error) kunErr.FinalError {
	return kunErr.NewGenericException(kunErr.BasicError{
		Code:    RealNameQualificationException,
		Cause:   cause,
		Message: "you must complete real name authentication : https://console.bce.baidu.com/qualify/#/qualify/index",
		Status:  http.StatusInternalServerError,
	}, lasterr)
}

// NewInvalidZipFileException creates a NewGenericException
// HTTP status code is StatusBadGateway (502)
func NewInvalidZipFileException(cause string, lasterr error) kunErr.FinalError {
	return kunErr.NewGenericException(kunErr.BasicError{
		Code:    InvalidZipFileException,
		Cause:   cause,
		Message: "Could not unzip the function zip file",
		Status:  http.StatusBadRequest,
	}, lasterr)
}

// 获取blsClient失败
func NewInitBlsClientException(message, cause string, lasterr error) kunErr.FinalError {
	return kunErr.NewGenericException(kunErr.BasicError{
		Code:    BlsAccessDeniedException,
		Cause:   cause,
		Message: message,
		Status:  http.StatusBadRequest,
	}, lasterr)
}

// bls索引不存在
func NewBlsNoIndexRequestException(message, cause string, lasterr error) kunErr.FinalError {
	return kunErr.NewGenericException(kunErr.BasicError{
		Code:    BlsNoIndexRequestException,
		Cause:   cause,
		Message: message,
		Status:  http.StatusBadRequest,
	}, lasterr)
}

// blsStore不存在
func NewLogStoreNotFoundtException(message, cause string, lasterr error) kunErr.FinalError {
	return kunErr.NewGenericException(kunErr.BasicError{
		Code:    LogStoreNotFoundException,
		Cause:   cause,
		Message: message,
		Status:  http.StatusBadRequest,
	}, lasterr)
}

// bls查询输入不合法
func NewBlsQueryInvalidException(message, cause string, lasterr error) kunErr.FinalError {
	return kunErr.NewGenericException(kunErr.BasicError{
		Code:    BlsQueryInvalidException,
		Cause:   cause,
		Message: message,
		Status:  http.StatusBadRequest,
	}, lasterr)
}

// 获取bosClient失败
func NewInitBosClientException(message, cause string, lasterr error) kunErr.FinalError {
	return kunErr.NewGenericException(kunErr.BasicError{
		Code:    BosAccessDeniedException,
		Cause:   cause,
		Message: message,
		Status:  http.StatusBadRequest,
	}, lasterr)
}

// object过大
func NewObjectTooLargeException(cause string, lasterr error) kunErr.FinalError {
	return kunErr.NewGenericException(kunErr.BasicError{
		Code:    OjbectTooLargeException,
		Cause:   cause,
		Message: "The Object is larger than 50MB",
		Status:  http.StatusRequestEntityTooLarge,
	}, lasterr)
}

// ObjectMeta不存在的情况
func NewObjectMetaNotFoundException(cause string, lasterr error) kunErr.FinalError {
	return kunErr.NewGenericException(kunErr.BasicError{
		Code:    ObjectMetaNotFoundException,
		Cause:   cause,
		Message: "The object meta does not exist",
		Status:  http.StatusNotFound,
	}, lasterr)
}

// object获取失败的情况
func NewObjectNotFoundException(cause string, lasterr error) kunErr.FinalError {
	return kunErr.NewGenericException(kunErr.BasicError{
		Code:    ObjectNotFoundException,
		Cause:   cause,
		Message: "The object does not exist",
		Status:  http.StatusNotFound,
	}, lasterr)
}

// 一个通用400错误
func NewInvalidRequestException(message, cause string, lasterr error) kunErr.FinalError {
	return kunErr.NewGenericException(kunErr.BasicError{
		Code:    kunErr.InvalidParameterValueException,
		Cause:   cause,
		Message: message,
		Status:  http.StatusBadRequest,
	}, nil)
}

// 请求参数过多的情况
func NewTooManyParametersException(cause string, lasterr error) kunErr.FinalError {
	return kunErr.NewGenericException(kunErr.BasicError{
		Code:    kunErr.InvalidParameterValueException,
		Cause:   cause,
		Message: "Too Many Parameters in the request",
		Status:  http.StatusBadRequest,
	}, nil)
}

// 根据req初始化函数信息失败
func NewInitFuncMetaException(message, cause string, lasterr error) kunErr.FinalError {
	return kunErr.NewGenericException(kunErr.BasicError{
		Code:    kunErr.InvalidParameterValueException,
		Cause:   cause,
		Message: message,
		Status:  http.StatusBadRequest,
	}, lasterr)
}

// 缺失必要参数信息
func NewMissingParametersException(cause string, lasterr error) kunErr.FinalError {
	return kunErr.NewGenericException(kunErr.BasicError{
		Code:    kunErr.InvalidParameterValueException,
		Cause:   cause,
		Message: "One of the parameters in the request is missing",
		Status:  http.StatusBadRequest,
	}, lasterr)
}

func NewUserNotFoundException(cause string, lasterr error) kunErr.FinalError {
	return kunErr.NewGenericException(kunErr.BasicError{
		Code:    UserNotFoundException,
		Cause:   cause,
		Message: "User not found",
		Status:  http.StatusUnauthorized,
	}, lasterr)
}

func NewQueryOrderException(cause string, lasterr error) kunErr.FinalError {
	return kunErr.NewGenericException(kunErr.BasicError{
		Code:    QueryOrderErrException,
		Cause:   cause,
		Message: "Query order from billing server err",
		Status:  http.StatusInternalServerError,
	}, lasterr)
}

func NewQueryPackageException(cause string, lasterr error) kunErr.FinalError {
	return kunErr.NewGenericException(kunErr.BasicError{
		Code:    QueryOrderErrException,
		Cause:   cause,
		Message: "Query package from billing server err",
		Status:  http.StatusInternalServerError,
	}, lasterr)
}

func CreatePackageException(cause string, lasterr error) kunErr.FinalError {
	return kunErr.NewGenericException(kunErr.BasicError{
		Code:    QueryOrderErrException,
		Cause:   cause,
		Message: "Create package from billing server err",
		Status:  http.StatusInternalServerError,
	}, lasterr)
}

func NewUpdateOrderException(cause string, lasterr error) kunErr.FinalError {
	return kunErr.NewGenericException(kunErr.BasicError{
		Code:    UpdateOrderErrException,
		Cause:   cause,
		Message: "Update order to billing server err",
		Status:  http.StatusInternalServerError,
	}, lasterr)
}

// NewLogContentLengthExceededException the request log is too large
func NewLogContentLengthExceededException(cause string, lasterr error) kunErr.FinalError {
	return kunErr.NewGenericException(kunErr.BasicError{
		Code:    LogContentLengthExceededException,
		Cause:   cause,
		Message: "the contents of the log are too large. Please download manually",
		Status:  http.StatusRequestEntityTooLarge,
	}, lasterr)
}

// NewTestEventConflictException creates a TestEventConflictException.
// HTTP status code is StatusConflict (409)
func NewTestEventConflictException(cause string, lasterr error) kunErr.FinalError {
	return kunErr.NewGenericException(kunErr.BasicError{
		Code:    TestEventConflictException,
		Cause:   cause,
		Message: "The test event already exists",
		Status:  http.StatusConflict,
	}, lasterr)
}

// NewTestEventStorageExceededException creates a TestEventStorageExceededException.
// HTTP status code is StatusBadRequest (400)
func NewTestEventStorageExceededException(cause string, lasterr error) kunErr.FinalError {
	return kunErr.NewGenericException(kunErr.BasicError{
		Code:    TestEventStorageExceededException,
		Cause:   cause,
		Message: "You have exceeded your maximum total test event size",
		Status:  http.StatusBadRequest,
	}, lasterr)
}

// NewTestEventNotFoundException creates a TestEventNotFoundException
// HTTP status code is StatusNotFound (404)
func NewTestEventNotFoundException(cause string, lasterr error) kunErr.FinalError {
	return kunErr.NewGenericException(kunErr.BasicError{
		Code:    TestEventNotFoundException,
		Cause:   cause,
		Message: "The test event specified in the request does not exist",
		Status:  http.StatusNotFound,
	}, lasterr)
}

// NewServiceNotFoundException creates a ServiceNotFoundException
// HTTP status code is StatusNotFound (404)
func NewServiceNotFoundException(cause string, lasterr error) kunErr.FinalError {
	return kunErr.NewGenericException(kunErr.BasicError{
		Code:    ServiceNotFoundException,
		Cause:   cause,
		Message: "The resource specified in the request does not exist",
		Status:  http.StatusNotFound,
	}, lasterr)
}

// DeleteServiceNotEmpty creates a ServiceNotEmptyException
func DeleteServiceNotEmpty(cause string, lasterr error) kunErr.FinalError {
	return kunErr.NewGenericException(kunErr.BasicError{
		Code:    ServiceNotEmptyException,
		Cause:   cause,
		Message: "The service contains functions can not delete",
		Status:  http.StatusConflict,
	}, lasterr)
}

// CreateServiceExceedMaximum creates a ServiceExceedMaximumException
func CreateServiceExceedMaximum(cause string, lasterr error) kunErr.FinalError {
	return kunErr.NewGenericException(kunErr.BasicError{
		Code:    ServiceExceedMaximumException,
		Cause:   cause,
		Message: "Total service number exceed maximum",
		Status:  http.StatusBadRequest,
	}, lasterr)
}
