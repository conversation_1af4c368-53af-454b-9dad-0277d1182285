package error

import (
	"github.com/stretchr/testify/assert"
	"testing"
)

func TestNewServiceNotFoundException(t *testing.T) {
	var err error
	errors := NewServiceNotFoundException("msg", err)
	assert.NotNil(t, errors)
}

func TestDeleteServiceNotEmpty(t *testing.T) {
	var err error
	errors := DeleteServiceNotEmpty("msg", err)
	assert.NotNil(t, errors)
}

func TestCreateServiceExceedMaximum(t *testing.T) {
	var err error
	errors := CreateServiceExceedMaximum("msg", err)
	assert.NotNil(t, errors)
}

func TestNewBlsNoIndexRequestException(t *testing.T) {
	var err error
	errors := NewBlsNoIndexRequestException("cause", "msg", err)
	assert.NotNil(t, errors)
}

func TestNewLogStoreNotFoundtException(t *testing.T) {
	var err error
	errors := NewLogStoreNotFoundtException("cause", "msg", err)
	assert.NotNil(t, errors)
}

func TestNewBlsQueryInvalidException(t *testing.T) {
	var err error
	errors := NewBlsQueryInvalidException("cause", "msg", err)
	assert.NotNil(t, errors)
}