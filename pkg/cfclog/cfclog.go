package cfclog

import (
	"bytes"
	"encoding/json"
	"errors"
	"time"
	"unicode/utf8"
)

type CfcLogFile interface {
	Write(l *CfcLog, buf []byte) (int, error)
	Close() error
}

// CfcLog marshals encoded JSONLog objects
type Cfc<PERSON>og struct {
	Created   time.Time `json:"ts"`
	RequestID string    `json:"rid"`
	FuncName  string    `json:"func,omitempty"`
	Version   string    `json:"version,omitempty"`
	UserID    string    `json:"userid,omitempty"`
	Source    string    `json:"src"`
	Message   json.RawMessage `json:"msg"`
}

func (l *CfcLog) Reset() {
	l.Created = time.Now()
	l.RequestID = ""
	l.Source = ""
	l.Message = []byte{}
}

// MarshalJSONBuf is an optimized JSON marshaller that avoids reflection
// and unnecessary allocation.
func (mj *CfcLog) MarshalJSONBuf(buf *bytes.Buffer) error {
	var first = true

	buf.WriteString(`{`)
	if len(mj.Message) != 0 {
		first = false
		buf.WriteString(`"msg":`)
		ffjsonWriteJSONBytesAsString(buf, mj.Message)
	}
	if len(mj.Source) != 0 {
		if first {
			first = false
		} else {
			buf.WriteString(`,`)
		}
		buf.WriteString(`"src":`)
		ffjsonWriteJSONBytesAsString(buf, []byte(mj.Source))
	}
	if len(mj.RequestID) != 0 {
		if first {
			first = false
		} else {
			buf.WriteString(`,`)
		}
		buf.WriteString(`"rid":`)
		ffjsonWriteJSONBytesAsString(buf, []byte(mj.RequestID))
	}
	if !first {
		buf.WriteString(`,`)
	}

	created, err := fastTimeMarshalJSON(mj.Created)
	if err != nil {
		return err
	}

	buf.WriteString(`"ts":`)
	buf.WriteString(created)
	buf.WriteString("}\n") // 每个json日志行追加一个\n
	return nil
}

func ffjsonWriteJSONBytesAsString(buf *bytes.Buffer, s []byte) {
	const hex = "0123456789abcdef"

	buf.WriteByte('"')
	start := 0
	for i := 0; i < len(s); {
		if b := s[i]; b < utf8.RuneSelf {
			if 0x20 <= b && b != '\\' && b != '"' && b != '<' && b != '>' && b != '&' {
				i++
				continue
			}
			if start < i {
				buf.Write(s[start:i])
			}
			switch b {
			case '\\', '"':
				buf.WriteByte('\\')
				buf.WriteByte(b)
			case '\n':
				buf.WriteByte('\\')
				buf.WriteByte('n')
			case '\r':
				buf.WriteByte('\\')
				buf.WriteByte('r')
			default:
				buf.WriteString(`\u00`)
				buf.WriteByte(hex[b>>4])
				buf.WriteByte(hex[b&0xF])
			}
			i++
			start = i
			continue
		}
		c, size := utf8.DecodeRune(s[i:])
		if c == utf8.RuneError && size == 1 {
			if start < i {
				buf.Write(s[start:i])
			}
			buf.WriteString(`\ufffd`)
			i += size
			start = i
			continue
		}

		if c == '\u2028' || c == '\u2029' {
			if start < i {
				buf.Write(s[start:i])
			}
			buf.WriteString(`\u202`)
			buf.WriteByte(hex[c&0xF])
			i += size
			start = i
			continue
		}
		i += size
	}
	if start < len(s) {
		buf.Write(s[start:])
	}
	buf.WriteByte('"')
}

const jsonFormat = `"` + time.RFC3339Nano + `"`

// fastTimeMarshalJSON avoids one of the extra allocations that
// time.MarshalJSON is making.
func fastTimeMarshalJSON(t time.Time) (string, error) {
	if y := t.Year(); y < 0 || y >= 10000 {
		// RFC 3339 is clear that years are 4 digits exactly.
		// See golang.org/issue/4556#c15 for more discussion.
		return "", errors.New("time.MarshalJSON: year outside of range [0,9999]")
	}
	return t.Format(jsonFormat), nil
}
