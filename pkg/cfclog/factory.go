package cfclog

import (
	"fmt"
)

type LogCreator func(string, int) (CfcLogFile, error)

var (
	creators = map[string]LogCreator{}
)

func RegisterLogWriter(logtype string, creator LogCreator) {
	creators[logtype] = creator
}

func CreateLogWriter(logtype string, fpath string, cap int) (CfcLogFile, error) {
	creator, ok := creators[logtype]
	if !ok {
		return nil, fmt.Errorf("unknown logtype %s", logtype)
	}
	return creator(fpath, cap)
}
