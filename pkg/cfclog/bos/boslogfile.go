package bos

import (
	"bytes"
	"errors"
	"os"
	"sync/atomic"
	"time"

	"icode.baidu.com/baidu/faas/kun/pkg/cfclog"
)

func init() {
	cfclog.RegisterLogWriter("bos", NewBOSLogFile)
}

type bosLogFile struct {
	writer   *os.File
	capacity int32
}

func NewBOSLogFile(fpath string, cap int) (cfclog.CfcLogFile, error) {
	file, err := os.OpenFile(fpath, os.O_WRONLY|os.O_CREATE|os.O_APPEND, 0644)
	if err != nil {
		return nil, err
	}
	return &bosLogFile{
		writer:   file,
		capacity: int32(cap),
	}, nil
}

var (
	LineByte        = []byte{'\n'}
	errLogClosed    = errors.New("log file closed")
	errOverCapacity = errors.New("log file over capacity")
)

func (f *bosLogFile) Write(l *cfclog.CfcLog, buf []byte) (int, error) {
	w := f.writer
	if w == nil {
		return 0, errLogClosed
	}
	if f.capacity <= 0 {
		return 0, errOverCapacity
	}
	tmp := bytes.NewBuffer(nil)
	i, j := 0, 0
	for i < len(buf) {
		j = bytes.Index(buf[i:], LineByte)
		if j < 0 {
			j = len(buf)
		} else if j > 0 {
			j = i + j
		} else { // j == 0，跳过空行
			i++
			continue
		}
		tmp.WriteString(l.Created.Format(time.RFC3339))
		tmp.WriteByte('\t')
		tmp.Write(buf[i:j])
		tmp.WriteByte('\n')
		i = j + 1
	}
	w.Write(tmp.Bytes())
	atomic.AddInt32(&f.capacity, int32(-i))
	return i, nil
}

func (f *bosLogFile) Close() error {
	w := f.writer
	f.writer = nil
	return w.Close()
}
