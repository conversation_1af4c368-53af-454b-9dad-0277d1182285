package bos

import (
	"fmt"
	"io/ioutil"
	"path"
	"testing"
	"time"

	"icode.baidu.com/baidu/faas/kun/pkg/cfclog"
)

func TestBOSLogfile(t *testing.T) {

	tmpdir, _ := ioutil.TempDir("", "jsonlog")
	tmpfile := path.Join(tmpdir, "testlog")
	jf, err := NewBOSLogFile(tmpfile, 1024)
	if err != nil {
		t.Error(err)
		return
	}
	l := &cfclog.CfcLog{
		Created:   time.Now(),
		RequestID: "66525001-1e97-469b-a151-cd264f519711",
		Source:    "cfc",
	}
	msg := []byte("www.baidu.com log message")
	for i := 0; i < 5; i++ {
		_, err = jf.Write(l, msg)
		if err != nil {
			t.Error(err)
			return
		}
	}
	i := 0
	for ; i < 10; i++ {
		_, err = jf.Write(l, msg)
		if err != nil {
			t.Log(err)
			break
		}
	}
	if i < 10 {
		t.Error("capacity error")
		return
	}
	jf.Close()
	_, err = jf.Write(l, msg)
	if err == nil {
		t.Error("close failed")
		return
	}
	t.Log(err)

	// dump文件内容
	data, err := ioutil.ReadFile(tmpfile)
	if err != nil {
		t.Error(err)
		return
	}
	fmt.Print(string(data))
}
