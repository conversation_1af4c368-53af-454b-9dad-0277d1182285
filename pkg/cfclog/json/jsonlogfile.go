package json

import (
	"bytes"
	"errors"
	"os"
	"sync/atomic"

	"icode.baidu.com/baidu/faas/kun/pkg/cfclog"
)

var (
	LineByte        = []byte{'\n'}
	errLogClosed    = errors.New("log file closed")
	errOverCapacity = errors.New("log file over capacity")
)

func init() {
	cfclog.RegisterLogWriter("bls", NewJSONLogFile)
	cfclog.RegisterLogWriter("es", NewJSONLogFile)
	cfclog.RegisterLogWriter("kafka", NewJSONLogFile)
	cfclog.RegisterLogWriter("fluentd_kafka", NewJSONLogFile)
}

type JSONLogFile struct {
	writer   *os.File // 文件
	capacity int32    // 最大容量
}

func NewJSONLogFile(fpath string, cap int) (cfclog.CfcLogFile, error) {
	f, err := os.OpenFile(fpath, os.O_WRONLY|os.O_APPEND|os.O_CREATE, 0644)
	if err != nil {
		return nil, err
	}
	w := &JSONLogFile{
		writer:   f,
		capacity: int32(cap),
	}
	return w, nil
}

func (f *JSONLogFile) Write(l *cfclog.CfcLog, buf []byte) (int, error) {
	w := f.writer
	if w == nil {
		return 0, errLogClosed
	}
	if f.capacity <= 0 {
		return 0, errOverCapacity
	}
	tmp := bytes.NewBuffer(nil)
	i, j := 0, 0
	for i < len(buf) {
		j = bytes.Index(buf[i:], LineByte)
		if j < 0 {
			j = len(buf)
		} else if j > 0 {
			j = i + j
		} else { // j == 0，跳过空行
			i++
			continue
		}
		l.Message = buf[i:j]
		if err := l.MarshalJSONBuf(tmp); err != nil {
			return 0, err
		}
		i = j + 1
	}
	w.Write(tmp.Bytes())
	atomic.AddInt32(&f.capacity, int32(-i))
	return i, nil
}

func (f *JSONLogFile) Close() error {
	w := f.writer
	f.writer = nil
	return w.Close()
}
