package json

import (
	"encoding/json"
	"fmt"
	"icode.baidu.com/baidu/faas/kun/pkg/cfclog"
	"icode.baidu.com/baidu/faas/kun/pkg/util/logs"
	"io"
	"io/ioutil"
	"os"
	"path"
	"testing"
	"time"
)

func TestJSONLogFile(t *testing.T) {
	tmpdir, _ := ioutil.TempDir("", "jsonlog")
	tmpfile := path.Join(tmpdir, "testlog")
	jf, err := NewJSONLogFile(tmpfile, 200)
	if err != nil {
		t.Error(err)
		return
	}
	l := &cfclog.CfcLog{
		//Created:   time.Now(),
		RequestID: "66525001-1e97-469b-a151-cd264f519711",
		Source:    "cfc",
	}
	msg := []byte("www.baidu.com log message")
	for i := 0; i < 5; i++ {
		l.Created = time.Now()
		//msg1 := []byte("START RequestId: ddf3c52a-0fd5-4254-87e1-2ca87bcacf5b Version: $LATEST")
		_, err = jf.Write(l, msg)
		if err != nil {
			t.Error(err)
			return
		}
	}
	i := 0
	for ; i < 10; i++ {
		_, err = jf.Write(l, msg)
		if err != nil {
			t.Log(err)
			break
		}
	}
	if i == 10 {
		t.Error("capacity error")
		return
	}
	jf.Close()
	_, err = jf.Write(l, msg)
	if err == nil {
		t.Error("close failed")
		return
	}
	t.Log(err)
	file, err := os.Open(tmpfile)
	defer file.Close()
	if err != nil {

		t.Error(err)
		return
	}
	decoder := json.NewDecoder(file)

	var log interface{}
	err = decoder.Decode(&log)
	fmt.Println(log)
	logs.Infof("===translate err :%+v===", err)
	// dump文件内容
	data, err := ioutil.ReadFile(tmpfile)
	if err != nil {
		t.Error(err)
		return
	}
	fmt.Print(string(data))
	// t.Log(string(data))
}

func translate(fpath string, cb func(log *cfclog.CfcLog) error) error {
	logs.Infof("===translate====")
	data, err := ioutil.ReadFile(fpath)
	logs.Infof("===data:%+v===", string(data))
	file, err := os.Open(fpath)
	if err != nil {
		return err
	}
	defer file.Close()

	decoder := json.NewDecoder(file)
	var log cfclog.CfcLog
	for {
		log.Reset()
		err = decoder.Decode(&log)
		logs.Infof("===translate err :%+v===", err)
		if err != nil {
			if err == io.EOF {
				return nil
			}
			return err
		}
		err = cb(&log)
		if err != nil {
			return err
		}
	}
	return nil
}
