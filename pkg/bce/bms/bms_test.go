package bms

import (
	"fmt"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"

	"icode.baidu.com/baidu/faas/kun/pkg/api"
	"icode.baidu.com/baidu/faas/kun/pkg/bce/sts_credential"
	"icode.baidu.com/baidu/faas/kun/pkg/util/json"
)

var topic = &Topic{
	PartitionCount: 1,
	TopicName:      "myTopic",
}

var (
	topicList             = &TopicList{}
	createCertificateArgs = &CreateCertificateArgs{
		AccountId: "uiduid",
	}
	authorization = &Authorization{
		TopicOperation: "Read",
		AccountId:      "uiduid",
		Topic:          "myTopic",
		CertificateId:  "c7ac82ae14ef42d1a4ffa3b2ececa17f",
		CertificateSN:  "73d1af3f69ec46d98d9f494d7b8b7005",
	}
	superAuthorization = &SuperAuthorization{
		TopicOwnerAccountID: "uiduid",
		Authorization:       authorization,
	}
	uid = "uiduid"
)

func TestNewBMSControl(t *testing.T) {
	stsCache, stsRoleName := prepare()
	ts := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
	}))
	defer ts.Close()
	res := NewBMSControl(ts.URL, nil, stsCache, stsRoleName)
	assert.NotNil(t, res)
}

func TestListTopic(t *testing.T) {
	stsCache, stsRoleName := prepare()
	topicList.Topics = append(topicList.Topics, topic)
	topicListJson, _ := json.Marshal(topicList)
	ts := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
		w.Write([]byte(topicListJson))
	}))
	defer ts.Close()
	bmsClient := NewBMSControl(ts.URL, nil, stsCache, stsRoleName)
	_, err := bmsClient.ListTopic(uid, "")
	assert.Equal(t, err, nil)
}

func TestListClusterTopic(t *testing.T) {
	stsCache, stsRoleName := prepare()
	topicList.Topics = append(topicList.Topics, topic)
	topicListJson, _ := json.Marshal(topicList)
	ts := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
		w.Write([]byte(topicListJson))
	}))
	defer ts.Close()
	bmsClient := NewBMSControl(ts.URL, nil, stsCache, stsRoleName)
	_, err := bmsClient.ListTopic(uid, "5d3213e0ee924c89bc460c4bd0ed8b67")
	assert.Equal(t, err, nil)
}

func TestListCluster(t *testing.T) {
	stsCache, stsRoleName := prepare()
	topicList.Topics = append(topicList.Topics, topic)
	topicListJson, _ := json.Marshal(topicList)
	ts := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
		w.Write([]byte(topicListJson))
	}))
	defer ts.Close()
	bmsClient := NewBMSControl(ts.URL, nil, stsCache, stsRoleName)
	_, err := bmsClient.ListClusters(uid)
	assert.Equal(t, err, nil)
}

func TestGetCluster(t *testing.T) {
	stsCache, stsRoleName := prepare()
	topicList.Topics = append(topicList.Topics, topic)
	topicListJson, _ := json.Marshal(topicList)
	ts := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
		w.Write([]byte(topicListJson))
	}))
	defer ts.Close()
	bmsClient := NewBMSControl(ts.URL, nil, stsCache, stsRoleName)
	_, err := bmsClient.GetCluster(uid, "5d3213e0ee924c89bc460c4bd0ed8b67")
	assert.Equal(t, err, fmt.Errorf("cluster: 5d3213e0ee924c89bc460c4bd0ed8b67 Not Found, user id: %s\n", uid))
}

func TestGetTopic(t *testing.T) {
	stsCache, stsRoleName := prepare()
	topicJson, _ := json.Marshal(topic)
	ts := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
		w.Write([]byte(topicJson))
	}))
	defer ts.Close()
	bmsClient := NewBMSControl(ts.URL, nil, stsCache, stsRoleName)
	_, err := bmsClient.GetTopic(uid, "myTopic")
	assert.Equal(t, err, nil)
}

func TestGetClusterEndPoint(t *testing.T) {
	stsCache, stsRoleName := prepare()
	topicJson, _ := json.Marshal(topic)
	ts := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
		w.Write([]byte(topicJson))
	}))
	defer ts.Close()
	bmsClient := NewBMSControl(ts.URL, nil, stsCache, stsRoleName)
	_, err := bmsClient.GetClusterEndPoint(uid, "b08969c502824cb889398236f93c3d5f")
	assert.Equal(t, err, nil)
}

func TestCreateCertificate(t *testing.T) {
	stsCache, stsRoleName := prepare()
	ts := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
	}))
	defer ts.Close()
	bmsClient := NewBMSControl(ts.URL, nil, stsCache, stsRoleName)

	err := bmsClient.CreateCertificate(uid, createCertificateArgs)
	assert.Equal(t, err, nil)
}

func TestListCertificate(t *testing.T) {
	stsCache, stsRoleName := prepare()
	certificate := &Certificate{
		AccountID:     "uiduid",
		Region:        "bj",
		Uuid:          "abcuid",
		CertificateCN: "abcdy78a90asdkmcaj98njad85nc",
	}
	certificateList := &CertificateList{
		Truncated: true,
	}
	certificateList.Certificates = append(certificateList.Certificates, certificate)
	certificateListJson, _ := json.Marshal(certificateList)

	ts := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
		w.Write([]byte(certificateListJson))
	}))
	defer ts.Close()
	bmsClient := NewBMSControl(ts.URL, nil, stsCache, stsRoleName)
	_, err := bmsClient.ListCertificate(uid)
	assert.Equal(t, err, nil)
}

func TestDownloadCertificate(t *testing.T) {
	stsCache, stsRoleName := prepare()
	ts := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
	}))
	defer ts.Close()
	bmsClient := NewBMSControl(ts.URL, nil, stsCache, stsRoleName)
	err := bmsClient.DownloadCertificate(uid, "abc123")
	assert.Equal(t, err, nil)
}

func TestAuthorizationAdd(t *testing.T) {
	stsCache, stsRoleName := prepare()
	ts := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
	}))
	defer ts.Close()
	bmsClient := NewBMSControl(ts.URL, nil, stsCache, stsRoleName)
	err := bmsClient.AuthorizationAdd(uid, authorization)
	assert.Equal(t, err, nil)
}

func TestListAuthorization(t *testing.T) {
	args := &ListAuthorizationArgs{
		Begin:         1,
		TopicLike:     "myTopic",
		AccountId:     "uiduid",
		CertificateSN: "73d1af3f69ec46d98d9f494d7b8b7005",
		Limit:         10,
	}

	topicAuthList := make([]*Authorization, 0)
	topicAuthList = append(topicAuthList, authorization)
	authorizationList := &AuthorizationList{}
	authorizationList.TopicAuthorizations.Begin = 1
	authorizationList.TopicAuthorizations.TopicAuthList = topicAuthList
	authorizationList.TopicAuthorizations.Size = 1
	authorizationListJson, _ := json.Marshal(authorizationList)
	stsCache, stsRoleName := prepare()
	ts := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
		w.Write([]byte(authorizationListJson))
	}))
	defer ts.Close()
	bmsClient := NewBMSControl(ts.URL, nil, stsCache, stsRoleName)

	_, err := bmsClient.ListAuthorization(args)
	assert.Equal(t, err, nil)
}

func TestAuthorizationCancel(t *testing.T) {
	stsCache, stsRoleName := prepare()
	ts := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
	}))
	defer ts.Close()
	bmsClient := NewBMSControl(ts.URL, nil, stsCache, stsRoleName)

	err := bmsClient.AuthorizationCancel(uid, authorization)
	assert.Equal(t, err, nil)
}

func TestCheckAuthorizationSuper(t *testing.T) {
	topicJson, _ := json.Marshal(topic)
	stsCache, stsRoleName := prepare()
	ts := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
		w.Write([]byte(topicJson))
	}))
	defer ts.Close()
	bmsClient := NewBMSControl(ts.URL, nil, stsCache, stsRoleName)

	err := bmsClient.AuthorizationSuper(superAuthorization, "Read")
	assert.Equal(t, err, nil)
}

func prepare() (stsCache sts_credential.Cache, stsRoleName string) {
	credential := &sts_credential.StsCredential{
		AccessKeyId:     "accesskeyid",
		AccessKeySecret: "accesskeysecret",
		SessionToken:    "sessionToken",
		Expiration:      time.Now().Add(60 * time.Minute),
	}
	stsRoleName = api.StsRoleName
	stsCache = sts_credential.NewLocalCredentialCache()
	stsCache.Set(uid, api.StsRoleName, credential)
	return stsCache, stsRoleName
}
