package bms

import (
	"time"
)

type CreateCertificateArgs struct {
	AccountId            string `json:"accountId"`
	DisplayName          string `json:"displayName"`
	UserName             string `json:"userName"`
	Email                string `json:"email"`
	CountryName          string `json:"countryName"`
	StateOrProvinceName  string `json:"stateOrProvinceName"`
	LocalityName         string `json:"localityName"`
	OrganizationName     string `json:"organizationName"`
	OrganizationUnitName string `json:"organizationUnitName"`
	Description          string `json:"description"`
	IsPrivilege          string `json:"isPrivilege"`
}

type DownloadCertificateArgs struct {
	CertificateSN string `json:"certificateSN"`
}

type ListAuthorizationArgs struct {
	Begin         int    `json:"begin"`
	TopicLike     string `json:"topicLike"`
	AccountId     string `json:"accountId"`
	CertificateSN string `json:"certificateSN"`
	Limit         int    `json:"limit"`
}

type Topic struct {
	PartitionCount    int       `json:"partitionCount"`
	CreateTime        time.Time `json:"createTime"`
	TopicName         string    `json:"topicName"`
	ReplicationFactor int       `json:"replicationFactor"`
}

type Tag struct {
	TagKey   string `json:"tagKey"`
	TagValue string `json:"tagValue"`
}

type Cluster struct {
	ClusterId           string    `json:"clusterId"`
	ClusterSid          string    `json:"clusterSid"`
	Name                string    `json:"name"`
	Region              string    `json:"region"`
	Type                string    `json:"type"`
	Mode                string    `json:"mode"`
	State               string    `json:"state"`
	KafkaVersion        string    `json:"kafkaVersion"`
	LogicalZones        []string  `json:"logicalZones"`
	Payment             string    `json:"payment"`
	AclEnabled          bool      `json:"aclEnabled"`
	PublicIpEnabled     bool      `json:"publicIpEnabled"`
	IntranetIpEnabled   bool      `json:"intranetIpEnabled"`
	AuthenticationModes []string  `json:"authenticationModes"`
	Tags                []*Tag    `json:"tags"`
	CreateTime          time.Time `json:"createTime"`
	ExpireTime          string    `json:"expireTime"`
}

type TopicList struct {
	Topics     []*Topic `json:"topics"`
	Truncated  bool     `json:"truncated"`
	NextMarker string   `json:"nextMarker"`
	Marker     string   `json:"marker"`
}

type ClustersList struct {
	Clusters    []*Cluster `json:"clusters"`
	IsTruncated bool       `json:"isTruncated"`
	NextMarker  string     `json:"nextMarker"`
	MaxKeys     uint       `json:"maxKeys"`
	Marker      string     `json:"marker"`
}

type KafkaEndpoint struct {
	SecurityProtocol string `json:"securityProtocol"`
	Endpoints        string `json:"endpoints"`
	Network          string `json:"network"`
}

type IntranetEndpoints struct {
	IntranetEndpoints struct {
		KafkaEndpoints []*KafkaEndpoint `json:"kafkaEndpoints"`
	} `json:"intranetEndpoints"`
}

type IntranetEndpointMapping struct {
	Result []*KafkaEndpoint `json:"result"`
}

type Certificate struct {
	AccountID     string `json:"accountId"`
	Region        string `json:"region"`
	Uuid          string `json:"uuid"`
	Description   string `json:"description"`
	CertificateCN string `json:"certificateCn"`
	CreateAt      string `json:"createAt"`
	RevokeAt      string `json:"revokeAt"`
	Privilege     string `json:"privilege"`
	Permission    string `json:"permission"`
}

type CertificateList struct {
	Certificates []*Certificate `json:"certificates"`
	Truncated    bool           `json:"truncated"`
	NextMarker   string         `json:"nextMarker"`
	Marker       string         `json:"marker"`
}

type AuthorizationList struct {
	TopicAuthorizations struct {
		Begin         int              `json:"begin"`
		TopicAuthList []*Authorization `json:"topicAuthList"`
		Size          int              `json:"size"`
	} `json:"topicAuthorizations"`
}

type Authorization struct {
	TopicOperation string `json:"topicOperation"`
	AccountId      string `json:"accountId"`
	Topic          string `json:"topic"`
	CertificateId  string `json:"certificateId"`
	CertificateSN  string `json:"certificateSN"`
}

type SuperAuthorization struct {
	TopicOwnerAccountID string
	*Authorization
}
