package bms

// 百度消息服务api

import (
	"errors"
	"fmt"
	"net/url"
	"regexp"
	"strings"
	"time"

	"icode.baidu.com/baidu/faas/kun/pkg/bce/auth"
	"icode.baidu.com/baidu/faas/kun/pkg/bce/iam"
	"icode.baidu.com/baidu/faas/kun/pkg/bce/sts_credential"
	"icode.baidu.com/baidu/faas/kun/pkg/rest"
)

var (
	RegWrite               = regexp.MustCompile(`(?i)Write`)
	RegRead                = regexp.MustCompile(`(?i)Read`)
	ReadOperation          = "Read"
	WriteOperation         = "Write"
	RWOperation            = "ReadWrite"
	ListAuthorizationBegin = 1
	ListAuthorizationLimit = 100
)

type BMSControlInterface interface {
	// 查询topic
	ListTopic(uid, clusterID string) (list *TopicList, err error)
	// 获取topic详情
	GetTopic(uid, name string) (topic *Topic, err error)
	// 查询cluster详细信息
	GetCluster(uid, clusterID string) (cluster *Cluster, err error)
	// 查询clusters list
	ListClusters(uid string) (list *ClustersList, err error)

	// 创建证书
	CreateCertificate(uid string, args *CreateCertificateArgs) error
	// 获取证书列表
	ListCertificate(uid string) (list *CertificateList, err error)
	// 下载证书
	DownloadCertificate(uid string, sn string) error
	//AuthorizationList()error

	// 查询授权的topic列表
	ListAuthorization(args *ListAuthorizationArgs) (list *AuthorizationList, err error)
	// 对topic授权
	AuthorizationAdd(uid string, args *Authorization) error
	// 取消topic 授权
	AuthorizationCancel(uid string, args *Authorization) error
	// 验证topic 并且向超级证书授权
	AuthorizationSuper(args *SuperAuthorization, topicOperation string) error

	// 获取集群接入点
	GetClusterEndPoint(uid, clusterID string) (intranetEndpoint *IntranetEndpoints, err error)
}

type BMSControl struct {
	client      *rest.RESTClient
	clientV2    *rest.RESTClient
	protocol    string
	port        int
	host        string
	iamClient   iam.ClientInterface
	StsCache    sts_credential.Cache
	stsRoleName string
}

func NewBMSControl(endPoint string, iamClient iam.ClientInterface, stsCache sts_credential.Cache, stsRoleName string) BMSControlInterface {
	version := "v1"
	version2 := "v2"
	config := rest.ContentConfig{
		BackendType: rest.BackendTypeInternal,
	}
	baseUrl, _ := url.Parse("http://" + strings.Replace(endPoint, "http://", "", 1))
	client, _ := rest.NewRESTClient(baseUrl, version, config, nil)
	clientV2, _ := rest.NewRESTClient(baseUrl, version2, config, nil)
	return &BMSControl{
		client:      client,
		clientV2:    clientV2,
		iamClient:   iamClient,
		StsCache:    stsCache,
		stsRoleName: stsRoleName,
	}
}

func (bms *BMSControl) sign(uid string, request *rest.Request) (err error) {
	// 从cache中查sts token
	c := bms.StsCache.Get(uid, bms.stsRoleName)
	if c == nil {
		// 调用sts服务assumeRole
		c, err = bms.iamClient.AssumeRole(uid, bms.stsRoleName, "", 3600)
		if err != nil {
			return err
		}
		// assumeRole成功后设置cache，过期时间1h - 10min
		bms.StsCache.Set(uid, bms.stsRoleName, c)
	}
	reqUrl := request.URL()
	request.SetHeader("Host", reqUrl.Host)
	request.SetHeader("X-Bce-Security-Token", c.SessionToken)
	bceAuth := auth.NewBceAuth(c.AccessKeyId, c.AccessKeySecret)
	sign := bceAuth.NewSigner().
		Method(request.Verb()).
		Path(reqUrl.Path).
		Params(reqUrl.Query()).
		Headers(request.Header()).
		Expire(3600).
		WithSignedHeader().
		GetSign()
	request.SetHeader("Authorization", sign)
	return nil
}

func (bms *BMSControl) ListTopic(uid, clusterID string) (list *TopicList, err error) {
	req := &rest.Request{}
	if len(clusterID) != 0 {
		resource := fmt.Sprintf("clusters/%s/topics", clusterID)
		req = bms.clientV2.Get().
			Resource(resource).
			Timeout(10 * time.Second)
	} else {
		resource := "topic"
		req = bms.client.Get().
			Resource(resource).
			Timeout(10 * time.Second)
	}
	bms.sign(uid, req)
	list = &TopicList{}
	err = req.Do().Into(list)
	return
}

func (bms *BMSControl) GetTopic(uid, name string) (topic *Topic, err error) {
	req := bms.client.Get().
		Resource(fmt.Sprintf("topic/%s", name)).
		Timeout(10 * time.Second)

	bms.sign(uid, req)
	topic = &Topic{}
	err = req.Do().Into(topic)
	// 请求的topic不是共享版，按照专享版处理, 兼容一下
	if err != nil {
		req = bms.clientV2.Get().
			Resource(fmt.Sprintf("clusters/topics/%s", name)).
			Timeout(10 * time.Second)
		bms.sign(uid, req)
		topic = &Topic{}
		err = req.Do().Into(topic)
	}
	return
}

func (bms *BMSControl) ListClusters(uid string) (list *ClustersList, err error) {
	req := bms.clientV2.Get().
		Resource("clusters").
		Timeout(10 * time.Second)

	bms.sign(uid, req)
	list = &ClustersList{}
	err = req.Do().Into(list)
	return
}

// todo: 这里复用了list cluster的接口实现, 其实应该使用kafka查询单个cluster的接口，但是这个接口有问题，部分字段decode不出，后续可能需要改进
func (bms *BMSControl) GetCluster(uid, clusterID string) (cluster *Cluster, err error) {
	req := bms.clientV2.Get().
		Resource("clusters").
		Timeout(10 * time.Second)

	bms.sign(uid, req)
	list := &ClustersList{}
	err = req.Do().Into(list)
	for _, c := range list.Clusters {
		if clusterID == c.ClusterId {
			return c, nil
		}
	}
	return nil, fmt.Errorf("cluster: %s Not Found, user id: %s\n", clusterID, uid)
}

func (bms *BMSControl) CreateCertificate(uid string, args *CreateCertificateArgs) error {
	req := bms.client.Post().
		Resource("certificates").
		Body(args).
		Timeout(10 * time.Second)
	bms.sign(uid, req)

	err := req.Do().Error()
	return err
}

func (bms *BMSControl) ListCertificate(uid string) (list *CertificateList, err error) {
	req := bms.client.Get().
		Resource("certificate/list").
		Timeout(10 * time.Second)

	bms.sign(uid, req)
	list = &CertificateList{}
	err = req.Do().Into(list)
	return
}

func (bms *BMSControl) DownloadCertificate(uid, id string) error {
	req := bms.client.Post().
		Resource("certificate/download").
		Body(&DownloadCertificateArgs{
			CertificateSN: id,
		}).
		Timeout(10 * time.Second)

	bms.sign(uid, req)

	err := req.Do().Error()
	return err
}

func (bms *BMSControl) AuthorizationAdd(uid string, args *Authorization) error {
	req := bms.client.Post().
		Resource("authorization/add").
		Body(args).
		Timeout(10 * time.Second)

	bms.sign(uid, req)

	err := req.Do().Error()
	return err
}

func (bms *BMSControl) ListAuthorization(args *ListAuthorizationArgs) (list *AuthorizationList, err error) {
	req := bms.client.Post().
		Resource("authorization/list").
		Body(args).
		Timeout(10 * time.Second)

	bms.sign(args.AccountId, req)
	list = &AuthorizationList{}
	err = req.Do().Into(list)
	return
}

func (bms *BMSControl) AuthorizationCancel(uid string, args *Authorization) error {
	req := bms.client.Post().
		Resource("v1/authorization/cancel").
		Body(args).
		Timeout(10 * time.Second)

	bms.sign(uid, req)

	err := req.Do().Error()
	return err
}

// 检查超级证书对用户topic是否有write权限，如果有Read权限，则改为ReadWrite权限
func (bms *BMSControl) AuthorizationSuper(args *SuperAuthorization, topicOperation string) error {
	// check topic
	topic, err := bms.GetTopic(args.TopicOwnerAccountID, args.Topic)
	if err != nil {
		return err
	}
	if topic == nil {
		return errors.New("topic not exists")
	}

	// 查询证书是否已对topic授权
	listAuthorizationArgs := &ListAuthorizationArgs{
		Begin:         ListAuthorizationBegin,
		AccountId:     args.AccountId,
		CertificateSN: args.CertificateSN,
		TopicLike:     args.Topic,
		Limit:         ListAuthorizationLimit,
	}
	listTopics, err := bms.ListAuthorization(listAuthorizationArgs)
	if err != nil {
		return err
	}

	var authorization = args.Authorization
	// 若topic未被授权，则向超级证书授权topicOperation权限
	authorization.TopicOperation = topicOperation
	switch topicOperation {
	case ReadOperation:
		for _, t := range listTopics.TopicAuthorizations.TopicAuthList {
			if t.Topic == args.Topic {
				if RegRead.MatchString(t.TopicOperation) {
					return nil
				} else if RegWrite.MatchString(t.TopicOperation) {
					// 有Write权限，则需要向超级证书追加Read权限,即添加读写权限
					authorization.TopicOperation = RWOperation
					break
				}
			}
		}
	case WriteOperation:
		for _, t := range listTopics.TopicAuthorizations.TopicAuthList {
			if t.Topic == args.Topic {
				if RegWrite.MatchString(t.TopicOperation) {
					return nil
				} else if RegRead.MatchString(t.TopicOperation) {
					// 有Read权限, 则需要向超级证书授权追加Write权限,即添加读写权限
					authorization.TopicOperation = RWOperation
					break
				}
			}
		}
	default:
		return errors.New("topic operation invalid")
	}

	err = bms.AuthorizationAdd(args.TopicOwnerAccountID, authorization)
	return err
}

func (bms *BMSControl) GetClusterEndPoint(uid, clusterID string) (intranetEndpoint *IntranetEndpoints, err error) {
	resource := fmt.Sprintf("clusters/%s/intranet-endpoints", clusterID)
	req := bms.clientV2.Get().
		Resource(resource).
		Timeout(10 * time.Second)

	bms.sign(uid, req)
	intranetEndpoint = &IntranetEndpoints{}
	err = req.Do().Into(intranetEndpoint)
	return
}
