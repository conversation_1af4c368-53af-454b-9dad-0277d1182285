package rocketmq

// 百度消息服务api

import (
	"fmt"
	"net/url"
	"strings"
	"time"

	"icode.baidu.com/baidu/faas/kun/pkg/bce/auth"
	"icode.baidu.com/baidu/faas/kun/pkg/bce/iam"
	"icode.baidu.com/baidu/faas/kun/pkg/bce/sts_credential"
	"icode.baidu.com/baidu/faas/kun/pkg/rest"
)

type RocketmqControlInterface interface {
	// 获取集群详情
	GetClusterDetail(uid, clusterID string) (cluster *Cluster, err error)

	// 获取集群接入点
	GetClusterEndPoint(uid, clusterID string) (endpoint *AccessEndpoint, err error)

	// 获取集群 topic 详情
	GetTopicDetail(uid, clusterID, topicName string) (topic *Topic, err error)

	// 获取消费组详情
	GetConsumerGroupDetail(uid, clusterID, groupName string) (group *ConsumerGroup, err error)

	// 新增：获取集群列表
	ListClusters(uid string) (resp *ClusterListResponse, err error)
	// 新增：获取集群下 topic 列表
	ListTopics(uid, clusterID string) (resp *TopicListResponse, err error)
	// 新增：获取集群下消费组列表
	ListConsumerGroups(uid, clusterID string) (resp *ConsumerGroupListResponse, err error)
	// 在接口中新增 ResetOffset 方法
	ResetOffset(uid, clusterID, groupName, topicName string, timestamp int64) error
	// 查询消费组在指定 topic 下的消费进度
	QueryConsumerOffset(uid, clusterID, groupName, topicName string) ([]ConsumeState, error)
}

type RocketmqControl struct {
	client      *rest.RESTClient
	clientV2    *rest.RESTClient
	iamClient   iam.ClientInterface
	StsCache    sts_credential.Cache
	stsRoleName string
}

// NewRocketmqControl NewRocketmqControl 创建一个新的RocketmqControl实例，用于与RocketMQ控制台进行交互
// endPoint (string) - RocketMQ控制台的URL，包括协议头（如http://）和主机名或IP地址
// iamClient (iam.ClientInterface) - IAM客户端接口，用于获取IAM角色的临时STS凭证
// stsCache (sts_credential.Cache) - STS凭证缓存，用于缓存IAM角色的临时STS凭证
// stsRoleName (string) - IAM角色名称，用于获取IAM角色的临时STS凭证
// 返回值 (RocketmqControlInterface) - RocketmqControl接口的实例
func NewRocketmqControl(endPoint string, iamClient iam.ClientInterface, stsCache sts_credential.Cache, stsRoleName string) RocketmqControlInterface {
	version := "v1"
	version2 := "v2"
	config := rest.ContentConfig{
		BackendType: rest.BackendTypeInternal,
	}
	baseUrl, _ := url.Parse("http://" + strings.Replace(endPoint, "http://", "", 1))
	client, _ := rest.NewRESTClient(baseUrl, version, config, nil)
	clientV2, _ := rest.NewRESTClient(baseUrl, version2, config, nil)
	return &RocketmqControl{
		client:      client,
		clientV2:    clientV2,
		iamClient:   iamClient,
		StsCache:    stsCache,
		stsRoleName: stsRoleName,
	}
}

// sign sign 签名函数，用于对请求进行签名操作
// uid: string, 用户ID
// request: *rest.Request, 需要签名的请求对象
// 返回值：error, 如果签名失败则返回错误信息
func (rmq *RocketmqControl) sign(uid string, request *rest.Request) (err error) {
	// 从cache中查sts token
	c := rmq.StsCache.Get(uid, rmq.stsRoleName)
	if c == nil {
		// 调用sts服务assumeRole
		c, err = rmq.iamClient.AssumeRole(uid, rmq.stsRoleName, "", 3600)
		if err != nil {
			return err
		}
		// assumeRole成功后设置cache，过期时间1h - 10min
		rmq.StsCache.Set(uid, rmq.stsRoleName, c)
	}
	reqUrl := request.URL()
	request.SetHeader("Host", reqUrl.Host)
	request.SetHeader("X-Bce-Security-Token", c.SessionToken)
	bceAuth := auth.NewBceAuth(c.AccessKeyId, c.AccessKeySecret)
	sign := bceAuth.NewSigner().
		Method(request.Verb()).
		Path(reqUrl.Path).
		Params(reqUrl.Query()).
		Headers(request.Header()).
		Expire(3600).
		WithSignedHeader().
		GetSign()
	request.SetHeader("Authorization", sign)
	return nil
}

// GetClusterDetail 获取集群详情，包括集群信息和节点列表等。
// 参数：
//
//	uid (string) - 用户ID，用于签名认证。
//	clusterID (string) - 集群ID，唯一标识集群。
//
// 返回值：
//
//	cluster (*Cluster) - 集群详情，包括集群信息和节点列表等。如果请求失败则为nil。
//	err (error) - 错误信息，如果请求成功则为nil。
func (rmq *RocketmqControl) GetClusterDetail(uid, clusterID string) (cluster *Cluster, err error) {
	resource := fmt.Sprintf("clusters/%s", clusterID)
	req := rmq.client.Get().
		Resource(resource).
		Timeout(10 * time.Second)

	rmq.sign(uid, req)
	resp := &ClusterDetailResponse{}
	err = req.Do().Into(resp)
	if err != nil {
		return nil, fmt.Errorf("get cluster: %s failed, user id: %s, err: %v", clusterID, uid, err)
	}

	cluster = resp.Cluster
	return
}

// GetClusterEndPoint 获取集群的访问端点，参数为用户ID和集群ID，返回值为*AccessEndpoint类型指针和error类型，如果出错则返回nil和对应的错误信息
func (rmq *RocketmqControl) GetClusterEndPoint(uid, clusterID string) (endpoint *AccessEndpoint, err error) {
	resource := fmt.Sprintf("clusters/%s/access-endpoints", clusterID)
	req := rmq.client.Get().
		Resource(resource).
		Timeout(10 * time.Second)

	rmq.sign(uid, req)
	resp := &AccessEndpointResponse{}
	err = req.Do().Into(resp)
	if err != nil {
		return nil, fmt.Errorf("get cluster: %s access-endpoints failed, user id: %s, err: %v", clusterID, uid, err)
	}
	if len(resp.AccessEndpoints) == 0 {
		return nil, fmt.Errorf("get cluster: %s access-endpoints is empty, user id: %s", clusterID, uid)
	}
	endpoint = resp.AccessEndpoints[0]

	return
}

// GetTopicDetail 获取指定主题的详细信息，包括消息数量、存储大小等。
// 参数：
//
//	uid (string) - 用户ID，用于鉴权
//	clusterID (string) - 集群ID
//	topicName (string) - 主题名称
//
// 返回值：
//
//	topic (*Topic) - 主题对象，包含消息数量、存储大小等信息
//	    messageCount (int64) - 消息数量
//	    storageSize (int64) - 存储大小，单位为字节（byte）
//	    lastUpdateTime (time.Time) - 最后更新时间
//	    createTime (time.Time) - 创建时间
//	    status (string) - 状态，可能值有："OK"、"DELETE"
//	    orderKey (int64) - 排序键，用于排序
//	    partitionNum (int32) - 分区数量
//	    replicaNum (int32) - 副本数量
//	  error (error) - 请求过程中出现的错误，如果没有错误则为nil
func (rmq *RocketmqControl) GetTopicDetail(uid, clusterID, topicName string) (topic *Topic, err error) {
	resource := fmt.Sprintf("clusters/%s/topics/%s", clusterID, topicName)
	req := rmq.client.Get().
		Resource(resource).
		Timeout(10 * time.Second)

	rmq.sign(uid, req)
	resp := &TopicDetailResponse{}
	err = req.Do().Into(resp)
	if err != nil {
		return nil, fmt.Errorf("get cluster: %s topic: %s failed, user id: %s, err: %v", clusterID, topicName, uid, err)
	}

	topic = resp.Topic

	return
}

// GetConsumerGroupDetail 获取消费组详情函数
// uid：用户ID，string类型，不能为空
// clusterID：集群ID，string类型，不能为空
// groupName：消费组名称，string类型，不能为空
// 返回值：group *ConsumerGroup，*ConsumerGroup类型的指针，表示消费组详情；err error，error类型，表示可能发生的错误
func (rmq *RocketmqControl) GetConsumerGroupDetail(uid, clusterID, groupName string) (group *ConsumerGroup, err error) {
	resource := fmt.Sprintf("clusters/%s/consumer-groups/%s", clusterID, groupName)
	req := rmq.client.Get().
		Resource(resource).
		Timeout(10 * time.Second)

	rmq.sign(uid, req)
	resp := &ConsumerGroupResponse{}
	err = req.Do().Into(resp)
	if err != nil {
		return nil, fmt.Errorf("get cluster: %s access-endpoints failed, user id: %s, err: %v", clusterID, uid, err)
	}

	group = resp.ConsumerGroup

	return
}

// 新增：集群列表响应结构体
// 对应 API 返回字段
//
//	{
//	  "requestId": "...",
//	  "isTruncated": true,
//	  "nextMarker": "...",
//	  "maxKeys": 2,
//	  "clusters": [ ... ]
//	}
type ClusterListResponse struct {
	RequestID   string     `json:"requestId"`
	IsTruncated bool       `json:"isTruncated"`
	NextMarker  string     `json:"nextMarker"`
	MaxKeys     int        `json:"maxKeys"`
	Clusters    []*Cluster `json:"clusters"`
}

type TopicListResponse struct {
	RequestID   string   `json:"requestId"`
	IsTruncated bool     `json:"isTruncated"`
	NextMarker  string   `json:"nextMarker"`
	MaxKeys     int      `json:"maxKeys"`
	Topics      []*Topic `json:"topics"`
}

type ConsumerGroupListResponse struct {
	RequestID      string           `json:"requestId"`
	IsTruncated    bool             `json:"isTruncated"`
	NextMarker     string           `json:"nextMarker"`
	MaxKeys        int              `json:"maxKeys"`
	ConsumerGroups []*ConsumerGroup `json:"consumerGroups"`
}

func (rmq *RocketmqControl) ListClusters(uid string) (resp *ClusterListResponse, err error) {
	resource := "clusters"
	req := rmq.client.Get().
		Resource(resource).
		Timeout(10 * time.Second)
	rmq.sign(uid, req)
	resp = &ClusterListResponse{}
	err = req.Do().Into(resp)
	if err != nil {
		return nil, fmt.Errorf("list clusters failed, user id: %s, err: %v", uid, err)
	}
	return resp, nil
}

func (rmq *RocketmqControl) ListTopics(uid, clusterID string) (resp *TopicListResponse, err error) {
	resource := fmt.Sprintf("clusters/%s/topics", clusterID)
	req := rmq.client.Get().
		Resource(resource).
		Timeout(10 * time.Second)
	rmq.sign(uid, req)
	resp = &TopicListResponse{}
	err = req.Do().Into(resp)
	if err != nil {
		return nil, fmt.Errorf("list topics failed, cluster: %s, user id: %s, err: %v", clusterID, uid, err)
	}
	return resp, nil
}

func (rmq *RocketmqControl) ListConsumerGroups(uid, clusterID string) (resp *ConsumerGroupListResponse, err error) {
	resource := fmt.Sprintf("clusters/%s/consumer-groups", clusterID)
	req := rmq.client.Get().
		Resource(resource).
		Timeout(10 * time.Second)
	rmq.sign(uid, req)
	resp = &ConsumerGroupListResponse{}
	err = req.Do().Into(resp)
	if err != nil {
		return nil, fmt.Errorf("list consumer groups failed, cluster: %s, user id: %s, err: %v", clusterID, uid, err)
	}
	return resp, nil
}

// 在 RocketmqControl 实现中新增 ResetOffset 方法
func (rmq *RocketmqControl) ResetOffset(uid, clusterID, groupName, topicName string, timestamp int64) error {
	resource := fmt.Sprintf("clusters/%s/consumer-groups/%s/offset", clusterID, groupName)
	body := map[string]interface{}{
		"topicName": topicName,
		"timestamp": timestamp,
	}
	req := rmq.client.Put().
		Resource(resource).
		Timeout(10 * time.Second).
		Body(body)
	rmq.sign(uid, req)
	var resp struct {
		RequestID string `json:"requestId"`
		Result    string `json:"result"`
	}
	err := req.Do().Into(&resp)
	if err != nil {
		return fmt.Errorf("reset offset failed, cluster: %s, group: %s, topic: %s, user id: %s, err: %v", clusterID, groupName, topicName, uid, err)
	}
	if resp.Result != "success" {
		return fmt.Errorf("reset offset failed, cluster: %s, group: %s, topic: %s, user id: %s, result: %s", clusterID, groupName, topicName, uid, resp.Result)
	}
	return nil
}

// ConsumeState 结构体
// 用于描述消费组在某个分区的消费进度
type ConsumeState struct {
	ConsumerGroupName string `json:"consumerGroupName"`
	MessageQueue      struct {
		BrokerName string `json:"brokerName"`
		TopicName  string `json:"topicName"`
		QueueId    int    `json:"queueId"`
	} `json:"messageQueue"`
	ClientId        string `json:"clientId"`
	BrokerOffset    int64  `json:"brokerOffset"`
	ConsumeOffset   int64  `json:"consumeOffset"`
	LastConsumeTime int64  `json:"lastConsumeTime"`
}

// QueryConsumerOffset 查询消费组在指定 topic 下的消费进度
func (rmq *RocketmqControl) QueryConsumerOffset(uid, clusterID, groupName, topicName string) ([]ConsumeState, error) {
	resource := fmt.Sprintf("clusters/%s/consumer-groups/%s/offset", clusterID, groupName)
	req := rmq.client.Get().
		Resource(resource).
		Param("topicName", topicName).
		Timeout(10 * time.Second)
	rmq.sign(uid, req)
	var resp struct {
		RequestID     string         `json:"requestId"`
		IsTruncated   bool           `json:"isTruncated"`
		MaxKeys       int            `json:"maxKeys"`
		ConsumeStates []ConsumeState `json:"consumeStates"`
	}
	err := req.Do().Into(&resp)
	if err != nil {
		return nil, fmt.Errorf("query consumer offset failed, cluster: %s, group: %s, topic: %s, user id: %s, err: %v", clusterID, groupName, topicName, uid, err)
	}
	return resp.ConsumeStates, nil
}
