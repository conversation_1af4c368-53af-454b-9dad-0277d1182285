package rocketmq

import (
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"

	"icode.baidu.com/baidu/faas/kun/pkg/api"
	"icode.baidu.com/baidu/faas/kun/pkg/bce/sts_credential"
	"icode.baidu.com/baidu/faas/kun/pkg/util/json"
)

var (
	uid = "uiduid"
)

// TestNewBMSControl 测试函数，用于创建一个新的BMSControl实例。
// 参数：
//
//	t *testing.T - *testing.T类型，表示当前测试用例。
//
// 返回值：
//
//	bool - 无返回值，但是会执行断言操作。
func TestNewBMSControl(t *testing.T) {
	stsCache, stsRoleName := prepare()
	ts := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
	}))
	defer ts.Close()
	res := NewRocketmqControl(ts.URL, nil, stsCache, stsRoleName)
	assert.NotNil(t, res)
}

// TestGetClusterDetail 测试函数，用于获取集群详情
// t *testing.T：测试对象指针，表示当前测试用例
func TestGetClusterDetail(t *testing.T) {
	// 模拟 HTTP 服务器
	clusterDetail := &ClusterDetailResponse{
		Cluster: &Cluster{
			ClusterID: "test-cluster-id",
			Name:      "test-cluster",
			Region:    "bj",
			State:     "ACTIVE",
		},
	}
	clusterDetailJSON, _ := json.Marshal(clusterDetail)
	ts := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
		w.Write(clusterDetailJSON)
	}))
	defer ts.Close()

	// 创建 RocketmqControl 实例
	stsCache, stsRoleName := prepare()
	rmq := NewRocketmqControl(ts.URL, nil, stsCache, stsRoleName)

	// 测试 GetClusterDetail
	cluster, err := rmq.GetClusterDetail(uid, "test-cluster-id")
	assert.NoError(t, err)
	assert.NotNil(t, cluster)
	assert.Equal(t, "test-cluster-id", cluster.ClusterID)
	assert.Equal(t, "test-cluster", cluster.Name)
	assert.Equal(t, "bj", cluster.Region)
	assert.Equal(t, "ACTIVE", cluster.State)
}

// TestGetClusterEndPoint 测试函数，用于获取集群端点信息。
// 参数 t *testing.T - 类型为 *testing.T，表示测试对象。
// 返回值：无返回值
func TestGetClusterEndPoint(t *testing.T) {
	// 模拟 HTTP 服务器
	endpointResponse := &AccessEndpointResponse{
		AccessEndpoints: []*AccessEndpoint{
			{
				CommunicationProtocol: "remoting",
				Endpoint:              "rocketmq-test-endpoint",
			},
		},
	}
	endpointResponseJSON, _ := json.Marshal(endpointResponse)
	ts := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
		w.Write(endpointResponseJSON)
	}))
	defer ts.Close()

	// 创建 RocketmqControl 实例
	stsCache, stsRoleName := prepare()
	rmq := NewRocketmqControl(ts.URL, nil, stsCache, stsRoleName)

	// 测试 GetClusterEndPoint
	endpoint, err := rmq.GetClusterEndPoint(uid, "test-cluster-id")
	assert.NoError(t, err)
	assert.NotNil(t, endpoint)
	assert.Equal(t, "remoting", endpoint.CommunicationProtocol)
	assert.Equal(t, "rocketmq-test-endpoint", endpoint.Endpoint)
}

// TestGetTopicDetail 测试函数，用于获取主题详情
// 参数 t *testing.T - 测试对象指针，表示当前测试用例
func TestGetTopicDetail(t *testing.T) {
	// 模拟 HTTP 服务器
	topicDetail := &TopicDetailResponse{
		Topic: &Topic{
			TopicName:     "test-topic",
			ReadQueueSum:  3,
			WriteQueueSum: 3,
		},
	}
	topicDetailJSON, _ := json.Marshal(topicDetail)
	ts := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
		w.Write(topicDetailJSON)
	}))
	defer ts.Close()

	// 创建 RocketmqControl 实例
	stsCache, stsRoleName := prepare()
	rmq := NewRocketmqControl(ts.URL, nil, stsCache, stsRoleName)

	// 测试 GetTopicDetail
	topic, err := rmq.GetTopicDetail(uid, "test-cluster-id", "test-topic")
	assert.NoError(t, err)
	assert.NotNil(t, topic)
	assert.Equal(t, "test-topic", topic.TopicName)
	assert.Equal(t, 3, topic.ReadQueueSum)
	assert.Equal(t, 3, topic.WriteQueueSum)
}

// TestGetClusterEndPoint_EmptyResponse 测试函数，用于获取集群端点，当接收到空的 AccessEndpoints 时会出现错误
func TestGetClusterEndPoint_EmptyResponse(t *testing.T) {
	// 模拟 HTTP 服务器返回空的 AccessEndpoints
	ts := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
		w.Write([]byte(`{"accessEndpoints":[]}`))
	}))
	defer ts.Close()

	// 创建 RocketmqControl 实例
	stsCache, stsRoleName := prepare()
	rmq := NewRocketmqControl(ts.URL, nil, stsCache, stsRoleName)

	// 测试 GetClusterEndPoint
	endpoint, err := rmq.GetClusterEndPoint(uid, "test-cluster-id")
	assert.Error(t, err)
	assert.Nil(t, endpoint)
	assert.Contains(t, err.Error(), "access-endpoints is empty")
}

// TestGetConsumerGroupDetail 测试函数，用于获取消费者组详情。
// 参数 t *testing.T - 测试用的指针，表示当前正在运行的测试。
func TestGetConsumerGroupDetail(t *testing.T) {
	// 模拟 HTTP 服务器
	consumerGroupResponse := &ConsumerGroupResponse{
		ConsumerGroup: &ConsumerGroup{
			GroupName:              "test-group",
			RetryMaxTimes:          5,
			ConsumeBroadcastEnable: true,
		},
	}
	consumerGroupResponseJSON, _ := json.Marshal(consumerGroupResponse)
	ts := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
		w.Write(consumerGroupResponseJSON)
	}))
	defer ts.Close()

	// 创建 RocketmqControl 实例
	stsCache, stsRoleName := prepare()
	rmq := NewRocketmqControl(ts.URL, nil, stsCache, stsRoleName)

	// 测试 GetConsumerGroupDetail
	group, err := rmq.GetConsumerGroupDetail(uid, "test-cluster-id", "test-group")
	assert.NoError(t, err)
	assert.NotNil(t, group)
	assert.Equal(t, "test-group", group.GroupName)
	assert.Equal(t, int32(5), group.RetryMaxTimes)
	assert.True(t, group.ConsumeBroadcastEnable)
}

// TestListClusters 测试函数，用于获取集群列表
// t *testing.T：测试对象指针，表示当前测试用例
func TestListClusters(t *testing.T) {
	// 模拟 HTTP 服务器
	resp := &ClusterListResponse{
		RequestID:   "req-1",
		IsTruncated: false,
		MaxKeys:     100,
		Clusters: []*Cluster{
			{
				ClusterID: "rocketmq-1",
				Name:      "test-cluster-1",
				Region:    "bj",
				State:     "ACTIVE",
			},
		},
	}
	respJSON, _ := json.Marshal(resp)
	ts := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
		w.Write(respJSON)
	}))
	defer ts.Close()

	stsCache, stsRoleName := prepare()
	rmq := NewRocketmqControl(ts.URL, nil, stsCache, stsRoleName)

	result, err := rmq.ListClusters(uid)
	assert.NoError(t, err)
	assert.NotNil(t, result)
	assert.Equal(t, 1, len(result.Clusters))
	assert.Equal(t, "rocketmq-1", result.Clusters[0].ClusterID)
}

// TestListTopics 测试函数，用于获取主题列表
// t *testing.T：测试对象指针，表示当前测试用例
func TestListTopics(t *testing.T) {
	resp := &TopicListResponse{
		RequestID:   "req-2",
		IsTruncated: false,
		MaxKeys:     100,
		Topics: []*Topic{
			{
				TopicName: "topic-1",
			},
		},
	}
	respJSON, _ := json.Marshal(resp)
	ts := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
		w.Write(respJSON)
	}))
	defer ts.Close()

	stsCache, stsRoleName := prepare()
	rmq := NewRocketmqControl(ts.URL, nil, stsCache, stsRoleName)

	result, err := rmq.ListTopics(uid, "rocketmq-1")
	assert.NoError(t, err)
	assert.NotNil(t, result)
	assert.Equal(t, 1, len(result.Topics))
	assert.Equal(t, "topic-1", result.Topics[0].TopicName)
}

// TestListConsumerGroups 测试函数，用于获取消费者组列表
// t *testing.T：测试对象指针，表示当前测试用例
func TestListConsumerGroups(t *testing.T) {
	resp := &ConsumerGroupListResponse{
		RequestID:      "req-3",
		IsTruncated:    false,
		MaxKeys:        100,
		ConsumerGroups: []*ConsumerGroup{{GroupName: "group-1"}},
	}
	respJSON, _ := json.Marshal(resp)
	ts := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
		w.Write(respJSON)
	}))
	defer ts.Close()

	stsCache, stsRoleName := prepare()
	rmq := NewRocketmqControl(ts.URL, nil, stsCache, stsRoleName)

	result, err := rmq.ListConsumerGroups(uid, "rocketmq-1")
	assert.NoError(t, err)
	assert.NotNil(t, result)
	assert.Equal(t, 1, len(result.ConsumerGroups))
	assert.Equal(t, "group-1", result.ConsumerGroups[0].GroupName)
}

// prepare prepare 函数用于准备工作，返回一个sts_credential.Cache类型的缓存对象和一个string类型的stsRoleName。
func prepare() (stsCache sts_credential.Cache, stsRoleName string) {
	credential := &sts_credential.StsCredential{
		AccessKeyId:     "accesskeyid",
		AccessKeySecret: "accesskeysecret",
		SessionToken:    "sessionToken",
		Expiration:      time.Now().Add(60 * time.Minute),
	}
	stsRoleName = api.StsRoleName
	stsCache = sts_credential.NewLocalCredentialCache()
	stsCache.Set(uid, api.StsRoleName, credential)
	return stsCache, stsRoleName
}
