package rocketmq

type ClusterDetailResponse struct {
	RequestID string   `json:"requestId"`
	Cluster   *Cluster `json:"cluster"`
}

type Cluster struct {
	ClusterID   string       `json:"clusterId"`
	Name        string       `json:"name"`
	Region      string       `json:"region"`
	State       string       `json:"state"`
	Provisioned *Provisioned `json:"provisioned"`
	Tags        []*Tag       `json:"tags"`
	CreateTime  string       `json:"createTime"`
}

type Provisioned struct {
	Version               string   `json:"version"`
	NumberOfBrokers       int      `json:"numberOfBrokers"`
	Arch                  string   `json:"arch"`
	AclEnabled            bool     `json:"aclEnabled"`
	EncryptionInTransit   []string `json:"encryptionInTransit"`
	Payment               string   `json:"payment"`
	Billing               *Billing `json:"billing"`
	ZoneNames             []string `json:"zoneNames"`
	SubnetIds             []string `json:"subnetIds"`
	SecurityGroupIds      []string `json:"securityGroupIds"`
	NodeType              string   `json:"nodeType"`
	DeploySetEnabled      bool     `json:"deploySetEnabled"`
	StorageType           string   `json:"storageType"`
	StorageSize           int      `json:"storageSize"`
	PublicAccessEnabled   bool     `json:"publicAccessEnabled"`
	PublicAccessBandwidth int      `json:"publicAccessBandwidth"`
	IntranetAccessEnabled bool     `json:"intranetAccessEnabled"`
}

type Billing struct {
	TimeLength int    `json:"timeLength"`
	TimeUnit   string `json:"timeUnit"`
}

type Tag struct {
	TagKey   string `json:"tagKey"`
	TagValue string `json:"tagValue"`
}

type AccessEndpointResponse struct {
	RequestID       string            `json:"requestId"`
	AccessEndpoints []*AccessEndpoint `json:"accessEndpoints"`
}

type AccessEndpoint struct {
	CommunicationProtocol string `json:"communicationProtocol"`
	Endpoint              string `json:"endpoint"`
}

type ConsumerGroupResponse struct {
	RequestID     string         `json:"requestId"`
	ConsumerGroup *ConsumerGroup `json:"consumerGroup"`
}

type ConsumerGroup struct {
	GroupName              string   `json:"groupName"`
	BrokerNames            []string `json:"brokerNames"`
	ConsumeBroadcastEnable bool     `json:"consumeBroadcastEnable"`
	RetryMaxTimes          int32    `json:"retryMaxTimes"`
	MessageModel           string   `json:"messageModel"`
}

type TopicDetailResponse struct {
	RequestID string `json:"requestId"`
	Topic     *Topic `json:"topic"`
}

type Topic struct {
	TopicName     string    `json:"topicName"`
	Brokers       []*Broker `json:"brokers"`
	Permission    int       `json:"permission"`
	ReadQueueSum  int       `json:"readQueueSum"`
	WriteQueueSum int       `json:"writeQueueSum"`
}

type Broker struct {
	BrokerName    string `json:"brokerName"`
	ReadQueueNum  int    `json:"readQueueNum"`
	WriteQueueNum int    `json:"writeQueueNum"`
}
