package bls

import (
	"github.com/baidubce/bce-sdk-go/auth"
	blscli "github.com/baidubce/bce-sdk-go/services/bls"
	blsapi "github.com/baidubce/bce-sdk-go/services/bls/api"

	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/global"
	"icode.baidu.com/baidu/faas/kun/pkg/server"
)

// NewClient sts方式访问用户的bucket
func NewClient(c *server.Context) (*blscli.Client, error) {
	credential, err := global.GetCredential(c)
	if err != nil {
		return nil, err
	}

	cli, err := blscli.NewClient(credential.AccessKeyId, credential.AccessKeySecret, global.AC.Config.BlsEndpoint)
	if err != nil {
		return nil, err
	}

	stsCredential, err := auth.NewSessionBceCredentials(
		credential.AccessKeyId,
		credential.AccessKeySecret,
		credential.SessionToken)

	if err != nil {
		return nil, err
	}

	cli.Config.Credentials = stsCredential
	return cli, nil
}

// 创建日志集
func CreateLogStore(logStore string, retention int, cli *blscli.Client) error {
	return cli.CreateLogStore(logStore, retention)
}

// 查看指定日志集
func DescribeLogStore(logStore string, cli *blscli.Client) (*blsapi.LogStore, error) {
	return cli.DescribeLogStore(logStore)
}

// 获取日志集列表
func ListLogStore(args *blsapi.QueryConditions, cli *blscli.Client) (*blsapi.ListLogStoreResult, error) {
	return cli.ListLogStore(args)
}

// 获取日志集列表
func ListLogstream(logStore string, args *blsapi.QueryConditions, cli *blscli.Client) (*blsapi.ListLogStreamResult, error) {
	return cli.ListLogStream(logStore, args)
}

// 查询日志记录
func PullLogRecord(logStore string, args *blsapi.PullLogRecordArgs, cli *blscli.Client) (*blsapi.PullLogRecordResult, error) {
	return cli.PullLogRecord(logStore, args)
}

// 查询日志记录sql或match
func QueryLogRecord(logStore string, args *blsapi.QueryLogRecordArgs, cli *blscli.Client) (*blsapi.QueryLogResult, error) {
	return cli.QueryLogRecord(logStore, args)
}

// 创建索引
func CreateIndex(logStore string, fulltext bool, fields map[string]blsapi.LogField, cli *blscli.Client) error {
	return cli.CreateIndex(logStore, fulltext, fields)
}

// 更新索引
func UpdateIndex(logStore string, fulltext bool, fields map[string]blsapi.LogField, cli *blscli.Client) error {
	return cli.UpdateIndex(logStore, fulltext, fields)
}

// 查询log stream
func ListLogStream(logStore string, args *blsapi.QueryConditions, cli *blscli.Client) (*blsapi.ListLogStreamResult, error) {
	return cli.ListLogStream(logStore, args)
}
