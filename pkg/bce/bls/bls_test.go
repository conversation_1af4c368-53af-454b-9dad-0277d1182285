package bls

import (
	"testing"

	"github.com/stretchr/testify/assert"

	blsapi "github.com/baidubce/bce-sdk-go/services/bls/api"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/global"
)

func TestNewClient(t *testing.T) {
	c := global.BuildNewKunCtx("GET", "/v1/logstore", ``, "c7ac82ae14ef42d1a4ffa3b2ececa17f", map[string]string{})
	global.MockAC()
	_, err := NewClient(c)
	assert.Equal(t, nil, err)
}

func TestCreateLogStore(t *testing.T) {
	t.Skip()
	global.MockAC()
	c := global.BuildNewKunCtx("GET", "/v1/logstore", ``, "c7ac82ae14ef42d1a4ffa3b2ececa17f", map[string]string{})
	cli, _ := NewClient(c)
	err := CreateLogStore("test", 7, cli)
	assert.Nil(t, err)
}

func TestDescribeLogStore(t *testing.T) {
	t.Skip()
	global.MockAC()
	c := global.BuildNewKunCtx("GET", "/v1/logstore", ``, "c7ac82ae14ef42d1a4ffa3b2ececa17f", map[string]string{})
	cli, _ := NewClient(c)
	_, err := DescribeLogStore("test", cli)
	assert.NotNil(t, err)
}

func TestListLogStore(t *testing.T) {
	t.Skip()
	global.MockAC()
	c := global.BuildNewKunCtx("GET", "/v1/logstore", ``, "c7ac82ae14ef42d1a4ffa3b2ececa17f", map[string]string{})
	cli, _ := NewClient(c)
	args := &blsapi.QueryConditions{}
	_, err := ListLogStore(args, cli)
	assert.NotNil(t, err)
}

func TestPullLogRecord(t *testing.T) {
	global.MockAC()
	c := global.BuildNewKunCtx("GET", "/v1/logstore", ``, "c7ac82ae14ef42d1a4ffa3b2ececa17f", map[string]string{})
	cli, _ := NewClient(c)
	args := &blsapi.PullLogRecordArgs{}
	_, err := PullLogRecord("test", args, cli)
	assert.NotEqual(t, nil, err)
}

func TestQueryLogRecord(t *testing.T) {
	global.MockAC()
	c := global.BuildNewKunCtx("GET", "/v1/logstore", ``, "c7ac82ae14ef42d1a4ffa3b2ececa17f", map[string]string{})
	cli, _ := NewClient(c)
	args := &blsapi.QueryLogRecordArgs{}
	_, err := QueryLogRecord("test", args, cli)
	assert.NotEqual(t, nil, err)
}

func TestCreateIndex(t *testing.T) {
	t.Skip()
	global.MockAC()
	c := global.BuildNewKunCtx("GET", "/v1/logstore", ``, "c7ac82ae14ef42d1a4ffa3b2ececa17f", map[string]string{})
	cli, _ := NewClient(c)
	err := CreateIndex("test", false, nil, cli)
	assert.Nil(t, err)
}

func TestUpdateIndex(t *testing.T) {
	global.MockAC()
	c := global.BuildNewKunCtx("GET", "/v1/logstore", ``, "c7ac82ae14ef42d1a4ffa3b2ececa17f", map[string]string{})
	cli, _ := NewClient(c)
	err := UpdateIndex("test", false, nil, cli)
	assert.Nil(t, err)
}

func TestListLogStream(t *testing.T) {
	t.Skip()
	global.MockAC()
	c := global.BuildNewKunCtx("GET", "/v1/logstore", ``, "c7ac82ae14ef42d1a4ffa3b2ececa17f", map[string]string{})
	cli, _ := NewClient(c)
	_, err := ListLogStream("test", nil, cli)
	assert.NotNil(t, err)
}
