package signer

import (
	"bytes"
	"errors"
	"io"
	"io/ioutil"
	"net/http"
	"strings"
	"time"

	"github.com/aws/aws-sdk-go/aws/credentials"
	awsSignerV4 "github.com/aws/aws-sdk-go/aws/signer/v4"
)

const (
	invalidAuthStrLen = "Error:  Authorization length err"
	invalidCredLen    = "Error: Credential length err"
)

func CheckAwsSignature(ak, sk string, req *http.Request, serviceName, region, signedHeaders string) (authRes bool, err error) {
	expectSig := req.Header.Get("Authorization")
	xAmzDate := req.Header.Get("X-Amz-Date")

	a := credentials.Value{
		AccessKeyID:     ak,
		SecretAccessKey: sk,
	}
	creds := credentials.NewStaticCredentialsFromCreds(a)
	awsSigner := awsSignerV4.NewSigner(creds, func(s *awsSignerV4.Signer) {
		s.DisableURIPathEscaping = false
		s.DisableHeaderHoisting = false
		s.DisableRequestBodyOverwrite = true
	})

	timeTmp, _ := time.Parse("20060102T150405Z", xAmzDate)

	newReq, reader := buildNewRequest(req, signedHeaders)
	req.Body = ioutil.NopCloser(reader)

	_, err = awsSigner.Sign(newReq, reader, serviceName, region, timeTmp)
	if err != nil {
		return false, err
	}
	actual := newReq.Header.Get("Authorization")

	if actual == expectSig {
		return true, nil
	}

	return false, nil
}

//由于aws Sign 方法中，计算Sign的header 会取全部符合规则的头。导致计算Sign的方式和客户端有差异
//处理方式1： 把req中多余的header删掉
//处理方式2： buildNewRequest
func buildNewRequest(r *http.Request, signedHeaders string) (*http.Request, io.ReadSeeker) {
	scheme := "http://"
	if r.TLS != nil {
		scheme = "https://"
	}
	url := strings.Join([]string{scheme, r.Host, r.RequestURI}, "")
	byteTmp, _ := ioutil.ReadAll(r.Body)

	reader := bytes.NewReader(byteTmp)
	newReq, _ := http.NewRequest(r.Method, url, reader)

	//设置要计算sign的header
	splitHeader := strings.Split(signedHeaders, ";")

	for _, v := range splitHeader {
		if v == "host" {
			continue
		}
		newReq.Header.Add(v, r.Header.Get(v))
	}
	return newReq, reader
}

func SplitAwsAuthStr(authorization string) (userAk, region, serviceName, signedHeaders string, err error) {
	//for aws
	//Authorization: AWS4-HMAC-SHA256 Credential=NTg3NGJmNTItNjM1Ni00/20170908/ap-northeast-1/lambda/aws4_request, SignedHeaders=host;x-amz-date, Signature=061b11080cc42679953a897da91acbcd04a4fae15a6e9ce31f6f2b31fe1d2b19

	splitAuthorization := strings.Split(authorization, " ")
	if len(splitAuthorization) < 4 {
		return "", "", "", "", errors.New(invalidAuthStrLen)
	}
	splitAuthorization[1] = strings.Replace(splitAuthorization[1], "Credential=", "", 1)
	signedHeaders = strings.Replace(splitAuthorization[2], "SignedHeaders=", "", 1)
	splitCredential := strings.Split(splitAuthorization[1], "/")
	if len(splitCredential) < 5 {
		return "", "", "", "", errors.New(invalidCredLen)
	}
	signedHeaders = signedHeaders[0 : len(signedHeaders)-1]

	return splitCredential[0], splitCredential[2], splitCredential[3], signedHeaders, nil

}
