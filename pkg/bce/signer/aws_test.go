package signer

import (
	"errors"
	"fmt"
	"io/ioutil"
	"net/http"
	"strings"
	"testing"
	"time"

	"github.com/aws/aws-sdk-go/aws/credentials"
	v4 "github.com/aws/aws-sdk-go/aws/signer/v4"
)

const AKID = "AKID"
const SKID = "SKID"

func buildRequest(method, region, body string) *http.Request {
	endpoint := "https://faas." + region + ".baidu.com"
	reader := strings.NewReader(body)
	req, _ := http.NewRequest(method, endpoint, reader)

	signer := buildSigner()
	signer.Sign(req, reader, "faas", region, time.Unix(0, 0))

	req.Header.Add("Content-Type", "application/json")
	req.Header.Add("Content-Length", fmt.Sprintf("%d", len(body)))

	return req
}

func buildSigner() *v4.Signer {
	a := credentials.Value{
		AccessKeyID:     AKID,
		SecretAccessKey: SKID,
	}
	creds := credentials.NewStaticCredentialsFromCreds(a)
	signer := v4.NewSigner(creds)
	return signer
}

func TestCheckAwsSignature(t *testing.T) {

	req := buildRequest("POST", "bj", `{"a":"b"}`)

	authRes, err := CheckAwsSignature(AKID, SKID, req, "faas", "bj", "host;x-amz-date")

	if err != nil {
		t.Errorf("err happend %v", err)
	}

	expectedRes := true

	if e, a := expectedRes, authRes; e != a {
		t.Errorf("expect %v, got %v", e, a)
	}

}

func TestbuildNewRequest(t *testing.T) {

	req := buildRequest("POST", "bj", `{"a":"b"}`)
	newReq, reader := buildNewRequest(req, "host;x-amz-date")
	req.Body = ioutil.NopCloser(reader)

	byteTmp, _ := ioutil.ReadAll(req.Body)
	byteTmp2, _ := ioutil.ReadAll(newReq.Body)

	if string(byteTmp) != string(byteTmp2) {
		t.Errorf("expect %v, got %v", string(byteTmp), string(byteTmp2))
	}

	if e, a := req.RequestURI, newReq.RequestURI; e != a {
		t.Errorf("expect %v, got %v", e, a)
	}

}

func TestSplitAwsAuthStr(t *testing.T) {
	cases := []struct {
		input    string
		authInfo map[string]string
		err      error
	}{
		{
			input: "AWS4-HMAC-SHA256 Credential=NTg3NGJmNTItNjM1Ni00/20170908/bj/lambda/aws4_request, SignedHeaders=host;x-amz-date",
			err:   errors.New(invalidAuthStrLen),
		},
		{
			input: "AWS4-HMAC-SHA256 Credential=NTg3NGJmNTItNjM1Ni00/20170908/bj/lambda, SignedHeaders=host;x-amz-date, Signature=061b11080cc42679953a897da91acbcd04a4fae15a6e9ce31f6f2b31fe1d2b19",
			err:   errors.New(invalidCredLen),
		},
		{
			input: "AWS4-HMAC-SHA256 Credential=NTg3NGJmNTItNjM1Ni00/20170908/bj/lambda/aws4_request, SignedHeaders=host;x-amz-date, Signature=061b11080cc42679953a897da91acbcd04a4fae15a6e9ce31f6f2b31fe1d2b19",
			authInfo: map[string]string{
				"userAk":        "NTg3NGJmNTItNjM1Ni00",
				"region":        "bj",
				"serviceName":   "lambda",
				"signedHeaders": "host;x-amz-date",
			},
		},
	}
	for _, tc := range cases {
		t.Run(tc.input, func(t *testing.T) {
			userAk, region, serviceName, signedHeaders, err := SplitAwsAuthStr(tc.input)

			if err == nil && tc.err != nil {
				t.Errorf("Expected err to be %v, but got nil", tc.err)
			} else if err != nil && tc.err == nil {
				t.Errorf("Expected err to be nil, but got %v", err)
			} else if err != nil && tc.err != nil && err.Error() != tc.err.Error() {
				t.Errorf("Expected err to be %v, but got %v", tc.err, err)
			}
			if userAk != (tc.authInfo)["userAk"] {
				t.Errorf("Expected  %v, got %v", (tc.authInfo)["userAk"], userAk)
			}
			if region != (tc.authInfo)["region"] {
				t.Errorf("Expected  %v, got %v", (tc.authInfo)["region"], userAk)
			}
			if serviceName != (tc.authInfo)["serviceName"] {
				t.Errorf("Expected  %v, got %v", (tc.authInfo)["serviceName"], userAk)
			}
			if signedHeaders != (tc.authInfo)["signedHeaders"] {
				t.Errorf("Expected  %v, got %v", (tc.authInfo)["signedHeaders"], userAk)
			}
		})
	}
}
