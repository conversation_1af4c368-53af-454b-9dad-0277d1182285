package sts_credential

import (
	"fmt"
	"time"

	"golang.org/x/sync/syncmap"
)

type stsLocalCredentialCache struct {
	internalMap syncmap.Map
	ticker      *time.Ticker
}

func NewLocalCredentialCache() Cache {
	cache := &stsLocalCredentialCache{}
	cache.ticker = time.NewTicker(time.Second * 37)
	go cache.clean()
	return cache
}

func (c *stsLocalCredentialCache) Get(userId, role string) *StsCredential {
	tkey := fmt.Sprintf("%s:%s", userId, role)
	tnow := time.Now()
	return c.get(tkey, tnow)
}

func (c *stsLocalCredentialCache) Set(userId, role string, credential *StsCredential) {
	tkey := fmt.Sprintf("%s:%s", userId, role)
	// 过期时间减少10分钟
	credential.Expiration = credential.Expiration.Add(-10 * time.Minute)
	c.internalMap.Store(tkey, credential)
}

func (c *stsLocalCredentialCache) get(tkey string, expire time.Time) *StsCredential {
	data, ok := c.internalMap.Load(tkey)
	if ok {
		credential := data.(*StsCredential)
		if credential.Expiration.After(expire) {
			return credential
		}
		c.internalMap.Delete(tkey)
	}
	return nil
}

func (c *stsLocalCredentialCache) clean() {
	for range c.ticker.C {
		c.doClean()
	}
}

func (c *stsLocalCredentialCache) doClean() {
	expired := make([]string, 0)
	now := time.Now()
	c.internalMap.Range(func(k, v interface{}) bool {
		key := k.(string)
		val := v.(*StsCredential)
		if val.Expiration.Before(now) {
			expired = append(expired, key)
		}
		return true
	})
	for _, key := range expired {
		v, ok := c.internalMap.Load(key)
		if ok {
			val := v.(*StsCredential)
			if val.Expiration.Before(now) {
				c.internalMap.Delete(key)
			}
		}
	}
}
