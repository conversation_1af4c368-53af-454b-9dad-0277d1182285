package sts_credential

import (
	"fmt"
	"time"

	"github.com/go-redis/redis"
	kunRedis "icode.baidu.com/baidu/faas/kun/pkg/rpc/redis"
	"icode.baidu.com/baidu/faas/kun/pkg/util/json"
	"icode.baidu.com/baidu/faas/kun/pkg/util/logs"
)

const keyPrefix = "StsCredential"

type stsCredentialCache struct {
	kunRedis.Client
}

func NewStsCredentialCacheRedis(redisOptions *kunRedis.RedisOptions) *stsCredentialCache {
	client := kunRedis.NewClient(redisOptions)
	return &stsCredentialCache{*client}
}

func (r *stsCredentialCache) Get(userId, role string) *StsCredential {
	key := r.stsCredentialKey(userId, role)
	val, err := r.Redis().Get(key).Result()
	if err == redis.Nil {
		return nil
	} else if err != nil {
		logs.Warnf("Failed when accessing redis for %s: %s", key, err.Error())
		return nil
	}

	credential := StsCredential{}
	if err = json.Unmarshal([]byte(val), &credential); err != nil {
		logs.Warnf("Failed when unmarshal redis result for %s: %s", key, err.Error())
		return nil
	}

	return &credential
}

func (r *stsCredentialCache) Set(userId, role string, credential *StsCredential) {
	expiration := credential.Expiration.Sub(time.Now()) - 10*time.Minute
	// 如果马上就要过期了，那就不缓存了
	if expiration <= 0 {
		return
	}
	key := r.stsCredentialKey(userId, role)
	err := r.Redis().Set(key, credential, expiration).Err()
	if err != nil {
		logs.Warnf("Failed when saving redis cache for %s: %s", key, err.Error())
	}
}

func (r *stsCredentialCache) stsCredentialKey(userId, role string) string {
	return fmt.Sprintf("%s%s:%s:%s", r.KeyPrefix(), keyPrefix, userId, role)
}
