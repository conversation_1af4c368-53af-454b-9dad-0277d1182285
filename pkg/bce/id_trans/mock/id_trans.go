// Code generated by MockGen. DO NOT EDIT.
// Source: icode.baidu.com/baidu/faas/kun/pkg/bce/id_trans (interfaces: Interface)

// Package mock is a generated GoMock package.
package mock

import (
	gomock "github.com/golang/mock/gomock"
	id_trans "icode.baidu.com/baidu/faas/kun/pkg/bce/id_trans"
	reflect "reflect"
)

// MockInterface is a mock of Interface interface
type MockInterface struct {
	ctrl     *gomock.Controller
	recorder *MockInterfaceMockRecorder
}

// MockInterfaceMockRecorder is the mock recorder for MockInterface
type MockInterfaceMockRecorder struct {
	mock *MockInterface
}

// NewMockInterface creates a new mock instance
func NewMockInterface(ctrl *gomock.Controller) *MockInterface {
	mock := &MockInterface{ctrl: ctrl}
	mock.recorder = &MockInterfaceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use
func (m *MockInterface) EXPECT() *MockInterfaceMockRecorder {
	return m.recorder
}

// TransServiceShortID mocks base method
func (m *MockInterface) TransServiceShortID(arg0 *id_trans.Short2LongReq) (*id_trans.Short2LongResp, error) {
	ret := m.ctrl.Call(m, "TransServiceShortID", arg0)
	ret0, _ := ret[0].(*id_trans.Short2LongResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// TransServiceShortID indicates an expected call of TransServiceShortID
func (mr *MockInterfaceMockRecorder) TransServiceShortID(arg0 interface{}) *gomock.Call {
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "TransServiceShortID", reflect.TypeOf((*MockInterface)(nil).TransServiceShortID), arg0)
}

// TransUserShortID mocks base method
func (m *MockInterface) TransUserShortID(arg0 string, arg1 *id_trans.Short2LongReq) (*id_trans.Short2LongResp, error) {
	ret := m.ctrl.Call(m, "TransUserShortID", arg0, arg1)
	ret0, _ := ret[0].(*id_trans.Short2LongResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// TransUserShortID indicates an expected call of TransUserShortID
func (mr *MockInterfaceMockRecorder) TransUserShortID(arg0, arg1 interface{}) *gomock.Call {
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "TransUserShortID", reflect.TypeOf((*MockInterface)(nil).TransUserShortID), arg0, arg1)
}
