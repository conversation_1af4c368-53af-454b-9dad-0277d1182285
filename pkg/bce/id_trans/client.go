package id_trans

import (
	"net/url"
	"strings"

	"icode.baidu.com/baidu/faas/kun/pkg/bce/auth"
	"icode.baidu.com/baidu/faas/kun/pkg/bce/iam"
	"icode.baidu.com/baidu/faas/kun/pkg/bce/sts_credential"
	"icode.baidu.com/baidu/faas/kun/pkg/rest"
)

type Interface interface {
	TransUserShortID(string, *Short2LongReq) (*Short2LongResp, error)
	TransServiceShortID(*Short2LongReq) (*Short2LongResp, error)
}

// Client xxx
type Client struct {
	client          *rest.RESTClient
	iamClient       iam.ClientInterface
	stsRole         string
	stsCache        sts_credential.Cache
	accessKey       string
	secretAccessKey string
}

type Short2LongReq struct {
	ShortIDs []string `json:"shortIds"`
}

type Short2LongResp struct {
	Map map[string]string `json:"map"`
}

// NewClient xxx
func NewClient(endpoint, stsRoleName, ak, sk string, cache sts_credential.Cache, iamClient iam.ClientInterface) *Client {
	version := "v1"
	config := rest.ContentConfig{
		BackendType: rest.BackendTypeBce,
	}

	baseURL, _ := url.Parse("http://" + strings.Replace(endpoint, "http://", "", 1))
	client, _ := rest.NewRESTClient(baseURL, version, config, nil)
	return &Client{
		client:          client,
		iamClient:       iamClient,
		stsCache:        cache,
		stsRole:         stsRoleName,
		accessKey:       ak,
		secretAccessKey: sk,
	}
}

func (c *Client) sign(ak, sk string, request *rest.Request) (err error) {
	reqURL := request.URL()
	request.SetHeader("Host", reqURL.Host)

	bceAuth := auth.NewBceAuth(ak, sk)
	sign := bceAuth.NewSigner().
		Method(request.Verb()).
		Path(reqURL.Path).
		Params(reqURL.Query()).
		Headers(request.Header()).
		Expire(3600).
		WithSignedHeader().
		GetSign()

	request.SetHeader("Authorization", sign)
	return nil
}

// TransUserShortID xxx
func (c *Client) TransUserShortID(uid string, param *Short2LongReq) (res *Short2LongResp, err error) {
	req := c.client.Post().
		Resource("/api/logical/network/id/short").
		Body(param)

	credential := c.stsCache.Get(uid, c.stsRole)
	if credential == nil {
		if credential, err = c.iamClient.AssumeRole(uid, c.stsRole, "", 3600); err != nil {
			return
		}
		c.stsCache.Set(uid, c.stsRole, credential)
	}

	req.SetHeader("X-Bce-Security-Token", credential.SessionToken)
	if err = c.sign(credential.AccessKeyId, credential.AccessKeySecret, req); err != nil {
		return
	}

	res = &Short2LongResp{
		Map: make(map[string]string),
	}

	err = req.Do().Into(res)
	return
}

// TransServiceShortID xxx
func (c *Client) TransServiceShortID(param *Short2LongReq) (res *Short2LongResp, err error) {
	req := c.client.Post().
		Resource("/api/logical/network/id/short").
		Body(param)

	if err = c.sign(c.accessKey, c.secretAccessKey, req); err != nil {
		return
	}

	res = &Short2LongResp{
		Map: make(map[string]string),
	}

	err = req.Do().Into(res)
	return
}

type mockClient struct{}

func NewMockClient() *mockClient {
	return &mockClient{}
}

func (m *mockClient) TransUserShortID(uid string, param *Short2LongReq) (res *Short2LongResp, err error) {
	return nil, nil
}

func (c *mockClient) TransServiceShortID(param *Short2LongReq) (res *Short2LongResp, err error) {
	return nil, nil
}
