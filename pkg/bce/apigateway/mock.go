package apigateway

import "errors"

type mockClient struct{}

func NewMockClient() *mockClient {
	return &mockClient{}
}

func (m *mockClient) ListApis(info *RequestInfo, groupId string, apiName string) (*GetApisOutput, error) {
	if apiName == "badcase" || info.UID == "1" {
		return nil, errors.New("not found")
	}
	if info.UID == "2" {
		return &GetApisOutput{
			TotalCount: 0,
			PageNo:     1,
			PageSize:   1,
			Apis:       nil}, nil
	}
	return &GetApisOutput{
		TotalCount: 1,
		PageNo:     1,
		PageSize:   1,
		Apis: []ApiSummary{
			{
				GroupId:     "groupId",
				GroupName:   "groupName",
				ApiId:       "apiId",
				Name:        "name",
				Description: "description",
				Visibility:  "visibility",
				Method:      "ANY",
				Path:        "/path",
				PathMode:    "pathMode",
			},
		},
	}, nil
}

func (m *mockClient) GetApi(info *RequestInfo, apiId string) (*GetApiOutput, error) {
	if info.UID == "1" {
		return nil, nil
	}
	return &GetApiOutput{
		Region:      "bj",
		ApiId:       "apiId",
		GroupName:   "test",
		GroupId:     "groupId",
		Name:        "name",
		Visibility:  "visibility",
		Description: "description",
		AuthConfig: AuthConfig{
			Type: "type",
		},
		RequestConfig: RequestConfig{
			Protocol:        "http",
			Method:          "ANY",
			Path:            "/path",
			HeaderMode:      "headMode",
			QueryMode:       "queryMode",
			BodyMode:        "bodyMode",
			BodyFormat:      "bodyFormat",
			BodyModel:       "",
			BodyDescription: "bodyDescription",
			PathMode:        "pathMode",
		},
		ServiceConfig: ServiceConfig{
			Type:    "type",
			Timeout: 90000,
			HttpConfig: HttpConfig{
				Hosts: []HttpHost{
					{
						Address: "brn:bce:cfc:bj:847b8d3d36fbfb4f191f048e041af26c:function:test:$LATEST",
						Type:    "ANY",
						Weight:  100,
						Region:  "DEFAULT",
					},
				},
				Path:                "/path",
				Method:              "ANY",
				ContentTypeCategory: "",
				ContentTypeValue:    "",
				PathMode:            "pathMode",
				BalanceStrategy:     "ROUND",
			},
		},
		RequestParameter: []RequestParameter{},
		ServiceParameter: []ServiceParameter{},
		ParametersMap:    []ServiceParametersMap{},
		ResultType:       "JSON",
		ResultSample:     "",
		FailResultSample: "",
		ErrorCodeSample:  []ErrorCodeSample{},
	}, nil
}

func (m *mockClient) GetApistageTag(info *RequestInfo, apiId string) ([]string, error) {
	return []string{}, nil
}

func (m *mockClient) CreateApi(info *RequestInfo, req *CreateApiInput) (*CreateApiOutput, error) {
	return &CreateApiOutput{
		ApiId: "apiId",
	}, nil
}

func (m *mockClient) UpdateApi(info *RequestInfo, req *CreateApiInput, apiId string) (*UpdateApiOutput, error) {
	return &UpdateApiOutput{
		ApiId: "apiId",
	}, nil
}

func (m *mockClient) DeleteApi(info *RequestInfo, apiId string) error {
	return nil
}

func (m *mockClient) GetGroup(info *RequestInfo, groupId string) (*GroupsInfo, error) {
	return &GroupsInfo{}, nil
}

func (m *mockClient) ListGroups(info *RequestInfo) (*GroupsInfo, error) {
	if info.UID == "errorUid" {
		return nil, errors.New(NotActiveAllowUser)
	}
	return &GroupsInfo{}, nil
}

func (m *mockClient) Deploy(info *RequestInfo, apiId string, stageTag string) (*Deployment, error) {
	return &Deployment{}, nil
}

func (m *mockClient) Abolish(info *RequestInfo, apiId string, stageTag []string) error {
	return nil
}
