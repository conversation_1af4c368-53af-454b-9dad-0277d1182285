package apigateway

import (
	"encoding/json"
	"errors"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"

	"icode.baidu.com/baidu/faas/kun/pkg/api"
	apiErr "icode.baidu.com/baidu/faas/kun/pkg/apiserver/error"
	"icode.baidu.com/baidu/faas/kun/pkg/bce/sts_credential"
	kunErr "icode.baidu.com/baidu/faas/kun/pkg/error"
)

var apiInfo = CreateApiInput{
	GroupId:     "groupId",
	Name:        "name",
	Visibility:  "visibility",
	Description: "description",
	AuthConfig: AuthConfig{
		Type: "type",
	},
	RequestConfig: RequestConfig{
		Protocol:        "http",
		Method:          "ANY",
		Path:            "/path",
		HeaderMode:      "headMode",
		QueryMode:       "queryMode",
		BodyMode:        "bodyMode",
		BodyFormat:      "bodyFormat",
		BodyModel:       "bodyModel",
		BodyDescription: "bodyDescription",
		PathMode:        "pathMode",
	},
	ServiceConfig: ServiceConfig{
		Type:    "type",
		Timeout: 90000,
		HttpConfig: HttpConfig{
			Hosts: []HttpHost{
				{
					Address: "functionBrn",
					Type:    "ANY",
					Weight:  1,
					Region:  "DEFAULT",
				},
			},
			Path:                "/path",
			Method:              "ANY",
			ContentTypeCategory: "",
			ContentTypeValue:    "",
			PathMode:            "pathMode",
			BalanceStrategy:     "ROUND",
		},
	},
	RequestParameter: []RequestParameter{},
	ServiceParameter: []ServiceParameter{},
	ParametersMap:    []ServiceParametersMap{},
	ResultType:       "JSON",
	ResultSample:     "",
	FailResultSample: "",
	ErrorCodeSample:  []ErrorCodeSample{},
}

var apiListResponse = GetApisOutput{
	TotalCount: 1,
	PageNo:     1,
	PageSize:   1,
	Apis: []ApiSummary{
		{
			GroupId:     "groupId",
			GroupName:   "groupName",
			ApiId:       "apiId",
			Name:        "name",
			Description: "description",
			Visibility:  "visibility",
			Method:      "ANY",
			Path:        "/path",
			PathMode:    "pathMode",
		},
	},
}

var apiResponse = GetApiOutput{
	GroupName:   "groupName",
	ApiId:       "apiId",
	Region:      "bj",
	GroupId:     "groupId",
	Name:        "name",
	Visibility:  "visibility",
	Description: "description",
	AuthConfig: AuthConfig{
		Type: "type",
	},
	RequestConfig: RequestConfig{
		Protocol:        "http",
		Method:          "ANY",
		Path:            "/path",
		HeaderMode:      "headMode",
		QueryMode:       "queryMode",
		BodyMode:        "bodyMode",
		BodyFormat:      "bodyFormat",
		BodyModel:       "",
		BodyDescription: "bodyDescription",
		PathMode:        "test",
	},
	ServiceConfig: ServiceConfig{
		Type:    "type",
		Timeout: 90000,
		HttpConfig: HttpConfig{
			Hosts: []HttpHost{
				{
					Address: "functionBrn",
					Type:    "ANY",
					Weight:  1,
					Region:  "DEFAULT",
				},
			},
			Path:                "/path",
			Method:              "ANY",
			ContentTypeCategory: "",
			ContentTypeValue:    "",
			BalanceStrategy:     "ROUND",
			PathMode:            "test",
		},
	},
	RequestParameter: []RequestParameter{},
	ServiceParameter: []ServiceParameter{},
	ParametersMap:    []ServiceParametersMap{},
	ResultType:       "JSON",
	ResultSample:     "",
	FailResultSample: "",
	ErrorCodeSample:  []ErrorCodeSample{},
}

var createApiOutput = CreateApiOutput{
	ApiId: "testtest",
}

var groupsInfo = GroupsInfo{
	TotalCount: 1,
	PageNumber: 1,
	PageSize:   100,
	Groups: []GroupModel{
		{
			GroupId:       "groupID",
			GroupName:     "GroupName",
			DefaultDomain: "domain",
			Description:   "description",
			CreateTime:    time.Now(),
			UpdateTime:    time.Now(),
			Region:        "region",
			ApiCount:      1,
			MaxQps:        -1,
			BillingStatus: "status",
			IllegalStatus: "sattus",
		},
	},
}

var apiListResponseJson, _ = json.Marshal(apiListResponse)
var apiResponseJson, _ = json.Marshal(apiResponse)
var createApiOutputJson, _ = json.Marshal(createApiOutput)
var groupsInfoJson, _ = json.Marshal(groupsInfo)

func TestCreateApi(t *testing.T) {
	info, input, cache := prepareCreate()
	ts := []*httptest.Server{
		httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			w.WriteHeader(http.StatusOK)
			w.Write(createApiOutputJson)
		})),
		httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			w.WriteHeader(400)
		})),
		httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			w.WriteHeader(404)
		})),
		httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			w.WriteHeader(409)
		})),
		httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			w.WriteHeader(500)
		})),
	}
	except := []error{
		nil,
		kunErr.NewInvalidParameterValueException("readObjectStart: expect { or n, but found \x00, error found in #0 byte of ...||..., bigger context ...||...", nil),
		apiErr.NewResourceNotFoundException("readObjectStart: expect { or n, but found \x00, error found in #0 byte of ...||..., bigger context ...||...", nil),
		apiErr.NewResourceConflictException("readObjectStart: expect { or n, but found \x00, error found in #0 byte of ...||..., bigger context ...||...", nil),
		kunErr.NewServiceException("readObjectStart: expect { or n, but found \x00, error found in #0 byte of ...||..., bigger context ...||...", nil),
	}

	for i, test := range ts {

		cli := NewClient(test.URL, api.StsRoleName, cache, nil)
		_, err := cli.CreateApi(info, input)
		assert.Equal(t, except[i], err)
	}
}

func TestUpdateApi(t *testing.T) {
	info, input, cache := prepareCreate()
	ts := []*httptest.Server{
		httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			w.WriteHeader(http.StatusOK)
			w.Write(createApiOutputJson)
		})),
		httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			w.WriteHeader(http.StatusConflict)
		})),
		httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			w.WriteHeader(http.StatusNotFound)
		})),
		httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			w.WriteHeader(http.StatusInternalServerError)
		})),
		httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			w.WriteHeader(http.StatusBadRequest)
		})),
	}

	except := []error{
		nil,
		apiErr.NewResourceConflictException("readObjectStart: expect { or n, but found \x00, error found in #0 byte of ...||..., bigger context ...||...", nil),
		apiErr.NewResourceNotFoundException("readObjectStart: expect { or n, but found \x00, error found in #0 byte of ...||..., bigger context ...||...", nil),
		kunErr.NewServiceException("readObjectStart: expect { or n, but found \x00, error found in #0 byte of ...||..., bigger context ...||...", nil),
		kunErr.NewInvalidParameterValueException("readObjectStart: expect { or n, but found \x00, error found in #0 byte of ...||..., bigger context ...||...", nil),
	}

	for i, test := range ts {
		cli := NewClient(test.URL, api.StsRoleName, cache, nil)
		_, err := cli.UpdateApi(info, input, "apiId")

		assert.Equal(t, err, except[i])
	}
}

func TestListApis(t *testing.T) {
	info, _, cache := prepareCreate()
	ts := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
		w.Write(apiListResponseJson)
	}))
	defer ts.Close()

	cli := NewClient(ts.URL, api.StsRoleName, cache, nil)
	apis, err := cli.ListApis(info, "", "")
	assert.Equal(t, err, nil)
	assert.Equal(t, *apis, apiListResponse)
}

func TestGetApi(t *testing.T) {
	info, _, cache := prepareCreate()
	ts := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
		w.Write(apiResponseJson)
	}))
	defer ts.Close()

	cli := NewClient(ts.URL, api.StsRoleName, cache, nil)

	_, err := cli.GetApi(info, "apiId")
	assert.Equal(t, err, nil)
}

func TestGetApistageTag(t *testing.T) {
	info, _, cache := prepareCreate()
	ts := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
		w.Write(apiResponseJson)
	}))
	defer ts.Close()

	cli := NewClient(ts.URL, api.StsRoleName, cache, nil)

	_, err := cli.GetApistageTag(info, "apiId")
	assert.Equal(t, err, nil)
}

func TestGetGroup(t *testing.T) {
	info, _, cache := prepareCreate()
	ts := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
		w.Write(apiResponseJson)
	}))
	defer ts.Close()

	cli := NewClient(ts.URL, api.StsRoleName, cache, nil)

	_, err := cli.GetGroup(info, "groupId")
	assert.Equal(t, err, nil)
}

func TestDeleteApi(t *testing.T) {
	info, _, cache := prepareCreate()
	ts := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
	}))
	defer ts.Close()

	cli := NewClient(ts.URL, api.StsRoleName, cache, nil)
	err := cli.DeleteApi(info, "apiId")
	assert.Equal(t, err, nil)
}

func TestListGroups(t *testing.T) {
	info, _, cache := prepareCreate()

	ts := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
		w.Write(groupsInfoJson)
	}))
	defer ts.Close()

	client := NewClient(ts.URL, api.StsRoleName, cache, nil)
	_, err := client.ListGroups(info)
	assert.Nil(t, err)
}

func TestDeploy(t *testing.T) {
	info, _, cache := prepareCreate()

	ts := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
	}))
	defer ts.Close()

	client := NewClient(ts.URL, api.StsRoleName, cache, nil)
	_, err := client.Deploy(info, "", "")
	assert.Nil(t, err)

}

func TestAbolish(t *testing.T) {
	info, _, cache := prepareCreate()

	ts := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
	}))
	defer ts.Close()

	client := NewClient(ts.URL, api.StsRoleName, cache, nil)
	err := client.Abolish(info, "", []string{"test"})
	assert.Nil(t, err)

}

func TestWrapErrorInfo(t *testing.T) {
	_, input, cache := prepareCreate()
	ts := []*httptest.Server{
		httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			w.WriteHeader(http.StatusOK)
		})),
		httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			w.WriteHeader(http.StatusConflict)
		})),
		httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			w.WriteHeader(http.StatusNotFound)
		})),
		httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			w.WriteHeader(http.StatusInternalServerError)
		})),
		httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			w.WriteHeader(http.StatusBadRequest)
		})),
		httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			w.WriteHeader(http.StatusBadRequest)
			w.Write([]byte(`{"requestId":"c8605d5b-1c6d-460b-926f-2ef6d7260f15","code":"NotActiveAllowUser","message":"The API gateway has not yet been activated, please contact customer service."}`))
		})),
	}
	exception := []error{
		nil,
		apiErr.NewResourceConflictException("readObjectStart: expect { or n, but found \x00, error found in #0 byte of ...||..., bigger context ...||...", nil),
		apiErr.NewResourceNotFoundException("readObjectStart: expect { or n, but found \x00, error found in #0 byte of ...||..., bigger context ...||...", nil),
		kunErr.NewServiceException("readObjectStart: expect { or n, but found \x00, error found in #0 byte of ...||..., bigger context ...||...", nil),
		kunErr.NewInvalidParameterValueException("readObjectStart: expect { or n, but found \x00, error found in #0 byte of ...||..., bigger context ...||...", nil),
		errors.New(NotActiveAllowUser),
	}

	for index, test := range ts {
		cli := NewClient(test.URL, api.StsRoleName, cache, nil)
		req := cli.client.Post().
			Resource("/api").
			Body(input)

		result := req.Do()

		assert.Equal(t, wrapErrorInfo(result), exception[index])
	}
}

func prepareCreate() (*RequestInfo, *CreateApiInput, sts_credential.Cache) {
	uid := "testUid"
	info := NewRequestInfo(uid, "requestID_123456")

	credential := &sts_credential.StsCredential{
		AccessKeyId:     "accessKeyId",
		AccessKeySecret: "accessKeySecret",
		SessionToken:    "sessionToken",
		Expiration:      time.Now().Add(60 * time.Minute),
	}

	cache := sts_credential.NewLocalCredentialCache()
	cache.Set(uid, api.StsRoleName, credential)

	return info, &apiInfo, cache
}
