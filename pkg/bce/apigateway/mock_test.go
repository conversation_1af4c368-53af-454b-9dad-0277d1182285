package apigateway

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestListApisMock(t *testing.T) {
	m := NewMockClient()
	_, err := m.ListApis(&RequestInfo{UID: "1"}, "", "")
	assert.NotEqual(t, nil, err)
	_, err = m.ListApis(&RequestInfo{UID: "2"}, "", "")
	assert.Equal(t, nil, err)
	_, err = m.ListApis(&RequestInfo{UID: "3"}, "", "")
	assert.Equal(t, nil, err)
}

func TestGetApiMock(t *testing.T) {
	m := NewMockClient()
	_, err := m.GetApi(&RequestInfo{UID: "1"}, "")
	assert.Equal(t, nil, err)
	_, err = m.GetApi(&RequestInfo{UID: "2"}, "")
	assert.Equal(t, nil, err)
}

func TestUpdateApiMock(t *testing.T) {
	m := NewMockClient()
	_, err := m.UpdateApi(nil, nil, "")
	assert.Equal(t, nil, err)
}

func TestDeleteApiMock(t *testing.T) {
	m := NewMockClient()
	err := m.DeleteApi(nil, "")
	assert.Equal(t, nil, err)
}

func TestCreateApiMock(t *testing.T) {
	m := NewMockClient()
	_, err := m.CreateApi(nil, nil)
	assert.Equal(t, nil, err)
}

func TestAbolishMock(t *testing.T) {
	m := NewMockClient()
	err := m.Abolish(nil, "", nil)
	assert.Equal(t, nil, err)
}

func TestGetGroupMock(t *testing.T) {
	m := NewMockClient()
	_, err := m.GetGroup(nil, "")
	assert.Equal(t, nil, err)
}

func TestGetApistageTagMock(t *testing.T) {
	m := NewMockClient()
	_, err := m.GetApistageTag(nil, "")
	assert.Equal(t, nil, err)
}

func TestListGroupsMock(t *testing.T) {
	m := NewMockClient()
	_, err := m.ListGroups(&RequestInfo{UID: "errorUid"})
	assert.NotEqual(t, nil, err)
	_, err = m.ListGroups(&RequestInfo{UID: "Uid"})
	assert.Equal(t, nil, err)
}
