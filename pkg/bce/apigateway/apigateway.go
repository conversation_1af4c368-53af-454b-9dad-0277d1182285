package apigateway

import (
	"errors"
	"fmt"
	"net/http"
	"net/url"
	"strings"
	"time"

	"icode.baidu.com/baidu/faas/kun/pkg/api"
	apiErr "icode.baidu.com/baidu/faas/kun/pkg/apiserver/error"
	"icode.baidu.com/baidu/faas/kun/pkg/bce/auth"
	"icode.baidu.com/baidu/faas/kun/pkg/bce/iam"
	"icode.baidu.com/baidu/faas/kun/pkg/bce/sts_credential"
	kunErr "icode.baidu.com/baidu/faas/kun/pkg/error"
	"icode.baidu.com/baidu/faas/kun/pkg/rest"
)

const NotActiveAllowUser = "The API gateway has not yet been activated, please contact customer service."

var StageTag = []string{"pre-release", "release", "test"}

type ApigatewayController interface {
	// 本函数用于根据groupId以及apiName获取对应API
	ListApis(*RequestInfo, string, string) (*GetApisOutput, error)

	// 本函数用于api-gateway创建api
	CreateApi(*RequestInfo, *CreateApiInput) (*CreateApiOutput, error)

	// 本函数用于更新API相关信息
	UpdateApi(*RequestInfo, *CreateApiInput, string) (*UpdateApiOutput, error)

	// 本函数用于删除对应API
	DeleteApi(*RequestInfo, string) error

	// 本函数用于通过apiId获取API相关详细信息
	GetApi(*RequestInfo, string) (*GetApiOutput, error)

	// 本函数通过apiId和API获取API对环境列表
	GetApistageTag(*RequestInfo, string) ([]string, error)

	// GetGroup函数用于获取group详细信息
	GetGroup(*RequestInfo, string) (*GroupsInfo, error)

	// ListGroups函数用于获取group列表
	ListGroups(*RequestInfo) (*GroupsInfo, error)

	// Deploy函数用于发布API
	Deploy(*RequestInfo, string, string) (*Deployment, error)

	// Abolish函数用于下线API
	Abolish(*RequestInfo, string, []string) error
}

type GetApiOutput struct {
	TagName          interface{}            `json:"tagName"`
	TagId            interface{}            `json:"tagId"`
	Region           string                 `json:"region"`
	ApiId            string                 `json:"apiId"`
	GroupName        string                 `json:"groupName"`
	CreateTime       time.Time              `json:"createTime"`
	UpdateTime       time.Time              `json:"updateTime"`
	GroupId          string                 `json:"groupId"`
	Name             string                 `json:"name"`
	Visibility       string                 `json:"visibility"`
	Description      string                 `json:"description"`
	AuthConfig       AuthConfig             `json:"authConfig"`
	RequestConfig    RequestConfig          `json:"requestConfig"`
	ServiceConfig    ServiceConfig          `json:"serviceConfig"`
	RequestParameter []RequestParameter     `json:"requestParameter"`
	ServiceParameter []ServiceParameter     `json:"serviceParameter"`
	ParametersMap    []ServiceParametersMap `json:"parametersMap"`
	ResultType       string                 `json:"resultType"`
	ResultSample     string                 `json:"resultSample"`
	FailResultSample string                 `json:"failResultSample"`
	ErrorCodeSample  []ErrorCodeSample      `json:"errorCodeSample"`
}

type GetApistageTagOutput struct {
	GroupName         string             `json:"groupName"`
	StageName         string             `json:"stageName"`
	StageTag          string             `json:"stageTag"`
	APIName           string             `json:"apiName"`
	Description       string             `json:"description"`
	Visibility        string             `json:"visibility"`
	AuthType          string             `json:"authType"`
	ResultType        string             `json:"resultType"`
	ResultSample      string             `json:"resultSample"`
	FailResultSample  string             `json:"failResultSample"`
	ErrorCodeSamples  []ErrorCodeSample  `json:"errorCodeSamples"`
	RequestConfig     RequestConfig      `json:"requestConfig"`
	RequestParameters []RequestParameter `json:"requestParameters"`
	GroupID           string             `json:"groupId"`
	ApiId             string             `json:"apiId"`
}

// APIGateway request 参数
type RequestParameter struct {
	Name         string `json:"name"`
	Location     string `json:"location"`
	Types        string `json:"type"`
	Required     string `json:"required"`
	DefaultValue string `json:"defaultValue"`
	FixedValue   string `json:"fixedValue"`
	DemoValue    string `json:"demoValue"`
	MaxValue     string `json:"maxValue"`
	MinValue     string `json:"minValue"`
	MaxLength    string `json:"maxLength"`
	MinLength    string `json:"minLength"`
	Regex        string `json:"regex"`
	JsonScheme   string `json:"jsonScheme"`
	EnumValue    string `json:"enumValue"`
	DocShow      string `json:"docShow"`
	Description  string `json:"description"`
}

// APIGateway暂未对外开放，为以后支持提前定义
type OpenIdConfig struct {
	Type           string `json:"type"`
	PublicKeyId    string `json:"publicKeyId"`
	PublicKey      string `json:"publicKey"`
	TokenParamName string `json:"tokenParamName"`
}

type AuthConfig struct {
	Type         string       `json:"type"`
	OpenIdConfig OpenIdConfig `json:"-"`
}

type ListGroupsError struct {
	RequestId string `json:"requestId"`
	Code      string `json:"code"`
	Message   string `json:"message"`
}

// APIGateway 请求配置
type RequestConfig struct {
	Protocol        string      `json:"protocol"`
	Method          string      `json:"method"`
	Path            string      `json:"path"`
	HeaderMode      string      `json:"headerMode"`
	QueryMode       string      `json:"queryMode"`
	BodyMode        string      `json:"bodyMode"`
	BodyFormat      string      `json:"bodyFormat"`
	BodyModel       string      `json:"-"`
	BodyDescription string      `json:"bodyDescription"`
	PathMode        interface{} `json:"pathMode"`
}

type HttpHost struct {
	Address string `json:"address"`
	Type    string `json:"type"`
	Weight  int    `json:"weight"`
	Region  string `json:"region"`
}

type HttpConfig struct {
	Hosts               []HttpHost  `json:"hosts"`
	Path                string      `json:"path"`
	Method              string      `json:"method"`
	ContentTypeCategory string      `json:"contentTypeCategory"`
	ContentTypeValue    string      `json:"contentTypeValue"`
	PathMode            interface{} `json:"pathMode"`
	BalanceStrategy     string      `json:"balanceStrategy"`
}

type ServiceConfig struct {
	Type          string        `json:"type"`
	Timeout       int           `json:"timeout"`
	HttpConfig    HttpConfig    `json:"httpConfig"`
	VpcConfig     VpcConfig     `json:"-"`
	ComputeConfig ComputeConfig `json:"-"`
	MockConfig    MockConfig    `json:"-"`
}

// 暂未开放，预留
type VpcConfig struct{}

// 暂未开放，预留
type ComputeConfig struct{}

type MockHeader struct {
	Key   string `json:"key"`
	Value string `json:"value"`
}
type MockConfig struct {
	Result  string       `json:"result"`
	Status  string       `json:"status"`
	Headers []MockHeader `json:"headers"`
}

type ServiceParameter struct {
	Name          string `json:"name"`
	Location      string `json:"location"`
	Type          string `json:"type"`
	Category      string `json:"category"`
	ConstantValue string `json:"constantValue"`
	SystemValue   string `json:"systemValue"`
}

type ServiceParametersMap struct {
	ServiceParameter string `json:"serviceParameter"`
	RequestParameter string `json:"requestParameter"`
}

type ErrorCodeSample struct {
	Code        string `json:"code"`
	Message     string `json:"message"`
	Description string `json:"description"`
}

type RequestInfo struct {
	UID       string
	RequestID string
}

type ApiSummary struct {
	GroupId     string    `json:"groupId"`
	GroupName   string    `json:"groupName"`
	ApiId       string    `json:"apiId"`
	Name        string    `json:"name"`
	Description string    `json:"description"`
	Visibility  string    `json:"visibility"`
	Method      string    `json:"method"`
	Path        string    `json:"path"`
	PathMode    string    `json:"pathMode"`
	CreateTime  time.Time `json:"createTime"`
	UpdateTime  time.Time `json:"updateTime"`
}

type GetApisOutput struct {
	TotalCount int          `json:"totalCount"`
	PageNo     int          `json:"pageNo"`
	PageSize   int          `json:"pageSize"`
	Apis       []ApiSummary `json:"apis"`
}

type UpdateApiOutput struct {
	ApiId string `json:"apiId"`
}

type CreateApiInput struct {
	Name             string                 `json:"name"`
	Visibility       string                 `json:"visibility"`
	Description      string                 `json:"description"`
	GroupId          string                 `json:"groupId"`
	AuthConfig       AuthConfig             `json:"authConfig"`
	RequestConfig    RequestConfig          `json:"requestConfig"`
	ServiceConfig    ServiceConfig          `json:"serviceConfig"`
	RequestParameter []RequestParameter     `json:"requestParameters"`
	ServiceParameter []ServiceParameter     `json:"serviceParameters"`
	ParametersMap    []ServiceParametersMap `json:"parametersMaps"`
	ResultType       string                 `json:"resultType"`
	ResultSample     string                 `json:"resultSample"`
	FailResultSample string                 `json:"failResultSample"`
	ErrorCodeSample  []ErrorCodeSample      `json:"errorCodeSamples"`
}

type UpdateApiInput struct {
	GroupId          string                 `json:"groupId"`
	Name             string                 `json:"name"`
	Visibility       string                 `json:"visibility"`
	Description      string                 `json:"description"`
	AuthConfig       AuthConfig             `json:"authConfig"`
	RequestConfig    RequestConfig          `json:"requestConfig"`
	ServiceConfig    ServiceConfig          `json:"serviceConfig"`
	RequestParameter []RequestParameter     `json:"requestParameter"`
	ServiceParameter []ServiceParameter     `json:"serviceParameter"`
	ParametersMap    []ServiceParametersMap `json:"parametersMap"`
	ResultType       string                 `json:"resultType"`
	ResultSample     string                 `json:"resultSample"`
	FailResultSample string                 `json:"failResultSample"`
	ErrorCodeSample  []ErrorCodeSample      `json:"errorCodeSample"`
}

type CreateApiOutput struct {
	ApiId string `json:"apiId"`
}

// 分组信息
type GroupsInfo struct {
	TotalCount int          `json:"totalCount"`
	PageNumber int          `json:"pageNumber"`
	PageSize   int          `json:"pageSize"`
	Groups     []GroupModel `json:"groups"`
}

// 分组详细信息
type GroupModel struct {
	GroupId       string    `json:"groupId"`
	GroupName     string    `json:"groupName"`
	DefaultDomain string    `json:"DefaultDomain"`
	Description   string    `json:"description"`
	CreateTime    time.Time `json:"createTime"`
	UpdateTime    time.Time `json:"updateTime"`
	Region        string    `json:"region"`
	ApiCount      int       `json:"apiCount"`
	MaxQps        int       `json:"maxQps"`
	BillingStatus string    `json:"billingStatus"`
	IllegalStatus string    `json:"illegalStatus"`
}

type DeployStage struct {
	StageTag    string `json:"stageTag"`
	Description string `json:"description"`
}

// API部署结果
type Deployment struct {
	TrafficPolicyId interface{}
	IpPolicyId      interface{}
}

type Client struct {
	client    *rest.RESTClient
	iamClient iam.ClientInterface
	stsRole   string
	stsCache  sts_credential.Cache
}

func NewRequestInfo(uid string, requestID string) *RequestInfo {
	return &RequestInfo{
		UID:       uid,
		RequestID: requestID,
	}
}

func NewClient(endpoint, stsRoleName string, cache sts_credential.Cache, iamClient iam.ClientInterface) *Client {
	version := "v1"
	config := rest.ContentConfig{
		BackendType: rest.BackendTypeBce,
	}

	baseURL, _ := url.Parse("http://" + strings.Replace(endpoint, "http://", "", 1))
	client, _ := rest.NewRESTClient(baseURL, version, config, nil)

	return &Client{
		client:    client,
		iamClient: iamClient,
		stsCache:  cache,
		stsRole:   stsRoleName,
	}
}

func (c *Client) sign(info *RequestInfo, request *rest.Request) (err error) {
	credential := c.stsCache.Get(info.UID, c.stsRole)
	if credential == nil {
		if credential, err = c.iamClient.AssumeRole(info.UID, c.stsRole, "", 3600); err != nil {
			return
		}
		c.stsCache.Set(info.UID, c.stsRole, credential)
	}

	reqURL := request.URL()
	request.SetHeader("Host", reqURL.Host)
	request.SetHeader(api.HeaderXBceSecurityToken, credential.SessionToken)

	if info.RequestID != "" {
		request.SetHeader(api.HeaderXRequestID, info.RequestID)
	}

	bceAuth := auth.NewBceAuth(credential.AccessKeyId, credential.AccessKeySecret)
	sign := bceAuth.NewSigner().
		Method(request.Verb()).
		Path(reqURL.Path).
		Params(reqURL.Query()).
		Headers(request.Header()).
		Expire(3600).
		WithSignedHeader().
		GetSign()

	request.SetHeader("Authorization", sign)

	return nil
}

// 由于GET API接口需要使用apiId，而apiId需在创建完成API之后方可获取。故此处使用List方法通过groupId与apiName获取对应api
func (c *Client) ListApis(info *RequestInfo, groupId string, apiName string) (*GetApisOutput, error) {
	req := c.client.Get().
		Resource("/api").
		Param("manner", "page").
		Param("groupId", groupId).
		Param("apiName", apiName)

	if err := c.sign(info, req); err != nil {
		return nil, err
	}

	apis := &GetApisOutput{}
	err := req.Do().Into(apis)
	if err != nil {
		return nil, err
	}
	return apis, nil
}

// 本函数用于创建触发器对应API
func (c *Client) CreateApi(info *RequestInfo, input *CreateApiInput) (*CreateApiOutput, error) {
	req := c.client.Post().
		Resource("/api").
		Body(input)

	if err := c.sign(info, req); err != nil {
		return nil, err
	}

	result := &CreateApiOutput{}
	res := req.Do()

	err := wrapErrorInfo(res)
	if err != nil {
		return nil, err
	}

	err = res.Into(result)
	if err != nil {
		return nil, err
	}
	return result, nil
}

func (c *Client) UpdateApi(info *RequestInfo, input *CreateApiInput, apiId string) (*UpdateApiOutput, error) {
	req := c.client.Put().
		Resource(fmt.Sprintf("/api/%s", apiId)).
		Body(input)

	if err := c.sign(info, req); err != nil {
		return nil, err
	}

	result := &UpdateApiOutput{}
	res := req.Do()

	err := wrapErrorInfo(res)
	if err != nil {
		return nil, err
	}

	err = res.Into(result)
	if err != nil {
		return nil, err
	}

	return result, nil
}

func (c *Client) DeleteApi(info *RequestInfo, apiId string) error {
	req := c.client.Delete().
		Resource(fmt.Sprintf("/api/%s", apiId))

	if err := c.sign(info, req); err != nil {
		return err
	}
	return wrapErrorInfo(req.Do())
}

func (c *Client) GetApi(info *RequestInfo, apiId string) (*GetApiOutput, error) {
	req := c.client.Get().
		Resource(fmt.Sprintf("/api/%s", apiId))

	if err := c.sign(info, req); err != nil {
		return nil, err
	}

	result := &GetApiOutput{}
	res := req.Do()

	err := wrapErrorInfo(res)
	if err != nil {
		return nil, err
	}

	err = res.Into(result)
	if err != nil {
		return nil, err
	}

	return result, nil
}
func (c *Client) GetApistageTag(info *RequestInfo, apiId string) ([]string, error) {
	result := []string{}
	for _, stageTag := range StageTag {
		req := c.client.Get().
			Resource(fmt.Sprintf("/api/%s", apiId)).
			Param("stageTag", stageTag).
			Param("doc", "")

		if err := c.sign(info, req); err != nil {
			return nil, err
		}

		apistageTagOutput := &GetApistageTagOutput{}
		res := req.Do()

		err := wrapErrorInfo(res)
		if err != nil {
			continue
		}

		err = res.Into(apistageTagOutput)
		if err != nil {
			continue
		}
		result = append(result, apistageTagOutput.StageTag)
	}
	return result, nil
}

// ListGroups函数用于获取APIGateway中分组列表
// 在创建APGW触发器时前端通过本函数获取对应APIGateway Group列表
func (c *Client) ListGroups(info *RequestInfo) (*GroupsInfo, error) {
	req := c.client.Get().
		Resource("/group").
		Param("manner", "page")

	if err := c.sign(info, req); err != nil {
		return nil, err
	}

	output := &GroupsInfo{}
	res := req.Do()
	err := wrapErrorInfo(res)
	if err != nil {
		return nil, err
	}
	err = res.Into(output)
	if err != nil {
		return nil, err
	}
	return output, nil
}

// 获取Group详细信息
func (c *Client) GetGroup(info *RequestInfo, groupId string) (*GroupsInfo, error) {
	req := c.client.Get().
		Resource("/group").
		Param("manner", "page").
		Param("groupId", groupId)

	if err := c.sign(info, req); err != nil {
		return nil, err
	}

	output := &GroupsInfo{}
	res := req.Do()
	err := wrapErrorInfo(res)
	if err != nil {
		return nil, err
	}
	err = res.Into(output)
	if err != nil {
		return nil, err
	}
	return output, nil
}

// Deploy函数用于发布触发器API至相关阶段
func (c *Client) Deploy(info *RequestInfo, apiId string, stageTag string) (*Deployment, error) {
	req := c.client.Put().
		Resource(fmt.Sprintf("/api/%s", apiId)).
		Param("deploy", "")

	if err := c.sign(info, req); err != nil {
		return nil, err
	}

	dep := &DeployStage{
		StageTag:    stageTag,
		Description: "cfc trigger",
	}
	req.Body(dep)

	result := req.Do()
	err := wrapErrorInfo(result)
	if err != nil {
		return nil, err
	}

	return nil, err
}

// Abolish函数从指定环境下线
func (c *Client) Abolish(info *RequestInfo, apiId string, stageTag []string) error {
	for _, stageTmp := range stageTag {
		req := c.client.Put().
			Resource(fmt.Sprintf("/api/%s", apiId)).
			Param("abolish", "")

		if err := c.sign(info, req); err != nil {
			return err
		}

		dep := &DeployStage{
			StageTag: stageTmp,
		}
		req.Body(dep)

		result := req.Do()
		err := wrapErrorInfo(result)
		if err != nil {
			return err
		}
	}
	return nil
}

func wrapErrorInfo(r rest.Result) error {
	if r.Error() != nil {
		var status int
		r.StatusCode(&status)
		// 用户未开通API Gateway
		if r.Error().Error() == NotActiveAllowUser {
			return errors.New(NotActiveAllowUser)
		}
		switch status {
		case http.StatusConflict:
			return apiErr.NewResourceConflictException(r.Error().Error(), nil)
		case http.StatusNotFound:
			return apiErr.NewResourceNotFoundException(r.Error().Error(), nil)
		case http.StatusInternalServerError:
			return kunErr.NewServiceException(r.Error().Error(), nil)
		default:
			return kunErr.NewInvalidParameterValueException(r.Error().Error(), nil)
		}
	}
	return nil
}
