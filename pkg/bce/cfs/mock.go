package cfs

import "errors"

type mockClient struct{}

func MockClient() *mockClient { return &mockClient{} }

func (m mockClient) listCFSInfos(uid string) (cfsInfo *CFSInfos, err error) {
	return &CFSInfos{
		FileSystemList: []*FileSystem{
			{
				FsId:   "fsid",
				FsName: "mockcfs",
				VpcId:  "vpcid",
				Type:   "type",
				MountTargetList: []*MountTarget{
					{
						Domain:   "domain",
						Ovip:     "ovip",
						SubnetId: "subnetid",
					},
				},
			},
		},
	}, nil
}

func (m mockClient) ListCFSInfosInSpecificSubnet(uid string, subnetId string) (res *FileSystemsRes, err error) {
	m.listCFSInfos(uid)
	m.GetCFSInfo(uid, "", "")
	return &FileSystemsRes{
		[]*FileSystem{
			{
				FsId:   "fsid",
				FsName: "mockcfs",
				VpcId:  "vpcid",
				Type:   "type",
				MountTargetList: []*MountTarget{
					{
						Domain:   "domain",
						Ovip:     "ovip",
						SubnetId: "subnetid",
					},
				},
			},
		},
	}, nil
}

func (m mockClient) GetCFSInfo(uid string, cfsId string, subnetId string) (FS *FileSystem, err error) {
	if uid == "" {
		return nil, errors.New("err")
	}
	return &FileSystem{
		FsId:   "fsid",
		FsName: "mockcfs",
		VpcId:  "vpcid",
		Type:   "type",
		MountTargetList: []*MountTarget{
			{
				Domain:   "domain",
				Ovip:     "ovip",
				SubnetId: "subnetid",
			},
		},
	}, nil
}
