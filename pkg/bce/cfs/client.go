package cfs

import (
	"errors"
	"net/url"
	"strings"

	"icode.baidu.com/baidu/faas/kun/pkg/bce/auth"
	"icode.baidu.com/baidu/faas/kun/pkg/bce/iam"
	"icode.baidu.com/baidu/faas/kun/pkg/bce/sts_credential"
	"icode.baidu.com/baidu/faas/kun/pkg/rest"
)

type Client struct {
	client    *rest.RESTClient
	iamClient iam.ClientInterface
	stsRole   string
	stsCache  sts_credential.Cache
}

func NewClient(endpoint, stsRoleName string, cache sts_credential.Cache, iamClient iam.ClientInterface) *Client {
	config := rest.ContentConfig{
		BackendType: rest.BackendTypeBce,
	}

	baseURL, _ := url.Parse("http://" + strings.Replace(endpoint, "http://", "", 1))
	client, _ := rest.NewRESTClient(baseURL, "", config, nil)
	return &Client{
		client:    client,
		iamClient: iamClient,
		stsCache:  cache,
		stsRole:   stsRoleName,
	}
}

func (c *Client) sign(uid string, request *rest.Request) (err error) {

	credential := c.stsCache.Get(uid, c.stsRole)
	if credential == nil {
		if credential, err = c.iamClient.AssumeRole(uid, c.stsRole, "", 3600); err != nil {
			return
		}
		c.stsCache.Set(uid, c.stsRole, credential)
	}

	reqURL := request.URL()
	request.SetHeader("Host", reqURL.Host)

	request.SetHeader("X-Bce-Security-Token", credential.SessionToken)

	// credential.AccessKeyId = "bb72c769133c4e3bbdb61cea2b1672a4"
	// credential.AccessKeySecret = "4de65fe6d7cb4084a3d604b99be8ebdd"
	bceAuth := auth.NewBceAuth(credential.AccessKeyId, credential.AccessKeySecret)
	sign := bceAuth.NewSigner().
		Method(request.Verb()).
		Path(reqURL.Path).
		Params(reqURL.Query()).
		Headers(request.Header()).
		Expire(3600).
		WithSignedHeader().
		GetSign()

	request.SetHeader("Authorization", sign)
	return nil
}

func (c *Client) listCFSInfos(uid string) (cfsInfos *CFSInfos, err error) {

	req := c.client.Get().
		Resource("/v1/cfs")

	if err = c.sign(uid, req); err != nil {
		return
	}

	cfsInfos = &CFSInfos{}
	err = req.Do().Into(cfsInfos)

	if err != nil {
		return nil, err
	}

	return
}

/**
获取文件系统在指定子网下的的挂载点信息
*/
func (c *Client) GetCFSInfo(uid string, cfsId string, subnetId string) (fsInfo *FileSystem, err error) {

	req := c.client.Get().
		Resource("/v1/cfs").Param("fsId", cfsId)

	if err = c.sign(uid, req); err != nil {
		return
	}

	cfsInfos := &CFSInfos{}

	err = req.Do().Into(cfsInfos)
	if err != nil {
		return nil, err
	}

	for _, mountInfo := range cfsInfos.FileSystemList[0].MountTargetList {
		if mountInfo.SubnetId == subnetId {
			fsInfo = cfsInfos.FileSystemList[0]
			fsInfo.MountTargetList = fsInfo.MountTargetList[0:1]
			fsInfo.MountTargetList[0] = mountInfo
			return
		}
	}

	err = errors.New("the cfs does not have mount point in such subnet")

	return nil, err
}

/**
 	通过Console端绑定的SubnetId筛选CFS
	一个文件系统在一个Subnet下只能有一个挂载点（mountInfo.Domain）
*/
func (c *Client) ListCFSInfosInSpecificSubnet(uid string, subnetId string) (res *FileSystemsRes, err error) {

	cfsInfo, err := c.listCFSInfos(uid)

	if err != nil {
		return
	}

	res = &FileSystemsRes{}

	res.FileSystemList = cfsInfo.FileSystemList

	return res, err

	/**
	TODO：子网匹配等VPC优化后重新开启
	*/
	// if err != nil {
	// 	return nil, err
	// }

	// res = &FileSystemsRes{}

	// fsInfos := make([]*FileSystem, 0)

	// for _, fs := range cfsInfo.FileSystemList {
	// 	for _, mountInfo := range fs.MountTargetList {
	// 		if mountInfo.SubnetId == subnetId {
	// 			fs.MountTargetList = fs.MountTargetList[0:1]
	// 			fs.MountTargetList[0] = mountInfo
	// 			fsInfos = append(fsInfos, fs)
	// 			break
	// 		}

	// 	}
	// }

	// if len(fsInfos) == 0 {
	// 	err = errors.New("no cfs statisfy the subnet condition")
	// 	return
	// }

	// res.FileSystemList = fsInfos

	// return
}
