package cfs

import (
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"gotest.tools/assert"
	"icode.baidu.com/baidu/faas/kun/pkg/bce/sts_credential"
	"icode.baidu.com/baidu/faas/kun/pkg/util/json"
)

var (
	cfsInfos = &CFSInfos{
		FileSystemList: []*FileSystem{
			{
				FsId:   "fsid",
				FsName: "mockcfs",
				VpcId:  "vpcid",
				Type:   "type",
				MountTargetList: []*MountTarget{
					{
						Domain:   "domain",
						Ovip:     "ovip",
						SubnetId: "subnetid",
					},
				},
			},
		},
	}
)

var cfsInfosJson, _ = json.Marshal(cfsInfos)

func TestListCFSInfosInSpecificSubnet(t *testing.T) {

	uid := "12345"

	credential := &sts_credential.StsCredential{
		AccessKeyId:     "accesskeyid",
		AccessKeySecret: "accesskeysecret",
		SessionToken:    "sessionToken",
		Expiration:      time.Now().Add(60 * time.Minute),
	}

	cache := sts_credential.NewLocalCredentialCache()
	cache.Set(uid, "name", credential)

	ts := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
		w.Write([]byte(cfsInfosJson))
	}))
	defer ts.Close()

	client := NewClient(ts.URL, "name", cache, nil)
	client.ListCFSInfosInSpecificSubnet(uid, "subnetid1")
	infos, _ := client.ListCFSInfosInSpecificSubnet(uid, "subnetid")

	assert.Equal(t, infos.FileSystemList[0].FsId, "fsid")
}

func TestListCFSInfosInSpecificSubnetError(t *testing.T) {

	uid := "12345"

	credential := &sts_credential.StsCredential{
		AccessKeyId:     "accesskeyid",
		AccessKeySecret: "accesskeysecret",
		SessionToken:    "sessionToken",
		Expiration:      time.Now().Add(60 * time.Minute),
	}

	cache := sts_credential.NewLocalCredentialCache()
	cache.Set(uid, "name", credential)

	ts := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
		w.Write([]byte("abc"))
	}))
	defer ts.Close()

	client := NewClient(ts.URL, "name", cache, nil)
	client.ListCFSInfosInSpecificSubnet(uid, "subnetid")
}

func TestGetMountInfo(t *testing.T) {

	uid := "12345"

	credential := &sts_credential.StsCredential{
		AccessKeyId:     "accesskeyid",
		AccessKeySecret: "accesskeysecret",
		SessionToken:    "sessionToken",
		Expiration:      time.Now().Add(60 * time.Minute),
	}

	cache := sts_credential.NewLocalCredentialCache()
	cache.Set(uid, "name", credential)

	ts := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
		w.Write([]byte(cfsInfosJson))
	}))
	defer ts.Close()

	client := NewClient(ts.URL, "name", cache, nil)
	client.GetCFSInfo(uid, "cfsid1", "subnetid")
	CFSInfo, _ := client.GetCFSInfo(uid, "cfsid", "subnetid")

	assert.Equal(t, CFSInfo.MountTargetList[0].SubnetId, "subnetid")
}
