package cfs

//CFS透传格式
type CFSInfos struct {
	NextMarker     string        `json:"nextMarker,omitempty"`
	Marker         string        `json:"marker,omitempty"`
	IsTruncated    bool          `json:"isTruncated,omitempty"`
	MaxKeys        int           `json:"maxKeys"`
	FileSystemList []*FileSystem `json:"FileSystemList"`
}

//console端响应格式
type FileSystemsRes struct {
	FileSystemList []*FileSystem `json:"FileSystemList"`
}

//文件系统信息
type FileSystem struct {
	FsId            string         `json:"fsId"`     //文件系统id
	FsName          string         `json:"fsName"`   //文件系统名称
	VpcId           string         `json:"vpcId"`    //VPC Id
	Protocol        string         `json:"protocol"` //文件系统采用的协议
	Type            string         `json:"type"`     //文件系统类型
	FsUsage         string         `json:"fsUsage"`  //文件系统已使用的大小
	Status          string         `json:"status"`
	CapacityQuota   int            `json:"capacityQuota"`
	MountTargetList []*MountTarget `json:"MountTargetList"`
}

//挂载信息
type MountTarget struct {
	AccessGroupName string `json:"accessGroupName"` //访问安全组
	MountId         string `json:"mountId"`         //挂载id
	Ovip            string `json:"ovip"`            //文件系统在该子网下的ip
	Domain          string `json:"domain"`          //文件系统在该子网下的挂载域名
	SubnetId        string `json:"subnetId"`        //子网id
}

type CFSInterface interface {
	listCFSInfos(uid string) (cfsInfo *CFSInfos, err error)
	ListCFSInfosInSpecificSubnet(uid string, subnetId string) (res *FileSystemsRes, err error)
	GetCFSInfo(uid string, cfsId string, subnetId string) (FS *FileSystem, err error)
}
