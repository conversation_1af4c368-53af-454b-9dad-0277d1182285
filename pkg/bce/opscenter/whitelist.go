package opscenter

const (
	navigationWhitelistCheckAPI = "/settings/navigation/valid"
	navigationWhitelistAddAPI   = "/settings/user/add"
	navigationWhitelistListAPI  = "/settings/user/list"
)

type navigationRequest struct {
	UserID  string `json:"userId"`
	Service string `json:"serviceType"`
}

func (c *OpsCenter) CheckUserNavigation(userid, service string) (bool, error) {
	body := navigationRequest{
		UserID:  userid,
		Service: service,
	}
	out := false
	if err := c.signedPostRequest(navigationWhitelistCheckAPI, nil, body, &out); err != nil {
		return false, err
	}
	return out, nil
}

type navigationListResponse struct {
	Services []string `json:"serviceTypes"`
}

func (c *OpsCenter) GetUserNavigationList(userid string) ([]string, error) {
	param := map[string]string{
		"userId": userid,
	}
	out := navigationListResponse{}
	if err := c.signedGetRequest(navigationWhitelistListAPI, param, nil, &out); err != nil {
		return nil, err
	}
	return out.Services, nil
}

type userSetting struct {
	ID      int    `json:"id"`
	Region  string `json:"region"`
	Product string `json:"productTag"`
	UserID  string `json:"userId"`
	Type    string `json:"type"`
	ExtData string `json:"extData"`
}

type userSettingRequest struct {
	Settings []*userSetting `json:"userSettings"`
}

func (c *OpsCenter) AddUserNavigationList(product, userid string) error {
	s := &userSetting{
		Product: product,
		UserID:  userid,
	}
	settings := userSettingRequest{
		Settings: []*userSetting{s},
	}
	if err := c.signedPostRequest(navigationWhitelistAddAPI, nil, settings, nil); err != nil {
		return err
	}
	return nil
}
