package opscenter

const (
	UserACLCheckAPI  = "/settings/acl/get"
	UserACLListAPI   = "/settings/acl/list"
	UserACLAddAPI    = "/settings/acl/user/add"
	UserACLDeleteAPI = "/settings/acl/user/delete"
)

type ACLConfig struct {
	FeatureType string `json:"featureType"`
	Region      string `json:"region"`
	AclType     string `json:"aclType"`
	AclName     string `json:"aclName"`
}

type CheckACLRequest = ACLConfig

type CheckACLResponse struct {
	IsExist bool `json:"isExist"`
}

func (c *OpsCenter) CheckUserACL(userid, featureType string) (bool, error) {
	body := CheckACLRequest{
		FeatureType: featureType,
		Region:      "bj",
		AclType:     "accountId",
		AclName:     userid,
	}
	out := new(CheckACLResponse)
	if err := c.signedPostRequest(UserACLCheckAPI, nil, body, out); err != nil {
		return false, err
	}
	return out.IsExist, nil
}

type ListACLRequest struct {
	Region  string `json:"region"`
	AclType string `json:"aclType"`
	AclName string `json:"aclName"`
}

type ListACLResponse struct {
	FeatureTypes []string `json:"featureTypes"`
}

func (c *OpsCenter) ListUserACL(userid string) ([]string, error) {
	body := ListACLRequest{
		Region:  "",
		AclType: "accountId",
		AclName: userid,
	}
	out := new(ListACLResponse)
	if err := c.signedPostRequest(UserACLListAPI, nil, body, out); err != nil {
		return nil, err
	}
	return out.FeatureTypes, nil
}

type AddACLRequest struct {
	ACLs []ACLConfig `json:"acls"`
}

func (c *OpsCenter) AddUserACL(userid, featureType string) (bool, error) {
	body := AddACLRequest{
		ACLs: []ACLConfig{
			{
				FeatureType: featureType,
				Region:      "bj",
				AclType:     "accountId",
				AclName:     userid,
			},
		},
	}

	if err := c.signedPostRequest(UserACLAddAPI, nil, body, nil); err != nil {
		return false, err
	}
	return true, nil
}

type DeleteACLRequest struct {
	ACLs []ACLConfig `json:"acls"`
}

func (c *OpsCenter) DeleteUserACL(userid, featureType string) (bool, error) {
	body := AddACLRequest{
		ACLs: []ACLConfig{
			{
				FeatureType: featureType,
				Region:      "bj",
				AclType:     "accountId",
				AclName:     userid,
			},
		},
	}

	if err := c.signedPostRequest(UserACLDeleteAPI, nil, body, nil); err != nil {
		return false, err
	}
	return true, nil
}
