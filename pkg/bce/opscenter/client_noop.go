package opscenter

import (
	"context"
)

type NoopClient struct{}

func NewNoopClient() OpsCenterInterface {
	return &NoopClient{}
}

func (NoopClient) SetRequestID(requestID string) {

}

func (NoopClient) CheckUserACL(userid, featureType string) (bool, error) {
	return true, nil
}

func (NoopClient) ListUserACL(userid string) ([]string, error) {
	return nil, nil
}

func (NoopClient) AddUserACL(userid, featureType string) (bool, error) {
	return true, nil
}

func (NoopClient) DeleteUserACL(userid, featureType string) (bool, error) {
	return true, nil
}

func (NoopClient) GetQuota(ctx context.Context, accountID, quotaType string) (int, error) {
	return 9999, nil
}

func (NoopClient) GetQuotaBatch(ctx context.Context, accountIDs []string, quotaType string) (map[string]int, error) {
	quotas := make(map[string]int)
	quotas["noopuser"] = 9999
	return quotas, nil
}

func (NoopClient) CheckUserNavigation(userid, service string) (bool, error) {
	return true, nil
}

func (NoopClient) GetUserNavigationList(userid string) ([]string, error) {
	services := []string{"CFC"}
	return services, nil
}

func (NoopClient) AddUserNavigationList(product, userid string) error {
	return nil
}
