package opscenter

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"

	"icode.baidu.com/baidu/faas/kun/pkg/bce/iam"
)

func TestGetQuota(t *testing.T) {
	t.<PERSON><PERSON>()
	iamcli, err := iam.CreateIAMClient("../iam/mock.yaml")
	if err != nil {
		t.Fatal(err)
	}
	c, err := NewOpsCenter(iamcli, "http://user-config.internal-qasandbox.baidu-int.com:8690")
	if err != nil {
		t.Fatal(err)
	}
	quota, err := c.<PERSON>uo<PERSON>(context.TODO(), "c7ac82ae14ef42d1a4ffa3b2ececa17f", "user_concurrency")
	assert.Equal(t, 0, quota)
	t.Logf("quota=%d", quota)
}

func TestGetQuotaBatch(t *testing.T) {
	t.<PERSON><PERSON>()
	iamcli, err := iam.CreateIAMClient("../iam/mock.yaml")
	if err != nil {
		t.Fatal(err)
	}
	c, err := NewOpsCenter(iamcli, "http://user-config.internal-qasandbox.baidu-int.com:8690")
	if err != nil {
		t.Fatal(err)
	}
	accountIDs := []string{"c7ac82ae14ef42d1a4ffa3b2ececa17f", "c7ac82ae14ef42d1a4ffa3b2ececa17f"}
	quotas, err := c.GetQuotaBatch(context.TODO(), accountIDs, "user_concurrency")
	assert.Equal(t, 0, len(quotas))
	t.Logf("quota=%+v", quotas)
}
