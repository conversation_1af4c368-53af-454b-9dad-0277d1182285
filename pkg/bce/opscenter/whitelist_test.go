package opscenter

import (
	"testing"

	"github.com/stretchr/testify/assert"

	"icode.baidu.com/baidu/faas/kun/pkg/bce/iam"
)

func TestUserNavigationWhiteList(t *testing.T) {
	t.Skip()
	iamcli, err := iam.CreateIAMClient("../iam/mock.yaml")
	if err != nil {
		t.Fatal(err)
	}
	client, err := NewOpsCenter(iamcli, "http://user-config.internal-qasandbox.baidu-int.com:8690")
	if err != nil {
		t.Fatal(err)
	}
	err = client.AddUserNavigationList("CFC", "00dc1b52d8354d9193536e4dd2c41ae6")
	if err != nil {
		t.Fatal(err)
	}
	services, _ := client.GetUserNavigationList("00dc1b52d8354d9193536e4dd2c41ae6")
	assert.Equal(t, 0, len(services))
	ok, _ := client.CheckUserNavigation("00dc1b52d8354d9193536e4dd2c41ae6", "CCE")
	assert.Equal(t, false, ok)
}
