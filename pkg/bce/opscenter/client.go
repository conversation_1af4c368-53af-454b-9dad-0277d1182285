package opscenter

import (
	"net/url"

	"icode.baidu.com/baidu/faas/kun/pkg/bce/auth"
	"icode.baidu.com/baidu/faas/kun/pkg/rest"
)

// OpsCenter 指“百度云前端统一接入平台”，用于和百度云整合的配额管理、白名单管理等功能
type OpsCenter struct {
	auth       *auth.BceAuth
	baseURL    *url.URL
	restClient *rest.RESTClient
	requestID  string
}

// ServiceAuthProvider 提供服务号的 ak/sk 授权
type ServiceAuthProvider interface {
	ServiceAuth() (*auth.BceAuth, error)
}

// NewOpsCenter create OpsCenter
func NewOpsCenter(provider ServiceAuthProvider, endpoint string) (OpsCenterInterface, error) {
	baseURL, err := url.Parse(endpoint)
	if err != nil {
		return nil, err
	}
	bceAuth, err := provider.ServiceAuth()
	if err != nil {
		return nil, err
	}
	config := rest.ContentConfig{
		BackendType: rest.BackendTypeBce,
	}
	restClient, err := rest.NewRESTClient(baseURL, "v1", config, nil)
	if err != nil {
		return nil, err
	}
	return &OpsCenter{
		auth:       bceAuth,
		baseURL:    baseURL,
		restClient: restClient,
	}, nil
}

// SetRequestID 设置请求的 requestID，便于问题追踪和串联
func (c *OpsCenter) SetRequestID(requestID string) {
	c.requestID = requestID
}

func (c *OpsCenter) sign(request *rest.Request) {
	sign := c.auth.NewSigner().
		Method(request.Verb()).
		Path(request.URL().Path).
		Params(request.URL().Query()).
		Headers(request.Header()).
		Expire(3600).
		WithSignedHeader().
		GetSign()
	request.SetHeader("Authorization", sign)
}

func (c *OpsCenter) signedGetRequest(path string, param map[string]string, payload interface{}, out interface{}) error {
	return c.signedRequest(c.restClient.Get(), path, param, payload, out)
}

func (c *OpsCenter) signedPostRequest(path string, param map[string]string, payload interface{}, out interface{}) error {
	return c.signedRequest(c.restClient.Post(), path, param, payload, out)
}

func (c *OpsCenter) signedRequest(request *rest.Request, path string, param map[string]string, payload interface{}, out interface{}) error {
	request.Resource(path).SetHeader("Host", c.baseURL.Host)

	if param != nil {
		for k, v := range param {
			request.Param(k, v)
		}
	}
	if payload != nil {
		request.Body(payload)
	}
	if c.requestID != "" {
		request.SetHeader("x-bce-request-id", c.requestID)
	}
	c.sign(request)

	var status int
	result := request.Do().StatusCode(&status)
	if out != nil {
		err := result.Into(out)
		if err != nil {
			return err
		}
	}
	return result.Error()
}
