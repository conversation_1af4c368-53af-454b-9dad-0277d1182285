package opscenter

import (
	"context"
	"strconv"

	//"go.opentelemetry.io/otel/api/trace"
	//"google.golang.org/grpc/codes"
)

const (
	quotaGetSingleAPI = "/settings/quota/get"
	quotaGetBatchAPI  = "/settings/quota/list_multi"
)

// 服务配额/用户配额管理
// 操作平台（沙盒）：http://opscenter.bce-sandbox.baidu.com/#/quota/service-quota/list
// 文档：http://wiki.baidu.com/pages/viewpage.action?pageId=541730245
//
// Quota白名单相关API，*代表已实现
//   (*) 1 获取单个Quota信息
//   ( ) 2 获取多个Quota信息
//   (*) 3 获取多个用户的多个Quota信息
//   ( ) 4 获取Quota信息
//   ( ) 5 更新用户Quota信息

// SingleQuotaRequest 获取单个Quota信息的请求
type SingleQuotaRequest struct {
	ServiceType string `json:"serviceType"`
	QuotaType   string `json:"quotaType"`
	UserType    string `json:"userType"`
	UserValue   string `json:"userValue"`
}

// SingleQuotaResponse 获取单个Quota信息的响应
type SingleQuotaResponse struct {
	Quota string `json:"quota"`
}

// GetQuota 该接口用于获取用户的quota信息
// POST /v1/settings/quota/get HTTP/1.1
func (c *OpsCenter) GetQuota(ctx context.Context, accountID, quotaType string) (int, error) {
	//_, span := trace.SpanFromContext(ctx).Tracer().Start(ctx, "OpsCenter/getQuota")
	//defer span.End()
	req := &SingleQuotaRequest{
		ServiceType: "CFC",
		QuotaType:   quotaType,
		UserType:    "AccountId",
		UserValue:   accountID,
	}
	resp := SingleQuotaResponse{}
	if err := c.signedPostRequest(quotaGetSingleAPI, nil, req, &resp); err != nil {
		//span.SetStatus(codes.Internal, err.Error())
		return 0, err
	}
	q, err := strconv.Atoi(resp.Quota)
	if err != nil {
		//span.SetStatus(codes.Internal, err.Error())
		return 0, err
	}
	return q, nil
}

// BatchQuotaRequest 获取单个Quota信息的请求
type BatchQuotaRequest struct {
	QuotaTypes []string `json:"quotaTypes"`
	UserType   string   `json:"userType"`
	UserValues []string `json:"userValues"`
}

// BatchQuotaResponse 获取单个Quota信息的响应
type BatchQuotaResponse struct {
	Quotas map[string]map[string]string `json:"multiUserQuotaType2quota"`
}

// GetQuotaBatch 该接口用于批量获取用户的quota信息
// POST /v1/settings/quota/list_multi HTTP/1.1
// response:
// {
//   "multiUserQuotaType2quota": {
//     "c7ac82ae14ef42d1a4ffa3b2ececa17f": {
//       "user_concurrency": "200"
//     }
//   }
// }
func (c *OpsCenter) GetQuotaBatch(ctx context.Context, accountIDs []string, quotaType string) (map[string]int, error) {
	//_, span := trace.SpanFromContext(ctx).Tracer().Start(ctx, "OpsCenter/getQuotaBatch")
	//defer span.End()
	req := &BatchQuotaRequest{
		QuotaTypes: []string{quotaType},
		UserType:   "AccountId",
		UserValues: accountIDs,
	}
	resp := BatchQuotaResponse{}

	if err := c.signedPostRequest(quotaGetBatchAPI, nil, req, &resp); err != nil {
		//span.SetStatus(codes.Internal, err.Error())
		return nil, err
	}

	quotas := map[string]int{}
	for userID, item := range resp.Quotas {
		i, err := strconv.Atoi(item[quotaType])
		if err != nil {
			continue
		}
		quotas[userID] = i
	}
	return quotas, nil
}
