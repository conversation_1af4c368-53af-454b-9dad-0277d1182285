package opscenter

import (
	"testing"

	"github.com/stretchr/testify/assert"

	"icode.baidu.com/baidu/faas/kun/pkg/bce/iam"
)

func TestCheckUserAcl(t *testing.T) {
	t.<PERSON>p()
	iamcli, err := iam.CreateIAMClient("../iam/mock.yaml")
	if err != nil {
		t.Fatal(err)
	}
	client, err := NewOpsCenter(iamcli, "http://user-config.internal-qasandbox.baidu-int.com:8690")
	if err != nil {
		t.Fatal(err)
	}
	res, _ := client.CheckUserACL("c7ac82ae14ef42d1a4ffa3b2ececa17f", "CFC@Edge")
	assert.Equal(t, false, res)
	t.Logf("res=%v", res)
	res2, _ := client.CheckUserACL("c7ac82ae14ef42d1a4ffa3b2ececa17f2", "CFC@Edge")
	assert.Equal(t, false, res2)
	t.Logf("res2=%v", res2)
	res3, err := client.AddUserACL("df391b08c64c426a81645468c75163a5", "CFC@Edge")
	if err != nil {
		t.Fatal(err)
	}
	t.Logf("res3=%v", res3)
	res31, err := client.AddUserACL("df391b08c64c426a81645468c75163a5", "edgeTest")
	if err != nil {
		t.Fatal(err)
	}
	t.Logf("res31=%v", res31)
	res4, err := client.ListUserACL("df391b08c64c426a81645468c75163a5")
	assert.Equal(t, 0, len(res4))
	t.Logf("res4=%+v", res4)
	res5, err := client.DeleteUserACL("df391b08c64c426a81645468c75163a5", "CFC@Edge")
	if err != nil {
		t.Fatal(err)
	}
	t.Logf("res5=%+v", res5)
	res6, err := client.ListUserACL("df391b08c64c426a81645468c75163a5")
	assert.Equal(t, 0, len(res6))
	t.Logf("res6=%+v", res6)
	res7, err := client.DeleteUserACL("df391b08c64c426a81645468c75163a5", "edgeTest")
	if err != nil {
		t.Fatal(err)
	}
	t.Logf("res7=%+v", res7)
}
