package opscenter

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestNewNoopClient(t *testing.T) {
	noopClient := NewNoopClient()

	assert.NotPanics(t, func() {
		noopClient.SetRequestID("")
		noopClient.CheckUserACL("", "")
		noopClient.ListUserACL("")
		noopClient.AddUserACL("", "")
		noopClient.DeleteUserACL("", "")
		noopClient.GetQuota(nil, "","")
		noopClient.GetQuotaBatch(nil, nil, "")
		noopClient.CheckUserNavigation("", "")
		noopClient.GetUserNavigationList("")
		noopClient.AddUserNavigationList("", "")
	})
}
