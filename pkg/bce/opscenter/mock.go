package opscenter

import (
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"strings"
)

func GetHttpTestServer() *httptest.Server {
	version := "/v1"

	ts := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.Write<PERSON>eader(http.StatusOK)
		var resp interface{}
		u := strings.TrimRight(r.URL.Path, version)

		switch u {
		case UserACLCheckAPI:
			resp = &CheckACLResponse{IsExist: true}
		case UserACLListAPI:
			resp = &ListACLResponse{
				FeatureTypes: make([]string, 0),
			}
		case UserACLAddAPI, UserACLDeleteAPI, navigationWhitelistAddAPI, navigationWhitelistListAPI:
		case navigationWhitelistCheckAPI:
			_, _ = w.Write([]byte("true"))
		case quotaGetSingleAPI:
			resp = &SingleQuotaResponse{
				Quota: "",
			}
		case quotaGetBatchAPI:
			_, _ = w.Write([]byte(`{
			  "multiUserQuotaType2quota": {
				"c7ac82ae14ef42d1a4ffa3b2ececa17f": {
				  "user_concurrency": "200"
				}
			  }
			}`))
		}

		if resp != nil {
			b, _ := json.Marshal(resp)
			_, _ = w.Write(b)
		}
	}))
	return ts
}
