package opscenter

import "context"
type OpsCenterInterface interface {
	<PERSON><PERSON><PERSON><PERSON><PERSON>(requestID string)
	CheckUserACL(userid, featureType string) (bool, error)
	ListUserACL(userid string) ([]string, error)
	AddUserACL(userid, featureType string) (bool, error)
	DeleteUserACL(userid, featureType string) (bool, error)
	GetQuota(ctx context.Context, accountID, quotaType string) (int, error)
	GetQuotaBatch(ctx context.Context, accountIDs []string, quotaType string) (map[string]int, error)
	CheckUserNavigation(userid, service string) (bool, error)
	GetUserNavigationList(userid string) ([]string, error)
	AddUserNavigationList(product, userid string) error
}
