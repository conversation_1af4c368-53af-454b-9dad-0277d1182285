package bcm

import "github.com/spf13/pflag"

type BcmConfiguration struct {
	EndPoint     string
	User         string
	AccessKey    string
	SecretKey    string
	Scope        string
	MaxRetry     int
	PushInterval int
}

func NewBcmConfiguration() *BcmConfiguration {
	return &BcmConfiguration{
		EndPoint:     "http://bcm.bj.baidubce.com",
		Scope:        BCMScopeCFC,
		MaxRetry:     3,
		PushInterval: 60,
	}
}

func (s *BcmConfiguration) AddUniversalFlags(fs *pflag.FlagSet) {
	fs.StringVar(&s.EndPoint, "bcm-endpoint", s.EndPoint, "BCM endpoint")
	fs.StringVar(&s.User, "bcm-user", s.User, "BCM user id")
	fs.StringVar(&s.<PERSON>ey, "bcm-ak", s.<PERSON>, "BCM ak")
	fs.StringVar(&s.<PERSON>, "bcm-sk", s<PERSON>, "BCM sk")
	fs.StringVar(&s.<PERSON>, "bcm-scope", s.<PERSON>, "BCM scope")
	fs.IntVar(&s.<PERSON>, "bcm-max-retry", s.MaxRetry, "BCM max retry times")
	fs.IntVar(&s.PushInterval, "bcm-push-interval", s.PushInterval, "bcm push interval")
}
