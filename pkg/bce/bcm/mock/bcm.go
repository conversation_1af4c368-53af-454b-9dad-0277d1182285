// Code generated by MockGen. DO NOT EDIT.
// Source: icode.baidu.com/baidu/faas/kun/pkg/bce/bcm (interfaces: Interface)

// Package mock is a generated GoMock package.
package mock

import (
	gomock "github.com/golang/mock/gomock"
	bcm "icode.baidu.com/baidu/faas/kun/pkg/bce/bcm"
	reflect "reflect"
)

// MockInterface is a mock of Interface interface
type MockInterface struct {
	ctrl     *gomock.Controller
	recorder *MockInterfaceMockRecorder
}

// MockInterfaceMockRecorder is the mock recorder for MockInterface
type MockInterfaceMockRecorder struct {
	mock *MockInterface
}

// NewMockInterface creates a new mock instance
func NewMockInterface(ctrl *gomock.Controller) *MockInterface {
	mock := &MockInterface{ctrl: ctrl}
	mock.recorder = &MockInterfaceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use
func (m *MockInterface) EXPECT() *MockInterfaceMockRecorder {
	return m.recorder
}

// PushMultiUserMetricData mocks base method
func (m *MockInterface) PushMultiUserMetricData(arg0 *bcm.MultiUserMetricDataRequest) error {
	ret := m.ctrl.Call(m, "PushMultiUserMetricData", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}
func (m *MockInterface) PushUserMetricData(arg0 *bcm.UserMetricData) error {
	ret := m.ctrl.Call(m, "PushMultiUserMetricData", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// PushMultiUserMetricData indicates an expected call of PushMultiUserMetricData
func (mr *MockInterfaceMockRecorder) PushMultiUserMetricData(arg0 interface{}) *gomock.Call {
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PushMultiUserMetricData", reflect.TypeOf((*MockInterface)(nil).PushMultiUserMetricData), arg0)
}

func (mr *MockInterfaceMockRecorder) PushUserMetricData(arg0 interface{}) *gomock.Call {
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PushMultiUserMetricData", reflect.TypeOf((*MockInterface)(nil).PushUserMetricData), arg0)
}
