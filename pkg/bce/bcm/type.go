package bcm

import (
	"time"

	"icode.baidu.com/baidu/faas/kun/pkg/util/json"
)

const (
	BCMScopeCFC   = "BCE_CFC"
	BCMScopeCBD   = "BCE_CBD"
	BCMTimeFormat = "2006-01-02T15:04:05Z"

	// 每个 bcm request body 里包含的 metricDatum 数量
	// 一个 metricDatum 约占 350 byte，bcm body 上限设为 32768 byte，那么 body 最多有 32768/350 = 93个 metricDatum
	MAX_DATUM_NUM = 80
)

const (
	CBDFunctionInvocationCount         = "FunctionInvocationCount"
	CBDFunctionResourceOccupationBytes = "FunctionResourceOccupationBytes"
)

type MultiUserMetricDataRequest struct {
	MultiUserMetricData []UserMetricData `json:"multiUserMetricData"`
}

type UserMetricData struct {
	Scope      string        `json:"scope,omitempty"`
	UserID     string        `json:"userId,omitempty"`
	MetricData []MetricDatum `json:"metricData"`
}

type MetricDatum struct {
	MetricName      string           `json:"metricName"`
	Dimensions      *[]DimensionType `json:"dimensions"`
	Value           uint64           `json:"value,omitempty"`
	StatisticValues *Statistic       `json:"statisticValues,omitempty"`
	Timestamp       time.Time        `json:"-"`
	BcmTimestamp    string           `json:"timestamp"`
}

type DimensionType struct {
	Name  string `json:"name"`
	Value string `json:"value"`
}

type Statistic struct {
	Average     float64 `json:"average,omitempty"`
	Maximum     float64 `json:"maximum,omitempty"`
	Minimum     float64 `json:"minimum,omitempty"`
	SampleCount int     `json:"sampleCount,omitempty"`
	Sum         float64 `json:"sum,omitempty"`
	Timestamp   string  `json:"timestamp,omitempty"`
}

// BCM 推送参数需要为UTC无毫秒时间戳
func (m *MetricDatum) MarshalJSON() ([]byte, error) {
	m.BcmTimestamp = m.Timestamp.UTC().Format(BCMTimeFormat)
	return json.Marshal(*m)
}
