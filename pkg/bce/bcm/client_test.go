package bcm

import (
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/spf13/pflag"
	"github.com/stretchr/testify/assert"
)

func TestPushMultiUserMetricData(t *testing.T) {
	ts := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		t.Log(r.URL)
		w.Write<PERSON>eader(http.StatusOK)
		w.Write([]byte("json result"))
	}))

	opt := NewBcmConfiguration()
	opt.AddUniversalFlags(pflag.CommandLine)
	opt.EndPoint = ts.URL
	opt.Scope = BCMScopeCFC

	c := NewClient(opt)

	multiUserReq := &MultiUserMetricDataRequest{
		MultiUserMetricData: []UserMetricData{
			{
				Scope:  BCMScopeCFC,
				UserID: "abc",
				MetricData: []MetricDatum{
					{
						MetricName: "invocation_count",
						Dimensions: nil,
						StatisticValues: &Statistic{
							SampleCount: 5,
							Sum:         6,
						},
					},
				},
			},
		},
	}

	err := c.PushMultiUserMetricData(multiUserReq)
	assert.Nil(t, err)

	singleUserReq := &UserMetricData{
		Scope:  BCMScopeCBD,
		UserID: "abc",
		MetricData: []MetricDatum{
			{
				MetricName: "invocation_count",
				Dimensions: nil,
				Value:      123,
			},
		},
	}

	err = c.PushUserMetricData(singleUserReq)
	assert.Nil(t, err)
}
