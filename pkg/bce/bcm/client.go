package bcm

import (
	"fmt"
	"net/url"
	"strings"
	"time"

	"github.com/google/uuid"
	"go.uber.org/zap"

	"icode.baidu.com/baidu/faas/kun/pkg/api"
	"icode.baidu.com/baidu/faas/kun/pkg/bce/auth"
	"icode.baidu.com/baidu/faas/kun/pkg/rest"
	"icode.baidu.com/baidu/faas/kun/pkg/util/logs"
)

type Interface interface {
	PushMultiUserMetricData(*MultiUserMetricDataRequest) error
	PushUserMetricData(data *UserMetricData) (err error)
}

type Client struct {
	client   *rest.RESTClient
	ak       string
	sk       string
	scope    string
	maxRetry int
}

// NewClient create a bcm client
func NewClient(opt *BcmConfiguration) *Client {
	config := rest.ContentConfig{
		BackendType: rest.BackendTypeBce,
	}
	baseURL, _ := url.Parse("http://" + strings.Replace(opt.EndPoint, "http://", "", 1))
	client, _ := rest.NewRESTClient(baseURL, "", config, nil)
	return &Client{
		client:   client,
		ak:       opt.AccessKey,
		sk:       opt.SecretKey,
		scope:    opt.Scope,
		maxRetry: opt.MaxRetry,
	}
}

// PushMultiUserMetricData xxx
func (c *Client) PushMultiUserMetricData(m *MultiUserMetricDataRequest) (err error) {
	for currRetry := 0; currRetry < c.maxRetry; currRetry++ {
		time.Sleep(time.Duration(currRetry) * 5 * time.Second)
		reqID := uuid.New().String()

		req := c.client.Post().
			Resource(fmt.Sprintf("/json-api/v1/metricdata/%s", c.scope)).
			SetHeader(api.HeaderXRequestID, reqID).
			Body(m)

		if err = c.sign(req); err != nil {
			continue
		}

		if err = req.Do().Error(); err != nil {
			logs.Error("push multi user data to bcm failed", zap.String("error", err.Error()), zap.String("request_id", reqID))
			continue
		}
		break
	}
	return err
}

// PushUserMetricData xxx
func (c *Client) PushUserMetricData(data *UserMetricData) (err error) {
	userID, scope := data.UserID, data.Scope
	data.UserID, data.Scope = "", ""
	for currRetry := 0; currRetry < c.maxRetry; currRetry++ {
		time.Sleep(time.Duration(currRetry) * 5 * time.Second)
		reqID := uuid.New().String()

		req := c.client.Post().
			Resource(fmt.Sprintf("/json-api/v1/metricdata/%s/%s", userID, scope)).
			SetHeader(api.HeaderXRequestID, reqID).Body(data)
		// fmt.Print(data.MetricData[0].BcmTimestamp)
		if err = c.sign(req); err != nil {
			continue
		}
		result := req.Do()
		// result.Print() // to debug
		if err := result.Error(); err != nil {
			logs.Error("push data to bcm failed", zap.String("error", err.Error()), zap.String("request_id", reqID))
			continue
		}
		logs.Info("report to bcm success!")
		break
	}
	return err
}

func (c *Client) sign(request *rest.Request) (err error) {
	reqURL := request.URL()
	request.SetHeader("Host", reqURL.Host)
	request.SetHeader("Content-Type", "application/json;charset=UTF-8")

	bceAuth := auth.NewBceAuth(c.ak, c.sk)
	sign := bceAuth.NewSigner().
		Method(request.Verb()).
		Path(reqURL.Path).
		Params(reqURL.Query()).
		Headers(request.Header()).
		Expire(3600).
		WithSignedHeader().
		GetSign()

	request.SetHeader("Authorization", sign)
	return nil
}
