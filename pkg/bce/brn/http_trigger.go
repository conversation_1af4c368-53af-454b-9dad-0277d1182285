package brn

import (
	"fmt"
)

type HTTPTriggerBRN struct {
	BRN
}

func GenerateHTTPTriggerBrn(region, uid, apiid, stage, verb, path string) HTTPTriggerBRN {
	// 一般path都是从/开始的，这样BRN里面会有出现连续两个//，所以去掉一个，好看一点
	if path[0] == '/' {
		path = path[1:]
	}

	resource := fmt.Sprintf("%s/%s/%s/%s", Md5BceUid(apiid), stage, verb, path)

	b := HTTPTriggerBRN{
		BRN: BRN{
			Partition: "bce",
			Service:   "cfc-http-trigger",
			Region:    region,
			AccountID: Md5BceUid(uid),
			Resource:  resource,
		},
	}

	return b
}
