//go:build go1.7
// +build go1.7

package brn

import (
	"errors"
	"fmt"
	"testing"
)

func TestParseBRN(t *testing.T) {
	cases := []struct {
		input string
		brn   BRN
		err   error
	}{
		{
			input: "invalid",
			err:   errors.New(invalidPrefix),
		},
		{
			input: "brn:nope",
			err:   errors.New(invalidSections),
		},
		{
			input: "brn:bce:ecr:us-west-2:************:repository/foo/bar",
			brn: BRN{
				Partition: "bce",
				Service:   "ecr",
				Region:    "us-west-2",
				AccountID: "************",
				Resource:  "repository/foo/bar",
			},
		},
		{
			input: "brn:bce:elasticbeanstalk:us-east-1:************:environment/My App/MyEnvironment",
			brn: BRN{
				Partition: "bce",
				Service:   "elasticbeanstalk",
				Region:    "us-east-1",
				AccountID: "************",
				Resource:  "environment/My App/MyEnvironment",
			},
		},
		{
			input: "brn:bce:iam::************:user/David",
			brn: BRN{
				Partition: "bce",
				Service:   "iam",
				Region:    "",
				AccountID: "************",
				Resource:  "user/David",
			},
		},
		{
			input: "brn:bce:rds:eu-west-1:************:db:mysql-db",
			brn: BRN{
				Partition: "bce",
				Service:   "rds",
				Region:    "eu-west-1",
				AccountID: "************",
				Resource:  "db:mysql-db",
			},
		},
		{
			input: "brn:bce:s3:::my_corporate_bucket/exampleobject.png",
			brn: BRN{
				Partition: "bce",
				Service:   "s3",
				Region:    "",
				AccountID: "",
				Resource:  "my_corporate_bucket/exampleobject.png",
			},
		},
		{
			input: "brn:bce:faas:bj:16:function:test-h:2",
			brn: BRN{
				Partition: "bce",
				Service:   "faas",
				Region:    "bj",
				AccountID: "16",
				Resource:  "function:test-h:2",
			},
		},
	}
	for _, tc := range cases {
		t.Run(tc.input, func(t *testing.T) {
			spec, err := Parse(tc.input)
			if tc.brn != spec {
				t.Errorf("Expected %q to parse as %v, but got %v", tc.input, tc.brn, spec)
			}
			if err == nil && tc.err != nil {
				t.Errorf("Expected err to be %v, but got nil", tc.err)
			} else if err != nil && tc.err == nil {
				t.Errorf("Expected err to be nil, but got %v", err)
			} else if err != nil && tc.err != nil && err.Error() != tc.err.Error() {
				t.Errorf("Expected err to be %v, but got %v", tc.err, err)
			}
		})
	}
}

// TestMD5 测试函数，用于生成MD5字符串并打印出来
// 参数t：*testing.T类型，表示当前的测试对象
func TestMD5(t *testing.T) {
	uid := "42f6fbc2cd374bfcb80d9967370fd8ff"
	brnStr := Md5BceUid(uid)
	fmt.Println(brnStr)

	functionBRN, err := Parse("test_invoke_81818")
	fmt.Println(err)
	fmt.Println(functionBRN.String())
}
