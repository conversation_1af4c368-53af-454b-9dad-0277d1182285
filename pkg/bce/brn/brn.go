// Package brn provides a parser for interacting with BCE Resource Names.
package brn

import (
	"crypto/md5"
	"errors"
	"fmt"
	"io"
	"strings"
)

const (
	brnDelimiter = ":"
	brnSections  = 6
	brnPrefix    = "brn:"

	// zero-indexed
	sectionPartition = 1
	sectionService   = 2
	sectionRegion    = 3
	sectionAccountID = 4
	sectionResource  = 5

	// errors
	invalidPrefix   = "brn: invalid prefix"
	invalidSections = "brn: not enough sections"
)

// BRN captures the individual fields of an BCE Resource Name.
// See http://docs.aws.amazon.com/general/latest/gr/aws-arns-and-namespaces.html for more information.
type BRN struct {
	// The partition that the resource is in. For standard BCE regions, the partition is "bce". If you have resources in
	// other partitions, the partition is "bce-partitionname". For example, the partition for resources in the China
	// (Beijing) region is "bce-cn".
	Partition string

	// The service namespace that identifies the BCE product. For a list of namespaces, see
	// http://docs.aws.amazon.com/general/latest/gr/aws-arns-and-namespaces.html#genref-aws-service-namespaces.
	Service string

	// The region the resource resides in. Note that the BRNs for some resources do not require a region, so this
	// component might be omitted.
	Region string

	// The ID of the BCE account that owns the resource, without the hyphens. For example, ************. Note that the
	// BRNs for some resources don't require an account number, so this component might be omitted.
	AccountID string

	// The content of this part of the BRN varies by service. It often includes an indicator of the type of resource —
	// for example, an IAM user or BCE RDS database - followed by a slash (/) or a colon (:), followed by the
	// resource name itself. Some services allows paths for resource names, as described in
	// http://docs.aws.amazon.com/general/latest/gr/aws-arns-and-namespaces.html#arns-paths.
	Resource string
}

// Parse parses an BRN into its constituent parts.
//
// Some example BRNs:
// brn:bce:elasticbeanstalk:us-east-1:************:environment/My App/MyEnvironment
// brn:bce:iam::************:user/David
// brn:bce:rds:eu-west-1:************:db:mysql-db
// brn:bce:s3:::my_corporate_bucket/exampleobject.png
func Parse(brn string) (BRN, error) {
	if !strings.HasPrefix(brn, brnPrefix) {
		return BRN{}, errors.New(invalidPrefix)
	}
	sections := strings.SplitN(brn, brnDelimiter, brnSections)
	if len(sections) != brnSections {
		return BRN{}, errors.New(invalidSections)
	}
	return BRN{
		Partition: sections[sectionPartition],
		Service:   sections[sectionService],
		Region:    sections[sectionRegion],
		AccountID: sections[sectionAccountID],
		Resource:  sections[sectionResource],
	}, nil
}

// String returns the canonical representation of the BRN
func (brn BRN) String() string {
	return brnPrefix +
		brn.Partition + brnDelimiter +
		brn.Service + brnDelimiter +
		brn.Region + brnDelimiter +
		brn.AccountID + brnDelimiter +
		brn.Resource
}

func GenerateCommonBrn(service, region, uid, resource, qualifier string) string {
	if len(qualifier) > 0 {
		resource = fmt.Sprintf("%s:%s", resource, qualifier)
	}

	b := &BRN{
		Partition: "bce",
		Service:   service,
		Region:    region,
		AccountID: Md5BceUid(uid),
		Resource:  resource,
	}

	return b.String()
}

func Md5BceUid(uid string) string {
	h := md5.New()
	io.WriteString(h, uid+"bce-cfc-2017")
	return fmt.Sprintf("%x", h.Sum(nil))
}
