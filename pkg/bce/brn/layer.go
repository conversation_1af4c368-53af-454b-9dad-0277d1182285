package brn

import (
	"errors"
	"fmt"
	"regexp"
	"strconv"
	"strings"
)

var (
	regLayerStr          = `^brn:bce:cfc:([a-z]{2,5}[0-9]*):[0-9a-z]{32}:layer:[a-zA-Z0-9-_]+(:[0-9]+)?$`
	regLayerBrn          = regexp.MustCompile(regLayerStr)
	layerBrnNotMatchErr  = errors.New(fmt.Sprintf("Member must satisfy regular expression pattern:%s", regLayerStr))
	validateLayerNameErr = errors.New("LayerName must have length 1 to 140")
	regLayerNameStr      = `^[a-zA-Z0-9-_]+$`
	regLayerName         = regexp.MustCompile(regLayerNameStr)
	layerNameNotMatchErr = errors.New(fmt.Sprintf("Member must satisfy regular expression pattern: (brn:bce:cfc:([a-z]{2,5}[0-9]*):[0-9a-z]{32}:layer:[a-zA-Z0-9-_]+)|[a-zA-Z0-9-_]+"))
)

type LayerBRN struct {
	BRN
	LayerName    string
	LayerVersion int64
}

func (brn LayerBRN) ShortBrn() string {
	resource := fmt.Sprintf("layer:%s", brn.LayerName)
	return brnPrefix +
		brn.Partition + brnDelimiter +
		brn.Service + brnDelimiter +
		brn.Region + brnDelimiter +
		brn.AccountID + brnDelimiter +
		resource
}

func GenerateLayerBrn(region, uid, layerName string) LayerBRN {
	resource := fmt.Sprintf("layer:%s", layerName)
	b := LayerBRN{
		BRN: BRN{
			Partition: "bce",
			Service:   "cfc",
			Region:    region,
			AccountID: Md5BceUid(uid),
			Resource:  resource,
		},
	}
	return b
}

func GenerateLayerVersionBrn(region, uid, layerName string, version int64) LayerBRN {
	resource := fmt.Sprintf("layer:%s:%d", layerName, version)
	b := LayerBRN{
		BRN: BRN{
			Partition: "bce",
			Service:   "cfc",
			Region:    region,
			AccountID: Md5BceUid(uid),
			Resource:  resource,
		},
		LayerName:    layerName,
		LayerVersion: version,
	}
	return b
}

// layer brn 只有完整BRN 不存在变种BRN
func ParseLayerBrn(brn string) (LayerBRN, error) {
	commonBrn, err := Parse(brn)
	if err != nil {
		return LayerBRN{}, err
	}
	if regLayerBrn.MatchString(brn) {
		parts := strings.SplitN(commonBrn.Resource, ":", 3)
		partsLen := len(parts)
		if partsLen < 2 {
			return LayerBRN{}, layerBrnNotMatchErr
		}
		var version int64 = 1
		if partsLen == 3 {
			version, _ = strconv.ParseInt(parts[2], 10, 64)
		}
		b := LayerBRN{
			BRN:          commonBrn,
			LayerName:    parts[1],
			LayerVersion: version,
		}
		return b, nil
	} else {
		return LayerBRN{}, layerBrnNotMatchErr
	}
}

func GetLayerName(str string) (string, error) {
	if str == "" || len(str) > 140 {
		return "", validateLayerNameErr
	}
	if !regLayerName.MatchString(str) && !regLayerBrn.MatchString(str) {
		return "", layerNameNotMatchErr
	}
	if strings.ContainsAny(str, ":") {
		brn, err := ParseLayerBrn(str)
		if err != nil {
			return "", err
		}
		return brn.LayerName, nil
	}
	return str, nil
}
