package brn

import (
	"errors"
	"testing"
)

func TestParseLayerBrn(t *testing.T) {
	cases := []struct {
		input string
		brn   LayerBRN
		err   error
	}{
		{
			input: "invalid",
			err:   errors.New(invalidPrefix),
		},
		{
			input: "brn:nope",
			err:   errors.New(invalidSections),
		},
		{
			input: "brn:bce:cfc:bj:8b58557f5a6828e45ea445919a394754:layer:layerName:1",
			brn: LayerBRN{
				BRN: BRN{
					Partition: "bce",
					Service:   "cfc",
					Region:    "bj",
					AccountID: "8b58557f5a6828e45ea445919a394754",
					Resource:  "layer:layerName:1",
				},
				LayerName:    "layerName",
				LayerVersion: 1,
			},
		},
		{
			input: "brn:bce:cfc:bj:8b58557f5a6828e45ea445919a394754:layer:layerName",
			brn: LayerBRN{
				BRN: BRN{
					Partition: "bce",
					Service:   "cfc",
					Region:    "bj",
					AccountID: "8b58557f5a6828e45ea445919a394754",
					Resource:  "layer:layerName",
				},
				LayerName:    "layerName",
				LayerVersion: 1,
			},
		},
		{
			input: "brn:bce:cfc:bj:8b58557f5a6828e4:layer:layerName:1",
			brn: LayerBRN{
				BRN: BRN{
					Partition: "bce",
					Service:   "cfc",
					Region:    "bj",
					AccountID: "8b58557f5a6828e45ea445919a394754",
					Resource:  "layer:layerName:1",
				},
				LayerName:    "layerName",
				LayerVersion: 1,
			},
			err: layerBrnNotMatchErr,
		},
		{
			input: "brn:bce:cfc:bj-gz-su:8b58557f5a6828e45ea445919a394754:layer:layerName:1",
			brn: LayerBRN{
				BRN: BRN{
					Partition: "bce",
					Service:   "cfc",
					Region:    "bj",
					AccountID: "8b58557f5a6828e45ea445919a394754",
					Resource:  "layer:layerName:1",
				},
				LayerName:    "layerName",
				LayerVersion: 1,
			},
			err: layerBrnNotMatchErr,
		},
	}
	for _, tc := range cases {
		t.Run(tc.input, func(t *testing.T) {
			spec, err := ParseLayerBrn(tc.input)
			if err == nil && tc.err != nil {
				t.Errorf("Expected err to be %v, but got nil", tc.err)
			} else if err != nil && tc.err == nil {
				t.Errorf("Expected err to be nil, but got %v", err)
			} else if err != nil && tc.err != nil && err.Error() != tc.err.Error() {
				t.Errorf("Expected err to be %v, but got %v", tc.err, err)
			}
			if err == nil && tc.brn != spec {
				t.Errorf("Expected %q to parse as %v, but got %v", tc.input, tc.brn, spec)
			}
		})
	}
}

func TestGetLayerName(t *testing.T) {
	cases := []struct {
		input  string
		output string
		err    error
	}{
		{
			input: "",
			err:   validateLayerNameErr,
		},
		{
			input: "brn:nope",
			err:   layerNameNotMatchErr,
		},
		{
			input:  "brn:bce:cfc:bj:8b58557f5a6828e45ea445919a394754:layer:layerName:1",
			output: "layerName",
		},
		{
			input:  "brn:bce:cfc:bj:-=-=--=--=-=--:layer:layerName:1",
			output: "layerName",
			err:    layerNameNotMatchErr,
		},
	}
	for _, tc := range cases {
		t.Run(tc.input, func(t *testing.T) {
			spec, err := GetLayerName(tc.input)
			if err == nil && tc.err != nil {
				t.Errorf("Expected err to be %v, but got nil", tc.err)
			} else if err != nil && tc.err == nil {
				t.Errorf("Expected err to be nil, but got %v", err)
			} else if err != nil && tc.err != nil && err.Error() != tc.err.Error() {
				t.Errorf("Expected err to be %v, but got %v", tc.err, err)
			}
			if err == nil && tc.output != spec {
				t.Errorf("Expected %q to parse as %v, but got %v", tc.input, tc.output, spec)
			}
		})
	}
}
