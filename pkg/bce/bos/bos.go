package bos

import (
	"github.com/baidubce/bce-sdk-go/auth"
	bosSdk "github.com/baidubce/bce-sdk-go/services/bos"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/global"
	"icode.baidu.com/baidu/faas/kun/pkg/server"
)

// NewClient sts方式访问用户的bucket
func NewClient(c *server.Context) (*bosSdk.Client, error) {
	credential, err := global.GetCredential(c)
	if err != nil {
		return nil, err
	}

	cli, err := bosSdk.NewClient(credential.AccessKeyId, credential.AccessKeySecret, global.AC.Config.BosEndpoint)
	if err != nil {
		return nil, err
	}

	stsCredential, err := auth.NewSessionBceCredentials(
		credential.AccessKeyId,
		credential.AccessKeySecret,
		credential.SessionToken)

	if err != nil {
		return nil, err
	}

	cli.Config.Credentials = stsCredential
	return cli, nil
}
