package vpc

type mockClient struct{}

func MockClient() *mockClient { return &mockClient{} }

func (mc mockClient) GetVpc(string, string) (*GetVpcRes, error) { return nil, nil }

func (mc mockClient) GetSubnet(string, string) (*GetSubnetRes, error) {

	mc.GetVpc("", "")

	return &GetSubnetRes{
		Subnet: SubnetInfo{
			VpcID:    "abc",
			Name:     "abc",
			SubnetId: "abc",
		},
	}, nil
}

func (mc mockClient) ListSecGroups(string, string, string) (*ListSecGroupRes, error) {

	return &ListSecGroupRes{
		SecurityGroups: []SecurityGroupInfo{
			{
				ID:   "abc",
				Name: "abc",
			},
		},
	}, nil
}
