package vpc

import (
	"fmt"
	"net/url"
	"strings"

	"icode.baidu.com/baidu/faas/kun/pkg/bce/auth"
	"icode.baidu.com/baidu/faas/kun/pkg/bce/iam"
	"icode.baidu.com/baidu/faas/kun/pkg/bce/sts_credential"
	"icode.baidu.com/baidu/faas/kun/pkg/rest"
)

// Client xxx
type Client struct {
	client    *rest.RESTClient
	iamClient iam.ClientInterface
	stsRole   string
	stsCache  sts_credential.Cache
}

// NewClient xxx
func NewClient(endpoint, stsRoleName string, cache sts_credential.Cache, iamClient iam.ClientInterface) *Client {
	config := rest.ContentConfig{
		BackendType: rest.BackendTypeBce,
	}

	baseURL, _ := url.Parse("http://" + strings.Replace(endpoint, "http://", "", 1))
	client, _ := rest.NewRESTClient(baseURL, "", config, nil)
	return &Client{
		client:    client,
		iamClient: iamClient,
		stsCache:  cache,
		stsRole:   stsRoleName,
	}
}

func (c *Client) sign(uid string, request *rest.Request) (err error) {
	credential := c.stsCache.Get(uid, c.stsRole)
	if credential == nil {
		if credential, err = c.iamClient.AssumeRole(uid, c.stsRole, "", 3600); err != nil {
			return
		}
		c.stsCache.Set(uid, c.stsRole, credential)
	}

	reqURL := request.URL()
	request.SetHeader("Host", reqURL.Host)
	request.SetHeader("X-Bce-Security-Token", credential.SessionToken)

	bceAuth := auth.NewBceAuth(credential.AccessKeyId, credential.AccessKeySecret)
	sign := bceAuth.NewSigner().
		Method(request.Verb()).
		Path(reqURL.Path).
		Params(reqURL.Query()).
		Headers(request.Header()).
		Expire(3600).
		WithSignedHeader().
		GetSign()

	request.SetHeader("Authorization", sign)
	return nil
}

// GetVpc xxx
func (c *Client) GetVpc(uid, vpcID string) (vpc *GetVpcRes, err error) {
	req := c.client.Get().
		Resource(fmt.Sprintf("/v1/vpc/%s", vpcID))

	if err = c.sign(uid, req); err != nil {
		return
	}

	vpc = &GetVpcRes{}
	err = req.Do().Into(vpc)
	return
}

// GetSubset xxx
func (c *Client) GetSubnet(uid, subnetID string) (sbn *GetSubnetRes, err error) {
	req := c.client.Get().
		Resource(fmt.Sprintf("/v1/subnet/%s", subnetID))

	if err = c.sign(uid, req); err != nil {
		return
	}

	sbn = &GetSubnetRes{}
	err = req.Do().Into(sbn)
	return
}

// ListSecGroups xxx
func (c *Client) ListSecGroups(uid, vpcID, marker string) (secGroups *ListSecGroupRes, err error) {
	req := c.client.Get().
		Resource("/v2/securityGroup")

	req.Param("vpcId", vpcID)
	if marker != "" {
		req.Param("marker", marker)
	}

	if err = c.sign(uid, req); err != nil {
		return
	}

	secGroups = &ListSecGroupRes{}
	err = req.Do().Into(secGroups)
	return
}
