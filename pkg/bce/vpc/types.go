package vpc

type Interface interface {
	GetVpc(string, string) (*GetVpcRes, error)
	GetSubnet(string, string) (*GetSubnetRes, error)
	ListSecGroups(string, string, string) (*ListSecGroupRes, error)
}

type GetVpcRes struct {
	Vpc VpcInfo `json:"vpc"`
}

type VpcInfo struct {
	VpcID string `json:"vpcId"`
	Cidr  string `json:"cidr"`
}

type GetSubnetRes struct {
	Subnet SubnetInfo `json:"subnet"`
}

type SubnetInfo struct {
	Name     string `json:"name"`
	SubnetId string `json:"subnetId"`
	VpcID    string `json:"vpcId"`
}

type ListSecGroupRes struct {
	NextMarker     string              `json:"nextMarker"`
	Marker         string              `json:"marker"`
	IsTruncated    bool                `json:"isTruncated"`
	MaxKeys        int                 `json:"maxKeys"`
	SecurityGroups []SecurityGroupInfo `json:"securityGroups"`
}

type SecurityGroupInfo struct {
	ID   string `json:"id"`
	Name string `json:"name"`
}
