package cdn

type mockClient struct{}

func MockClient() *mockClient { return &mockClient{} }

func (m *mockClient) ListDomains(r *RequestInfo, s string) (*GetDomainsOutput, error) {
	return &GetDomainsOutput{
		Domains: []DomainInfo{
			{Name: "www.a.com"},
			{Name: "www.b.com"},
		},
		IsTruncated: false,
	}, nil
}

func (m *mockClient) GetNotifications(r *RequestInfo, s string) (*[]Notification, error) {
	return &[]Notification{
		{
			UUID:        "123",
			Domains:     []string{"www.a.com"},
			EventType:   "LogFileCreated",
			FunctionBrn: "brn:bce:cfc:bj:8b1082e5027349262a218261c7a4e001:function:mock:$LATEST",
			Remark:      "remark",
			Starter:     "ON",
		},
	}, nil
}

func (m *mockClient) GetEdgeTriggers(r *RequestInfo, input *GetEdgeTriggersInput) (*[]EdgeTrigger, error) {
	return &[]EdgeTrigger{
		{
			UUID:        "123",
			Domain:      "www.a.com",
			Path:        "/images",
			EventType:   "viewer-request",
			FunctionBrn: "brn:bce:cfc:bj:8b1082e5027349262a218261c7a4e001:function:mock:$LATEST",
		},
	}, nil

}

func (m *mockClient) CreateEdgeTrigger(r *RequestInfo, e *EdgeTrigger) (*EdgeTrigger, error) {
	return &EdgeTrigger{
		UUID:        "123",
		Domain:      e.Domain,
		Path:        e.Path,
		EventType:   e.EventType,
		FunctionBrn: e.FunctionBrn,
	}, nil
}

func (m *mockClient) DeleteNotification(r *RequestInfo, s string) error        { return nil }
func (m *mockClient) CreateNotification(r *RequestInfo, n *Notification) error { return nil }
func (m *mockClient) UpdateNotification(r *RequestInfo, n *Notification) error { return nil }
func (m *mockClient) DeleteEdgeTrigger(r *RequestInfo, u string) error         { return nil }
func (m *mockClient) UpdateEdgeTrigger(r *RequestInfo, e *EdgeTrigger) error   { return nil }
func (m *mockClient) DeleteEdgeTriggers(info *RequestInfo, input *DeleteEdgeTriggersInput) (err error) {
	return nil
}
