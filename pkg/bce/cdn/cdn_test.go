package cdn

import (
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"

	"icode.baidu.com/baidu/faas/kun/pkg/api"
	"icode.baidu.com/baidu/faas/kun/pkg/bce/sts_credential"
	"icode.baidu.com/baidu/faas/kun/pkg/util/json"
)

var ntfsResponse = []Notification{
	{
		UUID:        "b7127a1e-4700-4172-a5ee-7c21b7bf087b",
		Domains:     []string{"www.a.com", "www.b.com"},
		EventType:   "CachedObjectsPushed",
		FunctionBrn: "brn:bce:cfc:bj:640c8817bd1d:function:http_297:$LATEST",
		Remark:      "remark",
		Starter:     "ON",
	},
	{
		UUID:        "b7127a1e-4700-4172-a5ee-7c21b7bf087b",
		Domains:     []string{"www.c.com", "www.d.com"},
		EventType:   "LogFileCreated",
		FunctionBrn: "brn:bce:cfc:bj:640c8817bd1d:function:http_297:$LATEST",
		Remark:      "remark",
		Starter:     "OFF",
	},
}

var domainsResponse = GetDomainsOutput{
	Domains: []DomainInfo{
		{Name: "www.a.com"},
		{Name: "www.b.com"},
	},
	IsTruncated: false,
}

var ntfsResponseJson, _ = json.Marshal(ntfsResponse)
var domainsResponseJson, _ = json.Marshal(domainsResponse)

func TestCreateNotification(t *testing.T) {
	info, ntf, cache := prepare()
	ts := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
	}))
	defer ts.Close()

	cli := NewClient(ts.URL, api.StsRoleName, cache, nil)
	err := cli.CreateNotification(info, ntf)
	assert.Equal(t, err, nil)
}

func TestGetNotification(t *testing.T) {
	info, _, cache := prepare()

	ts := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
		w.Write([]byte(ntfsResponseJson))
	}))
	defer ts.Close()

	cli := NewClient(ts.URL, api.StsRoleName, cache, nil)
	ntfs, err := cli.GetNotifications(info, "brn:bce:cfc:bj:640c8817bd1d:function:http_297:$LATEST")

	assert.Equal(t, err, nil)
	assert.Equal(t, *ntfs, ntfsResponse)
}

func TestGetDomains(t *testing.T) {
	info, _, cache := prepare()

	ts := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
		w.Write([]byte(domainsResponseJson))
	}))
	defer ts.Close()

	cli := NewClient(ts.URL, api.StsRoleName, cache, nil)
	domains, err := cli.ListDomains(info, "marker")

	assert.Equal(t, err, nil)
	assert.Equal(t, *domains, domainsResponse)
}

func TestUpdateNotification(t *testing.T) {
	info, ntf, cache := prepare()
	ts := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
	}))
	defer ts.Close()

	cli := NewClient(ts.URL, api.StsRoleName, cache, nil)
	err := cli.UpdateNotification(info, ntf)
	assert.Equal(t, err, nil)
}

func TestDeleteNotification(t *testing.T) {
	info, _, cache := prepare()
	ts := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
	}))
	defer ts.Close()

	cli := NewClient(ts.URL, api.StsRoleName, cache, nil)
	err := cli.DeleteNotification(info, "b7127a1e-4700-4172-a5ee-7c21b7bf087b")
	assert.Equal(t, err, nil)
}

func prepare() (*RequestInfo, *Notification, sts_credential.Cache) {
	uid := "12345"
	info := NewRequestInfo(uid, "requestID_123456")

	credential := &sts_credential.StsCredential{
		AccessKeyId:     "accesskeyid",
		AccessKeySecret: "accesskeysecret",
		SessionToken:    "sessionToken",
		Expiration:      time.Now().Add(60 * time.Minute),
	}

	cache := sts_credential.NewLocalCredentialCache()
	cache.Set(uid, api.StsRoleName, credential)

	return info, &ntfsResponse[0], cache
}
