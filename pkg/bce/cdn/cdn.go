package cdn

import (
	"net/url"
	"strings"

	"icode.baidu.com/baidu/faas/kun/pkg/api"
	"icode.baidu.com/baidu/faas/kun/pkg/bce/auth"
	"icode.baidu.com/baidu/faas/kun/pkg/bce/iam"
	"icode.baidu.com/baidu/faas/kun/pkg/bce/sts_credential"
	"icode.baidu.com/baidu/faas/kun/pkg/rest"
)

type Dialer interface {
	ListDomains(*RequestInfo, string) (*GetDomainsOutput, error)
	GetNotifications(*RequestInfo, string) (*[]Notification, error)
	DeleteNotification(*RequestInfo, string) error
	CreateNotification(*RequestInfo, *Notification) error
	UpdateNotification(*RequestInfo, *Notification) error
	GetEdgeTriggers(*RequestInfo, *GetEdgeTriggersInput) (*[]EdgeTrigger, error)
	DeleteEdgeTrigger(*RequestInfo, string) error
	CreateEdgeTrigger(*RequestInfo, *EdgeTrigger) (*EdgeTrigger, error)
	UpdateEdgeTrigger(*RequestInfo, *EdgeTrigger) error
	DeleteEdgeTriggers(info *RequestInfo, input *DeleteEdgeTriggersInput) (err error)
}

type RequestInfo struct {
	UID       string
	RequestID string
	EventType string
}

func NewRequestInfo(uid, requestID string) *RequestInfo {
	return &RequestInfo{
		UID:       uid,
		RequestID: requestID,
	}
}

// Notification xxx
type Notification struct {
	UUID        string   `json:"uuid"`
	Domains     []string `json:"domains"`
	EventType   string   `json:"eventType"`
	FunctionBrn string   `json:"functionBrn"`
	Remark      string   `json:"remark"`
	Starter     string   `json:"starter"`
}

type DomainInfo struct {
	Name string `json:"name"`
}

type EdgeTrigger struct {
	UUID        string `json:"id"`
	Domain      string `json:"domain"`
	Path        string `json:"path"`
	EventType   string `json:"eventType"`
	FunctionBrn string `json:"functionBrn"`
}

type GetEdgeTriggersInput struct {
	FunctionBrn string `json:"functionBrn"`
	Path        string `json:"path"`
	Domain      string `json:"domain"`
	EventType   string `json:"eventType"`
}

type DeleteEdgeTriggersInput struct {
	FunctionBrn string `json:"functionBrn"`
	Domain      string `json:"domain"`
}

type GetDomainsOutput struct {
	Domains     []DomainInfo `json:"domains"`
	IsTruncated bool         `json:"isTruncated"`
	NextMarker  string       `json:"nextMarker,omitempty"`
}

// Client xxx
type Client struct {
	client    *rest.RESTClient
	iamClient iam.ClientInterface
	stsRole   string
	stsCache  sts_credential.Cache
}

// NewClient xxx
func NewClient(endpoint, stsRoleName string, cache sts_credential.Cache, iamClient iam.ClientInterface) *Client {
	version := "v2"
	config := rest.ContentConfig{
		BackendType: rest.BackendTypeBce,
	}

	baseURL, _ := url.Parse("http://" + strings.Replace(endpoint, "http://", "", 1))
	client, _ := rest.NewRESTClient(baseURL, version, config, nil)
	return &Client{
		client:    client,
		iamClient: iamClient,
		stsCache:  cache,
		stsRole:   stsRoleName,
	}
}

func (c *Client) sign(info *RequestInfo, request *rest.Request) (err error) {
	credential := c.stsCache.Get(info.UID, c.stsRole)
	if credential == nil {
		if credential, err = c.iamClient.AssumeRole(info.UID, c.stsRole, "", 3600); err != nil {
			return
		}
		c.stsCache.Set(info.UID, c.stsRole, credential)
	}

	reqURL := request.URL()
	request.SetHeader("Host", reqURL.Host)
	request.SetHeader("X-Bce-Security-Token", credential.SessionToken)

	if info.RequestID != "" {
		request.SetHeader(api.HeaderXRequestID, info.RequestID)
	}

	bceAuth := auth.NewBceAuth(credential.AccessKeyId, credential.AccessKeySecret)
	sign := bceAuth.NewSigner().
		Method(request.Verb()).
		Path(reqURL.Path).
		Params(reqURL.Query()).
		Headers(request.Header()).
		Expire(3600).
		WithSignedHeader().
		GetSign()

	request.SetHeader("Authorization", sign)
	return nil
}

// CreateNotification xxx
func (c *Client) CreateNotification(info *RequestInfo, ntf *Notification) (err error) {
	req := c.client.Put().
		Resource("/trigger").
		Body(ntf)

	if err = c.sign(info, req); err != nil {
		return
	}

	return req.Do().Error()
}

// UpdateNotification xxx
func (c *Client) UpdateNotification(info *RequestInfo, ntf *Notification) (err error) {
	req := c.client.Post().
		Resource("/trigger").
		Param("uuid", ntf.UUID).
		Body(ntf)

	if err = c.sign(info, req); err != nil {
		return
	}

	return req.Do().Error()
}

// DeleteNotification xxx
func (c *Client) DeleteNotification(info *RequestInfo, uuid string) (err error) {
	req := c.client.Delete().
		Resource("/trigger").
		Param("uuid", uuid)

	if err = c.sign(info, req); err != nil {
		return
	}

	return req.Do().Error()
}

// GetNotifications xxx
func (c *Client) GetNotifications(info *RequestInfo, funcBrn string) (ntfs *[]Notification, err error) {
	req := c.client.Get().
		Resource("/trigger")

	if len(funcBrn) != 0 {
		req = req.Param("functionBrn", funcBrn)
	}

	if len(info.EventType) != 0 {
		req = req.Param("eventType", info.EventType)
	}

	if err = c.sign(info, req); err != nil {
		return
	}

	ntfs = &[]Notification{}
	err = req.Do().Into(ntfs)
	return
}

// ListDomains xxx
func (c *Client) ListDomains(info *RequestInfo, nextMarker string) (output *GetDomainsOutput, err error) {
	req := c.client.Get().
		Resource("/domain")

	if nextMarker != "" {
		req.Param("nextMarker", nextMarker)
	}

	if err = c.sign(info, req); err != nil {
		return
	}

	output = &GetDomainsOutput{}
	err = req.Do().Into(output)
	return
}

// CreateEdgeTrigger xxx
func (c *Client) CreateEdgeTrigger(info *RequestInfo, trigger *EdgeTrigger) (edgeTrigger *EdgeTrigger, err error) {
	req := c.client.Post().
		Resource("/edge-trigger").
		Body(trigger)

	if err = c.sign(info, req); err != nil {
		return
	}
	edgeTrigger = new(EdgeTrigger)
	if err = req.Do().Into(edgeTrigger); err != nil {
		return nil, err
	}
	return
}

// UpdateEdgeTrigger xxx
func (c *Client) UpdateEdgeTrigger(info *RequestInfo, trigger *EdgeTrigger) (err error) {
	req := c.client.Put().
		Resource("/edge-trigger/" + trigger.UUID).
		Body(trigger)

	if err = c.sign(info, req); err != nil {
		return
	}

	return req.Do().Error()
}

// DeleteEdgeTrigger xxx
func (c *Client) DeleteEdgeTrigger(info *RequestInfo, uuid string) (err error) {
	req := c.client.Delete().
		Resource("/edge-trigger/" + uuid)

	if err = c.sign(info, req); err != nil {
		return
	}

	return req.Do().Error()
}

// GetEdgeTriggers xxx
func (c *Client) GetEdgeTriggers(info *RequestInfo, input *GetEdgeTriggersInput) (triggers *[]EdgeTrigger, err error) {
	req := c.client.Get().
		Resource("/edge-trigger")

	if input.FunctionBrn != "" {
		req.Param("functionBrn", input.FunctionBrn)
	}

	if input.Path != "" {
		req.Param("path", input.Path)
	}

	if input.Domain != "" {
		req.Param("domain", input.Domain)
	}

	if input.EventType != "" {
		req.Param("eventType", input.EventType)
	}

	if err = c.sign(info, req); err != nil {
		return
	}
	triggers = new([]EdgeTrigger)
	err = req.Do().Into(triggers)
	return
}

// DeleteEdgeTriggers xxx
func (c *Client) DeleteEdgeTriggers(info *RequestInfo, input *DeleteEdgeTriggersInput) (err error) {
	req := c.client.Delete().
		Resource("/edge-trigger").
		Param("domain", input.Domain).
		Param("functionBrn", input.FunctionBrn)

	if err = c.sign(info, req); err != nil {
		return
	}

	return req.Do().Error()
}
