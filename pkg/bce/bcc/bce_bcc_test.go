package bcc

import (
	"testing"

	"icode.baidu.com/baidu/faas/kun/pkg/api"
	"icode.baidu.com/baidu/faas/kun/pkg/test"
)

type testMockBccClient struct {
	*test.TestHTTPServer
	BccInterface
}

func newMockBccClient() *testMockBccClient {
	testServer := test.NewTestHTTPServer()
	runOptions := &api.BceOptions{
		AccessKeyID:     "a3fc33602e91420b9f277d2e92422db4",
		AccessKeySecret: "08f1f848d97244cc9ce5553fefc5bf08",
		BccEndpoint:     testServer.Server.URL,
	}
	c := NewBccClient(runOptions)
	c.setClient(testServer.Server.Client())
	bccClient := &testMockBccClient{
		TestHTTPServer: testServer,
		BccInterface:   c,
	}

	return bccClient
}

func TestGetBccImageList(t *testing.T) {
	testBccClient := newMockBccClient()
	defer testBccClient.Close()

	res := &BccGetImageResponse{
		Result: make([]BccImage, 5),
	}
	testBccClient.Handler(&test.TestHandler{
		T:        t,
		Method:   "GET",
		Path:     "/api/logical/bcc/v1/glance",
		Response: res,
	})
	if images, err := testBccClient.GetBccImageList(); err != nil {
		t.Errorf("get node should success, images=%v, err=%v", images, err)
	} else {
		t.Logf("images_num=%d", len(images))
		t.Logf("images=%+v", images)
	}
}

func TestGetBccImageID(t *testing.T) {
	testBccClient := newMockBccClient()
	defer testBccClient.Close()

	res := &BccGetImageResponse{
		Result: make([]BccImage, 5),
	}
	osType := "linux"
	osName := "Ubuntu"
	osArch := "amd64 (64bit)"
	osVersion := "16.04 LTS"
	res.Result[0].ID = "ixxxxxxxxxxxxxx"
	res.Result[0].OsName = osName
	res.Result[0].OsType = osType
	res.Result[0].OsArch = osArch
	res.Result[0].OsVersion = osVersion

	testBccClient.Handler(&test.TestHandler{
		T:        t,
		Method:   "GET",
		Path:     "/api/logical/bcc/v1/glance",
		Response: res,
	})
	if imageID, err := testBccClient.GetBccImageLongID(osType, osName, osVersion, osArch); err != nil {
		t.Errorf("get image id should success, imageid=%v, err=%v", imageID, err)
	} else {
		if imageID == "ixxxxxxxxxxxxxx" {
			t.Logf("image is :%s", imageID)
		} else {
			t.Errorf("image id is not correct")

		}
	}

}

func TestGetStockWithSpec(t *testing.T) {
	testBccClient := newMockBccClient()
	defer testBccClient.Close()

	res := &GetStockWithSpecRes{
		BccStocks: make([]BccStock, 1),
	}

	testBccClient.Handler(&test.TestHandler{
		T:        t,
		Method:   "POST",
		Path:     "/api/logical/bcc/v1/instance/getStockWithSpec",
		Response: res,
	})

	if _, err := testBccClient.GetStockWithSpec("bcc.g3.c2m8"); err != nil {
		t.Errorf("getStockWithSpec fail, err: %v", err)
	}

}
