// Code generated by MockGen. DO NOT EDIT.
// Source: icode.baidu.com/baidu/faas/kun/pkg/bce/bcc (interfaces: BccInterface)

// Package mock is a generated GoMock package.
package mock

import (
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	bcc "icode.baidu.com/baidu/faas/kun/pkg/bce/bcc"
)

// MockBccInterface is a mock of BccInterface interface.
type MockBccInterface struct {
	ctrl     *gomock.Controller
	recorder *MockBccInterfaceMockRecorder
}

// MockBccInterfaceMockRecorder is the mock recorder for MockBccInterface.
type MockBccInterfaceMockRecorder struct {
	mock *MockBccInterface
}

// NewMockBccInterface creates a new mock instance.
func NewMockBccInterface(ctrl *gomock.Controller) *MockBccInterface {
	mock := &MockBccInterface{ctrl: ctrl}
	mock.recorder = &MockBccInterfaceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockBccInterface) EXPECT() *MockBccInterfaceMockRecorder {
	return m.recorder
}

// GetAvailableSpec mocks base method.
func (m *MockBccInterface) GetAvailableSpec() (map[string]bcc.ZoneSpec, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAvailableSpec")
	ret0, _ := ret[0].(map[string]bcc.ZoneSpec)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAvailableSpec indicates an expected call of GetAvailableSpec.
func (mr *MockBccInterfaceMockRecorder) GetAvailableSpec() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAvailableSpec", reflect.TypeOf((*MockBccInterface)(nil).GetAvailableSpec))
}

// GetBccImageList mocks base method.
func (m *MockBccInterface) GetBccImageList() ([]bcc.BccImage, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetBccImageList")
	ret0, _ := ret[0].([]bcc.BccImage)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetBccImageList indicates an expected call of GetBccImageList.
func (mr *MockBccInterfaceMockRecorder) GetBccImageList() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBccImageList", reflect.TypeOf((*MockBccInterface)(nil).GetBccImageList))
}

// GetBccImageLongID mocks base method.
func (m *MockBccInterface) GetBccImageLongID(arg0, arg1, arg2, arg3 string) (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetBccImageLongID", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetBccImageLongID indicates an expected call of GetBccImageLongID.
func (mr *MockBccInterfaceMockRecorder) GetBccImageLongID(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBccImageLongID", reflect.TypeOf((*MockBccInterface)(nil).GetBccImageLongID), arg0, arg1, arg2, arg3)
}

// GetStockWithSpec mocks base method.
func (m *MockBccInterface) GetStockWithSpec(arg0 string) ([]bcc.BccStock, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetStockWithSpec", arg0)
	ret0, _ := ret[0].([]bcc.BccStock)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetStockWithSpec indicates an expected call of GetStockWithSpec.
func (mr *MockBccInterfaceMockRecorder) GetStockWithSpec(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetStockWithSpec", reflect.TypeOf((*MockBccInterface)(nil).GetStockWithSpec), arg0)
}
