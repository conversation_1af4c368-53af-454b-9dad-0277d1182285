package bcc

import (
	"errors"
	"net/http"
	"net/url"

	"icode.baidu.com/baidu/faas/kun/pkg/util/logs"

	"github.com/google/uuid"

	"icode.baidu.com/baidu/faas/kun/pkg/api"
	"icode.baidu.com/baidu/faas/kun/pkg/bce/auth"
	"icode.baidu.com/baidu/faas/kun/pkg/rest"
)

type BccClient struct {
	host       string
	bceAuth    *auth.BceAuth
	restClient *rest.RESTClient
}

type BccGetImageResponse struct {
	PageNo     int        `json:"pageNo"`     //第几页
	PageSize   int        `json:"pageSize"`   //每页大小
	TotalCount int        `json:"totalCount"` //找到多少结果
	Result     []BccImage `json:"result"`     // 符合要求的image list结果列表
}

/*
example:

	{
	      "id":"724a0a29-cba1-4ba0-afe8-267b74f51da5",
	      "imageId":"m-KnIorpcB",
	      "name":"16.04 LTS amd64 (64bit)",
	      "desc":"ubuntu-16.04-amd64-20200601210654",
	      "cpu":0,
	      "createTime":"2020-06-02T07:23:41Z",
	      "diskSize":0,
	      "ephemeralSize":0,
	      "imageDescription":"ubuntu-16.04-amd64-20200601210654",
	      "memory":0,
	      "minMem":1,
	      "maxRamGb":0,
	      "minCpu":0,
	      "minDiskGb":20,
	      "osArch":"amd64 (64bit)",
	      "osBuild":"2020060100",
	      "osName":"Ubuntu",
	      "osType":"linux",
	      "osVersion":"16.04 LTS",
	      "specialVersion":"",
	      "osLang":"ENG",
	      "snapshotId":"",
	      "status":"active",
	      "fpgaType":"",
	      "imageType":"common",
	      "shareToUserNumLimit":0,
	      "sharedToUserNum":0,
	      "supportUserData":true,
	      "tags":Array[0],
	      "snapshots":[

	      ]
	  }
*/
type BccImage struct {
	ID                  string   `json:"id,omitempty"`         //镜像ID
	ImageID             string   `json:"imageid,omitempty"`    //镜像短ID
	Name                string   `json:"name,omitempty"`       //镜像名字
	Desc                string   `json:"desc,omitempty"`       //镜像描述
	Cpu                 float64  `json:"cpu,omitempty"`        //cpu数目
	CreateTime          string   `json:"createTime,omitempty"` //创建时间
	DiskSize            float64  `json:"diskSize,omitempty"`   //镜像所需硬盘大小
	EphemeralSize       float64  `json:"ephemeralSize,omitempty"`
	ImageDescription    string   `json:"imageDescription,omitempty"` //镜像描述
	Memory              float64  `json:"memory,omitempty"`           //内存
	MinMem              float64  `json:"minMem,omitempty"`           //最小内存要求
	MaxRamGb            float64  `json:"maxRamGb,omitempty"`         //最小Ram要求
	MinCpu              float64  `json:"minCpu,omitempty"`           //最小Cpu要求
	MinDiskGb           float64  `json:"minDiskGb,omitempty"`        //最小硬盘容量要求
	OsType              string   `json:"osType,omitempty"`           //操作系统类型
	OsArch              string   `json:"osArch,omitempty"`           //操作系统架构amd64
	OsName              string   `json:"osName,omitempty"`           //操作系统名字
	OsVersion           string   `json:"osVersion,omitempty"`        //操作系统版本号
	OsBuild             string   `json:"osBuild,omitempty"`          //镜像build时间
	SpecialVersion      string   `json:"specialVersion,omitempty"`
	OsLang              string   `json:"osLang,omitempty"`     //操作系统语言
	SnapshotId          string   `json:"snapshotId,omitempty"` //短ID
	Status              string   `json:"status,omitempty"`     //镜像状态
	FpgaType            string   `json:"fpgaType,omitempty"`
	ImageType           string   `json:"imageType,omitempty"` //镜像类型，common表示公共镜像
	ShareToUserNumLimit float64  `json:"shareToUserNumLimit,omitempty"`
	SharedToUserNum     float64  `json:"sharedToUserNum,omitempty"`
	SupportUserData     bool     `json:"supportUserData,omitempty"`
	Tags                []string `json:"tags,omitempty"`
	Snapshots           []string `json:"snapshots,omitempty"`
}

type GetStockWithSpecReq struct {
	Spec string `json:"spec"` // 实例套餐规格
	//DeploySetIds []string `json:"deploySetIds,omitempty"` // 部署集短id列表
}

// bcc库存规格
type BccStock struct {
	LogicalZone       string `json:"logicalZone"`       // 可用区
	Spec              string `json:"spec"`              // 实例规格
	SpecId            string `json:"specId"`            // 实例规格族
	InventoryQuantity int    `json:"inventoryQuantity"` // 库存数量
	UpdateTime        string `json:"updateTime"`        // 更新时间
	CollectionTime    string `json:"collectionTime"`    // 采集时间
	RootOnLocal       bool   `json:"rootOnLocal"`       // 是否是本地盘
}

type ZoneSpec map[string]string

type GetStockWithSpecRes struct {
	BccStocks []BccStock `json:"bccStocks"`
}

type GetAvailableSpecRes struct {
	ZoneResources []ZoneResourceDetailSpec `json:"zoneResources"`
}

type ZoneResourceDetailSpec struct {
	ZoneName     string       `json:"zoneName"`
	BccResources BccResources `json:"bccResources"`
}

type BccResources struct {
	FlavorGroups []FlavorGroups `json:"flavorGroups"`
}

type FlavorGroups struct {
	GroupID string      `json:"groupId"`
	Flavors []BccFlavor `json:"flavors"`
}

type BccFlavor struct {
	Spec string `json:"spec"`
}

// BccInterface
//
//go:generate mockgen -destination=./mock/bcc.go -package=mock icode.baidu.com/baidu/faas/kun/pkg/bce/bcc BccInterface
type BccInterface interface {
	GetBccImageList() ([]BccImage, error)
	GetBccImageLongID(osType string, osName string, osVersion string, osArch string) (string, error)
	GetStockWithSpec(spec string) ([]BccStock, error)
	GetAvailableSpec() (map[string]ZoneSpec, error)
}

func (c *BccClient) setClient(httpClient *http.Client) {
	c.restClient.Client = httpClient
}
func NewBccClient(runOptions *api.BceOptions) *BccClient {
	baseURL, err := url.Parse(runOptions.BccEndpoint)
	if err != nil {
		panic(err)
	}

	config := rest.ContentConfig{
		BackendType: rest.BackendTypeBce,
	}

	client, err := rest.NewRESTClient(baseURL, "", config, nil)
	if err != nil {
		panic(err)
	}
	return &BccClient{
		host:       baseURL.Host,
		bceAuth:    auth.NewBceAuth(runOptions.AccessKeyID, runOptions.AccessKeySecret),
		restClient: client,
	}
}

func NewBccOpenAPIClient(runOptions *api.BceOptions) *BccClient {
	baseURL, err := url.Parse(runOptions.BccOpenAPIEndpoint)
	if err != nil {
		panic(err)
	}

	config := rest.ContentConfig{
		BackendType: rest.BackendTypeBce,
	}

	client, err := rest.NewRESTClient(baseURL, "", config, nil)
	if err != nil {
		panic(err)
	}
	return &BccClient{
		host:       baseURL.Host,
		bceAuth:    auth.NewBceAuth(runOptions.AccessKeyID, runOptions.AccessKeySecret),
		restClient: client,
	}
}

// http://bcclogic.su.bce-internal.baidu.com/api/logical/bcc/v1/glance?imageTypes=common
func (c *BccClient) GetBccImageList() ([]BccImage, error) {

	out := BccGetImageResponse{}

	req := c.restClient.Get().
		Resource("api/logical/bcc/v1/glance").
		Param("imageTypes", "common").
		Param("manner", "page").
		SetHeader("Host", c.host)

	c.sign(req)

	err := req.Do().Into(&out)
	return out.Result, err

}

// 从公共镜像列表中找到符合要求的镜像ID
func (c *BccClient) GetBccImageLongID(osType string, osName string, osVersion string, osArch string) (string, error) {
	images, err := c.GetBccImageList()
	if err != nil {
		return "", err
	}
	for _, image := range images {
		if image.OsType == osType && image.OsName == osName && image.OsVersion == osVersion && image.OsArch == osArch {
			return image.ID, nil
		}
	}
	err = errors.New("there is no valid bcc image")
	return "", err
}

func (c *BccClient) sign(request *rest.Request) {
	reqUrl := request.URL()
	sign := c.bceAuth.NewSigner().
		Method(request.Verb()).
		Path(reqUrl.Path).
		Params(reqUrl.Query()).
		Headers(request.Header()).
		Expire(3600).
		WithSignedHeader().
		GetSign()

	request.SetHeader("Authorization", sign)
}

// 通过bcc规格查看库存
func (c *BccClient) GetStockWithSpec(spec string) ([]BccStock, error) {
	param := &GetStockWithSpecReq{
		Spec: spec,
	}
	bccStocks := GetStockWithSpecRes{}
	reqID := uuid.New().String()

	req := c.restClient.Post().
		Resource("/api/logical/bcc/v1/instance/getStockWithSpec").
		SetHeader("Host", c.host).
		SetHeader("X-Bce-Request-Id", reqID).
		Body(param)

	c.sign(req)

	err := req.Do().Into(&bccStocks)
	logs.Infof("scaling up param: %+v, reqId: %v, err: %v", param, reqID, err)
	return bccStocks.BccStocks, err
}

func (c *BccClient) GetAvailableSpec() (map[string]ZoneSpec, error) {

	specMap := make(map[string]ZoneSpec)

	/*
		1. /v{version}/instance/flavorSpec 获取所有能用的规格
		2. 将可用规格按照可用区分别存到 Map 中
	*/
	reqID := uuid.New().String()

	req := c.restClient.Get().
		Resource("/v2/instance/flavorSpec").
		SetHeader("Host", c.host).
		SetHeader("X-Bce-Request-Id", reqID)

	c.sign(req)

	res := GetAvailableSpecRes{}

	err := req.Do().Into(&res)

	if err != nil {
		logs.Infof("[GetAvailableSpec]scaling up , reqId: %v, err: %v", reqID, err)
		return nil, err
	}

	for _, z := range res.ZoneResources {

		zoneMap := make(map[string]string)

		specMap[z.ZoneName] = zoneMap

		for _, fg := range z.BccResources.FlavorGroups {

			for _, flavor := range fg.Flavors {
				zoneMap[flavor.Spec] = flavor.Spec
			}

		}

	}

	return specMap, err
}
