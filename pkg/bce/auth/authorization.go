package auth

import (
	"fmt"
	"icode.baidu.com/baidu/faas/kun/pkg/api"
	"strings"
)

const (
	AuthorizationV1Length = 6
	AuthorizationV2Length = 7
)

type AuthorizationV1 struct {
	Version           string // 版本号
	Ak                string // AK
	TimeStamp         string // timestamp是生成签名时的时间
	ExpirationSeconds string // 签名有效期限
	SignedHeaders     string // 签名算法中涉及到的头域列表,头域名之间用分号（;）分隔，如host;range;x-bce-date。列表按照字典序排列。当signedHeaders为空时表示取默认值
	Signature         string // 256位签名的十六进制表示，由64个小写字母组成
}

type AuthorizationV2 struct {
	Version       string //version
	Ak            string
	Date          string //格式为UTC时间的yyyymmdd
	Region        string //所请求服务资源所在的区域，小写。可参考请求endpoint中的region信息。对于全局资源endpoint无region的，值默认为bj
	Service       string //所请求的服务产品名，小写。如bos/bcc
	SignedHeaders string //签名算法中涉及到的头域列表,头域名之间用分号（;）分隔，如host;range;x-bce-date。列表按照字典序排列。当signedHeaders为空时表示取默认值
	Signature     string //256位签名的十六进制表示，由64个小写字母组成
}

// parse from authorization from authorization string
// 详细文档 http://gollum.baidu.com/BceApiSpec#8.1-%E7%AD%BE%E5%90%8D%E7%AE%97%E6%B3%95
// 认证字符串的格式为bce-auth-v{version}/{accessKeyId}/{timestamp}/{expirationPeriodInSeconds}/{signedHeaders}/{signature}
func (a *AuthorizationV1) ParseAuthorization(authorization string) error {
	authSep := strings.Split(authorization, "/")
	lenAuthSep := len(authSep)
	if lenAuthSep != AuthorizationV1Length || authSep[0] != BCE_AUTH_V1 {
		return fmt.Errorf("authorization:%s parse failed: authorization: is empty string or auth type is not BCE_AUTH_V1", authorization)
	}
	a.Version = authSep[0]
	a.Ak = authSep[1]
	a.TimeStamp = authSep[2]
	a.SignedHeaders = authSep[3]
	a.SignedHeaders = authSep[4]
	a.Signature = authSep[5]
	return nil
}

// authorization format is like this
// 详细说明 http://wiki.baidu.com/pages/viewpage.action?pageId=666352475
// bce-auth-v2/{accessKeyId}/{date}/{region}/{service}/{signedHeaders}/{signature}
func (a *AuthorizationV2) ParseAuthorization(authorization string) error {
	authSep := strings.Split(authorization, "/")
	lenAuthSep := len(authSep)
	if lenAuthSep != AuthorizationV2Length || authSep[0] != BCE_AUTH_V2 {
		return fmt.Errorf("authorization: %s parse failed: authorization is empty string or auth type is not BCE_AUTH_V2", authorization)
	}
	a.Version = authSep[0]
	a.Ak = authSep[1]
	a.Date = authSep[2]
	a.Region = authSep[3]
	a.Service = authSep[4]
	a.SignedHeaders = authSep[5]
	if !strings.Contains(a.SignedHeaders, api.HeaderxBceDate) {
		return fmt.Errorf("signHeader have no x-bce-date header")
	}

	a.Signature = authSep[6]
	return nil
}

func (a *AuthorizationV2) Scope() string {
	if a == nil {
		return ""
	}
	return fmt.Sprintf("%s/%s/%s/%s/%s", a.Version, a.Ak, a.Date, a.Region, a.Service)
}
