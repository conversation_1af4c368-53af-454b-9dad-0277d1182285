package auth

import (
	"github.com/spf13/pflag"
)

// DuerOptions contains the options to create a DuerRequest
type DuerOptions struct {
	CertDomainList []string
	// CertDomainCheck string
}

// NewDuerOptions create a default options for a generic api server.
func NewDuerOptions() *DuerOptions {
	return &DuerOptions{
		CertDomainList: []string{
			"duer.bdstatic.com",
			"rsa-proxy.bj.bcebos.com",
		},
		// CertDomainCheck: "strict",
	}
}

// AddFlags parse the flags
func (s *DuerOptions) AddFlags(fs *pflag.FlagSet) {

	fs.StringSliceVar(&s.CertDomainList, "duer-cert-domain-list", s.CertDomainList, ""+
		"official valid duer cert url domain, to prevent download malformed contents")

	// fs.StringVar(&s.CertDomainCheck, "duer-cert-domain-check", s.CertDomainCheck, ""+
	//	"[strict|loose|none] how to check dueros cert url domain, 'strict' means totally match domain, "+
	//	"'loose' means match second-level domain, eg '*.baidu.com', 'none' to skip check")
}
