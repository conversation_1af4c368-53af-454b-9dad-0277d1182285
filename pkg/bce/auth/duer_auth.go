package auth

import (
	"crypto"
	"crypto/rsa"
	"crypto/sha1"
	"crypto/x509"
	"encoding/base64"
	"encoding/pem"
	"errors"
	"fmt"
	"io/ioutil"
	"net/http"
	gourl "net/url"
	"strconv"
	"sync"
	"time"

	kunErr "icode.baidu.com/baidu/faas/kun/pkg/error"
	"icode.baidu.com/baidu/faas/kun/pkg/util/json"
	"icode.baidu.com/baidu/faas/kun/pkg/util/logs"
)

// FaaS和和度秘对接的认证过程，具体协议内容，可参考
// http://wiki.baidu.com/pages/viewpage.action?pageId=382817877

// DuerAuth 需要缓存证书下载地址和证书内容
type DuerAuth struct {
	lock      sync.RWMutex
	CertURL   string
	RsaPubKey *rsa.PublicKey
}

var (
	duerAuth = &DuerAuth{}
)

// UpdateCert 更新证书
func (a *DuerAuth) UpdateCert(certURL string) error {
	a.lock.Lock()
	defer a.lock.Unlock()

	// 如果下载链接没有变动，则不需要重新下载、解析证书
	if a.CertURL == certURL {
		return nil
	}

	cert, err := a.getCertFromURL(certURL)
	if err != nil {
		return err
	}
	pubkey, err := a.parsePubKeyFromCert(cert)
	if err != nil {
		return err
	}
	a.CertURL = certURL
	a.RsaPubKey = pubkey
	return nil
}

func (a *DuerAuth) getCertFromURL(certURL string) (*x509.Certificate, error) {
	res, err := http.Get(certURL)
	if err != nil {
		return nil, err
	}
	body, err := ioutil.ReadAll(res.Body)
	if err != nil {
		return nil, err
	}
	block, _ := pem.Decode(body)
	if block == nil || block.Type != "CERTIFICATE" {
		return nil, kunErr.NewInvalidRequestContentException("get certificate failed", nil)
	}
	return x509.ParseCertificate(block.Bytes)
}

func (a *DuerAuth) parsePubKeyFromCert(cert *x509.Certificate) (*rsa.PublicKey, error) {
	switch t := cert.PublicKey.(type) {
	case *rsa.PublicKey:
		return t, nil
	default:
		return nil, fmt.Errorf("unsupported pem type %T", t)
	}
}

// Verify 用公钥解密 Signature，并和 body 内容进行对比
func (a *DuerAuth) Verify(body, sign []byte) error {
	a.lock.RLock()
	defer a.lock.RUnlock()

	h := sha1.New()
	h.Write(body)
	digest := h.Sum(nil)

	logs.Debugf("digest=%v, sign=%v", digest, sign)

	return rsa.VerifyPKCS1v15(a.RsaPubKey, crypto.SHA1, digest, sign)
}

// DuerRequest xxx
type DuerRequest struct {
	certURL string
	sign    []byte
	body    []byte
}

// NewDuerRequest 由普通的请求构造成为度秘的请求
func NewDuerRequest(options *DuerOptions, r *http.Request, body []byte) (*DuerRequest, error) {
	// Signaturecerturl 是证书下载地址
	// 如果没有该字段，则认为请求不来自度秘
	url := r.Header.Get("Signaturecerturl")
	if len(url) == 0 {
		return nil, errors.New("no Signaturecerturl param found")
	}
	if !validUrl(options, url) {
		return nil, fmt.Errorf("invalid Signaturecerturl '%s'", url)
	}
	// Signature 是内容的签名，base64编码
	// 如果没有该字段，则认为请求不来自度秘
	signb64 := r.Header.Get("Signature")
	if len(signb64) == 0 {
		return nil, errors.New("no Signature param found")
	}
	sign, err := base64.StdEncoding.DecodeString(signb64)
	if err != nil {
		return nil, err
	}
	return &DuerRequest{
		certURL: url,
		sign:    sign,
		body:    body,
	}, nil
}

// CheckError 检查来自度秘的请求是否合法
func (r *DuerRequest) CheckError() error {
	if err := duerAuth.UpdateCert(r.certURL); err != nil {
		return err
	}
	// 校验 body 的 sha1 和 sign 是否匹配
	if err := duerAuth.Verify(r.body, r.sign); err != nil {
		return kunErr.NewUnrecognizedClientException("sign check fail", err)
	}
	// 校验 body 中的 timestamp 是否符合时间范围
	req := DuerBotRequestV2{}
	if err := json.Unmarshal(r.body, &req); err != nil {
		return kunErr.NewInvalidRequestContentException("bad request", err)
	}
	t, _ := strconv.Atoi(req.Request.Timestamp)
	ts := time.Unix(int64(t), 0).Add(150 * time.Second)
	if time.Now().After(ts) {
		return kunErr.NewUnrecognizedClientException("request expired", nil)
	}
	return nil
}

func validUrl(options *DuerOptions, url string) bool {
	u, err := gourl.Parse(url)
	if err != nil {
		return false
	}
	for _, domain := range options.CertDomainList {
		if u.Host == domain {
			return true
		}
	}
	return false
}
