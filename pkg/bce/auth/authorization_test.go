package auth

import (
	"errors"
	"testing"
)

type testCase struct {
	AuthStr string
	Want    error
}

func TestAuthorizationV1_ParseAuthorization(t *testing.T) {

	testCases := []testCase{
		{
			AuthStr: "bce-auth-v1/a3fc33602e91420b9f277d2e92422db4/2020-04-15T08:08:50Z/3600/host/76b769960344096ff1538447af672680034aef8a1877afbbc1b0ff6fbf9c82eb",
			Want:    nil,
		},
		{
			AuthStr: "bce-auth-v2/a3fc33602e91420b9f277d2e92422db4/2020-04-15T08:08:50Z/3600/host/76b769960344096ff1538447af672680034aef8a1877afbbc1b0ff6fbf9c82eb",
			Want:    errors.New("fake error"),
		},
		{
			AuthStr: "bce-auth-v2/a3fc33602e91420b9f277d2e92422db4/2020-04-15T08:08:50Z/3600/76b769960344096ff1538447af672680034aef8a1877afbbc1b0ff6fbf9c82eb",
			Want:    errors.New("fake error"),
		},
	}
	for _, c := range testCases {
		v1 := AuthorizationV1{}
		err := v1.ParseAuthorization(c.AuthStr)
		if err == nil && err != c.Want {
			t.Errorf("you result: %+v, want: %+v", err, c.Want)
		}
		if err != nil && c.Want == nil {
			t.Errorf("you result: %+v, want: %+v", err, c.Want)
		}
	}

}

func TestAuthorizationV2_ParseAuthorization(t *testing.T) {
	testCases := []testCase{
		{
			AuthStr: "bce-auth-v2/b5e478e040214973a2c44d49fba0adb4/20160104/bce:cfc/bj/content-length;content-md5;host;x-bce-content-sha256;x-bce-date;x-bce-request-id/7e7625bf561e24d47ef2b33e49eea0932407a0b9fec5b88319976a841136e6ab",
			Want:    nil,
		},
		{
			AuthStr: "bce-auth-v1/b5e478e040214973a2c44d49fba0adb4/20160104/bce:cfc/bj/content-length;content-md5;host;x-bce-content-sha256;x-bce-date;x-bce-request-id/7e7625bf561e24d47ef2b33e49eea0932407a0b9fec5b88319976a841136e6ab",
			Want:    errors.New("fake error"),
		},
		{
			AuthStr: "bce-auth-v2/a3fc33602e91420b9f277d2e92422db4/",
			Want:    errors.New("fake error"),
		},
	}
	for _, c := range testCases {
		v2 := AuthorizationV2{}
		err := v2.ParseAuthorization(c.AuthStr)
		if err == nil && err != c.Want {
			t.Errorf("you result: %+v, want: %+v", err, c.Want)
		}
		if err != nil && c.Want == nil {
			t.Errorf("you result: %+v, want: %+v", err, c.Want)
		}
	}

}

func TestAuthorizationV2_Scope(t *testing.T) {
	authStr := "bce-auth-v2/b5e478e040214973a2c44d49fba0adb4/20160104/bce:cfc/bj/content-length;content-md5;host;x-bce-content-sha256;x-bce-date;x-bce-request-id/7e7625bf561e24d47ef2b33e49eea0932407a0b9fec5b88319976a841136e6ab"
	v2 := AuthorizationV2{}
	err := v2.ParseAuthorization(authStr)
	exceptScope := "bce-auth-v2/b5e478e040214973a2c44d49fba0adb4/20160104/bce:cfc/bj"
	if err != nil && v2.Scope() != exceptScope {
		t.Errorf("scope: %s, except scope: %s", v2.Scope(), exceptScope)

	}
}
