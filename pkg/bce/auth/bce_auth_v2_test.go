package auth

import (
	"net/http"
	"net/url"
	"testing"
	"time"
)

var accessKey = "b5e478e040214973a2c44d49fba0adb4"
var secretKey = "49589fb2c3da4041b8fd9cd9bfbdeef3"

func TestSignerV2_GetSign(t *testing.T) {
	auth := NewBceAuth(accessKey, secretKey)
	headers := http.Header{}
	headers.Add("Content-Length", "13256")
	headers.Add("Content-MD5", "ujOLK9GE1xdbYdfKvfI1BA==")
	headers.Add("Host", "bos.qasandbox.bcetest.baidu.com")
	headers.Add("x-bce-content-sha256", "4604e6530e5a06dec3099e77977cab6c7c88eae21005f6edd66deffc203a5e6e")
	headers.Add("x-bce-date", "2016-01-04T06:12:04Z")
	headers.Add("x-bce-request-id", "f98023ac-1189-412b-93f5-859970585dce")

	params := url.Values{}
	params.Add("foo", "bar")
	params.Add("path", "/aaa/bbb/ccc")
	service := "bce:cfc"
	region := "bj"
	result := auth.NewSignerV2(service, region).
		Method("PUT").
		Path("/v1/hiphotos/2").
		Headers(headers).
		Params(params).
		Now(time.Unix(1451887924, 0)).
		Expire(1800).
		WithSignedHeader().
		GetSign()

	expectResult := "bce-auth-v2/b5e478e040214973a2c44d49fba0adb4/20160104/bce:cfc/bj/content-length;content-md5;host;x-bce-content-sha256;x-bce-date;x-bce-request-id/7e7625bf561e24d47ef2b33e49eea0932407a0b9fec5b88319976a841136e6ab"
	if result != expectResult {
		t.Errorf("want '%s', get '%s'", expectResult, result)
	}
}

func TestSignerV2_GetSigatureBySigningKey(t *testing.T) {
	auth := NewBceAuth(accessKey, secretKey)
	headers := http.Header{}
	headers.Add("Content-Length", "13256")
	headers.Add("Content-MD5", "ujOLK9GE1xdbYdfKvfI1BA==")
	headers.Add("Host", "bos.qasandbox.bcetest.baidu.com")
	headers.Add("x-bce-content-sha256", "4604e6530e5a06dec3099e77977cab6c7c88eae21005f6edd66deffc203a5e6e")
	headers.Add("x-bce-date", "2016-01-04T06:12:04Z")
	headers.Add("x-bce-request-id", "f98023ac-1189-412b-93f5-859970585dce")

	params := url.Values{}
	params.Add("foo", "bar")
	params.Add("path", "/aaa/bbb/ccc")
	service := "bce:cfc"
	region := "bj"
	signKey := "fd3fbd12940d005e5ed26e164d7dc54325295972d80e0e763df3aac3a1c8de79"
	singner := auth.NewSignerV2(service, region).
		Method("PUT").
		Path("/v1/hiphotos/2").
		Headers(headers).
		Params(params).
		Now(time.Unix(1451887924, 0)).
		Expire(1800).
		WithSignedHeader()

	signature := singner.GetSigatureBySigningKey(signKey)
	expectResult := "7e7625bf561e24d47ef2b33e49eea0932407a0b9fec5b88319976a841136e6ab"
	if signature != expectResult {
		t.Errorf("want '%s', get '%s'", expectResult, signature)
	}

	// test get singingkey
	getsignkey := singner.GetSingningKey()
	if getsignkey != signKey {
		t.Errorf("want '%s', get '%s'", signKey, getsignkey)
	}

}
