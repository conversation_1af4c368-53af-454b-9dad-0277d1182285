package auth

// 度秘 bot 协议，具体细节可以参考
// http://wiki.baidu.com/pages/viewpage.action?pageId=372645458

// DuerBotRequestV2 定义了 DuerOS 发给 Bot 的请求格式，它包括了会话信息、端信息和标准请求信息
// v1 协议字段比较杂，有的字段不清晰；还有一些隐私信息完全暴露未加密；有不必要的字段
type DuerBotRequestV2 struct {
	Request DuerLaunchRequest `json:"request"`
}

// DuerLaunchRequest “打开 Bot”的请求会被解析为 LaunchRequest
type DuerLaunchRequest struct {
	Type      string `json:"type"`
	RequestID string `json:"requestId"`
	Timestamp string `json:"timestamp"`
}
