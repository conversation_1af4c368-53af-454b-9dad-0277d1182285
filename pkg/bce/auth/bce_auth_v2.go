package auth

import (
	"fmt"
	"net/http"
	"net/url"
	"strings"
	"time"
)

// Signer for v2 xxx
type SignerV2 struct {
	Signer
	region  string
	service string
}

func (a *BceAuth) NewSignerV2(region string, service string) *SignerV2 {
	return &SignerV2{
		Signer: Signer{
			accessKey:   a.<PERSON>,
			secretKey:   a.<PERSON>,
			authVersion: BCE_AUTH_V2, //V2签名
			//以下header一定不加签
			ignoredHeaders: rules{
				blacklist{
					mapRule{
						"authorization": struct{}{},
						"user-agent":    struct{}{},
						"bce-faas-uid":  struct{}{},
						"x-auth-token":  struct{}{},
						"app":           struct{}{},
					},
				},
			},
		},
		region:  region,
		service: service,
	}
}

func (s *SignerV2) Now(v time.Time) *SignerV2       { s.now = v; return s }
func (s *SignerV2) Method(v string) *SignerV2       { s.method = v; return s }
func (s *SignerV2) Path(v string) *SignerV2         { s.path = v; return s }
func (s *SignerV2) Params(v url.Values) *SignerV2   { s.params = v; return s }
func (s *SignerV2) Headers(v http.Header) *SignerV2 { s.headers = v; return s }
func (s *SignerV2) Expire(v int64) *SignerV2        { s.expire = v; return s }
func (s *SignerV2) WithSignedHeader() *SignerV2     { s.withSignedHeader = true; return s }

func (s *SignerV2) AddIgnoredHeader(headerKeys ...string) *SignerV2 {
	s.ignoredHeaders = append(s.ignoredHeaders, blacklist{
		func(keys []string) mapRule {
			m := make(mapRule)
			for _, key := range keys {
				m[key] = struct{}{}
			}
			return m
		}(headerKeys),
	})
	return s
}

func (s *SignerV2) GetSign() string {
	if s.now.IsZero() {
		s.now = time.Now()
	}

	stringPrefix := s.getSignKeyPrefix()
	signKey := HmacSha256Hex(s.secretKey, stringPrefix)
	canonicalRequest, signHeaders := s.getCanonicalRequest()

	signature := HmacSha256Hex(signKey, canonicalRequest)
	var result string
	if s.withSignedHeader == true {
		result = fmt.Sprintf("%s/%s/%s", stringPrefix, strings.Join(signHeaders, ";"), signature)
	} else {
		result = fmt.Sprintf("%s//%s", stringPrefix, signature)
	}
	return result
}

//bce-auth-v2/{accessKeyId}/{date}/{region}/{service}/{signedHeaders}/{signature}
func (s *SignerV2) GetSigatureBySigningKey(signingKey string) string {
	canonicalRequest, _ := s.getCanonicalRequest()
	signature := HmacSha256Hex(signingKey, canonicalRequest)
	return signature
}

func (s *SignerV2) GetSingningKey() string {
	if s.now.IsZero() {
		s.now = time.Now()
	}

	stringPrefix := s.getSignKeyPrefix()
	signKey := HmacSha256Hex(s.secretKey, stringPrefix)
	return signKey
}

//bce-auth-v2/{accessKeyId}/{date}/{region}/{service}
func (s *SignerV2) getSignKeyPrefix() string {
	stringPrefix := fmt.Sprintf("%s/%s/%s/%s/%s", s.authVersion, s.accessKey, GetCanonicalDate(s.now), s.region, s.service)
	return stringPrefix
}

// 获取YYYYMMDD格式时间
func GetCanonicalDate(now time.Time) string {
	year, mon, day := now.UTC().Date()
	return fmt.Sprintf("%04d%02d%02d", year, mon, day)
}

func (s *Signer) getCanonicalRequest() (canonicalRequest string, signHeaders []string) {
	canonicalURI := getNormalizedString(s.path, true)
	canonicalQueryString := getCanonicalQueryString(s.params)
	canonicalHeaders, signHeaders := getCanonicalHeaders(s.headers, s.ignoredHeaders)

	canonicalRequest = s.method + "\n" + canonicalURI + "\n" + canonicalQueryString + "\n" + canonicalHeaders
	return canonicalRequest, signHeaders
}
