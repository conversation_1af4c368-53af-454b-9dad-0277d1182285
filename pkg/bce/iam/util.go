package iam

import (
	"strings"

	"icode.baidu.com/baidu/faas/kun/pkg/bce/auth"
)

func GetAuthV2Scope(authorization string) string {
	a := auth.AuthorizationV2{}
	err := a.ParseAuthorization(authorization)
	if err != nil {
		return ""
	}
	return a.Scope()
}

func CheckHeader(headers map[string]string, signedHeader string) bool {
	// "host must in signed header"
	if !strings.Contains(signedHeader, "host") {
		return false
	}
	//
	for k, v := range headers {
		lowk := strings.ToLower(k)
		headers[lowk] = v
	}
	sighedHeaderSlice := strings.Split(signedHeader, ";")
	for _, h := range sighedHeaderSlice {

		if _, ok := headers[h]; !ok {
			return false
		}
	}
	return true
}

func GetAuthVersion(authStr string) (version string) {
	if strings.HasPrefix(authStr, auth.BCE_AUTH_V1) || strings.HasPrefix(authStr, auth.JWT_AUTH){
		return auth.BCE_AUTH_V1
	} else if strings.HasPrefix(authStr, auth.BCE_AUTH_V2) {
		return auth.BCE_AUTH_V2
	} else if strings.HasPrefix(authStr, auth.BAIDU_INT_AUTH_V1) {
		return auth.BAIDU_INT_AUTH_V1
	}else {
		return ""
	}
}
