package iam

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestNewNoopClient(t *testing.T) {
	noopClient := NewNoopClient()

	assert.NotPanics(t, func() {
		noopClient.ServiceToken()
		noopClient.ServiceAuth()
		noopClient.AccessKey()
		noopClient.SecretKey()
		noopClient.Region()
		noopClient.ServiceUserName()
		noopClient.AuthenticateWithToken("", "")
		noopClient.AuthenticateWithAKSKRemote(nil)
		noopClient.AuthenticateWithAKSKV2(nil)
		noopClient.AuthenticateWithAKSK(nil)
		noopClient.PermissionVerifyWithUserID("", nil)
		noopClient.PermissionVerifyRemote("", nil)
		noopClient.PermissionVerify("", nil)
		noopClient.PermissionVerifyV2("", nil)
		noopClient.BatchPermissionVerify("", "", nil)
		noopClient.AssumeRole("", "", "", 0)
		noopClient.AssumeRoleAcl("", "", "", 0, "")
		noopClient.GetAccount("")
	})

}
