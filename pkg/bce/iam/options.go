/**
 * Created Date: Friday, October 20th 2017, 12:11:24 pm
 * Author: hefan<PERSON><PERSON>
 * Copyright (c) 2017 Baidu.Inc
 *
 */

package iam

import (
	"github.com/spf13/pflag"
)

// IAMOptions IAM鉴权客户端参数
type IAMOptions struct {
	IAMConfPath string `yaml:"IAMConfPath"`
	ServiceRole string `yaml:"ServiceRole"`
	IAMResource string `yaml:"IAMResource"`
	EnableInf   bool   `yaml:"EnableInf"`
}

// NewIAMOptions 创建IAM鉴权客户端参数
func NewIAMOptions() *IAMOptions {
	return &IAMOptions{
		IAMConfPath: "",
		ServiceRole: "BceServiceRole_cfc",
		IAMResource: "*",
		EnableInf:   false,
	}
}

// AddUniversalFlags parse the flags
func (s *IAMOptions) AddUniversalFlags(fs *pflag.FlagSet) {
	fs.StringVar(&s.IAMConfPath, "iam-conf", s.<PERSON>f<PERSON>, "BCE IAM client config file path")
	fs.StringVar(&s.ServiceRole, "service-role", s.ServiceRole, "服务角色")
	fs.StringVar(&s.IAMResource, "iam-resource", s.IAMResource, "IAM鉴权Resource")
	fs.BoolVar(&s.EnableInf, "enable-inf", s.EnableInf, "是否接云上百度iam，默认为false")
}
