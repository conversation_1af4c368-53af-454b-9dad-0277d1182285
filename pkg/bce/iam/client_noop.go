package iam

import (
	"time"

	"icode.baidu.com/baidu/faas/kun/pkg/bce/auth"
	"icode.baidu.com/baidu/faas/kun/pkg/bce/sts_credential"
)

var noopToken = Token{
	ID:        "noop-token",
	ExpiresAt: time.Date(2099, 12, 30, 0, 0, 0, 0, time.UTC),
	IssuedAt:  time.Date(2000, 12, 30, 0, 0, 0, 0, time.UTC),
	Methods:   nil,
	Domain:    nil,
	Project:   nil,
	User: User{
		ID:   "noopuser",
		Name: "Noop User",
		Domain: &Domain{
			ID:   "noopuser",
			Name: "Noop User",
		},
		Password: "",
	},
	Roles:   nil,
	Catalog: nil,
}

var noopSinglePermissionVerifyResult = SinglePermissionVerifyResult{
	Id:     "",
	Effect: PermissionAllow,
	Eid:    "",
}

type NoopClient struct{}

func NewNoopClient() ClientInterface {
	return &NoopClient{}
}

func (NoopClient) ServiceToken() (*Token, error) {
	return &noopToken, nil
}

func (NoopClient) ServiceAuth() (*auth.BceAuth, error) {
	return &auth.BceAuth{
		AccessKey: "",
		SecretKey: "",
	}, nil
}

func (NoopClient) AccessKey() string {
	return ""
}

func (NoopClient) SecretKey() string {
	return ""
}

func (NoopClient) Region() string {
	return "on-premise"
}

func (NoopClient) ServiceUserName() string {
	return ""
}

func (NoopClient) AuthenticateWithToken(subjectToken, requestId string) (*Token, error) {
	return &noopToken, nil
}

func (NoopClient) AuthenticateWithAKSKRemote(request *AuthenticationRequest) (*Token, error) {
	return &noopToken, nil
}

func (NoopClient) AuthenticateWithAKSKV2(request *AuthenticationRequest) (*Token, error) {
	return &noopToken, nil
}

func (NoopClient) AuthenticateWithAKSK(request *AuthenticationRequest) (*Token, error) {
	return &noopToken, nil
}

func (NoopClient) PermissionVerifyWithUserID(userid string, request *SinglePermissionVerifyRequest) (*SinglePermissionVerifyResult, error) {
	return &SinglePermissionVerifyResult{
		Id:     "",
		Effect: "",
		Eid:    "",
	}, nil
}

func (NoopClient) PermissionVerifyRemote(subjectToken string, request *SinglePermissionVerifyRequest) (*Token, *SinglePermissionVerifyResult, error) {
	return &noopToken, &noopSinglePermissionVerifyResult, nil
}

func (NoopClient) PermissionVerify(subjectToken string, request *SinglePermissionVerifyRequest) (*Token, *SinglePermissionVerifyResult, error) {
	return &noopToken, &noopSinglePermissionVerifyResult, nil
}

func (NoopClient) PermissionVerifyV2(subjectToken string, request *SinglePermissionVerifyRequest) (*Token, *SinglePermissionVerifyResult, error) {
	return &noopToken, &noopSinglePermissionVerifyResult, nil
}

func (NoopClient) BatchPermissionVerify(userid, requestId string, requests []*MultiplePermissionVerifyRequest) ([]*MultiplePermissionVerifyResult, error) {
	return nil, nil
}

func (NoopClient) AssumeRole(userid, roleName, requestId string, duration int) (*sts_credential.StsCredential, error) {
	return &sts_credential.StsCredential{
		AccessKeyId:     "",
		AccessKeySecret: "",
		SessionToken:    "",
		Expiration:      time.Date(2099, 12, 30, 0, 0, 0, 0, time.UTC),
		UserId:          "noopuser",
		RoleId:          "",
	}, nil
}

func (NoopClient) AssumeRoleAcl(userid, roleName, requestId string, duration int, body string) (*sts_credential.StsCredential, error) {
	return &sts_credential.StsCredential{
		AccessKeyId:     "",
		AccessKeySecret: "",
		SessionToken:    "",
		Expiration:      time.Date(2099, 12, 30, 0, 0, 0, 0, time.UTC),
		UserId:          "noopuser",
		RoleId:          "",
	}, nil
}

func (NoopClient) GetAccount(name string) (*UserAccount, error) {
	return nil, nil
}
