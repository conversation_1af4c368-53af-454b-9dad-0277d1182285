package iam

import (
	"errors"
	"fmt"
	"math"
	"strconv"
	"strings"
	"time"

	bceauth "icode.baidu.com/baidu/faas/kun/pkg/bce/auth"
)

const (
	MaxExpiration   time.Duration = 900 * time.Second
	TolerantTime    time.Duration = 300 * time.Second
	ReqHeaderPrifix               = "x-bce-"
)

type SignChecker interface {
	AuthenticationBySigningKey(request *AuthenticationRequest, signingKey string) error
}

// v1版本没必要进行本地验签先不实现
type SignCheckerV1 struct {
	auth bceauth.AuthorizationV1
}

// v2签名本地验签
type SignCheckerV2 struct {
	auth bceauth.AuthorizationV2
}

// new authV2 checker
func NewSignCheckerV2(authStr string) (*SignCheckerV2, error) {
	auth := bceauth.AuthorizationV2{}
	err := auth.ParseAuthorization(authStr)
	return &SignCheckerV2{auth: auth}, err
}

func (s *SignCheckerV2) Ak() string {
	return s.auth.Ak
}

func (s *SignCheckerV2) Scope() string {
	return s.auth.Scope()
}

func (s *SignCheckerV2) AuthenticationBySigningKey(request *AuthenticationRequest, signingKey string) error {
	// check request
	err := s.checkRequest(request)
	if err != nil {
		return fmt.Errorf("[AuthorizationBySigningKey] check Request failed: %s", err.Error())
	}
	// caculate signature
	auth := bceauth.NewBceAuth(s.auth.Ak, "")
	params := request.Request.GetParamsValue()
	header := request.Request.GetHttpSignedHeader(s.auth.SignedHeaders)
	signature := auth.NewSignerV2(s.auth.Region, s.auth.Service).
		Method(request.Request.Method).
		Path(request.Request.Uri).
		Params(params).
		Headers(header).
		WithSignedHeader().
		Expire(3600).
		GetSigatureBySigningKey(signingKey)

	// 本地验签
	if signature != s.auth.Signature {
		return fmt.Errorf("authoritation in local failed: caculate %s, want: %s", signature, s.auth.Signature)
	}
	return nil

}

func (s *SignCheckerV2) checkRequest(request *AuthenticationRequest) error {
	expiration, err := s.getRequestExpiration(request)
	if err != nil {
		return fmt.Errorf("Bad request: get expiration failed: %s", err)
	}

	requestTime, err := s.getRequestTime(request)
	if err != nil {
		return err
	}
	// check if request is expired
	if expiration > 0 {
		if time.Now().Add(TolerantTime).Sub(*requestTime) < 0 {
			return errors.New("Bad Request: request time is not valid")
		}
		maxExpTime := requestTime.Add(TolerantTime).Add(expiration)
		if time.Now().Sub(maxExpTime) > 0 {
			return errors.New("Auth Request is expired")
		}
	}

	if !CheckHeader(request.Request.Headers, s.auth.SignedHeaders) {
		return fmt.Errorf("Auth Request header check failed: %+v, %s", request.Request.Headers, s.auth.SignedHeaders)
	}
	return nil
}

func (s *SignCheckerV2) getRequestExpiration(request *AuthenticationRequest) (time.Duration, error) {
	header := ReqHeaderPrifix + "expiration"
	// 从request header查找到这个header
	found := false
	var expirationValue string
	for key, value := range request.Request.Headers {
		key = strings.ToLower(key)
		if key == header {
			if !strings.Contains(s.auth.SignedHeaders, key) {
				return MaxExpiration, fmt.Errorf("signedHeader have not this header: %s", header)
			}
			found = true
			expirationValue = value

		}
	}

	// 从request的 params找这个header
	for key, value := range request.Request.Params {
		key = strings.ToLower(key)
		if key == header {
			found = true
			expirationValue = value
		}
	}
	var expiration time.Duration
	if found {
		exp, err := strconv.ParseInt(expirationValue, 10, 64)
		if err != nil || exp > math.MaxInt64 || exp < math.MinInt64 {
			return MaxExpiration, fmt.Errorf("the expiration value not vaild")
		}
		expiration = time.Duration(exp) * time.Second
	}

	return expiration, nil
}

func (s *SignCheckerV2) getRequestTime(request *AuthenticationRequest) (*time.Time, error) {
	header := ReqHeaderPrifix + "date"
	// 从request header查找到这个header
	found := false
	var requestTimeValue string
	for key, value := range request.Request.Headers {
		key = strings.ToLower(key)
		if key == header {
			if !strings.Contains(s.auth.SignedHeaders, key) {
				return nil, fmt.Errorf("signedHeader not contains the header:%s", header)
			}
			found = true
			requestTimeValue = value

		}
	}

	// 从request的 params找这个header
	for key, value := range request.Request.Params {
		key = strings.ToLower(key)
		if key == header {
			found = true
			requestTimeValue = value
		}
	}

	if !found {
		return nil, fmt.Errorf("request have no header or params about: %s", header)
	}

	reqTime, err := time.Parse(time.RFC3339, requestTimeValue)
	if err != nil {
		return nil, fmt.Errorf("request time parse failed: %s", err.Error())
	}

	if reqTime.Second() < 0 {
		return nil, fmt.Errorf("request time is not valid")
	}
	return &reqTime, nil

}
