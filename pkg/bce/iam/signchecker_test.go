package iam

import (
	"net/http"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"

	"icode.baidu.com/baidu/faas/kun/pkg/bce/auth"
)

func TestSignCheckerV2_AuthorizationBySingingKey(t *testing.T) {
	request, signingKey := getTestRequestAndSigningKey()
	checker, err := NewSignCheckerV2(request.Authorization)
	assert.Nil(t, err)
	err = checker.AuthenticationBySigningKey(request, signingKey)
	assert.Nil(t, err)
}

func TestSignCheckerV2_Ak(t *testing.T) {
	authStr := "bce-auth-v2/5d4e433531d14d2f91de2dc576368a51/20200417/bj/cfc/host;x-bce-date;x-bce-expiration/574130d8a60e8e1a9245a35fdfee9d03447d42032bfd2890b446487957bde907"
	checker, err := NewSignCheckerV2(authStr)
	assert.Nil(t, err)
	ak := checker.Ak()
	assert.Equal(t, "5d4e433531d14d2f91de2dc576368a51", ak)

}

func TestSignCheckerV2_getRequestExpiration(t *testing.T) {
	request := getTestRequest()
	checker, err := NewSignCheckerV2(request.Authorization)
	assert.Nil(t, err)
	wantExp := time.Duration(3600) * time.Second
	expiration, err := checker.getRequestExpiration(request)
	assert.Nil(t, err)
	assert.Equal(t, wantExp, expiration)

}

func TestSignCheckV2_getRequestTime(t *testing.T) {
	request := getTestRequest()
	checker, err := NewSignCheckerV2(request.Authorization)
	assert.Nil(t, err)
	wantTime, _ := time.Parse(time.RFC3339, request.Request.Headers["X-Bce-Date"])
	reqTime, err := checker.getRequestTime(request)
	assert.Nil(t, err)
	assert.Equal(t, wantTime, *reqTime)
}

func getTestRequest() *AuthenticationRequest {
	request := AuthenticationRequest{}
	headers := http.Header{}
	headers.Add("Host", "127.0.0.1")
	headers.Add("x-bce-expiration", "3600")
	dateStr := auth.GetCanonicalTime(time.Now().UTC())

	headers.Add("x-bce-date", dateStr)
	request.Request.WithMethod(http.MethodPost).
		WithUri("/a/b/c").WithHeader(headers)
	request.Authorization = auth.NewBceAuth(
		"5d4e433531d14d2f91de2dc576368a51",
		"c13b18ad42a6405db7da9ced3bee40d2").
		NewSignerV2("bj", "cfc").Method(http.MethodPost).
		Path("/a/b/c").Headers(headers).Expire(1800).
		WithSignedHeader().GetSign()
	return &request
}

func getTestRequestAndSigningKey() (*AuthenticationRequest, string) {
	request := AuthenticationRequest{}
	headers := http.Header{}
	headers.Add("Host", "127.0.0.1")
	headers.Add("x-bce-expiration", "3600")
	dateStr := auth.GetCanonicalTime(time.Now().UTC())

	headers.Add("x-bce-date", dateStr)
	request.Request.WithMethod(http.MethodPost).
		WithUri("/a/b/c").WithHeader(headers)
	signer := auth.NewBceAuth(
		"5d4e433531d14d2f91de2dc576368a51",
		"c13b18ad42a6405db7da9ced3bee40d2").
		NewSignerV2("bj", "cfc").Method(http.MethodPost).
		Path("/a/b/c").Headers(headers).Expire(1800).
		WithSignedHeader()
	request.Authorization = signer.GetSign()

	signingKey := signer.GetSingningKey()
	return &request, signingKey
}
