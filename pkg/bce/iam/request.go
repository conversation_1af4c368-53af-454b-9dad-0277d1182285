package iam

import (
	"fmt"
	"net/http"
	"net/url"
	"strings"
	"time"
)

type SignRequest struct {
	Method  string            `json:"method"`
	Uri     string            `json:"uri"`
	Params  map[string]string `json:"params"`
	Headers map[string]string `json:"headers"`
}

func (r *SignRequest) WithMethod(method string) *SignRequest {
	r.Method = method
	return r
}

func (r *SignRequest) WithUri(uri string) *SignRequest {
	r.Uri = uri
	return r
}

func (r *SignRequest) WithParams(params url.Values) *SignRequest {
	if r.Params == nil {
		r.Params = make(map[string]string)
	}
	for key := range params {
		r.Params[key] = params.Get(key)
	}
	return r
}

func (r *SignRequest) WithHeader(headers http.Header) *SignRequest {
	if r.Headers == nil {
		r.Headers = make(map[string]string)
	}
	for key := range headers {
		r.Headers[key] = headers.Get(key)
	}
	return r
}

func (r *SignRequest) WithHost(host string) *SignRequest {
	if r.<PERSON><PERSON> == nil {
		r.<PERSON><PERSON> = make(map[string]string)
	}
	if len(host) > 0 {
		r.<PERSON><PERSON>["Host"] = host
	}
	return r
}

// get url.Value Params
func (r *SignRequest) GetParamsValue() url.Values {
	if len(r.Params) == 0 || r.Params == nil {
		return nil
	}
	//
	params := make(url.Values)
	for key, value := range r.Params {
		params.Set(key, value)
	}
	return params
}

// get http.Header header
func (r *SignRequest) GetHttpHeader() http.Header {
	if len(r.Headers) == 0 || r.Headers == nil {
		return nil
	}
	header := make(http.Header)
	for key, value := range r.Headers {
		header.Set(key, value)
	}
	return header
}

func (r *SignRequest) GetHttpSignedHeader(signHeaders string) http.Header {
	if len(r.Headers) == 0 || r.Headers == nil {
		return nil
	}
	header := make(http.Header)
	for key, value := range r.Headers {
		if strings.Contains(signHeaders, key) {
			header.Set(key, value)
		}
	}
	return header
}

type AuthenticationRequest struct {
	Authorization string      `json:"authorization"`
	Request       SignRequest `json:"request"`
	SecurityToken string      `json:"security_token,omitempty"`
	RequestId     string      `json:"-"`
}

type PermissionVerifyContext struct {
	IPAddr string
	Time   time.Time
	Refer  string
}

type SinglePermissionVerifyRequest struct {
	Service    string                   `json:"service"`
	Region     string                   `json:"region"`
	Resource   string                   `json:"resource"`
	Owner      string                   `json:"resource_owner"`
	Permission []string                 `json:"permission"`
	Context    *PermissionVerifyContext `json:"request_context,omitempty"`
	Auth       *AuthenticationRequest   `json:"auth,omitempty"`
	RequestId  string                   `json:"-"`
}

type SinglePermissionVerifyResult struct {
	Id     string `json:"id"`
	Effect string `json:"effect"`
	Eid    string `json:"eid"`
}

func (r *SinglePermissionVerifyRequest) WithService(
	service string) *SinglePermissionVerifyRequest {
	r.Service = service
	return r
}

func (r *SinglePermissionVerifyRequest) WithRegion(
	region string) *SinglePermissionVerifyRequest {
	r.Region = region
	return r
}

func (r *SinglePermissionVerifyRequest) WithResource(
	resource string) *SinglePermissionVerifyRequest {
	r.Resource = resource
	return r
}

func (r *SinglePermissionVerifyRequest) WithOwner(
	owner string) *SinglePermissionVerifyRequest {
	r.Owner = owner
	return r
}

func (r *SinglePermissionVerifyRequest) AddPermission(
	permision string) *SinglePermissionVerifyRequest {
	r.Permission = append(r.Permission, permision)
	return r
}

func (r *SinglePermissionVerifyRequest) WithContext(
	context *PermissionVerifyContext) *SinglePermissionVerifyRequest {
	r.Context = context
	return r
}

func (r *SinglePermissionVerifyRequest) WithAuth(
	auth *AuthenticationRequest) *SinglePermissionVerifyRequest {
	r.Auth = auth
	return r
}

func (r *SinglePermissionVerifyRequest) GetContextKey(ak string) string {
	permStr := strings.Join(r.Permission, ";")
	return fmt.Sprintf("%s/%s/%s/%s/%s/%s", ak, r.Region, r.Service, r.Resource, r.Owner, permStr)
}

type MultiplePermissionVerifyRequest struct {
	Service    string                   `json:"service"`
	Region     string                   `json:"region"`
	Resources  []string                 `json:"resource"`
	Owner      string                   `json:"resource_owner"`
	Permission []string                 `json:"permission"`
	Context    *PermissionVerifyContext `json:"request_context,omitempty"`
	Auth       *AuthenticationRequest   `json:"auth,omitempty"`
}

type MultiplePermissionVerifyResult struct {
	Results []SinglePermissionVerifyResult `json:"result"`
}

type UserAccount struct {
	Account struct {
		Name         string `json:"name"`
		DomainId     string `json:"domain_id"`
		DomainName   string `json:"domain_name"`
		Email        string `json:"email"`
		Phone        string `json:"phone"`
		MobilePhone  string `json:"mobile_phone"`
		Company      string `json:"company"`
		Industry     string `json:"industry"`
		RegisterTime string `json:"register_time"`
		ActivateTime string `json:"activate_time"`
	} `json:"account"`
}

func (r *MultiplePermissionVerifyRequest) WithService(
	service string) *MultiplePermissionVerifyRequest {
	r.Service = service
	return r
}

func (r *MultiplePermissionVerifyRequest) WithRegion(
	region string) *MultiplePermissionVerifyRequest {
	r.Region = region
	return r
}

func (r *MultiplePermissionVerifyRequest) AddResource(
	resource string) *MultiplePermissionVerifyRequest {
	r.Resources = append(r.Resources, resource)
	return r
}

func (r *MultiplePermissionVerifyRequest) WithOwner(
	owner string) *MultiplePermissionVerifyRequest {
	r.Owner = owner
	return r
}

func (r *MultiplePermissionVerifyRequest) AddPermission(
	permision string) *MultiplePermissionVerifyRequest {
	r.Permission = append(r.Permission, permision)
	return r
}

func (r *MultiplePermissionVerifyRequest) WithContext(
	context *PermissionVerifyContext) *MultiplePermissionVerifyRequest {
	r.Context = context
	return r
}

func (r *MultiplePermissionVerifyRequest) WithAuth(
	auth *AuthenticationRequest) *MultiplePermissionVerifyRequest {
	r.Auth = auth
	return r
}
