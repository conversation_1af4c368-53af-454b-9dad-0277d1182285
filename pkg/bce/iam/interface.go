package iam

import (
	"icode.baidu.com/baidu/faas/kun/pkg/bce/auth"
	"icode.baidu.com/baidu/faas/kun/pkg/bce/sts_credential"
)

type ClientInterface interface {
	ServiceToken() (*Token, error)
	ServiceAuth() (*auth.BceAuth, error)
	AccessKey() string
	SecretKey() string
	Region() string
	ServiceUserName() string
	AuthenticateWithToken(subjectToken, requestId string) (*Token, error)
	AuthenticateWithAKSKRemote(request *AuthenticationRequest) (*Token, error)
	AuthenticateWithAKSKV2(request *AuthenticationRequest) (*Token, error)
	AuthenticateWithAKSK(request *AuthenticationRequest) (*Token, error)
	PermissionVerifyWithUserID(userid string,
		request *SinglePermissionVerifyRequest) (*SinglePermissionVerifyResult, error)
	PermissionVerifyRemote(subjectToken string,
		request *SinglePermissionVerifyRequest) (*Token, *SinglePermissionVerifyResult, error)
	PermissionVerify(subjectToken string,
		request *SinglePermissionVerifyRequest) (*Token, *SinglePermissionVerifyResult, error)
	PermissionVerifyV2(subjectToken string,
		request *SinglePermissionVerifyRequest) (*Token, *SinglePermissionVerifyResult, error)
	BatchPermissionVerify(userid, requestId string,
		requests []*MultiplePermissionVerifyRequest) ([]*MultiplePermissionVerifyResult, error)
	AssumeRole(userid, roleName, requestId string, duration int) (*sts_credential.StsCredential, error)
	AssumeRoleAcl(userid, roleName, requestId string, duration int, body string) (*sts_credential.StsCredential, error)
	GetAccount(name string) (*UserAccount, error)
}
