package iam

import (
	"bytes"
	"errors"
	"fmt"
	"icode.baidu.com/baidu/faas/kun/pkg/api"
	"io"
	"io/ioutil"
	"net/http"
	"net/url"
	"strconv"
	"time"

	"github.com/go-yaml/yaml"
	"github.com/google/uuid"
	"k8s.io/apimachinery/pkg/util/cache"

	"icode.baidu.com/baidu/faas/kun/pkg/bce/auth"
	"icode.baidu.com/baidu/faas/kun/pkg/bce/sts_credential"
	"icode.baidu.com/baidu/faas/kun/pkg/util/json"
	"icode.baidu.com/baidu/faas/kun/pkg/util/logs"
)

const (
	AuthMethodPassword  = "password"
	AuthMethodSignature = "signature"

	PermissionAllow       = "ALLOW"
	PermissionDeny        = "DENY"
	PermissionDefaultDeny = "DEFAULT_DENY"
	DefaultCacheTTL       = 300 // seconds
)

type IAMConfig struct {
	Username              string `yaml:"username"`
	Password              string `yaml:"password"`
	AccessKey             string `yaml:"access_key"`
	SecretKey             string `yaml:"secret_key"`
	DefaultDomain         string `yaml:"default_domain"`
	SubUserSupport        bool   `yaml:"subuser_support"`
	AuthMethod            string `yaml:"auth_method"`
	Region                string `yaml:"region"`
	Host                  string `yaml:"host"`
	Port                  int    `yaml:"port"`
	StsHost               string `yaml:"stshost"`
	StsPort               int    `yaml:"stsport"`
	ServiceActive         bool   `yaml:"service_active"`
	TokenCacheSize        int    `yaml:"token_cache_size"`
	SecrectCacheSize      int    `yaml:"secrect_cache_size"`
	ActiveCacheSize       int    `yaml:"active_cache_size"`
	SigningKeyCacheSize   int    `yaml:"signingkey_cache_size"`
	VerifyResultCacheSize int    `yaml:"verify_result_cache_size"`
	CacheTTL              int    `yaml:"cache_ttl"`
}

type IAMClient struct {
	endpoint          string
	stsEndpoint       string
	tokenCache        *cache.LRUExpireCache // ak => token
	secretCache       *cache.LRUExpireCache // ak => sk
	activeCache       *cache.LRUExpireCache // accountid => active true or false
	verifyResultCache *cache.LRUExpireCache // content_key => verify result
	signingKeyCache   *cache.LRUExpireCache // auth scope => signingKey
	clientToken       *Token
	refreshCtrl       chan string
	config            *IAMConfig
	accesskeys        []*Accesskey
	client            *http.Client
}
type IAMError struct {
	RequestID string `json:"requestId"`
	Code      string `json:"code"`
	Message   string `json:"message"`
}

func (e *IAMError) Error() string {
	return fmt.Sprintf("IAM request error. requestID:%s, code:%s, message:%s",
		e.RequestID, e.Code, e.Message)
}

func loadConfig(confFile string) (*IAMConfig, error) {
	data, err := ioutil.ReadFile(confFile)
	if err != nil {
		return nil, err
	}
	config := IAMConfig{}
	err = yaml.Unmarshal(data, &config)
	if err != nil {
		return nil, err
	}
	if config.Username == "" ||
		config.Password == "" ||
		config.Host == "" {
		return nil, errors.New("invalid iam config file")
	}
	if (config.AuthMethod != AuthMethodPassword) &&
		(config.AuthMethod != AuthMethodSignature) {
		config.AuthMethod = AuthMethodPassword
	}
	if config.ActiveCacheSize <= 0 {
		config.ActiveCacheSize = 1000
	}
	if config.SecrectCacheSize <= 0 {
		config.SecrectCacheSize = 1000
	}
	if config.ActiveCacheSize <= 0 {
		config.ActiveCacheSize = 1000
	}
	if config.SigningKeyCacheSize <= 0 {
		config.SigningKeyCacheSize = 1000
	}
	if config.VerifyResultCacheSize <= 0 {
		config.VerifyResultCacheSize = 1000
	}
	if config.CacheTTL <= 0 {
		config.CacheTTL = DefaultCacheTTL
	}

	if config.Port == 0 {
		config.Port = 80
	}
	return &config, nil
}

func CreateIAMClient(confFile string) (ClientInterface, error) {
	if confFile == "" {
		return nil, errors.New("config file needed")
	}
	config, err := loadConfig(confFile)
	if err != nil {
		return nil, err
	}
	tc := cache.NewLRUExpireCache(config.TokenCacheSize)
	sc := cache.NewLRUExpireCache(config.SecrectCacheSize)
	ac := cache.NewLRUExpireCache(config.ActiveCacheSize)
	signingKeyCache := cache.NewLRUExpireCache(config.SigningKeyCacheSize)
	vc := cache.NewLRUExpireCache(config.VerifyResultCacheSize)

	client := &IAMClient{
		endpoint:          fmt.Sprintf("http://%s:%d", config.Host, config.Port),
		stsEndpoint:       fmt.Sprintf("http://%s:%d", config.StsHost, config.StsPort),
		refreshCtrl:       make(chan string, 2),
		config:            config,
		tokenCache:        tc,
		secretCache:       sc,
		activeCache:       ac,
		signingKeyCache:   signingKeyCache,
		verifyResultCache: vc,
		client: &http.Client{
			Transport: &http.Transport{
				MaxIdleConnsPerHost: 300, // host  poolmanager:port
				MaxIdleConns:        300 * 10,
			},
		},
	}
	err = client.authServiceToken()
	if err != nil {
		logs.Warnf("iam service auth failed, err=%s", err.Error())
	}
	userid, err := client.serviceUserID()
	if err == nil {
		keys, err := client.getAccessKeys(userid)
		if err != nil {
			logs.Error("fetch service accesskeys failed.")
		} else {
			client.accesskeys = keys
		}
	}

	go client.refreshRoutine()
	return client, nil
}

func NewAuthRequestByHttp(r *http.Request) *AuthenticationRequest {
	authReq := new(AuthenticationRequest)
	authReq.Authorization = r.Header.Get("Authorization")
	authReq.Request.
		WithMethod(r.Method).
		WithUri(r.URL.Path).
		WithParams(r.URL.Query()).
		WithHeader(r.Header).
		WithHost(r.Host)
	authReq.SecurityToken = r.Header.Get(api.HeaderXBceSecurityToken)
	return authReq
}

type authScope struct {
	Domain  *Domain  `json:"domain,omitempty"`
	Project *Project `json:"project"`
}
type authPassword struct {
	User User `json:"user"`
}
type authIdentity struct {
	Methods  []string     `json:"methods"`
	Password authPassword `json:"password"`
}
type authInfo struct {
	Identity authIdentity `json:"identity"`
	Scope    authScope    `json:"scope"`
}
type tokenAuth struct {
	Auth authInfo `json:"auth"`
}

type tokenResp struct {
	Token      *Token `json:"token"`
	SigningKey string `json:"signing_key"`
}

type serviceActiveResp struct {
	Services []serviceName `json:"services"`
}
type serviceName struct {
	Name string `json:"name"`
}

func newTokenAuth(username, password string) *tokenAuth {
	info := tokenAuth{}
	identity := &info.Auth.Identity
	identity.Methods = append(identity.Methods, "password")
	identity.Password.User.Domain = &Domain{
		Name: "default",
	}
	identity.Password.User.Name = username
	identity.Password.User.Password = password
	// info.Auth.Scope.Domain.ID = "default"

	info.Auth.Scope.Project = &Project{
		Domain: &Domain{
			ID: "default",
		},
		Name: "service",
	}
	return &info
}

func (c *IAMClient) authServiceToken() error {
	info := newTokenAuth(c.config.Username, c.config.Password)
	body := new(bytes.Buffer)
	err := json.NewEncoder(body).Encode(info)
	if err != nil {
		return err
	}
	rsp, err := http.Post(c.endpoint+"/v3/auth/tokens", "application/json", body)
	if err != nil {
		return err
	}
	if rsp.StatusCode != http.StatusCreated {
		return errors.New(rsp.Status)
	}

	token := tokenResp{}
	err = json.NewDecoder(rsp.Body).Decode(&token)
	if err != nil {
		return err
	}
	tokenid := rsp.Header.Get(api.HeaderXSubjectToken)

	token.Token.ID = tokenid
	c.clientToken = token.Token
	return nil
}

type accessKeyResponse struct {
	AccessKeys []*Accesskey `json:"accesskeys"`
}

func (c *IAMClient) getAccessKeys(userid string) ([]*Accesskey, error) {
	url := fmt.Sprintf("/v3/users/%s/accesskeys", userid)
	req, _ := c.newRequest(http.MethodGet, url, nil)
	err := c.addAuthHeader(req)
	if err != nil {
		return nil, err
	}
	rsp, err := c.doRequest(req)
	if err != nil {
		return nil, err
	}
	defer rsp.Body.Close()
	data := new(accessKeyResponse)
	err = json.NewDecoder(rsp.Body).Decode(data)
	if err != nil {
		return nil, err
	}
	return data.AccessKeys, nil
}

func (c *IAMClient) refreshRoutine() {
	for {
		duration := 10 * time.Second
		token := c.clientToken
		now := time.Now().UTC()
		if token != nil {
			duration = token.ExpiresAt.Sub(now)
			if duration > 10*time.Minute {
				duration -= 10 * time.Minute
			} else if duration < 0 {
				duration = 0 * time.Second
			}
		}
		select {
		case s := <-c.refreshCtrl:
			if s == "exit" {
				close(c.refreshCtrl)
				return
			}
		case <-time.After(duration):
		}
		err := c.authServiceToken()
		if err != nil {
			logs.Errorf("iam service auth failed, err=%s", err.Error())
			continue
		}
		userid, _ := c.serviceUserID()
		keys, err := c.getAccessKeys(userid)
		if err != nil {
			logs.Error("fetch service accesskeys failed.")
		}
		c.accesskeys = keys
	}
}

func (c *IAMClient) refreshToken() {
	select {
	case c.refreshCtrl <- "refresh":
	default: // channel full
	}
}

func (c *IAMClient) ServiceToken() (*Token, error) {
	t := c.clientToken
	if t == nil {
		return nil, errors.New("service token not exist")
	}
	return t, nil
}

func (c *IAMClient) serviceUserID() (string, error) {
	t := c.clientToken
	if t == nil {
		return "", errors.New("service token not exist")
	}
	return t.User.ID, nil
}

func (c *IAMClient) authMethod() string {
	return c.config.AuthMethod
}

func (c *IAMClient) ServiceAuth() (*auth.BceAuth, error) {
	if len(c.accesskeys) == 0 {
		return nil, errors.New("service authinfo invalid")
	}
	key := c.accesskeys[0]
	return &auth.BceAuth{
		AccessKey: key.AccessKey,
		SecretKey: key.SecretKey,
	}, nil
}

func (c *IAMClient) AccessKey() string {
	if c.config.AccessKey != "" {
		return c.config.AccessKey
	}
	if len(c.accesskeys) > 0 {
		key := c.accesskeys[0]
		return key.AccessKey
	}
	return ""
}

func (c *IAMClient) SecretKey() string {
	if c.config.AccessKey != "" {
		return c.config.AccessKey
	}
	if len(c.accesskeys) > 0 {
		key := c.accesskeys[0]
		return key.SecretKey
	}
	return ""
}

func (c *IAMClient) supportSubuser() bool {
	return c.config.SubUserSupport
}

func (c *IAMClient) Region() string {
	return c.config.Region
}

func (c *IAMClient) ServiceUserName() string {
	return c.config.Username
}

func (c *IAMClient) addAuthHeader(r *http.Request) error {
	if c.supportSubuser() {
		r.Header.Set(api.HeaderXSubuserSupport, "true")
	}
	if c.authMethod() == AuthMethodPassword {
		return c.addXAuthTokenHeader(r)
	}
	c.addAuthorizationHeader(r)
	return nil
}

func (c *IAMClient) addBceDateHeader(r *http.Request) {
	now := time.Now()
	r.Header.Set(api.HeaderxBceDate, now.Format(time.RFC3339))
}

func (c *IAMClient) addContentJsonHeader(r *http.Request) {
	r.Header.Set("content-type", "application/json")
}

func (c *IAMClient) addRequestIDHeader(r *http.Request, requestId string) {
	if requestId == "" {
		reqid, err := uuid.NewUUID()
		if err != nil {
			return
		}
		r.Header.Set(api.HeaderxBceRequestId, reqid.String())
	} else {
		r.Header.Set(api.HeaderxBceRequestId, requestId)
	}
}

func (c *IAMClient) addXAuthTokenHeader(r *http.Request) error {
	token, err := c.ServiceToken()
	if err != nil {
		return err
	}
	r.Header.Set(api.HeaderXAuthToken, token.ID)
	return nil
}

func (c *IAMClient) addAuthorizationHeader(r *http.Request) {
	auth := auth.NewBceAuth(c.AccessKey(), c.SecretKey())
	signature := auth.NewSigner().
		Method(r.Method).
		Path(r.URL.Path).
		Params(r.URL.Query()).
		Headers(r.Header).
		WithSignedHeader().
		Expire(3600).
		GetSign()
	r.Header.Set("Authorization", signature)
}

// /v3/auth/tokens
func (c *IAMClient) AuthenticateWithToken(subjectToken, requestId string) (*Token, error) {
	req, _ := c.newRequest(http.MethodGet, "/v3/auth/tokens", nil)
	req.Header.Set(api.HeaderXSubjectToken, subjectToken)
	if c.supportSubuser() {
		req.Header.Set(api.HeaderXSubuserSupport, "true")
	}
	err := c.addAuthHeader(req)
	if err != nil {
		return nil, err
	}
	c.addRequestIDHeader(req, requestId)

	rsp, err := c.doRequest(req)
	if err != nil {
		return nil, err
	}
	defer rsp.Body.Close()
	if rsp.StatusCode != http.StatusOK {
		iamerr := &IAMError{}
		json.NewDecoder(rsp.Body).Decode(iamerr)
		if rsp.StatusCode == http.StatusUnauthorized { // need refresh token
			c.refreshToken()
		}
		return nil, iamerr
	}
	token := tokenResp{}
	err = json.NewDecoder(rsp.Body).Decode(&token)
	if err != nil {
		return nil, err
	}
	if token.Token != nil {
		token.Token.ID = subjectToken
	}
	return token.Token, nil
}

// 从iam服务端进行认证
// /v3/BCE-CRED/accesskeys
func (c *IAMClient) AuthenticateWithAKSKRemote(request *AuthenticationRequest) (*Token, error) {
	authBody := map[string]interface{}{}
	authBody["auth"] = request
	body := new(bytes.Buffer)
	err := json.NewEncoder(body).Encode(authBody)
	if err != nil {
		return nil, err
	}

	req, _ := c.newRequest(http.MethodPost, "/v3/BCE-CRED/accesskeys", body)
	c.addContentJsonHeader(req)
	err = c.addAuthHeader(req)
	if err != nil {
		return nil, err
	}
	c.addRequestIDHeader(req, request.RequestId)
	rsp, err := c.doRequest(req)
	if err != nil {
		return nil, err
	}
	defer rsp.Body.Close()
	if rsp.StatusCode != http.StatusOK {
		iamerr := &IAMError{}
		json.NewDecoder(rsp.Body).Decode(iamerr)
		if rsp.StatusCode == http.StatusUnauthorized { // need refresh token
			c.refreshToken()
		}
		return nil, iamerr
	}
	token := tokenResp{}
	err = json.NewDecoder(rsp.Body).Decode(&token)
	if err != nil {
		return nil, err
	}
	if token.Token != nil {
		token.Token.ID = rsp.Header.Get(api.HeaderXSubjectToken)
	}
	if token.SigningKey != "" {
		// get scope
		scope := GetAuthV2Scope(request.Authorization)
		if scope != "" {
			c.addSingingKeyToCache(scope, token.SigningKey)
		}
	}
	return token.Token, nil
}

func (c *IAMClient) AuthenticateWithAKSKV2(request *AuthenticationRequest) (*Token, error) {
	if request == nil {
		return nil, errors.New("authenticationRequest is nil")
	}

	checker, err := NewSignCheckerV2(request.Authorization)
	if err != nil {
		return nil, fmt.Errorf("create sign checker failed: %s", err.Error())
	}

	// 从本地获取signerkey token
	scope := checker.Scope()
	ak := checker.Ak()
	signingKey := c.getSigningKeyFromCache(scope)
	token := c.getTokenFromCache(ak)
	if signingKey == "" || token == nil {
		// remote
		token, err := c.AuthenticateWithAKSKRemote(request)
		if err != nil {
			c.addTokenToCache(ak, token)
		}
		return token, err
	}

	// 继续本地验签
	err = checker.AuthenticationBySigningKey(request, signingKey)
	return token, err

}

func (c *IAMClient) AuthenticateWithAKSK(request *AuthenticationRequest) (*Token, error) {
	var authVersion string
	if request != nil {
		authorization := request.Authorization
		authVersion = GetAuthVersion(authorization)
	} else {
		authVersion = auth.BCE_AUTH_V1
	}

	switch authVersion {
	case auth.BCE_AUTH_V1:
		//v1版本认证
		return c.AuthenticateWithAKSKRemote(request) // use v1 interface
	case auth.BCE_AUTH_V2:
		//v2签名认证
		return c.AuthenticateWithAKSKV2(request)
	default:
		return nil, errors.New("authRequest auth version is unknown")
	}

}

// /v3/BCE-CRED/tokens  注意这个是一个特权接口，CFC服务无法IAM这个接口
func (c *IAMClient) getTokenWithAK(ak string, requestID string) (*Token, error) {
	req, _ := c.newRequest(http.MethodGet, "/v3/BCE-CRED/tokens", nil)
	q := req.URL.Query()
	q.Add("accesskey", ak)

	c.addContentJsonHeader(req)
	err := c.addAuthHeader(req)
	if err != nil {
		return nil, err
	}
	c.addRequestIDHeader(req, requestID)
	rsp, err := c.doRequest(req)
	if err != nil {
		return nil, err
	}
	defer rsp.Body.Close()
	if rsp.StatusCode != http.StatusOK {
		iamerr := &IAMError{}
		json.NewDecoder(rsp.Body).Decode(iamerr)
		if rsp.StatusCode == http.StatusUnauthorized { // need refresh token
			c.refreshToken()
		}
		return nil, iamerr
	}
	token := tokenResp{}
	err = json.NewDecoder(rsp.Body).Decode(&token)
	if err != nil {
		return nil, err
	}
	if token.Token != nil {
		token.Token.ID = rsp.Header.Get(api.HeaderXSubjectToken)
	}
	return token.Token, nil
}

// 只有bos业务会走这个接口
func (c *IAMClient) getServiceActive(accountID string, reqID string) (bool, error) {
	uri := fmt.Sprintf("/v3/domains/%s/services", accountID)
	req, _ := c.newRequest(http.MethodGet, uri, nil)
	c.addContentJsonHeader(req)
	err := c.addAuthHeader(req)
	if err != nil {
		return false, err
	}
	c.addRequestIDHeader(req, reqID)
	rsp, err := c.doRequest(req)
	if err != nil {
		return false, err
	}
	defer rsp.Body.Close()

	if rsp.StatusCode != http.StatusOK {
		iamerr := &IAMError{}
		json.NewDecoder(rsp.Body).Decode(iamerr)
		if rsp.StatusCode == http.StatusUnauthorized { // need refresh token
			c.refreshToken()
		}
		return false, iamerr
	}

	services := serviceActiveResp{}
	err = json.NewDecoder(rsp.Body).Decode(&services)
	if err != nil {
		return false, err
	}
	for _, service := range services.Services {
		if service.Name == c.config.Username {
			return true, nil
		}
	}
	return false, nil
}

type verifyResultWithToken struct {
	Result     *SinglePermissionVerifyResult `json:"verify_result"`
	Token      *Token                        `json:"token"`
	SigningKey string                        `json:"signing_key"`
}

func (c *IAMClient) PermissionVerifyWithUserID(userid string,
	request *SinglePermissionVerifyRequest) (*SinglePermissionVerifyResult, error) {
	uri := fmt.Sprintf("/v3/users/%s/permissions", userid)
	body := new(bytes.Buffer)
	err := json.NewEncoder(body).Encode(request)
	if err != nil {
		return nil, err
	}
	req, _ := c.newRequest(http.MethodPost, uri, body)
	c.addContentJsonHeader(req)
	err = c.addAuthHeader(req)
	if err != nil {
		return nil, err
	}
	c.addRequestIDHeader(req, request.RequestId)

	rsp, err := c.doRequest(req)
	if err != nil {
		return nil, err
	}
	defer rsp.Body.Close()
	if rsp.StatusCode != http.StatusOK {
		iamerr := &IAMError{}
		json.NewDecoder(rsp.Body).Decode(iamerr)
		if rsp.StatusCode == http.StatusUnauthorized {
			c.refreshToken()
		}
		return nil, iamerr
	}
	result := verifyResultWithToken{}
	err = json.NewDecoder(rsp.Body).Decode(&result)
	if err != nil {
		return nil, err
	}
	return result.Result, nil
}

// 远程向IAM进行用户认证和鉴权
// PermissionVerify /v3/BCE-CRED/permissions
func (c *IAMClient) PermissionVerifyRemote(subjectToken string,
	request *SinglePermissionVerifyRequest) (*Token, *SinglePermissionVerifyResult, error) {
	body, err := json.Marshal(request)

	if logs.Check(logs.V(9)) {
		logs.Infof("Request permission verify %s", string(body))
	}
	if err != nil {
		return nil, nil, err
	}
	req, _ := c.newRequest(http.MethodPost, "/v3/BCE-CRED/permissions", bytes.NewReader(body))
	c.addContentJsonHeader(req)
	err = c.addAuthHeader(req)
	if err != nil {
		return nil, nil, err
	}
	c.addRequestIDHeader(req, request.RequestId)
	if len(subjectToken) > 0 {
		req.Header.Set(api.HeaderXSubjectToken, subjectToken)
	}
	rsp, err := c.doRequest(req)
	if err != nil {
		return nil, nil, err
	}
	defer rsp.Body.Close()
	if rsp.StatusCode != http.StatusOK {
		iamerr := &IAMError{}
		json.NewDecoder(rsp.Body).Decode(iamerr)
		if rsp.StatusCode == http.StatusUnauthorized {
			c.refreshToken()
		}
		return nil, nil, iamerr
	}
	result := verifyResultWithToken{}
	err = json.NewDecoder(rsp.Body).Decode(&result)
	if err != nil {
		return nil, nil, err
	}
	if result.Token != nil && len(result.Token.ID) == 0 {
		result.Token.ID = subjectToken
	}

	// 仅仅v2请求的才缓存signingkey
	if request.Auth != nil {
		version := GetAuthVersion(request.Auth.Authorization)
		if version == auth.BCE_AUTH_V2 || result.SigningKey != "" {
			scope := GetAuthV2Scope(request.Auth.Authorization)
			c.addSingingKeyToCache(scope, result.SigningKey)
			logs.Debugf("add scope:%s, value:%s to cache", scope, result.SigningKey)
		}
	}

	return result.Token, result.Result, nil
}

// 用户认证加鉴权
func (c *IAMClient) PermissionVerify(subjectToken string,
	request *SinglePermissionVerifyRequest) (*Token, *SinglePermissionVerifyResult, error) {
	// 区分是v1 version 还是v2 version
	var authVersion string
	if request.Auth != nil {
		authorization := request.Auth.Authorization
		authVersion = GetAuthVersion(authorization)
	} else {
		authVersion = auth.BCE_AUTH_V1
	}

	switch authVersion {
	case auth.BCE_AUTH_V1, auth.BAIDU_INT_AUTH_V1:
		//v1版本签名直接远程
		return c.PermissionVerifyRemote(subjectToken, request) // use v1 interface
	case auth.BCE_AUTH_V2:
		//v2签名走这里
		return c.PermissionVerifyV2(subjectToken, request)
	default:
		return nil, nil, errors.New("signRequest auth version is unknown")
	}
}

// v2签名认证鉴权，先从本地缓存中获取signingkey和token,都有则进行本地认证，然后看是否有verify_result缓存，没有则远端鉴权；否则远程认证+远程鉴权
func (c *IAMClient) PermissionVerifyV2(subjectToken string,
	request *SinglePermissionVerifyRequest) (*Token, *SinglePermissionVerifyResult, error) {
	// 获取v2 signchecker
	checker, err := NewSignCheckerV2(request.Auth.Authorization)
	if err != nil {
		logs.Infof("create signchecker failed: %v", err)
		return nil, nil, err
	}
	// 查看是否有signingkey缓存
	scope := checker.Scope()
	signingKey := c.getSigningKeyFromCache(scope)
	var token *Token
	// 查看是否有token缓存
	ak := checker.Ak()
	token = c.getTokenFromCache(ak)
	contextKey := request.GetContextKey(ak)
	// 未命中缓存的话， 去iam远程用户认证和鉴权接口认证
	if signingKey == "" || token == nil {
		logs.Infof("from iam server to permission verify, reqID: %s", request.RequestId)
		token, verifyResult, err := c.PermissionVerifyRemote(subjectToken, request)
		if err != nil && token != nil || verifyResult != nil {
			// 添加结果到cache
			c.addTokenToCache(ak, token)
			c.addVerifyResultToCache(contextKey, verifyResult)
		}
		// 返回结果
		return token, verifyResult, err
	}

	if logs.Check(logs.V(9)) {
		body, _ := json.Marshal(request)
		logs.Infof("Request permission verify local %s", string(body))
	}

	signingKey = c.getSigningKeyFromCache(scope)
	// 进行本地认证
	err = checker.AuthenticationBySigningKey(request.Auth, signingKey)
	if err != nil {
		logs.Infof("local authorization by cached signingKey failed。 reqestID:%s, signingKey:%s", request.RequestId, signingKey)
		return nil, nil, err
	}

	verifyResult := c.getVerifyResultFromCache(contextKey)
	if verifyResult != nil {
		// 获取到缓存 直接从缓存取数据并返回
		logs.Debugf("local authorization success reqestID:%s", request.RequestId)
		return token, verifyResult, nil
	}

	// 未命中缓存,远程iam鉴权
	verifyResult, err = c.PermissionVerifyWithUserID(token.User.ID, request)
	if err != nil {
		logs.Infof("get verify result from iam failed: userID: %s, requestID: %s", token.User.ID, request)
		return nil, nil, err
	}
	// add to cache
	c.addVerifyResultToCache(contextKey, verifyResult)
	return token, verifyResult, nil
}

type batchVerifyRequest struct {
	Requests []*MultiplePermissionVerifyRequest `json:"verify_list"`
}
type batchVerifyResults struct {
	Results []*MultiplePermissionVerifyResult `json:"verify_results"`
}

func (c *IAMClient) BatchPermissionVerify(userid, requestId string,
	requests []*MultiplePermissionVerifyRequest) ([]*MultiplePermissionVerifyResult, error) {
	verifyReq := batchVerifyRequest{}
	verifyReq.Requests = requests
	body := new(bytes.Buffer)
	err := json.NewEncoder(body).Encode(verifyReq)
	if err != nil {
		return nil, err
	}
	uri := fmt.Sprintf("/v3/users/%s/batch_permissions", userid)
	req, _ := c.newRequest(http.MethodPost, uri, body)
	c.addContentJsonHeader(req)
	err = c.addAuthHeader(req)
	if err != nil {
		return nil, err
	}
	c.addRequestIDHeader(req, requestId)
	rsp, err := c.doRequest(req)
	if err != nil {
		return nil, err
	}
	defer rsp.Body.Close()
	if rsp.StatusCode != http.StatusOK {
		iamerr := &IAMError{}
		json.NewDecoder(rsp.Body).Decode(iamerr)
		if rsp.StatusCode == http.StatusUnauthorized {
			c.refreshToken()
		}
		return nil, iamerr
	}
	result := batchVerifyResults{}
	err = json.NewDecoder(rsp.Body).Decode(&result)
	if err != nil {
		return nil, err
	}
	return result.Results, nil
}

func (c *IAMClient) AssumeRole(userid, roleName, requestId string, duration int) (*sts_credential.StsCredential, error) {
	params := url.Values{}
	params.Add("assumeRole", "")
	params.Add("accountId", userid)
	params.Add("durationSeconds", strconv.Itoa(duration))
	params.Add("roleName", roleName)
	uri := fmt.Sprintf("/v1/credential?%s", params.Encode())

	req, _ := c.newStsRequest(http.MethodPost, uri, nil)
	req.Header.Set("Host", req.URL.Host)
	c.addAuthorizationHeader(req)
	c.addRequestIDHeader(req, requestId)

	rsp, err := c.doRequest(req)
	if err != nil {
		return nil, err
	}
	defer rsp.Body.Close()
	if rsp.StatusCode != http.StatusOK {
		iamerr := &IAMError{}
		json.NewDecoder(rsp.Body).Decode(iamerr)
		if rsp.StatusCode == http.StatusUnauthorized {
			c.refreshToken()
		}
		return nil, iamerr
	}

	result := &sts_credential.StsCredential{}
	err = json.NewDecoder(rsp.Body).Decode(&result)
	if err != nil {
		return nil, err
	}
	return result, nil
}

func (c *IAMClient) AssumeRoleAcl(userid, roleName, requestId string, duration int, body string) (*sts_credential.StsCredential, error) {
	params := url.Values{}
	params.Add("assumeRole", "")
	params.Add("accountId", userid)
	params.Add("durationSeconds", strconv.Itoa(duration))
	params.Add("roleName", roleName)
	uri := fmt.Sprintf("/v1/credential?%s", params.Encode())

	req, _ := c.newStsRequest(http.MethodPost, uri, bytes.NewBuffer([]byte(body)))
	req.Header.Set("Host", req.URL.Host)
	req.Header.Set("Content-Type", "application/json;charset=UTF-8")

	c.addAuthorizationHeader(req)
	c.addRequestIDHeader(req, requestId)

	rsp, err := c.doRequest(req)
	if err != nil {
		return nil, err
	}
	defer rsp.Body.Close()
	if rsp.StatusCode != http.StatusOK {
		iamerr := &IAMError{}
		json.NewDecoder(rsp.Body).Decode(iamerr)
		if rsp.StatusCode == http.StatusUnauthorized {
			c.refreshToken()
		}
		return nil, iamerr
	}

	result := &sts_credential.StsCredential{}
	err = json.NewDecoder(rsp.Body).Decode(&result)
	if err != nil {
		return nil, err
	}
	return result, nil
}

func (c *IAMClient) GetAccount(name string) (*UserAccount, error) {
	url := fmt.Sprintf("/v3/accounts/%s", name)
	req, _ := c.newRequest(http.MethodGet, url, nil)
	err := c.addAuthHeader(req)
	if err != nil {
		return nil, err
	}
	rsp, err := c.doRequest(req)
	if err != nil {
		return nil, err
	}
	defer rsp.Body.Close()
	data := &UserAccount{}
	err = json.NewDecoder(rsp.Body).Decode(data)
	if err != nil {
		return nil, err
	}
	return data, nil
}

func (c *IAMClient) getSigningKeyFromCache(scope string) string {
	if scope == "" {
		return ""
	}
	value, existed := c.signingKeyCache.Get(scope)
	if !existed {
		return ""
	}

	if signingkey, ok := value.(string); !ok {
		c.signingKeyCache.Remove(scope)
		return ""
	} else {
		return signingkey
	}

}

func (c *IAMClient) getTokenFromCache(ak string) *Token {
	if ak == "" {
		return nil
	}

	value, existed := c.tokenCache.Get(ak)
	if !existed || value == nil {
		return nil
	} else {
		//确保token没有超时
		token, ok := value.(*Token)
		if !ok || token.ExpiresAt.Before(time.Now()) {
			c.tokenCache.Remove(ak)
			return nil
		}
		return token
	}
}

func (c *IAMClient) getVerifyResultFromCache(contextKey string) *SinglePermissionVerifyResult {

	if contextKey == "" {
		return nil
	}

	value, existed := c.verifyResultCache.Get(contextKey)
	if !existed || value == nil {
		return nil
	}

	//命中缓存
	if result, ok := value.(*SinglePermissionVerifyResult); !ok {
		c.verifyResultCache.Remove(contextKey)
		return nil
	} else {
		return result
	}

}

func (c *IAMClient) addTokenToCache(ak string, token *Token) {
	c.tokenCache.Add(ak, token, time.Duration(c.config.CacheTTL)*time.Second)
}

func (c *IAMClient) addSingingKeyToCache(scope string, signingKey string) {
	c.signingKeyCache.Add(scope, signingKey, time.Duration(c.config.CacheTTL)*time.Second)
}

func (c *IAMClient) addVerifyResultToCache(contextKey string, result *SinglePermissionVerifyResult) {
	c.verifyResultCache.Add(contextKey, result, time.Duration(c.config.CacheTTL)*time.Second)
}

func (c *IAMClient) newRequest(method, uri string, body io.Reader) (*http.Request, error) {
	return http.NewRequest(method, c.endpoint+uri, body)
}

func (c *IAMClient) newStsRequest(method, uri string, body io.Reader) (*http.Request, error) {
	return http.NewRequest(method, c.stsEndpoint+uri, body)
}

func (c *IAMClient) doRequest(req *http.Request) (*http.Response, error) {
	return c.client.Do(req)
}
