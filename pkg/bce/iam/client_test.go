package iam

import (
	"fmt"
	"net/http"
	"net/url"
	"os"
	"testing"
	"time"

	boscli "github.com/baidubce/bce-sdk-go/services/bos"
	"github.com/stretchr/testify/assert"

	"icode.baidu.com/baidu/faas/kun/pkg/bce/auth"
	"icode.baidu.com/baidu/faas/kun/pkg/util/json"
)

func TestLoadConfig(t *testing.T) {
	if os.Getenv("TestWithIAM") == "" {
		t.Skip("skip TestLoadConfig")
	}
	config, err := loadConfig("./notexist.yaml")
	if err == nil {
		t.Error(config)
	}

	config, err = loadConfig("./test.yaml")
	if err != nil {
		t.Error(err)
	}
	fmt.Println(config)
	if config.Username != "cfc" ||
		config.Password != "l8DkXZlOhvQdpeh69aTh26QryCTLFySv" ||
		config.DefaultDomain != "default" ||
		config.SubUserSupport != true ||
		config.AuthMethod != "password" ||
		config.TokenCacheSize != 1000 ||
		config.SecrectCacheSize != 1000 ||
		config.ActiveCacheSize != 1000 {
		t.Error(config)
	}
}

func TestCreateIAMClient(t *testing.T) {
	client, err := CreateIAMClient("./test.yaml")
	if client == nil {
		t.Error(err)
	}
	tocken, _ := client.ServiceToken()
	to, err := client.AuthenticateWithToken(tocken.ID, "")
	if err != nil {
		t.Error(err)
	}
	fmt.Println(to)
}

func TestGetIAMClient(t *testing.T) {
	if os.Getenv("TestWithIAM") == "" {
		t.Skip("skip TestGetIAMClient")
	}
	client, err := CreateIAMClient("./test.yaml")
	if client == nil {
		t.Error(err)
	}
}

func TestRefreshToken(t *testing.T) {
	if os.Getenv("TestWithIAM") == "" {
		t.Skip("skip TestRefreshToken")
	}
	client := createTestClient(t, "./test.yaml")
	for i := 1; i < 10; i++ {
		client.refreshToken()
	}
	userid, err := client.serviceUserID()
	if err != nil {
		t.Error(err)
	}
	keys, err := client.getAccessKeys(userid)
	if err != nil {
		t.Error(err)
	}
	for _, k := range keys {
		t.Log(k)
	}
	fmt.Println(client.ServiceAuth())
	fmt.Println(client.ServiceToken())
}

func TestServiceTokenRefresh(t *testing.T) {
	if os.Getenv("TestWithIAM") == "" {
		t.Skip("skip TestServiceTokenRefresh")
	}
	client := createTestClient(t, "./test.yaml")
	select {
	case <-time.After(5 * time.Second):
		client.refreshCtrl <- "exit"
	}
	// fmt.Println("TestServiceTokenRefresh")
	time.Sleep(1 * time.Second)
	v, ok := <-client.refreshCtrl
	if ok {
		fmt.Println(v)
		t.Error("refreshCtrl channel should be closed")
	}
}

func TestAuthenticateWithHttp(t *testing.T) {
	if os.Getenv("TestWithIAM") == "" {
		t.Skip("skip TestAuthenticateWithHttp")
	}
	request, _ := http.NewRequest(http.MethodGet, "http://www.baidu.com/path/exist?pa=va&p1=p2", nil)
	request.Header.Add("x-bce-h1", "v3")
	request.Header.Add("Host", request.Host)
	authorization := auth.NewBceAuth(
		"77b3184c8da340d0954eee0acc247f57",
		"4e24782423d342128636bb26773c01fe").
		NewSigner().
		Method(http.MethodGet).
		Path(request.URL.Path).
		Params(request.URL.Query()).
		Headers(request.Header).
		WithSignedHeader().
		Expire(1800).GetSign()
	request.Header.Add("Authorization", authorization)
	authreq := NewAuthRequestByHttp(request)
	client, _ := CreateIAMClient("./test.yaml")
	token, err := client.AuthenticateWithAKSK(authreq)
	if err != nil {
		t.Error(err)
	}
	tmp, err := json.Marshal(token)
	fmt.Println("http request token: ", string(tmp))
}

// AK: 77b3184c8da340d0954eee0acc247f57
// SK: 4e24782423d342128636bb26773c01fe
func TestAuthenticateWithAKSK(t *testing.T) {
	if os.Getenv("TestWithIAM") == "" {
		t.Skip("skip TestAuthenticateWithAKSK")
	}
	request := AuthenticationRequest{}
	params := url.Values{}
	params.Add("pa", "va")
	headers := http.Header{}
	headers.Add("Host", "127.0.0.1")
	fmt.Println(headers)
	request.Request.WithMethod(http.MethodPost).
		WithUri("/a/b/c").WithParams(params).WithHeader(headers)

	request.Authorization = auth.NewBceAuth(
		"77b3184c8da340d0954eee0acc247f57",
		"4e24782423d342128636bb26773c01fe").
		NewSigner().Method(http.MethodPost).
		Path("/a/b/c").Params(params).Headers(headers).Expire(1800).
		WithSignedHeader().GetSign()
	client, _ := CreateIAMClient("./test.yaml")
	token, err := client.AuthenticateWithAKSK(&request)
	if err != nil {
		t.Error(err)
	}
	tmp, err := json.Marshal(token)
	fmt.Println("token: ", string(tmp))

	token2, err := client.AuthenticateWithToken(token.ID, "")
	if err != nil {
		t.Error(err)
	}
	tmp, err = json.Marshal(token2)
	fmt.Println("token2: ", string(tmp))

	// test permission verify with token
	verifyReq := SinglePermissionVerifyRequest{}
	verifyReq.WithService("bce:bcc").WithRegion("bj").
		WithResource("1d9c4ad3-a9f3-4ff5-945d-7fdf782ba63c").
		AddPermission("VM_READ").
		WithOwner("85ae1423f09f4660becb15d46402e9cd")
	token3, result, err := client.PermissionVerify(token.ID, &verifyReq)
	if err != nil {
		t.Error(err)
	}
	tmp, _ = json.Marshal(token3)
	fmt.Println("token3: ", string(tmp))
	tmp, _ = json.Marshal(result)
	fmt.Println("result: ", string(tmp))

	verifyReq = SinglePermissionVerifyRequest{}
	verifyReq.WithService("bce:bcc").WithRegion("bj").
		WithResource("1d9c4ad3-a9f3-4ff5-945d-7fdf782ba63c").
		AddPermission("VM_READ").
		WithOwner("85ae1423f09f4660becb15d46402e9cd").
		WithAuth(&request)
	token4, result, err := client.PermissionVerify("", &verifyReq)
	if err != nil {
		t.Error(err)
	}
	tmp, _ = json.Marshal(token4)
	fmt.Println("token4: ", string(tmp))
	tmp, _ = json.Marshal(result)
	fmt.Println("result: ", string(tmp))
}

// UserID: 85ae1423f09f4660becb15d46402e9cd
func TestPermissionVerifyWithUserID(t *testing.T) {
	if os.Getenv("TestWithIAM") == "" {
		t.Skip("skip TestPermissionVerifyWithUserID")
	}
	client, _ := CreateIAMClient("./test.yaml")
	verifyReq := SinglePermissionVerifyRequest{}
	verifyReq.WithService("bce:bcc").WithRegion("bj").
		WithResource("1d9c4ad3-a9f3-4ff5-945d-7fdf782ba63c").
		AddPermission("VM_READ").
		WithOwner("85ae1423f09f4660becb15d46402e9cd")
	result, err := client.PermissionVerifyWithUserID(
		"85ae1423f09f4660becb15d46402e9cd", &verifyReq)
	if err != nil {
		t.Error(err)
	}
	data, _ := json.Marshal(result)
	fmt.Println(string(data))
}

func TestBatchPermissionVerify(t *testing.T) {
	if os.Getenv("TestWithIAM") == "" {
		t.Skip("skip TestBatchPermissionVerify")
	}
	client, _ := CreateIAMClient("./test.yaml")
	verifyReq1 := MultiplePermissionVerifyRequest{}
	verifyReq1.WithService("bce:bcc").WithRegion("bj").
		AddResource("1d9c4ad3-a9f3-4ff5-945d-7fdf782ba63c").
		AddPermission("VM_READ").
		WithOwner("85ae1423f09f4660becb15d46402e9cd")
	verifyReq2 := MultiplePermissionVerifyRequest{}
	verifyReq2.WithService("bce:bcc").WithRegion("bj").
		AddResource("1d9c4ad3-a9f3-4ff5-945d-7fdf782ba63c").
		AddPermission("VM_READ").
		WithOwner("85ae1423f09f4660becb15d46402e9cd")
	reqs := []*MultiplePermissionVerifyRequest{
		&verifyReq1,
		&verifyReq2,
	}
	result, err := client.BatchPermissionVerify("85ae1423f09f4660becb15d46402e9cd", "", reqs)
	if err != nil {
		t.Error(err)
	}
	data, _ := json.Marshal(result)
	fmt.Println(string(data))
}

func TestAssumeRole(t *testing.T) {
	if os.Getenv("TestWithIAM2") == "" {
		t.Skip("skip TestAssumeRole")
	}
	client, _ := CreateIAMClient("./online.yaml")
	stsToken, err := client.AssumeRole(
		"78327ba7ba624869ac5cc197aaf6c375", "BceServiceRole_cfc", "", 3600)
	if err != nil {
		t.Error(err)
	}
	fmt.Println(stsToken)

	// 使用STS Token获取BOS数据
	bosClient, err := boscli.NewClient(stsToken.AccessKeyId, stsToken.AccessKeySecret, "bos-sandbox-bj.baidu-int.com")
	if err != nil {
		t.Fatal(err)
	}
	bosClient.Config.Credentials.SessionToken = stsToken.SessionToken // 设置sts token
	lists, err := bosClient.ListBuckets()
	if err != nil {
		t.Error(err)
	}
	data, _ := json.Marshal(lists)
	fmt.Println(string(data))
}

func TestAssumeRoleAcl(t *testing.T) {
	client, _ := CreateIAMClient("./sandbox.yaml")
	_, err := client.AssumeRoleAcl(
		"c7ac82ae14ef42d1a4ffa3b2ececa17f", "BceServiceRole_cfc", "", 3600, `{
    "id":"bos_access",
    "accessControlList":[
        {
            "service":"bce:bos",
            "region":"*",
            "resource":[
                "*"
            ],
            "effect":"Allow",
            "permission":[
                "FULL_CONTROL"
            ],
            "eid":"bos_access"
        }
    ]
}`)
	if err != nil {
		t.Error(err)
	}
}

func TestPermissionVerifyRemote(t *testing.T) {
	ak := "********************************"
	sk := "c13b18ad42a6405db7da9ced3bee40d2"

	// 测试v1签名
	verifyReq := getTestVerifyV1Request(ak, sk)
	client, _ := CreateIAMClient("./sandbox.yaml")

	token, result, err := client.PermissionVerify("", verifyReq)
	if err != nil {
		t.Error(err)
	}
	data, _ := json.Marshal(result)
	t.Log(string(data))
	tokenjson, _ := json.Marshal(token)
	t.Log(string(tokenjson))
	//测试v2签名
	verifyReq1 := getTestVerifyV2Request(ak, sk)

	token, result, err = client.PermissionVerify("", verifyReq1)
	if err != nil {
		t.Error(err)
	}
	data, _ = json.Marshal(result)
	t.Log(string(data))
	tokenjson, _ = json.Marshal(token)
	t.Log(string(tokenjson))
}

func TestPermissionVerify(t *testing.T) {
	ak := "********************************"
	sk := "c13b18ad42a6405db7da9ced3bee40d2"

	// 测试v1签名
	verifyReq := getTestVerifyV1Request(ak, sk)
	client, _ := CreateIAMClient("./sandbox.yaml")

	token, result, err := client.PermissionVerify("", verifyReq)
	if err != nil {
		t.Error(err)
	}
	data, _ := json.Marshal(result)
	t.Log(string(data))
	tokenjson, _ := json.Marshal(token)
	t.Log(string(tokenjson))
	//测试v2签名 第一次 走远程认证鉴权
	verifyReqFirst := getTestVerifyV2Request(ak, sk)

	token, result, err = client.PermissionVerify("", verifyReqFirst)
	if err != nil {
		t.Error(err)
	}
	data, _ = json.Marshal(result)
	t.Log(string(data))
	tokenjson, _ = json.Marshal(token)
	t.Log(string(tokenjson))
	//测试v2签名 第二次 走本地签名
	verifyReqSecond := getTestVerifyV2Request(ak, sk)

	token, result, err = client.PermissionVerify("", verifyReqSecond)
	if err != nil {
		t.Error(err)
	}
	data, _ = json.Marshal(result)
	t.Log(string(data))
	tokenjson, _ = json.Marshal(token)
	t.Log(string(tokenjson))
}

func TestPermissionVerifyV2(t *testing.T) {
	ak := "********************************"
	sk := "c13b18ad42a6405db7da9ced3bee40d2"
	client, _ := CreateIAMClient("./sandbox.yaml")

	// 第一次v1 verify request
	verifyReqFirst := getTestVerifyV2Request(ak, sk)

	token, result, err := client.PermissionVerify("", verifyReqFirst)
	if err != nil {
		t.Error(err)
	}
	data, _ := json.Marshal(result)
	t.Log(string(data))
	tokenjson, _ := json.Marshal(token)
	t.Log(string(tokenjson))
	//测试v2签名 第二次 走本地签名
	verifyReqSecond := getTestVerifyV2Request(ak, sk)

	token, result, err = client.PermissionVerify("", verifyReqSecond)
	if err != nil {
		t.Error(err)
	}
	data, _ = json.Marshal(result)
	t.Log(string(data))
	tokenjson, _ = json.Marshal(token)
	t.Log(string(tokenjson))

	// 异常case v1签名走进去了
	verifyReq := getTestVerifyV1Request(ak, sk)
	token, result, err = client.PermissionVerifyV2("", verifyReq)
	assert.NotNil(t, err)

	// 异常case request time out
	verifyReq = getTestVerifyV2Request(ak, sk)
	headers := http.Header{}
	headers.Add("Host", "127.0.0.1")
	dateStr := "2020-04-19T02:35:33Z"
	headers.Add("x-bce-date", dateStr)
	verifyReq.Auth.Request.WithHeader(headers)
	token, result, err = client.PermissionVerifyV2("", verifyReq)
	assert.NotNil(t, err)
}

func TestBatchPermissionVerifySandbox(t *testing.T) {
	client, _ := CreateIAMClient("./sandbox.yaml")
	verifyReq1 := MultiplePermissionVerifyRequest{}
	verifyReq1.WithService("bce:cfc").WithRegion("bj").
		AddResource("*").
		AddPermission("InvokeFunction").
		WithOwner("")
	reqs := []*MultiplePermissionVerifyRequest{
		&verifyReq1,
	}
	result, err := client.BatchPermissionVerify("42f6fbc2cd374bfcb80d9967370fd8ff", "", reqs)
	// "IAM request error. requestID:53d5d69a-82b0-11ea-81c1-f0189850388a, code:NotFound, message:Could not find credential"
	assert.NotNil(t, err)

	// requestID:
	result, err = client.BatchPermissionVerify("c7ac82ae14ef42d1a4ffa3b2ececa17f", "", reqs)
	assert.Nil(t, err)
	data, _ := json.Marshal(result)
	t.Logf(string(data))
}

func TestAuthenticateWithAKSKV1(t *testing.T) {
	ak := "********************************"
	sk := "c13b18ad42a6405db7da9ced3bee40d2"
	request := getTestAuthV1Request(ak, sk)
	client, _ := CreateIAMClient("./sandbox.yaml")
	token, err := client.AuthenticateWithAKSK(request)
	if err != nil {
		t.Error(err)
	}
	tmp, err := json.Marshal(token)
	t.Logf("token: :%s", string(tmp))

	token2, err := client.AuthenticateWithToken(token.ID, "")
	if err != nil {
		t.Error(err)
	}
	tmp, err = json.Marshal(token2)
	t.Logf("token2: %s", string(tmp))

	// test permission verify with token
	verifyReq := SinglePermissionVerifyRequest{}
	verifyReq.WithService("bce:bcc").WithRegion("bj").
		WithResource("1d9c4ad3-a9f3-4ff5-945d-7fdf782ba63c").
		AddPermission("VM_READ").
		WithOwner("85ae1423f09f4660becb15d46402e9cd")
	token3, result, err := client.PermissionVerify(token.ID, &verifyReq)
	if err != nil {
		t.Error(err)
	}
	tmp, _ = json.Marshal(token3)
	t.Logf("token3: %s ", string(tmp))
	tmp, _ = json.Marshal(result)
	t.Logf("result: %s", string(tmp))

	verifyReq = SinglePermissionVerifyRequest{}
	verifyReq.WithService("bce:bcc").WithRegion("bj").
		WithResource("1d9c4ad3-a9f3-4ff5-945d-7fdf782ba63c").
		AddPermission("VM_READ").
		WithOwner("85ae1423f09f4660becb15d46402e9cd").
		WithAuth(request)
	token4, result, err := client.PermissionVerify("", &verifyReq)
	if err != nil {
		t.Error(err)
	}
	tmp, _ = json.Marshal(token4)
	t.Logf("token4: %s ", string(tmp))
	tmp, _ = json.Marshal(result)
	t.Logf("result: %s ", string(tmp))
}

func TestAuthenticateWithAKSKV2(t *testing.T) {
	ak := "********************************"
	sk := "c13b18ad42a6405db7da9ced3bee40d2"
	request := getTestAuthV2Request(ak, sk)
	client, err := CreateIAMClient("./sandbox.yaml")
	if err != nil {
		t.Error(err)
	}
	token, err := client.AuthenticateWithAKSK(request)
	if err != nil {
		t.Error(err)
	}
	tmp, err := json.Marshal(token)
	t.Logf("token: %s", string(tmp))

	token2, err := client.AuthenticateWithToken(token.ID, "")
	if err != nil {
		t.Error(err)
	}
	tmp, err = json.Marshal(token2)
	t.Logf("token2: %s", string(tmp))

	// test permission verify with token
	verifyReq := SinglePermissionVerifyRequest{}
	verifyReq.WithService("bce:bcc").WithRegion("bj").
		WithResource("1d9c4ad3-a9f3-4ff5-945d-7fdf782ba63c").
		AddPermission("VM_READ").
		WithOwner("85ae1423f09f4660becb15d46402e9cd")
	token3, result, err := client.PermissionVerify(token.ID, &verifyReq)
	if err != nil {
		t.Error(err)
	}
	tmp, _ = json.Marshal(token3)
	t.Logf("token3: %s", string(tmp))
	tmp, _ = json.Marshal(result)
	t.Logf("result: %s", string(tmp))

	verifyReq = SinglePermissionVerifyRequest{}
	verifyReq.WithService("bce:bcc").WithRegion("bj").
		WithResource("1d9c4ad3-a9f3-4ff5-945d-7fdf782ba63c").
		AddPermission("VM_READ").
		WithOwner("85ae1423f09f4660becb15d46402e9cd").
		WithAuth(request)
	token4, result, err := client.PermissionVerify("", &verifyReq)
	if err != nil {
		t.Error(err)
	}
	tmp, _ = json.Marshal(token4)
	t.Logf("token4: %s ", string(tmp))
	tmp, _ = json.Marshal(result)
	t.Logf("result: %s ", string(tmp))
}

func getTestAuthV2Request(ak string, sk string) *AuthenticationRequest {
	request := AuthenticationRequest{}
	params := url.Values{}
	params.Add("pa", "va")
	headers := http.Header{}
	headers.Add("Host", "127.0.0.1")
	dateStr := auth.GetCanonicalTime(time.Now().UTC())
	headers.Add("x-bce-date", dateStr)
	request.Request.WithMethod(http.MethodPost).
		WithUri("/a/b/c").WithParams(params).WithHeader(headers)

	request.Authorization = auth.NewBceAuth(
		ak, sk).
		NewSignerV2("bj", "cfc").Method(http.MethodPost).
		Path("/a/b/c").Params(params).Headers(headers).Expire(1800).
		WithSignedHeader().GetSign()
	return &request
}

func getTestAuthV1Request(ak string, sk string) *AuthenticationRequest {
	request := AuthenticationRequest{}
	params := url.Values{}
	params.Add("pa", "va")
	headers := http.Header{}
	headers.Add("Host", "127.0.0.1")
	request.Request.WithMethod(http.MethodPost).
		WithUri("/a/b/c").WithParams(params).WithHeader(headers)

	request.Authorization = auth.NewBceAuth(ak, sk).
		NewSigner().Method(http.MethodPost).
		Path("/a/b/c").Params(params).Headers(headers).Expire(1800).
		WithSignedHeader().GetSign()
	return &request
}

func getTestVerifyV1Request(ak string, sk string) *SinglePermissionVerifyRequest {
	request := AuthenticationRequest{}
	params := url.Values{}
	params.Add("pa", "va")
	headers := http.Header{}
	headers.Add("Host", "127.0.0.1")
	dateStr := auth.GetCanonicalTime(time.Now().UTC())
	headers.Add("x-bce-date", dateStr)
	request.Request.WithMethod(http.MethodPost).
		WithUri("/a/b/c").WithParams(params).WithHeader(headers)

	request.Authorization = auth.NewBceAuth(
		ak,
		sk).
		NewSigner().Method(http.MethodPost).
		Path("/a/b/c").Params(params).Headers(headers).Expire(1800).
		WithSignedHeader().GetSign()

	verifyReq := SinglePermissionVerifyRequest{}
	verifyReq.WithService("bce:cfc").WithRegion("bj").
		WithResource("*").
		AddPermission("InvokeFunction").
		WithOwner("").
		WithAuth(&request)
	return &verifyReq
}

func getTestVerifyV2Request(ak string, sk string) *SinglePermissionVerifyRequest {
	request := AuthenticationRequest{}
	params := url.Values{}
	params.Add("pa", "va")
	headers := http.Header{}
	headers.Add("Host", "127.0.0.1")
	dateStr := auth.GetCanonicalTime(time.Now().UTC())
	headers.Add("x-bce-date", dateStr)
	request.Request.WithMethod(http.MethodPost).
		WithUri("/a/b/c").WithParams(params).WithHeader(headers)

	request.Authorization = auth.NewBceAuth(
		ak, sk).
		NewSignerV2("bce:cfc", "cfc").Method(http.MethodPost).
		Path("/a/b/c").Params(params).Headers(headers).Expire(1800).
		WithSignedHeader().GetSign()

	verifyReq := SinglePermissionVerifyRequest{}
	verifyReq.WithService("bce:cfc").WithRegion("bj").
		WithResource("*").
		AddPermission("InvokeFunction").
		WithOwner("").
		WithAuth(&request)
	return &verifyReq
}

func TestSingingKeyCache(t *testing.T) {
	client := createTestClient(t, "./sandbox.yaml")
	scope := "bce-auth-v2/********************************/20200417/bj/cfc"
	singkingKey := "19151b533af91102b5bc1a556231564041eba3e6b2b0f0cb0a14585bd957639b"

	client.addSingingKeyToCache(scope, singkingKey)
	get := client.getSigningKeyFromCache(scope)
	assert.Equal(t, singkingKey, get)
}

func TestTokenCache(t *testing.T) {
	client := createTestClient(t, "./sandbox.yaml")
	ak := "********************************"
	token := Token{
		ID:        "bbb",
		ExpiresAt: time.Now().Add(time.Duration(200000000)),
	}
	client.addTokenToCache(ak, &token)
	get := client.getTokenFromCache(ak)
	assert.Equal(t, token, *get)
}

func TestVerifyCache(t *testing.T) {
	client := createTestClient(t, "./sandbox.yaml")
	contextKey := "********************************"
	verifyResult := SinglePermissionVerifyResult{
		Id:     "xxxx",
		Effect: PermissionAllow,
	}
	client.addVerifyResultToCache(contextKey, &verifyResult)
	get := client.getVerifyResultFromCache(contextKey)
	assert.Equal(t, verifyResult, *get)
}

func getTestReq(ak string, sk string) *SinglePermissionVerifyRequest {
	request := AuthenticationRequest{}
	params := url.Values{}
	headers := http.Header{}
	headers.Add("Host", "127.0.0.1:8601")
	dateStr := auth.GetCanonicalTime(time.Now().UTC())
	headers.Add("x-bce-date", dateStr)
	headers.Add("Accept-Encoding", "gzip")
	headers.Add("App", "eventhub")
	headers.Add("Content-Length", "2")
	headers.Add("User-Agent", "Go-http-client/1.1")
	headers.Add("Content-Type", "application/json")
	headers.Add("X-Amz-Invocation-Type", "RequestResponse")
	headers.Add("X-Auth-Token", "cfc-auth-2018")
	headers.Add("X-Bce-Faas-Trigger", "generic")
	headers.Add("X-Bce-Request-Id", "620b0caa-3c90-4891-a1c7-785da3f3ad08")

	request.Request.WithMethod(http.MethodPost).
		WithUri("/v1/functions/test0420/invocations").WithParams(params).WithHeader(headers)

	request.Authorization = auth.NewBceAuth(
		ak, sk).
		NewSignerV2("bj", "cfc").Method(http.MethodPost).
		Path("/v1/functions/test0420/invocations").Params(params).Headers(headers).Expire(3600).
		WithSignedHeader().GetSign()

	verifyReq := SinglePermissionVerifyRequest{}
	verifyReq.WithService("bce:cfc").WithRegion("bj").
		WithResource("*").
		AddPermission("InvokeFunction").
		WithOwner("").
		WithAuth(&request)
	return &verifyReq
}

func createTestClient(t *testing.T, path string) *IAMClient {
	c, err := CreateIAMClient("./sandbox.yaml")
	if err != nil {
		t.Error(err)
	}
	return c.(*IAMClient)
}
