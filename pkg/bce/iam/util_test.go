package iam

import (
	"testing"

	"github.com/stretchr/testify/assert"

	"icode.baidu.com/baidu/faas/kun/pkg/bce/auth"
)

func TestCheckHeader(t *testing.T) {
	headers := map[string]string{
		"Authorization":    "bce-auth-v1/a3fc33602e91420b9f277d2e92422db4/2020-04-21T02:56:11Z/3600/host/458d239372f039634475b2d3834525b14ab0761ef673756344d044c2d682e45b",
		"Host":             "iam.bj.bce-internal.baidu.com",
		"X-Bce-Date":       "2020-04-21T02:56:11Z",
		"X-Bce-Request-Id": "db695be5-227b-4e5a-9d33-b5ad6a587da8",
	}
	signedHeader := "host;x-bce-date"
	result := CheckHeader(headers, signedHeader)
	assert.Equal(t, true, result)

}

func TestGetAuthV2Scope(t *testing.T) {
	authStr := "bce-auth-v2/5d4e433531d14d2f91de2dc576368a51/20200417/bj/cfc/host;x-bce-date;x-bce-expiration/574130d8a60e8e1a9245a35fdfee9d03447d42032bfd2890b446487957bde907"
	scope := GetAuthV2Scope(authStr)
	expect := "bce-auth-v2/5d4e433531d14d2f91de2dc576368a51/20200417/bj/cfc"
	assert.Equal(t, expect, scope)

}

func TestGetAuthVersion(t *testing.T) {
	authStr := "bce-auth-v2/5d4e433531d14d2f91de2dc576368a51/20200417/bj/cfc/host;x-bce-date;x-bce-expiration/574130d8a60e8e1a9245a35fdfee9d03447d42032bfd2890b446487957bde907"
	version := GetAuthVersion(authStr)
	assert.Equal(t, auth.BCE_AUTH_V2, version)

	authStr = "bce-auth-v1/a3fc33602e91420b9f277d2e92422db4/2020-04-21T02:56:11Z/3600/host/458d239372f039634475b2d3834525b14ab0761ef673756344d044c2d682e45b"
	version = GetAuthVersion(authStr)
	assert.Equal(t, auth.BCE_AUTH_V1, version)

	authStr = "xxx/xyyy"
	version = GetAuthVersion(authStr)
	assert.Equal(t, "", version)

	authStr = "baidu-int-auth-v1/a3fc33602e91420b9f277d2e92422db4/2020-04-21T02:56:11Z/3600/host/458d239372f039634475b2d3834525b14ab0761ef673756344d044c2d682e45b"
	version = GetAuthVersion(authStr)
	assert.Equal(t, auth.BAIDU_INT_AUTH_V1, version)
}
