/*
 * Copyright 2021 Baidu, Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not use this file
 * except in compliance with the License. You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software distributed under the
 * License is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND,
 * either express or implied. See the License for the specific language governing permissions
 * and limitations under the License.
 */

// eni.go - the eni APIs definition supported by the eni service
package eni

import (
	"encoding/hex"
	"fmt"
	"strconv"
	"strings"
	"time"

	"github.com/baidubce/bce-sdk-go/bce"
	"github.com/baidubce/bce-sdk-go/http"
)

func (c *Client) getEncryptdHeader() string {
	data := c.accountId + "/" + time.Now().UTC().Format(time.RFC3339)
	encrypted := AesEncrypt(data, c.encryptionKey)
	return hex.EncodeToString(encrypted)
}

// CreateEni - create an eni with the specific parameters
//
// PARAMS:
//   - args: the arguments to create an eni
//
// RETURNS:
//   - *CreateEniResult: the result of create eni
//   - error: nil if success otherwise the specific error
func (c *Client) CreateEni(uid string, args *CreateEniArgs) (*CreateEniResult, error) {
	if args == nil {
		return nil, fmt.Errorf("The createEniArgs cannot be nil.")
	}

	if err := c.updateSTSCredential(uid); err != nil {
		return nil, err
	}

	result := &CreateEniResult{}
	err := bce.NewRequestBuilder(c).
		WithURL(getURLForEni()).
		WithMethod(http.POST).
		WithBody(args).
		WithHeader(RESOURCE_ACCOUNTID, c.getEncryptdHeader()).
		WithHeader(RESOURCE_SOURCE, "cfc").
		WithResult(result).
		Do()

	return result, err
}

// DeleteEni - delete an eni
//
// PARAMS:
//   - DeleteEniArgs: the arguments to delete an eni
//
// RETURNS:
//   - error: nil if success otherwise the specific error
func (c *Client) DeleteEni(uid string, args *DeleteEniArgs) error {
	if err := c.updateSTSCredential(uid); err != nil {
		return err
	}

	return bce.NewRequestBuilder(c).
		WithURL(getURLForEniId(args.EniId)).
		WithMethod(http.DELETE).
		WithHeader(RESOURCE_ACCOUNTID, c.getEncryptdHeader()).
		WithHeader(RESOURCE_SOURCE, "cfc").
		WithQueryParamFilter("clientToken", args.ClientToken).
		Do()
}

// ListEnis - list all eni with the specific parameters
//
// PARAMS:
//   - args: the arguments to list all eni
//
// RETURNS:
//   - *ListEniResult: the result of list all eni
//   - error: nil if success otherwise the specific error
func (c *Client) ListEni(uid string, args *ListEniArgs) (*ListEniResult, error) {
	if args == nil {
		return nil, fmt.Errorf("The ListEniArgs cannot be nil.")
	}
	if args.MaxKeys == 0 {
		args.MaxKeys = 1000
	}

	if err := c.updateSTSCredential(uid); err != nil {
		return nil, err
	}

	result := &ListEniResult{}
	builder := bce.NewRequestBuilder(c).
		WithURL(getURLForEni()).
		WithMethod(http.GET).
		WithQueryParam("vpcId", args.VpcId).
		WithQueryParamFilter("instanceId", args.InstanceId).
		WithQueryParamFilter("name", args.Name).
		WithQueryParamFilter("marker", args.Marker).
		WithQueryParamFilter("maxKeys", strconv.Itoa(args.MaxKeys))

	if len(args.PrivateIpAddress) != 0 {
		builder.WithQueryParam("privateIpAddress",
			strings.Replace(strings.Trim(fmt.Sprint(args.PrivateIpAddress), "[]"), " ", ",", -1))
	}

	err := builder.WithResult(result).Do()

	return result, err
}

// GetEniDetail - get the eni detail
//
// PARAMS:
//   - eniId: the specific eniId
//
// RETURNS:
//   - *Eni: the eni
//   - error: nil if success otherwise the specific error
func (c *Client) GetEniDetail(uid string, eniId string) (*Eni, error) {
	if eniId == "" {
		return nil, fmt.Errorf("The eniId cannot be empty.")
	}

	if err := c.updateSTSCredential(uid); err != nil {
		return nil, err
	}

	result := &Eni{}
	err := bce.NewRequestBuilder(c).
		WithURL(getURLForEniId(eniId)).
		WithMethod(http.GET).
		WithResult(result).
		Do()

	return result, err
}

// AttachEniInstance - eni attach instance
//
// PARAMS:
//   - args: the arguments to attach instance
//
// RETURNS:
//   - error: nil if success otherwise the specific error
func (c *Client) AttachEniInstance(uid string, args *EniInstance) error {
	if args == nil {
		return fmt.Errorf("The EniInstance cannot be nil.")
	}

	if err := c.updateSTSCredential(uid); err != nil {
		return err
	}

	err := bce.NewRequestBuilder(c).
		WithURL(getURLForEniId(args.EniId)).
		WithMethod(http.PUT).
		WithQueryParam("attach", "").
		WithHeader(RESOURCE_ACCOUNTID, c.getEncryptdHeader()).
		WithHeader(RESOURCE_SOURCE, "cfc").
		WithBody(args).
		WithQueryParamFilter("clientToken", args.ClientToken).
		Do()

	return err
}

// DetachEniInstance - eni detach instance
//
// PARAMS:
//   - args: the arguments to detach instance
//
// RETURNS:
//   - error: nil if success otherwise the specific error
func (c *Client) DetachEniInstance(uid string, args *EniInstance) error {
	if args == nil {
		return fmt.Errorf("The EniInstance cannot be nil.")
	}

	if err := c.updateSTSCredential(uid); err != nil {
		return err
	}

	err := bce.NewRequestBuilder(c).
		WithURL(getURLForEniId(args.EniId)).
		WithMethod(http.PUT).
		WithQueryParam("detach", "").
		WithHeader(RESOURCE_ACCOUNTID, c.getEncryptdHeader()).
		WithHeader(RESOURCE_SOURCE, "cfc").
		WithBody(args).
		WithQueryParamFilter("clientToken", args.ClientToken).
		Do()

	return err
}
