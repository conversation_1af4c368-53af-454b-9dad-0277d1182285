package eni

import (
	"github.com/baidubce/bce-sdk-go/auth"
	"github.com/baidubce/bce-sdk-go/bce"
	"github.com/google/uuid"
	"github.com/spf13/pflag"
	"icode.baidu.com/baidu/faas/kun/pkg/api"
	"icode.baidu.com/baidu/faas/kun/pkg/bce/iam"
	"icode.baidu.com/baidu/faas/kun/pkg/bce/sts_credential"
)

const (
	URI_PREFIX = bce.URI_PREFIX + "v1"

	DEFAULT_ENI = "bcc." + bce.DEFAULT_REGION + ".baidubce.com"

	REQUEST_ENI_URL = "/eni"

	RESOURCE_SOURCE    = "resource-source"
	RESOURCE_ACCOUNTID = "resource-accountId"
)

type Options struct {
	Endpoint          string
	EncryptionKey     string // 用于生成加密 header
	ResourceAccountId string // cfc 的资源账号 id，用于标记自己的服务身份
}

func NewOptions() *Options {
	return &Options{}
}

func (o *Options) AddFlags(fs *pflag.FlagSet) {
	fs.StringVar(&o.Endpoint, "eni-endpoint", o.Endpoint, "ENI 服务的访问地址")
	fs.StringVar(&o.EncryptionKey, "eni-encryption-key", o.EncryptionKey, "访问ENI服务需提前配置的密钥")
	fs.StringVar(&o.ResourceAccountId, "eni-resource-account-id", o.ResourceAccountId, "CFC资源账号ID")
}

// Client of ENI service is a kind of BceClient, so derived from BceClient
type Client struct {
	*bce.BceClient
	encryptionKey string
	accountId     string // cfc 的资源账号 id，用于标记自己的服务身份

	stsCache  sts_credential.Cache
	iamClient iam.ClientInterface
}

func NewClient(o *Options, iamClient iam.ClientInterface) (*Client, error) {
	if len(o.Endpoint) == 0 {
		o.Endpoint = DEFAULT_ENI
	}
	client, err := bce.NewBceClientWithAkSk("a", "b", o.Endpoint)
	if err != nil {
		return nil, err
	}

	c := &Client{
		BceClient:     client,
		encryptionKey: o.EncryptionKey,
		accountId:     o.ResourceAccountId,
		stsCache:      sts_credential.NewLocalCredentialCache(),
		iamClient:     iamClient,
	}
	return c, nil
}

func (c *Client) updateSTSCredential(uid string) error {
	credential := c.stsCache.Get(uid, api.StsRoleName)
	var err error
	if credential == nil {
		// 调用sts服务assumeRole
		credential, err = c.iamClient.AssumeRole(uid, api.StsRoleName, uuid.New().String(), 3600)
		if err != nil {
			return err
		}

		c.stsCache.Set(uid, api.StsRoleName, credential)
	}

	stsCredential, err := auth.NewSessionBceCredentials(
		credential.AccessKeyId,
		credential.AccessKeySecret,
		credential.SessionToken)

	if err != nil {
		return err
	}

	c.Config.Credentials = stsCredential
	return nil
}

func getURLForEni() string {
	return URI_PREFIX + REQUEST_ENI_URL
}

func getURLForEniId(eniId string) string {
	return getURLForEni() + "/" + eniId
}
