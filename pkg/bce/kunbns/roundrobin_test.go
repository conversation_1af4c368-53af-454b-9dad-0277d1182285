package kunbns

import (
	"context"
	"fmt"
	"testing"

	"icode.baidu.com/baidu/gdp/bns"
	"icode.baidu.com/baidu/gdp/bns/packing"
)

type MockBnsClient struct {
}

func (m MockBnsClient) Call(ctx context.Context, req bns.Request, resp bns.Response) (err error) {
	var port int32
	fmt.Println(resp.String())
	hostname := "localhost"
	port = 80
	infos := []*packing.InstanceInfo{
		{
			HostName: &hostname,
			InstanceStatus: &packing.InstanceStatus{
				Port: &port,
			},
		},
	}
	resp = &bns.NamingResponse{
		Resp: packing.LocalNamingResponse{
			InstanceInfo: infos,
		},
	}

	return nil
}

func (m MockBnsClient) Close() error {
	return nil
}

func TestGetBnsClient(t *testing.T) {
	c := GetBnsClient()
	if c == nil {
		t.Error("created failed")
	}
}

func TestGetEndpoint(t *testing.T) {

	c := NewMockBNSClient()

	bnsClient = &MockBnsClient{}

	if _, er := c.GetEndpoint("abc"); er != nil {
		t.Errorf(er.<PERSON>rror())
	}

}
