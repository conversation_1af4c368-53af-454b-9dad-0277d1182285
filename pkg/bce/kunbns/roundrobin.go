package kunbns

import (
	"container/ring"
	"context"
	"fmt"
	"sync"
	"time"

	"icode.baidu.com/baidu/gdp/bns"
)

/*
*

	RRClient implements  GetEndpoint method by round robin

*
*/
type RRClient struct {
	bnsMap map[string]*instanceBuffer // key: BNS name ; value : BNS instance info
	rwLock sync.RWMutex
}

var client *RRClient

var bnsClient bns.Client

type instanceBuffer struct {
	endpointsRing *ring.Ring // the element of ring is the format of "instance.hostname:instance.port" like "localhost:8080"
	updateTime    time.Time  // the local endponits will update if current time is 1 minute after updateTime
}

func NewRRClient() *RRClient {
	if client == nil {
		return newRRClient()
	}
	return client
}

func newRRClient() *RRClient {

	client = &RRClient{
		bnsMap: make(map[string]*instanceBuffer, 1),
	}

	return client
}

func (c *RRClient) GetEndpoint(BNSName string) (string, error) {

	c.rwLock.RLock()
	v, ok := c.bnsMap[BNSName]
	c.rwLock.RUnlock()

	if !ok {
		v = &instanceBuffer{}

		c.rwLock.Lock()
		defer c.rwLock.Unlock()

		c.bnsMap[BNSName] = v

	}

	// singleton && after 1 minute the host info should be updated
	if v.endpointsRing == nil || time.Now().After(v.updateTime.Add(time.Minute)) {

		v.updateTime = time.Now()

		err := v.BuildHostsRing(BNSName)
		if err != nil {
			return "", err
		}

	}

	endpoint := v.endpointsRing.Value.(string)

	v.endpointsRing = v.endpointsRing.Next()

	return endpoint, nil
}

func (ib *instanceBuffer) BuildHostsRing(BNSName string) error {

	req := &bns.NamingRequest{ServiceName: BNSName}
	resp := &bns.NamingResponse{}

	if errCall := GetBnsClient().Call(context.Background(), req, resp); errCall != nil {
		return errCall
	}

	instanceInfos := resp.Resp.GetInstanceInfo()

	ib.endpointsRing = ring.New(len(instanceInfos))

	for _, info := range resp.Resp.GetInstanceInfo() {

		ib.endpointsRing.Value = fmt.Sprintf("%s:%d", *info.HostName, *info.InstanceStatus.Port)
		ib.endpointsRing = ib.endpointsRing.Next()

	}

	return nil
}

func GetBnsClient() bns.Client {
	if bnsClient == nil {
		bnsClient = bns.DefaultClient()
	}
	return bnsClient
}
