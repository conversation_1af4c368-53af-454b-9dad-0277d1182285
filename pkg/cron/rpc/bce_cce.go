// CCE API Doc:
// http://agroup.baidu.com/share/md/25dce29ce7084fc69aef7b554f8c28ff
// https://cloud.baidu.com/doc/CCE/API.html

package rpc

import (
	"errors"
	"fmt"
	"net/http"
	"net/url"
	"time"

	"github.com/google/uuid"

	"icode.baidu.com/baidu/faas/kun/pkg/api"
	"icode.baidu.com/baidu/faas/kun/pkg/bce/auth"
	"icode.baidu.com/baidu/faas/kun/pkg/rest"
	"icode.baidu.com/baidu/faas/kun/pkg/util/logs"
)

// 默认扩容参数
const (
	DefaultRegion = "bj"

	DefaultMachineSpec      = "bcc.c3.c2m4"
	DefaultCpu              = 2
	DefaultMemory           = 4
	DefaultRootDiskSizeInGb = 30    //  默认根磁盘大小 单位GB
	DefaultRootDiskType     = "hp1" //  默认根磁盘类型

	DefaultSsdMountPath = "/data" //  默认CDS磁盘大小 单位GB
	DefaultSsdDiskSize  = 15      //  默认CDS磁盘大小 单位GB
	DefaultCDSType      = "hp1"   //  默认CDS磁盘类型 单位GB

	DefaultBccImageID = "3c9832ea-3277-4716-926c-925489aa165d" //  默认OS镜像ID
	DefaultOsType     = "linux"                                //  默认OS 类型
	DefaultOsVersion  = "16.04 LTS amd64 (64bit)"              //  默认OS 版本
	DefaultPageSize   = "5000"
)

type ScalingUpParam struct {
	Number int //scaling up node numbers

	Zone       string
	SubnetUUID string
	Region     string

	InstanceType     int    // v1 api use
	MachineSpec      string // v2 api to use
	Cpu              int
	Memory           int
	RootDiskType     string
	RootDiskSizeInGb int

	// cds config
	SsdMountPath string // path
	CDSType      string // cds type
	SsdDiskSize  int    // cds size

	// os image config
	BccImageID string //  目前只有这个起作用
	OsType     string
	OsVersion  string

	AdminPassType string // v1 api use
	AdminPass     string // custom password

	PreDownloadContainerImageScript string // 预下载容器镜像脚本

	K8sLabels map[string]string
}

// EnsureValue 将参数中的空值设为默认值
func (p *ScalingUpParam) EnsureValue() {
	if p.Region == "" {
		p.Region = DefaultRegion
	}

	if p.Cpu == 0 {
		p.Cpu = DefaultCpu
	}
	if p.Memory == 0 {
		p.Memory = DefaultMemory
	}

	if p.RootDiskType == "" {
		p.RootDiskType = DefaultRootDiskType
	}
	if p.RootDiskSizeInGb == 0 {
		p.RootDiskSizeInGb = DefaultRootDiskSizeInGb
	}

	if p.SsdMountPath == "" {
		p.SsdMountPath = DefaultSsdMountPath
	}
	if p.SsdDiskSize == 0 {
		p.SsdDiskSize = DefaultSsdDiskSize
	}
	if p.CDSType == "" {
		p.CDSType = DefaultCDSType
	}

	if p.BccImageID == "" {
		p.BccImageID = DefaultBccImageID
	}

	if p.OsType == "" {
		p.OsType = DefaultOsType
	}
	if p.OsVersion == "" {
		p.OsVersion = DefaultOsVersion
	}

	if p.AdminPassType == "" {
		p.AdminPassType = "random"
	}

}

// CceInterface xxx
//
//go:generate mockgen -destination=./mock/bce_cce.go -package=mock icode.baidu.com/baidu/faas/kun/pkg/cron/rpc CceInterface
type CceInterface interface {
	GetNodes(clusterUUID string) ([]CceNode, error)
	ScalingDown(clusterUUID string, nodeId []string) error
	ScalingUp(clusterUUID string, param *ScalingUpParam) error
	GetClusterInfo(clusterUUID string) (*CceClusterInfoResponse, error)
	IsClusterRunning(clusterUUID string) bool
	GetImageList(input *GetImageListRequest) ([]ImageModel, error)
	GetCreatedFailedNodes(clusterUUID string) ([]CceNode, error)
}

const (
	CceNodeStatusRunning      = "RUNNING"
	CceNodeStatusCreating     = "CREATING"
	CceNodeStatusCreateFailed = "CREATE_FAILED"
	CceNodeStatusDeleteFailed = "DELETE_FAILED"
	CceNodeStatusError        = "ERROR"
)

// CceNode xxx
type CceNode struct {
	ShortID       string `json:"instanceShortId"`
	UUID          string `json:"instanceUuid"`
	Name          string `json:"instanceName"`
	ClusterUUID   string `json:"clusterUuid"`
	Zone          string `json:"availableZone"`
	VpcID         string `json:"vpcId"`
	VpcCidr       string `json:"vpcCidr"`
	SubnetID      string `json:"subnetId"`
	Eip           string `json:"eip"`
	EipBandwidth  int    `json:"eipBandwidth"`
	Cpu           int    `json:"cpu"`
	Memory        int    `json:"memory"`
	InstanceType  string `json:"instanceType"`
	Blb           string `json:"blb"`
	FloatingIp    string `json:"floatingIp"`
	FixIp         string `json:"fixIp"`
	CreateTime    string `json:"createTime"`
	DeleteTime    string `json:"deleteTime"`
	Status        string `json:"status"`
	PaymentMethod string `json:"paymentMethod"`
	CceInstanceID string `json:"cceInstanceID"`
}

// CceGetNodesResponse xxx
type CceGetNodesResponse struct {
	Marker      string    `json:"marker"`
	IsTruncated bool      `json:"isTruncated"`
	NextMarker  string    `json:"nextMarker"`
	MaxKeys     int       `json:"maxKeys"`
	Nodes       []CceNode `json:"nodes"`
}

type ScalingDownNodeInfo struct {
	InstanceId string `json:"instanceId"`
}

type CceScalingDownRequest struct {
	ClusterUuid string                `json:"clusterUuid"`
	NodeInfo    []ScalingDownNodeInfo `json:"nodeInfo"`
}

type CceScalingUpRequest struct {
	ClusterUuid     string                    `json:"clusterUuid"`
	CdsPreMountInfo *CdsPreMountInfo          `json:"cdsPreMountInfo"`
	OrderContent    *BaseCreateOrderRequestVo `json:"orderContent"`
}

type CceScalingUpResponse struct {
	ClusterUuid string   `json:"clusterUuid"`
	OrderId     []string `json:"orderId"`
}

type CceClusterInfoResponse struct {
	ClusterUuid   string            `json:"clusterUuid"`   // 集群的uuid
	ClusterName   string            `json:"clusterName"`   // 集群的名字
	Region        string            `json:"region"`        // 所属区域
	SlaveVmCount  int               `json:"slaveVmCount"`  // k8s集群从节点虚机数
	MasterVmCount int               `json:"masterVmCount"` // k8s集群主节点虚机数,请填写1台
	VpcCidr       string            `json:"vpcCidr"`       // vpcCidr
	ZoneSubnetMap map[string]string `json:"zoneSubnetMap"` // zone->subnetCidet
	Status        string            `json:"status"`        // 集群状态
	CreateTime    string            `json:"createTime"`    // 集群创建时间
	DeleteTime    string            `json:"deleteTime"`    // 集群删除时间
	Comment       string            `json:"comment"`       // 集群备注
}

type GetImageListRequest struct {
	Marker  string `json:"marker"`
	MaxKeys int    `json:"maxKeys"`
	User    string `json:"user"`
}

type GetImageListResponse struct {
	Marker      string       `json:"marker"`
	IsTruncated bool         `json:"isTruncated"`
	NextMarker  string       `json:"nextMarker"`
	MaxKeys     int          `json:"maxKeys"`
	Images      []ImageModel `json:"images"`
}

type ImageModel struct {
	CreateTime string    `json:"createTime"`
	Address    string    `json:"address"`
	IsPublic   bool      `json:"isPublic"`
	ImageMeta  ImageMeta `json:"imageMeta"`
}

type ImageMeta struct {
	NameSpace   string `json:"namespace"`
	Repository  string `json:"repository"`
	Tag         string `json:"tag"`
	Description string `string:"description"`
}

type BaseCreateOrderRequestVo struct {
	PaymentMethod []string      `json:"paymentMethod"`
	Items         []OrderConfig `json:"items"`
}

type OrderConfig struct {
	Config        interface{} `json:"config"`
	PaymentMethod []string    `json:"paymentMethod"`
}

type BCCConfig struct {
	ProductType         string            `json:"productType,omitempty"`      // 付费类型，一期只支持postpay
	Region              string            `json:"region,omitempty"`           // “zoneA”
	LogicalZone         string            `json:"logicalZone,omitempty"`      // 区域
	InstanceType        int               `json:"instanceType,string"`        // 默认1000
	FpgaCard            string            `json:"fpgaCard,omitempty"`         // 这些参数默认就行 容器产品用不到
	GpuCard             string            `json:"gpuCard,omitempty"`          // 这些参数默认就行 容器产品用不到
	GpuCount            int               `json:"gpuCount,omitempty"`         // 这些参数默认就行 容器产品用不到
	Cpu                 int               `json:"cpu,omitempty"`              // “month” 间隔单位
	Memory              int               `json:"memory,omitempty"`           // memory数量
	RootDiskSizeInGb    int               `json:"rootDiskSizeInGb,omitempty"` // 系统盘大小
	ImageType           string            `json:"imageType,omitempty"`        // 就一个镜像 ubuntu1604
	OsType              string            `json:"osType,omitempty"`           // os类型
	OsVersion           string            `json:"osVersion,omitempty"`        // os版本
	DiskSize            int               `json:"diskSize,omitempty"`         // 类型 “EIP”
	EbsSize             []string          `json:"ebsSize,omitempty"`          // ebs的大小
	IfBuyEip            int               `json:"ifBuyEip"`                   // 是否购买eip
	EipName             string            `json:"eipName,omitempty"`          // eip的名字
	SubProductType      string            `json:"subProductType,omitempty"`   // sub 类型
	BandwidthInMbps     int               `json:"bandwidthInMbps,omitempty"`  // eip带宽
	SubnetUuid          string            `json:"subnetUuid,omitempty"`       // 子网uuid
	SecurityGroupId     string            `json:"securityGroupId,omitempty"`  // 安全组uuid
	NameType            string            `json:"nameType,omitempty"`
	AdminPassType       string            `json:"adminPassType,omitempty"`
	AdminPass           string            `json:"adminPass,omitempty"`           // 密码
	AdminPassConfirm    string            `json:"adminPassConfirm,omitempty"`    // 重复密码
	PurchaseLength      int               `json:"purchaseLength,omitempty"`      // 购买时长
	PurchaseNum         int               `json:"purchaseNum,omitempty"`         // 购买的虚机个数
	AutoRenewTimeUnit   string            `json:"autoRenewTimeUnit,omitempty"`   // 自动生成的时间单元(“month”)
	AutoRenewTime       int64             `json:"autoRenewTime,omitempty"`       // 密码
	CreateEphemeralList []CreateEphemeral `json:"createEphemeralList,omitempty"` // CreateEphemeralList描述见附录
	AutoRenew           bool              `json:"autoRenew,omitempty"`           // 是否自动续费 默认即可 后付费不存在这个问题
	ImageId             string            `json:"imageId,omitempty"`             // 镜像id 用默认即可 固定是ubuntu1604
	OsName              string            `json:"osName,omitempty"`              // 系统名
	SecurityGroupName   string            `json:"securityGroupName,omitempty"`   // 安全组名字
	ServiceType         string            `json:"serviceType,omitempty"`         // 服务类型 如BCC
}

type CDSConfig struct {
	ProductType       string        `json:"productType,omitempty"`       // 付费类型，一期只支持postpay
	LogicalZone       string        `json:"logicalZone,omitempty"`       // “zoneA”
	Region            string        `json:"region,omitempty"`            // 区域
	SubProductType    int           `json:"subProductType,omitempty"`    // 默认1000
	PurchaseNum       int           `json:"purchaseNum,omitempty"`       // EIP购买数量应该是购买BCC数量的总和
	PurchaseLength    int           `json:"purchaseLength,omitempty"`    // 购买时长
	AutoRenewTime     int           `json:"autoRenewTime,omitempty"`     // 自动重新生成
	AutoRenewTimeUnit string        `json:"autoRenewTimeUnit,omitempty"` // “month” 间隔单位
	CdsDiskSize       []CdsDiskSize `json:"cdsDiskSize,omitempty"`       // 包含size,snapshotId,volumeType字段
	ServiceType       string        `json:"serviceType,omitempty"`       // 类型 “EIP”
}

type EIPConfig struct {
	ProductType       string `json:"productType,omitempty"`       // 付费类型，一期只支持postpay
	BandwidthInMbps   int    `json:"bandwidthInMbps,omitempty"`   // 默认1000
	Region            string `json:"region,omitempty"`            // 区域
	SubProductType    int    `json:"subProductType,omitempty"`    // 默认1000
	PurchaseNum       int    `json:"purchaseNum,omitempty"`       // EIP购买数量应该是购买BCC数量的总和
	PurchaseLength    int    `json:"purchaseLength,omitempty"`    // 购买时长
	AutoRenewTime     int    `json:"autoRenewTime,omitempty"`     // 自动重新生成
	AutoRenewTimeUnit string `json:"autoRenewTimeUnit,omitempty"` // “month” 间隔单位
	Name              string `json:"name,omitempty"`              // “hstm123”
	ServiceType       string `json:"serviceType,omitempty"`       // 类型 “EIP”
}

type CreateEphemeral struct {
	StorageType string `json:"storageType"` // 磁盘存储类型 从页面创建虚机时 看到请求 默认是ssd
	SizeInGB    int    `json:"sizeInGB"`    // 磁盘大小
}

type CdsDiskSize struct {
	SnapshotId string `json:"snapshotId"`
	VolumeType string `json:"volumeType"`
	Size       int    `json:"size"`
}

type CdsPreMountInfo struct {
	MountPath string        `json:"mountPath"`
	CdsConfig []CdsDiskSize `json:"cdsConfig"`
}

// CceClient xxx
type CceClient struct {
	host              string
	bceAuth           *auth.BceAuth
	restClient        *rest.RESTClient
	securityGroupId   string
	securityGroupName string
}

// NewCceClient 生成新的cce client
func NewCceClient(runOptions *api.BceOptions) *CceClient {
	baseURL, err := url.Parse(runOptions.CceEndpoint)
	if err != nil {
		panic(err)
	}

	config := rest.ContentConfig{
		BackendType: rest.BackendTypeBce,
	}

	client, err := rest.NewRESTClient(baseURL, "v1", config, nil)
	if err != nil {
		panic(err)
	}

	return &CceClient{
		host:              baseURL.Host,
		bceAuth:           auth.NewBceAuth(runOptions.AccessKeyID, runOptions.AccessKeySecret),
		restClient:        client,
		securityGroupId:   runOptions.SecurityGroupId,
		securityGroupName: runOptions.SecurityGroupName,
	}
}

func (c *CceClient) setClient(httpClient *http.Client) {
	c.restClient.Client = httpClient
}

func (c *CceClient) GetCreatedFailedNodes(clusterUUID string) ([]CceNode, error) {

	out := CceGetNodesResponse{}

	req := c.restClient.Get().
		Resource("node").
		Param("clusterUuid", clusterUUID).
		Param("maxKeys", DefaultPageSize).
		Param("phases", "create_failed").
		SetHeader("Host", c.host)

	c.sign(req)

	err := req.Do().Into(&out)

	if err != nil {
		err = fmt.Errorf("request cluster/%s/instances failed : %s", clusterUUID, err.Error())
	}

	return out.Nodes, err
}

// GetNodes xxx
func (c *CceClient) GetNodes(clusterUUID string) ([]CceNode, error) {

	out := CceGetNodesResponse{}

	req := c.restClient.Get().
		Resource("node").
		Param("clusterUuid", clusterUUID).
		Param("maxKeys", DefaultPageSize).
		SetHeader("Host", c.host)

	c.sign(req)

	err := req.Do().Into(&out)

	return out.Nodes, err
}

func (c *CceClient) ScalingDown(clusterUUID string, nodeId []string) error {
	body := CceScalingDownRequest{
		ClusterUuid: clusterUUID,
	}

	nodeInfo := []ScalingDownNodeInfo{}
	for _, id := range nodeId {
		nodeInfo = append(nodeInfo, ScalingDownNodeInfo{id})
	}
	body.NodeInfo = nodeInfo

	req := c.restClient.Post().
		Resource("cluster").
		Param("scalingDown", "").
		Param("deleteEipCds", "true").
		Param("deleteSnap", "true").
		SetHeader("Host", c.host).
		Body(body)

	c.sign(req)

	var statusCode int
	raw, err := req.Do().
		StatusCode(&statusCode).
		Raw()

	if err != nil {
		return err
	}

	if statusCode != http.StatusOK {
		return errors.New(fmt.Sprintf("ScalingDown request error with status %d: %s", statusCode, raw))
	}

	return nil
}

func (c *CceClient) ScalingUp(clusterUUID string, param *ScalingUpParam) error {
	param.EnsureValue()
	reqID := uuid.New().String()

	request := CceScalingUpRequest{
		ClusterUuid: clusterUUID,
		CdsPreMountInfo: &CdsPreMountInfo{
			MountPath: param.SsdMountPath,
			CdsConfig: []CdsDiskSize{
				{
					VolumeType: "ssd",
					SnapshotId: "",
					Size:       param.SsdDiskSize,
				},
			},
		},
		OrderContent: &BaseCreateOrderRequestVo{
			PaymentMethod: []string{},
			Items: []OrderConfig{
				{
					PaymentMethod: []string{},
					Config: BCCConfig{
						AdminPassType:     param.AdminPassType,
						AdminPass:         param.AdminPass,
						BandwidthInMbps:   1000,
						Cpu:               param.Cpu,
						EbsSize:           []string{},
						IfBuyEip:          0,
						ImageId:           param.BccImageID,
						InstanceType:      param.InstanceType,
						LogicalZone:       param.Zone,
						Memory:            param.Memory,
						RootDiskSizeInGb:  param.RootDiskSizeInGb,
						NameType:          "random",
						OsType:            param.OsType,
						OsVersion:         param.OsVersion,
						ProductType:       "postpay",
						PurchaseNum:       param.Number,
						Region:            param.Region,
						SecurityGroupId:   c.securityGroupId,
						SecurityGroupName: c.securityGroupName,
						ServiceType:       "BCC",
						SubnetUuid:        param.SubnetUUID,
						SubProductType:    "netraffic",
					},
				},
				{
					PaymentMethod: []string{},
					Config: CDSConfig{
						ProductType:    "postpay",
						LogicalZone:    param.Zone,
						Region:         param.Region,
						SubProductType: 1000,
						PurchaseNum:    param.Number,
						CdsDiskSize: []CdsDiskSize{
							{
								VolumeType: "ssd",
								SnapshotId: "",
								Size:       param.SsdDiskSize,
							},
						},
						ServiceType: "CDS",
					},
				},
			},
		},
	}

	req := c.restClient.Post().
		Resource("cluster").
		Param("scalingUp", "").
		SetHeader("Host", c.host).
		SetHeader("X-Bce-Request-Id", reqID).
		Body(request)

	c.sign(req)

	logs.Infof("CCE scaling up  reqId:%s, param: %+v", reqID, param)
	var response CceScalingUpResponse
	var status int
	err := req.Do().StatusCode(&status).Into(&response)
	logs.Infof("CCE scaling up  reqId: %s, status: %d, err : %v", reqID, status, err)
	if err != nil {
		logs.Errorf("Cce scaling failed: reqId:%s, err: %s", reqID, err.Error())
		return err
	}

	logs.Infof("CCE scaling up successfully with order id %v, reqId:%s, clusterID: %s", response.OrderId, reqID, response.ClusterUuid)

	return nil
}

func (c *CceClient) GetClusterInfo(clusterUUID string) (*CceClusterInfoResponse, error) {
	var response CceClusterInfoResponse

	req := c.restClient.Get().
		Resource("cluster/"+clusterUUID).
		SetHeader("Host", c.host)

	c.sign(req)

	err := req.Do().Into(&response)

	if err != nil {
		return nil, err
	}
	return &response, nil
}

func (c *CceClient) IsClusterRunning(clusterUUID string) bool {
	res, err := c.GetClusterInfo(clusterUUID)
	if err != nil {
		return false
	}

	if res.Status == "RUNNING" {
		return true
	}

	return false
}

// 获取cce镜像列表
func (c *CceClient) GetImageList(input *GetImageListRequest) ([]ImageModel, error) {
	reqID := uuid.New().String()
	req := c.restClient.Get().
		Resource("image").
		SetHeader("X-Bce-Request-Id", reqID).
		SetHeader("Host", c.host)

	if input.Marker != "" {
		req.Param("marker", input.Marker)
	}

	if input.MaxKeys != 0 {
		req.Param("maxKeys", fmt.Sprintf("%d", input.MaxKeys))
	}
	if input.User != "" {
		req.Param("user", input.User)
	}

	c.sign(req)

	response := &GetImageListResponse{}
	err := req.Do().Into(response)

	if err != nil {
		return nil, err
	}
	return response.Images, nil
}

func (c *CceClient) sign(request *rest.Request) {
	reqUrl := request.URL()
	sign := c.bceAuth.NewSigner().
		Method(request.Verb()).
		Path(reqUrl.Path).
		Params(reqUrl.Query()).
		Headers(request.Header()).
		Expire(3600).
		WithSignedHeader().
		GetSign()

	request.SetHeader("Authorization", sign)
}

func (node *CceNode) ConvertToObsoluteNodeInfo() *api.NodeInfo {
	nodeID := node.GenerateID()
	nodeInfo := &api.NodeInfo{
		ID:                 nodeID,
		Name:               node.FixIp,
		NodeLabels:         map[string]string{},
		OriginalMemorySize: 0,
		MemoryAvailable:    0,
		State:              api.NodeStateCceObsolete,
		StateUpdateTime:    time.Now(),
		CceClusterUUID:     node.ClusterUUID,
		InstanceShortId:    node.ShortID,
		FloatingIP:         node.FloatingIp,
		ContainerImage:     "",
		KataRuntimeImageID: "",
	}
	return nodeInfo
}

func (node *CceNode) GenerateID() string {
	return node.ClusterUUID + ":" + node.FixIp + ":" + node.ShortID
}
