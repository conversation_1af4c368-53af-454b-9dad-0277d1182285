// Code generated by MockGen. DO NOT EDIT.
// Source: /Users/<USER>/go/src/icode.baidu.com/baidu/faas/kun/pkg/cron/rpc/bcc_ccr.go

// Package mock is a generated GoMock package.
package mock

import (
	gomock "github.com/golang/mock/gomock"
	rpc "icode.baidu.com/baidu/faas/kun/pkg/cron/rpc"
	reflect "reflect"
)

// MockCcrInterface is a mock of CcrInterface interface
type MockCcrInterface struct {
	ctrl     *gomock.Controller
	recorder *MockCcrInterfaceMockRecorder
}

// MockCcrInterfaceMockRecorder is the mock recorder for MockCcrInterface
type MockCcrInterfaceMockRecorder struct {
	mock *MockCcrInterface
}

// NewMockCcrInterface creates a new mock instance
func NewMockCcrInterface(ctrl *gomock.Controller) *MockCcrInterface {
	mock := &MockCcrInterface{ctrl: ctrl}
	mock.recorder = &MockCcrInterfaceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use
func (m *MockCcrInterface) EXPECT() *MockCcrInterfaceMockRecorder {
	return m.recorder
}

// GetImageInfo mocks base method
func (m *MockCcrInterface) GetImageInfo(repoName string, projectId int32) (*rpc.GetImageInfoResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetImageInfo", repoName, projectId)
	ret0, _ := ret[0].(*rpc.GetImageInfoResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetImageInfo indicates an expected call of GetImageInfo
func (mr *MockCcrInterfaceMockRecorder) GetImageInfo(repoName, projectId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetImageInfo", reflect.TypeOf((*MockCcrInterface)(nil).GetImageInfo), repoName, projectId)
}

// GetImageList mocks base method
func (m *MockCcrInterface) GetImageList(input *rpc.GetCcrImageListRequest) ([]rpc.CcrImageModel, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetImageList", input)
	ret0, _ := ret[0].([]rpc.CcrImageModel)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetImageList indicates an expected call of GetImageList
func (mr *MockCcrInterfaceMockRecorder) GetImageList(input interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetImageList", reflect.TypeOf((*MockCcrInterface)(nil).GetImageList), input)
}

// GetImageTags mocks base method
func (m *MockCcrInterface) GetImageTags(input *rpc.GetImageTagsRequest) ([]rpc.CcrImageTag, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetImageTags", input)
	ret0, _ := ret[0].([]rpc.CcrImageTag)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetImageTags indicates an expected call of GetImageTags
func (mr *MockCcrInterfaceMockRecorder) GetImageTags(input interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetImageTags", reflect.TypeOf((*MockCcrInterface)(nil).GetImageTags), input)
}
