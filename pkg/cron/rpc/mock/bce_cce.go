// Code generated by MockGen. DO NOT EDIT.
// Source: icode.baidu.com/baidu/faas/kun/pkg/cron/rpc (interfaces: CceInterface)

// Package mock is a generated GoMock package.
package mock

import (
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	rpc "icode.baidu.com/baidu/faas/kun/pkg/cron/rpc"
)

// MockCceInterface is a mock of CceInterface interface.
type MockCceInterface struct {
	ctrl     *gomock.Controller
	recorder *MockCceInterfaceMockRecorder
}

// MockCceInterfaceMockRecorder is the mock recorder for MockCceInterface.
type MockCceInterfaceMockRecorder struct {
	mock *MockCceInterface
}

// NewMockCceInterface creates a new mock instance.
func NewMockCceInterface(ctrl *gomock.Controller) *MockCceInterface {
	mock := &MockCceInterface{ctrl: ctrl}
	mock.recorder = &MockCceInterfaceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockCceInterface) EXPECT() *MockCceInterfaceMockRecorder {
	return m.recorder
}

// GetClusterInfo mocks base method.
func (m *MockCceInterface) GetClusterInfo(arg0 string) (*rpc.CceClusterInfoResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetClusterInfo", arg0)
	ret0, _ := ret[0].(*rpc.CceClusterInfoResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetClusterInfo indicates an expected call of GetClusterInfo.
func (mr *MockCceInterfaceMockRecorder) GetClusterInfo(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetClusterInfo", reflect.TypeOf((*MockCceInterface)(nil).GetClusterInfo), arg0)
}

// GetCreatedFailedNodes mocks base method.
func (m *MockCceInterface) GetCreatedFailedNodes(arg0 string) ([]rpc.CceNode, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCreatedFailedNodes", arg0)
	ret0, _ := ret[0].([]rpc.CceNode)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCreatedFailedNodes indicates an expected call of GetCreatedFailedNodes.
func (mr *MockCceInterfaceMockRecorder) GetCreatedFailedNodes(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCreatedFailedNodes", reflect.TypeOf((*MockCceInterface)(nil).GetCreatedFailedNodes), arg0)
}

// GetImageList mocks base method.
func (m *MockCceInterface) GetImageList(arg0 *rpc.GetImageListRequest) ([]rpc.ImageModel, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetImageList", arg0)
	ret0, _ := ret[0].([]rpc.ImageModel)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetImageList indicates an expected call of GetImageList.
func (mr *MockCceInterfaceMockRecorder) GetImageList(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetImageList", reflect.TypeOf((*MockCceInterface)(nil).GetImageList), arg0)
}

// GetNodes mocks base method.
func (m *MockCceInterface) GetNodes(arg0 string) ([]rpc.CceNode, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetNodes", arg0)
	ret0, _ := ret[0].([]rpc.CceNode)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetNodes indicates an expected call of GetNodes.
func (mr *MockCceInterfaceMockRecorder) GetNodes(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetNodes", reflect.TypeOf((*MockCceInterface)(nil).GetNodes), arg0)
}

// IsClusterRunning mocks base method.
func (m *MockCceInterface) IsClusterRunning(arg0 string) bool {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "IsClusterRunning", arg0)
	ret0, _ := ret[0].(bool)
	return ret0
}

// IsClusterRunning indicates an expected call of IsClusterRunning.
func (mr *MockCceInterfaceMockRecorder) IsClusterRunning(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IsClusterRunning", reflect.TypeOf((*MockCceInterface)(nil).IsClusterRunning), arg0)
}

// ScalingDown mocks base method.
func (m *MockCceInterface) ScalingDown(arg0 string, arg1 []string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ScalingDown", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// ScalingDown indicates an expected call of ScalingDown.
func (mr *MockCceInterfaceMockRecorder) ScalingDown(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ScalingDown", reflect.TypeOf((*MockCceInterface)(nil).ScalingDown), arg0, arg1)
}

// ScalingUp mocks base method.
func (m *MockCceInterface) ScalingUp(arg0 string, arg1 *rpc.ScalingUpParam) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ScalingUp", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// ScalingUp indicates an expected call of ScalingUp.
func (mr *MockCceInterfaceMockRecorder) ScalingUp(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ScalingUp", reflect.TypeOf((*MockCceInterface)(nil).ScalingUp), arg0, arg1)
}
