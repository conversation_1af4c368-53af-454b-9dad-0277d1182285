// Code generated by MockGen. DO NOT EDIT.
// Source: icode.baidu.com/baidu/faas/kun/pkg/cron/rpc (interfaces: K8sInterface)

// Package mock is a generated GoMock package.
package mock

import (
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	api "icode.baidu.com/baidu/faas/kun/pkg/api"
	rpc "icode.baidu.com/baidu/faas/kun/pkg/cron/rpc"
	v1 "k8s.io/api/core/v1"
	resource "k8s.io/apimachinery/pkg/api/resource"
	watch "k8s.io/apimachinery/pkg/watch"
)

// MockK8sInterface is a mock of K8sInterface interface.
type MockK8sInterface struct {
	ctrl     *gomock.Controller
	recorder *MockK8sInterfaceMockRecorder
}

// GetPodsByAllNamespacesAndByNode mocks base method.
func (m *MockK8sInterface) GetPodsByAllNamespacesAndByNode(arg0 string) ([]v1.Pod, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPodsByAllNamespacesAndByNode", arg0)
	ret0, _ := ret[0].([]v1.Pod)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPodsByAllNamespacesAndByNode indicates an expected call of GetPodsByAllNamespacesAndByNode.
func (mr *MockK8sInterfaceMockRecorder) GetPodsByAllNamespacesAndByNode(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPodsByAllNamespacesAndByNode", reflect.TypeOf((*MockK8sInterface)(nil).GetPodsByAllNamespacesAndByNode), arg0)
}

// MockK8sInterfaceMockRecorder is the mock recorder for MockK8sInterface.
type MockK8sInterfaceMockRecorder struct {
	mock *MockK8sInterface
}

// NewMockK8sInterface creates a new mock instance.
func NewMockK8sInterface(ctrl *gomock.Controller) *MockK8sInterface {
	mock := &MockK8sInterface{ctrl: ctrl}
	mock.recorder = &MockK8sInterfaceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockK8sInterface) EXPECT() *MockK8sInterfaceMockRecorder {
	return m.recorder
}

// ApplyDaemonSet mocks base method.
func (m *MockK8sInterface) ApplyDaemonSet(arg0 string, arg1 []byte) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ApplyDaemonSet", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// ApplyDaemonSet indicates an expected call of ApplyDaemonSet.
func (mr *MockK8sInterfaceMockRecorder) ApplyDaemonSet(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ApplyDaemonSet", reflect.TypeOf((*MockK8sInterface)(nil).ApplyDaemonSet), arg0, arg1)
}

// CleanDaemonSetPods mocks base method.
func (m *MockK8sInterface) CleanDaemonSetPods(arg0 *rpc.LabelSelector, arg1 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CleanDaemonSetPods", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// CleanDaemonSetPods indicates an expected call of CleanDaemonSetPods.
func (mr *MockK8sInterfaceMockRecorder) CleanDaemonSetPods(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CleanDaemonSetPods", reflect.TypeOf((*MockK8sInterface)(nil).CleanDaemonSetPods), arg0, arg1)
}

// CleanPendingPods mocks base method.
func (m *MockK8sInterface) CleanPendingPods(arg0 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CleanPendingPods", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// CleanPendingPods indicates an expected call of CleanPendingPods.
func (mr *MockK8sInterfaceMockRecorder) CleanPendingPods(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CleanPendingPods", reflect.TypeOf((*MockK8sInterface)(nil).CleanPendingPods), arg0)
}

// CleanPodsByFieldSelector mocks base method.
func (m *MockK8sInterface) CleanPodsByFieldSelector(arg0 string, arg1 *rpc.LabelSelector) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CleanPodsByFieldSelector", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// CleanPodsByFieldSelector indicates an expected call of CleanPodsByFieldSelector.
func (mr *MockK8sInterfaceMockRecorder) CleanPodsByFieldSelector(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CleanPodsByFieldSelector", reflect.TypeOf((*MockK8sInterface)(nil).CleanPodsByFieldSelector), arg0, arg1)
}

// ComputeCpuAndMenLimitForPod mocks base method.
func (m *MockK8sInterface) ComputeCpuAndMenLimitForPod(arg0, arg1 int64) (resource.Quantity, resource.Quantity, resource.Quantity, resource.Quantity, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ComputeCpuAndMenLimitForPod", arg0, arg1)
	ret0, _ := ret[0].(resource.Quantity)
	ret1, _ := ret[1].(resource.Quantity)
	ret2, _ := ret[2].(resource.Quantity)
	ret3, _ := ret[3].(resource.Quantity)
	ret4, _ := ret[4].(error)
	return ret0, ret1, ret2, ret3, ret4
}

// ComputeCpuAndMenLimitForPod indicates an expected call of ComputeCpuAndMenLimitForPod.
func (mr *MockK8sInterfaceMockRecorder) ComputeCpuAndMenLimitForPod(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ComputeCpuAndMenLimitForPod", reflect.TypeOf((*MockK8sInterface)(nil).ComputeCpuAndMenLimitForPod), arg0, arg1)
}

// CreatePod mocks base method.
func (m *MockK8sInterface) CreatePod(arg0, arg1 string, arg2 api.ServiceType, arg3, arg4 int64) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreatePod", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].(error)
	return ret0
}

// CreatePod indicates an expected call of CreatePod.
func (mr *MockK8sInterfaceMockRecorder) CreatePod(arg0, arg1, arg2, arg3, arg4 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreatePod", reflect.TypeOf((*MockK8sInterface)(nil).CreatePod), arg0, arg1, arg2, arg3, arg4)
}

// DeleteDaemonSetPod mocks base method.
func (m *MockK8sInterface) DeleteDaemonSetPod(arg0 string, arg1 bool) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteDaemonSetPod", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteDaemonSetPod indicates an expected call of DeleteDaemonSetPod.
func (mr *MockK8sInterfaceMockRecorder) DeleteDaemonSetPod(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteDaemonSetPod", reflect.TypeOf((*MockK8sInterface)(nil).DeleteDaemonSetPod), arg0, arg1)
}

// DeletePod mocks base method.
func (m *MockK8sInterface) DeletePod(arg0 string, arg1 bool) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeletePod", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeletePod indicates an expected call of DeletePod.
func (mr *MockK8sInterfaceMockRecorder) DeletePod(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeletePod", reflect.TypeOf((*MockK8sInterface)(nil).DeletePod), arg0, arg1)
}

// GetDaemonSetsOnNode mocks base method.
func (m *MockK8sInterface) GetDaemonSetsOnNode(arg0 string) ([]v1.Pod, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetDaemonSetsOnNode", arg0)
	ret0, _ := ret[0].([]v1.Pod)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetDaemonSetsOnNode indicates an expected call of GetDaemonSetsOnNode.
func (mr *MockK8sInterfaceMockRecorder) GetDaemonSetsOnNode(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetDaemonSetsOnNode", reflect.TypeOf((*MockK8sInterface)(nil).GetDaemonSetsOnNode), arg0)
}

// GetNodeInfo mocks base method.
func (m *MockK8sInterface) GetNodeInfo(arg0 string) (*v1.Node, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetNodeInfo", arg0)
	ret0, _ := ret[0].(*v1.Node)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetNodeInfo indicates an expected call of GetNodeInfo.
func (mr *MockK8sInterfaceMockRecorder) GetNodeInfo(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetNodeInfo", reflect.TypeOf((*MockK8sInterface)(nil).GetNodeInfo), arg0)
}

// GetNodesByLabel mocks base method.
func (m *MockK8sInterface) GetNodesByLabel(arg0 *rpc.LabelSelector) ([]v1.Node, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetNodesByLabel", arg0)
	ret0, _ := ret[0].([]v1.Node)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetNodesByLabel indicates an expected call of GetNodesByLabel.
func (mr *MockK8sInterfaceMockRecorder) GetNodesByLabel(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetNodesByLabel", reflect.TypeOf((*MockK8sInterface)(nil).GetNodesByLabel), arg0)
}

// GetPodInfo mocks base method.
func (m *MockK8sInterface) GetPodInfo(arg0 string) (*v1.Pod, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPodInfo", arg0)
	ret0, _ := ret[0].(*v1.Pod)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPodInfo indicates an expected call of GetPodInfo.
func (mr *MockK8sInterfaceMockRecorder) GetPodInfo(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPodInfo", reflect.TypeOf((*MockK8sInterface)(nil).GetPodInfo), arg0)
}

// GetPodsByLabelAndNode mocks base method.
func (m *MockK8sInterface) GetPodsByLabelAndNode(arg0 *rpc.LabelSelector, arg1 string) ([]v1.Pod, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPodsByLabelAndNode", arg0, arg1)
	ret0, _ := ret[0].([]v1.Pod)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPodsByLabelAndNode indicates an expected call of GetPodsByLabelAndNode.
func (mr *MockK8sInterfaceMockRecorder) GetPodsByLabelAndNode(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPodsByLabelAndNode", reflect.TypeOf((*MockK8sInterface)(nil).GetPodsByLabelAndNode), arg0, arg1)
}

// GetPodsByNamespaceAndNode mocks base method.
func (m *MockK8sInterface) GetPodsByNamespaceAndNode(arg0, arg1 string) ([]v1.Pod, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPodsByNamespaceAndNode", arg0, arg1)
	ret0, _ := ret[0].([]v1.Pod)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPodsByNamespaceAndNode indicates an expected call of GetPodsByNamespaceAndNode.
func (mr *MockK8sInterfaceMockRecorder) GetPodsByNamespaceAndNode(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPodsByNamespaceAndNode", reflect.TypeOf((*MockK8sInterface)(nil).GetPodsByNamespaceAndNode), arg0, arg1)
}

// SetNodeLabel mocks base method.
func (m *MockK8sInterface) SetNodeLabel(arg0 string, arg1 *rpc.LabelSelector) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetNodeLabel", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetNodeLabel indicates an expected call of SetNodeLabel.
func (mr *MockK8sInterfaceMockRecorder) SetNodeLabel(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetNodeLabel", reflect.TypeOf((*MockK8sInterface)(nil).SetNodeLabel), arg0, arg1)
}

// SetPodLabel mocks base method.
func (m *MockK8sInterface) SetPodLabel(arg0 string, arg1 *rpc.LabelSelector) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetPodLabel", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetPodLabel indicates an expected call of SetPodLabel.
func (mr *MockK8sInterfaceMockRecorder) SetPodLabel(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetPodLabel", reflect.TypeOf((*MockK8sInterface)(nil).SetPodLabel), arg0, arg1)
}

// WatchPod mocks base method.
func (m *MockK8sInterface) WatchPod(arg0 string, arg1 chan struct{}, arg2 int64) (<-chan watch.Event, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "WatchPod", arg0, arg1, arg2)
	ret0, _ := ret[0].(<-chan watch.Event)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// WatchPod indicates an expected call of WatchPod.
func (mr *MockK8sInterfaceMockRecorder) WatchPod(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "WatchPod", reflect.TypeOf((*MockK8sInterface)(nil).WatchPod), arg0, arg1, arg2)
}

// CleanNamespacePendingPods 清除namespace下pending的pod.
func (mr *MockK8sInterfaceMockRecorder) CleanNamespacePendingPods(arg0 interface{}, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CleanNamespacePendingPods", reflect.TypeOf((*MockK8sInterface)(nil).CleanNamespacePendingPods), arg0, arg1)
}

func (m *MockK8sInterface) CleanNamespacePendingPods(arg0 string, arg1 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CleanNamespacePendingPods", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}
