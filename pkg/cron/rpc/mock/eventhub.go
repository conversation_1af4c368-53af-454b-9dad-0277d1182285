// Code generated by MockGen. DO NOT EDIT.
// Source: icode.baidu.com/baidu/faas/kun/pkg/cron/rpc (interfaces: EventhubInterface)

// Package mock is a generated GoMock package.
package mock

import (
	gomock "github.com/golang/mock/gomock"
	dao "icode.baidu.com/baidu/faas/kun/pkg/apiserver/dao"
	rest "icode.baidu.com/baidu/faas/kun/pkg/rest"
	reflect "reflect"
)

// MockEventhubInterface is a mock of EventhubInterface interface
type MockEventhubInterface struct {
	ctrl     *gomock.Controller
	recorder *MockEventhubInterfaceMockRecorder
}

// MockEventhubInterfaceMockRecorder is the mock recorder for MockEventhubInterface
type MockEventhubInterfaceMockRecorder struct {
	mock *MockEventhubInterface
}

// NewMockEventhubInterface creates a new mock instance
func NewMockEventhubInterface(ctrl *gomock.Controller) *MockEventhubInterface {
	mock := &MockEventhubInterface{ctrl: ctrl}
	mock.recorder = &MockEventhubInterfaceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use
func (m *MockEventhubInterface) EXPECT() *MockEventhubInterfaceMockRecorder {
	return m.recorder
}

// DryRun mocks base method
func (m *MockEventhubInterface) DryRun(arg0 *dao.FunctionReserved) (*rest.Result, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DryRun", arg0)
	ret0, _ := ret[0].(*rest.Result)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DryRun indicates an expected call of DryRun
func (mr *MockEventhubInterfaceMockRecorder) DryRun(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DryRun", reflect.TypeOf((*MockEventhubInterface)(nil).DryRun), arg0)
}
