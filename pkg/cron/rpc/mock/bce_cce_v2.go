// Code generated by MockGen. DO NOT EDIT.
// Source: icode.baidu.com/baidu/faas/kun/pkg/cron/rpc (interfaces: CceV2Interface)

// Package mock is a generated GoMock package.
package mock

import (
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	rpc "icode.baidu.com/baidu/faas/kun/pkg/cron/rpc"
)

// MockCceV2Interface is a mock of CceV2Interface interface.
type MockCceV2Interface struct {
	ctrl     *gomock.Controller
	recorder *MockCceV2InterfaceMockRecorder
}

// MockCceV2InterfaceMockRecorder is the mock recorder for MockCceV2Interface.
type MockCceV2InterfaceMockRecorder struct {
	mock *MockCceV2Interface
}

// NewMockCceV2Interface creates a new mock instance.
func NewMockCceV2Interface(ctrl *gomock.Controller) *MockCceV2Interface {
	mock := &MockCceV2Interface{ctrl: ctrl}
	mock.recorder = &MockCceV2InterfaceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockCceV2Interface) EXPECT() *MockCceV2InterfaceMockRecorder {
	return m.recorder
}

// GetClusterInfo mocks base method.
func (m *MockCceV2Interface) GetClusterInfo(arg0 string) (*rpc.CceGetV2ClusterInfoResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetClusterInfo", arg0)
	ret0, _ := ret[0].(*rpc.CceGetV2ClusterInfoResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetClusterInfo indicates an expected call of GetClusterInfo.
func (mr *MockCceV2InterfaceMockRecorder) GetClusterInfo(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetClusterInfo", reflect.TypeOf((*MockCceV2Interface)(nil).GetClusterInfo), arg0)
}

// GetCreatedFailedNodes mocks base method.
func (m *MockCceV2Interface) GetCreatedFailedNodes(arg0 string) ([]rpc.Instance, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCreatedFailedNodes", arg0)
	ret0, _ := ret[0].([]rpc.Instance)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCreatedFailedNodes indicates an expected call of GetCreatedFailedNodes.
func (mr *MockCceV2InterfaceMockRecorder) GetCreatedFailedNodes(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCreatedFailedNodes", reflect.TypeOf((*MockCceV2Interface)(nil).GetCreatedFailedNodes), arg0)
}

// GetNodes mocks base method.
func (m *MockCceV2Interface) GetNodes(arg0 string) ([]rpc.Instance, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetNodes", arg0)
	ret0, _ := ret[0].([]rpc.Instance)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetNodes indicates an expected call of GetNodes.
func (mr *MockCceV2InterfaceMockRecorder) GetNodes(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetNodes", reflect.TypeOf((*MockCceV2Interface)(nil).GetNodes), arg0)
}

// IsClusterRunning mocks base method.
func (m *MockCceV2Interface) IsClusterRunning(arg0 string) bool {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "IsClusterRunning", arg0)
	ret0, _ := ret[0].(bool)
	return ret0
}

// IsClusterRunning indicates an expected call of IsClusterRunning.
func (mr *MockCceV2InterfaceMockRecorder) IsClusterRunning(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IsClusterRunning", reflect.TypeOf((*MockCceV2Interface)(nil).IsClusterRunning), arg0)
}

// ScalingDown mocks base method.
func (m *MockCceV2Interface) ScalingDown(arg0 string, arg1 []string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ScalingDown", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// ScalingDown indicates an expected call of ScalingDown.
func (mr *MockCceV2InterfaceMockRecorder) ScalingDown(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ScalingDown", reflect.TypeOf((*MockCceV2Interface)(nil).ScalingDown), arg0, arg1)
}

// ScalingUp mocks base method.
func (m *MockCceV2Interface) ScalingUp(arg0 string, arg1 *rpc.ScalingUpParam) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ScalingUp", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// ScalingUp indicates an expected call of ScalingUp.
func (mr *MockCceV2InterfaceMockRecorder) ScalingUp(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ScalingUp", reflect.TypeOf((*MockCceV2Interface)(nil).ScalingUp), arg0, arg1)
}
