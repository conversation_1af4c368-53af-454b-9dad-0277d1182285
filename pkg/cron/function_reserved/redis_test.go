package function_reserved

import (
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/assert"

	"icode.baidu.com/baidu/faas/kun/pkg/api"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/dao"
	"icode.baidu.com/baidu/faas/kun/pkg/cron/rpc/mock"
	etcdMock "icode.baidu.com/baidu/faas/kun/pkg/etcd/mock"
	reserveMock "icode.baidu.com/baidu/faas/kun/pkg/store/mock"
	"icode.baidu.com/baidu/faas/kun/pkg/store/reserve"
	"icode.baidu.com/baidu/faas/kun/pkg/util/convert"
)

func TestGetReservedNodesFromRedis(t *testing.T) {
	mockCtrl := gomock.NewController(t)
	defer mockCtrl.Finish()
	manager := newMockReservedManager(t)

	config := &api.FunctionConfiguration{
		Uid: "uid",
	}
	config.FunctionArn = convert.String("brn")
	r := &api.GetFunctionOutput{
		Configuration: config,
	}
	ac := mock.NewMockApiserverInterface(mockCtrl)
	manager.ApiserverClient = ac
	ac.EXPECT().GetFunction("uid", "brn").Return(r, nil)

	ac.EXPECT().GetFunction("uid", "brn").Return(r, nil)
	labels, _ := manager.getLabels("uid", "brn")

	nodes := testNewNodes()
	etcdCli := etcdMock.NewMockEtcdInterface(mockCtrl)
	manager.EtcdClient = etcdCli
	n := &api.NodeInfo{
		ID:                 "node1",
		Name:               "1",
		ServiceType:        api.ServiceTypeCFC,
		ClusterLabels:      map[string]string{api.LabelCephfsZone: "zone1"},
		NodeLabels:         map[string]string{api.LabelUserID: "uid", api.LabelVipUser: "", api.LabelVpcConfigID: ""},
		OriginalMemorySize: 128,
	}
	etcdCli.EXPECT().GetNode("node1").Return(n, nil)

	reservedMgr := reserveMock.NewMockReserveManager(mockCtrl)
	manager.ReserveManager = reservedMgr
	nodeArgs := &reserve.GetReservedNodeMembersArgs{
		ServiceType:    api.ServiceTypeCFC,
		Labels:         labels,
		OriginMemory:   128,
		SchedulingType: api.SchedulingTypeCentral,
		Reserved:       true,
	}
	reservedMgr.EXPECT().GetReservedNodeMembers(nodeArgs).Return(nodes, nil)

	rs := &dao.FunctionReserved{
		Uid:           "uid",
		Uuid:          "uuid",
		FunctionBrn:   "brn",
		RealCount:     1,
		ReservedCount: 0,
	}
	_, err := manager.GetReservedNodesFromRedis(rs)
	assert.Nil(t, err)
}

func TestGetReservedPodsFromRedis(t *testing.T) {
	mockCtrl := gomock.NewController(t)
	defer mockCtrl.Finish()
	manager := newMockReservedManager(t)

	config := &api.FunctionConfiguration{
		Uid: "uid",
	}
	config.FunctionArn = convert.String("brn")
	r := &api.GetFunctionOutput{
		Configuration: config,
	}
	ac := mock.NewMockApiserverInterface(mockCtrl)
	manager.ApiserverClient = ac
	ac.EXPECT().GetFunction("uid", "brn").Return(r, nil)

	ac.EXPECT().GetFunction("uid", "brn").Return(r, nil)
	labels, _ := manager.getLabels("uid", "brn")

	pods := testNewPods()
	podArgs := &reserve.GetReservedPodMembersAgrs{
		ServiceType: api.ServiceTypeCFC,
		Labels:      labels,
		MemorySize:  int64(128),
		CommitID:    "commitid",
		Reserved:    true,
	}

	reservedMgr := reserveMock.NewMockReserveManager(mockCtrl)
	manager.ReserveManager = reservedMgr
	reservedMgr.EXPECT().GetReservedPodMembers(podArgs).Return(pods, nil)

	rs := &dao.FunctionReserved{
		Uid:           "uid",
		Uuid:          "uuid",
		FunctionBrn:   "brn",
		RealCount:     1,
		ReservedCount: 0,
		CommitID:      convert.String("commitid"),
		MemorySize:    128,
	}
	_, err := manager.GetReservedPodsFromRedis(rs)
	assert.Nil(t, err)
}

func TestRemReservedPod(t *testing.T) {
	mockCtrl := gomock.NewController(t)
	defer mockCtrl.Finish()
	manager := newMockReservedManager(t)

	config := &api.FunctionConfiguration{
		Uid: "uid",
	}
	config.FunctionArn = convert.String("brn")
	r := &api.GetFunctionOutput{
		Configuration: config,
	}
	ac := mock.NewMockApiserverInterface(mockCtrl)
	manager.ApiserverClient = ac
	ac.EXPECT().GetFunction("uid", "brn").Return(r, nil)

	ac.EXPECT().GetFunction("uid", "brn").Return(r, nil)
	labels, _ := manager.getLabels("uid", "brn")

	pods := testNewPods()
	podArgs := &reserve.GetReservedPodMembersAgrs{
		ServiceType: api.ServiceTypeCFC,
		Labels:      labels,
		MemorySize:  int64(128),
		CommitID:    "commitid",
		Reserved:    true,
	}

	reservedMgr := reserveMock.NewMockReserveManager(mockCtrl)
	manager.ReserveManager = reservedMgr
	reservedMgr.EXPECT().GetReservedPodMembers(podArgs).Return(pods, nil)

	pod := &api.PodInfo{
		NodeID:         "node1",
		PodName:        "pod1",
		IP:             "127.0.0.1",
		ServiceType:    api.ServiceTypeCFC,
		Labels:         labels,
		CommitID:       "commitid",
		MemorySize:     int64(128),
		SchedulingType: api.SchedulingTypeCentral,
		Reserved:       true,
	}

	reservedMgr.EXPECT().RemWarmPod(pod).Return(int64(1), nil)

	rs := &dao.FunctionReserved{
		Uid:           "uid",
		Uuid:          "uuid",
		FunctionBrn:   "brn",
		RealCount:     1,
		ReservedCount: 0,
		CommitID:      convert.String("commitid"),
		MemorySize:    128,
	}
	err := manager.RemReservedPod(rs)
	assert.Nil(t, err)
}
