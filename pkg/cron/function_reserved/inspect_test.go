package function_reserved

import (
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/assert"

	"icode.baidu.com/baidu/faas/kun/pkg/api"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/dao"
	implMock "icode.baidu.com/baidu/faas/kun/pkg/cron/impl/mock"
	"icode.baidu.com/baidu/faas/kun/pkg/cron/rpc/mock"
	etcdMock "icode.baidu.com/baidu/faas/kun/pkg/etcd/mock"
	reserveMock "icode.baidu.com/baidu/faas/kun/pkg/store/mock"
	"icode.baidu.com/baidu/faas/kun/pkg/store/reserve"
	"icode.baidu.com/baidu/faas/kun/pkg/util/convert"
)

func TestInspectUpdate(t *testing.T) {
	mockCtrl := gomock.NewController(t)
	defer mockCtrl.Finish()
	manager := newMockReservedManager(t)
	r := &dao.FunctionReserved{
		Uid:           "uid",
		Uuid:          "uuid",
		RealCount:     1,
		ReservedCount: 0,
	}

	ac := mock.NewMockApiserverInterface(mockCtrl)
	manager.ApiserverClient = ac
	ac.EXPECT().Update(r).Return(nil)
	err := manager.inspectUpdate(r, 0)
	assert.Nil(t, err)
}

func TestInspectTask(t *testing.T) {
	mockCtrl := gomock.NewController(t)
	defer mockCtrl.Finish()
	manager := newMockReservedManager(t)

	config := &api.FunctionConfiguration{
		Uid: "uid",
	}
	config.FunctionArn = convert.String("brn")
	r := &api.GetFunctionOutput{
		Configuration: config,
	}
	ac := mock.NewMockApiserverInterface(mockCtrl)
	manager.ApiserverClient = ac
	ac.EXPECT().GetFunction("uid", "brn").Return(r, nil)

	ac.EXPECT().GetFunction("uid", "brn").Return(r, nil)
	labels, _ := manager.getLabels("uid", "brn")

	pods := testNewPods()
	podArgs := &reserve.GetReservedPodMembersAgrs{
		ServiceType: api.ServiceTypeCFC,
		Labels:      labels,
		MemorySize:  int64(128),
		CommitID:    "commitid",
		Reserved:    true,
	}

	reservedMgr := reserveMock.NewMockReserveManager(mockCtrl)
	manager.ReserveManager = reservedMgr
	reservedMgr.EXPECT().GetReservedPodMembers(podArgs).Return(pods, nil)

	etcdCli := etcdMock.NewMockEtcdInterface(mockCtrl)
	manager.EtcdClient = etcdCli
	n := &api.NodeInfo{
		ID:                 "node1",
		Name:               "1",
		ServiceType:        api.ServiceTypeCFC,
		ClusterLabels:      map[string]string{api.LabelCephfsZone: "zone1"},
		NodeLabels:         map[string]string{api.LabelUserID: "uid", api.LabelVipUser: "", api.LabelVpcConfigID: ""},
		OriginalMemorySize: 128,
		FloatingIP:         "127.0.0.1",
		Reserved:           true,
	}
	etcdCli.EXPECT().GetNode("node1").Return(n, nil)

	podCli := implMock.NewMockPodControl(mockCtrl)
	manager.PodControl = podCli
	pod1 := &api.ContainerInfo{
		Hostname: "pod1",
	}
	podList := make([]*api.ContainerInfo, 0)
	podList = append(podList, pod1)
	podCli.EXPECT().GetHealthyContainersOnNode(gomock.Any()).Return(podList, nil)
	pod := &api.PodInfo{
		NodeID:         "node1",
		PodName:        "pod1",
		IP:             "127.0.0.1",
		ServiceType:    api.ServiceTypeCFC,
		Labels:         labels,
		CommitID:       "commitid",
		MemorySize:     int64(128),
		SchedulingType: api.SchedulingTypeCentral,
		Reserved:       true,
	}

	reservedMgr.EXPECT().RemWarmPod(pod).Return(int64(1), nil)
	podCli.EXPECT().GetHealthyContainersOnNode(gomock.Any()).Return(podList, nil)
	podCli.EXPECT().GetZombieContainersOnNode(gomock.Any()).Return(podList, nil)
	podCli.EXPECT().CoolDownPod(gomock.Any(), gomock.Any()).Return(nil)
	funcReserveds := testFunctionReserveds()
	err := manager.inspectTask(funcReserveds)
	assert.Nil(t, err)
}

func TestDoCoolDown(t *testing.T) {
	mockCtrl := gomock.NewController(t)
	defer mockCtrl.Finish()
	manager := newMockReservedManager(t)
	podCli := implMock.NewMockPodControl(mockCtrl)
	manager.PodControl = podCli

	pods := testNewPods()
	nodes := testNewNodes()
	containers := testNewContainers()
	podCli.EXPECT().GetHealthyContainersOnNode(gomock.Any()).Return(nil, nil)
	podCli.EXPECT().GetZombieContainersOnNode(gomock.Any()).Return(containers, nil)
	podCli.EXPECT().CoolDownPod(gomock.Any(), gomock.Any()).Return(nil)
	_, err := manager.doCoolDown(&pods[0], &nodes[0])
	assert.Nil(t, err)
}
