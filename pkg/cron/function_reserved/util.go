package function_reserved

import (
	"icode.baidu.com/baidu/faas/kun/pkg/api"
	"icode.baidu.com/baidu/faas/kun/pkg/util/logs"
)

func groupPodByNode(rm *ReservedManager, pods []api.PodInfo) (nodes map[string][]api.PodInfo, nodeMap map[string]api.NodeInfo) {
	nodes = make(map[string][]api.PodInfo, 0)
	nodeMap = make(map[string]api.NodeInfo, 0)
	nodeIDs := make(map[string]string) // 存储nodeID:floatingIP
	for _, p := range pods {
		var floatingIP string
		var nodeInfo *api.NodeInfo
		var err error
		// 根据nodeID获取nodeInfo，主要是为了获取node.FloatingIP
		if _, ok := nodeIDs[p.NodeID]; ok {
			floatingIP = nodeIDs[p.NodeID]
		} else {
			nodeInfo, err = rm.EtcdClient.GetNode(p.NodeID)
			if err != nil || !nodeInfo.Reserved {
				// etcd上node不存在，或不可用，则redis上pod不可用，需要将pod删除
				p.Reserved = true
				if _, err := rm.ReserveManager.RemWarmPod(&p); err != nil {
					logs.Infof("[function_reserved]rm pod from redis fail, err: %v", err)
				}
				logs.Infof("[function_reserved]get nodeInfo from etcd fail success")
				continue
			}
			if nodeInfo != nil {
				floatingIP = nodeInfo.FloatingIP
				nodeIDs[p.NodeID] = floatingIP
			}
		}

		if floatingIP != "" {
			if nodeInfo != nil {
				nodeMap[floatingIP] = *nodeInfo
			}
			nodes[floatingIP] = append(nodes[floatingIP], p)
		}

	}
	return
}


// 比较redis中保存的reserved pod与node上健康pod
// resp: redis中存在且是非健康状态的pod，即需要从redis中删除的pods
// resp: healthy containers，但redis中不存在的，说明pod已被unReserved，需要执行cooldown
func comparePods(commitId string, reservedPods []api.PodInfo, healthyContainers []*api.ContainerInfo) ([]*api.PodInfo, []*api.ContainerInfo) {
	var healthyMap = make(map[string]*api.ContainerInfo)
	var reservedMap = make(map[string]api.PodInfo)
	var changePods = make([]*api.PodInfo, 0)
	var cooldownContainers = make([]*api.ContainerInfo, 0)
	for _, c := range healthyContainers {
		healthyMap[c.Hostname] = c
	}
	for _, p := range reservedPods {
		reservedMap[p.PodName] = p
	}
	// redis reserved pod不是healthy pod
	for _, r := range reservedPods {
		if _, ok := healthyMap[r.PodName]; !ok {
			changePods = append(changePods, &r)
		} else {
			hc := healthyMap[r.PodName]
			if hc.RuntimeInfo == nil || (hc.RuntimeInfo != nil && hc.CommitID != r.CommitID) {
				// 此时pod已被回收，或被其他函数占用，则也需要将该pod从redis中删除
				changePods = append(changePods, &r)
			}
		}
	}

	// healthy pod不在redis上，说明该pod已不再预留，需要cooldown
	for _, h := range healthyContainers {
		if _, ok := reservedMap[h.Hostname]; !ok {
			if h.RuntimeInfo != nil && h.CommitID == commitId {
				cooldownContainers = append(cooldownContainers, h)
			}
		}
	}

	return changePods, cooldownContainers
}
