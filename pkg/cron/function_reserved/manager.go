package function_reserved

import (
	"errors"
	"fmt"
	"time"

	"github.com/coreos/etcd/clientv3"
	"go.uber.org/zap"

	"icode.baidu.com/baidu/faas/kun/cmd/cron/app/options"
	"icode.baidu.com/baidu/faas/kun/pkg/api"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/dao"
	"icode.baidu.com/baidu/faas/kun/pkg/cron/impl"
	"icode.baidu.com/baidu/faas/kun/pkg/cron/rpc"
	"icode.baidu.com/baidu/faas/kun/pkg/etcd"
	"icode.baidu.com/baidu/faas/kun/pkg/eventhub/invoke/label"
	funclet "icode.baidu.com/baidu/faas/kun/pkg/funclet/client"
	"icode.baidu.com/baidu/faas/kun/pkg/rpc/redis"
	"icode.baidu.com/baidu/faas/kun/pkg/store/reserve"
	"icode.baidu.com/baidu/faas/kun/pkg/util/logs"
	"icode.baidu.com/baidu/faas/kun/pkg/whitelist"
)

var ReservedConfig *api.ReservedConfig
var VipUsers  *whitelist.WhiteList

type ReservedManagerInterface interface {
	Run(concurrency int) // 预留实例预留/删除预留
	Inspect()            // 预留实例健康检查
	Recycle()            // 预留实例回收
}

type ReservedManager struct {
	logger             *logs.Logger
	notifier           *etcd.Notifier
	notifyChan         chan *api.FunctionReservedNotifyMsg
	mysqlChan          chan *dao.FunctionReserved // 预留实例任务池
	notifierState      etcd.NotificationType
	Options            *options.CronOptions
	EtcdClient         etcd.EtcdInterface
	ApiserverClient    rpc.ApiserverInterface
	EventhubClient     rpc.EventhubInterface
	PodControl         impl.PodControl
	ReserveManager     reserve.ReserveManager // redis manager
	LabelMgr           *label.Manager
}

func NewReservedManager(o *options.CronOptions) (*ReservedManager, error) {
	logger := logs.NewLogger().With(zap.String(api.AppNameKey, "cron"), zap.String("model", "function_reserved"))

	// 初始化etcd notifier
	etcdNotifier, err := etcd.NewNotifier(o.GenericEtcdOptions, api.FunctionReservedEtcdPrefix, true)
	if err != nil {
		return nil, err
	}

	// 初始化apiserver client
	apiserverCli := rpc.NewApiserverClient(o.CodeSweeperOptions.ApiserverEndpoint)

	// 初始化eventhub client
	eventhubCli, err := rpc.NewEventhubImpl(o)
	if err != nil {
		return nil, err
	}

	// 初始化etcd client
	etcdClient := etcd.NewClient()
	err = etcdClient.StartClient(o.GenericEtcdOptions)
	if err != nil {
		return nil, err
	}

	// 先从etcd中读取reserved config
	rc, _ := etcdClient.GetReservedConfig("function_reserved")
	if rc == nil {
		ReservedConfig = api.NewDefaultReservedConfig()
	} else {
		ReservedConfig = rc
	}

	VipUsers, err = whitelist.NewWhiteList(ReservedConfig.VIPUserOptions)
	if err != nil {
		return nil, err
	}
	go func() {
		for {
			rc, err := etcdClient.GetReservedConfig("function_reserved")
			if err != nil {
				logs.V(6).Warnf("[function_reserved] get reserved config from etcd fail, err: %v", err)
			}
			if rc != nil {
				ReservedConfig = rc
			}
			logs.V(9).Infof("[function_reserved] rc: %v", rc)
			// 更新vips
			VipUsers, err = whitelist.NewWhiteList(ReservedConfig.VIPUserOptions)
			if err != nil {
				logs.V(6).Warnf("[function_reserved] new vips fail, err: %v", err)
			}

			time.Sleep(10 * time.Second)
		}

	}()

	// 初始化funclet client
	funcletCli := funclet.NewFuncletClient(o.FuncletOptions)

	// 初始化reserve manager
	redisCli := redis.NewClient(o.RedisClusterOptions)
	rm := reserve.NewManager(redisCli, o.ReserveOptions)
	podCli := impl.NewPodControl(funcletCli, logger)

	return &ReservedManager{
		notifyChan:         make(chan *api.FunctionReservedNotifyMsg, 100),
		mysqlChan:          make(chan *dao.FunctionReserved, 100),
		notifier:           etcdNotifier,
		EtcdClient:         etcdClient,
		ApiserverClient:    apiserverCli,
		EventhubClient:     eventhubCli,
		Options:            o,
		logger:             logger,
		PodControl:         podCli,
		ReserveManager:     rm,
		LabelMgr:           &label.Manager{},
	}, nil
}

func (rm *ReservedManager) syncMysql() error {
	var reserveds []*dao.FunctionReserved
	var err error
	rm.logger.V(9).Infof("[function_reserved]start sync mysql")
	reserveds, err = rm.ApiserverClient.List("reserved", 0)
	if err != nil {
		return errors.New(fmt.Sprintf("sync mysql list function reserved fail, err: %v", err))
	}
	rm.logger.V(9).Debugf("[function_reserved]sync mysql list function reserved success, reserveds: %v", reserveds)
	for _, r := range reserveds {
		rm.mysqlChan <- r
	}

	return nil
}

func (rm *ReservedManager) watch() {
	// etcd 状态处理
	rm.notifier.SetNotificationHandler(func(ntf *etcd.Notification) {
		switch ntf.Type {
		case etcd.NotifyStart:
			rm.logger.V(9).Infof("[function_reserved]etcd notifier start msg %s", ntf.Msg)
		case etcd.NotifyStop:
			rm.logger.V(9).Infof("[function_reserved]etcd notifier stop msg %s", ntf.Msg)
		case etcd.NotifyError:
			rm.logger.V(9).Errorf("[function_reserved]etcd notifier err msg %s", ntf.Msg)
		}
		rm.notifierState = ntf.Type
	})

	rm.logger.V(9).Infof("[function_reserved]notifier set handler")
	// 收到通知之后处理流程
	rm.notifier.SetEventHandler(func(event *clientv3.Event) {
		if event.Type == clientv3.EventTypeDelete {
			return
		}
		notifyMsg := api.NewFunctionReservedNotifyMsg()
		err := notifyMsg.Unmarshal(event.Kv.Value)
		if err != nil {
			rm.logger.V(9).Errorf("[function_reserved]unmarshal notify msg (%s) error(%v)", event.Kv.Value, err)
			return
		}
		rm.logger.V(9).Debugf("[function_reserved]notify msg %s", event.Kv.Value)
		// 写入notify通知事件
		rm.notifyChan <- notifyMsg
	})
	// 开始watch
	go rm.notifier.Watch()
	rm.logger.V(9).Infof("[function_reserved]notifier watch begin")
}

func (rm *ReservedManager) doReservedMsg(r *dao.FunctionReserved) error {
	var err error
	if r.ReservedCount > r.RealCount {
		err = rm.reserved(r)
	} else {
		err = rm.unReserved(r)
		rm.logger.V(6).Infof("[function_reserved] unReserved err : %v", err)
	}
	return err
}

func (rm *ReservedManager) doNotifyMsg(msg *api.FunctionReservedNotifyMsg) {
	rm.logger.V(6).Infof("[function_reserved] receive etcd notify, uuid list :%v", msg.UUIDList)
	for _, v := range msg.UUIDList {
		// 查找数据库
		r, err := rm.ApiserverClient.Get(v)
		if err != nil {
			rm.logger.V(6).Errorf("get function reserved fail, uuid:%s, err: %v", v, err)
			continue
		}

		err = rm.doReservedMsg(r)
		if err != nil {
			rm.logger.V(6).Errorf("deal function reserved fail, uuid: %s, err: %v", v, err)
			continue
		}
		rm.logger.V(6).Infof("[function_reserved] deal function reserved success, uuid: %s", v)
		// TODO 预留实例信息更改，通知billing上报
	}

}

func (rm *ReservedManager) Run(concurrency int) {
	rm.logger.V(6).Infof("release %d workers to work", concurrency)
	for i := 0; i < concurrency; i++ {
		go rm.work()
	}

	// 监听etcd通知
	if rm.notifier != nil {
		rm.watch()
	}

	// 同步mysql
	go func() {
		for {
			err := rm.syncMysql()
			if err != nil {
				rm.logger.V(6).Errorf("sync mysql fail, err: %v", err)
				time.Sleep(5 * time.Second)
			}
			time.Sleep(ReservedConfig.SyncMysqlInterval * time.Second)
		}
	}()
}

func (rm *ReservedManager) work() {
	for {
		select {
		case r := <-rm.mysqlChan:
			if err := rm.doReservedMsg(r); err != nil {
				continue
			}
		case notifyMsg := <-rm.notifyChan:
			rm.doNotifyMsg(notifyMsg)
		}
	}
}

// 预留实例健康检查
func (rm *ReservedManager) Inspect() {
	for {
		rm.logger.V(6).Infof("[function_reserved][inspect] start inspect function reserved")

		// 从mysql中获取预留实例列表
		var reserveds []*dao.FunctionReserved
		var err error
		reserveds, err = rm.ApiserverClient.List("inspect", 0)
		if err != nil {
			rm.logger.V(6).Errorf("[function_reserved][inspect] get function reserved list fail, err: %v", err)
			time.Sleep(5 * time.Second)
			continue
		}

		if err := rm.inspectTask(reserveds); err != nil {
			rm.logger.V(6).Errorf("[function_reserved][inspect] inspectTask fail, err: %v", err)
			time.Sleep(5 * time.Second)
			continue
		}

		rm.logger.V(6).Infof("[function_reserved][inspect] end inspect function reserved")
		time.Sleep(ReservedConfig.SyncMysqlInterval * time.Second)
	}
}

// 预留实例回收
func (rm *ReservedManager) Recycle() {
	for {
		rm.logger.V(6).Infof("[function_reserved][recycle] start recycle function reserved")

		// 从mysql中获取删除状态的预留实例列表
		var reserveds []*dao.FunctionReserved
		var delReserveds []*dao.FunctionReserved
		var err error
		// TODO 可以只选择List一定时间内删除数据，比如一周内，具有时间可以在ReservedConfig中配置
		reserveds, err = rm.ApiserverClient.List("recycle", ReservedConfig.ListReservedDuration)
		if err != nil {
			rm.logger.V(6).Errorf("[function_reserved][recycle] get function reserved list fail, err: %v", err)
			time.Sleep(5 * time.Second)
			continue
		}

		// 过滤，只取已经删除的预留实例
		for _, r := range reserveds {
			// 根据状态和预留实例个数进行判断
			if r.Status != api.ReservedCreating && (r.ReservedCount == 0 && r.RealCount == 0) {
				delReserveds = append(delReserveds, r)
			}
		}

		rm.logger.V(6).Infof("[function_reserved][recycle] delReserveds: %v", delReserveds)
		if err = rm.recycleTask(delReserveds); err != nil {
			rm.logger.V(6).Errorf("[function_reserved][recycle] recycleTask fail, err: %v", err)
			time.Sleep(5 * time.Second)
			continue
		}

		rm.logger.V(6).Infof("[function_reserved][recycle] end recycle function reserved")
		time.Sleep(ReservedConfig.SyncMysqlInterval * time.Second)
	}
}
