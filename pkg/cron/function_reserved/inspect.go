package function_reserved

import (
	"sync"
	"sync/atomic"
	"time"

	"icode.baidu.com/baidu/faas/kun/pkg/api"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/dao"
)

// 根据健康检查结果更新实际预留实例个数
func (rm *ReservedManager) inspectUpdate(r *dao.FunctionReserved, count int) (err error) {
	rm.logger.V(6).Infof("[function_reserved] [inspect] start update ")

	// 检查预留实例状态，是否可以更改, 若预留实例状态正在更改中，则跳过本轮健康检查
	timeout := time.Now().Unix() - r.ReservedTs
	if r.Status == api.ReservedCreating && timeout < ReservedConfig.TaskExpiration {
		rm.logger.V(6).Errorf("[function_reserved] [inspect] function reserved is creating, please skip this inspect task")
		return
	}

	if r.RealCount == count {
		rm.logger.V(6).Infof("[function_reserved] [inspect] there is no need to update real count")
		return
	}

	// 系统真正删除预留实例
	if r.ReservedCount == 0 && r.RealCount == 0 && count == 0 {
		r.Status = api.ReservedDeleted
	}
	// 可以更新数据库中预留实例信息
	r.RealCount = count
	r.ReservedTs = time.Now().Unix()
	err = rm.ApiserverClient.Update(r)
	if err != nil {
		rm.logger.V(6).Errorf("[function_reserved] [inspect] update function reserved fail, err: %v", err)
		return
	}

	rm.logger.V(6).Infof("[function_reserved] [inspect] end update, update reserved RealCount success!")
	return
}

// 预留实例健康检查, 只对node上healthy pod数 < redis上reserved pod数情况
// 其他情况交给预留实例回收
func (rm *ReservedManager) inspectTask(reserveds []*dao.FunctionReserved) (err error) {
	var wg sync.WaitGroup
	var sem = make(chan struct{}, ReservedConfig.ReservedStep)
	for _, r := range reserveds {
		wg.Add(1)
		go func(fr *dao.FunctionReserved) {
			sem <- struct{}{}
			defer func() {
				<-sem
				wg.Done()

			}()
			// 获取redis reserved pod集合
			redisPods, err := rm.GetReservedPodsFromRedis(r)
			if err != nil {
				rm.logger.V(6).Errorf("[function_reserved][inspect] get pod from redis fail err: %v", err)
				return
			}

			rm.logger.V(6).Debugf("[function_reserved][inspect] redis reserved pod: %v, len: %v", redisPods, len(redisPods))
			// 预留实例为空
			if len(redisPods) == 0 {
				err = rm.inspectUpdate(r, 0)
				rm.logger.V(6).Infof("[function_reserved][inspect] redis reserved pod is empty")
				return
			}

			// 预留实例不为空的情况
			/*
			1、pod根据node进行分组
			2、检查node上健康pod
			3、对比node上健康pod、redis中保存的reserved pod
			 */
			nodes, nodeInfoMap := groupPodByNode(rm, redisPods)
			count := 0 // 记录系统中预留实例个数更改数量
			for ip, ps := range nodes {
				n := nodeInfoMap[ip]
				if &n == nil {
					rm.logger.V(6).Infof("[function_reserved] node is nil")
					continue
				}
				// 调用funclet获取node上健康pod列表
				healthyContainers, err := rm.PodControl.GetHealthyContainersOnNode(&n)
				if err != nil {
					rm.logger.V(6).Errorf("[function_reserved][inspect] get healthy containers fail, floatingIP: %s ,err: %v", ip, err)
					continue
				}

				rm.logger.V(6).Debugf("[function_reserved][inspect] get healthy containers success, containers: %v", healthyContainers)
				// 对比node上预留pod与健康pod，如果预留pod为非健康pod，则需将redis上保存的预留pod删除
				changePods, cooldownContainers := comparePods(*r.CommitID, ps, healthyContainers)

				var memoryRecycled int64 = 0 // 已回收的内存数量
				// cooldown containers
				var coolDownSem = make(chan struct{}, ReservedConfig.CoolDownConcurrency)
				var coolDownWg sync.WaitGroup
				for _, cc := range cooldownContainers {
					coolDownWg.Add(1)
					go func(c *api.ContainerInfo, node *api.NodeInfo) {
						coolDownSem <- struct{}{}
						defer func() {
							<- coolDownSem
							coolDownWg.Done()
						}()

						if err := rm.PodControl.CoolDownPod(c, node); err != nil {
							rm.logger.V(6).Errorf("[function_reserved][inspect] cool dowm pod fail, err: %v", err)
							return
						}
						if cc.ResourceStats != nil && cc.ResourceStats.MemoryStats != nil {
							memory := api.Byte2Mi(cc.ResourceStats.MemoryStats.Limit)
							atomic.AddInt64(&memoryRecycled, memory)
						}
						return
					}(cc, &n)
				}
				coolDownWg.Wait()

				// cooldown change pods
				var changeSem = make(chan struct{}, ReservedConfig.CoolDownConcurrency)
				var changeWg sync.WaitGroup
				for _, cp := range changePods {
					if cp != nil {
						changeWg.Add(1)
						// 补充预留标识
						cp.Reserved = true
						res, err := rm.ReserveManager.RemWarmPod(cp)
						if err != nil || res != 1 {
							rm.logger.V(6).Errorf("[function_reserved][inspect] rem unhealthy pod from redis fail, err: %v", err)
							continue
						}
						rm.logger.V(6).Infof("[function_reserved][inspect] rem unhealthy pod from redis success")
						count++

						// 对不健康pod做cooldown操作
						// 这里只CoolDown一次，失败后，只能交给cron通过健康检查回收了
						go func(pod *api.PodInfo, node *api.NodeInfo) {
							changeSem <- struct{}{}
							defer func() {
								<-changeSem
								changeWg.Done()
							}()
							memory, err := rm.doCoolDown(pod, node)
							if err != nil {
								rm.logger.V(6).Errorf("[function_reserved][inspect] cool dowm pod fail, err: %v", err)
								return
							}
							atomic.AddInt64(&memoryRecycled, memory)
						}(cp, &n)
					}
				}
				changeWg.Wait()

				if memoryRecycled != 0 {
					n.Reserved = true
					_, err = rm.ReserveManager.IncrWarmNodeMemoryXX(&n, memoryRecycled)
					if err != nil {
						rm.logger.V(6).Errorf("[function_reserved][inspect] incr reserved node memory fail, err: %v", err)
					}
					rm.logger.V(6).Infof("[function_reserved][inspect] incr reserved node memory success")
				}
				rm.logger.V(6).Infof("[function_reserved][inspect] inspect containers success")
			}


			// 更新数据库中realCount
			realCount := len(redisPods) - count
			// 更正realCount
			if realCount < 0 {
				realCount = 0
			}
			rm.inspectUpdate(r, realCount)
		}(r)
	}

	wg.Wait()
	return
}

func (rm *ReservedManager) redisPod2Container(pod *api.PodInfo, containers []*api.ContainerInfo) *api.ContainerInfo {
	for _, c := range containers{
		if pod.PodName == c.Hostname {
			return c
		}
	}
	return nil
}

func (rm *ReservedManager) doCoolDown(pod *api.PodInfo, node *api.NodeInfo) (memory int64, err error){
	var zc *api.ContainerInfo
	healthyContainers, err := rm.PodControl.GetHealthyContainersOnNode(node)
	if err != nil {
		rm.logger.V(6).Errorf("[function_reserved][inspect] get healthy containers fail, floatingIP: %s ,err: %v", node.FloatingIP, err)
		return
	}


	zombieContainers, err := rm.PodControl.GetZombieContainersOnNode(node)
	if err != nil {
		rm.logger.V(6).Errorf("[function_reserved][inspect] get zombie containers fail, floatingIP: %s ,err: %v", node.FloatingIP, err)
		return
	}
	zc = rm.redisPod2Container(pod, healthyContainers)
	if zc == nil {
		zc = rm.redisPod2Container(pod, zombieContainers)
	}
	if zc != nil {
		rm.logger.V(6).Infof("[function_reserved][inspect] start cooldown zombie pod")
		if err = rm.PodControl.CoolDownPod(zc, node); err != nil {
			rm.logger.V(6).Errorf("[function_reserved][inspect] cooldown zombie pod fail, err: %v", err)
			return
		}
		rm.logger.V(6).Infof("[function_reserved][inspect] cooldown zombie pod success")
		if zc.ResourceStats != nil && zc.ResourceStats.MemoryStats != nil {
			memory = api.Byte2Mi(zc.ResourceStats.MemoryStats.Limit)
			return
		}
	}
	return
}
