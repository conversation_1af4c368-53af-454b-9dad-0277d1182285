package function_reserved

func(rm *ReservedManager) getLabels(uid string, brn string) (labels map[string]string, err error) {
	// 获取函数信息
	f, err := rm.ApiserverClient.GetFunction(uid, brn)
	if err != nil {
		rm.logger.V(6).<PERSON>rrorf("[function_reserved] get function fail, err: %v", err)
		return
	}
	clusterVipLabel := ""
	vipUser, is_vip := VipUsers.Get(uid)
	if is_vip {
		clusterVipLabel = vipUser.Source
	}
	labels = rm.LabelMgr.GetLabel(uid, f, clusterVipLabel)
	return labels, nil
}

