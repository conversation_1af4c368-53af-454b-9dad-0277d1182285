package function_reserved

import (
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/assert"

	"icode.baidu.com/baidu/faas/kun/pkg/api"
	"icode.baidu.com/baidu/faas/kun/pkg/cron/rpc/mock"
)

func TestGetLabels(t *testing.T) {
	mockCtrl := gomock.NewController(t)
	defer mockCtrl.Finish()
	manager := newMockReservedManager(t)
	config := &api.FunctionConfiguration{
		Uid: "uid",
	}
	r := &api.GetFunctionOutput{
		Configuration:config,
	}
	ac := mock.NewMockApiserverInterface(mockCtrl)
	manager.ApiserverClient = ac
	ac.EXPECT().GetFunction("uid", "brn").Return(r, nil)
	_, err := manager.getLabels("uid", "brn")
	assert.Nil(t, err)

}
