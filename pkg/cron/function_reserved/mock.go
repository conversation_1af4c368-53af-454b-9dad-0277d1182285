package function_reserved

import (
	"testing"

	"github.com/golang/mock/gomock"

	"icode.baidu.com/baidu/faas/kun/pkg/api"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/dao"
	"icode.baidu.com/baidu/faas/kun/pkg/etcd"
	"icode.baidu.com/baidu/faas/kun/pkg/util/convert"
	"icode.baidu.com/baidu/faas/kun/pkg/whitelist"
)

const testPrefix = "kun_test"

func testFunctionReserveds() []*dao.FunctionReserved {
	return []*dao.FunctionReserved{
		{
			Uid:           "uid",
			Uuid:          "uuid",
			FunctionBrn:   "brn",
			RealCount:     0,
			ReservedCount: 0,
			CommitID:      convert.String("commitid"),
			MemorySize:    128,
		},
	}
}

func testNewContainers() []*api.ContainerInfo {
	ms := &api.MemoryStats{
		Limit: 128,
	}
	rs := &api.ResourceStats{
		MemoryStats: ms,
	}
	runtime := &api.RuntimeInfo{
		CommitID:    "commitID",
		ServiceType: "cfc",
		Labels:      map[string]string{"test": "test"},
	}
	return []*api.ContainerInfo{
		{
			Hostname:      "pod1",
			ResourceStats: rs,
			RuntimeInfo:   runtime,
		},
	}
}

func testNewPods() []api.PodInfo {
	return []api.PodInfo{
		{
			NodeID:         "node1",
			PodName:        "pod1",
			IP:             "127.0.0.1",
			ServiceType:    api.ServiceTypeCFC,
			Labels:         map[string]string{api.LabelUserID: "uid", api.LabelVipUser: "", api.LabelVpcConfigID: ""},
			CommitID:       "commitid",
			MemorySize:     128,
			SchedulingType: api.SchedulingTypeCentral,
		},
	}
}

func testNewNodes() []api.NodeInfo {
	return []api.NodeInfo{
		{
			ID:                 "node1",
			Name:               "1",
			ServiceType:        api.ServiceTypeCFC,
			ClusterLabels:      map[string]string{api.LabelCephfsZone: "zone1"},
			NodeLabels:         map[string]string{api.LabelUserID: "uid", api.LabelVipUser: "", api.LabelVpcConfigID: ""},
			OriginalMemorySize: 128,
		},
		// {
		//	ID:                 "node2",
		//	Name:               "2",
		//	ServiceType:        api.ServiceTypeCFC,
		//	ClusterLabels:      map[string]string{api.LabelCephfsZone: "zone2"},
		//	NodeLabels:         map[string]string{},
		//	OriginalMemorySize: 128,
		// },
	}
}

func newMockReservedManager(t *testing.T) *ReservedManager {
	mockCtrl := gomock.NewController(t)
	defer mockCtrl.Finish()
	etcdNotify, _ := etcd.NewNotifier(&etcd.Options{Endpoints: []string{"127.0.0.1:2379"}, Prefix: testPrefix}, "notify", true)
	ReservedConfig = api.NewDefaultReservedConfig()
	VipUsers, _ = whitelist.NewWhiteList(ReservedConfig.VIPUserOptions)
	return &ReservedManager{
		notifyChan: make(chan *api.FunctionReservedNotifyMsg, 10),
		mysqlChan:  make(chan *dao.FunctionReserved, 10),
		notifier:   etcdNotify,
	}
}
