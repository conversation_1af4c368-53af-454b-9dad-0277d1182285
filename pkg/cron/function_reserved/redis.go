package function_reserved

import (
	"fmt"
	"math"

	"icode.baidu.com/baidu/faas/kun/pkg/api"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/dao"
	"icode.baidu.com/baidu/faas/kun/pkg/store/reserve"
	"icode.baidu.com/baidu/faas/kun/pkg/util/logs"
)

// 获取redis上预留node列表
func (rm *ReservedManager) GetReservedNodesFromRedis(r *dao.FunctionReserved)(nodes []api.NodeInfo, err error) {
	// 获取labels
	labels, err := rm.getLabels(r.Uid, r.FunctionBrn)
	if err != nil {
		rm.logger.V(6).Errorf("[function_reserved]get labels fail, uid: %s, brn: %s, err: %v", r.Uid, r.FunctionBrn, err)
		return
	}

	// 获取redis上保存的预留node列表
	nodeArgs := &reserve.GetReservedNodeMembersArgs{
		ServiceType: api.ServiceTypeCFC,
		Labels:  labels,
		OriginMemory:128,
		SchedulingType: api.SchedulingTypeCentral,
		Reserved: true,
	}
	redisNodes, err := rm.ReserveManager.GetReservedNodeMembers(nodeArgs)
	if err != nil {
		rm.logger.V(6).Errorf("[function_reserved]get reserved nodes from redis fail, uid: %s, brn: %s, err: %v", r.Uid, r.FunctionBrn, err)
		return
	}

	nodes = rm.GetNodesInfo(redisNodes)
	return
}

// 获取redis上预留pod列表
func (rm *ReservedManager) GetReservedPodsFromRedis(r *dao.FunctionReserved) (pods []api.PodInfo, err error) {
	// 获取redis保存预留pod列表
	labels, err := rm.getLabels(r.Uid, r.FunctionBrn)
	if err != nil {
		return
	}

	podArgs := &reserve.GetReservedPodMembersAgrs{
		ServiceType: api.ServiceTypeCFC,
		Labels:labels,
		MemorySize: int64(r.MemorySize),
		CommitID: *r.CommitID,
		Reserved: true,
	}

	pods, err = rm.ReserveManager.GetReservedPodMembers(podArgs)
	if err != nil {
		rm.logger.V(6).Errorf("[function_reserved]get reserved pods from redis fail, err: %v", err)
		return
	}
	return
}

func (rm *ReservedManager) RemReservedPod(r *dao.FunctionReserved) (err error) {
	// 获取redis保存预留pod列表
	pods, err := rm.GetReservedPodsFromRedis(r)
	if err != nil {
		return
	}
	if len(pods) == 0 {
		// TODO 是否需要将mysql中的RealCount更新为0
		rm.logger.V(6).Errorf("[function_reserved]reserved pods is empty")
		err = fmt.Errorf("reserved pod is empty")
		return
	}
	pod := ChoosePod(pods, ReservedConfig.PodMaxConcurrency)
	if pod != nil {
		// 补充pod.Reserved，防止pod.Reserved为填写的情况
		pod.Reserved = true
		// 防止多个goroutine选择到同一个pod，这里需要判断删除是否成功，只有成功时，才增加unReserved值
		res, err1 := rm.ReserveManager.RemWarmPod(pod)
		if err1 != nil || res != 1 {
			if err1 == nil {
				err = fmt.Errorf("reserved pod is is not exist")
			}
			rm.logger.V(6).Errorf("[function_reserved]remove reserved pod from redis fail, pod: %v, err :%v",pod, err)
			return
		}
		rm.logger.V(6).Infof("[function_reserved]remove reserved pod from redis success, pod: %v", pod)
	} else {
		rm.logger.V(6).Infof("[function_reserved]reserved pod is used")
		err = fmt.Errorf("reserved pod is used")
	}

	return
}

// 选择从较少预留pod的node上删除预留pod
func ChoosePod(pods []api.PodInfo, concurrency int64)(pod *api.PodInfo) {
	nodes := map[string][]api.PodInfo{}
	for _, p := range pods {
		// 只选择并发度为0，即没被占用的pod
		if p.Concurrency > 0 && p.Concurrency < concurrency{
			logs.V(6).Infof("[function_reserved] pod concurrency: %v", p.Concurrency)
			continue
		}
		nodes[p.NodeID] = append(nodes[p.NodeID], p)
	}
	count := math.MaxInt16
	id := ""
	for i, p := range nodes {
		if len(p) < count {
			count = len(p)
			id = i
		}
	}

	// TODO 选择空闲pod删除, Concurrency=0?
	if _, ok := nodes[id]; ok {
		if len(nodes[id]) > 0 {
			pod = &nodes[id][0]
		}
	}
	return
}

// 获取从etcd上获取nodeInfo，主要因为redis上存储的node信息补全，没有floatingIP
func(rm *ReservedManager) GetNodesInfo(nodes []api.NodeInfo)(res []api.NodeInfo){

	// 存储nodeID与FloatingIP
	nodeIDMap := make(map[string]api.NodeInfo)

	for _, n := range nodes {
		if _, ok := nodeIDMap[n.ID]; ok {
			res = append(res, nodeIDMap[n.ID])
			continue
		}
		// 不存在则查etcd
		nodeInfo, err := rm.EtcdClient.GetNode(n.ID)
		if err != nil {
			// 出错表示etcd上node不存在或node不可用
			// etcd上node不存在，则将redis上存储的node member删除
			n.Reserved = true
			if _, err = rm.ReserveManager.RemWarmNode(&n); err != nil {
				rm.logger.V(6).Warnf("[function_reserved]rm node from redis fail, err: %v", err)
			}
			continue
		}
		//n.FloatingIP = nodeInfo.FloatingIP
		if nodeInfo != nil {
			nodeIDMap[n.ID] = *nodeInfo
			res = append(res, *nodeInfo)
		}
	}
	return res
}
