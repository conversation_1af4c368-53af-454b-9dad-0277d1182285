package function_reserved

import (
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/assert"

	"icode.baidu.com/baidu/faas/kun/pkg/api"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/dao"
	implMock "icode.baidu.com/baidu/faas/kun/pkg/cron/impl/mock"
	"icode.baidu.com/baidu/faas/kun/pkg/cron/rpc/mock"
	etcdMock "icode.baidu.com/baidu/faas/kun/pkg/etcd/mock"
	reserveMock "icode.baidu.com/baidu/faas/kun/pkg/store/mock"
	"icode.baidu.com/baidu/faas/kun/pkg/util/convert"
)

func TestRecycle(t *testing.T) {
	mockCtrl := gomock.NewController(t)
	defer mockCtrl.Finish()
	manager := newMockReservedManager(t)

	config := &api.FunctionConfiguration{
		Uid: "uid",
	}
	config.FunctionArn = convert.String("brn")
	r := &api.GetFunctionOutput{
		Configuration: config,
	}
	ac := mock.NewMockApiserverInterface(mockCtrl)
	manager.ApiserverClient = ac
	ac.EXPECT().GetFunction(gomock.Any(), gomock.Any()).Return(r, nil)

	nodes := testNewNodes()
	etcdCli := etcdMock.NewMockEtcdInterface(mockCtrl)
	manager.EtcdClient = etcdCli
	n := &api.NodeInfo{
		ID:                 "node1",
		Name:               "1",
		ServiceType:        api.ServiceTypeCFC,
		ClusterLabels:      map[string]string{},
		NodeLabels:         map[string]string{api.LabelUserID: "uid", api.LabelVipUser: "", api.LabelVpcConfigID: ""},
		OriginalMemorySize: 128,
		Reserved:           true,
	}
	etcdCli.EXPECT().GetNode("node1").Return(n, nil)

	reservedMgr := reserveMock.NewMockReserveManager(mockCtrl)
	manager.ReserveManager = reservedMgr
	reservedMgr.EXPECT().GetReservedNodeMembers(gomock.Any()).Return(nodes, nil)
	podCli := implMock.NewMockPodControl(mockCtrl)
	manager.PodControl = podCli

	containers := testNewContainers()
	podCli.EXPECT().GetHealthyContainersOnNode(gomock.Any()).Return(containers, nil)

	reservedMgr.EXPECT().IsReservedPodExist(gomock.Any()).Return(false)

	reservedMgr.EXPECT().RemWarmNode(n).Return(int64(1), nil)
	etcdCli.EXPECT().SetNode(gomock.Any()).Return(nil)
	reserveds := testFunctionReserveds()
	err := manager.recycleTask(reserveds)
	assert.Nil(t, err)
}

func TestCheckHealthyContainers(t *testing.T) {
	mockCtrl := gomock.NewController(t)
	defer mockCtrl.Finish()
	manager := newMockReservedManager(t)

	config := &api.FunctionConfiguration{
		Uid: "uid",
	}
	config.FunctionArn = convert.String("brn")
	r := &api.GetFunctionOutput{
		Configuration: config,
	}
	ac := mock.NewMockApiserverInterface(mockCtrl)
	manager.ApiserverClient = ac

	ac.EXPECT().GetFunction(gomock.Any(), gomock.Any()).Return(r, nil)
	manager.getLabels("uid", "brn")

	reservedMgr := reserveMock.NewMockReserveManager(mockCtrl)
	manager.ReserveManager = reservedMgr

	c := &api.ContainerInfo{
		Hostname: "pod1",
	}
	rt := &api.RuntimeInfo{
		MemorySize: 128,
		CommitID:   "commitid",
	}
	c.RuntimeInfo = rt
	healthyContainers := []*api.ContainerInfo{
		c,
	}
	n := &api.NodeInfo{
		ID:                 "node1",
		Name:               "1",
		ServiceType:        api.ServiceTypeCFC,
		ClusterLabels:      map[string]string{},
		NodeLabels:         map[string]string{api.LabelUserID: "uid", api.LabelVipUser: "", api.LabelVpcConfigID: ""},
		OriginalMemorySize: 128,
		FloatingIP:         "127.0.0.1",
		Reserved:           true,
	}

	ac.EXPECT().GetFunction("uid", "brn").Return(r, nil)
	reservedMgr.EXPECT().IsReservedPodExist(gomock.Any()).Return(true)
	rs := &dao.FunctionReserved{
		Uid:           "uid",
		Uuid:          "uuid",
		FunctionBrn:   "brn",
		RealCount:     1,
		ReservedCount: 0,
		CommitID:      convert.String("commitid"),
		MemorySize:    128,
	}
	flag := manager.checkHealthyContainers(rs, n, healthyContainers)
	assert.Equal(t, true, flag)
}
