package function_reserved

import (
	"testing"

	"github.com/stretchr/testify/assert"

	"icode.baidu.com/baidu/faas/kun/pkg/api"
)

func TestComparePods(t *testing.T) {
	pods := testNewPods()
	containers := testNewContainers()

	runtime := &api.RuntimeInfo{
		CommitID:"commitid",
		ServiceType: "cfc",
		Labels: map[string]string{"test":"test"},
	}
	c := &api.ContainerInfo{
		Hostname:"pod2",
		RuntimeInfo: runtime,
	}
	containers = append(containers, c)
	p := api.PodInfo{
		PodName:"pod3",
	}
	pods = append(pods, p)
	res1, res2 := comparePods("commitid", pods, containers)
	assert.Equal(t, 2, len(res1))
	assert.Equal(t, 1, len(res2))
}
