package function_reserved

import (
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/assert"

	"icode.baidu.com/baidu/faas/kun/cmd/cron/app/options"
	"icode.baidu.com/baidu/faas/kun/pkg/api"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/dao"
	"icode.baidu.com/baidu/faas/kun/pkg/cron/rpc/mock"
	"icode.baidu.com/baidu/faas/kun/pkg/rest"
	"icode.baidu.com/baidu/faas/kun/pkg/util/convert"
)

func TestNewReservedManager(t *testing.T) {
	o := options.NewCronOptions()
	o.IAMConfiguration.IAMConfPath = "../../bce/iam/mock.yaml"
	_, err := NewReservedManager(o)
	assert.Nil(t, err)
}

func TestSyncMysql(t *testing.T) {
	mockCtrl := gomock.NewController(t)
	defer mockCtrl.Finish()
	manager := newMockReservedManager(t)
	reserveds := testFunctionReserveds()
	ac := mock.NewMockApiserverInterface(mockCtrl)
	manager.ApiserverClient = ac
	ac.EXPECT().List(gomock.Any(), gomock.Any()).Return(reserveds, nil)
	err := manager.syncMysql()
	assert.Nil(t, err)

}

func TestWatch(t *testing.T) {
	mockCtrl := gomock.NewController(t)
	defer mockCtrl.Finish()
	manager := newMockReservedManager(t)
	manager.watch()
}

func TestDoNotifyMsg(t *testing.T) {
	mockCtrl := gomock.NewController(t)
	defer mockCtrl.Finish()
	manager := newMockReservedManager(t)

	msg := &api.FunctionReservedNotifyMsg{
		UUIDList: []string{"uuid"},
		Method:"create",
	}

	r := &dao.FunctionReserved {
		Uid: "uid",
		Uuid: "uuid",
		RealCount: 0,
		ReservedCount:1,
	}

	ac := mock.NewMockApiserverInterface(mockCtrl)
	ac.EXPECT().Get("uuid").Return(r, nil)
	ac.EXPECT().Get("uuid").Return(r, nil)
	ac.EXPECT().Update(r).Return(nil)
	manager.ApiserverClient = ac

	res1 := &rest.Result{}
	res1.StatusCode(convert.Int(200))

	ec := mock.NewMockEventhubInterface(mockCtrl)
	manager.EventhubClient = ec
	ec.EXPECT().DryRun(r).Return(res1, nil)

	ac.EXPECT().Update(r).Return(nil)
	manager.doNotifyMsg(msg)
}
