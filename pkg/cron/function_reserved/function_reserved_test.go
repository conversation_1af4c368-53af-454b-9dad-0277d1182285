package function_reserved

import (
	"testing"
	"time"

	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/assert"

	"icode.baidu.com/baidu/faas/kun/pkg/api"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/dao"
	"icode.baidu.com/baidu/faas/kun/pkg/cron/rpc/mock"
	"icode.baidu.com/baidu/faas/kun/pkg/rest"
	reserveMock "icode.baidu.com/baidu/faas/kun/pkg/store/mock"
	"icode.baidu.com/baidu/faas/kun/pkg/store/reserve"
	"icode.baidu.com/baidu/faas/kun/pkg/util/convert"
)

func TestPreHandler(t *testing.T) {
	mockCtrl := gomock.NewController(t)
	defer mockCtrl.Finish()
	manager := newMockReservedManager(t)
	r := &dao.FunctionReserved {
		Uid: "uid",
		Uuid: "uuid",
	}

	res := &dao.FunctionReserved{
		Uid:"uid",
		Uuid: "uuid",
		CommitID: convert.String("test"),
		FunctionBrn:"testfunc",
		FunctionVersion:"1",
		FunctionName:"testfc",
		MemorySize:128,
		ReservedTs:time.Now().Unix(),
		ReservedCount:1,
		Status:2,
	}
	ac := mock.NewMockApiserverInterface(mockCtrl)
	ac.EXPECT().Get("uuid").Return(res, nil)
	ac.EXPECT().Update(res).Return(nil)
	manager.ApiserverClient = ac
	err := manager.preHandler(r, "reserved")
	assert.Nil(t, err)
}

func TestPostHandler(t *testing.T) {
	mockCtrl := gomock.NewController(t)
	defer mockCtrl.Finish()
	manager := newMockReservedManager(t)
	r := &dao.FunctionReserved{
		Uid: "uid",
		Uuid: "uuid",
		RealCount: 1,
		ReservedCount:0,
	}
	ac := mock.NewMockApiserverInterface(mockCtrl)
	manager.ApiserverClient = ac
	ac.EXPECT().Update(r).Return(nil)
	err := manager.postHandler(r, 1, "unReserved")
	assert.Nil(t, err)
}

func TestDryRun(t *testing.T) {
	mockCtrl := gomock.NewController(t)
	defer mockCtrl.Finish()
	manager := newMockReservedManager(t)
	r := &dao.FunctionReserved{
		Uid: "uid",
		Uuid: "uuid",
		RealCount: 0,
		ReservedCount:1,
	}

	res := &rest.Result{}
	res.StatusCode(convert.Int(200))

	ec := mock.NewMockEventhubInterface(mockCtrl)
	manager.EventhubClient = ec
	ec.EXPECT().DryRun(r).Return(res, nil)
	_, err := manager.dryRun(r)
	assert.Nil(t, err)
}

func TestReserved(t *testing.T) {
	mockCtrl := gomock.NewController(t)
	defer mockCtrl.Finish()
	manager := newMockReservedManager(t)

	r := &dao.FunctionReserved {
		Uid: "uid",
		Uuid: "uuid",
		RealCount: 0,
		ReservedCount:1,
	}

	res := &dao.FunctionReserved{
		Uid:"uid",
		Uuid: "uuid",
		CommitID: convert.String("test"),
		FunctionBrn:"testfunc",
		FunctionVersion:"1",
		FunctionName:"testfc",
		MemorySize:128,
		ReservedTs:time.Now().Unix(),
		ReservedCount:1,
		Status:2,
	}
	ac := mock.NewMockApiserverInterface(mockCtrl)
	ac.EXPECT().Get("uuid").Return(res, nil)
	ac.EXPECT().Update(res).Return(nil)
	manager.ApiserverClient = ac

	res1 := &rest.Result{}
	res1.StatusCode(convert.Int(200))

	ec := mock.NewMockEventhubInterface(mockCtrl)
	manager.EventhubClient = ec
	ec.EXPECT().DryRun(r).Return(res1, nil)

	ac.EXPECT().Update(r).Return(nil)

	err := manager.reserved(r)
	assert.Nil(t, err)
}

func TestUnReserved(t *testing.T) {
	mockCtrl := gomock.NewController(t)
	defer mockCtrl.Finish()
	manager := newMockReservedManager(t)

	r := &dao.FunctionReserved {
		Uid: "uid",
		Uuid: "uuid",
		RealCount: 1,
		ReservedCount:0,
		FunctionBrn:"brn",
		CommitID: convert.String("commitid"),
		MemorySize:128,
	}

	res := &dao.FunctionReserved{
		Uid:"uid",
		Uuid: "uuid",
		CommitID: convert.String("test"),
		FunctionBrn:"brn",
		FunctionVersion:"1",
		FunctionName:"testfc",
		MemorySize:128,
		ReservedTs:time.Now().Unix(),
		ReservedCount:0,
		RealCount:1,
		Status:2,
	}
	ac := mock.NewMockApiserverInterface(mockCtrl)
	ac.EXPECT().Get("uuid").Return(res, nil)
	ac.EXPECT().Update(res).Return(nil)
	manager.ApiserverClient = ac

	config := &api.FunctionConfiguration{
		Uid: "uid",
	}
	config.FunctionArn = convert.String("brn")

	f := &api.GetFunctionOutput{
		Configuration: config,
	}

	ac.EXPECT().GetFunction("uid", "brn").Return(f, nil)
	ac.EXPECT().GetFunction("uid", "brn").Return(f, nil)
	labels, _ := manager.getLabels("uid", "brn")

	pods := testNewPods()
	podArgs := &reserve.GetReservedPodMembersAgrs{
		ServiceType: api.ServiceTypeCFC,
		Labels:      labels,
		MemorySize:  int64(128),
		CommitID:    "commitid",
		Reserved:    true,
	}

	reservedMgr := reserveMock.NewMockReserveManager(mockCtrl)
	manager.ReserveManager = reservedMgr
	reservedMgr.EXPECT().GetReservedPodMembers(podArgs).Return(pods, nil)

	pod := &api.PodInfo{
		NodeID:         "node1",
		PodName:        "pod1",
		IP:             "127.0.0.1",
		ServiceType:    api.ServiceTypeCFC,
		Labels:         labels,
		CommitID:       "commitid",
		MemorySize:     int64(128),
		SchedulingType: api.SchedulingTypeCentral,
		Reserved:       true,
	}

	reservedMgr.EXPECT().RemWarmPod(pod).Return(int64(1), nil)

	ac.EXPECT().Update(r).Return(nil)
	err := manager.unReserved(r)
	assert.Nil(t, err)
}
