package function_reserved

import (
	"sync"

	"icode.baidu.com/baidu/faas/kun/pkg/api"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/dao"
)

func (rm *ReservedManager) recycleTask(reserveds []*dao.FunctionReserved) (err error) {
	var wg sync.WaitGroup
	var sem = make(chan struct{}, ReservedConfig.ReservedStep)
	for _, r := range reserveds {
		wg.Add(1)
		go func(fr *dao.FunctionReserved) {
			sem <- struct{}{}
			defer func() {
				<-sem
				wg.Done()

			}()
			/*
			// node回收
			1、查看redis上预留node列表
			2、查看预留node上是否有预留pod，若无，则将该node从预留node集合中删除，等待cron的正常回收流程；
			   否则，跳过此次回收
			 */
			nodes, err := rm.GetReservedNodesFromRedis(r)
			if err != nil {
				rm.logger.V(6).Errorf("[function_reserved][recycle] nodes err: %v", err)
				return
			}

			for _, n := range nodes {
				if &n == nil {
					continue
				}
				// 调用funclet获取node上健康pod列表
				healthyContainers, err := rm.PodControl.GetHealthyContainersOnNode(&n)
				if err != nil {
					rm.logger.V(6).Errorf("[function_reserved][recycle] get healthy containers fail, floatingIP: %s ,err: %v", n.FloatingIP, err)
					continue
				}
				if rm.checkHealthyContainers(r, &n, healthyContainers) {
					rm.logger.V(6).Infof("[function_reserved][recycle] node has reserved pod, skip the recycle task")
					continue
				}

				// 到此, node上已没有预留pod，可以将该node从预留node集合中删除啦
				n.Reserved = true  // 补充reserved标识
				rm.logger.V(6).Infof("[function_reserved][recycle] start rm node: %v, Reserved: %v", &n, n.Reserved)
				res, err := rm.ReserveManager.RemWarmNode(&n)
				if err != nil || res != 1 {
					if err != nil {
						rm.logger.V(6).Errorf("[function_reserved][recycle] rem reserved node fail, floatingIP: %s, err: %v", n.FloatingIP, err)
					}
					continue
				}
				// 到此, redis上node被删除，可以更新etcd上node reserved标识
				n.Reserved = false
				err = rm.EtcdClient.SetNode(&n)
				if err != nil {
					rm.logger.V(6).Errorf("[function_reserved] set node to etcd fail, err: %v", err)
				}
			}

		}(r)
	}
	wg.Wait()
	return
}

// 查看node上healthy containers是否在reserved pod set中
func (rm *ReservedManager) checkHealthyContainers(r *dao.FunctionReserved, node *api.NodeInfo, healthyContainers []*api.ContainerInfo) (isExist bool) {
	for _, c := range healthyContainers {
		if c.RuntimeInfo == nil {
			// RuntimeInfo为nil，则说明该pod没被占用，不用检查是否在reserved pod set中
			continue
		}
		pod := &api.PodInfo{
			NodeID: node.ID,
			PodName: c.Hostname,
			IP: node.FloatingIP,
			ServiceType:node.ServiceType,
			SchedulingType: api.SchedulingTypeCentral,
			NodeOccupiedVersion:node.OccupiedVersion,
			Reserved: true, // 查预留实例集合中是否存在
		}

		var labels api.RequestLabels
		if c.RuntimeInfo != nil {
			labels = c.Labels
			pod.MemorySize = int64(r.MemorySize) // memorySize重新复制
			pod.CommitID = c.CommitID
		}
		// labels为空时，补充
		if labels == nil {
			labels, _ = rm.getLabels(r.Uid, r.FunctionBrn)
		}
		pod.Labels = labels

		// 有一个存在则返回true
		isExist = rm.ReserveManager.IsReservedPodExist(pod)
		if isExist {
			rm.logger.V(6).Infof("[function_reserved][recycle] redis healthy podName: %v, isEsist: %v", pod.PodName, isExist)
			return
		}
	}
	return
}
