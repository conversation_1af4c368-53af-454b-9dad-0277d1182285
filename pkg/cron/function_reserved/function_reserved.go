package function_reserved

import (
	"fmt"
	"math"
	"sync"
	"sync/atomic"
	"time"

	"icode.baidu.com/baidu/faas/kun/pkg/api"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/dao"
)

func (rm *ReservedManager) preHandler(r *dao.FunctionReserved, method string) (err error) {
	// 再查一次数据库
	fr, err := rm.ApiserverClient.Get(r.Uuid)
	if err != nil {
		rm.logger.V(6).Errorf("[function_reserved] get function reserved fail, err: %v", err)
		return err
	}
	// 查看数据库状态, 并判断状态是否过期
	timeout := time.Now().Unix() - fr.ReservedTs
	if fr.Status == api.ReservedCreating && timeout < ReservedConfig.TaskExpiration {
		rm.logger.V(6).Infof("[function_reserved] function reserved is creating, please ship this task")
		return fmt.Errorf("function reserved is creating")
	}

	if (method == api.ReservedMethod && fr.ReservedCount <= fr.RealCount) || (method == api.UnReservedMethod && fr.ReservedCount >= fr.RealCount) {
		rm.logger.V(6).Infof("[function_reserved] function reserved count is equal to real count, skip")
		return fmt.Errorf("no need to reserved")
	}

	rm.logger.V(6).Infof("[function_reserved] preHandler start update function reserved")
	// 更新预留实例状态，status标识为创建中, 预留时间设为当前时间
	fr.Status = api.ReservedCreating
	fr.ReservedTs = time.Now().Unix()
	err = rm.ApiserverClient.Update(fr)
	if err != nil {
		rm.logger.V(6).Errorf("[function_reserved] update function reserved fail, err: %v", err)
		return err
	}
	rm.logger.V(6).Infof("[function_reserved] preHandler update function reserved success")
	return nil
}

func (rm *ReservedManager) postHandler(r *dao.FunctionReserved, count int, method string) (err error) {
	r.ReservedTs = time.Now().Unix()
	r.Status = api.ReservedCreated
	if count <= 0 {
		// 这里，可能出错，直接退出，status可能是创建中，需等待过期时间后，下次任务继续更新
		rm.logger.V(6).Infof("[function_reserved] count is zero")
		return
	}

	if method == api.ReservedMethod {
		r.RealCount += count
	} else if method == api.UnReservedMethod {
		r.RealCount -= count
		// realcount修正
		if r.RealCount < 0 {
			r.RealCount = 0
		}
		if r.RealCount == 0 && r.ReservedCount == 0 {
			// 此时需要将function_reserved改为deleted
			r.Status = api.ReservedDeleted
		}
	}

	// 不论dryRun失败或成功，都将status改成2,供下次任务继续
	err = rm.ApiserverClient.Update(r)
	if err != nil {
		rm.logger.V(6).Errorf("[function_reserved] update function reserved fail, functionBrn :%s, err: %v", r.FunctionBrn, err)
		return err
	}
	rm.logger.V(6).Infof("[function_reserved] update function reserved success, functionBrn :%s, realCount: %v", r.FunctionBrn, r.RealCount)
	return
}

func (rm *ReservedManager) dryRun(r *dao.FunctionReserved) (reserved int32, err error) {
	var wg sync.WaitGroup
	var sem = make(chan struct{}, ReservedConfig.ReservedStep)
	if r.RealCount >= r.ReservedCount {
		rm.logger.V(6).Infof("[function_reserved] no need to dryrun")
		return
	}
	needReserved := int(math.Abs(float64(r.ReservedCount) - float64(r.RealCount)))
	// 每次按一定步长批量执行dryrun
	for i := 0; i < needReserved; i++ {
		rm.logger.V(6).Debugf("[function_reserved] function reserved dryrun, expectCount: %d, realCount:%d", r.ReservedCount, r.RealCount)
		wg.Add(1)
		go func() {
			sem <- struct{}{}
			defer func() {
				<-sem
				wg.Done()

			}()
			rm.logger.V(6).Infof("[function_reserved] function reserved dryrun start")
			_, err = rm.EventhubClient.DryRun(r)
			if err != nil {
				rm.logger.V(6).Errorf("[function_reserved] function reserved dryrun fail, functionBrn: %s, err: %v", r.FunctionBrn, err)
				return
			}
			atomic.AddInt32(&reserved, 1)
			rm.logger.V(6).Infof("[function_reserved] function reserved dryrun success, functionBrn: %s, reservedCount: %v", r.FunctionBrn, reserved)
		}()

	}
	wg.Wait()
	return
}

// 预留
func (rm *ReservedManager) reserved(r *dao.FunctionReserved) error {
	var err error
	var reserved int32 // 预留成功个数

	if err = rm.preHandler(r, api.ReservedMethod); err != nil {
		return err
	}

	// 开始dryRun
	rm.logger.V(6).Infof("[function_reserved] start dryrun, function reserved :%v", r)

	reserved, err = rm.dryRun(r)

	err = rm.postHandler(r, int(reserved), api.ReservedMethod)

	return err
}

// 删除预留实例
func (rm *ReservedManager) unReserved(r *dao.FunctionReserved) (err error) {
	var unReserved int32 // 成功删除预留实例个数
	if err = rm.preHandler(r, api.UnReservedMethod); err != nil {
		return err
	}

	// 开始unReserved
	rm.logger.V(6).Infof("[function_reserved] start unReserved, function reserved :%v", r)

	var wg sync.WaitGroup
	var sem = make(chan struct{}, ReservedConfig.ReservedStep)
	needUnReserved := int(math.Abs(float64(r.RealCount) - float64(r.ReservedCount)))
	for i := 0; i < needUnReserved; i++ {
		rm.logger.V(6).Infof("[function_reserved] rem function reserved, expectCount: %d, realCount:%d", r.ReservedCount, r.RealCount)
		wg.Add(1)
		go func() {
			sem <- struct{}{}
			defer func() {
				<-sem
				wg.Done()

			}()
			rm.logger.V(6).Infof("[function_reserved] rem function reserved start")
			err = rm.RemReservedPod(r)
			if err != nil {
				rm.logger.V(6).Errorf("[function_reserved] rem function reserved fail, functionBrn: %s, err: %v", r.FunctionBrn, err)
				return
			}
			atomic.AddInt32(&unReserved, 1)
			rm.logger.V(6).Infof("[function_reserved] rem function reserved success, functionBrn: %s, unReserved: %v", r.FunctionBrn, unReserved)
		}()
	}
	wg.Wait()

	err = rm.postHandler(r, int(unReserved), api.UnReservedMethod)
	return err
}
