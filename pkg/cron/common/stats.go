package common

import (
	"strconv"
	"sync"

	"go.uber.org/zap/zapcore"

	"go.uber.org/zap"

	"icode.baidu.com/baidu/faas/kun/pkg/util/logs"
)

// Stats 是统计数据结构
type Stats struct {
	NodeCount         uint64
	ScheduleNodeCount uint64
	UsedNodeCount     uint64
	TotalPodCount     uint64 // schedule层node的总pod数量
	PodCount          uint64 // busy状态的node中的pod数量
	UsedPodCount      uint64 // busy状态的node中使用的pod数量
	UnhealthyPodCount uint64
	MemorySize        uint64
	UsedMemorySize    uint64
	PodStatsMap       PodStatsMap  // 不同内存型号对应的pod统计，包括所有已初始化的node
	NodeStatsMap      NodeStatsMap // 记录每个流转node的pod使用量，用于pod扩缩容计算

	lock sync.Mutex
}

// AtomicAdd 与另一个统计对象原子性相加
func (s *Stats) AtomicAdd(stat *Stats) {
	s.lock.Lock()
	defer s.lock.Unlock()

	s.Add(stat)
}

// Add 与另一个统计对象相加
func (s *Stats) Add(stat *Stats) {
	s.NodeCount += stat.NodeCount
	s.ScheduleNodeCount += stat.ScheduleNodeCount
	s.UsedNodeCount += stat.UsedNodeCount
	s.TotalPodCount += stat.TotalPodCount
	s.PodCount += stat.PodCount
	s.UsedPodCount += stat.UsedPodCount
	s.UnhealthyPodCount += stat.UnhealthyPodCount
	s.MemorySize += stat.MemorySize
	s.UsedMemorySize += stat.UsedMemorySize

	for k, v := range stat.PodStatsMap {
		ps, ok := s.PodStatsMap[k]
		if !ok {
			ps = &PodStats{}
			s.PodStatsMap[k] = ps
		}
		ps.PodCount += v.PodCount
		ps.UsedMemorySize += v.UsedMemorySize
	}

	// 不可能重复，直接写入就行
	for k, v := range stat.NodeStatsMap {
		s.NodeStatsMap[k] = v
	}
}

// Log 将统计输出到日志
func (s *Stats) Log(l *logs.Logger) {
	s.lock.Lock()
	defer s.lock.Unlock()

	l.With(zap.Uint64("total_node_count", s.NodeCount),
		zap.Uint64("used_node_count", s.UsedNodeCount),
		zap.Uint64("schedule_node_count", s.ScheduleNodeCount),
		zap.Uint64("total_pod_count", s.TotalPodCount),
		zap.Uint64("pod_count", s.PodCount),
		zap.Uint64("used_pod_count", s.UsedPodCount),
		zap.Uint64("unhealthy_pod_count", s.UnhealthyPodCount),
		zap.Uint64("memory_size", s.MemorySize),
		zap.Uint64("used_memory_size", s.UsedMemorySize),
		zap.Object("pod_stat", s.PodStatsMap)).
		Info("count statistics")

	var nodeActivityRatio, nodeUseRatio, podUseRatio, podUnhealthRatio, memoryUseRatio float64
	if s.NodeCount != 0 {
		nodeActivityRatio = int2percent(s.ScheduleNodeCount, s.NodeCount)
		nodeUseRatio = int2percent(s.UsedNodeCount, s.NodeCount)
	}

	if s.PodCount != 0 {
		podUseRatio = int2percent(s.UsedPodCount, s.PodCount)
		podUnhealthRatio = int2percent(s.UnhealthyPodCount, s.PodCount)
	}

	if s.MemorySize != 0 {
		memoryUseRatio = int2percent(s.UsedMemorySize, s.MemorySize)
	}

	l.With(zap.Float64("node_activity_ratio", nodeActivityRatio),
		zap.Float64("node_use_ratio", nodeUseRatio),
		zap.Float64("pod_use_ratio", podUseRatio),
		zap.Float64("pod_unhealth_ratio", podUnhealthRatio),
		zap.Float64("memory_use_ratio", memoryUseRatio)).
		Info("ratio statistics")
}

func NewStats() *Stats {
	return &Stats{
		PodStatsMap:  make(PodStatsMap),
		NodeStatsMap: make(NodeStatsMap),
	}
}

type PodStats struct {
	PodCount       uint64 // 当前主要用来计算pod扩缩容，所以数据按NodeInfo.PodCount来算
	UsedMemorySize uint64
}

func (ps *PodStats) MarshalLogObject(enc zapcore.ObjectEncoder) error {
	enc.AddUint64("pod_count", ps.PodCount)
	enc.AddUint64("used_memory_size", ps.UsedMemorySize)
	return nil
}

type PodStatsMap map[int64]*PodStats

func (m PodStatsMap) MarshalLogObject(enc zapcore.ObjectEncoder) error {
	if m == nil || len(m) == 0 {
		return nil
	}

	for k, v := range m {
		enc.AddObject(strconv.FormatInt(k, 10), v)
	}
	return nil
}

// NodeStats 表示一个node的统计数据
type NodeStats struct {
	HealthyPodCount uint64
	UsedPodCount    uint64
	UsedMemorySize  uint64
}

type NodeStatsMap map[string]*NodeStats // key是node id

// int2percent 返回两个整数相除的百分比
func int2percent(x, y uint64) float64 {
	return float64(x) / float64(y) * 100
}
