package common

import "time"

type CacheType string

const (
	CacheTypeColdNode = "cold_node"
	CacheTypeWarmNode = "warm_node"
)

func CacheKey(ct CacheType, key string) string {
	return string(ct) + ":" + key
}

func (t *Common) GetCache(ct CacheType, key string) (interface{}, bool) {
	return t.Cache.Get(CacheKey(ct, key))
}

func (t *Common) GetCacheWithExpiration(ct CacheType, key string) (interface{}, time.Time, bool) {
	return t.Cache.GetWithExpiration(Cache<PERSON>ey(ct, key))
}

func (t *Common) SetCache(ct CacheType, key string, value interface{}, d time.Duration) {
	t.Cache.Set(CacheKey(ct, key), value, d)
}

func (t *Common) AddCache(ct CacheType, key string, value interface{}, d time.Duration) error {
	return t.Cache.Add(CacheKey(ct, key), value, d)
}

func (t *Common) SetCacheDefault(ct CacheType, key string, value interface{}) {
	t.Cache.SetDefault(CacheKey(ct, key), value)
}

func (t *Common) DeleteCache(ct CacheType, key string) {
	t.Cache.Delete(CacheKey(ct, key))
}
