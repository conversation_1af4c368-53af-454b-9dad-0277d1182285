package common

import (
	"icode.baidu.com/baidu/faas/kun/cmd/cron/app/options"
	"icode.baidu.com/baidu/faas/kun/pkg/bce/iam"
	"icode.baidu.com/baidu/faas/kun/pkg/cron/impl"
	"icode.baidu.com/baidu/faas/kun/pkg/etcd"
	funclet "icode.baidu.com/baidu/faas/kun/pkg/funclet/client"
	"icode.baidu.com/baidu/faas/kun/pkg/nodefsm"
	proxy "icode.baidu.com/baidu/faas/kun/pkg/proxyagent/client"
	"icode.baidu.com/baidu/faas/kun/pkg/rpc/redis"
	nodeinfo "icode.baidu.com/baidu/faas/kun/pkg/store/node-info"
	"icode.baidu.com/baidu/faas/kun/pkg/store/reserve"
	"icode.baidu.com/baidu/faas/kun/pkg/util/baiduhi"
	"icode.baidu.com/baidu/faas/kun/pkg/util/cache"
	"icode.baidu.com/baidu/faas/kun/pkg/util/logs"
)

const (
	NodeIDKey    = "nodeID"
	ClusterIDKey = "cluster_id"
)

// TaskControl 是任务的数据/控制接口
type TaskControl struct {
	// TODO: 需要将下面的通用控制接口与上面层次控制接口分离
	NodeStore       impl.NodeStore
	ClusterStore    impl.ClusterInfoStore
	ReserveManager  reserve.ReserveManager
	NodeInfoManager nodeinfo.Interface
	NodeFSM         nodefsm.FSMInterface
}

// TaskClient 包含任务所需的全部客户端
type TaskClient struct {
	RedisClusterClient *redis.Client
	FuncletClient      funclet.FuncletInterface
	EtcdClient         etcd.EtcdInterface
	IamClient          iam.ClientInterface
	HiClient           baiduhi.BaiduHiControl
	ProxyAgentClient   proxy.ProxyAgentInterface
}

// TaskConfig 是解析后的任务配置，避免每次循环都再次解析
type TaskConfig struct {
}

// Common 是任务通用类，将各种控制接口打包成一个结构
type Common struct {
	*TaskControl // TODO: 需要转移到子任务中
	*TaskClient
	TaskConfig
	RunOptions *options.CronOptions
	Cache      *cache.Cache
	Logger     logs.Logger // 非指针，保证变量地址永久不变
}

type ScaleUpDownHiMessage struct {
	Title          string //通告开头
	StartTime      string // 扩缩容开始时间
	EndTime        string // 扩容荣结束时间
	Region         string // 扩缩容区域
	Cluster        string // 扩缩容集群ID
	ResourcePool   string //集群资源池
	DesiredNumbers int    // 期望扩缩容node数量
	Result         string // 扩缩容结果
	Message        string // 其他信息（错误信息）
}
