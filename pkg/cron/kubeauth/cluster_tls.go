package kubeauth

import (
	"bufio"
	"crypto/tls"
	"crypto/x509"
	"encoding/base64"
	"errors"
	"io"
	"net/http"
	"os"
	"strings"

	"icode.baidu.com/baidu/faas/kun/pkg/util/logs"
)

// +k8s:deepcopy-gen=true
// TLSClientConfig contains settings to enable transport layer security
type TLSClientConfig struct {
	// Server should be accessed without verifying the TLS certificate. For testing only.
	Insecure bool
	// ServerName is passed to the server for SNI and is used in the client to check server
	// ceritificates against. If ServerName is empty, the hostname used to contact the
	// server is used.
	ServerName string

	// Server requires TLS client certificate authentication
	CertFile string
	// Server requires TLS client certificate authentication
	KeyFile string
	// Trusted root certificates for server
	CAFile string

	// CertData holds PEM-encoded bytes (typically read from a client certificate file).
	// CertData takes precedence over CertFile
	CertData []byte
	// KeyData holds PEM-encoded bytes (typically read from a client certificate key file).
	// KeyData takes precedence over KeyFile
	KeyData []byte
	// CAData holds PEM-encoded bytes (typically read from a root certificates bundle).
	// CAData takes precedence over CAFile
	CAData []byte
}

func NewTLSClient() *TLSClientConfig {
	return &TLSClientConfig{}
}

func (t *TLSClientConfig) ConfigRootCAs(config *tls.Config) error {
	var CACertPool *x509.CertPool

	CACertPool = x509.NewCertPool()
	if ok := CACertPool.AppendCertsFromPEM(t.CAData); !ok {
		return errors.New("failed to parse root certificate")
	}
	config.RootCAs = CACertPool
	return nil
}

func (t *TLSClientConfig) ConfigCertAndKey(config *tls.Config) error {
	var cert tls.Certificate
	var err error
	cert, err = tls.X509KeyPair(t.CertData, t.KeyData)
	if err != nil {
		return errors.New("X509KeyPair failed, err: " + err.Error())
	}
	config.Certificates = []tls.Certificate{cert}

	return nil
}

func (t *TLSClientConfig) loadconfig(configContent string) error {
	configMap := ReadConfig(strings.NewReader(configContent))
	var err error
	if t.CAData, err = base64.StdEncoding.DecodeString(configMap["certificate-authority-data"]); err != nil {
		return err
	}
	if t.KeyData, err = base64.StdEncoding.DecodeString(configMap["client-key-data"]); err != nil {
		return err
	}
	if t.CertData, err = base64.StdEncoding.DecodeString(configMap["client-certificate-data"]); err != nil {
		return err
	}
	return nil
}

func NewTransHttpClient(configContent string) (*http.Client, error) {
	//load from configPath
	tlsClient := NewTLSClient()

	if err := tlsClient.loadconfig(configContent); err != nil {
		return nil, err
	}
	tlsConf := new(tls.Config)
	if err := tlsClient.ConfigRootCAs(tlsConf); err != nil {
		return nil, err
	}
	if err := tlsClient.ConfigCertAndKey(tlsConf); err != nil {
		return nil, err
	}

	tr := &http.Transport{
		TLSClientConfig: tlsConf,
	}
	return &http.Client{Transport: tr}, nil
}

func ReadConfigFile(filePath string) map[string]string {
	file, err := os.Open(filePath)
	if err != nil {
	}
	defer file.Close()
	return ReadConfig(file)
}

func ReadConfig(r io.Reader) map[string]string {
	data := make(map[string]string)
	buf := bufio.NewReader(r)
	for {
		l, err := buf.ReadString('\n')
		line := strings.TrimSpace(l)
		if err != nil {
			if len(line) == 0 {
				break
			}
		}
		switch {
		case len(line) == 0:
		case string(line[0]) == "#": //过滤注释
			continue
		default:
			i := strings.IndexAny(line, ":")
			value := strings.TrimSpace(line[i+1 : len(line)])
			data[strings.TrimSpace(line[0:i])] = value
		}
	}
	logs.V(6).Infof("cadata=%v", data)
	return data
}
