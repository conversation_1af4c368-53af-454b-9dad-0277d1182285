package kubeauth

import (
	"strings"
	"testing"

	"github.com/stretchr/testify/assert"
)

const testConfig = `apiVersion: v1
clusters:
- cluster:
    certificate-authority-data: LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1J
    server: https://180.76.158.41:6443
  name: kubernetes
contexts:
- context:
    cluster: kubernetes
    user: kubernetes-admin
  name: kubernetes-admin@kubernetes
current-context: kubernetes-admin@kubernetes
kind: Config
preferences: {}
users:
- name: kubernetes-admin
  user:
    client-certificate-data: LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1J
    client-key-data: LS0tLS1CRUdJTiBSU0EgUFJJVkFURSBLRVktLS0tLQpNSUlF`

func TestReadConfig(t *testing.T) {
	m := ReadConfig(strings.NewReader(testConfig))
	assert.Equal(t, m["certificate-authority-data"], "LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1J")
}
