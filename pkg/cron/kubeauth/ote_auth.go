package kubeauth

import (
	"crypto/hmac"
	"crypto/sha256"
	"fmt"
	"time"
)

const DefaultExpire = 1800

type OTESigner struct {
	UserID string
	Token  string
	Expire int
}

func (s *OTESigner) GetOTESign() string {
	now := time.Now().UTC()
	year, mon, day := now.Date()
	hour, min, sec := now.Clock()
	stringPrefix := fmt.Sprintf("%s/%s/%d/", s.UserID,
		fmt.Sprintf("%04d-%02d-%02dT%02d:%02d:%02dZ", year, mon, day, hour, min, sec), s.Expire)
	mac := hmac.New(sha256.New, []byte(s.Token))
	mac.Write([]byte(stringPrefix))
	signature := fmt.Sprintf("ote-auth-v1/%s%x", stringPrefix, mac.Sum(nil))
	return signature
}
