package client

import (
	"fmt"
	"net/url"
	"strconv"

	"github.com/spf13/pflag"

	"icode.baidu.com/baidu/faas/kun/pkg/api"
	"icode.baidu.com/baidu/faas/kun/pkg/rest"
)

//go:generate mockgen -destination=../../test/cron/mock/client.go -package=mock icode.baidu.com/baidu/faas/kun/pkg/cron/client CronClientInterface
type CronClientInterface interface {
	OfflineCluster(clusterID string) error
	OfflineNode(nodeID string) error
	UpgradeCluster(clusterID string, interval int) error
	GetNode(nodeID string, count int) ([]*api.NodeInfo, error)
	UpgradeNode(nodeID string) error
	StartTask() error
	ReportNode(request *api.NodeInfo) error
	FreezeNode(nodeID string) error
	ThawNode(nodeID string) error
	UpdateClusterKataRuntimeList(clusterID string, runtimeImages string) error
	GrayRelaeseKataRuntimeImage(clusterID string, nodeID string, imageID string) error
	FullRelaeseKataRuntimeImage(clusterID string, imageID string) error
}

var _ CronClientInterface = &Client{}

type Client struct {
	client   *rest.RESTClient
	protocol string
	host     string
	port     int
}

// NewCronClient create a pod client
func NewCronClient(options *CronOptions) *Client {
	version := "v1"
	config := rest.ContentConfig{
		BackendType: rest.BackendTypeInternal,
	}
	restClient, _ := rest.NewRESTClient(&url.URL{}, version, config, nil)
	return &Client{
		client:   restClient,
		protocol: options.Protocol,
		host:     options.Host,
		port:     options.Port,
	}
}

const (
	ProtocolTCP  = "tcp"
	ProtocolHTTP = "http"
)

type CronOptions struct {
	Protocol string
	Host     string
	Port     int
}

func NewCronOptions() *CronOptions {
	return &CronOptions{
		Protocol: ProtocolHTTP,
		Host:     "127.0.0.1",
		Port:     80,
	}
}

func (o *CronOptions) AddFlags(fs *pflag.FlagSet) {
	fs.StringVar(&o.Protocol, "cron-protocol", o.Protocol, ""+
		"The network protocol to access cron")

	fs.StringVar(&o.Host, "cron-host", o.Host, ""+
		"The host to access cron")

	fs.IntVar(&o.Port, "cron-port", o.Port, ""+
		"The port to access cron")
}

func (p *Client) baseURL() *url.URL {
	baseURL, _ := url.Parse(fmt.Sprintf("%s://%s:%d", p.protocol, p.host, p.port))
	return baseURL
}

// OfflineCluster 对一个集群执行下线操作
func (p *Client) OfflineCluster(clusterID string) error {
	req := p.client.Put().
		BaseURL(p.baseURL()).
		Resource(fmt.Sprintf("clusters/%s/offline", clusterID))

	return req.Do().Error()
}

// OfflineNode 下线node
func (p *Client) OfflineNode(nodeID string) error {
	req := p.client.Put().
		BaseURL(p.baseURL()).
		Resource(fmt.Sprintf("nodes/%s/offline", nodeID))

	return req.Do().Error()
}

// UpgradeCluster 重置集群的全部node
func (p *Client) UpgradeCluster(clusterID string, interval int) error {
	req := p.client.Put().
		BaseURL(p.baseURL()).
		Body(fmt.Sprintf("interval=%d", interval)).
		Resource(fmt.Sprintf("cluster/%s/upgrade", clusterID))

	return req.Do().Error()
}

// GetNode 获取node最近的count次版本
func (p *Client) GetNode(nodeID string, count int) ([]*api.NodeInfo, error) {
	req := p.client.Get().
		BaseURL(p.baseURL()).
		Param("count", strconv.Itoa(count)).
		Body(fmt.Sprintf("nodes/%s", nodeID)).
		Resource(fmt.Sprintf("nodes/%s", nodeID))

	result := req.Do()
	if err := result.Error(); err != nil {
		return nil, err
	}

	var res api.GetNodeResponse
	err := result.Into(&res)
	return res.Node, err
}

// UpgradeNode 重置node，即送入k8s层
func (p *Client) UpgradeNode(nodeID string) error {
	req := p.client.Put().
		BaseURL(p.baseURL()).
		Resource(fmt.Sprintf("nodes/%s/upgrade", nodeID))

	return req.Do().Error()
}

// StartTask 立即开始一次任务循环
func (p *Client) StartTask() error {
	req := p.client.Post().
		BaseURL(p.baseURL()).
		Resource("starttask")

	return req.Do().Error()
}

// FreezeNode 冻结node
func (p *Client) FreezeNode(nodeID string) error {
	req := p.client.Put().
		BaseURL(p.baseURL()).
		Resource(fmt.Sprintf("nodes/%s/freeze", nodeID))

	return req.Do().Error()
}

// ThawNode 解冻node
func (p *Client) ThawNode(nodeID string) error {
	req := p.client.Put().
		BaseURL(p.baseURL()).
		Resource(fmt.Sprintf("nodes/%s/thaw", nodeID))

	return req.Do().Error()
}

// ReportNode 上报NodeInfo信息
func (p *Client) ReportNode(node *api.NodeInfo) error {
	req := p.client.Post().
		BaseURL(p.baseURL()).
		Resource("nodes/report").
		Body(node)

	return req.Do().Error()
}

//更新集群kata rumtime list
func (p *Client) UpdateClusterKataRuntimeList(clusterID string, runtimeImages string) error {
	req := p.client.Put().
		BaseURL(p.baseURL()).
		Body(fmt.Sprintf("kataRuntimeImages=%s", runtimeImages)).
		Resource(fmt.Sprintf("cluster/%s/upgrade", clusterID))

	return req.Do().Error()
}

//灰度kata image
func (p *Client) GrayRelaeseKataRuntimeImage(clusterID string, nodeID string, imageID string) error {
	req := p.client.Put().
		BaseURL(p.baseURL()).
		Body(fmt.Sprintf("nodeID=%s,kataRuntimeImageID=%s", nodeID, imageID)).
		Resource(fmt.Sprintf("cluster/%s/KataRuntimeImageID/gray", clusterID))

	return req.Do().Error()
}

//全量kata image
func (p *Client) FullRelaeseKataRuntimeImage(clusterID string, imageID string) error {
	req := p.client.Put().
		BaseURL(p.baseURL()).
		Body(fmt.Sprintf("kataRuntimeImageID=%s", imageID)).
		Resource(fmt.Sprintf("cluster/%s/KataRuntimeImageID/full", clusterID))

	return req.Do().Error()
}
