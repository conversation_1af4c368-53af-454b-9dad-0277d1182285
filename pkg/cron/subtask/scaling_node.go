package subtask

import (
	"fmt"
	"math"
	"sort"
	"time"

	"icode.baidu.com/baidu/faas/kun/pkg/api"
	"icode.baidu.com/baidu/faas/kun/pkg/cron/common"
	"icode.baidu.com/baidu/faas/kun/pkg/nodefsm/schedule-events"
	"icode.baidu.com/baidu/faas/kun/pkg/store/reserve"
	"icode.baidu.com/baidu/faas/kun/pkg/util/baiduhi"
)

var (
	needScalingDownCnt = 0 //缩容计数周期，N个周期缩容一次
)

// scalingNodeStat 是对一种内存规格的扩缩容前的node资源统计
type scalingNodeStat struct {
	occupied          int // 被占用，也就是busy状态
	unoccupied        int // 未被占用
	initializing      int // 初始化中的
	initialized       int // 初始化完成
	fakeColdNodeCnt   int // 未填回redis的node
	actualColdNodeCnt int // 真正可用的 cold node
}

// 记录nodeId和node的单价
type nodeCharge struct {
	nodeId string
	charge float64
}

func (st *Subtask) autoScalingNode(resourcePool string) {
	//st.Logger.V(5).Infof("compute desired nodes for clusters %v", st.k8sInfo.CceClusterUUID)
	resourceLogger := st.Logger.WithField("resourcePool", resourcePool)
	resourceLogger.Infof("[scalingNodePool] start compute scalingNodeCount")
	cnt, err := st.computeScalingNodeCount(resourcePool)
	if err != nil {
		resourceLogger.Errorf("compute desired node count failed: %s", err.Error())
		return
	}
	resourceLogger.Infof("[scalingNodePool] compute scalingNodeCount finished")
	switch {
	case cnt > 0:
		resourceLogger.Infof("[autoScalingNode] try to scaling up %d nodes", cnt)
		st.autoScalingUpNode(resourcePool, cnt)
		resourceLogger.Infof("[autoScalingNode] try to scaling up nodes finished")
	case cnt < 0:
		if (resourcePool == api.DefaultResourcePool && !st.k8sInfo.ScalingOptions.ScalingDown) ||
			!st.k8sInfo.ResourcePoolConfig.ExtraResourcePools[resourcePool].ScalingOptions.ScalingDown {
			// 跳过缩容
			resourceLogger.Infof("[autoScalingNode] skip scaling down %d nodes", -cnt)
		} else {
			res := st.autoScalingDownNode(resourcePool, -cnt)
			resourceLogger.Infof("[autoScalingNode] finished offline %d nodes, expected %d", res, -cnt)
		}
	default:
		resourceLogger.Infof("[autoScalingNode] no need for scaling LOL")
	}
}

// computeScalingNodeCount 计算当前已有和
func (st *Subtask) computeScalingNodeCount(resourcePool string) (totalScalingCount int, err error) {
	stat := st.computeOccupiedNode(resourcePool)
	maxNodeNumThreshold := st.k8sInfo.ScalingOptions.MaxNodeNumThreshold
	// 要求 occupiedMap 中每种规格需要在 unoccupiedMap 中有一定冗余，如果不够就调用 CCE 接口扩容
	// 扩容出来的机器在下次定时任务启动时会计入unoccupied
	thresholdMap := st.k8sInfo.ScalingOptions.ThresholdMap
	if resourcePool != api.DefaultResourcePool {
		maxNodeNumThreshold = st.k8sInfo.ResourcePoolConfig.ExtraResourcePools[resourcePool].ScalingOptions.MaxNodeNumThreshold
		thresholdMap = st.k8sInfo.ResourcePoolConfig.ExtraResourcePools[resourcePool].ScalingOptions.ThresholdMap
	}
	if thresholdMap == nil {
		st.Logger.Errorf("scaling threshold is empty")
	}
	for k, thre := range thresholdMap {
		cnt := st.computeScalingNodeCountByThreshold(stat[k], thre)
		st.Logger.V(8).Infof("[computeScalingNodeCount] memory [%d] scaling node count is [%d]", k, cnt)
		totalScalingCount += cnt
	}
	// 如果是扩容，需要扣除正在扩容中的
	if totalScalingCount > 0 && st.resourcePoolToPendNodeCount[resourcePool] > 0 {
		st.Logger.Infof("[computeScalingNodeCount] exclude pending %d nodes", st.pendingNodeCount)
		pendingUpNodeCount := int(st.resourcePoolToPendNodeCount[resourcePool])
		if totalScalingCount > pendingUpNodeCount {
			totalScalingCount -= pendingUpNodeCount
		} else {
			totalScalingCount = 0
		}
	}
	if totalScalingCount > 0 {
		//如果是扩容，计算k8s_raw和k8s_initializing状态的node比例
		overScalingLimit := st.scalingNodeLimit(resourcePool)
		if overScalingLimit {
			//k8s_raw和k8s_initializing状态的node比例过高,不再扩容。增加报警
			st.Logger.Infof("[autoScalingNode] k8s_raw and k8s_initializing node ratio is too high, resourcePool: %s",
				resourcePool)
			totalScalingCount = 0
		}
		// 如果集群node数量超过配置值，不再扩容
		if len(st.resourcePoolToNodes[resourcePool]) >= maxNodeNumThreshold {
			st.Logger.Infof("[autoScalingNode] node number is too high , current:%d MaxNodeNumThreshold:%d resourcePool:%s",
				len(st.nodeMap), st.k8sInfo.ScalingOptions.MaxNodeNumThreshold, resourcePool)
			totalScalingCount = 0
		}
	}
	st.Logger.Infof("[computeScalingNodeCount] Cluster nodes desired %d", totalScalingCount)
	return
}

// 如果k8s_raw和K8S_initializing状态的node数目到达阈值，true表示不再扩容。false表示集群Node节点个数过少未开启阈值检查和未达到阈值需要扩容
func (st *Subtask) scalingNodeLimit(resourcePool string) bool {
	//集群Node节点个数过少，不开启阈值检查
	scalingUpNodeNumThreshold := 0
	var k8sRawInitializingRatio float64
	if resourcePool == api.DefaultResourcePool {
		scalingUpNodeNumThreshold = st.k8sInfo.ScalingOptions.MaxNodeNumThreshold
		k8sRawInitializingRatio = st.k8sInfo.ScalingOptions.K8sRawInitializingRatio
	} else {
		scalingUpNodeNumThreshold = st.k8sInfo.ResourcePoolConfig.
			ExtraResourcePools[resourcePool].ScalingOptions.MaxNodeNumThreshold
		k8sRawInitializingRatio = st.k8sInfo.ResourcePoolConfig.
			ExtraResourcePools[resourcePool].ScalingOptions.K8sRawInitializingRatio
	}
	if len(st.resourcePoolToNodes[resourcePool]) < scalingUpNodeNumThreshold {
		return false
	}
	var k8sRawInitializingNodeCount int64
	for _, node := range st.resourcePoolToNodes[resourcePool] {
		if node.State == api.NodeStateK8sRaw || node.State == api.NodeStateK8sInitializing {
			k8sRawInitializingNodeCount++
		}
	}
	//计算比例
	ratio := float64(k8sRawInitializingNodeCount) / float64(len(st.nodeMap))
	if ratio >= k8sRawInitializingRatio {
		return true
	}
	return false
}

// 增加resourcePool参数，目前得该最下层调用的函数
func (st *Subtask) computeOccupiedNode(resourcePool string) (scalingNodeStatMap map[int64]*scalingNodeStat) {
	var stat scalingNodeStat
	scalingNodeStatMap = map[int64]*scalingNodeStat{podMemorySize: &stat} // TODO： 暂时只对一种内存规格生效

	for _, node := range st.resourcePoolToNodes[resourcePool] {
		if !node.Available() {
			// 不可用的node不能初始化，因此不纳入计算结果中
			st.Logger.WithField(common.NodeIDKey, node.ID).Warnf("node is available, skip compute")
			continue
		}

		switch node.State {
		case api.NodeStateScheduleBusy:
			// 因为busy的node最终会被回收重置为OriginalMemorySize的规格，所以这里依然使用原始内存计算，不使用实际内存
			stat.occupied++

		// 刚同步的node算作未占用，且被计入初始化中
		case api.NodeStateCceRaw:
			stat.initializing++
			stat.unoccupied++

		// k8s层正常状态都算作未占用的，且被计入初始化中
		case api.NodeStateK8sRaw, api.NodeStateK8sInitializing, api.NodeStateK8sInitialized:
			stat.initializing++
			stat.unoccupied++

		// 回收中和完成回收的算作未占用的; 只有NodeStateScheduleInitialized 计为完成的
		case api.NodeStateScheduleInitialized:
			stat.unoccupied++
			stat.initialized++

			// 查看 cold node是否已被加入redis
			_, err := st.ScheduleNodeControl.GetColdNodeMemory(node)
			if err != nil && err == reserve.ErrResourceNotExist {
				stat.fakeColdNodeCnt++
				st.fakeColdNodeMapMutex.Lock()
				st.fakeColdNodeMap[node.ID] = struct{}{}
				st.fakeColdNodeMapMutex.Unlock()
			}

		case api.NodeStateScheduleInactive:
			stat.unoccupied++

		// 正在向k8s层转移的算作未占用的，防止集群中node批量更新runner引起大规模扩容
		case api.NodeStateScheduleOutdated, api.NodeStateScheduleUpgrading:
			stat.unoccupied++
		}
	}

	// 计算真正可用的node数量
	stat.actualColdNodeCnt = stat.initialized - stat.fakeColdNodeCnt

	st.Logger.V(5).Infof("[computeOccupiedNode] current count report: %+v", stat)

	return
}

func (st *Subtask) computeScalingNodeCountByThreshold(stat *scalingNodeStat, threshold *api.ScalingThreshold) (scalingNodeCount int) {
	if stat == nil {
		st.Logger.Errorf("empty scaling node statistics")
		return
	}

	// 工具函数，以node占用量和阈值生成对应的node数量
	computeNodeCountFunc := func(thre float64) int {
		cntThre := float64(stat.occupied) * thre
		cntRed := float64(stat.occupied + int(threshold.UnoccupiedRedundancy))
		// return int(math.Ceil(math.Max(cntThre, cntRed)))
		return int(math.Ceil(math.Min(cntThre, cntRed)))
	}

	// 当前node总数
	// currentNodeCount := stat.occupied + stat.unoccupied

	// 当前node总数按occupied和initialized状态，即真正可用的node计算
	currentNodeCount := stat.occupied + stat.initialized

	// 所需的最小node数量，不足则需要扩容
	minNodeCount := computeNodeCountFunc(threshold.ScalingUpTrigger)
	// occupied_node数为0时，防止扩缩扩不出来，将minNodeCount初始化为ActualRedundancy
	if minNodeCount == 0 {
		minNodeCount = int(threshold.ActualRedundancy)
	}

	// 所需的最大node数量，超出则需缩容
	maxNodeCount := computeNodeCountFunc(threshold.ScalingDownTrigger)
	if maxNodeCount == 0 {
		maxNodeCount = int(threshold.ActualRedundancy)
	}

	st.Logger.Infof("currentNodeCount: %d; minNodeCount:%d; maxNodeCount: %d\n", currentNodeCount, minNodeCount, maxNodeCount)

	// 打印这个日志方便事后事后追查使用
	st.Logger.V(5).Infof("[computeScalingNodeCountByThreshold] input: stat %+v,  threshold %+v", stat, threshold)

	switch {
	case minNodeCount > currentNodeCount: // 需要扩容
		scalingNodeCount = minNodeCount - currentNodeCount
		//if stat.initializing >= scalingNodeCount {
		//	// 如果k8s层有正在初始化node，且初始化node数大于需要扩容node数，则不需要扩容，等待node初始化完成, 避免在node初始化过程中频繁扩容
		//	st.Logger.Infof("%d node is initializing, the initializing node is equal to the scaling node, so skip scaling up", stat.initializing)
		//	return
		//}
		if scalingNodeCount > st.k8sInfo.MaxScalingUpSize {
			st.Logger.Infof("expected scale up  %d nodes bigger than maxScalingUpSize", scalingNodeCount)
			// 设置一次扩容的最大扩容批次
			scalingNodeCount = st.k8sInfo.MaxScalingUpSize
		}

	case maxNodeCount < currentNodeCount:
		// 每三个周期考虑缩容一次
		needScalingDownCnt += 1
		if needScalingDownCnt < st.k8sInfo.ScalingDownCycleCnt {
			return
		}
		st.Logger.Infof("needScalingDownCnt： %d , Fit ScalingDown", needScalingDownCnt)
		needScalingDownCnt = 0

		// 如果是23:00-2:00之间，不进行缩容
		// TODO：这块后续引入智能扩缩容算法，根据之前的请求量来动态预估调整今天的请求量
		t := time.Now()
		tHour := t.Hour()
		for _, hour := range st.k8sInfo.ScalingDownWhiteHours {
			if hour == tHour {
				st.Logger.Infof("NowTimeHour is ： %d , Do not ScalingDown", tHour)
				return
			}
		}

		if stat.initializing > 0 {
			// 如果k8s层还有node，说明初始化未完成，等稳定后再缩容
			st.Logger.Infof("%d node is initializing, so skip scaling down", stat.initializing)
		} else {
			// 计算真正可用的node数量
			actualNodeCnt := stat.actualColdNodeCnt
			// 可用node不足，跳过此次缩容
			if actualNodeCnt <= int(threshold.ActualRedundancy) {
				st.Logger.Infof("actual available node num is %d , not enough, skip scaling down", actualNodeCnt)
				return
			}

			// 计算缩容后的node数量
			//finalNodeCount := computeNodeCountFunc(threshold.ScalingDownResult)

			//保证可用node然后缩容
			// scalingNodeCount = finalNodeCount - currentNodeCount + int(threshold.ActualRedundancy)

			// scalingNodeCount为负值
			scalingNodeCount = maxNodeCount - currentNodeCount
			// 如果一次缩容的node数量超过最大缩容node数量，则将缩容node数量设置为最大缩容数量
			if scalingNodeCount < -st.k8sInfo.MaxScalingDownSize {
				st.Logger.Infof("expected scaling down %d nodes bigger than maxScalingDownSize", -scalingNodeCount)
				scalingNodeCount = -st.k8sInfo.MaxScalingDownSize
			}
		}

	default:
		st.Logger.V(5).Infof("[computeScalingNodeCountByThreshold] no need to scaling node")
	}
	return
}

// autoScalingUpNode 对node进行扩容
// 按资源池对节点进行扩容
func (st *Subtask) autoScalingUpNode(resourcePool string, totalScalingUpDesired int) {

	startTime := time.Now().String()
	message := common.ScaleUpDownHiMessage{
		Title:          "扩容通告",
		StartTime:      startTime,
		Region:         st.k8sInfo.ScalingUpOptions.Region,
		Cluster:        st.k8sInfo.CceClusterUUID,
		ResourcePool:   resourcePool,
		DesiredNumbers: totalScalingUpDesired,
	}
	if err := st.ClusterControl.ScalingUpWithResourcePool(resourcePool, totalScalingUpDesired); err != nil {
		message.EndTime = time.Now().String()
		message.Result = "fail"
		message.Message = err.Error()

		st.Logger.Errorf("[autoScalingUpNode] scaling up failed: %s", err.Error())
	} else {
		message.EndTime = time.Now().String()
		message.Result = "success"
		st.Logger.Infof("[autoScalingUpNode] finished scaling up [%d] nodes", totalScalingUpDesired)
	}
	//发送Hi扩容通告
	hiMsg := FormatHiMessage(&message)
	err := st.SendHiMessage(hiMsg)
	if err != nil {
		st.Logger.Infof("[autoScalingUpNode] sendhi failed: %s", err.Error())
	}
}

// autoScalingDownNode 对node触发offline事件，只对initialized状态的node缩容
// 返回成功缩容的node个数
func (st *Subtask) autoScalingDownNode(resourcePool string, cnt int) (doneCnt int) {

	scalingDownNodeFunc := func(node *api.NodeInfo) bool {
		if err := st.NodeFSM.TriggerEvent(schedule.EventNameOffline, node, nil); err != nil {
			st.Logger.Errorf("[autoScalingDownNode] offline nodeId : %s; failed: %s", node.ID, err.Error())
			return false
		}
		return true
	}

	nodeChargeList := make([]nodeCharge, 0)
	for _, node := range st.resourcePoolToNodes[resourcePool] {
		//对空闲或者busy的非预留node做缩容操作
		if !node.Reserved && (node.State == api.NodeStateScheduleInitialized || node.State == api.NodeStateScheduleBusy) {
			nodeChargeList = append(nodeChargeList, nodeCharge{nodeId: node.ID, charge: node.Flavor.Charge})
		}
	}
	//按照node的charge降序排列
	sort.Slice(nodeChargeList, func(i, j int) bool {
		return nodeChargeList[i].charge > nodeChargeList[j].charge
	})

	// 全局缩容，优先缩容贵的
	for _, nodeCharge := range nodeChargeList {
		success := scalingDownNodeFunc(st.nodeMap[nodeCharge.nodeId])
		if !success {
			continue
		}
		st.Logger.Infof("[autoScalingDownNode] offline nodeId:%s; nodeSpec:%s; nodeCharge:%f", nodeCharge.nodeId, st.nodeMap[nodeCharge.nodeId].Flavor.Spec, nodeCharge.charge)
		cnt--
		doneCnt++
		if cnt == 0 {
			break
		}
	}
	return
}

// SendHiMessage send hi message
func (st *Subtask) SendHiMessage(message string) error {
	args := baiduhi.SendHiMsgArgs{
		ToHiGroupID: st.RunOptions.BaiduHiOptions.ToHiGroup,
		Type:        "TEXT",
		Content:     message,
	}
	err := st.HiClient.SendHiMsg(&args)
	if err != nil {
		return err
	}
	return nil
}

// FormatHiMessage format messsage
func FormatHiMessage(hiMsg *common.ScaleUpDownHiMessage) (message string) {
	return fmt.Sprintf("*****************%s****************\n开始时间：%s\n结束时间: %s\n区域：%s\n集群：%s\n资源池: %s\n期望node数量: %d\n结果: %s\n其他信息: %s\n",
		hiMsg.Title, hiMsg.StartTime, hiMsg.EndTime, hiMsg.Region, hiMsg.Cluster, hiMsg.ResourcePool, hiMsg.DesiredNumbers, hiMsg.Result, hiMsg.Message)
}
