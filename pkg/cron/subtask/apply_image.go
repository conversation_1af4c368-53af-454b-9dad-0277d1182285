package subtask

import (
	"icode.baidu.com/baidu/faas/kun/pkg/cron/resourcepool"
	"reflect"

	"icode.baidu.com/baidu/faas/kun/pkg/api"
	"icode.baidu.com/baidu/faas/kun/pkg/cron/common"
	"icode.baidu.com/baidu/faas/kun/pkg/cron/rpc"
	"icode.baidu.com/baidu/faas/kun/pkg/nodefsm/schedule-events"
)

// 应用node的 新的runner/ds container image
func (st *Subtask) ApplyNewImage() {

	// 统计处于更新中的node数目（k8s层的node个数）
	applying := 0
	var needApplyNewImageNode []*api.NodeInfo
	var clusterDaemonSetMap = make(map[string]bool)
	poolHelper := resourcepool.NewHelper(st.Logger)
	for i := range st.nodeMap {
		node := st.nodeMap[i]
		switch node.State {
		case api.NodeStateScheduleOutdated, api.NodeStateScheduleUpgrading, api.NodeStateK8sRaw, api.NodeStateK8sInitializing, api.NodeStateK8sInitialized:
			applying++
			continue
		case api.NodeStateScheduleBusy, api.NodeStateScheduleInitialized:
			isNeeded := false

			for ds, versionInfo := range st.k8sInfo.DsReleaseConfigMap {

				if versionInfo.Md5 == "" {
					st.Logger.Infof("[ApplyNewImage] etcd ds: %s md5 not set, skip", ds)
					continue
				}

				if node.DsReleaseConfigMap == nil {
					st.Logger.Infof("[ApplyNewImage] node's DsReleaseConfigMap is nil, wait for sync node, skip", ds)
					continue
				}
				// 刚打开开关或者新增一个ds 进集群时 送回去重新初始化一下
				if _, ok := node.DsReleaseConfigMap[ds]; !ok {
					clusterDaemonSetMap[ds] = true
					isNeeded = true
				}

				if versionInfo.Md5 != node.DsReleaseConfigMap[ds] {
					clusterDaemonSetMap[ds] = true
					isNeeded = true
				}
			}
			//if node.ContainerImage != st.k8sInfo.ContainerImage || isNeeded {
			//	needApplyNewImageNode = append(needApplyNewImageNode, node)
			//}
			if !poolHelper.IsNodeContainerImageEqual(node, st.k8sInfo) || isNeeded {
				needApplyNewImageNode = append(needApplyNewImageNode, node)
			}

			if st.k8sInfo.ServiceType == api.ServiceTypeCFCKata && st.k8sInfo.KataContainerSpec != nil && !reflect.DeepEqual(node.KataContainerSpec, st.k8sInfo.KataContainerSpec) {
				needApplyNewImageNode = append(needApplyNewImageNode, node)
			}

		}
	}

	if len(needApplyNewImageNode) <= 0 {
		st.Logger.V(4).Infof("[ApplyNewImage]: need apply new image node number is %d, skipping ...", len(needApplyNewImageNode))
		return
	}

	st.Logger.V(4).Infof("[ApplyNewImage]: current applying new image node number: %d", applying)
	if applying >= st.k8sInfo.NodeConfig.ApplyImageBatchNodeSize {
		st.Logger.Infof("[ApplyNewImage]: applying new image nodes's number %d is bigger or equal than batch size, skipping ...", applying)
		return
	}

	// 获取当前配置中的且存在于镜像仓库的镜像列表
	checkResult, err := st.checkNewImage()
	if err != nil {
		st.Logger.Errorf("check imageID from registry hub error : %s", err)
		return
	}

	// 将需要apply到集群的配置 apply一下：
	for ds, needApply := range clusterDaemonSetMap {
		// 检查imageID是不是存在于镜像仓库中
		if _, ok := checkResult[st.k8sInfo.DsReleaseConfigMap[ds].ImageID]; !ok {
			st.Logger.Errorf("ds: %s imageID: %s not exist in hub", ds, st.k8sInfo.DsReleaseConfigMap[ds].ImageID)
			return
		}
		if needApply {
			err := st.ClusterControl.ApplyDaemonSet(ds)
			if err != nil {
				st.Logger.Errorf("[ApplyNewImage] apply DaemonSet %s error: %v", ds, err)
				return
			}
			st.Logger.Infof("[ApplyNewImage] apply DaemonSet: %s, new conf file md5: %s", ds, st.k8sInfo.DsReleaseConfigMap[ds].Md5)
		}

	}
	// 检查containerImage 是不是存在于
	if _, ok := checkResult[st.k8sInfo.ContainerImage]; !ok {
		st.Logger.Errorf("imageID: %s not exist in registry hub", st.k8sInfo.ContainerImage)
		return
	}
	if st.k8sInfo.ServiceType == api.ServiceTypeCFCKata && st.k8sInfo.KataContainerSpec != nil {
		if _, ok := checkResult[st.k8sInfo.KataContainerSpec.Image]; !ok {
			st.Logger.Errorf("imageID: %s not exist in registry hub", st.k8sInfo.KataContainerSpec.Image)
			return
		}

	}

	// 计算此批需要升级的node个数
	curApplySize := st.k8sInfo.NodeConfig.ApplyImageBatchNodeSize - applying
	// 检查需要更新image的node，发送Outdate事件
	sendNum := 0
	for _, node := range needApplyNewImageNode {
		if sendNum < curApplySize {
			// 发送outdate事件
			st.Logger.WithField(common.NodeIDKey, node.ID).V(1).Infof(
				"[ApplyNewImagee] starting apply new node image %s, %s, %s", node.ID, node.State, node.ContainerImage)
			err := st.NodeFSM.TriggerEvent(schedule.EventNameOutdate, node, nil)
			if err != nil {
				st.Logger.WithField(common.NodeIDKey, node.ID).Errorf("[ApplyNewImagee] applying new image failed: %v", err)
				continue
			}
			sendNum++
		} else {
			break
		}
	}
}

// 检验k8sinfo中各imageID是否存在于镜像仓库
func (st *Subtask) checkNewImage() (map[string]struct{}, error) {
	images, err := st.ClusterControl.GetImageListV2()
	if err != nil {
		return nil, err
	}
	size := len(images)
	checkResult := make(map[string]struct{}, size)
	for _, image := range images {
		switch t := image.(type) {
		case rpc.ImageModel:
			checkResult[t.Address] = struct{}{}
		case rpc.CcrImageModel:
			checkResult[t.Address] = struct{}{}
		}
	}
	// Debug 打印checkResult
	st.Logger.Debugf("check image result: %+v", checkResult)
	return checkResult, nil

}
