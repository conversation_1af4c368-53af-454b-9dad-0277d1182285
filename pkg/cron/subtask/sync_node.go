package subtask

import (
	"time"

	"icode.baidu.com/baidu/faas/kun/pkg/cron/impl"

	"icode.baidu.com/baidu/faas/kun/pkg/api"
	"icode.baidu.com/baidu/faas/kun/pkg/cron/common"
	"icode.baidu.com/baidu/faas/kun/pkg/util/logs"
)

// SyncNodes 将集群的获取的node信息同步到etcd
func (st *Subtask) SyncNodes() {
	// 获取集群node
	var err error
	st.clusterNodeMap, err = st.getClusterNodes()
	if err != nil {
		st.Logger.Errorf("get cluster node failed %s", err.Error())
		return
	}
	st.Logger.Infof("[syncNodes] get %d cluster nodes", len(st.clusterNodeMap))

	// 比较和更新node
	st.compareNodes(st.clusterNodeMap, st.nodeMap)

	// 检查node信息合法性
	st.checkNodes(st.nodeMap)
}

// getClusterNodes 获取集群的全部node
func (st *Subtask) getClusterNodes() (m map[string]*api.NodeInfo, err error) {
	clusterNodes, err := st.ClusterControl.ListNodes()

	// 失败将使本集群状态转为错误，不参加扩缩容之类的集群操作
	if err != nil {
		st.status = ClusterStatusError
		return
	}

	st.pendingNodeCount = clusterNodes.PendingCount
	st.resourcePoolToPendNodeCount = clusterNodes.ResourcePoolToPendingCount
	st.Logger.Infof("[getClusterNodes] get %d cluster nodes, get %d waitScalDown cce nodes, pending %d, unavailable %d",
		len(clusterNodes.Nodes), len(clusterNodes.WaitScaleDownNodes), clusterNodes.PendingCount, clusterNodes.UnavailableCount)

	st.waitScaleDownCceNodeMap = clusterNodes.WaitScaleDownNodes
	m = make(map[string]*api.NodeInfo)
	for _, clusterNode := range clusterNodes.Nodes {
		m[clusterNode.ID] = clusterNode
	}

	return
}

// checkNodes 检查node信息的合法性
func (st *Subtask) checkNodes(nodeMap map[string]*api.NodeInfo) {
	for _, node := range nodeMap {
		isChanged := false
		if node.State == api.NodeStateScheduleBusy {
			// 检查node启动时间的正确性，为空的话将当前时间补充上去
			if node.StartTime.IsZero() {
				node.StartTime = time.Now()
				isChanged = true
				logs.Warnf("[checkNodes] add start time to node %s", node.ID)
			}
		}

		// 兼容旧格式的node，为已初始化的node填上pod count
		if node.OriginalMemorySize != 0 && node.PodCount == 0 {
			isChanged = true
			node.PodCount = impl.ComputeInitDeployPodCount(node, st.k8sInfo.Flavors)
		}

		if isChanged {
			logs.V(6).Infof("[checkNodes] write node %s to etcd", node.ID)
			err := st.NodeStore.SetNode(node)
			if err != nil {
				logs.Errorf("[checkNodes] write node %s to etcd failed: %v", node.ID, err)
			}
		}
		//在nodeMap记录node的价格
		if node.Flavor.Charge == 0 {
			for _, flavor := range st.k8sInfo.Flavors {
				if node.Flavor.Spec == flavor.Spec {
					node.Flavor.Charge = flavor.Charge
				}
			}
		}
	}
}

// compareNodes 同步集群与etcd的node
func (st *Subtask) compareNodes(clusterNodeMap map[string]*api.NodeInfo,
	etcdNodeMap map[string]*api.NodeInfo) {

	// 因为cce出现接口不稳定的情况，因此可能导致错误删除；这里本来的删除node就不做了；依靠健康检查来看node是否可用

	for nodeID, etcdNode := range etcdNodeMap {
		// 将etcd中node状态已经为CCE_Delete的node删除掉,因为自动扩缩容开了以后遗留的CCE_delete数据较多
		if etcdNode.State == api.NodeStateCceDeleted {
			delete(etcdNodeMap, nodeID)
			err := st.NodeStore.DeleteEntireNode(nodeID)
			st.Logger.WithField(common.NodeIDKey, nodeID).Infof("delete CCE_deleted node from etcd")
			if err != nil {
				st.Logger.WithField(common.NodeIDKey, nodeID).Infof("delete CCE_deleted node from etcd error %v", err)

			}
		}

		// 将etcd中状态为CCE_error的node，并且该node已不在cce node列表中的node删除
		if etcdNode.State == api.NodeStateCceError {
			if _, ok := clusterNodeMap[nodeID]; !ok {
				delete(etcdNodeMap, nodeID)
				err := st.NodeStore.DeleteEntireNode(nodeID)
				st.Logger.WithField(common.NodeIDKey, nodeID).Infof("delete CCE_error node from etcd")
				if err != nil {
					st.Logger.WithField(common.NodeIDKey, nodeID).Infof("delete CCE_error node from etcd error %v", err)

				}
			}
		}
	}

	// 增加和更新node
	for nodeID, clusterNode := range clusterNodeMap {
		logger := st.Logger.WithField(common.NodeIDKey, nodeID)
		isChanged := false
		var etcdNode *api.NodeInfo

		var ok bool
		if etcdNode, ok = etcdNodeMap[nodeID]; ok {
			// TODO: 在更新node时处理node不可用的情景
			// 更新node，兼容新版
			if etcdNode.ServiceType != clusterNode.ServiceType {
				etcdNode.ServiceType = clusterNode.ServiceType
				isChanged = true
				logger.Infof("add service type %s", etcdNode.ServiceType)
			}

			if !etcdNode.ClusterLabels.Equal(clusterNode.ClusterLabels) {
				etcdNode.ClusterLabels = clusterNode.ClusterLabels
				isChanged = true
				logger.Infof("add cluster label %v", etcdNode.ClusterLabels)

			}

			//兼容回退
			if len(st.k8sInfo.DsReleaseConfigMap) == 0 {
				logger.Infof("no ds config")
				etcdNode.DsReleaseConfigMap = nil
				isChanged = true
			} else {
				if etcdNode.DsReleaseConfigMap == nil && clusterNode.DsReleaseConfigMap != nil {
					logger.Infof("update DsReleaseConfigMap from k8s")
					etcdNode.DsReleaseConfigMap = clusterNode.DsReleaseConfigMap
					isChanged = true
				}
			}

			if etcdNode.ServiceType == api.ServiceTypeCFCKata && etcdNode.KataRuntimeImageID == "" && etcdNode.AvailableKataRuntimeImageIDs.Contains(clusterNode.KataRuntimeImageID) {
				logger.Infof("update KataRuntimeImageID from k8s")
				etcdNode.KataRuntimeImageID = clusterNode.KataRuntimeImageID
				isChanged = true
			}

		} else {
			// 把集群中读出的node添加到etcd
			etcdNode = clusterNode
			etcdNodeMap[nodeID] = etcdNode
			logger.Infof("[compareNodes] add node %s to etcd", nodeID)
			isChanged = true
		}

		if isChanged {
			err := st.NodeStore.SetNode(etcdNode)
			logger.V(5).Infof("[compareNodes] write node %+v to etcd", etcdNode)
			if err != nil {
				logger.Errorf("[compareNodes] write node %s to etcd error %v", etcdNode.ID, err)
			}
		}
	}
	//更新node规格到etcdNodeMap中
	for nodeID, etcdNode := range etcdNodeMap {
		//避免etcdNodeMap中有node,但是clusterNodeMap没有的情况
		if _, ok := clusterNodeMap[nodeID]; ok && etcdNode.Flavor.Spec == "" {
			etcdNode.Flavor.Spec = clusterNodeMap[nodeID].Flavor.Spec
		}
	}
	return
}
