package subtask

import (
	"sync"

	"icode.baidu.com/baidu/faas/kun/pkg/api"
	"icode.baidu.com/baidu/faas/kun/pkg/cron/resourcepool"
)

// autoScalingNodeWithResourcePool 支持ResourcePool的扩缩容逻辑（复用现有逻辑）
// 伸缩最上层函数需要校验各种数据、空指针
func (st *Subtask) autoScalingNodeWithResourcePool() {
	// 1. 先将节点按资源池进行划分存入st.resourcePoolToNodes
	// 2. 检查各种配置逻辑
	// 3. 并发为每个池子执行自动扩缩容
	helper := resourcepool.NewHelper(st.Logger)
	st.resourcePoolToNodes = helper.ClassifyNodesMapByResourcePool(st.nodeMap)
	// 如果集群没有开启资源池配置(包含资源池配置为nil的场景)
	if !helper.IsResourcePoolEnabled(st.k8sInfo) {
		// 清理掉现存的非默认资源池节点
		st.recycleResourcePoolNodes()
		// 默认池正常执行自动伸缩
		st.autoScalingNode(api.DefaultResourcePool)
		return
	}
	// 集群开启资源池配置
	// 只会被缩容的池子（即池子在配置中以及disable或者被删除了）
	scalingDownPool := make(map[string]struct{})
	// 正常扩缩容的池子（既有池子节点，也有池子配置）
	normalPool := make(map[string]struct{})
	// 先添加默认池，必定是正常扩缩容的池子
	normalPool[api.DefaultResourcePool] = struct{}{}
	// 如果集群有池子节点，但是无池子配置，说明这部分节点应该被offline
	for pool, _ := range st.resourcePoolToNodes {
		if _, ok := st.k8sInfo.ResourcePoolConfig.ExtraResourcePools[pool]; !ok {
			scalingDownPool[pool] = struct{}{}
		} else {
			normalPool[pool] = struct{}{}
		}
	}
	// 如果集群中有配置，但是集群中无池子节点，需要走正常扩缩容逻辑
	for pool, _ := range st.k8sInfo.ResourcePoolConfig.ExtraResourcePools {
		if _, ok := st.resourcePoolToNodes[pool]; !ok {
			normalPool[pool] = struct{}{}
			// 需要添加，因为后续扩缩容都是通过resourcePool从poolToNodes中查找
			st.resourcePoolToNodes[pool] = make([]*api.NodeInfo, 0)
		}
	}

	// 对不同的池子进行并发扩缩容
	for pool, _ := range scalingDownPool {
		go st.recycleNodesInPool(pool, st.resourcePoolToNodes[pool])
	}
	// 等待所有正常池的扩缩容结束
	wg := &sync.WaitGroup{}
	for pool, _ := range normalPool {
		wg.Add(1)
		go func() {
			defer wg.Done()
			st.autoScalingNode(pool)
		}()
	}
	wg.Wait()
}
