package subtask

import (
	"errors"
	"fmt"
	"sync"
	"time"

	"icode.baidu.com/baidu/faas/kun/pkg/api"
	"icode.baidu.com/baidu/faas/kun/pkg/cron/common"
	"icode.baidu.com/baidu/faas/kun/pkg/cron/impl"
	"icode.baidu.com/baidu/faas/kun/pkg/nodefsm"
	"icode.baidu.com/baidu/faas/kun/pkg/nodefsm/cce-events"
	ce "icode.baidu.com/baidu/faas/kun/pkg/nodefsm/common-events"
	"icode.baidu.com/baidu/faas/kun/pkg/nodefsm/k8s-events"
	"icode.baidu.com/baidu/faas/kun/pkg/nodefsm/schedule-events"
	"icode.baidu.com/baidu/faas/kun/pkg/nodefsm/trans-events"
	"icode.baidu.com/baidu/faas/kun/pkg/util/baiduhi"
	"icode.baidu.com/baidu/faas/kun/pkg/util/logs"
)

//go:generate mockgen -destination=./mock/cluster_subtask.go -package=mock icode.baidu.com/baidu/faas/kun/pkg/cron/subtask SubtaskInterface
type SubtaskInterface interface {
	AddNode(node *api.NodeInfo)
	Run()
	Stats() *common.Stats
}

const (
	ClusterStatusOK uint8 = iota
	ClusterStatusError
)

func NewNodeFSM(nodeStore impl.NodeStore, m impl.ScheduleNodeControl,
	logger *logs.Logger) nodefsm.FSMInterface {
	// 初始化状态机，注册全部事件
	fsm := nodefsm.NewFSM(nodeStore, logger)

	scheduleEvents := schedule.CreateCloudCronEvents(m)
	fsm.AddEvent(scheduleEvents...)

	k8sEvents := k8s.CreateK8sEvents()
	fsm.AddEvent(k8sEvents...)

	cceEvents := cce.CreateCloudCronEvents()
	fsm.AddEvent(cceEvents...)

	transEvents := trans.CreateCloudCronEvents(m)
	fsm.AddEvent(transEvents...)

	commonEvents := ce.CreateCronEvents(m)
	fsm.AddEvent(commonEvents...)
	return fsm
}

// Subtask 负责以集群为单位执行任务
type Subtask struct {
	*common.Common
	k8sInfo              *api.K8sInfo
	ScheduleNodeControl  impl.ScheduleNodeControl
	NodeContainerControl impl.NodeContainerControl
	ClusterControl       impl.ClusterControl
	NodeFSM              nodefsm.FSMInterface
	HiClient             baiduhi.BaiduHiControl
	status               uint8
	Logger               *logs.Logger  // 覆盖Task里的日志对象
	stats                *common.Stats // 记录本循环的统计结果

	// 缓存
	nodeMap                     map[string]*api.NodeInfo   // 缓存每次循环从nodeStore获取到的node信息，key是node id
	fakeColdNodeMap             map[string]struct{}        // 未填回redis的cold node, key是node id
	fakeColdNodeMapMutex        sync.RWMutex               // 加锁防止并发读写
	clusterNodeMap              map[string]*api.NodeInfo   // 缓存每次循环从nodeReserveControl获取到的node，key是node id
	waitScaleDownCceNodeMap     map[string]*api.NodeInfo   // 缓存每次循环从cce list node 获取到的node信息， key是 node id
	pendingNodeCount            uint                       // 扩容后还未准备好的node数量
	resourcePoolToPendNodeCount map[string]uint            // 统计各个资源池正在扩容的节点数量
	resourcePoolToNodes         map[string][]*api.NodeInfo // key为resourcePool name，值为该池子下所有的节点，遍历nodeMap后划分
}

func NewClusterSubtask(t *common.Common, k8sInfo *api.K8sInfo) *Subtask {
	logger := t.Logger.WithField(common.ClusterIDKey, k8sInfo.CceClusterUUID)
	baseClusterControl := impl.NewBaseClusterControl(logger, t.RunOptions, k8sInfo, t.FuncletClient) // k8s层与cce层共享集群配置
	scheduleControl := impl.NewScheduleNodeControlByClients(
		t.EtcdClient, t.FuncletClient,
		logger, t.ReserveManager, baseClusterControl, t.ProxyAgentClient)
	nodeFSM := NewNodeFSM(t.EtcdClient, scheduleControl, logger)
	if t.TaskControl.NodeFSM == nil {
		t.TaskControl.NodeFSM = nodeFSM
	}

	var clusterCtrl impl.ClusterControl
	clusterCtrl = impl.NewCceNodeControl(baseClusterControl)
	if k8sInfo.CCEClusterAPIVersion == api.CCEClusterVersionV2 {
		clusterCtrl = impl.NewCceNodeControlV2(baseClusterControl)
	}
	st := &Subtask{
		Common:                      t,
		k8sInfo:                     k8sInfo,
		ScheduleNodeControl:         scheduleControl,
		NodeContainerControl:        impl.NewK8sNodeControl(baseClusterControl),
		ClusterControl:              clusterCtrl,
		HiClient:                    t.HiClient,
		NodeFSM:                     nodeFSM,
		nodeMap:                     make(map[string]*api.NodeInfo),
		resourcePoolToPendNodeCount: make(map[string]uint),
		resourcePoolToNodes:         make(map[string][]*api.NodeInfo),
		clusterNodeMap:              make(map[string]*api.NodeInfo),
		fakeColdNodeMap:             make(map[string]struct{}),
		stats:                       common.NewStats(),
		Logger:                      logger,
	}
	return st
}

func (st *Subtask) ClusterStatus() uint8 {
	return st.status
}

func (st *Subtask) CheckClusterStatusIfAvailableScaling() bool {
	if st.status != ClusterStatusOK || st.k8sInfo.ServiceType == api.ServiceTypeCFCKata {
		return false
	}
	return true
}

// AddNode 将etcd node加入缓存
func (st *Subtask) AddNode(node *api.NodeInfo) {
	// cluster id 理论上不可能错误
	if node.CceClusterUUID != st.k8sInfo.CceClusterUUID {
		panic(fmt.Sprintf("wrong cluster id %s", node.CceClusterUUID))
	}

	st.nodeMap[node.ID] = node
}

// ListNodes 返回集群里的node列表
func (st *Subtask) ListNodes() (nodes []*api.NodeInfo) {
	nodes = make([]*api.NodeInfo, len(st.nodeMap))
	i := 0
	for _, node := range st.nodeMap {
		nodes[i] = node
		i++
	}
	return
}

func (st *Subtask) Stats() *common.Stats {
	return st.stats
}

func (st *Subtask) Run() {
	begTime := time.Now()
	st.Logger.Infof("SubTask [initTask] begin at %s", begTime)

	if err := st.checkConfig(); err != nil {
		st.Logger.Errorf("check cluster config error: %s", err.Error())
		return
	}

	if st.k8sInfo.NodeConfig.SyncNode {
		st.Logger.Infof("SubTask [syncNodes] begin at %s", time.Now())
		st.SyncNodes()
	} else {
		st.Logger.Info("SubTask [syncNodes] skip")
	}

	st.Logger.Infof("SubTask [container] begin at  %s", time.Now())
	st.Container()

	st.Logger.Infof("SubTask [trans] begin at  %s", time.Now())
	st.Trans()

	st.Logger.Infof("SubTask [inspect] begin at  %s", time.Now())
	st.Inspect()

	st.Logger.Infof("SubTask [schedule] begin at  %s", time.Now())
	st.Schedule()

	if st.k8sInfo.AutoApplyImage {
		st.Logger.Infof("SubTask [applyNewImage] begin at  %s", time.Now())
		st.ApplyNewImage()
	}

	if st.k8sInfo.ServiceType == api.ServiceTypeCFCKata {
		st.Logger.Infof("SubTask [checkAndPullKataImages] begin at  %s", time.Now())
		st.CheckAndPullKataImages()
	}

	st.Logger.Infof("SubTask [initAllNodes] begin at  %s", time.Now())
	st.initAllNodes()

	switch st.k8sInfo.ScalingOptions.AutoScalingType {
	case api.AutoScalingTypeNode:
		st.Logger.Infof("SubTask [autoScalingNodeWithResourcePool] begin at  %s", time.Now())
		st.autoScalingNodeWithResourcePool() // 使用支持ResourcePool的扩缩容逻辑
	case api.AutoScalingTypePod:
		st.Logger.Infof("SubTask [autoScalingPod] begin at  %s", time.Now())
		st.autoScalingPod()
	default:
		st.Logger.Info("SubTask [autoScaling] skip")
	}

	if st.k8sInfo.ScalingOptions.ScalingDown {
		st.Logger.Infof("SubTask [scalingDownNode] begin at  %s", time.Now())
		st.processScalingDownNode()
	} else {
		st.Logger.Info("SubTask [scalingDownNode] skip")
	}

	endTime := time.Now()
	st.Logger.Infof("SubTask end at %s, spend %s", endTime, endTime.Sub(begTime))
}

const scalingThresholdGap float64 = 0.2 // 3个扩缩容阈值的默认间隔值

// checkConfig 检查集群配置合法性
func (st *Subtask) checkConfig() error {
	if err := st.checkNodeConfig(); err != nil {
		return err
	}

	scalingOptions := &st.k8sInfo.ScalingOptions

	// 如果不开node同步，扩容是没意义的
	if !st.k8sInfo.NodeConfig.SyncNode &&
		(scalingOptions.AutoScalingType == api.AutoScalingTypeNode ||
			scalingOptions.AutoScalingType == api.AutoScalingTypePod) {
		scalingOptions.AutoScalingType = api.AutoScalingTypeNil
	}

	if err := st.checkScalingThreshold(); err != nil {
		return err
	}

	return nil
}

func (st *Subtask) checkNodeConfig() error {
	nodeConfig := &st.k8sInfo.NodeConfig
	for _, flavor := range st.k8sInfo.Flavors {
		if flavor.MaxPodDeploy <= 0 {
			return errors.New("flavor maxPodDeploy should be positive")
		}
	}

	if nodeConfig.PodMemoryReservedRatio < 1 {
		return errors.New("PodMemoryReservedRatio should be lower than 1")
	}

	return nil
}

// checkScalingThreshold 检查扩缩容配置的合法性
func (st *Subtask) checkScalingThreshold() error {
	scalingOptions := &st.k8sInfo.ScalingOptions
	if scalingOptions.AutoScalingType == api.AutoScalingTypeNil {
		return nil
	}

	// 检查扩缩容配置合法性，填入默认值，当前只支持128内存
	if len(scalingOptions.ThresholdMap) != 1 {
		return errors.New("count of scaling options must be 1")
	}
	scalingThreshold := scalingOptions.ThresholdMap[podMemorySize]
	if scalingThreshold == nil {
		return fmt.Errorf("can't find %d scaling options", podMemorySize)
	}

	// 冗余值为0太危险，不允许
	if scalingThreshold.UnoccupiedRedundancy == 0 {
		return fmt.Errorf("UnoccupiedRedundancy should be large than 0")
	}
	if scalingThreshold.ScalingUpTrigger < 1.0 {
		// 木有办法，至少得有扩容阈值
		return errors.New("ScalingUpTrigger should be large than 1")
	}
	//if scalingThreshold.ScalingDownResult < scalingThreshold.ScalingUpTrigger {
	//	scalingThreshold.ScalingDownResult = scalingThreshold.ScalingUpTrigger + scalingThresholdGap
	//	st.Logger.Infof("set ScalingDownResult to %f", scalingThreshold.ScalingDownResult)
	//}
	if scalingThreshold.ScalingDownTrigger < scalingThreshold.ScalingUpTrigger {
		scalingThreshold.ScalingDownTrigger = scalingThreshold.ScalingUpTrigger + scalingThresholdGap
		st.Logger.Infof("set ScalingDownTrigger to %f", scalingThreshold.ScalingDownTrigger)
	}

	return nil
}
