package subtask

import (
	"icode.baidu.com/baidu/faas/kun/pkg/api"
	"icode.baidu.com/baidu/faas/kun/pkg/cron/resourcepool"
	"icode.baidu.com/baidu/faas/kun/pkg/nodefsm/schedule-events"
)

// recycleResourcePoolNodes 回收所有ResourcePool专用节点
// 回收集群所有extra资源池的节点
func (st *Subtask) recycleResourcePoolNodes() {
	helper := resourcepool.NewHelper(st.Logger)
	if helper == nil {
		st.Logger.Warnf("ResourcePool helper not available, skipping node recycle")
		return
	}

	// 回收所有ResourcePool专用节点
	for poolName, poolNodes := range st.resourcePoolToNodes {
		// 跳过default池
		if poolName == api.DefaultResourcePool {
			continue
		}

		st.Logger.Infof("Recycling %d nodes from ResourcePool %s", len(poolNodes), poolName)
		// 资源池并行回收
		go st.recycleNodesInPool(poolName, poolNodes)
	}
}

// recycleNodesInPool 回收特定ResourcePool中的节点
func (st *Subtask) recycleNodesInPool(poolName string, nodes []*api.NodeInfo) int {
	recycleCount := 0
	for _, node := range nodes {
		st.Logger.Infof("Recycling node %s from removed ResourcePool %s", node.ID, poolName)
		res := st.recycleResourcePoolNode(node, poolName, "ResourcePool was deleted: "+poolName)
		if res {
			recycleCount++
		}
	}
	st.Logger.Infof("[recyclePool] %d nodes from removed ResourcePool %s, %d nodes recycled failed",
		recycleCount, poolName, len(nodes)-recycleCount)
	return recycleCount
}

// recycleResourcePoolNode 回收单个ResourcePool节点
func (st *Subtask) recycleResourcePoolNode(node *api.NodeInfo, poolName, reason string) bool {
	st.Logger.Infof("Starting recycle for ResourcePool node %s (pool: %s), reason: %s",
		node.ID, poolName, reason)
	// 节点进入offline状态即从redis摘流，等待后续任务回收即可
	scalingDownNodeFunc := func(node *api.NodeInfo) bool {
		if err := st.NodeFSM.TriggerEvent(schedule.EventNameOffline, node, nil); err != nil {
			st.Logger.Errorf("[recycleResourcePool] offline nodeId : %s; failed: %s", node.ID, err.Error())
			return false
		}
		return true
	}
	// 等后续任务将节点转换为initialized状态或busy状态再删除
	if node.State != api.NodeStateScheduleInitialized && node.State != api.NodeStateScheduleBusy {
		st.Logger.Infof("node: %s is not in initialized or busy state, skipping, wait next round task", node.ID)
		return false
	}
	res := scalingDownNodeFunc(node)
	if res {
		st.Logger.Infof("[recycleReourcePool] offline success nodeId: %s, resourecePool: %s", node.ID, poolName)
	}
	return res
}

// checkResourcePoolNodesForRecycle 检查ResourcePool节点是否需要回收
func (st *Subtask) checkResourcePoolNodesForRecycle() {
	helper := resourcepool.NewHelper(st.Logger)
	if helper == nil {
		return
	}

	// 如果ResourcePool功能被禁用，回收所有专用节点
	if !helper.IsResourcePoolEnabled(st.k8sInfo) {
		st.Logger.Infof("ResourcePool feature disabled, recycling all ResourcePool nodes")
		st.recycleResourcePoolNodes()
		return
	}

	// 如果ResourcePool功能启用，检查是否有池子被移除
	st.checkRemovedResourcePools()
}

// checkRemovedResourcePools 检查被移除的ResourcePool并回收相关节点
func (st *Subtask) checkRemovedResourcePools() {
	helper := resourcepool.NewHelper(st.Logger)
	if helper == nil {
		return
	}

	// 获取所有节点
	nodes, err := st.NodeStore.GetAllNodes()
	if err != nil {
		st.Logger.Errorf("Failed to get nodes for ResourcePool check: %v", err)
		return
	}

	// 按ResourcePool分类节点
	nodesByPool := helper.ClassifyNodesByResourcePool(nodes)

	// 检查每个池子是否还有效
	for poolName, poolNodes := range nodesByPool {
		// 跳过default池
		if poolName == "default" {
			continue
		}

		// 检查该ResourcePool是否还在配置中
		if !helper.IsValidResourcePool(st.k8sInfo, poolName) {
			st.Logger.Infof("ResourcePool %s no longer exists, recycling %d nodes", poolName, len(poolNodes))
			st.recycleNodesInPool(poolName, poolNodes)
		}
	}
}
