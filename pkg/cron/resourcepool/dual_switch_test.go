package resourcepool

import (
	"testing"

	"icode.baidu.com/baidu/faas/kun/pkg/api"
	"icode.baidu.com/baidu/faas/kun/pkg/util/logs"
)

// TestCronHelperDualSwitch 测试cron模块helper的双开关逻辑
func TestCronHelperDualSwitch(t *testing.T) {
	logger := logs.NewLogger()
	helper := &Helper{logger: logger}

	// 测试用例1：全局开关关闭
	t.Run("GlobalSwitchDisabled", func(t *testing.T) {
		k8sInfo := &api.K8sInfo{
			ResourcePoolConfig: &api.ResourcePoolConfig{
				Enabled: false, // 全局开关关闭
				ExtraResourcePools: map[string]api.ResourcePool{
					"ubuntu2204-pool": {
						Enabled: true, // 池子开关开启
					},
				},
			},
		}

		// ResourcePool功能应该被禁用
		if helper.IsResourcePoolEnabled(k8sInfo) {
			t.Error("ResourcePool should be disabled when global switch is off")
		}

		// 池子应该无效
		if helper.IsValidResourcePool(k8sInfo, "ubuntu2204-pool") {
			t.Error("Pool should be invalid when global switch is off")
		}

		// 获取池子配置应该返回nil
		if config := helper.GetExtraPoolConfig(k8sInfo, "ubuntu2204-pool"); config != nil {
			t.Error("Should return nil when global switch is off")
		}
	})

	// 测试用例2：全局开关开启，池子开关关闭
	t.Run("GlobalEnabled_PoolDisabled", func(t *testing.T) {
		k8sInfo := &api.K8sInfo{
			ResourcePoolConfig: &api.ResourcePoolConfig{
				Enabled: true, // 全局开关开启
				ExtraResourcePools: map[string]api.ResourcePool{
					"ubuntu2204-pool": {
						Enabled: false, // 池子开关关闭
					},
				},
			},
		}

		// ResourcePool功能应该启用
		if !helper.IsResourcePoolEnabled(k8sInfo) {
			t.Error("ResourcePool should be enabled when global switch is on")
		}

		// 池子应该无效（被单独禁用）
		if helper.IsValidResourcePool(k8sInfo, "ubuntu2204-pool") {
			t.Error("Pool should be invalid when individually disabled")
		}

		// 获取池子配置应该返回nil
		if config := helper.GetExtraPoolConfig(k8sInfo, "ubuntu2204-pool"); config != nil {
			t.Error("Should return nil when pool is disabled")
		}
	})

	// 测试用例3：双开关都开启
	t.Run("BothSwitchesEnabled", func(t *testing.T) {
		k8sInfo := &api.K8sInfo{
			ResourcePoolConfig: &api.ResourcePoolConfig{
				Enabled: true, // 全局开关开启
				ExtraResourcePools: map[string]api.ResourcePool{
					"ubuntu2204-pool": {
						Enabled: true, // 池子开关开启
						ResourcePoolInfo: api.ResourcePoolInfo{
							Description: "Ubuntu 22.04 pool",
						},
					},
				},
			},
		}

		// ResourcePool功能应该启用
		if !helper.IsResourcePoolEnabled(k8sInfo) {
			t.Error("ResourcePool should be enabled")
		}

		// 池子应该有效
		if !helper.IsValidResourcePool(k8sInfo, "ubuntu2204-pool") {
			t.Error("Pool should be valid when both switches are enabled")
		}

		// 获取池子配置应该成功
		config := helper.GetExtraPoolConfig(k8sInfo, "ubuntu2204-pool")
		if config == nil {
			t.Error("Should return pool config when both switches are enabled")
		}
		if config.ResourcePoolInfo.Description != "Ubuntu 22.04 pool" {
			t.Error("Should return correct pool config")
		}
	})

	// 测试用例4：多个池子混合状态
	t.Run("MultiplePoolsMixedStates", func(t *testing.T) {
		k8sInfo := &api.K8sInfo{
			ResourcePoolConfig: &api.ResourcePoolConfig{
				Enabled: true, // 全局开关开启
				ExtraResourcePools: map[string]api.ResourcePool{
					"enabled-pool": {
						Enabled: true, // 启用
						ResourcePoolInfo: api.ResourcePoolInfo{
							Description: "Enabled pool",
						},
					},
					"disabled-pool": {
						Enabled: false, // 禁用
						ResourcePoolInfo: api.ResourcePoolInfo{
							Description: "Disabled pool",
						},
					},
				},
			},
		}

		// 启用的池子应该有效
		if !helper.IsValidResourcePool(k8sInfo, "enabled-pool") {
			t.Error("Enabled pool should be valid")
		}

		// 禁用的池子应该无效
		if helper.IsValidResourcePool(k8sInfo, "disabled-pool") {
			t.Error("Disabled pool should be invalid")
		}

		// 启用的池子应该能获取配置
		enabledConfig := helper.GetExtraPoolConfig(k8sInfo, "enabled-pool")
		if enabledConfig == nil {
			t.Error("Should return config for enabled pool")
		}

		// 禁用的池子应该返回nil
		disabledConfig := helper.GetExtraPoolConfig(k8sInfo, "disabled-pool")
		if disabledConfig != nil {
			t.Error("Should return nil for disabled pool")
		}
	})

	// 测试用例5：default池总是有效（如果全局开关开启）
	t.Run("DefaultPoolBehavior", func(t *testing.T) {
		// 全局开关开启时，default池有效
		k8sInfoEnabled := &api.K8sInfo{
			ResourcePoolConfig: &api.ResourcePoolConfig{
				Enabled: true,
				ExtraResourcePools: map[string]api.ResourcePool{},
			},
		}

		if !helper.IsValidResourcePool(k8sInfoEnabled, api.DefaultResourcePool) {
			t.Error("Default pool should be valid when global switch is enabled")
		}

		// 全局开关关闭时，default池无效
		k8sInfoDisabled := &api.K8sInfo{
			ResourcePoolConfig: &api.ResourcePoolConfig{
				Enabled: false,
				ExtraResourcePools: map[string]api.ResourcePool{},
			},
		}

		if helper.IsValidResourcePool(k8sInfoDisabled, api.DefaultResourcePool) {
			t.Error("Default pool should be invalid when global switch is disabled")
		}
	})

	// 测试用例6：GetEnabledExtraPoolConfig方法测试
	t.Run("GetEnabledExtraPoolConfig", func(t *testing.T) {
		k8sInfo := &api.K8sInfo{
			ResourcePoolConfig: &api.ResourcePoolConfig{
				Enabled: true,
				ExtraResourcePools: map[string]api.ResourcePool{
					"enabled-pool": {
						Enabled: true,
						ResourcePoolInfo: api.ResourcePoolInfo{
							Description: "Enabled pool",
						},
					},
					"disabled-pool": {
						Enabled: false,
						ResourcePoolInfo: api.ResourcePoolInfo{
							Description: "Disabled pool",
						},
					},
				},
			},
		}

		// 启用的池子应该返回配置
		enabledConfig := helper.GetEnabledExtraPoolConfig(k8sInfo, "enabled-pool")
		if enabledConfig == nil {
			t.Error("Should return config for enabled pool")
		}
		if enabledConfig.ResourcePoolInfo.Description != "Enabled pool" {
			t.Error("Should return correct config")
		}

		// 禁用的池子应该返回nil
		disabledConfig := helper.GetEnabledExtraPoolConfig(k8sInfo, "disabled-pool")
		if disabledConfig != nil {
			t.Error("Should return nil for disabled pool")
		}

		// 不存在的池子应该返回nil
		nonExistentConfig := helper.GetEnabledExtraPoolConfig(k8sInfo, "non-existent-pool")
		if nonExistentConfig != nil {
			t.Error("Should return nil for non-existent pool")
		}
	})
}
