package resourcepool

import (
	"icode.baidu.com/baidu/faas/kun/pkg/api"
	"icode.baidu.com/baidu/faas/kun/pkg/util/logs"
)

// Helper ResourcePool辅助工具
type Helper struct {
	logger *logs.Logger
}

// NewHelper 创建ResourcePool辅助工具
func NewHelper(logger *logs.Logger) *Helper {
	return &Helper{
		logger: logger,
	}
}

// IsResourcePoolSchedulingEnabled 检查是否启用ResourcePool调度功能
func (h *Helper) IsResourcePoolSchedulingEnabled(k8sInfo *api.K8sInfo) bool {
	if k8sInfo == nil || k8sInfo.ResourcePoolConfig == nil {
		return false
	}
	return k8sInfo.ResourcePoolConfig.SchedulingEnabled
}

// IsResourcePoolScalingEnabled 检查是否启用ResourcePool扩缩容功能
func (h *Helper) IsResourcePoolScalingEnabled(k8sInfo *api.K8sInfo) bool {
	if k8sInfo == nil || k8sInfo.ResourcePoolConfig == nil {
		return false
	}
	return k8sInfo.ResourcePoolConfig.ScalingEnabled
}

// IsResourcePoolEnabled 检查是否启用ResourcePool功能（兼容性方法，检查扩缩容开关）
func (h *Helper) IsResourcePoolEnabled(k8sInfo *api.K8sInfo) bool {
	return h.IsResourcePoolScalingEnabled(k8sInfo)
}

// GetResourcePoolFromNode 从节点获取ResourcePool名称
func (h *Helper) GetResourcePoolFromNode(node *api.NodeInfo) string {
	if node == nil || node.ClusterLabels == nil {
		return api.DefaultResourcePool
	}

	if pool := node.ClusterLabels.Get(api.LabelResourcePool); pool != "" {
		return pool
	}

	return api.DefaultResourcePool
}

// SetResourcePoolLabelForNode 为节点设置ResourcePool标签
func (h *Helper) SetResourcePoolLabelForNode(node *api.NodeInfo, poolName string) {
	if node == nil {
		return
	}

	if node.ClusterLabels == nil {
		node.ClusterLabels = make(api.RequestLabels)
	}

	if poolName != "" && poolName != api.DefaultResourcePool {
		node.ClusterLabels.Set(api.LabelResourcePool, poolName)
		h.logger.V(6).Infof("Set ResourcePool label for node %s: %s", node.ID, poolName)
	}
}

// ClearResourcePoolLabelForNode 清除节点的ResourcePool标签
func (h *Helper) ClearResourcePoolLabelForNode(node *api.NodeInfo) {
	if node == nil || node.ClusterLabels == nil {
		return
	}

	node.ClusterLabels.Delete(api.LabelResourcePool)
	h.logger.V(6).Infof("Cleared ResourcePool label for node %s", node.ID)
}

// IsValidResourcePool 检查ResourcePool是否在配置中有效（功能分离开关设计）
func (h *Helper) IsValidResourcePool(k8sInfo *api.K8sInfo, poolName string) bool {
	if !h.IsResourcePoolScalingEnabled(k8sInfo) {
		h.logger.V(6).Infof("ResourcePool scaling not enabled")
		return false
	}

	// default池总是有效（如果全局开关开启）
	if poolName == api.DefaultResourcePool {
		return true
	}

	// 检查extraPools中是否存在该池且启用
	if k8sInfo.ResourcePoolConfig.ExtraResourcePools != nil {
		if pool, exists := k8sInfo.ResourcePoolConfig.ExtraResourcePools[poolName]; exists {
			if !pool.Enabled {
				h.logger.V(6).Infof("ResourcePool %s disabled individually", poolName)
				return false
			}
			return true
		}
	}

	return false
}

// GetEnabledExtraPoolConfig 获取启用的额外池子配置（双开关设计）
func (h *Helper) GetEnabledExtraPoolConfig(k8sInfo *api.K8sInfo, poolName string) *api.ResourcePool {
	if !h.IsResourcePoolEnabled(k8sInfo) {
		return nil
	}

	if k8sInfo.ResourcePoolConfig.ExtraResourcePools != nil {
		if pool, exists := k8sInfo.ResourcePoolConfig.ExtraResourcePools[poolName]; exists {
			if pool.Enabled {
				return pool
			}
			h.logger.V(6).Infof("ResourcePool %s exists but disabled", poolName)
		}
	}

	return nil
}

// GetPoolConfig 获取指定ResourcePool的配置
func (h *Helper) getPoolConfig(k8sInfo *api.K8sInfo, poolName string) *api.ResourcePool {
	if !h.IsResourcePoolEnabled(k8sInfo) {
		return nil
	}

	// 对于extraPools，返回对应配置
	if k8sInfo.ResourcePoolConfig.ExtraResourcePools != nil {
		if pool, exists := k8sInfo.ResourcePoolConfig.ExtraResourcePools[poolName]; exists {
			return pool
		}
	}

	return nil
}

// GetExtraPoolConfig 获取额外池子配置（双开关设计）
// 如果不存在/资源池未开启/池子被禁用，都返回nil
func (h *Helper) GetExtraPoolConfig(k8sInfo *api.K8sInfo, poolName string) *api.ResourcePool {
	if poolName == api.DefaultResourcePool {
		return nil
	}
	if !h.IsResourcePoolEnabled(k8sInfo) {
		return nil
	}

	// 对于extraPools，返回对应配置（需要检查单个池子开关）
	if k8sInfo.ResourcePoolConfig.ExtraResourcePools != nil {
		if pool, exists := k8sInfo.ResourcePoolConfig.ExtraResourcePools[poolName]; exists {
			if pool.Enabled {
				return pool
			}
			h.logger.V(6).Infof("ResourcePool %s exists but disabled", poolName)
		}
	}

	return nil
}

// ClassifyNodesByResourcePool 按ResourcePool对节点进行分类
func (h *Helper) ClassifyNodesByResourcePool(nodes []*api.NodeInfo) map[string][]*api.NodeInfo {
	result := make(map[string][]*api.NodeInfo)

	for _, node := range nodes {
		poolName := h.GetResourcePoolFromNode(node)
		result[poolName] = append(result[poolName], node)
	}

	return result
}

// 针对subtask中的map[string]*api.NodeInfo
func (h *Helper) ClassifyNodesMapByResourcePool(nodesMap map[string]*api.NodeInfo) map[string][]*api.NodeInfo {
	res := make(map[string][]*api.NodeInfo)
	if nodesMap == nil {
		return res
	}
	for _, node := range nodesMap {
		poolName := h.GetResourcePoolFromNode(node)
		res[poolName] = append(res[poolName], node)
	}
	return res
}

/*
*
如果资源池不存在/集群没有开启资源池，让节点去使用默认池的镜像配置，方便后续回收
*/
func (h *Helper) IsNodeContainerImageEqual(node *api.NodeInfo, k8sInfo *api.K8sInfo) bool {
	// 开启了集群配置，则找到对应的配置中的镜像
	resourcePool := h.GetResourcePoolFromNode(node)
	if resourcePool == api.DefaultResourcePool {
		return node.ContainerImage == k8sInfo.ContainerImage
	}
	resourcePoolConfig := h.GetExtraPoolConfig(k8sInfo, resourcePool)
	if resourcePoolConfig == nil {
		// 如果资源池不存在，直接返回true，不进行节点上镜像更新，后续直接回收
		return true
	}
	// 资源池配置存在
	return resourcePoolConfig.ContainerImage == node.ContainerImage
}
