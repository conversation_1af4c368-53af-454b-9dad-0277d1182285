package impl

import (
	"os"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"k8s.io/apimachinery/pkg/api/resource"

	"icode.baidu.com/baidu/faas/kun/pkg/api"
	"icode.baidu.com/baidu/faas/kun/pkg/util/logs"
)

func TestIsAllContainersCold(t *testing.T) {
	pods := make([]*api.ContainerInfo, 10)
	for i := range pods {
		pods[i] = &api.ContainerInfo{
			RuntimeInfo: &api.RuntimeInfo{},
		}
	}

	// pod全部在使用
	for _, pod := range pods {
		pod.Status = api.ContainerStatusWarm
	}
	b := IsAllContainersCold(pods)
	assert.False(t, b)

	// pod全部没使用
	for _, pod := range pods {
		pod.Status = api.ContainerStatusCold
	}
	b = IsAllContainersCold(pods)
	assert.True(t, b)

	// 一部分pod在使用
	pods[0].Status = api.ContainerStatusWarm
	b = IsAllContainersCold(pods)
	assert.False(t, b)
}

func TestIsAllContainersIdle(t *testing.T) {
	pods := make([]*api.ContainerInfo, 10)
	for i := range pods {
		pods[i] = &api.ContainerInfo{
			RuntimeInfo: &api.RuntimeInfo{},
		}
	}

	// pod全部在使用
	for _, pod := range pods {
		pod.Status = api.ContainerStatusWarm
		pod.RuntimeInfo = &api.RuntimeInfo{
			RequestID: "commitID",
		}
	}
	b := IsAllContainersIdle(pods)
	assert.False(t, b)

	// pod全部没使用
	for _, pod := range pods {
		pod.Status = api.ContainerStatusCold
	}
	b = IsAllContainersIdle(pods)
	assert.True(t, b)

	// 一部分pod在使用
	pods[0].Status = api.ContainerStatusWarm
	pods[0].RuntimeInfo = &api.RuntimeInfo{
		RequestID: "commitID",
	}
	b = IsAllContainersIdle(pods)
	assert.False(t, b)
}

func TestIsContainerExpired(t *testing.T) {
	maxPodExpire := 3
	functionTTL := 15

	var argResults = []struct {
		Status         api.ContainerStatusType
		RequestID      string
		CommitID       string
		LastAccessTime int64
		IsExpired      bool
	}{
		{ // warmup中，未超时
			api.ContainerStatusWarmup,
			"",
			"",
			toAccessTime(functionTTL + maxPodExpire - 1),
			false,
		},
		{ // warmup中，已超时
			api.ContainerStatusWarmup,
			"",
			"",
			toAccessTime(functionTTL + maxPodExpire + 1),
			true,
		},
		{ // warmup完毕，更新未超时
			api.ContainerStatusWarm,
			"",
			"",
			toAccessTime(functionTTL + maxPodExpire - 1),
			false,
		},
		{ // warmup完毕，更新已超时
			api.ContainerStatusWarm,
			"",
			"",
			toAccessTime(functionTTL + maxPodExpire + 1),
			true,
		},
		{ // 执行中，未超时
			api.ContainerStatusWarm,
			"heihei",
			"10",
			toAccessTime(functionTTL - 1),
			false,
		},
		{ // 执行中，已超时
			api.ContainerStatusWarm,
			"heihei",
			"10",
			toAccessTime(functionTTL + 1),
			true,
		},
		{ // 未执行空闲中，未超时
			api.ContainerStatusWarm,
			"",
			"10",
			toAccessTime(maxPodExpire - 1),
			false,
		},
		{ // 未执行空闲中，已超时
			api.ContainerStatusWarm,
			"",
			"10",
			toAccessTime(maxPodExpire + 1),
			true,
		},
		{ // 未使用过
			api.ContainerStatusCold,
			"",
			"",
			toAccessTime(functionTTL + maxPodExpire + 1),
			false,
		},
		{ // 不会被使用
			api.ContainerStatusMemoryMismatch,
			"",
			"",
			toAccessTime(functionTTL + maxPodExpire + 1),
			true,
		},
		{
			api.ContainerStatusSick,
			"",
			"",
			toAccessTime(maxPodExpire + 1),
			false,
		},
	}

	for _, a := range argResults {
		container := &api.ContainerInfo{
			Status: a.Status,
			RuntimeInfo: &api.RuntimeInfo{
				RequestID:      a.RequestID,
				CommitID:       a.CommitID,
				LastAccessTime: a.LastAccessTime,
			},
		}
		res := isContainerExpired(container, toAccessTime(maxPodExpire),
			toAccessTime(functionTTL), toAccessTime(maxPodExpire+functionTTL), logs.NewLogger())
		assert.Equalf(t, a.IsExpired, res, "argument %+v", a)
	}

	// kata container sick pod
	argResults = []struct {
		Status         api.ContainerStatusType
		RequestID      string
		CommitID       string
		LastAccessTime int64
		IsExpired      bool
	}{
		{
			api.ContainerStatusSick,
			"",
			"",
			toAccessTime(functionTTL - 1),
			true,
		},
	}

	for _, a := range argResults {
		container := &api.ContainerInfo{
			Status: a.Status,
			RuntimeInfo: &api.RuntimeInfo{
				RequestID:      a.RequestID,
				CommitID:       a.CommitID,
				LastAccessTime: a.LastAccessTime,
				ServiceType:    api.ServiceTypeCFCKata,
			},
		}
		res := isContainerExpired(container, toAccessTime(maxPodExpire),
			toAccessTime(functionTTL), toAccessTime(maxPodExpire+functionTTL), logs.NewLogger())
		assert.Equalf(t, a.IsExpired, res, "argument %+v", a)
	}

}

func TestGetDaemonSetByRunningMode(t *testing.T) {
	argResults := []struct {
		RunningMode string
		daemonset   int
	}{
		{
			RunningMode: "ote",
			daemonset:   2,
		},
		{
			RunningMode: "cloud",
			daemonset:   3,
		},
	}
	for _, a := range argResults {
		res := getDaemonSetByRunningMode(a.RunningMode)
		assert.Equalf(t, a.daemonset, res, "argument %+v", a)
	}
}

func TestComputeCountByNodeMemory(t *testing.T) {
	cnt, err := computeCountByNodeMemory("1100Mi", 200, 500, 1.2) // (1100-500-1001-100)/1.2 = 333
	assert.EqualValues(t, 333, cnt)
	assert.Nil(t, err)

	cnt, err = computeCountByNodeMemory("4Gi", 600, 1000, 1.6) // (4*1024-500-100-100)/1.6=1560
	assert.EqualValues(t, 1560, cnt)
	assert.Nil(t, err)
}

func TestToAccessTime(t *testing.T) {
	at := toAccessTime(1)
	ret := time.Now().Add(-1*time.Second).UnixNano() / int64(time.Millisecond)
	assert.True(t, (ret-at) < 1000)
}

func TestComputeUsedMemory(t *testing.T) {
	num := 10
	containers := make([]*api.ContainerInfo, num)
	for i := 0; i < num; i++ {
		containers[i] = &api.ContainerInfo{
			Status: api.ContainerStatusWarm,
			ResourceStats: &api.ResourceStats{
				MemoryStats: &api.MemoryStats{
					Limit: 128 * 1024 * 1024,
				},
			},
		}
	}
	mem := ComputeUsedMemory(containers)
	assert.EqualValues(t, 128*num, mem)
}

func TestIsPathDir(t *testing.T) {
	// Dir
	os.MkdirAll("/tmp/testDir", os.ModePerm)
	os.RemoveAll("/tmp/testDir/")

	// test a unexisted file
	testDirFile := "/tmp/testDir/test/testFile"
	isDir := IsPathDir(testDirFile)
	if isDir {
		t.Errorf("test dir not exist")
	}

	testDir := "/tmp/testDir/test"
	os.MkdirAll(testDir, os.ModePerm)
	isDir = IsPathDir(testDir)
	if !isDir {
		t.Errorf("test dir test want be true, but it fail")
	}

	testFile := "/tmp/testDir/test/testfile"
	_, err := os.Create(testFile)
	if err != nil {
		t.Errorf("testfile create failed")
	}

	isDir = IsPathDir(testFile)
	if isDir {
		t.Errorf("testfile is not a dir")
	}
	os.RemoveAll("/tmp/testDir/")
}

func TestIsPathExists(t *testing.T) {
	os.MkdirAll("/tmp/testDir", os.ModePerm)
	os.RemoveAll("/tmp/testDir/")

	testDir := "/tmp/testDir/test"
	isExist := IsPathExists(testDir)
	if isExist {
		t.Errorf("testdir want be false")
	}
	os.MkdirAll(testDir, os.ModePerm)
	isExist = IsPathExists(testDir)
	if !isExist {
		t.Errorf("testdir want be true")
	}

	testFile := "/tmp/testDir/test/testfile"
	isExist = IsPathExists(testFile)
	if isExist {
		t.Errorf("test file not exist, result should be false")
	}

	_, err := os.Create(testFile)
	if err != nil {
		t.Errorf("testfile create failed")
	}
	isExist = IsPathExists(testFile)
	if !isExist {
		t.Errorf("test file exist, result should be true")
	}
}

func TestGenPodDeadline(t *testing.T) {
	podDeadline := genPodDeadline(900, 360)
	assert.Equal(t, podDeadline.functionDeadline, toAccessTime(900))
}

func TestComputeInitDeployPodCount(t *testing.T) {
	flavorList := []api.BccFlavor{
		{
			Cpu:          2,
			Memory:       8,
			SsdDiskSize:  0,
			MaxPodDeploy: 35,
		},
	}
	node := &api.NodeInfo{
		ID:   "node0",
		Name: "node0",
		PodStatus: api.PodStatus{
			PodCount: 10,
		},
		CceClusterUUID:     "cluster1",
		ServiceType:        api.ServiceTypeCFCKata,
		CpuAvailable:       1195,
		OriginalCpuSize:    42,
		MemoryAvailable:    6153,
		OriginalMemorySize: 128,
		PodsNumAvailable:   54,
	}

	cnt := ComputeInitDeployPodCount(node, flavorList)
	// assert.EqualValues(t, 28, cnt)
	assert.EqualValues(t, 35, cnt)

	flavorList = []api.BccFlavor{}

	node = &api.NodeInfo{
		ID:   "node0",
		Name: "node0",
		PodStatus: api.PodStatus{
			PodCount: 10,
		},
		CceClusterUUID:     "cluster1",
		ServiceType:        api.ServiceTypeCFCKata,
		CpuAvailable:       0,
		OriginalCpuSize:    0,
		MemoryAvailable:    0,
		OriginalMemorySize: 1,
		PodsNumAvailable:   0,
	}

	cnt = ComputeInitDeployPodCount(node, flavorList)
	assert.EqualValues(t, 35, cnt)
}

func TestComputeCountByNodeCpu(t *testing.T) {
	cnt, err := computeCountByNodeCpu(resource.MustParse("1900m"), 200, 505, 1.0)
	assert.EqualValues(t, 1195, cnt)
	assert.Nil(t, err)
}

func TestComputeAvailablePodNum(t *testing.T) {
	cnt, err := computeAvailablePodNum(resource.MustParse("64"), 5)
	assert.EqualValues(t, 54, cnt)
	assert.Nil(t, err)
}

func TestGetInstanceType(t *testing.T) {
	ty := GetInstanceType("c3")
	assert.EqualValues(t, "N3", ty)
}
