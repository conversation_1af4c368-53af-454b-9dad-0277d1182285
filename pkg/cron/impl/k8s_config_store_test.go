package impl

import (
	"testing"

	"github.com/golang/mock/gomock"

	"icode.baidu.com/baidu/faas/kun/pkg/api"
	"icode.baidu.com/baidu/faas/kun/pkg/etcd/mock"
	"icode.baidu.com/baidu/faas/kun/pkg/util/logs"
)

func TestListClusterInfo(t *testing.T) {
	mockCtrl := gomock.NewController(t)
	defer mockCtrl.Finish()

	etcdClient := mock.NewMockEtcdInterface(mockCtrl)
	etcdClient.EXPECT().GetAllK8s().Return([]*api.K8sInfo{}, nil)

	s := NewK8sConfigStore(etcdClient, logs.NewLogger())
	s.ListClusterInfo()
}
