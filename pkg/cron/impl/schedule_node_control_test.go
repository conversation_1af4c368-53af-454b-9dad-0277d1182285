package impl

import (
	"testing"
	"time"

	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/assert"
	"icode.baidu.com/baidu/faas/kun/pkg/cron/rpc/mock"

	etcdMock "icode.baidu.com/baidu/faas/kun/pkg/etcd/mock"
	funcletMock "icode.baidu.com/baidu/faas/kun/pkg/funclet/mock"
	storeMock "icode.baidu.com/baidu/faas/kun/pkg/store/mock"

	"icode.baidu.com/baidu/faas/kun/pkg/api"
	"icode.baidu.com/baidu/faas/kun/pkg/util/logs"
)

func TestIsNodeStale(t *testing.T) {
	nodeInfo := &api.NodeInfo{
		StartTime: time.Now().Add(-100 * time.Second),
	}

	b := IsNodeStale(nodeInfo, 10)
	assert.True(t, b)

	b = IsNodeStale(nodeInfo, 200)
	assert.False(t, b)
}

func TestPurgeNode(t *testing.T) {
	mockCtrl := gomock.NewController(t)
	defer mockCtrl.Finish()

	reserveMgr := storeMock.NewMockReserveManager(mockCtrl)
	funclet := funcletMock.NewMockFuncletInterface(mockCtrl)
	etcdClient := etcdMock.NewMockEtcdInterface(mockCtrl)
	baseClusterControl := testNewBaseClusterControl()
	k8sClient := mock.NewMockK8sInterface(mockCtrl)
	baseClusterControl.k8sClient = k8sClient
	globalLog := logs.NewLogger()
	scheduleCtrl := NewScheduleNodeControlByClients(etcdClient, funclet, globalLog, reserveMgr, baseClusterControl, nil)
	testNode := api.NodeInfo{ID: "xxx"}

	// pod is nil
	resp := &api.ListPodsResponse{}
	funclet.EXPECT().ListPods(gomock.Any()).Return(resp, nil)
	err := scheduleCtrl.PurgeNode(&testNode, nil, 20, 300)
	assert.Nil(t, err)

	// mock pod nil
	pod1 := &api.ContainerInfo{
		Hostname: "h1",
		Status:   "warm",
	}
	pod2 := &api.ContainerInfo{
		Hostname: "h2",
		Status:   "cold",
	}

	resp = &api.ListPodsResponse{
		pod1,
		pod2,
	}
	funclet.EXPECT().ListPods(gomock.Any()).Return(resp, nil)
	funclet.EXPECT().CoolDown(gomock.Any()).Return(nil)
	err = scheduleCtrl.PurgeNode(&testNode, nil, 20, 300)
	assert.Nil(t, err)

	runtime := &api.RuntimeInfo{
		RequestID: "nodejs12",
		UserID:    "uid",
	}
	pod1.RuntimeInfo = runtime
	timeout := 10
	pod1.MaxFunctionTimeout = &timeout
	funclet.EXPECT().ListPods(gomock.Any()).Return(resp, nil)
	funclet.EXPECT().CoolDown(gomock.Any()).Return(nil)
	err = scheduleCtrl.PurgeNode(&testNode, nil, 20, 300)
	assert.Nil(t, err)

	labels := map[string]string{"source": "dasou"}
	labels["source"] = "dasou"
	pod1.Labels = labels
	sourcePodExpire := map[string]int{"dasou": 10}
	funclet.EXPECT().ListPods(gomock.Any()).Return(resp, nil)
	funclet.EXPECT().CoolDown(gomock.Any()).Return(nil)
	err = scheduleCtrl.PurgeNode(&testNode, sourcePodExpire, 20, 300)
	assert.Nil(t, err)

}
