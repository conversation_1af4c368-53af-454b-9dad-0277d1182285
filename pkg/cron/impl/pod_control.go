package impl

import (
	"fmt"

	"github.com/google/uuid"
	"k8s.io/apimachinery/pkg/api/resource"

	"icode.baidu.com/baidu/faas/kun/pkg/api"
	"icode.baidu.com/baidu/faas/kun/pkg/funclet/client"
	"icode.baidu.com/baidu/faas/kun/pkg/util/logs"
)

type podControl struct {
	funcletClient client.FuncletInterface
	logger        *logs.Logger
}

func NewPodControl(f client.FuncletInterface, l *logs.Logger) PodControl {
	return &podControl{
		funcletClient: f,
		logger:        l,
	}
}

// ResetNode 通知funclet重置node
func (m *podControl) ResetNode(node *api.NodeInfo) error {
	originalMemoryQuantity, err := resource.ParseQuantity(fmt.Sprintf("%dMi", node.OriginalMemorySize))
	if err != nil {
		return err
	}
	originalMemoryInBytes := originalMemoryQuantity.Value()
	requestID := uuid.New().String()
	m.logger.V(6).With<PERSON>ield("request_id", requestID).Infof("[ResetNode] %s %d", node.FloatingIP, originalMemoryInBytes)
	return m.funcletClient.ResetNode(
		&api.FuncletClientResetNodeInput{
			Host:           node.FloatingIP,
			OriginalMemory: originalMemoryInBytes,
			RequestID:      requestID,
		},
	)
}

// GetHealthyContainersOnNode 获取node上已连接funclet的容器
func (m *podControl) GetHealthyContainersOnNode(node *api.NodeInfo) ([]*api.ContainerInfo, error) {
	return m.getContainersByCriteria(node.FloatingIP, nil)
}

func (m *podControl) getContainersByCriteria(host string, criteria *api.ListPodCriteria) ([]*api.ContainerInfo, error) {
	requestID := uuid.New().String()
	//m.logger.V(6).WithField("request_id", requestID).Infof("[getContainersByCriteria] %s", host)
	resp, err := m.funcletClient.ListPods(&api.FuncletClientListPodsInput{Host: host, Criteria: criteria, RequestID: requestID})

	if err != nil || resp == nil {
		return []*api.ContainerInfo{}, err
	}

	containers := *resp
	return containers, nil
}

func (m *podControl) GetZombieContainersOnNode(node *api.NodeInfo) ([]*api.ContainerInfo, error) {
	requestID := uuid.New().String()
	resp, err := m.funcletClient.ListZombiePods(
		&api.FuncletClientListZombiePodsInput{
			Host:      node.FloatingIP,
			RequestID: requestID},
	)

	if err != nil || resp == nil {
		return []*api.ContainerInfo{}, err
	}

	containers := *resp
	return containers, nil
}

func (m *podControl) CoolDownPod(container *api.ContainerInfo, node *api.NodeInfo) error {
	pod := &api.PodInfo{
		PodName:    container.Hostname,
		IP:         node.FloatingIP,
		MemorySize: node.OriginalMemorySize,
		NodeID:     node.ID,
	}

	requestID := uuid.New().String()
	m.logger.V(6).WithField("request_id", requestID).Infof("[CoolDownPod] cooldown [%s] [%s] [%d]",
		pod.IP, pod.PodName, pod.MemorySize)

	if err := m.funcletClient.CoolDown(
		&api.FuncletClientCoolDownInput{
			Host:      pod.IP,
			Pod:       pod,
			RequestID: requestID,
		}); err != nil {
		m.logger.WithField("request_id", requestID).Errorf("[CoolDownPods] cool down pod %s failed: %s", pod, err.Error())
		return err
	}

	return nil
}

// check and update kata runtime images
func (m *podControl) CheckAndPullImages(node *api.NodeInfo, images []string) (map[string]bool, error) {
	requestID := uuid.New().String()
	resp, err := m.funcletClient.CheckAndPullImages(&api.FuncletCheckAndPullImagesInput{
		Host:      node.FloatingIP,
		RequestID: requestID,
		Images:    images,
	})
	if err != nil {
		m.logger.WithField("request_id", requestID).Errorf("[CheckAndPullImages] ccheck and pull images %s failed: %s", node.FloatingIP, err.Error())
		return nil, err
	}
	return resp.Results, nil
}
