package impl

import (
	"errors"
	"fmt"
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/assert"
	kubeapi "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/api/resource"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/watch"

	"icode.baidu.com/baidu/faas/kun/cmd/cron/app/options"
	"icode.baidu.com/baidu/faas/kun/pkg/api"
	"icode.baidu.com/baidu/faas/kun/pkg/cron/rpc/mock"
)

func TestConvertK8sNode(t *testing.T) {
	k8sNode := &k8sNodeInfo{}
	k8sNode.Name = "node1"
	k8sNode.Spec.ProviderID = "baidubce://ins1"
	k8sNode.Status.Conditions = []kubeapi.NodeCondition{
		{
			Type:   kubeapi.NodeReady,
			Status: kubeapi.ConditionTrue,
		},
	}

	k8sInfo := &api.K8sInfo{
		CceClusterUUID: "cluster1",
		NodeConfig: api.NodeConfig{
			ServiceType:            api.ServiceTypeCFC,
			ClusterLabels:          map[string]string{api.LabelAppID: "app1"},
			MemoryReserved:         500,
			PodMemoryReservedRatio: 1.2,
		},
	}
	nodeInfo := k8sNode.ConvertToNodeInfo(k8sInfo)
	assert.Equal(t, nodeInfo.ID, k8sNode.GenerateID("cluster1"))
	assert.EqualValues(t, k8sInfo.ServiceType, nodeInfo.ServiceType)
	assert.EqualValues(t, k8sInfo.ClusterLabels, nodeInfo.ClusterLabels)
}

func TestGetInstanceShortID(t *testing.T) {
	k8sNode := &k8sNodeInfo{}
	k8sNode.Spec.DoNotUse_ExternalID = "i-2eBXJt03"
	k8sNode.Spec.ProviderID = ""
	assert.Equal(t, "i-2eBXJt03", k8sNode.GetInstanceShortID())

	k8sNode.Spec.DoNotUse_ExternalID = ""
	k8sNode.Spec.ProviderID = "baidubce://i-2eBXJt03"
	assert.Equal(t, "i-2eBXJt03", k8sNode.GetInstanceShortID())

	// 格式错误
	k8sNode.Spec.DoNotUse_ExternalID = ""
	k8sNode.Spec.ProviderID = "baidubce://i-2eBXJt03\n"
	assert.Equal(t, "", k8sNode.GetInstanceShortID())
}

// pass
func TestCheckIfPreparedForInitNormal(t *testing.T) {
	node := &api.NodeInfo{
		ID:   "node0",
		Name: "node0",
		PodStatus: api.PodStatus{
			PodCount: 10,
		},
		CceClusterUUID: "cluster1",
		ServiceType:    api.ServiceTypeCFCKata,
	}

	mockCtrl := gomock.NewController(t)
	defer mockCtrl.Finish()
	opt := &options.CronOptions{
		RunningMode: "cloud",
	}
	mockK8sInfo := api.NewDefaultK8sInfo()

	k8sClient := mock.NewMockK8sInterface(mockCtrl)
	k8sCtrl := &k8sNodeControl{
		&baseClusterControl{
			k8sClient: k8sClient,
			k8sInfo:   mockK8sInfo,
			opt:       opt,
		},
	}

	// 正常
	mockDsContainers := make([]kubeapi.Container, 1, 1)
	dsPods := make([]kubeapi.Pod, 3)
	for i := 0; i < len(dsPods); i++ {
		pod := &dsPods[i]
		pod.Status.Phase = kubeapi.PodRunning
		pod.Spec.Containers = mockDsContainers
	}

	dsPods[0].Spec.Containers = append(mockDsContainers, kubeapi.Container{Name: "invoker", Image: "imag1"})
	dsPods[1].Spec.Containers = append(mockDsContainers, kubeapi.Container{Name: "funclet", Image: "imag2"})
	dsPods[2].Spec.Containers = append(mockDsContainers, kubeapi.Container{Name: "runtimes", Image: "imag3"})

	dsPods[0].Labels = map[string]string{"app": "funclet"}
	dsPods[1].Labels = map[string]string{"app": "invoker"}
	dsPods[2].Labels = map[string]string{"app": "runtimes"}

	k8sClient.EXPECT().GetDaemonSetsOnNode(node.Name).Return(dsPods, nil).AnyTimes()
	k8sClient.EXPECT().GetPodsByLabelAndNode(nil, node.Name).Return([]kubeapi.Pod{}, nil).MaxTimes(2)
	k8sClient.EXPECT().DeletePod(gomock.Any(), gomock.Any()).Return(nil).AnyTimes()

	err := k8sCtrl.checkIfPreparedForInit(node)
	assert.Nil(t, err)

}

func TestCheckIfPreparedForInitError(t *testing.T) {
	node := &api.NodeInfo{
		ID:   "node0",
		Name: "node0",
		PodStatus: api.PodStatus{
			PodCount: 10,
		},
		CceClusterUUID: "cluster1",
		ServiceType:    api.ServiceTypeCFCKata,
	}

	mockCtrl := gomock.NewController(t)
	defer mockCtrl.Finish()
	opt := &options.CronOptions{
		RunningMode: "cloud",
	}
	mockK8sInfo := api.NewDefaultK8sInfo()

	k8sClient := mock.NewMockK8sInterface(mockCtrl)
	k8sCtrl := &k8sNodeControl{
		&baseClusterControl{
			k8sClient: k8sClient,
			k8sInfo:   mockK8sInfo,
			opt:       opt,
		},
	}

	// 正常
	var mockContainers []kubeapi.Container
	pods := make([]kubeapi.Pod, node.PodCount)
	for i := 0; i < len(pods); i++ {
		pod := &pods[i]
		pod.Status.Phase = kubeapi.PodRunning
		pod.Spec.Containers = mockContainers
	}
	mockDsContainers := make([]kubeapi.Container, 1, 1)
	dsPods := make([]kubeapi.Pod, 3)
	for i := 0; i < len(dsPods); i++ {
		pod := &dsPods[i]
		pod.Status.Phase = kubeapi.PodRunning
		pod.Spec.Containers = mockDsContainers
	}

	dsPods[0].Spec.Containers = append(mockDsContainers, kubeapi.Container{Name: "invoker", Image: "imag1"})
	dsPods[1].Spec.Containers = append(mockDsContainers, kubeapi.Container{Name: "funclet", Image: "imag2"})
	dsPods[2].Spec.Containers = append(mockDsContainers, kubeapi.Container{Name: "runtimes", Image: "imag3"})

	dsPods[0].Labels = map[string]string{"app": "funclet"}
	dsPods[1].Labels = map[string]string{"app": "invoker"}
	dsPods[2].Labels = map[string]string{"app": "runtimes"}

	k8sClient.EXPECT().GetDaemonSetsOnNode(node.Name).Return(dsPods, nil).AnyTimes()
	k8sClient.EXPECT().GetPodsByLabelAndNode(nil, node.Name).Return(pods, nil).AnyTimes()
	k8sClient.EXPECT().DeletePod(gomock.Any(), gomock.Any()).Return(nil).AnyTimes()

	err := k8sCtrl.checkIfPreparedForInit(node)
	assert.NotNil(t, err)
}

func TestCheckIfPreparedForInitError2(t *testing.T) {
	node := &api.NodeInfo{
		ID:   "node0",
		Name: "node0",
		PodStatus: api.PodStatus{
			PodCount: 10,
		},
		CceClusterUUID: "cluster1",
		ServiceType:    api.ServiceTypeCFCKata,
	}

	mockCtrl := gomock.NewController(t)
	defer mockCtrl.Finish()
	opt := &options.CronOptions{
		RunningMode: "cloud",
	}
	mockK8sInfo := api.NewDefaultK8sInfo()

	k8sClient := mock.NewMockK8sInterface(mockCtrl)
	k8sCtrl := &k8sNodeControl{
		&baseClusterControl{
			k8sClient: k8sClient,
			k8sInfo:   mockK8sInfo,
			opt:       opt,
		},
	}

	// 正常
	var mockContainers []kubeapi.Container
	pods := make([]kubeapi.Pod, node.PodCount)
	for i := 0; i < len(pods); i++ {
		pod := &pods[i]
		pod.Status.Phase = kubeapi.PodRunning
		pod.Spec.Containers = mockContainers
	}
	mockDsContainers := make([]kubeapi.Container, 1, 1)
	dsPods := make([]kubeapi.Pod, 3)
	for i := 0; i < len(dsPods); i++ {
		pod := &dsPods[i]
		pod.Status.Phase = kubeapi.PodRunning
		pod.Spec.Containers = mockDsContainers
	}

	dsPods[0].Spec.Containers = append(mockDsContainers, kubeapi.Container{Name: "invoker", Image: "imag1"})
	dsPods[1].Spec.Containers = append(mockDsContainers, kubeapi.Container{Name: "funclet", Image: "imag2"})
	dsPods[2].Spec.Containers = append(mockDsContainers, kubeapi.Container{Name: "runtimes", Image: "imag3"})

	dsPods[0].Labels = map[string]string{"app": "funclet"}
	dsPods[1].Labels = map[string]string{"app": "invoker"}
	dsPods[2].Labels = map[string]string{"app": "runtimes"}

	k8sClient.EXPECT().GetDaemonSetsOnNode(node.Name).Return(dsPods, errors.New("fake error"))
	err := k8sCtrl.checkIfPreparedForInit(node)
	assert.NotNil(t, err)

	dsPods[0].Status.Phase = kubeapi.PodPending
	k8sClient.EXPECT().GetDaemonSetsOnNode(node.Name).Return(dsPods, nil)
	err = k8sCtrl.checkIfPreparedForInit(node)
	assert.NotNil(t, err)

	dsPods[0].Status.Phase = kubeapi.PodRunning
	dsPods = dsPods[:1]
	k8sClient.EXPECT().GetDaemonSetsOnNode(node.Name).Return(dsPods, nil)
	err = k8sCtrl.checkIfPreparedForInit(node)
	assert.NotNil(t, err)

}

func TestCheckIfAllPodsRunningAndRunnerCorrect(t *testing.T) {
	node := &api.NodeInfo{
		ID:   "node0",
		Name: "node0",
		PodStatus: api.PodStatus{
			PodCount:         10,
			ExpectedPodCount: 10,
		},
		CceClusterUUID: "cluster1",
	}

	mockCtrl := gomock.NewController(t)
	defer mockCtrl.Finish()
	opt := &options.CronOptions{
		RunningMode: "cloud",
	}
	mockK8sInfo := api.NewDefaultK8sInfo()

	k8sClient := mock.NewMockK8sInterface(mockCtrl)
	k8sCtrl := &k8sNodeControl{
		&baseClusterControl{
			k8sClient: k8sClient,
			k8sInfo:   mockK8sInfo,
			opt:       opt,
		},
	}

	// 正常
	var mockContainers []kubeapi.Container
	mockContainers = make([]kubeapi.Container, 1, 1)
	pods := make([]kubeapi.Pod, node.ExpectedPodCount)
	for i := 0; i < len(pods); i++ {
		pod := &pods[i]
		pod.Status.Phase = kubeapi.PodRunning
		pod.Spec.Containers = mockContainers
	}
	mockDsContainers := make([]kubeapi.Container, 1, 1)
	dsPods := make([]kubeapi.Pod, 3)
	for i := 0; i < len(dsPods); i++ {
		pod := &dsPods[i]
		pod.Status.Phase = kubeapi.PodRunning
		pod.Spec.Containers = mockDsContainers
	}

	dsPods[0].Spec.Containers = append(mockDsContainers, kubeapi.Container{Name: "invoker", Image: "imag1"})
	dsPods[1].Spec.Containers = append(mockDsContainers, kubeapi.Container{Name: "funclet", Image: "imag2"})
	dsPods[2].Spec.Containers = append(mockDsContainers, kubeapi.Container{Name: "runtimes", Image: "imag3"})

	dsPods[0].Labels = map[string]string{"app": "funclet"}
	dsPods[1].Labels = map[string]string{"app": "invoker"}
	dsPods[2].Labels = map[string]string{"app": "runtimes"}

	k8sClient.EXPECT().GetDaemonSetsOnNode(node.Name).Return(dsPods, nil).AnyTimes()
	k8sClient.EXPECT().GetPodsByLabelAndNode(nil, node.Name).Return(pods, nil)

	err := k8sCtrl.checkIfAllPodsRunningAndPodImageCorrect(node)
	assert.Nil(t, err)

	// pod状态不对
	pods[0].Status.Phase = kubeapi.PodPending
	k8sClient.EXPECT().GetPodsByLabelAndNode(nil, node.Name).Return(pods, nil)
	err = k8sCtrl.checkIfAllPodsRunningAndPodImageCorrect(node)
	assert.NotNil(t, err)

	// pod数量过少
	pods = make([]kubeapi.Pod, node.PodCount-1)
	for i := 0; i < len(pods); i++ {
		pod := &pods[i]
		pod.Status.Phase = kubeapi.PodRunning
		pod.Spec.Containers = mockContainers
	}
	k8sClient.EXPECT().GetPodsByLabelAndNode(nil, node.Name).Return(pods, nil)
	err = k8sCtrl.checkIfAllPodsRunningAndPodImageCorrect(node)
	assert.NotNil(t, err)

	// pod的container与k8s container image一致
	mockK8sInfo.ContainerImage = "imag5"
	pods = make([]kubeapi.Pod, node.PodCount)
	for i := 0; i < len(pods); i++ {
		pod := &pods[i]
		pod.Status.Phase = kubeapi.PodRunning
		pod.Spec.Containers = make([]kubeapi.Container, 1, 1)
		pods[i].Spec.Containers[0].Name = "pmpod-" + fmt.Sprintf("%d", i)
		pods[i].Spec.Containers[0].Image = "imag5"
	}

	pods[0].Status.Phase = kubeapi.PodRunning
	k8sClient.EXPECT().GetDaemonSetsOnNode(node.Name).Return(dsPods, nil).AnyTimes()
	k8sClient.EXPECT().GetPodsByLabelAndNode(nil, node.Name).Return(pods, nil)
	err = k8sCtrl.checkIfAllPodsRunningAndPodImageCorrect(node)
	assert.Nil(t, err)
	// pod的container与k8s container image不一致
	pods[0].Spec.Containers[0].Image = "image2"
	k8sClient.EXPECT().GetPodsByLabelAndNode(nil, node.Name).Return(pods, nil)
	k8sClient.EXPECT().CreatePod(pods[0].Name, node.Name, node.ServiceType, node.OriginalCpuSize, node.OriginalMemorySize).Return(nil).AnyTimes()
	k8sClient.EXPECT().DeletePod(pods[0].Name, true).Return(nil).AnyTimes()
	err = k8sCtrl.checkIfAllPodsRunningAndPodImageCorrect(node)
	assert.NotNil(t, err)

}

func TestCheckIfRunningPodOverRatio(t *testing.T) {
	node := &api.NodeInfo{
		ID:   "node0",
		Name: "node0",
		PodStatus: api.PodStatus{
			PodCount:         10,
			ExpectedPodCount: 10,
		},
		CceClusterUUID: "cluster1",
		Flavor: api.BccFlavor{
			RunningPodRatio: 0.2,
		},
	}

	mockCtrl := gomock.NewController(t)
	defer mockCtrl.Finish()
	opt := &options.CronOptions{
		RunningMode: "cloud",
	}
	mockK8sInfo := api.NewDefaultK8sInfo()

	k8sClient := mock.NewMockK8sInterface(mockCtrl)
	k8sCtrl := &k8sNodeControl{
		&baseClusterControl{
			k8sClient: k8sClient,
			k8sInfo:   mockK8sInfo,
			opt:       opt,
		},
	}

	var mockContainers []kubeapi.Container
	mockContainers = make([]kubeapi.Container, 1, 1)
	pods := make([]kubeapi.Pod, node.ExpectedPodCount)
	//超过阈值
	runningNodeCount := int(node.Flavor.RunningPodRatio * float64(node.ExpectedPodCount))
	for i := 0; i < (len(pods)); i++ {
		pod := &pods[i]
		if i < runningNodeCount {
			pod.Status.Phase = kubeapi.PodRunning
		} else {
			pod.Status.Phase = kubeapi.PodPending
		}
		pod.Spec.Containers = mockContainers
	}
	k8sClient.EXPECT().CleanNamespacePendingPods(node.Name, api.K8sPoolmgrNamespace).Return(nil).Times(1)
	isOverRatio, err := k8sCtrl.checkIfRunningPodOverRatio(node, 5)
	assert.True(t, isOverRatio)
	assert.Nil(t, err)
	node.PodCount = 10
	//超过阈值,CleanPendingPods出错
	pods = make([]kubeapi.Pod, node.ExpectedPodCount)
	runningNodeCount = int(node.Flavor.RunningPodRatio * float64(node.ExpectedPodCount))
	for i := 0; i < (len(pods)); i++ {
		pod := &pods[i]
		if i < runningNodeCount {
			pod.Status.Phase = kubeapi.PodRunning
		} else {
			pod.Status.Phase = kubeapi.PodPending
		}
		pod.Spec.Containers = mockContainers
	}
	k8sClient.EXPECT().CleanNamespacePendingPods(node.Name, api.K8sPoolmgrNamespace).Return(errors.New("cleanNamespacePendingPods failed")).Times(1)
	isOverRatio, err = k8sCtrl.checkIfRunningPodOverRatio(node, 2)
	assert.False(t, isOverRatio)
	assert.NotNil(t, err)
	node.PodCount = 10
	//未超过阈值
	pods = make([]kubeapi.Pod, node.ExpectedPodCount)
	runningNodeCount = int(node.Flavor.RunningPodRatio * float64(node.ExpectedPodCount))
	for i := 0; i < (len(pods)); i++ {
		pod := &pods[i]
		pod.Status.Phase = kubeapi.PodPending
		pod.Spec.Containers = mockContainers
	}
	isOverRatio, err = k8sCtrl.checkIfRunningPodOverRatio(node, 1)
	assert.False(t, isOverRatio)
	assert.Nil(t, err)
}

func TestK8sNodeControl_RebuildK8sPod(t *testing.T) {
	node := &api.NodeInfo{
		ID:                 "node0",
		Name:               "node0",
		OriginalMemorySize: 128,
		PodStatus: api.PodStatus{
			PodCount: 10,
		},
		CceClusterUUID: "cluster1",
	}

	mockCtrl := gomock.NewController(t)
	defer mockCtrl.Finish()
	opt := &options.CronOptions{
		RunningMode: "cloud",
		TaskSchedulerOptions: &options.TaskSchedulerOptions{
			TaskCloudSchedulerOptions: &options.TaskCloudSchedulerOptions{
				InitNodeTimeout: 60,
			},
		},
	}
	mockK8sInfo := api.NewDefaultK8sInfo()
	mockK8sInfo.ServiceType = api.ServiceTypeCFCKata
	k8sClient := mock.NewMockK8sInterface(mockCtrl)
	k8sCtrl := &k8sNodeControl{
		&baseClusterControl{
			k8sClient: k8sClient,
			k8sInfo:   mockK8sInfo,
			opt:       opt,
		},
	}

	// testCase 1
	podName := "test-cfc-kata-pod1"
	k8sClient.EXPECT().CreatePod(gomock.Any(), node.Name, node.ServiceType, node.OriginalCpuSize, node.OriginalMemorySize).Return(nil).AnyTimes()
	k8sClient.EXPECT().DeletePod(podName, true).Return(nil).AnyTimes()
	mockEvents := []watch.Event{newPodEvent(watch.Added, podName, kubeapi.PodRunning, "")}
	eventCh := make(chan watch.Event, 1)
	for _, e := range mockEvents {
		eventCh <- e
	}
	close(eventCh)

	k8sClient.EXPECT().WatchPod(gomock.Any(), gomock.Any(), gomock.Any()).Return(eventCh, nil)
	_, err := k8sCtrl.RebuildPod(node, podName)
	assert.Nil(t, err)

	// testCase2
	podName = "test-cfc-kata-pod2"
	k8sClient.EXPECT().CreatePod(podName, node.Name, node.ServiceType, node.OriginalCpuSize, node.OriginalMemorySize).Return(nil).AnyTimes()
	k8sClient.EXPECT().DeletePod(podName, true).Return(nil).AnyTimes()
	mockEvents = []watch.Event{newPodEvent(watch.Added, podName, kubeapi.PodFailed, "pod failed")}
	eventCh = make(chan watch.Event, 1)
	for _, e := range mockEvents {
		eventCh <- e
	}
	close(eventCh)

	k8sClient.EXPECT().WatchPod(gomock.Any(), gomock.Any(), gomock.Any()).Return(eventCh, nil).AnyTimes()
	_, err = k8sCtrl.RebuildPod(node, podName)
	assert.NotNil(t, err)

	// testCase3
	podName = "test-cfc-kata-pod3"
	k8sClient.EXPECT().CreatePod(podName, node.Name, node.ServiceType, node.OriginalCpuSize, node.OriginalMemorySize).Return(nil).AnyTimes()
	k8sClient.EXPECT().DeletePod(podName, true).Return(fmt.Errorf("delete pod failed")).AnyTimes()
	mockEvents = []watch.Event{newPodEvent(watch.Added, podName, kubeapi.PodFailed, "pod failed")}
	eventCh = make(chan watch.Event, 1)
	for _, e := range mockEvents {
		eventCh <- e
	}
	close(eventCh)

	k8sClient.EXPECT().WatchPod(podName, gomock.Any(), gomock.Any()).Return(eventCh, nil).AnyTimes()
	_, err = k8sCtrl.RebuildPod(node, podName)
	assert.NotNil(t, err)

	// testCase4
	podName = "test-cfc-kata-pod4"
	k8sClient.EXPECT().CreatePod(podName, node.Name, node.ServiceType, node.OriginalCpuSize, node.OriginalMemorySize).Return(nil).AnyTimes()
	k8sClient.EXPECT().DeletePod(podName, true).Return(fmt.Errorf("delete pod failed")).AnyTimes()
	mockEvents = []watch.Event{newPodEvent(watch.Added, podName, kubeapi.PodFailed, "pod failed")}
	eventCh = make(chan watch.Event, 1)
	for _, e := range mockEvents {
		eventCh <- e
	}
	close(eventCh)

	k8sClient.EXPECT().WatchPod(podName, gomock.Any(), gomock.Any()).Return(eventCh, fmt.Errorf("watch failed")).AnyTimes()
	_, err = k8sCtrl.RebuildPod(node, podName)
	assert.NotNil(t, err)

}

func TestUpdateNodeMemoryAndCpuAllocatable(t *testing.T) {
	k8sNode := &kubeapi.Node{}
	k8sNode.Name = "node1"
	k8sNode.Spec.ProviderID = "baidubce://ins1"
	k8sNode.Status.Conditions = []kubeapi.NodeCondition{
		{
			Type:   kubeapi.NodeReady,
			Status: kubeapi.ConditionTrue,
		},
	}
	k8sNode.Status.Allocatable = kubeapi.ResourceList{"cpu": resource.MustParse("1900m")}

	node := &api.NodeInfo{
		ID:                 "node0",
		Name:               "node0",
		OriginalMemorySize: 128,
		PodStatus: api.PodStatus{
			PodCount: 10,
		},
		CceClusterUUID: "cluster1",
		State:          api.NodeStateK8sRaw,
	}

	mockCtrl := gomock.NewController(t)
	defer mockCtrl.Finish()
	opt := &options.CronOptions{
		RunningMode: "cloud",
		TaskSchedulerOptions: &options.TaskSchedulerOptions{
			TaskCloudSchedulerOptions: &options.TaskCloudSchedulerOptions{
				InitNodeTimeout: 60,
			},
		},
	}
	mockK8sInfo := api.NewDefaultK8sInfo()
	mockK8sInfo.ServiceType = api.ServiceTypeCFCKata
	k8sClient := mock.NewMockK8sInterface(mockCtrl)
	k8sCtrl := &k8sNodeControl{
		&baseClusterControl{
			k8sClient: k8sClient,
			k8sInfo:   mockK8sInfo,
			opt:       opt,
		},
	}
	// 正常
	mockDsContainers := make([]kubeapi.Container, 1, 1)
	sysPods := make([]kubeapi.Pod, 3)
	for i := 0; i < len(sysPods); i++ {
		pod := &sysPods[i]
		pod.Status.Phase = kubeapi.PodRunning
		pod.Spec.Containers = mockDsContainers
	}
	sysPods[0].Spec.Containers = append(mockDsContainers, kubeapi.Container{Name: "kubedns", Image: "imag1"})
	sysPods[1].Spec.Containers = append(mockDsContainers, kubeapi.Container{Name: "kube-proxy-npmj9", Image: "imag2"})
	sysPods[2].Spec.Containers = append(mockDsContainers, kubeapi.Container{Name: "cce-ip-masq-agent-brpz8", Image: "imag3"})

	k8sClient.EXPECT().GetPodsByNamespaceAndNode("kube-system", node.Name).Return(sysPods, nil).AnyTimes()
	k8sClient.EXPECT().GetPodsByNamespaceAndNode("logging", node.Name).Return(sysPods, nil).AnyTimes()
	k8sClient.EXPECT().GetPodsByAllNamespacesAndByNode(node.Name).Return(sysPods, nil).AnyTimes()

	err := k8sCtrl.updateNodeMemoryAndCpuAllocatable(node, k8sNode)
	assert.Nil(t, err)

	//状态不是K8S_raw
	node.State = api.NodeStateK8sInitializing
	err = k8sCtrl.updateNodeMemoryAndCpuAllocatable(node, k8sNode)
	assert.NotNil(t, err)

}

func TestSetOriginalPodRequests(t *testing.T) {

	node := &api.NodeInfo{
		ID:                 "node0",
		Name:               "node0",
		OriginalMemorySize: 128,
		OriginalCpuSize:    42,
		PodStatus: api.PodStatus{
			PodCount: 10,
		},
		CceClusterUUID: "cluster1",
		State:          api.NodeStateK8sRaw,
	}

	mockCtrl := gomock.NewController(t)
	defer mockCtrl.Finish()
	opt := &options.CronOptions{
		RunningMode: "cloud",
		TaskSchedulerOptions: &options.TaskSchedulerOptions{
			TaskCloudSchedulerOptions: &options.TaskCloudSchedulerOptions{
				InitNodeTimeout: 60,
			},
		},
	}
	mockK8sInfo := api.NewDefaultK8sInfo()
	mockK8sInfo.ServiceType = api.ServiceTypeCFCKata
	k8sClient := mock.NewMockK8sInterface(mockCtrl)
	k8sCtrl := &k8sNodeControl{
		&baseClusterControl{
			k8sClient: k8sClient,
			k8sInfo:   mockK8sInfo,
			opt:       opt,
		},
	}

	k8sClient.EXPECT().ComputeCpuAndMenLimitForPod(int64(42), int64(128)).Return(
		resource.MustParse("42m"), resource.MustParse("840m"),
		resource.MustParse("128M"), resource.MustParse("128M"), nil).AnyTimes()

	err := k8sCtrl.setOriginalPodRequests(node, 128)
	assert.Nil(t, err)
}

// new pod event
func newPodEvent(eventtype watch.EventType, name string, phase kubeapi.PodPhase, message string) watch.Event {
	return watch.Event{
		Type:   eventtype,
		Object: newPod(name, phase, message),
	}
}

// new pod
func newPod(name string, phase kubeapi.PodPhase, message string) *kubeapi.Pod {
	return &kubeapi.Pod{
		ObjectMeta: metav1.ObjectMeta{
			Namespace: metav1.NamespaceDefault,
			Name:      name,
		},
		Status: kubeapi.PodStatus{
			Phase:   phase,
			Message: message,
		},
	}
}
