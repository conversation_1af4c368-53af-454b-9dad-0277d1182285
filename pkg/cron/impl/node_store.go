package impl

import (
	"icode.baidu.com/baidu/faas/kun/pkg/etcd"
	"icode.baidu.com/baidu/faas/kun/pkg/util/logs"
)

type nodeStore struct {
	etcd.NodeInterface // 暂时继承，未来将实现转移进来
	logger             *logs.Logger
}

var _ NodeStore = &nodeStore{}

func NewNodeStore(etcdClient etcd.NodeInterface, logger *logs.Logger) NodeStore {
	return &nodeStore{
		NodeInterface: etcdClient,
		logger:        logger,
	}
}
