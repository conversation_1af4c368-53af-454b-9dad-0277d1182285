package impl

import (
	"fmt"
	"strconv"
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/assert"
	k8sapi "k8s.io/api/core/v1"
	kubeapi "k8s.io/api/core/v1"

	"icode.baidu.com/baidu/faas/kun/cmd/cron/app/options"
	"icode.baidu.com/baidu/faas/kun/pkg/api"
	"icode.baidu.com/baidu/faas/kun/pkg/cron/rpc"
	"icode.baidu.com/baidu/faas/kun/pkg/cron/rpc/mock"
	funcletMock "icode.baidu.com/baidu/faas/kun/pkg/funclet/mock"
	"icode.baidu.com/baidu/faas/kun/pkg/util/logs"
)

func testNewBaseClusterControl() *baseClusterControl {
	return &baseClusterControl{
		k8sInfo: api.NewDefaultK8sInfo(),
		logger:  logs.NewLogger(),
		opt:     options.NewCronOptions(),
	}
}

func TestNewBaseClusterControl(t *testing.T) {
	mockCtrl := gomock.NewController(t)
	defer mockCtrl.Finish()

	k8sInfo := api.NewDefaultK8sInfo()
	k8sInfo.K8sOptions = api.K8sOptions{
		Endpoint:           "*******",
		Version:            "v1",
		Namespace:          "croncfc",
		DaemonSetNamespace: "cfc",
		ContainerImage:     "hub.baidubce.com/cfc/runner:70db697",
		ImagePullPolicy:    "IfNotPresent",
		ImagePullSecrets:   []string{"regsecret"},
		VolumeHostPath:     "/var/faas/invoker/run",
		VolumeMountPath:    "/var/run/faas",
		RunAsUser:          0,
		ClusterCA:          fmt.Sprintf("apiVersion: v1\nclusters:\n- cluster:\n    certificate-authority-data: LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1JSURwRENDQW95Z0F3SUJBZ0lVZEFsdG9XRG9jTDU5NVU5Rmw4aFNnakovQWY4d0RRWUpLb1pJaHZjTkFRRUwKQlFBd2FqRUxNQWtHQTFVRUJoTUNRMDR4RURBT0JnTlZCQWdUQjBKbGFVcHBibWN4RURBT0JnTlZCQWNUQjBKbAphVXBwYm1jeEREQUtCZ05WQkFvVEEyczRjekVVTUJJR0ExVUVDeE1MWTJ4dmRXUnVZWFJwZG1VeEV6QVJCZ05WCkJBTVRDbXQxWW1WeWJtVjBaWE13SGhjTk1Ua3dNekU0TVRFMU9EQXdXaGNOTWpRd016RTJNVEUxT0RBd1dqQnEKTVFzd0NRWURWUVFHRXdKRFRqRVFNQTRHQTFVRUNCTUhRbVZwU21sdVp6RVFNQTRHQTFVRUJ4TUhRbVZwU21sdQpaekVNTUFvR0ExVUVDaE1EYXpoek1SUXdFZ1lEVlFRTEV3dGpiRzkxWkc1aGRHbDJaVEVUTUJFR0ExVUVBeE1LCmEzVmlaWEp1WlhSbGN6Q0NBU0l3RFFZSktvWklodmNOQVFFQkJRQURnZ0VQQURDQ0FRb0NnZ0VCQU1RajNsdzkKeE93QWpneUVCTEM4ZjFCYXpld29UNmNVYUVhdkZkQnBTcGt2V1JhZXh6OC9LZXdGRFI1VEZSNW9UbGEyNGIrdApRa1hiaHRadjIzOXhTMk9kY2NkQU1JcE5GSVJ6NlBodlpwdDM3T00rRFNBQ1YzWDgyODdyM24yanpuaEtWRGdMCm02MUhDNDhhT004RTJKd3loWG5VaUFFNWg1WlpCRG5xNWxHZzNGYmV2bldlTHduM2ZpRXdzRmtvRWdsRXhka1cKd3RLdTRXcGNVeFZocVpXckE1Mm5ONTlhd0U0VzZpVFl1Q05lcWNFS0lqejFwZERmczZmYXZOSXY5M3NGcHNGaQp4UGpSQmlXMDIyUW9SUGNMU1lmaEFNbzlvUmFNVmR3L0tLSlBybTliaWtRNlZMRnVEbmcvbGRIY0d1emJTMDF3CkF6TGJrd1VPb3E3dVNta0NBd0VBQWFOQ01FQXdEZ1lEVlIwUEFRSC9CQVFEQWdFR01BOEdBMVVkRXdFQi93UUYKTUFNQkFmOHdIUVlEVlIwT0JCWUVGRWdEazd4U1lKbFVSMWV2UVY3RUhPZ0JGQjdDTUEwR0NTcUdTSWIzRFFFQgpDd1VBQTRJQkFRQjJoUThJZEljOTN3RFBFMEpYN3pIVGE3NVpQUGpTQUtDajVmWEh3eGxPTVV5TFJnNXJCNUkzCmF4aFE4dDE1ZTkrUkJKUStHTnlsaUVPRGRWWERXK0N1a0tqVlJldW9BZFhKNVYxdHFXazJhOGpKTTBkakVrK3cKN05PZmNIbCtoemNqN0ZtdTRYTW1RZ2dDd3JjTmJFd0laUlhhZ2ZxYnVwM1NTa2RVUnNHZml6Y3oyZmphSGJRSgpiamN3NVNzUjhDUGpZYW5wZmU5WDVPdTEyamhldDJSUVRVYlorNDVESXlId00xS2l4MFBFYU5hR1ZRUmF3Zi9oCkdyakRHWjcxVTRhbC9QQkNmUEdiakltbDhOYUErb0VMd3gyZ3B3cGFoL1Bmdmg4RUZhSXJtMCttOW56WEx6Sk4KWUZpbnBLTE8zMGJPdFU3cDd6aFJlWG0yZXloVXpqUGkKLS0tLS1FTkQgQ0VSVElGSUNBVEUtLS0tLQo=\n    server: %s\n  name: kubernetes\ncontexts:\n- context:\n    cluster: kubernetes\n    user: kubernetes-admin\n  name: kubernetes-admin@kubernetes\ncurrent-context: kubernetes-admin@kubernetes\nkind: Config\npreferences: {}\nusers:\n- name: kubernetes-admin\n  user:\n    client-certificate-data: 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\n    client-key-data: ****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************", "*******"),
		SpecificResource: &api.SpecificResource{
			Requests: map[string]string{
				"cpu":    "500m",
				"memory": "512Mi",
			},
			Limits: map[string]string{
				"cpu":    "1",
				"memory": "1024Mi",
			},
		},
	}

	funcletClient := funcletMock.NewMockFuncletInterface(mockCtrl)
	NewBaseClusterControl(logs.NewLogger(), options.NewCronOptions(), k8sInfo, funcletClient)
}

func TestGetK8sNodeMap(t *testing.T) {
	mockCtrl := gomock.NewController(t)
	defer mockCtrl.Finish()

	baseClusterControl := testNewBaseClusterControl()
	k8sClient := mock.NewMockK8sInterface(mockCtrl)
	baseClusterControl.k8sClient = k8sClient
	baseClusterControl.k8sInfo.MatchLabels = map[string]string{"k": "v"}

	selector := rpc.NewLabelSelector()
	selector.Add("k", "v")

	nodeCnt := 10
	sysPods := make([]kubeapi.Pod, 3)
	mockDsContainers := make([]kubeapi.Container, 1, 1)
	for i := 0; i < len(sysPods); i++ {
		pod := &sysPods[i]
		pod.Status.Phase = kubeapi.PodRunning
		pod.Spec.Containers = mockDsContainers
	}
	sysPods[0].Spec.Containers = append(mockDsContainers, kubeapi.Container{Name: "kubedns", Image: "imag1"})
	sysPods[1].Spec.Containers = append(mockDsContainers, kubeapi.Container{Name: "kube-proxy-npmj9", Image: "imag2"})
	sysPods[2].Spec.Containers = append(mockDsContainers, kubeapi.Container{Name: "cce-ip-masq-agent-brpz8", Image: "imag3"})
	nodes := make([]k8sapi.Node, nodeCnt)
	for i := 0; i < nodeCnt; i++ {
		node := &nodes[i]
		id := strconv.Itoa(i)
		node.Name = "node" + id
		node.Status.Conditions = append(node.Status.Conditions, k8sapi.NodeCondition{Type: k8sapi.NodeReady, Status: k8sapi.ConditionTrue})
		k8sClient.EXPECT().GetPodsByNamespaceAndNode("kube-system", node.Name).Return(sysPods, nil).AnyTimes()
		k8sClient.EXPECT().GetPodsByNamespaceAndNode("logging", node.Name).Return(sysPods, nil).AnyTimes()
	}
	k8sClient.EXPECT().GetNodesByLabel(selector).Return(nodes, nil)
	nodeMap, unavailableCount, err := baseClusterControl.getK8sNodeMap()
	assert.Nil(t, err)
	assert.EqualValues(t, 0, unavailableCount)
	assert.Equal(t, nodeCnt, len(nodeMap))
}
