package impl

import (
	"errors"
	"fmt"
	"math"
	"sort"

	"icode.baidu.com/baidu/faas/kun/pkg/api"
	"icode.baidu.com/baidu/faas/kun/pkg/bce/bcc"
	"icode.baidu.com/baidu/faas/kun/pkg/cron/rpc"
	"icode.baidu.com/baidu/faas/kun/pkg/util/logs"
)

type cceNodeControl struct {
	*baseClusterControl
	cceClient rpc.CceInterface
	bccClient bcc.BccInterface
	ccrClient rpc.CcrInterface
}

var _ ClusterControl = &cceNodeControl{}

var EXSIT = 1

type UnavailableSpecSet map[string]int                      //key: instanceType.c4m8 value :无意义  每个zone SpecSet 记录不可用的spec信息
type ZonesUnavailableSpecMap map[string]*UnavailableSpecSet //key: zone , value :SpecSet  记录所有可用区的不可用规格信息

func NewCceNodeControl(c *baseClusterControl) *cceNodeControl {
	ctrl := &cceNodeControl{
		baseClusterControl: c,
	}
	if c.k8sInfo.BceOptions == nil { // 只有cce配置存在时才会初始化
		panic("cce config is nil")
	}
	ctrl.cceClient = rpc.NewCceClient(c.k8sInfo.BceOptions)
	ctrl.bccClient = bcc.NewBccClient(c.k8sInfo.BceOptions)
	ctrl.ccrClient = rpc.NewCcrClient(c.k8sInfo.BceOptions)
	return ctrl
}

func (m *cceNodeControl) precheckAvailabilityOfFlavors(clusterID string) (zonesUnavailableSpecMap *ZonesUnavailableSpecMap, err error) {

	//避免请求cce失败后zonesUnavailableSpecMap为nil 导致后续步骤出错
	zonesUnavailableSpecMap = &ZonesUnavailableSpecMap{}

	nodes, err := m.cceClient.GetCreatedFailedNodes(clusterID)

	if err != nil {
		return
	}

	transToV2Type := func(instanceType string) string {
		switch instanceType {
		case "0":
			return "N1"
		case "7":
			return "N2"
		case "10":
			return "N3"
		case "13":
			return "N5"
		default:
			return "N4"
		}
	}

	for _, node := range nodes {

		spec := fmt.Sprintf("%s.c%dm%d", transToV2Type(node.InstanceType), node.Cpu, node.Memory)
		if _, exists := (*zonesUnavailableSpecMap)[node.Zone]; !exists {
			(*zonesUnavailableSpecMap)[node.Zone] = &UnavailableSpecSet{}
		}
		specSet := (*zonesUnavailableSpecMap)[node.Zone]
		(*specSet)[spec] = EXSIT
	}
	return
}

func (m *cceNodeControl) ScalingUp(number int) error {
	if !m.cceClient.IsClusterRunning(m.k8sInfo.CceClusterUUID) {
		return &ClusterNotRunningError{m.k8sInfo.CceClusterUUID}
	}

	// 使用当前集群的zone和subnet配置来扩容
	zoneSubnetMap := m.k8sInfo.ScalingUpOptions.ZoneConfigMap
	if zoneSubnetMap == nil || len(zoneSubnetMap) == 0 {
		return errors.New("empty zone and subnet in k8s config")
	}

	zonesUnavailableSpecMap, err := m.precheckAvailabilityOfFlavors(m.k8sInfo.CceClusterUUID)
	if err != nil {
		m.logger.Warnf("[ScalingUp]: precheck availability of flavaors failed: %s", err.Error())
	}

	// 由于无法判断zone是否有资源，只能尝试对每个zone扩容，直至成功
	scalingUpOptions := m.k8sInfo.ScalingUpOptions

	// 实时获取bcc image id，如果获取失败使用etcd中配置好的image id
	bccImageID, err := m.bccClient.GetBccImageLongID(scalingUpOptions.OsType, scalingUpOptions.OsName, scalingUpOptions.OsVersion, scalingUpOptions.OsArch)
	if err != nil || bccImageID == "" {
		m.logger.Errorf("[ScalingUp]: get bcc imageID error %s", err.Error())
		bccImageID = scalingUpOptions.BccImageID
	}

	m.logger.Infof("[ScalingUp]: bcc image id is %s", bccImageID)

	// 按照价格从小到大排序，优先创建便宜的虚机
	sort.SliceStable(scalingUpOptions.Flavors, func(i, j int) bool {
		return scalingUpOptions.Flavors[i].Charge < scalingUpOptions.Flavors[j].Charge
	})

	// 遍历flavors，查询各可用区库存，优先扩库存量充足的可用区
	for _, flavor := range scalingUpOptions.Flavors {

		zone, stock := m.getScalableZone(flavor, zoneSubnetMap, zonesUnavailableSpecMap)
		if stock > 0 {
			scalingCount := math.Min(math.Min(float64(number), float64(scalingUpOptions.SingleScalingRequestSize)), float64(stock))
			param := &rpc.ScalingUpParam{
				Number:           int(scalingCount),
				Zone:             zone,
				SubnetUUID:       zoneSubnetMap[zone].SubnetUUID,
				InstanceType:     flavor.InstanceType,
				Region:           scalingUpOptions.Region,
				BccImageID:       bccImageID,
				Cpu:              flavor.Cpu,
				Memory:           flavor.Memory,
				RootDiskSizeInGb: flavor.RootDiskSizeInGb,
				OsType:           scalingUpOptions.OsType,
				OsVersion:        scalingUpOptions.OsVersion,
				SsdMountPath:     scalingUpOptions.SsdMountPath,
				SsdDiskSize:      flavor.SsdDiskSize,
				AdminPassType:    scalingUpOptions.AdminPassType,
				AdminPass:        scalingUpOptions.AdminPass,
			}
			m.logger.Infof("[ScalingUp] scaling up cluster [%s] zone [%s] subnet [%s] instanceType [%d] region [%s] number [%d]",
				m.k8sInfo.CceClusterUUID, zone, param.SubnetUUID, flavor.InstanceType, param.Region, param.Number)
			err = m.cceClient.ScalingUp(m.k8sInfo.CceClusterUUID, param)
			m.logger.Infof("[ScalingUp] scaling up param %+v", param)
			if err != nil {
				m.logger.Errorf("[ScalingUp] scaling up cluster in zone [%s] failed: %s", zone, err)
			} else {
				m.logger.Infof("[ScalingUp] scaling up success cluster [%s] zone [%s] number [%d]", m.k8sInfo.CceClusterUUID, zone, param.Number)
				return nil
			}
		}
	}

	return errors.New("all zones are abnormal")
}

func (m *cceNodeControl) ScalingDown(nodeIDs []string) error {
	return m.cceClient.ScalingDown(m.k8sInfo.CceClusterUUID, nodeIDs)
}

func (m *cceNodeControl) ListNodes() (*ClusterNodes, error) {
	// 先获取k8s node，转换为NodeInfo
	k8sNodeMap, unavailableCount, err := m.getK8sNodeMap()
	if err != nil {
		return nil, err
	}
	m.logger.V(6).Infof("[ListNodes] get %d nodes from k8s %s", len(k8sNodeMap), m.k8sInfo.CceClusterUUID)

	clusterNodes := &ClusterNodes{
		UnavailableCount:   unavailableCount,
		WaitScaleDownNodes: make(map[string]*api.NodeInfo, 0),
	}
	if m.cceClient != nil {
		// 获取cce node，为了获取floating ip
		var cceNodeMap map[string]*rpc.CceNode
		nodeMapReturns, err := m.getCceNodeMap()
		if err != nil {
			return nil, err
		}

		cceNodeMap = nodeMapReturns.NodeMap
		clusterNodes.PendingCount = nodeMapReturns.PendingCount
		// 将待缩容的cce node转换成api.NodeInfo格式，并将其node state设置为 CCE_obsolute状态
		for _, cceNode := range nodeMapReturns.WaitScaleDownNodeMap {
			nodeInfo := cceNode.ConvertToObsoluteNodeInfo()
			clusterNodes.WaitScaleDownNodes[nodeInfo.ID] = nodeInfo
		}

		m.logger.V(6).Infof("[ListNodes] get %d nodes from cce %s, %d cce nodes need to scale down", len(cceNodeMap), m.k8sInfo.CceClusterUUID, len(nodeMapReturns.WaitScaleDownNodeMap))

		m.updateNodesFromCCE(k8sNodeMap, cceNodeMap)
	}

	clusterNodes.Nodes = make([]*api.NodeInfo, len(k8sNodeMap))
	i := 0
	for _, k8sNode := range k8sNodeMap {
		clusterNodes.Nodes[i] = k8sNode
		i++
	}
	return clusterNodes, nil
}

// updateNodesFromCCE 比较k8s和cce获取的node信息，对结果最进一步处理
func (m *cceNodeControl) updateNodesFromCCE(k8sNodeMap map[string]*api.NodeInfo, cceNodeMap map[string]*rpc.CceNode) {
	// 写入floating ip
	for id, k8sNode := range k8sNodeMap {
		cceNode, ok := cceNodeMap[k8sNode.InstanceShortId]

		// 如果cce中不存在，就把node从结果中删除
		if !ok {
			m.logger.Warnf("can not find k8s node %s in cce, so exclude it", id)
			delete(k8sNodeMap, id)
			continue
		}

		// 填入floating IP
		k8sNode.FloatingIP = cceNode.FloatingIp
		k8sNode.CceInstanceID = cceNode.CceInstanceID
		k8sNode.Flavor = api.BccFlavor{
			Cpu:    cceNode.Cpu,
			Memory: cceNode.Memory,
		}

		// 写入initial labels
		if m.k8sInfo.ClusterLabels != nil && len(m.k8sInfo.ClusterLabels) != 0 {
			k8sNode.ClusterLabels = m.k8sInfo.ClusterLabels
		}

		// 写入服务类型
		k8sNode.ServiceType = m.k8sInfo.ServiceType
	}
}

type CceNodeMapReturn struct {
	NodeMap              map[string]*rpc.CceNode
	WaitScaleDownNodeMap map[string]*rpc.CceNode
	PendingCount         uint
}

func (m *cceNodeControl) getCceNodeMap() (CceNodeMapReturn, error) {
	nodes, err := m.cceClient.GetNodes(m.k8sInfo.CceClusterUUID)
	if err != nil {
		return CceNodeMapReturn{nil, nil, 0}, err
	}

	var pendingCount uint
	nodeMap := make(map[string]*rpc.CceNode)
	waitScaleDownNodeMap := make(map[string]*rpc.CceNode)
	for i := range nodes {
		node := &nodes[i]
		if node.Status == rpc.CceNodeStatusCreating {
			m.logger.Warnf("cce node %s is creating", node.ShortID)
			pendingCount++
			continue
		}

		// 扩容缩容失败的等需要处理的的cce node
		if node.Status == rpc.CceNodeStatusCreateFailed || node.Status == rpc.CceNodeStatusDeleteFailed || node.Status == rpc.CceNodeStatusError {
			m.logger.Infof("cce node %s is %s", node.ShortID, node.Status)
			waitScaleDownNodeMap[node.ShortID] = node
			continue
		}

		if node.Status != rpc.CceNodeStatusRunning {
			continue
		}
		nodeMap[node.ShortID] = node
	}
	return CceNodeMapReturn{nodeMap, waitScaleDownNodeMap, pendingCount}, nil
}

func (m *cceNodeControl) ApplyDaemonSet(dsName string) (err error) {
	return m.baseClusterControl.ApplyDaemonSet(dsName)
}

// Get image list
func (m *cceNodeControl) GetImageList() ([]rpc.ImageModel, error) {
	input := rpc.GetImageListRequest{User: m.k8sInfo.BceOptions.DockerHubUser}
	return m.cceClient.GetImageList(&input)
}

func (m *cceNodeControl) GetImageListV2() ([]interface{}, error) {
	var (
		res       []interface{}
		ccrImages []rpc.CcrImageModel
		err       error
	)

	// 获取ccr镜像列表
	ccrInput := rpc.GetCcrImageListRequest{Keyword: m.k8sInfo.BceOptions.CcrProjectName}
	ccrImages, err = m.ccrClient.GetImageList(&ccrInput)
	if err != nil {
		// 有错误打印一条错误日志
		logs.Errorf("get ccr images fail, err: %+v", err)
	}

	// 返回结果包含cce镜像 + ccr镜像
	if len(ccrImages) > 0 {
		for _, im := range ccrImages {
			res = append(res, im)
		}
	}
	return res, err
}
