package impl

import (
	"errors"
	"os"
	"strconv"
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/assert"

	"icode.baidu.com/baidu/faas/kun/cmd/cron/app/options"
	"icode.baidu.com/baidu/faas/kun/pkg/api"
	"icode.baidu.com/baidu/faas/kun/pkg/cron/rpc"
	"icode.baidu.com/baidu/faas/kun/pkg/cron/rpc/mock"

	"icode.baidu.com/baidu/faas/kun/pkg/bce/bcc"
	bccMock "icode.baidu.com/baidu/faas/kun/pkg/bce/bcc/mock"
)

func TestScalingUp(t *testing.T) {
	mockCtrl := gomock.NewController(t)
	defer mockCtrl.Finish()
	// ctl, cceNodes := testCceControl(cceClient, nil, nil)

	failedNodeList := []rpc.CceNode{}

	cceClient := mock.NewMockCceInterface(mockCtrl)
	bccClient := bccMock.NewMockBccInterface(mockCtrl)
	ctl, _ := testCceControl(cceClient, bccClient, nil)
	cceClient.EXPECT().IsClusterRunning("cluster1").Return(true)
	cceClient.EXPECT().ScalingUp("cluster1", gomock.Any()).Return(nil)
	cceClient.EXPECT().GetCreatedFailedNodes("cluster1").Return(failedNodeList, nil)
	bccClient.EXPECT().GetBccImageLongID(ctl.k8sInfo.ScalingUpOptions.OsType, ctl.k8sInfo.ScalingUpOptions.OsName,
		ctl.k8sInfo.ScalingUpOptions.OsVersion, ctl.k8sInfo.ScalingUpOptions.OsArch).Return("id-123", nil)
	stocks := []bcc.BccStock{
		{
			LogicalZone:       "cn-bj-b",
			Spec:              "bcc.g3.c2m8",
			InventoryQuantity: 8,
			RootOnLocal:       false,
		},
		{
			LogicalZone:       "cn-bj-a",
			Spec:              "bcc.g3.c4m8",
			InventoryQuantity: 20,
			RootOnLocal:       false,
		},
		{
			LogicalZone:       "cn-bj-d",
			Spec:              "bcc.g3.c8m16",
			InventoryQuantity: 25,
			RootOnLocal:       false,
		},
		{
			LogicalZone:       "cn-bj-c",
			Spec:              "bcc.g3.c4m16",
			InventoryQuantity: 7,
			RootOnLocal:       false,
		},
	}
	bccClient.EXPECT().GetStockWithSpec(gomock.Any()).Return(stocks, nil)
	bccClient.EXPECT().GetAvailableSpec().Return(nil, errors.New("err"))

	err := ctl.ScalingUp(2)
	assert.Nil(t, err)
}

func TestGetCceNodeMap(t *testing.T) {
	mockCtrl := gomock.NewController(t)
	defer mockCtrl.Finish()

	cceClient := mock.NewMockCceInterface(mockCtrl)
	ctl, cceNodes := testCceControl(cceClient, nil, nil)
	cceClient.EXPECT().GetNodes("cluster1").Return(cceNodes, nil)
	//gotCceNods, pendingCnt, err := ctl.getCceNodeMap()
	nodeReturns, err := ctl.getCceNodeMap()
	assert.Nil(t, err)
	assert.EqualValues(t, 5, len(nodeReturns.NodeMap))
	assert.EqualValues(t, 1, nodeReturns.PendingCount)
	assert.EqualValues(t, 3, len(nodeReturns.WaitScaleDownNodeMap))
}

func testCceControl(cceClient rpc.CceInterface, bccClient bcc.BccInterface, ccrClient rpc.CcrInterface) (*cceNodeControl, []rpc.CceNode) {

	cceNodes := make([]rpc.CceNode, 5)
	zoneAConfig := api.ZoneConfig{InstanceTypes: []int{1}, SubnetUUID: "net1"}
	for i := 0; i < len(cceNodes)-1; i++ {
		cceNodes[i] = rpc.CceNode{
			ShortID:      "ins" + strconv.Itoa(i),
			Zone:         "zoneA",
			SubnetID:     zoneAConfig.SubnetUUID,
			InstanceType: strconv.Itoa(zoneAConfig.InstanceTypes[0]),
			Status:       rpc.CceNodeStatusRunning,
		}
	}

	zoneBConfig := api.ZoneConfig{InstanceTypes: []int{1, 2}, SubnetUUID: "net2"}
	cceNode1 := rpc.CceNode{
		ShortID:      "ins10",
		Zone:         "zoneB",
		SubnetID:     zoneBConfig.SubnetUUID,
		InstanceType: strconv.Itoa(zoneBConfig.InstanceTypes[1]),
		Status:       rpc.CceNodeStatusCreating,
	}
	cceNode2 := rpc.CceNode{
		ShortID:      "ins11",
		Zone:         "zoneB",
		SubnetID:     zoneBConfig.SubnetUUID,
		InstanceType: strconv.Itoa(zoneBConfig.InstanceTypes[0]),
		Status:       rpc.CceNodeStatusRunning,
	}
	cceNode3 := rpc.CceNode{
		ShortID:      "ins12",
		Zone:         "zoneB",
		SubnetID:     zoneBConfig.SubnetUUID,
		InstanceType: strconv.Itoa(zoneBConfig.InstanceTypes[0]),
		Status:       "testStatus",
	}

	cceNode4 := rpc.CceNode{
		ShortID:      "ins13",
		Zone:         "zoneB",
		SubnetID:     zoneBConfig.SubnetUUID,
		InstanceType: strconv.Itoa(zoneBConfig.InstanceTypes[0]),
		Status:       rpc.CceNodeStatusDeleteFailed,
	}

	cceNode5 := rpc.CceNode{
		ShortID:      "ins14",
		Zone:         "zoneB",
		SubnetID:     zoneBConfig.SubnetUUID,
		InstanceType: strconv.Itoa(zoneBConfig.InstanceTypes[0]),
		Status:       rpc.CceNodeStatusCreateFailed,
	}
	cceNode6 := rpc.CceNode{
		ShortID:      "ins15",
		Zone:         "zoneB",
		SubnetID:     zoneBConfig.SubnetUUID,
		InstanceType: strconv.Itoa(zoneBConfig.InstanceTypes[0]),
		Status:       rpc.CceNodeStatusError,
	}

	cceNodes = append(cceNodes, cceNode1)
	cceNodes = append(cceNodes, cceNode2)
	cceNodes = append(cceNodes, cceNode3)
	cceNodes = append(cceNodes, cceNode4)
	cceNodes = append(cceNodes, cceNode5)
	cceNodes = append(cceNodes, cceNode6)

	ctrl := &cceNodeControl{
		cceClient: cceClient,
		bccClient: bccClient,
		baseClusterControl: &baseClusterControl{
			k8sInfo: &api.K8sInfo{
				CceClusterUUID: "cluster1",
				ScalingUpOptions: api.ScalingUpOptions{
					ZoneConfigMap: map[string]api.ZoneConfig{
						"zoneA": zoneAConfig,
						"zoneB": zoneBConfig,
					},

					OsType:    "Linux",
					OsName:    "Ubuntu",
					OsVersion: "16.04 LTS",
					OsArch:    "amd64 (64bit)",
					Flavors: []api.BccFlavor{
						{
							Cpu:          2,
							Memory:       4,
							SsdDiskSize:  50,
							MaxPodDeploy: 20,
							Spec:         "bcc.g3.c2m8",
							Charge:       0.1,
						},
						{
							Cpu:          16,
							Memory:       8,
							SsdDiskSize:  50,
							MaxPodDeploy: 20,
							Spec:         "bcc.g3.c2m8",
							Charge:       0.9,
						},
						{
							Cpu:          8,
							Memory:       16,
							SsdDiskSize:  50,
							MaxPodDeploy: 20,
							Spec:         "bcc.g3.c2m8",
							Charge:       0.7,
						},
						{
							Cpu:          6,
							Memory:       16,
							SsdDiskSize:  50,
							MaxPodDeploy: 20,
							Spec:         "bcc.g3.c2m8",
							Charge:       0.5,
						},
					},
				},
			},
			opt: &options.CronOptions{
				TaskSchedulerOptions: &options.TaskSchedulerOptions{
					TaskCloudSchedulerOptions: &options.TaskCloudSchedulerOptions{},
				},
			},
			bccClient:        bccClient,
			ccrClient:        ccrClient,
			bccOpenAPIClient: bccClient,
		},
	}
	return ctrl, cceNodes
}

func TestApplyDaemonSet(t *testing.T) {
	mockCtrl := gomock.NewController(t)
	defer mockCtrl.Finish()

	k8sInfo := api.NewDefaultK8sInfo()
	k8sInfo.BceOptions = &api.BceOptions{}
	k8sInfo.OteOptions = api.OteOptions{}
	k8sInfo.BccEndpoint = "127.0.0.1"

	k8sInfo.AccessKeyID = "test"
	k8sInfo.CcrEndpoint = "127.0.0.1"
	k8sInfo.CceClusterUUID = "cluster_test"

	dsConfig := api.DsReleaseConfig{
		Md5:          "22df32192d5e3be05d44ba764f5ece6d",
		ImageID:      "registry.baidubce.com/cfc_resource/funclet:4d75f6",
		YamlFilePath: "mock/funclet_ol_su.yaml",
	}

	dsMap := make(map[string]api.DsReleaseConfig)
	dsMap["funclet"] = dsConfig
	k8sInfo.DsReleaseConfigMap = dsMap
	cp := options.NewCronOptions()
	fileStr, _ := os.Getwd()
	cp.ConfDir = fileStr
	cceClient := mock.NewMockCceInterface(mockCtrl)
	m, _ := testCceControl(cceClient, nil, nil)
	m.k8sInfo = k8sInfo
	m.opt = cp
	err := m.ApplyDaemonSet("funclet")
	if err == nil {
		t.Fatalf("err")
	}

	dsConfig1 := api.DsReleaseConfig{
		Md5:          "93e97bf72a672c907a44164d959321c1",
		ImageID:      "registry.baidubce.com/cfc_resource/funclet:4d75f6",
		YamlFilePath: "mock/funclet_new_su.yaml",
	}
	dsMap["funclet"] = dsConfig1
	k8sInfo.Version = "v2"
	err = m.ApplyDaemonSet("funclet")
	if err == nil {
		t.Fatalf("err")
	}
}

func TestGetScalableZone(t *testing.T) {
	flavor := api.BccFlavor{
		Cpu:          2,
		Memory:       8,
		SsdDiskSize:  30,
		MaxPodDeploy: 35,
		Spec:         "bcc.g3.c2m8",
		InstanceType: 13,
		Charge:       0.0066236,
	}
	zoneConfigMap := make(map[string]api.ZoneConfig)
	instanceTypes := []int{10, 13}
	zoneConfigMap["zoneD"] = api.ZoneConfig{
		SubnetUUID:    "b4eef8a1-578e-4fbe-9437-f21b33ac0c61",
		InstanceTypes: instanceTypes,
	}

	stocks := []bcc.BccStock{
		{
			LogicalZone:       "cn-bj-d",
			Spec:              "bcc.g3.c2m8",
			InventoryQuantity: 10,
			RootOnLocal:       false,
		},
	}
	mockCtrl := gomock.NewController(t)
	defer mockCtrl.Finish()

	bccClient := bccMock.NewMockBccInterface(mockCtrl)
	cceClient := mock.NewMockCceInterface(mockCtrl)

	bccClient.EXPECT().GetStockWithSpec(gomock.Any()).Return(stocks, nil)
	bccClient.EXPECT().GetAvailableSpec().Return(nil, nil)

	ctl, _ := testCceControl(cceClient, bccClient, nil)
	ctl.bccOpenAPIClient = bccClient

	zonesUnavailableSpecMap := &ZonesUnavailableSpecMap{}
	(*zonesUnavailableSpecMap)["zoneD"] = &UnavailableSpecSet{}
	zone, _ := ctl.getScalableZone(flavor, zoneConfigMap, zonesUnavailableSpecMap)
	assert.Equal(t, zone, "zoneD")
}
