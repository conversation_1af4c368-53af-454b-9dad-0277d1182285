package impl

import (
	"icode.baidu.com/baidu/faas/kun/pkg/api"
	"icode.baidu.com/baidu/faas/kun/pkg/etcd"
	"icode.baidu.com/baidu/faas/kun/pkg/util/logs"
)

type k8sConfigStore struct {
	etcd.K8sInterface // TODO:暂时继承，未来将实现转移进来
	logger            *logs.Logger
}

var _ ClusterInfoStore = &k8sConfigStore{}

func NewK8sConfigStore(etcdClient etcd.K8sInterface, logger *logs.Logger) ClusterInfoStore {
	return &k8sConfigStore{
		K8sInterface: etcdClient,
		logger:       logger,
	}
}

func (s *k8sConfigStore) ListClusterInfo() ([]*api.K8sInfo, error) {
	kis, err := s.GetAllK8s()
	if err != nil {
		return nil, err
	}

	return kis, nil
}
