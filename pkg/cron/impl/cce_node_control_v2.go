package impl

import (
	"bytes"
	"errors"
	"fmt"
	"math"
	"sort"
	"text/template"

	"icode.baidu.com/baidu/faas/kun/pkg/api"
	"icode.baidu.com/baidu/faas/kun/pkg/bce/bcc"
	"icode.baidu.com/baidu/faas/kun/pkg/cron/rpc"
)

type cceNodeControlV2 struct {
	*baseClusterControl
	cceClient rpc.CceV2Interface
	bccClient bcc.BccInterface
	ccrClient rpc.CcrInterface
}

var _ ClusterControl = &cceNodeControlV2{}

func NewCceNodeControlV2(c *baseClusterControl) *cceNodeControlV2 {
	ctrl := &cceNodeControlV2{
		baseClusterControl: c,
	}
	if c.k8sInfo.BceOptions == nil { // 只有cce配置存在时才会初始化
		panic("cce config is nil")
	}
	ctrl.cceClient = rpc.NewCceClientV2(c.k8sInfo.BceOptions)
	ctrl.bccClient = bcc.NewBccClient(c.k8sInfo.BceOptions)
	ctrl.ccrClient = rpc.NewCcrClient(c.k8sInfo.BceOptions)
	return ctrl
}

func (m *cceNodeControlV2) precheckAvailabilityOfFlavors(clusterID string) (zonesUnavailableSpecMap *ZonesUnavailableSpecMap, err error) {

	//避免请求cce失败后zonesUnavailableSpecMap为nil 导致后续步骤出错
	zonesUnavailableSpecMap = &ZonesUnavailableSpecMap{}

	nodes, err := m.cceClient.GetCreatedFailedNodes(clusterID)

	if err != nil {
		return
	}

	for _, node := range nodes {

		spec := fmt.Sprintf("%s.c%dm%d", node.Spec.InstanceType, node.Spec.InstanceResource.Cpu, node.Spec.InstanceResource.Mem)
		m.logger.Infof("spec %s", spec)
		if _, exists := (*zonesUnavailableSpecMap)[node.Spec.VPCConfig.AvailableZone]; !exists {
			(*zonesUnavailableSpecMap)[node.Spec.VPCConfig.AvailableZone] = &UnavailableSpecSet{}
		}
		specSet := (*zonesUnavailableSpecMap)[node.Spec.VPCConfig.AvailableZone]
		(*specSet)[spec] = EXSIT
	}
	return
}

func (m *cceNodeControlV2) ScalingUp(number int) error {
	if !m.cceClient.IsClusterRunning(m.k8sInfo.CceClusterUUID) {
		return &ClusterNotRunningError{m.k8sInfo.CceClusterUUID}
	}

	// 使用当前集群的zone和subnet配置来扩容
	zoneSubnetMap := m.k8sInfo.ScalingUpOptions.ZoneConfigMap
	if len(zoneSubnetMap) == 0 {
		return errors.New("empty zone and subnet in k8s config")
	}

	zonesUnavailableSpecMap, err := m.precheckAvailabilityOfFlavors(m.k8sInfo.CceClusterUUID)
	if err != nil {
		m.logger.Warnf("[ScalingUp]: precheck availability of flavaors failed: %s", err.Error())
	}

	// 由于无法判断zone是否有资源，只能尝试对每个zone扩容，直至成功
	scalingUpOptions := m.k8sInfo.ScalingUpOptions

	// 实时获取bcc image id，如果获取失败使用etcd中配置好的image id
	bccImageID, err := m.bccClient.GetBccImageLongID(scalingUpOptions.OsType, scalingUpOptions.OsName, scalingUpOptions.OsVersion, scalingUpOptions.OsArch)
	if err != nil || bccImageID == "" {
		m.logger.Errorf("[ScalingUp]: get bcc imageID error %s", err.Error())
		bccImageID = scalingUpOptions.BccImageID
	}

	m.logger.Infof("[ScalingUp]: bcc image id is %s", bccImageID)

	// 按照价格从小到大排序，优先创建便宜的虚机
	sort.SliceStable(scalingUpOptions.Flavors, func(i, j int) bool {
		return scalingUpOptions.Flavors[i].Charge < scalingUpOptions.Flavors[j].Charge
	})

	downloadScript, err := m.genDownloadContanierScript()
	if err != nil {
		m.logger.Errorf("[ScalingUp]: get bcc imageID error %s", err.Error())
	}
	m.logger.Debugf("[ScalingUp]: downloadScript is %s", downloadScript)
	for _, flavor := range scalingUpOptions.Flavors {

		zone, stock := m.getScalableZone(flavor, zoneSubnetMap, zonesUnavailableSpecMap)
		if stock > 0 {
			scalingCount := math.Min(math.Min(float64(number), float64(scalingUpOptions.SingleScalingRequestSize)), float64(stock))
			param := &rpc.ScalingUpParam{
				Number:                          int(scalingCount),
				Zone:                            zone,
				SubnetUUID:                      zoneSubnetMap[zone].VPCSubnetID,
				InstanceType:                    flavor.InstanceType,
				Region:                          scalingUpOptions.Region,
				BccImageID:                      bccImageID,
				Cpu:                             flavor.Cpu,
				Memory:                          flavor.Memory,
				RootDiskSizeInGb:                flavor.RootDiskSizeInGb,
				OsType:                          scalingUpOptions.OsType,
				OsVersion:                       scalingUpOptions.OsVersion,
				SsdMountPath:                    scalingUpOptions.SsdMountPath,
				SsdDiskSize:                     flavor.SsdDiskSize,
				AdminPassType:                   scalingUpOptions.AdminPassType,
				AdminPass:                       scalingUpOptions.AdminPass,
				MachineSpec:                     flavor.Spec,
				PreDownloadContainerImageScript: downloadScript,
			}
			m.logger.Infof("[ScalingUp] scaling up cluster [%s] zone [%s] subnet [%s] instanceType [%d] region [%s] number [%d] spec [%s]",
				m.k8sInfo.CceClusterUUID, zone, param.SubnetUUID, flavor.InstanceType, param.Region, param.Number, flavor.Spec)
			err = m.cceClient.ScalingUp(m.k8sInfo.CceClusterUUID, param)
			if err != nil {
				m.logger.Errorf("[ScalingUp] scaling up cluster in zone [%s] failed: %s", zone, err)
			} else {
				m.logger.Infof("[ScalingUp] scaling up success cluster [%s] zone [%s] number [%d] spec [%s]", m.k8sInfo.CceClusterUUID, zone, param.Number, flavor.Spec)
				return nil
			}
		}
	}
	return errors.New("[ScalingUp] all zones are abnormal")
}

// fork scalingup接口
func (m *cceNodeControlV2) ScalingUpWithResourcePool(resourcePool string, number int) error {
	if !m.cceClient.IsClusterRunning(m.k8sInfo.CceClusterUUID) {
		return &ClusterNotRunningError{m.k8sInfo.CceClusterUUID}
	}

	// 使用当前集群的zone和subnet配置来扩容
	zoneSubnetMap := m.k8sInfo.ScalingUpOptions.ZoneConfigMap
	if len(zoneSubnetMap) == 0 {
		return errors.New("empty zone and subnet in k8s config")
	}

	zonesUnavailableSpecMap, err := m.precheckAvailabilityOfFlavors(m.k8sInfo.CceClusterUUID)
	if err != nil {
		m.logger.Warnf("[ScalingUp]: precheck availability of flavaors failed: %s", err.Error())
	}

	// 由于无法判断zone是否有资源，只能尝试对每个zone扩容，直至成功
	scalingUpOptions := m.k8sInfo.ScalingUpOptions

	// 实时获取bcc image id，如果获取失败使用etcd中配置好的image id
	bccImageID, err := m.bccClient.GetBccImageLongID(scalingUpOptions.OsType, scalingUpOptions.OsName, scalingUpOptions.OsVersion, scalingUpOptions.OsArch)
	if err != nil || bccImageID == "" {
		m.logger.Errorf("[ScalingUp]: get bcc imageID error %s", err.Error())
		bccImageID = scalingUpOptions.BccImageID
	}

	m.logger.Infof("[ScalingUp]: bcc image id is %s", bccImageID)

	// 按照价格从小到大排序，优先创建便宜的虚机
	sort.SliceStable(scalingUpOptions.Flavors, func(i, j int) bool {
		return scalingUpOptions.Flavors[i].Charge < scalingUpOptions.Flavors[j].Charge
	})

	downloadScript, err := m.genDownloadContanierScript()
	if err != nil {
		m.logger.Errorf("[ScalingUp]: get bcc imageID error %s", err.Error())
	}
	k8sLabels := make(map[string]string)
	if resourcePool != api.DefaultResourcePool {
		k8sLabels[api.LabelResourcePool] = resourcePool
	}
	m.logger.Debugf("[ScalingUp]: downloadScript is %s", downloadScript)
	for _, flavor := range scalingUpOptions.Flavors {

		zone, stock := m.getScalableZone(flavor, zoneSubnetMap, zonesUnavailableSpecMap)
		if stock > 0 {
			scalingCount := math.Min(math.Min(float64(number), float64(scalingUpOptions.SingleScalingRequestSize)), float64(stock))
			param := &rpc.ScalingUpParam{
				Number:                          int(scalingCount),
				Zone:                            zone,
				SubnetUUID:                      zoneSubnetMap[zone].VPCSubnetID,
				InstanceType:                    flavor.InstanceType,
				Region:                          scalingUpOptions.Region,
				BccImageID:                      bccImageID,
				Cpu:                             flavor.Cpu,
				Memory:                          flavor.Memory,
				RootDiskSizeInGb:                flavor.RootDiskSizeInGb,
				OsType:                          scalingUpOptions.OsType,
				OsVersion:                       scalingUpOptions.OsVersion,
				SsdMountPath:                    scalingUpOptions.SsdMountPath,
				SsdDiskSize:                     flavor.SsdDiskSize,
				AdminPassType:                   scalingUpOptions.AdminPassType,
				AdminPass:                       scalingUpOptions.AdminPass,
				MachineSpec:                     flavor.Spec,
				PreDownloadContainerImageScript: downloadScript,
				K8sLabels:                       k8sLabels,
			}
			m.logger.Infof("[ScalingUp] scaling up cluster [%s] zone [%s] subnet [%s] instanceType [%d] region [%s] number [%d] spec [%s]",
				m.k8sInfo.CceClusterUUID, zone, param.SubnetUUID, flavor.InstanceType, param.Region, param.Number, flavor.Spec)
			err = m.cceClient.ScalingUp(m.k8sInfo.CceClusterUUID, param)
			if err != nil {
				m.logger.Errorf("[ScalingUp] scaling up cluster in zone [%s] failed: %s", zone, err)
			} else {
				m.logger.Infof("[ScalingUp] scaling up success cluster [%s] zone [%s] number [%d] spec [%s]", m.k8sInfo.CceClusterUUID, zone, param.Number, flavor.Spec)
				return nil
			}
		}
	}
	return errors.New("[ScalingUp] all zones are abnormal")
}

func (m *cceNodeControlV2) ScalingDown(nodeIDs []string) error {
	return m.cceClient.ScalingDown(m.k8sInfo.CceClusterUUID, nodeIDs)
}

func (m *cceNodeControlV2) ListNodes() (*ClusterNodes, error) {
	// 先获取k8s node，转换为NodeInfo
	k8sNodeMap, unavailableCount, err := m.getK8sNodeMap()
	if err != nil {
		return nil, err
	}
	m.logger.V(6).Infof("[ListNodes] get %d nodes from k8s %s", len(k8sNodeMap), m.k8sInfo.CceClusterUUID)

	clusterNodes := &ClusterNodes{
		UnavailableCount:           unavailableCount,
		WaitScaleDownNodes:         make(map[string]*api.NodeInfo, 0),
		ResourcePoolToPendingCount: make(map[string]uint),
	}
	if m.cceClient != nil {
		// 获取cce node，为了获取floating ip
		var cceNodeMap map[string]*rpc.Instance
		nodeMapReturns, err := m.getCceNodeMap()
		if err != nil {
			return nil, err
		}

		cceNodeMap = nodeMapReturns.NodeMap
		clusterNodes.PendingCount = nodeMapReturns.PendingCount
		clusterNodes.ResourcePoolToPendingCount = nodeMapReturns.ResourcePoolPendingCount
		// 将待缩容的cce node转换成api.NodeInfo格式，并将其node state设置为 CCE_obsolute状态
		for _, cceNode := range nodeMapReturns.WaitScaleDownNodeMap {
			nodeInfo := cceNode.ConvertToObsoluteNodeInfo()
			clusterNodes.WaitScaleDownNodes[nodeInfo.ID] = nodeInfo
		}

		m.logger.V(6).Infof("[ListNodes] get %d nodes from cce %s, %d cce nodes need to scale down",
			len(cceNodeMap), m.k8sInfo.CceClusterUUID, len(nodeMapReturns.WaitScaleDownNodeMap))

		m.updateNodesFromCCE(k8sNodeMap, cceNodeMap)
	}

	clusterNodes.Nodes = make([]*api.NodeInfo, len(k8sNodeMap))
	i := 0
	for _, k8sNode := range k8sNodeMap {
		clusterNodes.Nodes[i] = k8sNode
		i++
	}
	return clusterNodes, nil
}

// updateNodesFromCCE 比较k8s和cce获取的node信息，对结果最进一步处理
func (m *cceNodeControlV2) updateNodesFromCCE(k8sNodeMap map[string]*api.NodeInfo, cceNodeMap map[string]*rpc.Instance) {
	// 写入floating ip
	for id, k8sNode := range k8sNodeMap {
		cceNode, ok := cceNodeMap[k8sNode.InstanceShortId]

		// 如果cce中不存在，就把node从结果中删除
		if !ok {
			m.logger.Warnf("can not find k8s node %s in cce, so exclude it", id)
			delete(k8sNodeMap, id)
			continue
		}

		// 填入VPC ID
		k8sNode.VpcID = cceNode.Spec.VPCConfig.VpcID
		// 填入floating IP
		k8sNode.FloatingIP = cceNode.InternalFields.FloatingIP
		// 填入cceInstanceID
		k8sNode.CceInstanceID = cceNode.Spec.CceInstanceID
		//写入node的规格
		k8sNode.Flavor = api.BccFlavor{
			Cpu:    cceNode.Spec.InstanceResource.Cpu,
			Memory: cceNode.Spec.InstanceResource.Mem,
			Spec:   cceNode.Spec.InstanceResource.MachineSpec,
		}

		// 写入initial labels
		if m.k8sInfo.ClusterLabels != nil && len(m.k8sInfo.ClusterLabels) != 0 {
			k8sNode.ClusterLabels = m.k8sInfo.ClusterLabels
		}

		// 写入服务类型
		k8sNode.ServiceType = m.k8sInfo.ServiceType
	}
}

type CceInstanceMapReturn struct {
	NodeMap                  map[string]*rpc.Instance
	WaitScaleDownNodeMap     map[string]*rpc.Instance
	PendingCount             uint
	ResourcePoolPendingCount map[string]uint
}

// get cce node info
func (m *cceNodeControlV2) getCceNodeMap() (CceInstanceMapReturn, error) {
	nodes, err := m.cceClient.GetNodes(m.k8sInfo.CceClusterUUID)
	if err != nil {
		return CceInstanceMapReturn{nil, nil, 0, nil}, err
	}
	// 获取cce节点的tag标签
	getNodeLabel := func(node *rpc.Instance, key string) string {
		if node.Spec.Labels == nil {
			return ""
		}
		val, ok := node.Spec.Labels[key]
		if !ok {
			return ""
		}
		return val
	}

	var pendingCount uint
	nodeMap := make(map[string]*rpc.Instance)
	waitScaleDownNodeMap := make(map[string]*rpc.Instance)
	resourcePoolPendingCount := make(map[string]uint)
	for i := range nodes {
		node := &nodes[i]
		if node.Status.InstancePhase == rpc.CceInstancePhasePending || node.Status.InstancePhase == rpc.CceInstancePhaseProvisioned || node.Status.InstancePhase == rpc.CceInstancePhaseProvisioning {
			m.logger.Warnf("cce node %s is creating", node.Spec.CceInstanceID)
			pendingCount++
			// 按池统计pending的节点数量
			resourcePool := getNodeLabel(node, api.LabelResourcePool)
			if resourcePool == "" {
				// 默认池
				resourcePoolPendingCount[api.DefaultResourcePool]++
			} else {
				resourcePoolPendingCount[resourcePool]++
			}
			continue
		}

		// 扩容缩容失败的等需要处理的的cce node
		if node.Status.InstancePhase == rpc.CceInstancePhaseDeleteFailed || node.Status.InstancePhase == rpc.CceInstancePhasesCreateFailed {
			m.logger.Infof("cce node uuid %s is %s", node.Spec.CceInstanceID, node.Status.InstancePhase)
			//
			waitScaleDownNodeMap[node.Status.Machine.InstanceID] = node
			continue
		}

		if node.Status.InstancePhase != rpc.CceInstancePhaseRunning {
			continue
		}
		nodeMap[node.Status.Machine.InstanceID] = node
	}
	return CceInstanceMapReturn{nodeMap, waitScaleDownNodeMap, pendingCount, resourcePoolPendingCount}, nil
}

// Apply DaemonSet
func (m *cceNodeControlV2) ApplyDaemonSet(dsName string) (err error) {
	return m.baseClusterControl.ApplyDaemonSet(dsName)
}

// v2集群都用ccr镜像吧
func (m *cceNodeControlV2) GetImageListV2() ([]interface{}, error) {
	return m.baseClusterControl.GetImageListV2()
}

var downloadContainerImageScriptTemplate = `
docker login --username={{ .BceOptions.DockerHubUser }} -p '{{ .BceOptions.DockerHubPasswd }}' registry.baidubce.com

{{ range $key, $value := .K8sOptions.DsReleaseConfigMap }}
echo "download {{ $key }} image"
nohup docker pull {{ $value.ImageID }} &
{{end}}

nohup docker pull {{ $.K8sOptions.ContainerImage }} &
`

func (m *cceNodeControlV2) genDownloadContanierScript() (string, error) {
	t := template.Must(template.New("downloadContainerImageScriptTemplate").Parse(downloadContainerImageScriptTemplate))
	var tpl bytes.Buffer

	err := t.Execute(&tpl, m.k8sInfo)
	if err != nil {
		return "", err
	}
	return tpl.String(), nil
}
