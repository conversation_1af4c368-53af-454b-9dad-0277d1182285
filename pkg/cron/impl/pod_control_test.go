package impl

import (
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/assert"

	"icode.baidu.com/baidu/faas/kun/pkg/api"
	"icode.baidu.com/baidu/faas/kun/pkg/funclet/mock"
	"icode.baidu.com/baidu/faas/kun/pkg/util/logs"
)

func TestResetNode(t *testing.T) {
	mockCtrl := gomock.NewController(t)
	defer mockCtrl.Finish()

	node := &api.NodeInfo{
		ID: "node1",
	}
	funcletClient := mock.NewMockFuncletInterface(mockCtrl)
	funcletClient.EXPECT().ResetNode(gomock.Any()).Return(nil)

	s := NewPodControl(funcletClient, logs.NewLogger())
	err := s.ResetNode(node)
	assert.Nil(t, err)
}

func TestPodControl_GetHealthyContainersOnNode(t *testing.T) {
	mockCtrl := gomock.NewController(t)
	defer mockCtrl.Finish()

	node := &api.NodeInfo{
		ID: "node1",
	}

	funcletClient := mock.NewMockFuncletInterface(mockCtrl)
	resp := &api.ListPodsResponse{}
	funcletClient.EXPECT().ListPods(gomock.Any()).Return(resp, nil)

	s := NewPodControl(funcletClient, logs.NewLogger())
	_, err := s.GetHealthyContainersOnNode(node)
	assert.Nil(t, err)
}

func TestPodControl_GetZombieContainersOnNode(t *testing.T) {
	mockCtrl := gomock.NewController(t)
	defer mockCtrl.Finish()

	node := &api.NodeInfo{
		ID: "node1",
	}

	funcletClient := mock.NewMockFuncletInterface(mockCtrl)
	resp := &api.ListPodsResponse{}
	funcletClient.EXPECT().ListZombiePods(gomock.Any()).Return(resp, nil)

	s := NewPodControl(funcletClient, logs.NewLogger())
	_, err := s.GetZombieContainersOnNode(node)
	assert.Nil(t, err)
}

func TestNewPodControl_CoolDownPod(t *testing.T) {
	mockCtrl := gomock.NewController(t)
	defer mockCtrl.Finish()

	node := &api.NodeInfo{
		ID: "node1",
	}
	container := &api.ContainerInfo{
		Hostname: "pod1",
	}

	funcletClient := mock.NewMockFuncletInterface(mockCtrl)
	funcletClient.EXPECT().CoolDown(gomock.Any()).Return(nil)

	s := NewPodControl(funcletClient, logs.NewLogger())
	err := s.CoolDownPod(container, node)
	assert.Nil(t, err)
}

func TestPodControl_CheckAndPullImages(t *testing.T) {
	mockCtrl := gomock.NewController(t)
	defer mockCtrl.Finish()

	node := &api.NodeInfo{
		ID: "node1",
	}
	images := []string{"aa", "bb"}
	funcletClient := mock.NewMockFuncletInterface(mockCtrl)
	testResult := make(map[string]bool, 3)
	testResult["aa"] = true
	testResult["bb"] = true
	resp := &api.FuncletCheckAndPullImagesResponse{
		Results: testResult,
		Err:     nil,
	}
	funcletClient.EXPECT().CheckAndPullImages(gomock.Any()).Return(resp, nil)
	s := NewPodControl(funcletClient, logs.NewLogger())
	_, err := s.CheckAndPullImages(node, images)
	assert.Nil(t, err)
}
