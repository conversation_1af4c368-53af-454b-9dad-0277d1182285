package impl

import (
	"testing"

	kubeapi "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"

	"icode.baidu.com/baidu/faas/kun/pkg/api"
)

func TestExtractAndSetResourcePoolFromK8s(t *testing.T) {
	// Test case 1: K8s节点有resourcePool标签，且配置中存在该ResourcePool
	t.Run("ValidResourcePoolInK8sLabels", func(t *testing.T) {
		k8sInfo := &api.K8sInfo{
			NodeConfig: api.NodeConfig{
				ClusterLabels: map[string]string{
					api.LabelVipUser: "common",
				},
			},
			ResourcePoolConfig: &api.ResourcePoolConfig{
				Enabled: true,
				ExtraResourcePools: map[string]api.ResourcePool{
					"ubuntu2204-pool": {
						ResourcePoolInfo: api.ResourcePoolInfo{
							Description:     "Ubuntu 22.04 pool",
							OSType:          "ubuntu22.04",
							SupportRuntimes: []string{"java17", "nodejs18"},
						},
					},
				},
			},
		}

		k8sNode := &k8sNodeInfo{
			ObjectMeta: metav1.ObjectMeta{
				Name: "test-node-1",
				Labels: map[string]string{
					"resourcePool": "ubuntu2204-pool",
				},
			},
			Spec: kubeapi.NodeSpec{
				ProviderID: "cce://i-test123",
			},
		}

		nodeInfo := k8sNode.ConvertToNodeInfo(k8sInfo)

		// 验证ResourcePool标签被正确设置
		if resourcePool := nodeInfo.ClusterLabels[api.LabelResourcePool]; resourcePool != "ubuntu2204-pool" {
			t.Errorf("Expected ResourcePool label 'ubuntu2204-pool', got '%s'", resourcePool)
		}

		// 验证基础标签仍然存在
		if vipUser := nodeInfo.ClusterLabels[api.LabelVipUser]; vipUser != "common" {
			t.Errorf("Expected vipUser label 'common', got '%s'", vipUser)
		}
	})

	// Test case 2: K8s节点有resourcePool标签，但配置中不存在该ResourcePool
	t.Run("InvalidResourcePoolInK8sLabels", func(t *testing.T) {
		k8sInfo := &api.K8sInfo{
			NodeConfig: api.NodeConfig{
				ClusterLabels: map[string]string{
					api.LabelVipUser: "common",
				},
			},
			ResourcePoolConfig: &api.ResourcePoolConfig{
				Enabled: true,
				ExtraResourcePools: map[string]api.ResourcePool{
					"ubuntu2204-pool": {
						ResourcePoolInfo: api.ResourcePoolInfo{
							Description: "Ubuntu 22.04 pool",
						},
					},
				},
			},
		}

		k8sNode := &k8sNodeInfo{
			ObjectMeta: metav1.ObjectMeta{
				Name: "test-node-2",
				Labels: map[string]string{
					"resourcePool": "invalid-pool", // 不存在的ResourcePool
				},
			},
			Spec: kubeapi.NodeSpec{
				ProviderID: "cce://i-test456",
			},
		}

		nodeInfo := k8sNode.ConvertToNodeInfo(k8sInfo)

		// 验证无效的ResourcePool标签不被设置
		if resourcePool := nodeInfo.ClusterLabels[api.LabelResourcePool]; resourcePool != "" {
			t.Errorf("Expected no ResourcePool label for invalid pool, got '%s'", resourcePool)
		}

		// 验证基础标签仍然存在
		if vipUser := nodeInfo.ClusterLabels[api.LabelVipUser]; vipUser != "common" {
			t.Errorf("Expected vipUser label 'common', got '%s'", vipUser)
		}
	})

	// Test case 3: K8s节点没有resourcePool标签（存量节点）
	t.Run("NoResourcePoolInK8sLabels", func(t *testing.T) {
		k8sInfo := &api.K8sInfo{
			NodeConfig: api.NodeConfig{
				ClusterLabels: map[string]string{
					api.LabelVipUser: "common",
				},
			},
			ResourcePoolConfig: &api.ResourcePoolConfig{
				Enabled: true,
				ExtraResourcePools: map[string]api.ResourcePool{
					"ubuntu2204-pool": {
						ResourcePoolInfo: api.ResourcePoolInfo{
							Description: "Ubuntu 22.04 pool",
						},
					},
				},
			},
		}

		k8sNode := &k8sNodeInfo{
			ObjectMeta: metav1.ObjectMeta{
				Name:   "test-node-3",
				Labels: map[string]string{}, // 没有resourcePool标签
			},
			Spec: kubeapi.NodeSpec{
				ProviderID: "cce://i-test789",
			},
		}

		nodeInfo := k8sNode.ConvertToNodeInfo(k8sInfo)

		// 验证没有ResourcePool标签被设置
		if resourcePool := nodeInfo.ClusterLabels[api.LabelResourcePool]; resourcePool != "" {
			t.Errorf("Expected no ResourcePool label for node without resourcePool, got '%s'", resourcePool)
		}

		// 验证基础标签仍然存在
		if vipUser := nodeInfo.ClusterLabels[api.LabelVipUser]; vipUser != "common" {
			t.Errorf("Expected vipUser label 'common', got '%s'", vipUser)
		}
	})

	// Test case 4: ResourcePool功能未启用
	t.Run("ResourcePoolDisabled", func(t *testing.T) {
		k8sInfo := &api.K8sInfo{
			NodeConfig: api.NodeConfig{
				ClusterLabels: map[string]string{
					api.LabelVipUser: "common",
				},
			},
			ResourcePoolConfig: &api.ResourcePoolConfig{
				Enabled: false, // 功能未启用
				ExtraResourcePools: map[string]api.ResourcePool{
					"ubuntu2204-pool": {
						ResourcePoolInfo: api.ResourcePoolInfo{
							Description: "Ubuntu 22.04 pool",
						},
					},
				},
			},
		}

		k8sNode := &k8sNodeInfo{
			ObjectMeta: metav1.ObjectMeta{
				Name: "test-node-4",
				Labels: map[string]string{
					"resourcePool": "ubuntu2204-pool",
				},
			},
			Spec: kubeapi.NodeSpec{
				ProviderID: "cce://i-test000",
			},
		}

		nodeInfo := k8sNode.ConvertToNodeInfo(k8sInfo)

		// 验证即使有标签，但功能未启用时不设置ResourcePool标签
		if resourcePool := nodeInfo.ClusterLabels[api.LabelResourcePool]; resourcePool != "" {
			t.Errorf("Expected no ResourcePool label when feature disabled, got '%s'", resourcePool)
		}
	})
}

func TestIsValidResourcePool(t *testing.T) {
	// Test case 1: ResourcePool功能未启用
	k8sInfo1 := &api.K8sInfo{
		ResourcePoolConfig: &api.ResourcePoolConfig{
			Enabled: false,
		},
	}
	if isValidResourcePool(k8sInfo1, "any-pool") {
		t.Error("Expected false when ResourcePool feature is disabled")
	}

	// Test case 2: ResourcePool配置为nil
	k8sInfo2 := &api.K8sInfo{
		ResourcePoolConfig: nil,
	}
	if isValidResourcePool(k8sInfo2, "any-pool") {
		t.Error("Expected false when ResourcePool config is nil")
	}

	// Test case 3: default池总是有效
	k8sInfo3 := &api.K8sInfo{
		ResourcePoolConfig: &api.ResourcePoolConfig{
			Enabled: true,
		},
	}
	if !isValidResourcePool(k8sInfo3, "default") {
		t.Error("Expected true for 'default' pool")
	}

	// Test case 4: 存在的额外ResourcePool
	k8sInfo4 := &api.K8sInfo{
		ResourcePoolConfig: &api.ResourcePoolConfig{
			Enabled: true,
			ExtraResourcePools: map[string]api.ResourcePool{
				"ubuntu2204-pool": {},
			},
		},
	}
	if !isValidResourcePool(k8sInfo4, "ubuntu2204-pool") {
		t.Error("Expected true for existing extra ResourcePool")
	}

	// Test case 5: 不存在的额外ResourcePool
	if isValidResourcePool(k8sInfo4, "non-existent-pool") {
		t.Error("Expected false for non-existent ResourcePool")
	}
}
