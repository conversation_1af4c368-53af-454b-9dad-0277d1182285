// Code generated by MockGen. DO NOT EDIT.
// Source: icode.baidu.com/baidu/faas/kun/pkg/cron/impl (interfaces: ClusterInfoStore)

// Package mock is a generated GoMock package.
package mock

import (
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	api "icode.baidu.com/baidu/faas/kun/pkg/api"
)

// MockClusterInfoStore is a mock of ClusterInfoStore interface.
type MockClusterInfoStore struct {
	ctrl     *gomock.Controller
	recorder *MockClusterInfoStoreMockRecorder
}

// MockClusterInfoStoreMockRecorder is the mock recorder for MockClusterInfoStore.
type MockClusterInfoStoreMockRecorder struct {
	mock *MockClusterInfoStore
}

// NewMockClusterInfoStore creates a new mock instance.
func NewMockClusterInfoStore(ctrl *gomock.Controller) *MockClusterInfoStore {
	mock := &MockClusterInfoStore{ctrl: ctrl}
	mock.recorder = &MockClusterInfoStoreMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockClusterInfoStore) EXPECT() *MockClusterInfoStoreMockRecorder {
	return m.recorder
}

// ListClusterInfo mocks base method.
func (m *MockClusterInfoStore) ListClusterInfo() ([]*api.K8sInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListClusterInfo")
	ret0, _ := ret[0].([]*api.K8sInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListClusterInfo indicates an expected call of ListClusterInfo.
func (mr *MockClusterInfoStoreMockRecorder) ListClusterInfo() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListClusterInfo", reflect.TypeOf((*MockClusterInfoStore)(nil).ListClusterInfo))
}
