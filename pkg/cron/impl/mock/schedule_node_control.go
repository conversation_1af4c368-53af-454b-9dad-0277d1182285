// Code generated by MockGen. DO NOT EDIT.
// Source: icode.baidu.com/baidu/faas/kun/pkg/cron/impl (interfaces: ScheduleNodeControl)

// Package mock is a generated GoMock package.
package mock

import (
	"time"

	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	api "icode.baidu.com/baidu/faas/kun/pkg/api"
	etcd "icode.baidu.com/baidu/faas/kun/pkg/etcd"
	reserve "icode.baidu.com/baidu/faas/kun/pkg/store/reserve"
)

// MockScheduleNodeControl is a mock of ScheduleNodeControl interface.
type MockScheduleNodeControl struct {
	ctrl     *gomock.Controller
	recorder *MockScheduleNodeControlMockRecorder
}

// MockScheduleNodeControlMockRecorder is the mock recorder for MockScheduleNodeControl.
type MockScheduleNodeControlMockRecorder struct {
	mock *MockScheduleNodeControl
}

// NewMockScheduleNodeControl creates a new mock instance.
func NewMockScheduleNodeControl(ctrl *gomock.Controller) *MockScheduleNodeControl {
	mock := &MockScheduleNodeControl{ctrl: ctrl}
	mock.recorder = &MockScheduleNodeControlMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockScheduleNodeControl) EXPECT() *MockScheduleNodeControlMockRecorder {
	return m.recorder
}

// AddTunnelPeer mocks base method.
func (m *MockScheduleNodeControl) AddTunnelPeer(arg0 *api.AddTunnelPeerRequest) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddTunnelPeer", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// AddTunnelPeer indicates an expected call of AddTunnelPeer.
func (mr *MockScheduleNodeControlMockRecorder) AddTunnelPeer(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddTunnelPeer", reflect.TypeOf((*MockScheduleNodeControl)(nil).AddTunnelPeer), arg0)
}

// CheckAndPullImages mocks base method.
func (m *MockScheduleNodeControl) CheckAndPullImages(arg0 *api.NodeInfo, arg1 []string) (map[string]bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckAndPullImages", arg0, arg1)
	ret0, _ := ret[0].(map[string]bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckAndPullImages indicates an expected call of CheckAndPullImages.
func (mr *MockScheduleNodeControlMockRecorder) CheckAndPullImages(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckAndPullImages", reflect.TypeOf((*MockScheduleNodeControl)(nil).CheckAndPullImages), arg0, arg1)
}

// CheckHealth mocks base method.
func (m *MockScheduleNodeControl) CheckHealth(arg0 *api.CheckHealthRequest) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckHealth", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// CheckHealth indicates an expected call of CheckHealth.
func (mr *MockScheduleNodeControlMockRecorder) CheckHealth(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckHealth", reflect.TypeOf((*MockScheduleNodeControl)(nil).CheckHealth), arg0)
}

// CheckNodeConditionFinishedInit mocks base method.
func (m *MockScheduleNodeControl) CheckNodeConditionFinishedInit(arg0 *api.NodeInfo) (int, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckNodeConditionFinishedInit", arg0)
	ret0, _ := ret[0].(int)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckNodeConditionFinishedInit indicates an expected call of CheckNodeConditionFinishedInit.
func (mr *MockScheduleNodeControlMockRecorder) CheckNodeConditionFinishedInit(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckNodeConditionFinishedInit", reflect.TypeOf((*MockScheduleNodeControl)(nil).CheckNodeConditionFinishedInit), arg0)
}

// CheckNodeInCondition mocks base method.
func (m *MockScheduleNodeControl) CheckNodeInCondition(arg0 *api.NodeInfo, arg1 uint) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckNodeInCondition", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// CheckNodeInCondition indicates an expected call of CheckNodeInCondition.
func (mr *MockScheduleNodeControlMockRecorder) CheckNodeInCondition(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckNodeInCondition", reflect.TypeOf((*MockScheduleNodeControl)(nil).CheckNodeInCondition), arg0, arg1)
}

// CheckRunningPodRatio mocks base method.
func (m *MockScheduleNodeControl) CheckRunningPodRatio(arg0 *api.NodeInfo, arg1 int) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckRunningPodRatio", arg0, arg1)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckRunningPodRatio indicates an expected call of CheckRunningPodRatio.
func (mr *MockScheduleNodeControlMockRecorder) CheckRunningPodRatio(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckRunningPodRatio", reflect.TypeOf((*MockScheduleNodeControl)(nil).CheckRunningPodRatio), arg0, arg1)
}

// ClearErrorTimes mocks base method.
func (m *MockScheduleNodeControl) ClearErrorTimes(arg0 string, arg1 ...string) error {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0}
	for _, a := range arg1 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ClearErrorTimes", varargs...)
	ret0, _ := ret[0].(error)
	return ret0
}

// ClearErrorTimes indicates an expected call of ClearErrorTimes.
func (mr *MockScheduleNodeControlMockRecorder) ClearErrorTimes(arg0 interface{}, arg1 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0}, arg1...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ClearErrorTimes", reflect.TypeOf((*MockScheduleNodeControl)(nil).ClearErrorTimes), varargs...)
}

// ClearReserveCacheOnNode mocks base method.
func (m *MockScheduleNodeControl) ClearReserveCacheOnNode(arg0 *api.NodeInfo) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ClearReserveCacheOnNode", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// ClearReserveCacheOnNode indicates an expected call of ClearReserveCacheOnNode.
func (mr *MockScheduleNodeControlMockRecorder) ClearReserveCacheOnNode(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ClearReserveCacheOnNode", reflect.TypeOf((*MockScheduleNodeControl)(nil).ClearReserveCacheOnNode), arg0)
}

// CoolDownPod mocks base method.
func (m *MockScheduleNodeControl) CoolDownPod(arg0 *api.ContainerInfo, arg1 *api.NodeInfo) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CoolDownPod", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// CoolDownPod indicates an expected call of CoolDownPod.
func (mr *MockScheduleNodeControlMockRecorder) CoolDownPod(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CoolDownPod", reflect.TypeOf((*MockScheduleNodeControl)(nil).CoolDownPod), arg0, arg1)
}

// CreatePods mocks base method.
func (m *MockScheduleNodeControl) CreatePods(arg0 *api.NodeInfo, arg1 int64) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreatePods", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// CreatePods indicates an expected call of CreatePods.
func (mr *MockScheduleNodeControlMockRecorder) CreatePods(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreatePods", reflect.TypeOf((*MockScheduleNodeControl)(nil).CreatePods), arg0, arg1)
}

// DeleteEntireNode mocks base method.
func (m *MockScheduleNodeControl) DeleteEntireNode(arg0 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteEntireNode", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteEntireNode indicates an expected call of DeleteEntireNode.
func (mr *MockScheduleNodeControlMockRecorder) DeleteEntireNode(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteEntireNode", reflect.TypeOf((*MockScheduleNodeControl)(nil).DeleteEntireNode), arg0)
}

// DeleteEntireNodes mocks base method.
func (m *MockScheduleNodeControl) DeleteEntireNodes() error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteEntireNodes")
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteEntireNodes indicates an expected call of DeleteEntireNodes.
func (mr *MockScheduleNodeControlMockRecorder) DeleteEntireNodes() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteEntireNodes", reflect.TypeOf((*MockScheduleNodeControl)(nil).DeleteEntireNodes))
}

// DeleteErrorTimes mocks base method.
func (m *MockScheduleNodeControl) DeleteErrorTimes(arg0, arg1, arg2 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteErrorTimes", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteErrorTimes indicates an expected call of DeleteErrorTimes.
func (mr *MockScheduleNodeControlMockRecorder) DeleteErrorTimes(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteErrorTimes", reflect.TypeOf((*MockScheduleNodeControl)(nil).DeleteErrorTimes), arg0, arg1, arg2)
}

// DeleteNode mocks base method.
func (m *MockScheduleNodeControl) DeleteNode(arg0 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteNode", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteNode indicates an expected call of DeleteNode.
func (mr *MockScheduleNodeControlMockRecorder) DeleteNode(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteNode", reflect.TypeOf((*MockScheduleNodeControl)(nil).DeleteNode), arg0)
}

// DeleteNodes mocks base method.
func (m *MockScheduleNodeControl) DeleteNodes() error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteNodes")
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteNodes indicates an expected call of DeleteNodes.
func (mr *MockScheduleNodeControlMockRecorder) DeleteNodes() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteNodes", reflect.TypeOf((*MockScheduleNodeControl)(nil).DeleteNodes))
}

// DeletePod mocks base method.
func (m *MockScheduleNodeControl) DeletePod(arg0 *api.NodeInfo, arg1 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeletePod", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeletePod indicates an expected call of DeletePod.
func (mr *MockScheduleNodeControlMockRecorder) DeletePod(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeletePod", reflect.TypeOf((*MockScheduleNodeControl)(nil).DeletePod), arg0, arg1)
}

// DeletePods mocks base method.
func (m *MockScheduleNodeControl) DeletePods(arg0 *api.NodeInfo, arg1 bool) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeletePods", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeletePods indicates an expected call of DeletePods.
func (mr *MockScheduleNodeControlMockRecorder) DeletePods(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeletePods", reflect.TypeOf((*MockScheduleNodeControl)(nil).DeletePods), arg0, arg1)
}

// GetAllNodes mocks base method.
func (m *MockScheduleNodeControl) GetAllNodes() ([]*api.NodeInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAllNodes")
	ret0, _ := ret[0].([]*api.NodeInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAllNodes indicates an expected call of GetAllNodes.
func (mr *MockScheduleNodeControlMockRecorder) GetAllNodes() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAllNodes", reflect.TypeOf((*MockScheduleNodeControl)(nil).GetAllNodes))
}

// GetColdNodeMemory mocks base method.
func (m *MockScheduleNodeControl) GetColdNodeMemory(arg0 *api.NodeInfo) (int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetColdNodeMemory", arg0)
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetColdNodeMemory indicates an expected call of GetColdNodeMemory.
func (mr *MockScheduleNodeControlMockRecorder) GetColdNodeMemory(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetColdNodeMemory", reflect.TypeOf((*MockScheduleNodeControl)(nil).GetColdNodeMemory), arg0)
}

// GetErrorTimes mocks base method.
func (m *MockScheduleNodeControl) GetErrorTimes(arg0, arg1, arg2 string) (uint64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetErrorTimes", arg0, arg1, arg2)
	ret0, _ := ret[0].(uint64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetErrorTimes indicates an expected call of GetErrorTimes.
func (mr *MockScheduleNodeControlMockRecorder) GetErrorTimes(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetErrorTimes", reflect.TypeOf((*MockScheduleNodeControl)(nil).GetErrorTimes), arg0, arg1, arg2)
}

// GetHealthyContainersOnNode mocks base method.
func (m *MockScheduleNodeControl) GetHealthyContainersOnNode(arg0 *api.NodeInfo) ([]*api.ContainerInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetHealthyContainersOnNode", arg0)
	ret0, _ := ret[0].([]*api.ContainerInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetHealthyContainersOnNode indicates an expected call of GetHealthyContainersOnNode.
func (mr *MockScheduleNodeControlMockRecorder) GetHealthyContainersOnNode(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetHealthyContainersOnNode", reflect.TypeOf((*MockScheduleNodeControl)(nil).GetHealthyContainersOnNode), arg0)
}

// GetNode mocks base method.
func (m *MockScheduleNodeControl) GetNode(arg0 string) (*api.NodeInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetNode", arg0)
	ret0, _ := ret[0].(*api.NodeInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetNode indicates an expected call of GetNode.
func (mr *MockScheduleNodeControlMockRecorder) GetNode(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetNode", reflect.TypeOf((*MockScheduleNodeControl)(nil).GetNode), arg0)
}

// GetNodeFromCache mocks base method.
func (m *MockScheduleNodeControl) GetNodeFromCache(arg0 string) *api.NodeInfo {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetNodeFromCache", arg0)
	ret0, _ := ret[0].(*api.NodeInfo)
	return ret0
}

// GetNodeFromCache indicates an expected call of GetNodeFromCache.
func (mr *MockScheduleNodeControlMockRecorder) GetNodeFromCache(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetNodeFromCache", reflect.TypeOf((*MockScheduleNodeControl)(nil).GetNodeFromCache), arg0)
}

// GetNodeHistoryRecords mocks base method.
func (m *MockScheduleNodeControl) GetNodeHistoryRecords(arg0 string, arg1 int) ([]*api.NodeInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetNodeHistoryRecords", arg0, arg1)
	ret0, _ := ret[0].([]*api.NodeInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetNodeHistoryRecords indicates an expected call of GetNodeHistoryRecords.
func (mr *MockScheduleNodeControlMockRecorder) GetNodeHistoryRecords(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetNodeHistoryRecords", reflect.TypeOf((*MockScheduleNodeControl)(nil).GetNodeHistoryRecords), arg0, arg1)
}

// GetNodesFromCache mocks base method.
func (m *MockScheduleNodeControl) GetNodesFromCache() []*api.NodeInfo {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetNodesFromCache")
	ret0, _ := ret[0].([]*api.NodeInfo)
	return ret0
}

// GetNodesFromCache indicates an expected call of GetNodesFromCache.
func (mr *MockScheduleNodeControlMockRecorder) GetNodesFromCache() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetNodesFromCache", reflect.TypeOf((*MockScheduleNodeControl)(nil).GetNodesFromCache))
}

// GetReserveByMemorySize mocks base method.
func (m *MockScheduleNodeControl) GetReserveByMemorySize(arg0 *reserve.AtomGetNodeMemoryArgs) (*reserve.AtomGetNodeMemoryRes, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetReserveByMemorySize", arg0)
	ret0, _ := ret[0].(*reserve.AtomGetNodeMemoryRes)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetReserveByMemorySize indicates an expected call of GetReserveByMemorySize.
func (mr *MockScheduleNodeControlMockRecorder) GetReserveByMemorySize(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetReserveByMemorySize", reflect.TypeOf((*MockScheduleNodeControl)(nil).GetReserveByMemorySize), arg0)
}

// GetReservedNodeMembers mocks base method.
func (m *MockScheduleNodeControl) GetReservedNodeMembers(arg0 *reserve.GetReservedNodeMembersArgs) ([]api.NodeInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetReservedNodeMembers", arg0)
	ret0, _ := ret[0].([]api.NodeInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetReservedNodeMembers indicates an expected call of GetReservedNodeMembers.
func (mr *MockScheduleNodeControlMockRecorder) GetReservedNodeMembers(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetReservedNodeMembers", reflect.TypeOf((*MockScheduleNodeControl)(nil).GetReservedNodeMembers), arg0)
}

// GetReservedPodMembers mocks base method.
func (m *MockScheduleNodeControl) GetReservedPodMembers(arg0 *reserve.GetReservedPodMembersAgrs) ([]api.PodInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetReservedPodMembers", arg0)
	ret0, _ := ret[0].([]api.PodInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetReservedPodMembers indicates an expected call of GetReservedPodMembers.
func (mr *MockScheduleNodeControlMockRecorder) GetReservedPodMembers(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetReservedPodMembers", reflect.TypeOf((*MockScheduleNodeControl)(nil).GetReservedPodMembers), arg0)
}

// GetTimeoutPods mocks base method.
func (m *MockScheduleNodeControl) GetTimeoutPods(arg0 string) (map[string]interface{}, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTimeoutPods", arg0)
	ret0, _ := ret[0].(map[string]interface{})
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTimeoutPods indicates an expected call of GetTimeoutPods.
func (mr *MockScheduleNodeControlMockRecorder) GetTimeoutPods(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTimeoutPods", reflect.TypeOf((*MockScheduleNodeControl)(nil).GetTimeoutPods), arg0)
}

// GetWarmNodeMemory mocks base method.
func (m *MockScheduleNodeControl) GetWarmNodeMemory(arg0 *api.NodeInfo) (int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetWarmNodeMemory", arg0)
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetWarmNodeMemory indicates an expected call of GetWarmNodeMemory.
func (mr *MockScheduleNodeControlMockRecorder) GetWarmNodeMemory(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetWarmNodeMemory", reflect.TypeOf((*MockScheduleNodeControl)(nil).GetWarmNodeMemory), arg0)
}

// GetWarmPodWithConcurrency mocks base method.
func (m *MockScheduleNodeControl) GetWarmPodWithConcurrency(arg0 *api.PodInfo) (int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetWarmPodWithConcurrency", arg0)
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetWarmPodWithConcurrency indicates an expected call of GetWarmPodWithConcurrency.
func (mr *MockScheduleNodeControlMockRecorder) GetWarmPodWithConcurrency(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetWarmPodWithConcurrency", reflect.TypeOf((*MockScheduleNodeControl)(nil).GetWarmPodWithConcurrency), arg0)
}

// GetZombieContainersOnNode mocks base method.
func (m *MockScheduleNodeControl) GetZombieContainersOnNode(arg0 *api.NodeInfo) ([]*api.ContainerInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetZombieContainersOnNode", arg0)
	ret0, _ := ret[0].([]*api.ContainerInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetZombieContainersOnNode indicates an expected call of GetZombieContainersOnNode.
func (mr *MockScheduleNodeControlMockRecorder) GetZombieContainersOnNode(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetZombieContainersOnNode", reflect.TypeOf((*MockScheduleNodeControl)(nil).GetZombieContainersOnNode), arg0)
}

// Hello mocks base method.
func (m *MockScheduleNodeControl) Hello(arg0 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Hello", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// Hello indicates an expected call of Hello.
func (mr *MockScheduleNodeControlMockRecorder) Hello(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Hello", reflect.TypeOf((*MockScheduleNodeControl)(nil).Hello), arg0)
}

// IncrAndCheckErrorTimes mocks base method.
func (m *MockScheduleNodeControl) IncrAndCheckErrorTimes(arg0 *etcd.IncrErrorTimesArgs) bool {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "IncrAndCheckErrorTimes", arg0)
	ret0, _ := ret[0].(bool)
	return ret0
}

// IncrAndCheckErrorTimes indicates an expected call of IncrAndCheckErrorTimes.
func (mr *MockScheduleNodeControlMockRecorder) IncrAndCheckErrorTimes(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IncrAndCheckErrorTimes", reflect.TypeOf((*MockScheduleNodeControl)(nil).IncrAndCheckErrorTimes), arg0)
}

// IncrErrorTimes mocks base method.
func (m *MockScheduleNodeControl) IncrErrorTimes(arg0, arg1, arg2 string) (uint64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "IncrErrorTimes", arg0, arg1, arg2)
	ret0, _ := ret[0].(uint64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// IncrErrorTimes indicates an expected call of IncrErrorTimes.
func (mr *MockScheduleNodeControlMockRecorder) IncrErrorTimes(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IncrErrorTimes", reflect.TypeOf((*MockScheduleNodeControl)(nil).IncrErrorTimes), arg0, arg1, arg2)
}

// IncrWarmNodeMemoryXX mocks base method.
func (m *MockScheduleNodeControl) IncrWarmNodeMemoryXX(arg0 *api.NodeInfo, arg1 int64) (int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "IncrWarmNodeMemoryXX", arg0, arg1)
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// IncrWarmNodeMemoryXX indicates an expected call of IncrWarmNodeMemoryXX.
func (mr *MockScheduleNodeControlMockRecorder) IncrWarmNodeMemoryXX(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IncrWarmNodeMemoryXX", reflect.TypeOf((*MockScheduleNodeControl)(nil).IncrWarmNodeMemoryXX), arg0, arg1)
}

// IncrWarmPodConcurrencyXX mocks base method.
func (m *MockScheduleNodeControl) IncrWarmPodConcurrencyXX(arg0 *api.PodInfo, arg1 int64) (int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "IncrWarmPodConcurrencyXX", arg0, arg1)
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// IncrWarmPodConcurrencyXX indicates an expected call of IncrWarmPodConcurrencyXX.
func (mr *MockScheduleNodeControlMockRecorder) IncrWarmPodConcurrencyXX(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IncrWarmPodConcurrencyXX", reflect.TypeOf((*MockScheduleNodeControl)(nil).IncrWarmPodConcurrencyXX), arg0, arg1)
}

// InitNode mocks base method.
func (m *MockScheduleNodeControl) InitNode(arg0 *api.NodeInfo, arg1 int64) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "InitNode", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// InitNode indicates an expected call of InitNode.
func (mr *MockScheduleNodeControlMockRecorder) InitNode(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "InitNode", reflect.TypeOf((*MockScheduleNodeControl)(nil).InitNode), arg0, arg1)
}

// IsReservedNodeExist mocks base method.
func (m *MockScheduleNodeControl) IsReservedNodeExist(arg0 *api.NodeInfo) bool {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "IsReservedNodeExist", arg0)
	ret0, _ := ret[0].(bool)
	return ret0
}

// IsReservedNodeExist indicates an expected call of IsReservedNodeExist.
func (mr *MockScheduleNodeControlMockRecorder) IsReservedNodeExist(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IsReservedNodeExist", reflect.TypeOf((*MockScheduleNodeControl)(nil).IsReservedNodeExist), arg0)
}

// IsReservedPodExist mocks base method.
func (m *MockScheduleNodeControl) IsReservedPodExist(arg0 *api.PodInfo) bool {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "IsReservedPodExist", arg0)
	ret0, _ := ret[0].(bool)
	return ret0
}

// IsReservedPodExist indicates an expected call of IsReservedPodExist.
func (mr *MockScheduleNodeControlMockRecorder) IsReservedPodExist(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IsReservedPodExist", reflect.TypeOf((*MockScheduleNodeControl)(nil).IsReservedPodExist), arg0)
}

// NewNodeAllocatingLock mocks base method.
func (m *MockScheduleNodeControl) NewNodeAllocatingLock(arg0 string, arg1 int64, arg2 ...int) etcd.Locker {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "NewNodeAllocatingLock", varargs...)
	ret0, _ := ret[0].(etcd.Locker)
	return ret0
}

// NewNodeAllocatingLock indicates an expected call of NewNodeAllocatingLock.
func (mr *MockScheduleNodeControlMockRecorder) NewNodeAllocatingLock(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "NewNodeAllocatingLock", reflect.TypeOf((*MockScheduleNodeControl)(nil).NewNodeAllocatingLock), varargs...)
}

// NewNodeLock mocks base method.
func (m *MockScheduleNodeControl) NewNodeLock(arg0 string) etcd.Locker {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "NewNodeLock", arg0)
	ret0, _ := ret[0].(etcd.Locker)
	return ret0
}

// NewNodeLock indicates an expected call of NewNodeLock.
func (mr *MockScheduleNodeControlMockRecorder) NewNodeLock(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "NewNodeLock", reflect.TypeOf((*MockScheduleNodeControl)(nil).NewNodeLock), arg0)
}

// NotifyOccupy mocks base method.
func (m *MockScheduleNodeControl) NotifyOccupy(arg0 *api.NotifyOccupyRequest) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "NotifyOccupy", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// NotifyOccupy indicates an expected call of NotifyOccupy.
func (mr *MockScheduleNodeControlMockRecorder) NotifyOccupy(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "NotifyOccupy", reflect.TypeOf((*MockScheduleNodeControl)(nil).NotifyOccupy), arg0)
}

// NotifyRelease mocks base method.
func (m *MockScheduleNodeControl) NotifyRelease(arg0 *api.NotifyReleaseRequest) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "NotifyRelease", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// NotifyRelease indicates an expected call of NotifyRelease.
func (mr *MockScheduleNodeControlMockRecorder) NotifyRelease(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "NotifyRelease", reflect.TypeOf((*MockScheduleNodeControl)(nil).NotifyRelease), arg0)
}

// OccupyWarmPod mocks base method.
func (m *MockScheduleNodeControl) OccupyWarmPod(arg0 *reserve.PodIndex, arg1, arg2 int64, arg3 string) (*reserve.PodConcurrency, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "OccupyWarmPod", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(*reserve.PodConcurrency)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// OccupyWarmPod indicates an expected call of OccupyWarmPod.
func (mr *MockScheduleNodeControlMockRecorder) OccupyWarmPod(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "OccupyWarmPod", reflect.TypeOf((*MockScheduleNodeControl)(nil).OccupyWarmPod), arg0, arg1, arg2, arg3)
}

// PurgeNode mocks base method.
func (m *MockScheduleNodeControl) PurgeNode(arg0 *api.NodeInfo, arg1 map[string]int, arg2, arg3 int) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PurgeNode", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// PurgeNode indicates an expected call of PurgeNode.
func (mr *MockScheduleNodeControlMockRecorder) PurgeNode(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PurgeNode", reflect.TypeOf((*MockScheduleNodeControl)(nil).PurgeNode), arg0, arg1, arg2, arg3)
}

// RebuildPod mocks base method.
func (m *MockScheduleNodeControl) RebuildPod(arg0 *api.NodeInfo, arg1 string) (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RebuildPod", arg0, arg1)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// RebuildPod indicates an expected call of RebuildPod.
func (mr *MockScheduleNodeControlMockRecorder) RebuildPod(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RebuildPod", reflect.TypeOf((*MockScheduleNodeControl)(nil).RebuildPod), arg0, arg1)
}

// RemColdNode mocks base method.
func (m *MockScheduleNodeControl) RemColdNode(arg0 *api.NodeInfo) (int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RemColdNode", arg0)
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// RemColdNode indicates an expected call of RemColdNode.
func (mr *MockScheduleNodeControlMockRecorder) RemColdNode(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RemColdNode", reflect.TypeOf((*MockScheduleNodeControl)(nil).RemColdNode), arg0)
}

// RemMember mocks base method.
func (m *MockScheduleNodeControl) RemMember(arg0, arg1 string) (int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RemMember", arg0, arg1)
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// RemMember indicates an expected call of RemMember.
func (mr *MockScheduleNodeControlMockRecorder) RemMember(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RemMember", reflect.TypeOf((*MockScheduleNodeControl)(nil).RemMember), arg0, arg1)
}

// RemNXGreIP mocks base method.
func (m *MockScheduleNodeControl) RemNXGreIP(arg0, arg1 string) (int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RemNXGreIP", arg0, arg1)
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// RemNXGreIP indicates an expected call of RemNXGreIP.
func (mr *MockScheduleNodeControlMockRecorder) RemNXGreIP(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RemNXGreIP", reflect.TypeOf((*MockScheduleNodeControl)(nil).RemNXGreIP), arg0, arg1)
}

// GetMCPSessionPod mocks base method.
func (m *MockScheduleNodeControl) GetMCPSessionPod(arg0, arg1 string) (*api.PodInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMCPSessionPod", arg0, arg1)
	ret0, _ := ret[0].(*api.PodInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMCPSessionPod indicates an expected call of GetMCPSessionPod.
func (mr *MockScheduleNodeControlMockRecorder) GetMCPSessionPod(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMCPSessionPod", reflect.TypeOf((*MockScheduleNodeControl)(nil).GetMCPSessionPod), arg0, arg1)
}

// SetNXMCPSessionPod mocks base method.
func (m *MockScheduleNodeControl) SetNXMCPSessionPod(arg0, arg1 string, arg2 *api.PodInfo, arg3 time.Duration) (bool, error){
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetNXMCPSessionPod", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetNXMCPSessionPod indicates an expected call of SetNXMCPSessionPod.
func (mr *MockScheduleNodeControlMockRecorder) SetNXMCPSessionPod(arg0, arg1,arg2,arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetNXMCPSessionPod", reflect.TypeOf((*MockScheduleNodeControl)(nil).SetNXMCPSessionPod), arg0, arg1, arg2, arg3)
}

// RemNXMCPSessionPod mocks base method.
func (m *MockScheduleNodeControl) RemNXMCPSessionPod(arg0, arg1 string) (int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RemNXMCPSessionPod", arg0, arg1)
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// RemNXMCPSessionPod indicates an expected call of RemNXMCPSessionPod.
func (mr *MockScheduleNodeControlMockRecorder) RemNXMCPSessionPod(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RemNXMCPSessionPod", reflect.TypeOf((*MockScheduleNodeControl)(nil).RemNXMCPSessionPod), arg0, arg1)
}

// RemWarmNode mocks base method.
func (m *MockScheduleNodeControl) RemWarmNode(arg0 *api.NodeInfo) (int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RemWarmNode", arg0)
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// RemWarmNode indicates an expected call of RemWarmNode.
func (mr *MockScheduleNodeControlMockRecorder) RemWarmNode(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RemWarmNode", reflect.TypeOf((*MockScheduleNodeControl)(nil).RemWarmNode), arg0)
}

// RemWarmNodeWithMemory mocks base method.
func (m *MockScheduleNodeControl) RemWarmNodeWithMemory(arg0 *api.NodeInfo) (int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RemWarmNodeWithMemory", arg0)
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// RemWarmNodeWithMemory indicates an expected call of RemWarmNodeWithMemory.
func (mr *MockScheduleNodeControlMockRecorder) RemWarmNodeWithMemory(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RemWarmNodeWithMemory", reflect.TypeOf((*MockScheduleNodeControl)(nil).RemWarmNodeWithMemory), arg0)
}

// RemWarmPod mocks base method.
func (m *MockScheduleNodeControl) RemWarmPod(arg0 *api.PodInfo) (int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RemWarmPod", arg0)
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// RemWarmPod indicates an expected call of RemWarmPod.
func (mr *MockScheduleNodeControlMockRecorder) RemWarmPod(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RemWarmPod", reflect.TypeOf((*MockScheduleNodeControl)(nil).RemWarmPod), arg0)
}

// RemWarmPodWithConcurrency mocks base method.
func (m *MockScheduleNodeControl) RemWarmPodWithConcurrency(arg0 *api.PodInfo) (int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RemWarmPodWithConcurrency", arg0)
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// RemWarmPodWithConcurrency indicates an expected call of RemWarmPodWithConcurrency.
func (mr *MockScheduleNodeControlMockRecorder) RemWarmPodWithConcurrency(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RemWarmPodWithConcurrency", reflect.TypeOf((*MockScheduleNodeControl)(nil).RemWarmPodWithConcurrency), arg0)
}

// RemoveTunnelPeer mocks base method.
func (m *MockScheduleNodeControl) RemoveTunnelPeer(arg0 *api.RemoveTunnelPeerRequest) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RemoveTunnelPeer", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// RemoveTunnelPeer indicates an expected call of RemoveTunnelPeer.
func (mr *MockScheduleNodeControlMockRecorder) RemoveTunnelPeer(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RemoveTunnelPeer", reflect.TypeOf((*MockScheduleNodeControl)(nil).RemoveTunnelPeer), arg0)
}

// RepairNode mocks base method.
func (m *MockScheduleNodeControl) RepairNode(arg0 *api.NodeInfo) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RepairNode", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// RepairNode indicates an expected call of RepairNode.
func (mr *MockScheduleNodeControlMockRecorder) RepairNode(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RepairNode", reflect.TypeOf((*MockScheduleNodeControl)(nil).RepairNode), arg0)
}

// ResetNode mocks base method.
func (m *MockScheduleNodeControl) ResetNode(arg0 *api.NodeInfo) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ResetNode", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// ResetNode indicates an expected call of ResetNode.
func (mr *MockScheduleNodeControlMockRecorder) ResetNode(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ResetNode", reflect.TypeOf((*MockScheduleNodeControl)(nil).ResetNode), arg0)
}

// ResetPodsOnNode mocks base method.
func (m *MockScheduleNodeControl) ResetPodsOnNode(arg0 *api.NodeInfo) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ResetPodsOnNode", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// ResetPodsOnNode indicates an expected call of ResetPodsOnNode.
func (mr *MockScheduleNodeControlMockRecorder) ResetPodsOnNode(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ResetPodsOnNode", reflect.TypeOf((*MockScheduleNodeControl)(nil).ResetPodsOnNode), arg0)
}

// ScanAllKeys mocks base method.
func (m *MockScheduleNodeControl) ScanAllKeys() ([]string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ScanAllKeys")
	ret0, _ := ret[0].([]string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ScanAllKeys indicates an expected call of ScanAllKeys.
func (mr *MockScheduleNodeControlMockRecorder) ScanAllKeys() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ScanAllKeys", reflect.TypeOf((*MockScheduleNodeControl)(nil).ScanAllKeys))
}

// ScanAllMemoryData mocks base method.
func (m *MockScheduleNodeControl) ScanAllMemoryData() ([]reserve.ScanMemoryData, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ScanAllMemoryData")
	ret0, _ := ret[0].([]reserve.ScanMemoryData)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ScanAllMemoryData indicates an expected call of ScanAllMemoryData.
func (mr *MockScheduleNodeControlMockRecorder) ScanAllMemoryData() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ScanAllMemoryData", reflect.TypeOf((*MockScheduleNodeControl)(nil).ScanAllMemoryData))
}

// ScanMemoryData mocks base method.
func (m *MockScheduleNodeControl) ScanMemoryData(arg0 string) ([]reserve.ScanMemoryData, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ScanMemoryData", arg0)
	ret0, _ := ret[0].([]reserve.ScanMemoryData)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ScanMemoryData indicates an expected call of ScanMemoryData.
func (mr *MockScheduleNodeControlMockRecorder) ScanMemoryData(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ScanMemoryData", reflect.TypeOf((*MockScheduleNodeControl)(nil).ScanMemoryData), arg0)
}

// SetColdNodeMemoryNX mocks base method.
func (m *MockScheduleNodeControl) SetColdNodeMemoryNX(arg0 *api.NodeInfo, arg1 int64) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetColdNodeMemoryNX", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetColdNodeMemoryNX indicates an expected call of SetColdNodeMemoryNX.
func (mr *MockScheduleNodeControlMockRecorder) SetColdNodeMemoryNX(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetColdNodeMemoryNX", reflect.TypeOf((*MockScheduleNodeControl)(nil).SetColdNodeMemoryNX), arg0, arg1)
}

// SetColdNodeMemoryXX mocks base method.
func (m *MockScheduleNodeControl) SetColdNodeMemoryXX(arg0 *api.NodeInfo, arg1 int64) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetColdNodeMemoryXX", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetColdNodeMemoryXX indicates an expected call of SetColdNodeMemoryXX.
func (mr *MockScheduleNodeControlMockRecorder) SetColdNodeMemoryXX(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetColdNodeMemoryXX", reflect.TypeOf((*MockScheduleNodeControl)(nil).SetColdNodeMemoryXX), arg0, arg1)
}

// SetDubiousPod mocks base method.
func (m *MockScheduleNodeControl) SetDubiousPod(arg0 *api.PodInfo) (*reserve.DubiousPodResult, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetDubiousPod", arg0)
	ret0, _ := ret[0].(*reserve.DubiousPodResult)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetDubiousPod indicates an expected call of SetDubiousPod.
func (mr *MockScheduleNodeControlMockRecorder) SetDubiousPod(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetDubiousPod", reflect.TypeOf((*MockScheduleNodeControl)(nil).SetDubiousPod), arg0)
}

// SetNXGreIP mocks base method.
func (m *MockScheduleNodeControl) SetNXGreIP(arg0, arg1 string) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetNXGreIP", arg0, arg1)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetNXGreIP indicates an expected call of SetNXGreIP.
func (mr *MockScheduleNodeControlMockRecorder) SetNXGreIP(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetNXGreIP", reflect.TypeOf((*MockScheduleNodeControl)(nil).SetNXGreIP), arg0, arg1)
}

// SetNode mocks base method.
func (m *MockScheduleNodeControl) SetNode(arg0 *api.NodeInfo) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetNode", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetNode indicates an expected call of SetNode.
func (mr *MockScheduleNodeControlMockRecorder) SetNode(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetNode", reflect.TypeOf((*MockScheduleNodeControl)(nil).SetNode), arg0)
}

// SetTimeoutPods mocks base method.
func (m *MockScheduleNodeControl) SetTimeoutPods(arg0, arg1 string, arg2 ...string) error {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SetTimeoutPods", varargs...)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetTimeoutPods indicates an expected call of SetTimeoutPods.
func (mr *MockScheduleNodeControlMockRecorder) SetTimeoutPods(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetTimeoutPods", reflect.TypeOf((*MockScheduleNodeControl)(nil).SetTimeoutPods), varargs...)
}

// SetWarmNodeMemory mocks base method.
func (m *MockScheduleNodeControl) SetWarmNodeMemory(arg0 *api.NodeInfo, arg1 int64) (int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetWarmNodeMemory", arg0, arg1)
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetWarmNodeMemory indicates an expected call of SetWarmNodeMemory.
func (mr *MockScheduleNodeControlMockRecorder) SetWarmNodeMemory(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetWarmNodeMemory", reflect.TypeOf((*MockScheduleNodeControl)(nil).SetWarmNodeMemory), arg0, arg1)
}

// SetWarmPodConcurrencyNX mocks base method.
func (m *MockScheduleNodeControl) SetWarmPodConcurrencyNX(arg0 *api.PodInfo, arg1 int64) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetWarmPodConcurrencyNX", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetWarmPodConcurrencyNX indicates an expected call of SetWarmPodConcurrencyNX.
func (mr *MockScheduleNodeControlMockRecorder) SetWarmPodConcurrencyNX(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetWarmPodConcurrencyNX", reflect.TypeOf((*MockScheduleNodeControl)(nil).SetWarmPodConcurrencyNX), arg0, arg1)
}

// SetWarmPodConcurrencyXX mocks base method.
func (m *MockScheduleNodeControl) SetWarmPodConcurrencyXX(arg0 *api.PodInfo, arg1 int64) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetWarmPodConcurrencyXX", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetWarmPodConcurrencyXX indicates an expected call of SetWarmPodConcurrencyXX.
func (mr *MockScheduleNodeControlMockRecorder) SetWarmPodConcurrencyXX(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetWarmPodConcurrencyXX", reflect.TypeOf((*MockScheduleNodeControl)(nil).SetWarmPodConcurrencyXX), arg0, arg1)
}

// SyncNodes mocks base method.
func (m *MockScheduleNodeControl) SyncNodes() error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SyncNodes")
	ret0, _ := ret[0].(error)
	return ret0
}

// SyncNodes indicates an expected call of SyncNodes.
func (mr *MockScheduleNodeControlMockRecorder) SyncNodes() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SyncNodes", reflect.TypeOf((*MockScheduleNodeControl)(nil).SyncNodes))
}
