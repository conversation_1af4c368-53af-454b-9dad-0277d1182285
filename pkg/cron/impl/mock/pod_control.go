// Code generated by MockGen. DO NOT EDIT.
// Source: icode.baidu.com/baidu/faas/kun/pkg/cron/impl (interfaces: PodControl)

// Package mock is a generated GoMock package.
package mock

import (
	gomock "github.com/golang/mock/gomock"
	api "icode.baidu.com/baidu/faas/kun/pkg/api"
	reflect "reflect"
)

// MockPodControl is a mock of PodControl interface
type MockPodControl struct {
	ctrl     *gomock.Controller
	recorder *MockPodControlMockRecorder
}

// MockPodControlMockRecorder is the mock recorder for MockPodControl
type MockPodControlMockRecorder struct {
	mock *MockPodControl
}

// NewMockPodControl creates a new mock instance
func NewMockPodControl(ctrl *gomock.Controller) *MockPodControl {
	mock := &MockPodControl{ctrl: ctrl}
	mock.recorder = &MockPodControlMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use
func (m *MockPodControl) EXPECT() *MockPodControlMockRecorder {
	return m.recorder
}

// CheckAndPullImages mocks base method
func (m *MockPodControl) CheckAndPullImages(arg0 *api.NodeInfo, arg1 []string) (map[string]bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckAndPullImages", arg0, arg1)
	ret0, _ := ret[0].(map[string]bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckAndPullImages indicates an expected call of CheckAndPullImages
func (mr *MockPodControlMockRecorder) CheckAndPullImages(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckAndPullImages", reflect.TypeOf((*MockPodControl)(nil).CheckAndPullImages), arg0, arg1)
}

// CoolDownPod mocks base method
func (m *MockPodControl) CoolDownPod(arg0 *api.ContainerInfo, arg1 *api.NodeInfo) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CoolDownPod", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// CoolDownPod indicates an expected call of CoolDownPod
func (mr *MockPodControlMockRecorder) CoolDownPod(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CoolDownPod", reflect.TypeOf((*MockPodControl)(nil).CoolDownPod), arg0, arg1)
}

// GetHealthyContainersOnNode mocks base method
func (m *MockPodControl) GetHealthyContainersOnNode(arg0 *api.NodeInfo) ([]*api.ContainerInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetHealthyContainersOnNode", arg0)
	ret0, _ := ret[0].([]*api.ContainerInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetHealthyContainersOnNode indicates an expected call of GetHealthyContainersOnNode
func (mr *MockPodControlMockRecorder) GetHealthyContainersOnNode(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetHealthyContainersOnNode", reflect.TypeOf((*MockPodControl)(nil).GetHealthyContainersOnNode), arg0)
}

// GetZombieContainersOnNode mocks base method
func (m *MockPodControl) GetZombieContainersOnNode(arg0 *api.NodeInfo) ([]*api.ContainerInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetZombieContainersOnNode", arg0)
	ret0, _ := ret[0].([]*api.ContainerInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetZombieContainersOnNode indicates an expected call of GetZombieContainersOnNode
func (mr *MockPodControlMockRecorder) GetZombieContainersOnNode(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetZombieContainersOnNode", reflect.TypeOf((*MockPodControl)(nil).GetZombieContainersOnNode), arg0)
}

// ResetNode mocks base method
func (m *MockPodControl) ResetNode(arg0 *api.NodeInfo) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ResetNode", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// ResetNode indicates an expected call of ResetNode
func (mr *MockPodControlMockRecorder) ResetNode(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ResetNode", reflect.TypeOf((*MockPodControl)(nil).ResetNode), arg0)
}
