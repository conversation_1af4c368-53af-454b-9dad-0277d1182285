// Code generated by MockGen. DO NOT EDIT.
// Source: icode.baidu.com/baidu/faas/kun/pkg/cron/impl (interfaces: ClusterControl)

// Package mock is a generated GoMock package.
package mock

import (
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	impl "icode.baidu.com/baidu/faas/kun/pkg/cron/impl"
)

// MockClusterControl is a mock of ClusterControl interface.
type MockClusterControl struct {
	ctrl     *gomock.Controller
	recorder *MockClusterControlMockRecorder
}

// MockClusterControlMockRecorder is the mock recorder for MockClusterControl.
type MockClusterControlMockRecorder struct {
	mock *MockClusterControl
}

// NewMockClusterControl creates a new mock instance.
func NewMockClusterControl(ctrl *gomock.Controller) *MockClusterControl {
	mock := &MockClusterControl{ctrl: ctrl}
	mock.recorder = &MockClusterControlMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockClusterControl) EXPECT() *MockClusterControlMockRecorder {
	return m.recorder
}

// ApplyDaemonSet mocks base method.
func (m *MockClusterControl) ApplyDaemonSet(arg0 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ApplyDaemonSet", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// ApplyDaemonSet indicates an expected call of ApplyDaemonSet.
func (mr *MockClusterControlMockRecorder) ApplyDaemonSet(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ApplyDaemonSet", reflect.TypeOf((*MockClusterControl)(nil).ApplyDaemonSet), arg0)
}

// GetImageListV2 mocks base method.
func (m *MockClusterControl) GetImageListV2() ([]interface{}, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetImageListV2")
	ret0, _ := ret[0].([]interface{})
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetImageListV2 indicates an expected call of GetImageListV2.
func (mr *MockClusterControlMockRecorder) GetImageListV2() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetImageListV2", reflect.TypeOf((*MockClusterControl)(nil).GetImageListV2))
}

// ListNodes mocks base method.
func (m *MockClusterControl) ListNodes() (*impl.ClusterNodes, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListNodes")
	ret0, _ := ret[0].(*impl.ClusterNodes)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListNodes indicates an expected call of ListNodes.
func (mr *MockClusterControlMockRecorder) ListNodes() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListNodes", reflect.TypeOf((*MockClusterControl)(nil).ListNodes))
}

// ScalingDown mocks base method.
func (m *MockClusterControl) ScalingDown(arg0 []string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ScalingDown", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// ScalingDown indicates an expected call of ScalingDown.
func (mr *MockClusterControlMockRecorder) ScalingDown(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ScalingDown", reflect.TypeOf((*MockClusterControl)(nil).ScalingDown), arg0)
}

// ScalingUp mocks base method.
func (m *MockClusterControl) ScalingUp(arg0 int) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ScalingUp", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// ScalingUp indicates an expected call of ScalingUp.
func (mr *MockClusterControlMockRecorder) ScalingUp(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ScalingUp", reflect.TypeOf((*MockClusterControl)(nil).ScalingUp), arg0)
}
