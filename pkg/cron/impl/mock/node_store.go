// Code generated by MockGen. DO NOT EDIT.
// Source: icode.baidu.com/baidu/faas/kun/pkg/cron/impl (interfaces: NodeStore)

// Package mock is a generated GoMock package.
package mock

import (
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	api "icode.baidu.com/baidu/faas/kun/pkg/api"
	etcd "icode.baidu.com/baidu/faas/kun/pkg/etcd"
)

// MockNodeStore is a mock of NodeStore interface.
type MockNodeStore struct {
	ctrl     *gomock.Controller
	recorder *MockNodeStoreMockRecorder
}

// MockNodeStoreMockRecorder is the mock recorder for MockNodeStore.
type MockNodeStoreMockRecorder struct {
	mock *MockNodeStore
}

// NewMockNodeStore creates a new mock instance.
func NewMockNodeStore(ctrl *gomock.Controller) *MockNodeStore {
	mock := &MockNodeStore{ctrl: ctrl}
	mock.recorder = &MockNodeStoreMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockNodeStore) EXPECT() *MockNodeStoreMockRecorder {
	return m.recorder
}

// ClearErrorTimes mocks base method.
func (m *MockNodeStore) ClearErrorTimes(arg0 string, arg1 ...string) error {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0}
	for _, a := range arg1 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ClearErrorTimes", varargs...)
	ret0, _ := ret[0].(error)
	return ret0
}

// ClearErrorTimes indicates an expected call of ClearErrorTimes.
func (mr *MockNodeStoreMockRecorder) ClearErrorTimes(arg0 interface{}, arg1 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0}, arg1...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ClearErrorTimes", reflect.TypeOf((*MockNodeStore)(nil).ClearErrorTimes), varargs...)
}

// DeleteEntireNode mocks base method.
func (m *MockNodeStore) DeleteEntireNode(arg0 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteEntireNode", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteEntireNode indicates an expected call of DeleteEntireNode.
func (mr *MockNodeStoreMockRecorder) DeleteEntireNode(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteEntireNode", reflect.TypeOf((*MockNodeStore)(nil).DeleteEntireNode), arg0)
}

// DeleteEntireNodes mocks base method.
func (m *MockNodeStore) DeleteEntireNodes() error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteEntireNodes")
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteEntireNodes indicates an expected call of DeleteEntireNodes.
func (mr *MockNodeStoreMockRecorder) DeleteEntireNodes() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteEntireNodes", reflect.TypeOf((*MockNodeStore)(nil).DeleteEntireNodes))
}

// DeleteErrorTimes mocks base method.
func (m *MockNodeStore) DeleteErrorTimes(arg0, arg1, arg2 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteErrorTimes", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteErrorTimes indicates an expected call of DeleteErrorTimes.
func (mr *MockNodeStoreMockRecorder) DeleteErrorTimes(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteErrorTimes", reflect.TypeOf((*MockNodeStore)(nil).DeleteErrorTimes), arg0, arg1, arg2)
}

// DeleteNode mocks base method.
func (m *MockNodeStore) DeleteNode(arg0 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteNode", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteNode indicates an expected call of DeleteNode.
func (mr *MockNodeStoreMockRecorder) DeleteNode(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteNode", reflect.TypeOf((*MockNodeStore)(nil).DeleteNode), arg0)
}

// DeleteNodes mocks base method.
func (m *MockNodeStore) DeleteNodes() error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteNodes")
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteNodes indicates an expected call of DeleteNodes.
func (mr *MockNodeStoreMockRecorder) DeleteNodes() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteNodes", reflect.TypeOf((*MockNodeStore)(nil).DeleteNodes))
}

// GetAllNodes mocks base method.
func (m *MockNodeStore) GetAllNodes() ([]*api.NodeInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAllNodes")
	ret0, _ := ret[0].([]*api.NodeInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAllNodes indicates an expected call of GetAllNodes.
func (mr *MockNodeStoreMockRecorder) GetAllNodes() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAllNodes", reflect.TypeOf((*MockNodeStore)(nil).GetAllNodes))
}

// GetErrorTimes mocks base method.
func (m *MockNodeStore) GetErrorTimes(arg0, arg1, arg2 string) (uint64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetErrorTimes", arg0, arg1, arg2)
	ret0, _ := ret[0].(uint64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetErrorTimes indicates an expected call of GetErrorTimes.
func (mr *MockNodeStoreMockRecorder) GetErrorTimes(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetErrorTimes", reflect.TypeOf((*MockNodeStore)(nil).GetErrorTimes), arg0, arg1, arg2)
}

// GetNode mocks base method.
func (m *MockNodeStore) GetNode(arg0 string) (*api.NodeInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetNode", arg0)
	ret0, _ := ret[0].(*api.NodeInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetNode indicates an expected call of GetNode.
func (mr *MockNodeStoreMockRecorder) GetNode(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetNode", reflect.TypeOf((*MockNodeStore)(nil).GetNode), arg0)
}

// GetNodeFromCache mocks base method.
func (m *MockNodeStore) GetNodeFromCache(arg0 string) *api.NodeInfo {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetNodeFromCache", arg0)
	ret0, _ := ret[0].(*api.NodeInfo)
	return ret0
}

// GetNodeFromCache indicates an expected call of GetNodeFromCache.
func (mr *MockNodeStoreMockRecorder) GetNodeFromCache(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetNodeFromCache", reflect.TypeOf((*MockNodeStore)(nil).GetNodeFromCache), arg0)
}

// GetNodeHistoryRecords mocks base method.
func (m *MockNodeStore) GetNodeHistoryRecords(arg0 string, arg1 int) ([]*api.NodeInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetNodeHistoryRecords", arg0, arg1)
	ret0, _ := ret[0].([]*api.NodeInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetNodeHistoryRecords indicates an expected call of GetNodeHistoryRecords.
func (mr *MockNodeStoreMockRecorder) GetNodeHistoryRecords(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetNodeHistoryRecords", reflect.TypeOf((*MockNodeStore)(nil).GetNodeHistoryRecords), arg0, arg1)
}

// GetNodesFromCache mocks base method.
func (m *MockNodeStore) GetNodesFromCache() []*api.NodeInfo {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetNodesFromCache")
	ret0, _ := ret[0].([]*api.NodeInfo)
	return ret0
}

// GetNodesFromCache indicates an expected call of GetNodesFromCache.
func (mr *MockNodeStoreMockRecorder) GetNodesFromCache() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetNodesFromCache", reflect.TypeOf((*MockNodeStore)(nil).GetNodesFromCache))
}

// GetTimeoutPods mocks base method.
func (m *MockNodeStore) GetTimeoutPods(arg0 string) (map[string]interface{}, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTimeoutPods", arg0)
	ret0, _ := ret[0].(map[string]interface{})
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTimeoutPods indicates an expected call of GetTimeoutPods.
func (mr *MockNodeStoreMockRecorder) GetTimeoutPods(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTimeoutPods", reflect.TypeOf((*MockNodeStore)(nil).GetTimeoutPods), arg0)
}

// IncrAndCheckErrorTimes mocks base method.
func (m *MockNodeStore) IncrAndCheckErrorTimes(arg0 *etcd.IncrErrorTimesArgs) bool {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "IncrAndCheckErrorTimes", arg0)
	ret0, _ := ret[0].(bool)
	return ret0
}

// IncrAndCheckErrorTimes indicates an expected call of IncrAndCheckErrorTimes.
func (mr *MockNodeStoreMockRecorder) IncrAndCheckErrorTimes(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IncrAndCheckErrorTimes", reflect.TypeOf((*MockNodeStore)(nil).IncrAndCheckErrorTimes), arg0)
}

// IncrErrorTimes mocks base method.
func (m *MockNodeStore) IncrErrorTimes(arg0, arg1, arg2 string) (uint64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "IncrErrorTimes", arg0, arg1, arg2)
	ret0, _ := ret[0].(uint64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// IncrErrorTimes indicates an expected call of IncrErrorTimes.
func (mr *MockNodeStoreMockRecorder) IncrErrorTimes(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IncrErrorTimes", reflect.TypeOf((*MockNodeStore)(nil).IncrErrorTimes), arg0, arg1, arg2)
}

// NewNodeAllocatingLock mocks base method.
func (m *MockNodeStore) NewNodeAllocatingLock(arg0 string, arg1 int64, arg2 ...int) etcd.Locker {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "NewNodeAllocatingLock", varargs...)
	ret0, _ := ret[0].(etcd.Locker)
	return ret0
}

// NewNodeAllocatingLock indicates an expected call of NewNodeAllocatingLock.
func (mr *MockNodeStoreMockRecorder) NewNodeAllocatingLock(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "NewNodeAllocatingLock", reflect.TypeOf((*MockNodeStore)(nil).NewNodeAllocatingLock), varargs...)
}

// NewNodeLock mocks base method.
func (m *MockNodeStore) NewNodeLock(arg0 string) etcd.Locker {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "NewNodeLock", arg0)
	ret0, _ := ret[0].(etcd.Locker)
	return ret0
}

// NewNodeLock indicates an expected call of NewNodeLock.
func (mr *MockNodeStoreMockRecorder) NewNodeLock(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "NewNodeLock", reflect.TypeOf((*MockNodeStore)(nil).NewNodeLock), arg0)
}

// SetNode mocks base method.
func (m *MockNodeStore) SetNode(arg0 *api.NodeInfo) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetNode", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetNode indicates an expected call of SetNode.
func (mr *MockNodeStoreMockRecorder) SetNode(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetNode", reflect.TypeOf((*MockNodeStore)(nil).SetNode), arg0)
}

// SetTimeoutPods mocks base method.
func (m *MockNodeStore) SetTimeoutPods(arg0, arg1 string, arg2 ...string) error {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SetTimeoutPods", varargs...)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetTimeoutPods indicates an expected call of SetTimeoutPods.
func (mr *MockNodeStoreMockRecorder) SetTimeoutPods(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetTimeoutPods", reflect.TypeOf((*MockNodeStore)(nil).SetTimeoutPods), varargs...)
}

// SyncNodes mocks base method.
func (m *MockNodeStore) SyncNodes() error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SyncNodes")
	ret0, _ := ret[0].(error)
	return ret0
}

// SyncNodes indicates an expected call of SyncNodes.
func (mr *MockNodeStoreMockRecorder) SyncNodes() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SyncNodes", reflect.TypeOf((*MockNodeStore)(nil).SyncNodes))
}
