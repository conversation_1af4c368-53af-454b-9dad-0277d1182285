apiVersion: apps/v1
kind: DaemonSet
metadata:
  labels:
    app: funclet
    hostNetwork: 'true'
    port: '8231'
  name: funclet
  namespace: faas
spec:
  selector:
    matchLabels:
      app: funclet
  template:
    metadata:
      labels:
        app: funclet
        hostNetwork: 'true'
        port: '8231'
    spec:
      containers:
        - args:
            - '--bind-address=0.0.0.0'
            - '--cache-path=/var/faas/cache'
            - '--conf-path=/var/faas/conf'
            - '--forbid-access-ip='
            - '--invoker=/var/run/faas/.server.sock'
            - '--invoker-port=8200'
            - '--listen=/var/run/faas/.funclet.sock'
            - '--nsmount=/nsmount'
            - '--port=8231'
            - '--runner-cmd=/init'
            - '--runner-matcher=/init %s'
            - '--runtime-path=/var/faas/runtime'
            - '--tcode-path=/var/task'
            - '--tconf-path=/etc/faas'
            - '--tmp-path=/var/faas/tmp'
            - '--traffic-excludes=**********/10,***********/16'
            - '--truntime-path=/var/runtime'
            - '--enable-timetrack'
            - '--logtostderr'
            - '--banat-cidr=**********/21'
            - '--k8s-endpoints=************,************,*************,*************'
            - '-v9'
          command:
            - ./funclet
          image: 'hub.baidubce.com/cfc/funclet:ba7214'
          name: funclet
          ports:
            - containerPort: 8231
          resources:
            limits:
              cpu: 500m
              memory: 384Mi
            requests:
              cpu: 100m
              memory: 384Mi
          env:
            - name: FAAS_POD_IP
              valueFrom:
                fieldRef:
                  fieldPath: status.podIP
            - name: NSENTER_PID
              value: "1"
          securityContext:
            privileged: true
          volumeMounts:
            - mountPath: /var/faas
              name: faas
            - mountPath: /var/run/faas
              name: run
            - name: inner-run
              mountPath: /var/run/faas-inner
            - mountPath: /var/run/docker.sock
              name: docker-run
            - mountPath: /etc/localtime
              name: prc-zoneinfo
            - mountPath: /var/cgroup
              name: host-cgroup
      hostNetwork: true
      hostPID: true
      imagePullSecrets:
        - name: cfc-resource-ccr
      terminationGracePeriodSeconds: 5
      volumes:
        - hostPath:
            path: /var/faas
          name: faas
        - hostPath:
            path: /var/faas/invoker/run
          name: run
        - name: inner-run
          hostPath:
            path: /var/faas/invoker/inner
        - hostPath:
            path: /var/run/docker.sock
          name: docker-run
        - hostPath:
            path: /usr/share/zoneinfo/Asia/Shanghai
          name: prc-zoneinfo
        - hostPath:
            path: /sys/fs/cgroup
          name: host-cgroup
  updateStrategy:
    type: OnDelete
