package mock

import (
	"fmt"

	"icode.baidu.com/baidu/faas/kun/pkg/api"
	"icode.baidu.com/baidu/faas/kun/pkg/etcd/mock"
)

var Node = &api.NodeInfo{
	ID:                 "node1",
	Name:               "dummy",
	ClusterLabels:      map[string]string{},
	NodeLabels:         map[string]string{api.LabelUserID: "user1"},
	OriginalMemorySize: 128,
	MemoryAvailable:    1280,
	FloatingIP:         "***********",
	InstanceShortId:    "1234",
	CceClusterUUID:     "cce1",
	State:              api.NodeStateScheduleBusy,
}

type EtcdClient struct {
	mock.EtcdMockClient
}

func NewEtcdClient() *EtcdClient {
	client := &EtcdClient{}
	client.InitEtcdData()
	return client
}

func (c *EtcdClient) InitEtcdData() {
	c.ClearEtcdData()

	k8sCnt := 5
	k8sArr := make([]api.K8sInfo, k8sCnt)
	for i := 0; i < k8sCnt; i++ {
		k8s := &k8sArr[i]
		k8s.CceClusterUUID = fmt.Sprintf("k8s%d", i)
		k8s.MatchLabels = make(map[string]string)
		c.K8sMap[k8s.CceClusterUUID] = k8s

		nodeCnt := 10
		for j := 0; j < nodeCnt; j++ {
			var node api.NodeInfo
			node.Name = fmt.Sprintf("node%d", j)
			node.ID = k8s.CceClusterUUID + ":" + node.Name
			c.NodeMap[node.ID] = &node
		}
	}
}

func (c *EtcdClient) ClearEtcdData() {
	c.K8sMap = make(map[string]*api.K8sInfo)
	c.NodeMap = make(map[string]*api.NodeInfo)
	c.KeyValue = make(map[string]string)
	c.ErrorTimesMap = make(map[string]map[string]uint64)
}
