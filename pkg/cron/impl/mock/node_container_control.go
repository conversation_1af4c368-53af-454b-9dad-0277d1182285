// Code generated by MockGen. DO NOT EDIT.
// Source: icode.baidu.com/baidu/faas/kun/pkg/cron/impl (interfaces: NodeContainerControl)

// Package mock is a generated GoMock package.
package mock

import (
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	api "icode.baidu.com/baidu/faas/kun/pkg/api"
)

// MockNodeContainerControl is a mock of NodeContainerControl interface.
type MockNodeContainerControl struct {
	ctrl     *gomock.Controller
	recorder *MockNodeContainerControlMockRecorder
}

// MockNodeContainerControlMockRecorder is the mock recorder for MockNodeContainerControl.
type MockNodeContainerControlMockRecorder struct {
	mock *MockNodeContainerControl
}

// NewMockNodeContainerControl creates a new mock instance.
func NewMockNodeContainerControl(ctrl *gomock.Controller) *MockNodeContainerControl {
	mock := &MockNodeContainerControl{ctrl: ctrl}
	mock.recorder = &MockNodeContainerControlMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockNodeContainerControl) EXPECT() *MockNodeContainerControlMockRecorder {
	return m.recorder
}

// CheckNodeConditionFinishedInit mocks base method.
func (m *MockNodeContainerControl) CheckNodeConditionFinishedInit(arg0 *api.NodeInfo) (int, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckNodeConditionFinishedInit", arg0)
	ret0, _ := ret[0].(int)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckNodeConditionFinishedInit indicates an expected call of CheckNodeConditionFinishedInit.
func (mr *MockNodeContainerControlMockRecorder) CheckNodeConditionFinishedInit(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckNodeConditionFinishedInit", reflect.TypeOf((*MockNodeContainerControl)(nil).CheckNodeConditionFinishedInit), arg0)
}

// CheckNodeInCondition mocks base method.
func (m *MockNodeContainerControl) CheckNodeInCondition(arg0 *api.NodeInfo, arg1 uint) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckNodeInCondition", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// CheckNodeInCondition indicates an expected call of CheckNodeInCondition.
func (mr *MockNodeContainerControlMockRecorder) CheckNodeInCondition(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckNodeInCondition", reflect.TypeOf((*MockNodeContainerControl)(nil).CheckNodeInCondition), arg0, arg1)
}

// CheckRunningPodRatio mocks base method.
func (m *MockNodeContainerControl) CheckRunningPodRatio(arg0 *api.NodeInfo, arg1 int) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckRunningPodRatio", arg0, arg1)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckRunningPodRatio indicates an expected call of CheckRunningPodRatio.
func (mr *MockNodeContainerControlMockRecorder) CheckRunningPodRatio(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckRunningPodRatio", reflect.TypeOf((*MockNodeContainerControl)(nil).CheckRunningPodRatio), arg0, arg1)
}

// CreatePods mocks base method.
func (m *MockNodeContainerControl) CreatePods(arg0 *api.NodeInfo, arg1 int64) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreatePods", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// CreatePods indicates an expected call of CreatePods.
func (mr *MockNodeContainerControlMockRecorder) CreatePods(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreatePods", reflect.TypeOf((*MockNodeContainerControl)(nil).CreatePods), arg0, arg1)
}

// DeletePod mocks base method.
func (m *MockNodeContainerControl) DeletePod(arg0 *api.NodeInfo, arg1 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeletePod", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeletePod indicates an expected call of DeletePod.
func (mr *MockNodeContainerControlMockRecorder) DeletePod(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeletePod", reflect.TypeOf((*MockNodeContainerControl)(nil).DeletePod), arg0, arg1)
}

// DeletePods mocks base method.
func (m *MockNodeContainerControl) DeletePods(arg0 *api.NodeInfo, arg1 bool) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeletePods", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeletePods indicates an expected call of DeletePods.
func (mr *MockNodeContainerControlMockRecorder) DeletePods(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeletePods", reflect.TypeOf((*MockNodeContainerControl)(nil).DeletePods), arg0, arg1)
}

// InitNode mocks base method.
func (m *MockNodeContainerControl) InitNode(arg0 *api.NodeInfo, arg1 int64) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "InitNode", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// InitNode indicates an expected call of InitNode.
func (mr *MockNodeContainerControlMockRecorder) InitNode(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "InitNode", reflect.TypeOf((*MockNodeContainerControl)(nil).InitNode), arg0, arg1)
}

// RebuildPod mocks base method.
func (m *MockNodeContainerControl) RebuildPod(arg0 *api.NodeInfo, arg1 string) (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RebuildPod", arg0, arg1)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// RebuildPod indicates an expected call of RebuildPod.
func (mr *MockNodeContainerControlMockRecorder) RebuildPod(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RebuildPod", reflect.TypeOf((*MockNodeContainerControl)(nil).RebuildPod), arg0, arg1)
}

// RepairNode mocks base method.
func (m *MockNodeContainerControl) RepairNode(arg0 *api.NodeInfo) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RepairNode", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// RepairNode indicates an expected call of RepairNode.
func (mr *MockNodeContainerControlMockRecorder) RepairNode(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RepairNode", reflect.TypeOf((*MockNodeContainerControl)(nil).RepairNode), arg0)
}
