/*
 * @Author: your name
 * @Date: 2022-02-14 14:20:10
 * @LastEditTime: 2022-02-25 11:18:23
 * @LastEditors: your name
 * @Description: 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 * @FilePath: /kun/pkg/cron/impl/type.go
 */
package impl

import (
	"icode.baidu.com/baidu/faas/kun/pkg/api"
	"icode.baidu.com/baidu/faas/kun/pkg/etcd"
	proxy "icode.baidu.com/baidu/faas/kun/pkg/proxyagent/client"
	"icode.baidu.com/baidu/faas/kun/pkg/store/reserve"
)

//go:generate mockgen -destination=./mock/node_store.go -package=mock icode.baidu.com/baidu/faas/kun/pkg/cron/impl NodeStore
type NodeStore interface {
	etcd.NodeInterface
}

//go:generate mockgen -destination=./mock/cluster_info_store.go -package=mock icode.baidu.com/baidu/faas/kun/pkg/cron/impl ClusterInfoStore
type ClusterInfoStore interface {
	ListClusterInfo() ([]*api.K8sInfo, error)
}

type PodControl interface {
	ResetNode(node *api.NodeInfo) error
	CoolDownPod(container *api.ContainerInfo, node *api.NodeInfo) error

	GetHealthyContainersOnNode(node *api.NodeInfo) ([]*api.ContainerInfo, error)
	GetZombieContainersOnNode(node *api.NodeInfo) ([]*api.ContainerInfo, error)
	CheckAndPullImages(node *api.NodeInfo, images []string) (map[string]bool, error)
}

//go:generate mockgen -destination=./mock/schedule_node_control.go -package=mock icode.baidu.com/baidu/faas/kun/pkg/cron/impl ScheduleNodeControl
type ScheduleNodeControl interface {
	PodControl
	NodeContainerControl
	reserve.ReserveManager
	etcd.NodeInterface
	proxy.ProxyAgentInterface

	PurgeNode(node *api.NodeInfo, sourcePodExpire map[string]int, defaultPodExpire, functionTTL int) (err error)
	ResetPodsOnNode(node *api.NodeInfo) error
	ClearReserveCacheOnNode(node *api.NodeInfo) error
}

type NodeConditionType int

const (
	NodeConditionPreparedForInit uint = iota
	NodeConditionFinishedInit
	NodeConditionCorrectRunner
)

//go:generate mockgen -destination=./mock/node_container_control.go -package=mock icode.baidu.com/baidu/faas/kun/pkg/cron/impl NodeContainerControl
type NodeContainerControl interface {
	CheckNodeInCondition(node *api.NodeInfo, t uint) error
	InitNode(node *api.NodeInfo, memorySize int64) error
	RepairNode(node *api.NodeInfo) error
	CreatePods(node *api.NodeInfo, num int64) error
	DeletePod(node *api.NodeInfo, podName string) error
	DeletePods(node *api.NodeInfo, immediately bool) error
	RebuildPod(node *api.NodeInfo, podName string) (string, error)
	CheckRunningPodRatio(node *api.NodeInfo, runningPodNum int) (bool, error)
	CheckNodeConditionFinishedInit(node *api.NodeInfo) (int, error)
}

type ClusterNodes struct {
	Nodes                      []*api.NodeInfo          // 可用node列表
	WaitScaleDownNodes         map[string]*api.NodeInfo // 待缩容cce node列表
	PendingCount               uint                     // 准备中的node数量，一般是在扩容过程中
	UnavailableCount           uint                     // 由于属性错误而不可用的node数量
	ResourcePoolToPendingCount map[string]uint          // 各个资源池中pending的node数量
}

//go:generate mockgen -destination=./mock/cluster_control.go -package=mock icode.baidu.com/baidu/faas/kun/pkg/cron/impl ClusterControl
type ClusterControl interface {
	ScalingUp(number int) error
	ScalingDown(nodeIDs []string) error
	ListNodes() (*ClusterNodes, error)
	ApplyDaemonSet(dsName string) (err error)
	GetImageListV2() ([]interface{}, error)
	ScalingUpWithResourcePool(resourcePool string, number int) error
}
