package impl

import (
	"errors"
	"fmt"
	"os"
	"strings"
	"time"

	"gopkg.in/inf.v0"
	v1 "k8s.io/api/core/v1"
	apierrs "k8s.io/apimachinery/pkg/api/errors"
	"k8s.io/apimachinery/pkg/api/resource"
	"k8s.io/apimachinery/pkg/watch"

	"icode.baidu.com/baidu/faas/kun/cmd/cron/app/options"
	"icode.baidu.com/baidu/faas/kun/pkg/api"
	"icode.baidu.com/baidu/faas/kun/pkg/util/logs"
)

var (
	memoryReserved      = resource.MustParse("500Mi")
	defaultMaxPodDeploy = 35 // 2核8G机器可建的 pod 数量
)

// IsAllContainersCold 在purge操作后使用，判断是否所有容器都已被回收，非warm或warmup状态都属于已被回收
func IsAllContainersCold(containers []*api.ContainerInfo) bool {
	for _, container := range containers {
		if container.Status == api.ContainerStatusWarmup || container.Status == api.ContainerStatusWarm {
			return false
		}
	}
	return true
}

// IsAllContainersIdle 判断所有容器是否空闲，不在warm up状态或者warm状态没有运行都认为是空闲
func IsAllContainersIdle(containers []*api.ContainerInfo) bool {
	for _, container := range containers {
		// warm up流程中
		if container.Status == api.ContainerStatusWarmup {
			return false
		}

		// 正在执行函数
		if container.Status == api.ContainerStatusWarm && container.RuntimeInfo != nil &&
			container.RequestID != "" {
			return false
		}
	}
	return true
}

// IsNodeStale 判断node是否超出最长存活时间
func IsNodeStale(node *api.NodeInfo, maxNodeExpire int) bool {
	maxNodeDuration := time.Duration(maxNodeExpire) * time.Second
	nodeExpireTime := node.StartTime.Add(maxNodeDuration) // node过期时间点
	if nodeExpireTime.After(time.Now()) {
		// node还年轻
		return false
	}
	return true
}

// isContainerExpired 判断container是否过期
func isContainerExpired(container *api.ContainerInfo, idleDeadline, functionDeadline, statusUpdateDeadline int64,
	logger *logs.Logger) bool {
	switch container.Status {
	case api.ContainerStatusWarmup:
		if container.RuntimeInfo == nil { // 缺少runtime info，直接回收
			logger.Warnf("[isContainerExpired] warmup pod %v has no runtime info", container.Hostname)
			return true
		} else if container.LastAccessTime > statusUpdateDeadline { // warmup还未超时
			logger.V(6).Infof("[isContainerExpired] warmup pod %v is warming up", container.Hostname)
			return false
		}
		logger.V(6).Infof("[isContainerExpired] warmup pod %v warm up timeout", container.Hostname)
		return true

	case api.ContainerStatusWarm:
		if container.RuntimeInfo == nil { // 缺少runtime info，直接回收
			logger.Warnf("[isContainerExpired] warm pod %v has no runtime info", container.Hostname)
			return true
		} else if container.CommitID == "" { // 刚warm up完毕，还未更新属性
			if container.LastAccessTime > statusUpdateDeadline { // 更新还未超时
				logger.V(6).Infof("[isContainerExpired] warm pod %v is upgrading", container.Hostname)
				return false
			}
		} else if container.RequestID != "" { // 正在执行任务
			if container.LastAccessTime > functionDeadline { // 运行还未超时
				logger.V(6).Infof("[isContainerExpired] warm pod %v is running", container.Hostname)
				return false
			}
		} else { // 未执行任务
			if container.LastAccessTime > idleDeadline { // 空闲未超时
				logger.V(6).Infof("[isContainerExpired] warm pod %v is not running", container.Hostname)
				return false
			}
		}
		logger.V(6).Infof("[isContainerExpired] warm pod %v idle timeout", container.Hostname)
		return true

	case api.ContainerStatusSick:
		logger.V(6).Infof("[isContainerExpired] sick pod %v", container.Hostname)
		if container.RuntimeInfo != nil && container.ServiceType == api.ServiceTypeCFCKata {
			return true
		} else {
			return false
		}

	case api.ContainerStatusDisconnected:
		logger.V(6).Infof("[isContainerExpired] pod %v status disconnected", container.Hostname)
		return true

	case api.ContainerStatusZombie:
		logger.V(6).Infof("[isContainerExpired] pod %v status zombie", container.Hostname)
		return true

	case api.ContainerStatusMemoryMismatch:
		logger.V(6).Infof("[isContainerExpired] pod %v status memory mismatch", container.Hostname)
		return true

	case api.ContainerStatusCold:
		logger.V(6).Infof("[isContainerExpired] pod %v status cold", container.Hostname)
		return false

	default:
		return false
	}
}

type podDeadline struct {
	idleDeadline         int64 // pod空闲期限
	functionDeadline     int64 // 函数最后运行期限
	statusUpdateDeadline int64 // 容器idle过期时间+最长运行时间
	coolDownDeadline     int64 // pod重置兜底时间，即 idle过期时间+最长运行时间+30秒
}

// genPodDeadline 按配置生成pod回收所需的各个判断条件
func genPodDeadline(podExpire, functionTTL int) *podDeadline {
	// 根据podExpire设置函数最后运行时间，如函数Timeout=900，则podExpire=functionDeadline=900
	// 防止函数运行时提前退出 Process exited before completing request
	functionDeadline := functionTTL
	if podExpire > functionTTL {
		functionDeadline = podExpire
	}
	return &podDeadline{
		idleDeadline:         toAccessTime(podExpire),
		functionDeadline:     toAccessTime(functionDeadline),
		statusUpdateDeadline: toAccessTime(podExpire + functionTTL),
		coolDownDeadline:     toAccessTime(podExpire + functionTTL + 30),
	}
}

// toAccessTime 由秒数算出pod过期访问时间点
func toAccessTime(sec int) int64 {
	duration := time.Duration(sec) * time.Second
	return time.Now().Add(-duration).UnixNano() / int64(time.Millisecond)
}

/*计算初始的pod数量
1、首先按照内存计算pod数量，再计算cpu request的阈值，如果大于cpuRequestLowerLimit，那么就按照memory划分，podnumber=podCountByMemory,并更新originalCpuSize
2、如果小于cpuRequestLowerLimit，那么podNumber=CpuAvailable/cpuRequestLowerLimit
3、最后根据公式计算，podCount是etcd里面设置的MaxPodDeploy，可创建的pod数量podsNumAvailable以及计算的podNumber三者中的最小值
podCount = min(m.k8sInfo.Flavors.MaxPodDeploy, podNumber,podsNumAvailable)
*/

func ComputeInitDeployPodCount(node *api.NodeInfo, flavorList []api.BccFlavor) (podNumber int64) {
	logs.V(9).Infof("[computePodCount] nodeID: %s  flavorList: [%+v] node.Flavor: [%+v]", node.ID, flavorList, node.Flavor)
	maxPodDeploy := 0
	var cpuRequestLowerLimit int
	var nodeUsedRatio float64
	for _, flavor := range flavorList {
		if node.Flavor.Cpu == flavor.Cpu && node.Flavor.Memory == flavor.Memory {
			maxPodDeploy = flavor.MaxPodDeploy
			cpuRequestLowerLimit = flavor.CpuRequestLowerLimit
			node.Flavor.CpuRequestLowerLimit = cpuRequestLowerLimit
			node.Flavor.RunningPodRatio = flavor.RunningPodRatio
			nodeUsedRatio = flavor.NodeUsedRatio
			node.Flavor.NodeUsedRatio = flavor.NodeUsedRatio
			break
		}
	}
	var podCountByMemory int64 = 0
	var originalCpuSize int64 = 0
	if node.OriginalMemorySize > 0 {
		podCountByMemory = node.MemoryAvailable / node.OriginalMemorySize
	}
	if podCountByMemory > 0 {
		originalCpuSize = node.CpuAvailable / podCountByMemory
	}
	if int(originalCpuSize) >= cpuRequestLowerLimit {
		podNumber = podCountByMemory
		podNumber = min(podNumber, (int64(maxPodDeploy)))
		podNumber = min(podNumber, (node.PodsNumAvailable))
		node.OriginalCpuSize = originalCpuSize
		//node使用率
		podNumber = int64(float64(podNumber) * nodeUsedRatio)
		logs.V(6).Infof("[computePodCount] nodeID: %s byMemory: %d originalCpuSize:%d maxPodDeploy:%d podsNumAvailable %d podNumber %d",
			node.ID, podCountByMemory, node.OriginalCpuSize, maxPodDeploy, node.PodsNumAvailable, podNumber)
	} else {
		podCountByCpu := (node.CpuAvailable / (int64(cpuRequestLowerLimit)))
		podNumber = min(podCountByCpu, (int64(maxPodDeploy)))
		podNumber = min(podNumber, (node.PodsNumAvailable))
		node.OriginalCpuSize = int64(cpuRequestLowerLimit)
		//node使用率
		podNumber = int64(float64(podNumber) * nodeUsedRatio)
		logs.V(6).Infof("[computePodCount] nodeID: %s byCpu: %d originalCpuSize:%d maxPodDeploy:%d podsNumAvailable %d podNumber %d",
			node.ID, podCountByCpu, node.OriginalCpuSize, maxPodDeploy, node.PodsNumAvailable, podNumber)
	}

	// 如果cpu和memory获取不到pod数量，设置一个默认值
	if podNumber <= 0 {
		podNumber = int64(defaultMaxPodDeploy)
	}
	return
}

// computeCountByNodeMemory 计算node可以用来初始化的内存数量，其中memory是字符串内存格式
// 返回值以Mi为单位
func computeCountByNodeMemory(nodeMemory string, totalRequests int64, reserved int64, podReservedRatio float64) (ret int64, err error) {
	memoryAllocatable, err := resource.ParseQuantity(nodeMemory)
	if err != nil {
		return
	}

	// 将内存转换为以Mi为单位的整数
	unit := resource.MustParse("1Mi")
	ret, ok := new(inf.Dec).QuoRound(memoryAllocatable.AsDec(), unit.AsDec(), 0, inf.RoundDown).Unscaled()
	if !ok {
		err = errors.New("unscaled unsuccessfully")
		return
	}
	// Allocatable 内存似乎并不等于空闲的内存，所以 DaemonSet 使用的内存需要被单独计算扣除
	// 参考 https://github.com/kubernetes/community/blob/master/contributors/design-proposals/node/node-allocatable.md
	ret -= reserved
	ret -= totalRequests
	// 为pod预留
	ret = int64(float64(ret) / podReservedRatio)

	return
}

// computeCountByNodeCpu 计算node可以用来初始化的cpu数量
/**
cpu 可用cpu k8s Allocatable cpu
totalRequests 公共组件 kube system 占用的cpu
reserved faas的deamonset 预留占用的cpu
usage cpu使用率 默认为1.0 即100% 可配置 因为最后计算的pod数据是int取整，实际不会达到100%
*/
func computeCountByNodeCpu(cpu resource.Quantity, totalRequests int64, reserved int64, usage float64) (ret int64, err error) {
	unit := resource.MustParse("1m")
	cpuInUnit, ok := new(inf.Dec).QuoRound(cpu.AsDec(), unit.AsDec(), 0, inf.RoundDown).Unscaled()
	if !ok {
		err = errors.New("unscaled cpu unsuccessfully")
		return 0, nil
	}

	ret = int64(float64(cpuInUnit)*usage) - totalRequests - reserved
	return ret, nil
}

// computeAvailablePodNum 计算node可以创建的最大pods数量
/**
pods 节点配置的pods数量
kubePods 公共组件 kube system的 pod数量
*/
func computeAvailablePodNum(pods resource.Quantity, kubePods int) (ret int64, err error) {
	unit := resource.MustParse("1")
	podsInUnit, ok := new(inf.Dec).QuoRound(pods.AsDec(), unit.AsDec(), 0, inf.RoundDown).Unscaled()
	if !ok {
		err = errors.New("unscaled pods unsuccessfully")
		return 0, nil
	}

	ret = podsInUnit - int64(kubePods) - api.K8sDeamonSetPodCount
	return ret, nil
}

// ComputeUsedMemory 计算容器总共使用了多少内存
func ComputeUsedMemory(containers []*api.ContainerInfo) (memoryMi int64) {
	for _, container := range containers {
		if !container.MayHasBeenUsed() {
			continue
		}
		if container.ResourceStats == nil || container.ResourceStats.MemoryStats == nil {
			continue
		}
		mem := api.Byte2Mi(container.ResourceStats.MemoryStats.Limit)
		memoryMi += mem
	}
	return
}

// generatePodName 生成node所需的一个pod name。
// 格式是 pmpod-内存大小-nodeIP-当前时间戳
// 时间戳可以保证所有pod不会重名
// 由于pod name只能使用小写字母、数字和-，IP需要经过字符转换
func generatePodName(node *api.NodeInfo, runningMode string) string {
	var podName string
	if runningMode != options.RunningModeOTE {
		nodeIP := strings.Replace(node.Name, ".", "-", -1) // 转换ipv4
		nodeIP = strings.Replace(nodeIP, ":", "-", -1)     // 转换ipv6
		podName = fmt.Sprintf("pmpod-%d-%s-%d", node.OriginalMemorySize, nodeIP, time.Now().UnixNano())
	} else {
		podName = fmt.Sprintf("pmpod-%d-%d", node.OriginalMemorySize, time.Now().UnixNano())
	}
	return podName
}

func getDaemonSetByRunningMode(runningMode string) int {
	var count int
	switch runningMode {
	case options.RunningModeCloud, options.RunningModeDuedge:
		count = 3
	case options.RunningModeOTE:
		count = 2
	default:
	}
	return count
}

// waitForPod watches the pod it until it finishes
func waitForPod(podName string, podCh <-chan watch.Event) error {
	for {
		event, ok := <-podCh
		if !ok {
			return fmt.Errorf("pod %s watch channel had been closed", podName)
		}
		if event.Type == watch.Error {
			return apierrs.FromObject(event.Object)
		}
		pod, ok := event.Object.(*v1.Pod)
		if !ok {
			return fmt.Errorf("pod %s watch event object is not pod event, but: %+v\n", podName, event)
		}
		switch event.Type {
		case watch.Added, watch.Modified:
			// succeeded/running
			if pod.Status.Phase == v1.PodSucceeded || pod.Status.Phase == v1.PodRunning {
				return nil
			}
			if pod.Status.Phase == v1.PodFailed {
				if pod.Status.Message != "" {
					return fmt.Errorf(pod.Status.Message)
				} else {
					return fmt.Errorf("pod: %s failed, pod.Status.Message unknown.", podName)
				}
			}

		case watch.Deleted:
			return fmt.Errorf("pod: %s was deleted", podName)

		case watch.Error:
			return fmt.Errorf("pod: %s watcher failed", podName)
		}
	}
}

// 判断所给路径文件、文件夹是否存在
func IsPathExists(path string) bool {
	_, err := os.Stat(path)
	if err != nil {
		if os.IsExist(err) {
			return true
		}
		return false
	}
	return true
}

// 判断所给路径是否为文件夹
func IsPathDir(path string) bool {
	s, err := os.Stat(path)
	if err != nil {
		return false
	}
	return s.IsDir()
}

func min(a, b int64) int64 {
	if a <= b {
		return a
	}
	return b
}

// 根据机器计算类型获取instanceType
func GetInstanceType(machineType string) string {
	switch machineType {
	case "g1", "c1", "ic1", "m1":
		return "N1"
	case "g2", "c2", "ic2", "m2":
		return "N2"
	case "g3", "c3", "ic3", "m3":
		return "N3"
	case "g3ne", "c3ne", "m3ne":
		return "N4"
	case "g4", "ic4", "c4", "m4":
		return "N5"
	default:
		return ""
	}
}
