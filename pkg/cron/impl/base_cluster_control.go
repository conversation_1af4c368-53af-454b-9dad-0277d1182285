package impl

import (
	"crypto/md5"
	"errors"
	"fmt"
	"icode.baidu.com/baidu/faas/kun/pkg/cron/resourcepool"
	"io/ioutil"
	"path/filepath"
	"sort"
	"strings"

	appsv1 "k8s.io/api/apps/v1"
	"k8s.io/apimachinery/pkg/util/json"
	"k8s.io/client-go/kubernetes/scheme"

	"icode.baidu.com/baidu/faas/kun/cmd/cron/app/options"
	"icode.baidu.com/baidu/faas/kun/pkg/api"
	"icode.baidu.com/baidu/faas/kun/pkg/bce/bcc"
	"icode.baidu.com/baidu/faas/kun/pkg/cron/rpc"
	"icode.baidu.com/baidu/faas/kun/pkg/funclet/client"
	"icode.baidu.com/baidu/faas/kun/pkg/util/logs"
)

// NewBaseClusterControl 生成通用集群管理接口，供k8s和cce接口使用
func NewBaseClusterControl(l *logs.Logger, opt *options.CronOptions, k8sInfo *api.K8sInfo, f client.FuncletInterface) *baseClusterControl {
	pdClient := NewPodControl(f, l)
	bccClient := bcc.NewBccClient(k8sInfo.BceOptions)
	bccOpenAPIClient := bcc.NewBccOpenAPIClient(k8sInfo.BceOptions)
	ccrClient := rpc.NewCcrClient(k8sInfo.BceOptions)
	poolHelper := resourcepool.NewHelper(l)
	c := &baseClusterControl{
		k8sClient:        rpc.NewK8sClient(k8sInfo, &k8sInfo.OteOptions),
		podClient:        pdClient,
		k8sInfo:          k8sInfo,
		logger:           l,
		opt:              opt,
		poolHelper:       poolHelper,
		bccClient:        bccClient,
		bccOpenAPIClient: bccOpenAPIClient,
		ccrClient:        ccrClient,
	}

	return c
}

var _ ClusterControl = &baseClusterControl{}

// baseClusterControl 提供最基础的k8s型集群管理，存储集群信息和客户端，如果cce配置存在，则初始化cce客户端接口
type baseClusterControl struct {
	k8sClient        rpc.K8sInterface
	podClient        PodControl
	k8sInfo          *api.K8sInfo
	logger           *logs.Logger
	opt              *options.CronOptions
	poolHelper       *resourcepool.Helper // 资源池帮助类
	bccClient        bcc.BccInterface
	bccOpenAPIClient bcc.BccInterface
	ccrClient        rpc.CcrInterface
}

// 空实现
func (m *baseClusterControl) ScalingUpWithResourcePool(resourcePool string, number int) error {
	return nil
}

func (m *baseClusterControl) ListNodes() (*ClusterNodes, error) {

	// 先获取k8s node，转换为NodeInfo
	k8sNodeMap, unavailableCount, err := m.getK8sNodeMap()
	if err != nil {
		return nil, err
	}

	clusterNodes := &ClusterNodes{
		UnavailableCount: unavailableCount,
	}
	nodeCnt := len(k8sNodeMap)
	m.logger.V(6).Infof("get %d nodes from k8s %s", nodeCnt, m.k8sInfo.CceClusterUUID)
	clusterNodes.Nodes = make([]*api.NodeInfo, nodeCnt)
	i := 0
	for _, node := range k8sNodeMap {
		clusterNodes.Nodes[i] = node
		i++
	}
	return clusterNodes, nil
}

func (m *baseClusterControl) getK8sNodeMap() (nodeMap map[string]*api.NodeInfo, unavailableCount uint, err error) {
	// 查找label适配的node
	var ls *rpc.LabelSelector
	k8sInfo := m.k8sInfo
	if k8sInfo.MatchLabels == nil || len(k8sInfo.MatchLabels) == 0 {
		ls = nil
	} else {
		ls = rpc.NewLabelSelector()
		for key, value := range k8sInfo.MatchLabels {
			ls.Add(key, value)
		}
	}

	k8sNodes, err := m.k8sClient.GetNodesByLabel(ls)
	if err != nil {
		return
	}

	nodeMap = make(map[string]*api.NodeInfo)
	// 将k8s node结构转换为NodeInfo
	for i := range k8sNodes {
		k8sNode := (*k8sNodeInfo)(&k8sNodes[i])
		node := k8sNode.ConvertToNodeInfo(k8sInfo)

		if node.ID == "" {
			m.logger.Errorf("can't get k8s node %s:%s id", k8sInfo.CceClusterUUID, k8sNode.Name)
			continue
		}

		if node.InstanceShortId == "" {
			m.logger.Errorf("can't get k8s node %s:%s short id", k8sInfo.CceClusterUUID, k8sNode.Name)
			continue
		}

		// 丢弃不可用node，计数加1
		if !k8sNode.IsAvailableK8sNode() {
			m.logger.Errorf("node %s is unavailable", node.ID)
			unavailableCount++
			continue
		}

		nodeMap[node.ID] = node
	}
	return
}

func (m *baseClusterControl) ScalingUp(number int) error {
	panic("no implement")
}

func (m *baseClusterControl) ScalingDown(nodeIDs []string) error {
	panic("no implement")
}

func (m *baseClusterControl) ApplyDaemonSet(dsName string) error {

	dsInfo := m.k8sInfo.DsReleaseConfigMap[dsName]

	// inspect conf
	if dsInfo.Md5 == "" || m.opt.ConfDir == "" || dsInfo.YamlFilePath == "" {
		return errors.New("etcd DsReleaseConfigMap conf error")
	}
	// 校验cron配置目录是否存在：
	if !IsPathDir(m.opt.ConfDir) {
		return fmt.Errorf("cron conf dir not exist: %s", m.opt.ConfDir)
	}

	// try download and decompression
	dsConfFile := filepath.Join(m.opt.ConfDir, dsInfo.YamlFilePath)
	if !IsPathExists(dsConfFile) {
		return fmt.Errorf("ds conf file not exist: %s", dsConfFile)
	}

	// read conf
	yamlBytes, err := ioutil.ReadFile(dsConfFile)
	if err != nil {
		return err
	}

	// 校验一下md5是否一致
	checkMd5 := md5.Sum(yamlBytes)
	checkMd5Str := fmt.Sprintf("%x", checkMd5)
	if checkMd5Str != dsInfo.Md5 {
		return fmt.Errorf("k8s ds: %s md5: %s not equal with local yaml file md5: %s", dsName, dsInfo.Md5, checkMd5Str)
	}

	decode := scheme.Codecs.UniversalDeserializer().Decode
	obj, _, err := decode(yamlBytes, nil, nil)
	if err != nil {
		return err
	}

	var (
		dsNameYaml string
		imageYaml  string
		jsonBytes  []byte
	)

	// 使用appsv1解析ds conf file
	// k8s从v1.9开始支持apps/v1的API version
	ds, ok := obj.(*appsv1.DaemonSet)
	if !ok {
		return errors.New("the file not a daemonset conf file")
	}

	dsNameYaml = ds.Name
	imageYaml = ds.Spec.Template.Spec.Containers[0].Image
	jsonBytes, err = json.Marshal(ds)
	if err != nil {
		fmt.Errorf("marshal daemonset conf to json err: %s", err)
		return err
	}

	if dsNameYaml != dsName {
		return fmt.Errorf("yaml loads ds name: %s is not equal with dsName: %s", dsNameYaml, dsName)
	}

	if imageYaml != dsInfo.ImageID {
		return fmt.Errorf("ds imageID check failed: imageID in local file: %s is different with etcd: %s", imageYaml, dsInfo.ImageID)
	}

	m.logger.Infof("[ApplyDaemonSet] load ds conf success")

	return m.k8sClient.ApplyDaemonSet(dsName, jsonBytes)
}

func (m *baseClusterControl) GetImageListV2() ([]interface{}, error) {
	var (
		res       []interface{}
		ccrImages []rpc.CcrImageModel
		err       error
	)

	// 获取ccr镜像列表
	ccrInput := rpc.GetCcrImageListRequest{Keyword: m.k8sInfo.BceOptions.CcrProjectName}
	ccrImages, err = m.ccrClient.GetImageList(&ccrInput)
	if err != nil {
		// 有错误打印一条错误日志
		logs.Errorf("get ccr images fail, err: %+v", err)
	}

	// 返回结果中只包含ccr镜像
	if len(ccrImages) > 0 {
		for _, im := range ccrImages {
			res = append(res, im)
		}
	}
	return res, err
}

// 获取最优可扩容的可用区及库存
func (m *baseClusterControl) getScalableZone(flavor api.BccFlavor, zoneMap map[string]api.ZoneConfig, zonesUnavailableSpecMap *ZonesUnavailableSpecMap) (zone string, stock int) {

	/*
		按照可用区进行遍历：
		1. 当前规格在当前可用区可用，直接扩容
		2. 当前规格在当前可用区不可用，跳到下一可用区
	*/

	// 获取bcc库存
	stocks, err := m.bccClient.GetStockWithSpec(flavor.Spec)
	if err != nil {
		m.logger.Errorf("[scaling up] get bcc stocks fail, spec: %s, err: %v", flavor.Spec, err)
		return
	}

	availableSpecMap, err := m.bccOpenAPIClient.GetAvailableSpec()
	if err != nil {
		m.logger.Errorf("[scaling up] get bcc available spec fail, spec: %s, err: %v", flavor.Spec, err)
	}

	// rootOnLocal true代表本地系统盘，false代表cds系统盘，扩容时应该选择cds系统盘
	formatStocksFunc := func(bccStocks []bcc.BccStock) []bcc.BccStock {
		res := make([]bcc.BccStock, 0)
		for _, s := range bccStocks {

			// 可用规格不包含对应的zone 或者 对应的资源规格spec在可用区不存在，则不作为扩容的目标可用区
			if availableSpecMap != nil {

				specSet, zoneExist := availableSpecMap[s.LogicalZone]
				if !zoneExist {
					continue
				}

				if _, specAvailable := specSet[s.Spec]; !specAvailable {
					m.logger.Infof("[scaling up] %s %s unavailable", s.LogicalZone, flavor.Spec)
					continue
				}
			}

			if !s.RootOnLocal && s.InventoryQuantity > 0 {
				res = append(res, s)
			}
		}
		return res
	}

	formatStocks := formatStocksFunc(stocks)
	// 根据etcd配置的可用区，选择库存量较大的可用区
	sort.SliceStable(formatStocks, func(i, j int) bool {
		return formatStocks[i].InventoryQuantity > formatStocks[j].InventoryQuantity
	})

	for _, s := range formatStocks {
		z := "zone" + strings.ToUpper(s.LogicalZone[strings.LastIndex(s.LogicalZone, "-")+1:])
		if _, zoneExists := zoneMap[z]; zoneExists {

			/*
				1. 判断库存信息中的zone 是否存在于zonesUnavailableSpecMap
				2. 如果存在，判断zone 对应的unavailableSpecSet是否包含了该flavor规格，包含的话则不对这类规则进行扩容，直接continue
			*/
			if unavailableSpecSet, zoneExists := (*zonesUnavailableSpecMap)[z]; zoneExists {

				// flavor.Spec示例：bcc.g4.c2m8 ,切分后根据g4获取instanceType

				infos := strings.Split(flavor.Spec, ".")

				instanceType := GetInstanceType(infos[1])

				spec := infos[2]

				specKey := fmt.Sprintf("%s.%s", instanceType, spec)

				if _, specUnavailabe := (*unavailableSpecSet)[specKey]; specUnavailabe {
					m.logger.V(6).Infof("[getScalableZone] unavailableZone: %+s; flavorSpec: %+s", z, flavor.Spec)
					continue
				}
			}

			zone = z
			stock = s.InventoryQuantity
			m.logger.Infof("[scaling up] the optimal scaling zone is :%s, stock is %d", zone, stock)
			return
		}
		m.logger.V(6).Infof("[getScalableZone] Out of stock,Zone: %+s; flavorSpec: %+s", z, flavor.Spec)

	}

	m.logger.Warn("[scaling up] all zones bcc stocks are empty")
	return
}

type ClusterNotExistError struct {
	ClusterID string
}

func (e ClusterNotExistError) Error() string {
	return fmt.Sprintf("cluster %s does not exist", e.ClusterID)
}

type ClusterNotRunningError struct {
	ClusterID string
}

func (e ClusterNotRunningError) Error() string {
	return fmt.Sprintf("cluster %s is not running", e.ClusterID)
}
