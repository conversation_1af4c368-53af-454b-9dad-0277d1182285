package impl

import (
	"errors"
	"sync"
	"sync/atomic"

	"go.uber.org/zap"

	"icode.baidu.com/baidu/faas/kun/pkg/api"
	"icode.baidu.com/baidu/faas/kun/pkg/etcd"
	"icode.baidu.com/baidu/faas/kun/pkg/funclet/client"
	proxy "icode.baidu.com/baidu/faas/kun/pkg/proxyagent/client"
	"icode.baidu.com/baidu/faas/kun/pkg/store/reserve"
	"icode.baidu.com/baidu/faas/kun/pkg/util/logs"
)

var (
	ErrNoAvailablePod = errors.New("no available pod")
)

const (
	RUNNERMEMORY128M       = int64(134217728)
	coolDownPodConcurrency = 56
	MaxPodConcurrency      = 1 << 53 // dubious pod score
)

type scheduleNodeControl struct {
	PodControl
	NodeContainerControl
	etcd.EtcdInterface
	proxy.ProxyAgentInterface

	reserve.ReserveManager
	logger *logs.Logger
}

var _ ScheduleNodeControl = &scheduleNodeControl{}

func NewScheduleNodeControlByClients(e etcd.EtcdInterface, f client.FuncletInterface, l *logs.Logger,
	m reserve.ReserveManager, bc *baseClusterControl, p proxy.ProxyAgentInterface) ScheduleNodeControl {
	c := NewPodControl(f, l)
	nc := NewK8sNodeControl(bc)
	return NewScheduleNodeControl(c, m, nc, e, l, p)
}

func NewScheduleNodeControl(c PodControl, m reserve.ReserveManager, nc NodeContainerControl,
	e etcd.EtcdInterface, l *logs.Logger, p proxy.ProxyAgentInterface) ScheduleNodeControl {
	return &scheduleNodeControl{
		PodControl:           c,
		ReserveManager:       m,
		NodeContainerControl: nc,
		EtcdInterface:        e,
		logger:               l,
		ProxyAgentInterface:  p,
	}
}

// getAllContainersOnNode 获取node上的所有container
func (m *scheduleNodeControl) getAllContainersOnNode(node *api.NodeInfo) ([]*api.ContainerInfo, error) {
	healthyContainers, err := m.GetHealthyContainersOnNode(node)
	if err != nil {
		return []*api.ContainerInfo{}, err
	}

	zombieContainers, err := m.GetZombieContainersOnNode(node)
	if err != nil {
		return []*api.ContainerInfo{}, err
	}

	all := append(healthyContainers, zombieContainers...)
	return all, nil
}

// ClearReserveCacheOnNode 尽力删除redis上node相关的所有缓存，包括cold node、warm node、warm pod
func (m *scheduleNodeControl) ClearReserveCacheOnNode(node *api.NodeInfo) error {
	l := m.logger.WithField("nodeID", node.ID)

	// 移除cold node
	_, err := m.ReserveManager.RemColdNode(node)
	if err != nil {
		l.Warnf("[ClearReserveCacheOnNode] remove warm node %s failed: %s", node.ID, err.Error())
	}

	// 尝试移除warm node
	_, err = m.ReserveManager.RemWarmNode(node)
	if err != nil {
		l.Warnf("[ClearReserveCacheOnNode] remove warm node %s failed: %s", node.ID, err.Error())
	}

	// 从funclet获知所有的pod，包括各种异常的
	allContainers, err := m.getAllContainersOnNode(node)
	if err != nil {
		m.IncrErrorTimes(node.ID, api.NodeOpTypeInspect, api.InspectErrorCallFunclet)
		l.Errorf("[ClearReserveCacheOnNode] get pods from `%s` failed: %v", node.ID, err)
		return err
	}
	l.V(5).Infof("[ClearReserveCacheOnNode] checking %d pods on node `%s`...", len(allContainers), node.ID)

	var wg sync.WaitGroup
	for _, container := range allContainers {
		if !container.MayHasBeenUsed() || container.RuntimeInfo == nil ||
			(container.ResourceStats == nil || container.ResourceStats.MemoryStats == nil) {
			// 没有被使用过，或者信息不全，则跳过
			continue
		}
		wg.Add(1)
		go func(container *api.ContainerInfo) {
			defer wg.Done()
			// pod被使用过，尝试移除warm pod
			// podInfo := container2Pod(container, node)
			podInfo := container.Container2Pod(node)
			l.V(5).Infof("[ClearReserveCacheOnNode] delete warm pod %s", podInfo)

			_, err = m.ReserveManager.RemWarmPod(podInfo)
			if err != nil {
				l.Warnf("[ClearReserveCacheOnNode] delete warm pod %s error: %v", podInfo.PodName, err)
			}
		}(container)
	}
	wg.Wait()

	return nil
}

// PurgeNode 回收node上的多个warm pod，返回成功的
func (m *scheduleNodeControl) PurgeNode(node *api.NodeInfo, sourcePodExpire map[string]int, defaultPodExpire, functionTTL int) (err error) {
	l := m.logger.WithField("nodeID", node.ID)

	defaultPodDeadline := genPodDeadline(defaultPodExpire, functionTTL)
	deadlineConfig := "default"

	containers, err := m.GetHealthyContainersOnNode(node)
	if err != nil {
		l.Errorf("[PurgeNode] get healthy containers on node %s failed: %v, increment one times error", node.ID, err)
		m.IncrErrorTimes(node.ID, api.NodeOpTypeInspect, api.InspectErrorCallFunclet)
		return
	}

	if len(containers) == 0 {
		return
	}

	sem := make(chan struct{}, coolDownPodConcurrency)
	var wg sync.WaitGroup
	var memoryRecycled int64 = 0 // 已回收的内存数量
	for _, container := range containers {
		// 万事要小心
		if container == nil {
			l.Warnf("[PurgeNode] container is empty, so skip it")
			continue
		}
		// 按pod source使用不同的回收阈值
		pd := defaultPodDeadline
		if container.RuntimeInfo != nil {
			source := container.Labels.Get(api.LabelSource)
			if source != "" && sourcePodExpire != nil {
				specPodExpire, ok := sourcePodExpire[source]
				if ok && specPodExpire > 0 {
					pd = genPodDeadline(specPodExpire, functionTTL)
					deadlineConfig = source
				}
			} else {
				if container.MaxFunctionTimeout != nil {
					pd = genPodDeadline(*container.MaxFunctionTimeout, functionTTL)
				}
			}
		}

		l.V(6).Infof("[PurgeNode] start check pod: %v with deadline config : %s， container runtimeinfo: %v", container, deadlineConfig, container.RuntimeInfo)
		if !isContainerExpired(container, pd.idleDeadline, pd.functionDeadline,
			pd.statusUpdateDeadline, l) {
			continue
		}

		if container.RuntimeInfo != nil {
			l.Warnf("[PurgeNode] container %s memory size: %d", container.Hostname, container.MemorySize)
		}
		l.V(6).Infof("[PurgeNode] we're going to recycle pod `%s`(%s) on node `%s`",
			container.Hostname, container.Status, node.Name)
		wg.Add(1)
		go func(c *api.ContainerInfo) {
			// 通过 semaphore 来限制同时执行 doCoolDownPod 的个数
			sem <- struct{}{}
			if m.doCoolDownPod(node, c, pd.coolDownDeadline) {
				// 回收成功则可以填回warm node
				l.Debugf("[PurgeNode] cooldown pod: %+v", c)
				if c.ResourceStats != nil && c.ResourceStats.MemoryStats != nil {
					memory := api.Byte2Mi(c.ResourceStats.MemoryStats.Limit)
					l.Infof("[PurgeNode] pod: %+v, memory size: %d", c, memory)
					//  如果一个pod被使用过，但没有service type字段，说明是旧版poolmgr在使用，那么就不将内存加回redis。
					if c.RuntimeInfo != nil && c.ServiceType == "" {
						l.Warnf("[PurgeNode] pod %s has no service type or labels, so discard it", c.Hostname)
					} else {
						l.Infof("[PurgeNode] add pod %s memory %d to node", c.Hostname, memory)
						atomic.AddInt64(&memoryRecycled, memory)
					}
				} else {
					l.Warnf("[PurgeNode] can't get pod %s limit", c.Hostname)
				}
			}
			<-sem
			wg.Done()
		}(container)
	}

	wg.Wait()

	if memoryRecycled != 0 {
		// 将pod的内存一次性填回warm node
		l.Infof("[PurgeNode] add recycled memory %d to warm node", memoryRecycled)
		_, err = m.ReserveManager.IncrWarmNodeMemoryXX(node, memoryRecycled)
		if err != nil {
			l.Warnf("[PurgeNode] increase warm node %s memory failed: %s", node.ID, err.Error())
		}
	} else {
		l.V(8).Infof("[PurgeNode] no pod has been recycled")
	}
	return nil
}

// doCoolDownPod 把一个 Pod 从 PodInfo SET 中删除，并且重置它
func (m *scheduleNodeControl) doCoolDownPod(node *api.NodeInfo, container *api.ContainerInfo,
	coolDownDeadline int64) bool {
	l := m.logger.WithField("nodeID", node.ID)

	// 如果没有运行时信息或者内存信息，说明容器信息不全，直接使用 CoolDown 恢复容器
	// 如果RuntimeInfo意外丢失，导致删不掉，就会在WarmPod中留下阴影。。。
	if container.RuntimeInfo != nil &&
		(container.ResourceStats != nil && container.ResourceStats.MemoryStats != nil) {
		if len(container.Labels) != 0 {
			// podInfo := container2Pod(container, node)
			podInfo := container.Container2Pod(node)
			l.V(4).Infof("[doCoolDownPod] try to remove pod `%s:%s` from warm pod set...",
				podInfo.PodName, container.Status)

			// 删除pod，同时查看并发数，看是否在被使用
			c, err := m.ReserveManager.RemWarmPodWithConcurrency(podInfo)

			if err == reserve.ErrResourceNotExist {
				// pod不在redis中，有两种情况：
				// 	1. poolmgr调用warm up时出错，未将pod写入redis
				// 	2. cron在之前的任务循环删除，但因并发度不是0而未重置。到本次循环时已过期，那么不需要再等到兜底时间了。
				l.Infof("[doCoolDownPod] pod does not exist, so will be reset，podinfo: %+v", podInfo)
			} else if err != nil || (c != 0 && c < MaxPodConcurrency) {
				// 从redis中删除发生错误，或者pod还在被使用，需要再次判断异常持续时间是否超过兜底时间
				if err != nil {
					l.Errorf("[doCoolDownPod] remove pod %s from warm pod failed: %s, pod memory size: %d", podInfo.PodName, err.Error(), podInfo.MemorySize)
				} else {
					l.Warnf("[doCoolDownPod] pod %s concurrency %d is using by others, pod memory size: %d", podInfo.PodName, c, podInfo.MemorySize)
				}

				if container.LastAccessTime > coolDownDeadline { // 再次判断，还未到兜底时间
					return false
				}
				// 运行到此，必须重置
				l.Warnf("[doCoolDownPod] pod %s reached deadline, and will be reset, pod memory size: %d", podInfo.PodName, podInfo.MemorySize)
			}
		} else {
			l.Warnf("[doCoolDownPod] pod %s has no labels, so we can't try to remove it from warm pod set, "+
				"which might be a potential problem!!", container.Hostname)
		}
	} else {
		l.Warnf("[doCoolDownPod] pod %v has no RuntimeInfo or memory size so we can't try to remove it from warm pod set, "+
			"which might be a potential problem!!", container)
	}

	// now we can reset or rebuild it
	if node.ServiceType == api.ServiceTypeCFCKata {
		// rebuild pod
		timeoutPodName, err := m.RebuildPod(node, container.Hostname)
		if err != nil {
			l.Errorf("[doCoolDownPod] rebuild pod %s failed, will put to sick set: %s", container.Hostname, err.Error())
			if timeoutPodName != "" {
				// add it to etcd
				l.Infof("[doCoolDownPod] try set it to etcd")
				err = m.SetTimeoutPods(node.ID, etcd.AddTimeOutPodsType, timeoutPodName)
				if err != nil {
					l.Warnf("[doCoolDownPod] set timeout pod: %s failed: %s", timeoutPodName, err.Error())
				}
				return false
			}
			return false
		}
	} else {
		if err := m.CoolDownPod(container, node); err != nil {
			l.Errorf("[doCoolDownPod] reset pod %s failed, will put to sick set: %s", container.Hostname, err.Error())
			if container.RuntimeInfo != nil {
				l.Errorf("[doCoolDownPod] reset pod %s failed, will put to sick set: %s, memory size: %d", container.Hostname, err.Error(), container.MemorySize)
			}
			if container.ResourceStats != nil && container.ResourceStats.MemoryStats != nil {
				memory := api.Byte2Mi(container.ResourceStats.MemoryStats.Limit)
				l.Errorf("[doCoolDownPod] reset pod %s failed, will put to sick set: %s, memory size limit: %d", container.Hostname, err.Error(), memory)
			}
			// TODO: put sick pod
			return false
		}
	}

	if container.RuntimeInfo != nil {
		l.Infof("[doCoolDownPod] recycle warm pod %s in [%d]ms, memory size: %d", container.Hostname,
			toAccessTime(0)-container.LastAccessTime, container.MemorySize)
	}

	l.V(5).Infof("[doCoolDownPod] reset pod %s successfully", container.Hostname)
	if container.RuntimeInfo != nil {
		l.V(5).Infof("[doCoolDownPod] reset pod %s successfully, container memory size: %d", container.Hostname, container.MemorySize)
	}
	return true
}

// ResetPodsOnNode 通知funclet重置node上所有使用过的pod
func (m *scheduleNodeControl) ResetPodsOnNode(node *api.NodeInfo) error {
	l := m.logger.With(zap.String("nodeID", node.ID))

	allContainers, err := m.getAllContainersOnNode(node)
	if err != nil {
		m.IncrErrorTimes(node.ID, api.NodeOpTypeInspect, api.InspectErrorCallFunclet)
		return err
	}

	l.V(5).Info("[ResetPodsOnNode] checking all pods", zap.Int("all_pods_num", len(allContainers)))

	sem := make(chan struct{}, coolDownPodConcurrency)
	var wg sync.WaitGroup
	for _, container := range allContainers {
		if !container.NeedCoolDown() {
			continue
		}

		// 通知funclet重置pod
		wg.Add(1)
		func(c *api.ContainerInfo) {
			sem <- struct{}{}
			l.Infof("[ResetPodsOnNode] cool down pod %s, status %s", c.Hostname, c.Status)
			err = m.CoolDownPod(c, node)
			if err != nil {
				l.Warnf("[ResetPodsOnNode] cool down pod %s, status %s failed: %s", c.Hostname, c.Status, err.Error())
			}
			<-sem
			wg.Done()
		}(container)
	}

	wg.Wait()
	return nil
}
