package impl

import (
	"strconv"
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/assert"

	"icode.baidu.com/baidu/faas/kun/cmd/cron/app/options"
	"icode.baidu.com/baidu/faas/kun/pkg/api"
	"icode.baidu.com/baidu/faas/kun/pkg/bce/bcc"
	bccMock "icode.baidu.com/baidu/faas/kun/pkg/bce/bcc/mock"
	"icode.baidu.com/baidu/faas/kun/pkg/cron/rpc"
	"icode.baidu.com/baidu/faas/kun/pkg/cron/rpc/mock"
)

func TestGetCceNodeMapV2(t *testing.T) {
	mockCtrl := gomock.NewController(t)
	defer mockCtrl.Finish()

	cceClient := mock.NewMockCceV2Interface(mockCtrl)
	ctl, cceNodes := testCceControlV2(cceClient, nil, nil)
	cceClient.EXPECT().GetNodes("cluster1").Return(cceNodes, nil)
	nodeReturns, err := ctl.getCceNodeMap()
	assert.Nil(t, err)
	assert.EqualValues(t, 5, len(nodeReturns.NodeMap))
	assert.EqualValues(t, 1, nodeReturns.PendingCount)
	assert.EqualValues(t, 2, len(nodeReturns.WaitScaleDownNodeMap))
}

// mock test cce control v2
func testCceControlV2(cceClient rpc.CceV2Interface, bccClient bcc.BccInterface, ccrClient rpc.CcrInterface) (*cceNodeControlV2, []rpc.Instance) {
	cceNodes := make([]rpc.Instance, 5)
	zoneAConfig := api.ZoneConfig{InstanceTypes: []int{1}, SubnetUUID: "net1"}
	for i := 0; i < len(cceNodes)-1; i++ {
		cceNodes[i] = rpc.Instance{
			Spec: rpc.InstanceSpec{
				CceInstanceID: "ins" + strconv.Itoa(i),
				VPCConfig: rpc.VPCConfig{
					AvailableZone: "zoneA",
					VpcSubnetID:   zoneAConfig.SubnetUUID,
				},
				InstanceType: strconv.Itoa(zoneAConfig.InstanceTypes[0]),
			},
			Status: rpc.InstanceStatus{
				InstancePhase: rpc.CceInstancePhaseRunning,
				Machine: rpc.Machine{
					InstanceID: "ins" + strconv.Itoa(i),
				},
			},
		}

	}

	zoneBConfig := api.ZoneConfig{InstanceTypes: []int{1, 2}, SubnetUUID: "net2"}
	cceNode1 := rpc.Instance{
		Spec: rpc.InstanceSpec{
			CceInstanceID: "ins10",
			VPCConfig: rpc.VPCConfig{
				AvailableZone: "zoneB",
				VpcSubnetID:   zoneBConfig.SubnetUUID,
			},
			InstanceType: strconv.Itoa(zoneBConfig.InstanceTypes[1]),
		},
		Status: rpc.InstanceStatus{
			InstancePhase: rpc.CceInstancePhasePending,
			Machine: rpc.Machine{
				VpcIP:      "xxx",
				InstanceID: "ins10",
			},
		},
	}

	cceNode2 := rpc.Instance{
		Spec: rpc.InstanceSpec{
			CceInstanceID: "ins11",
			VPCConfig: rpc.VPCConfig{
				AvailableZone: "zoneB",
				VpcSubnetID:   zoneBConfig.SubnetUUID,
			},
			InstanceType: strconv.Itoa(zoneBConfig.InstanceTypes[0]),
		},
		Status: rpc.InstanceStatus{
			InstancePhase: rpc.CceInstancePhaseRunning,
			Machine: rpc.Machine{
				VpcIP:      "xxx",
				InstanceID: "ins11",
			},
		},
	}

	cceNode3 := rpc.Instance{
		Spec: rpc.InstanceSpec{
			CceInstanceID: "ins12",
			VPCConfig: rpc.VPCConfig{
				AvailableZone: "zoneB",
				VpcSubnetID:   zoneBConfig.SubnetUUID,
			},
			InstanceType: strconv.Itoa(zoneBConfig.InstanceTypes[0]),
		},
		Status: rpc.InstanceStatus{
			InstancePhase: "testStatus",
			Machine: rpc.Machine{
				VpcIP:      "xxx",
				InstanceID: "ins12",
			},
		},
	}

	cceNode4 := rpc.Instance{
		Spec: rpc.InstanceSpec{
			CceInstanceID: "ins13",
			VPCConfig: rpc.VPCConfig{
				AvailableZone: "zoneB",
				VpcSubnetID:   zoneBConfig.SubnetUUID,
			},
			InstanceType: strconv.Itoa(zoneBConfig.InstanceTypes[0]),
		},
		Status: rpc.InstanceStatus{
			InstancePhase: rpc.CceInstancePhaseDeleteFailed,
			Machine: rpc.Machine{
				VpcIP:      "xxx",
				InstanceID: "ins13",
			},
		},
	}
	cceNode5 := rpc.Instance{
		Spec: rpc.InstanceSpec{
			CceInstanceID: "ins14",
			VPCConfig: rpc.VPCConfig{
				AvailableZone: "zoneB",
				VpcSubnetID:   zoneBConfig.SubnetUUID,
			},
			InstanceType: strconv.Itoa(zoneBConfig.InstanceTypes[0]),
		},
		Status: rpc.InstanceStatus{
			InstancePhase: rpc.CceInstancePhasesCreateFailed,
			Machine: rpc.Machine{
				VpcIP:      "xxx",
				InstanceID: "ins14",
			},
		},
	}

	cceNodes = append(cceNodes, cceNode1)
	cceNodes = append(cceNodes, cceNode2)
	cceNodes = append(cceNodes, cceNode3)
	cceNodes = append(cceNodes, cceNode4)
	cceNodes = append(cceNodes, cceNode5)

	ctrl := &cceNodeControlV2{
		cceClient: cceClient,
		bccClient: bccClient,
		baseClusterControl: &baseClusterControl{
			k8sInfo: &api.K8sInfo{
				CceClusterUUID: "cluster1",
				K8sOptions: api.K8sOptions{
					DsReleaseConfigMap: map[string]api.DsReleaseConfig{
						"funclet": {ImageID: "funclet-image-id-1"},
						"invoker": {ImageID: "invoker-image-id-1"},
					},
				},
				ScalingUpOptions: api.ScalingUpOptions{
					ZoneConfigMap: map[string]api.ZoneConfig{
						"zoneA": zoneAConfig,
						"zoneB": zoneBConfig,
					},
					OsType:    "Linux",
					OsName:    "Ubuntu",
					OsVersion: "16.04 LTS",
					OsArch:    "amd64 (64bit)",
					Flavors: []api.BccFlavor{
						{
							Cpu:          2,
							Memory:       4,
							SsdDiskSize:  50,
							MaxPodDeploy: 20,
						},
					},
				},
			},
			opt: &options.CronOptions{
				TaskSchedulerOptions: &options.TaskSchedulerOptions{
					TaskCloudSchedulerOptions: &options.TaskCloudSchedulerOptions{},
				},
			},
			bccClient:        bccClient,
			ccrClient:        ccrClient,
			bccOpenAPIClient: bccClient,
		},
	}
	return ctrl, cceNodes
}

func TestGetScalableZoneV2(t *testing.T) {
	flavor := api.BccFlavor{
		Cpu:          2,
		Memory:       8,
		SsdDiskSize:  30,
		MaxPodDeploy: 35,
		Spec:         "bcc.g3.c2m8",
		InstanceType: 13,
		Charge:       0.0066236,
	}
	zoneConfigMap := make(map[string]api.ZoneConfig, 10)
	instanceTypes := []int{10, 13}
	zoneConfigMap["zoneD"] = api.ZoneConfig{
		SubnetUUID:    "b4eef8a1-578e-4fbe-9437-f21b33ac0c61",
		InstanceTypes: instanceTypes,
	}

	stocks := []bcc.BccStock{
		{
			LogicalZone:       "cn-bj-d",
			Spec:              "bcc.g3.c2m8",
			InventoryQuantity: 10,
			RootOnLocal:       false,
		},
	}
	mockCtrl := gomock.NewController(t)
	defer mockCtrl.Finish()

	bccClient := bccMock.NewMockBccInterface(mockCtrl)
	bccClient.EXPECT().GetStockWithSpec(gomock.Any()).Return(stocks, nil)
	cceClient := mock.NewMockCceV2Interface(mockCtrl)

	ctl, _ := testCceControlV2(cceClient, bccClient, nil)

	availableSpecMap := make(map[string]bcc.ZoneSpec)

	specMap := make(map[string]string)
	specMap["bcc.g3.c2m8"] = "bcc.g3.c2m8"
	specMap["bcc.g4.c2m8"] = "bcc.g4.c2m8"

	availableSpecMap["cn-bj-d"] = specMap

	bccClient.EXPECT().GetAvailableSpec().Return(availableSpecMap, nil)

	zonesUnavailableSpecMap := &ZonesUnavailableSpecMap{}

	zone, _ := ctl.getScalableZone(flavor, zoneConfigMap, zonesUnavailableSpecMap)

	assert.Equal(t, zone, "zoneD")
}

func TestGenDownloadContanierImageScript(t *testing.T) {
	mockCtrl := gomock.NewController(t)
	defer mockCtrl.Finish()

	cceClient := mock.NewMockCceV2Interface(mockCtrl)
	ctrl, _ := testCceControlV2(cceClient, nil, nil)
	ctrl.k8sInfo.K8sOptions.DsReleaseConfigMap = make(map[string]api.DsReleaseConfig)
	ctrl.k8sInfo.K8sOptions.DsReleaseConfigMap["funclet"] = api.DsReleaseConfig{ImageID: "funclet-image-id-1"}
	ctrl.k8sInfo.BceOptions = &api.BceOptions{DockerHubUser: "xxx"}
	_, err := ctrl.genDownloadContanierScript()
	assert.Equal(t, nil, err)
}
