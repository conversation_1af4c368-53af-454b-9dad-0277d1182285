package impl

import (
	"errors"
	"fmt"
	"net/url"
	"time"

	"icode.baidu.com/baidu/faas/kun/pkg/cron/resourcepool"

	"gopkg.in/inf.v0"
	kubeapi "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/api/resource"

	"icode.baidu.com/baidu/faas/kun/pkg/api"
	"icode.baidu.com/baidu/faas/kun/pkg/cron/rpc"
	"icode.baidu.com/baidu/faas/kun/pkg/util/logs"
)

const nodeIDKey = "nodeID"

// k8sNodeControl 提供k8s层容器接口
type k8sNodeControl struct {
	*baseClusterControl
}

var _ NodeContainerControl = &k8sNodeControl{}

// NewK8sNodeControl 生成k8s层容器管理接口
func NewK8sNodeControl(c *baseClusterControl) *k8sNodeControl {
	return &k8sNodeControl{c}
}

func (m *k8sNodeControl) RepairNode(node *api.NodeInfo) error {
	panic(errors.New("no implement"))
}

// InitNode 进行node的pod的初始化操作
func (m *k8sNodeControl) InitNode(node *api.NodeInfo, memorySize int64) error {
	l := m.logger.WithStringField(nodeIDKey, node.ID)
	// 设置 node 的 ContainerImage
	node.ContainerImage = m.k8sInfo.ContainerImage
	resourcePool := m.poolHelper.GetResourcePoolFromNode(node)
	if resourcePool != api.DefaultResourcePool {
		resourcePoolConfig := m.poolHelper.GetExtraPoolConfig(m.k8sInfo, resourcePool)
		if resourcePoolConfig != nil {
			node.ContainerImage = resourcePoolConfig.ContainerImage
		} else {
			// 如果资源池配置不存在/资源池开关关闭，清理resourcePool标签，加入到默认池，使用默认镜像
			l.Infof("can't init pods, because resource pool:%s config is nil or disable, add to default pool",
				resourcePool)
			m.poolHelper.ClearResourcePoolLabelForNode(node)
		}
	}
	node.KataContainerSpec = m.k8sInfo.KataContainerSpec

	if m.k8sInfo.AutoApplyImage && m.k8sInfo.DsReleaseConfigMap != nil {
		// 检查并设置node的daemonSet info
		for ds, dsInfo := range m.k8sInfo.DsReleaseConfigMap {

			if node.DsReleaseConfigMap == nil {
				node.DsReleaseConfigMap = make(map[string]string, len(m.k8sInfo.DsReleaseConfigMap))
			}

			nodeDs, ok := node.DsReleaseConfigMap[ds]
			if !ok {
				// new node
				node.DsReleaseConfigMap[ds] = dsInfo.Md5
				continue
			}

			if dsInfo.Md5 != "" && nodeDs != dsInfo.Md5 {
				// delete this node's daemonset
				l.V(5).Infof("clean daemonset: %s pods on node %s", ds, node.Name)
				labelSelector := rpc.NewLabelSelector().
					Add("app", []string{ds})
				err := m.k8sClient.CleanDaemonSetPods(labelSelector, node.Name)
				if err != nil {
					l.Errorf("clean DeamonSet pods on node %s failed: %s", node.Name, err.Error())
					return err
				}
				// copy this node's info
				node.DsReleaseConfigMap[ds] = dsInfo.Md5
			}
		}
	}

	// 做点准备工作，先把潜在的pending状态的pod删除一下（不用等待删除完成）
	// 主要是为了等下初始化pod的时候，不让k8s自作主张去把pending的pod重新恢复，导致计算出的pod数量过多的问题
	l.V(5).Infof("clean pending pods on node %s", node.Name)
	err := m.k8sClient.CleanPendingPods(node.Name)
	if err != nil {
		l.Errorf("clean pending pods on node %s failed: %s", node.Name, err.Error())
		return err
	}

	l.V(5).Infof("InitNode: Deleting current pods under user pod namespace if exists on node `%s`...", node.Name)
	err = m.DeletePods(node, true)
	if err != nil {
		l.Errorf("InitNode: %s", err.Error())
	}

	k8sNode, err := m.getK8sNodeInfo(node)
	if err != nil {
		l.Errorf("InitNode: %s", err.Error())
	}

	// node刚删除过pod，可用内存会发生变化
	if err = m.updateNodeMemoryAndCpuAllocatable(node, k8sNode); err != nil {
		return err
	}

	// 设置当前node的pod的资源限制
	if err = m.setOriginalPodRequests(node, memorySize); err != nil {
		return err
	}

	node.InitTime = time.Now()
	l.V(5).Infof("init ok, set OriginalMemorySize=%d to node %s", node.OriginalMemorySize, node.ID)
	// 通过node 节点的资源信息(cpu memory)计算期望 pod的数量
	node.ExpectedPodCount = ComputeInitDeployPodCount(node, m.k8sInfo.Flavors)
	l.V(5).Infof("InitNode: node `%s`... %d ExpectedPodCount", node.Name, node.ExpectedPodCount)
	err = m.deployPodByNode(node)
	if err != nil {
		return err
	}
	// check and pull kata image
	if node.ServiceType == api.ServiceTypeCFCKata {
		result, err := m.podClient.CheckAndPullImages(node, m.k8sInfo.KataRuntimeImageIDList)
		if err != nil {
			return err
		}

		var curAvailImages []string
		for id, ifAvailable := range result {
			if ifAvailable {
				curAvailImages = append(curAvailImages, id)
			}
		}
		node.AvailableKataRuntimeImageIDs = curAvailImages
	}

	return nil
}

// updateNodeMemoryAllocatable 更新node空余内存
func (m *k8sNodeControl) getK8sNodeInfo(node *api.NodeInfo) (*kubeapi.Node, error) {
	return m.k8sClient.GetNodeInfo(node.Name)
}

// updateNodeMemoryAndCpuAllocatable 更新node空余内存
func (m *k8sNodeControl) updateNodeMemoryAndCpuAllocatable(node *api.NodeInfo, k8sNode *kubeapi.Node) error {
	l := m.logger.WithStringField(nodeIDKey, node.ID)
	if node.State != api.NodeStateK8sRaw {
		return errors.New("[updateNodeMemoryAndCpuAllocatable] node state should be K8S_raw")
	}
	memory := k8sNode.Status.Allocatable[kubeapi.ResourceMemory]
	cpu := k8sNode.Status.Allocatable[kubeapi.ResourceCPU]
	maxPods := k8sNode.Status.Allocatable["pods"]
	//获取所有的pods
	allPods, err := m.getAllNamespacesPods(node)
	if err != nil {
		return fmt.Errorf("get all pods failed: %s", err.Error())
	}
	//筛选namespace不是poolmgr的pod
	pods := []kubeapi.Pod{}
	for i := range allPods {
		pod := &allPods[i]
		if pod.GetNamespace() != api.K8sPoolmgrNamespace {
			pods = append(pods, allPods[i])
		}
	}
	//计算memory占用
	totalMemoryRequests, err := m.getMemory(pods)
	if err != nil {
		return err
	}
	//totalMemoryRequests已经计算了非poolmgr占用的memory，因此无需再减去deamonset 预留占用的memory
	memoryAllocatable, err := computeCountByNodeMemory(memory.String(), totalMemoryRequests,
		0, m.k8sInfo.NodeConfig.PodMemoryReservedRatio)
	if memoryAllocatable < 0 {
		l.V(5).Infof("computeCountByNodeMemory: node `%s` memoryAllocatable:%d less than 0", node.Name, memoryAllocatable)
		memoryAllocatable = 0
	}
	if err != nil {
		return err
	}
	if node.MemoryAvailable != memoryAllocatable {
		m.logger.WithStringField(nodeIDKey, node.ID).V(5).Infof("[updateNodeMemoryAllocatable] update node "+
			"allocatable memory from %s to %s", node.MemoryAvailable, memoryAllocatable)
		node.MemoryAvailable = memoryAllocatable
	}

	//计算cpu占用
	totalCpuRequests, err := m.getCpu(pods)
	if err != nil {
		return err
	}
	//totalCpuRequests已经计算了非poolmgr占用的cpu，因此无需再减去deamonset 预留占用的cpu
	cpuAvailable, err := computeCountByNodeCpu(cpu, totalCpuRequests,
		0, m.k8sInfo.NodeConfig.CpuUsageReservedRatio)
	if err != nil {
		return err
	}
	if cpuAvailable < 0 {
		l.V(5).Infof("computeCountByNodeCpu: node `%s` cpuAvailable:%d less than 0", node.Name, cpuAvailable)
		cpuAvailable = 0
	}
	node.CpuAvailable = cpuAvailable
	m.logger.WithStringField(nodeIDKey, node.ID).V(5).Infof("[updateNodeCpuAllocatable] update node "+
		"allocatable cpu from %d to %d", node.CpuAvailable, cpuAvailable)

	//计算podsNumAvailable，需要减去非pollmgr占用的pod数量
	podsNumAvailable, err := computeAvailablePodNum(maxPods, len(pods))
	if err != nil {
		return err
	}
	node.PodsNumAvailable = podsNumAvailable
	return nil
}

// getKubeSystemCostResource 获取占用的cpu资源
// 单位为m
func (m *k8sNodeControl) getCpu(pods []kubeapi.Pod) (int64, error) {

	var totalRequests int64 = 0
	for i := range pods {
		pod := &pods[i]
		containers := pod.Spec.Containers
		for j := range containers {
			container := &containers[j]
			resources := container.Resources
			cpu := resources.Requests.Cpu()

			unit := resource.MustParse("1m")
			ret, ok := new(inf.Dec).QuoRound(cpu.AsDec(), unit.AsDec(), 0, inf.RoundDown).Unscaled()
			if !ok {
				continue
			}
			totalRequests += ret
		}
	}
	return totalRequests, nil
}

// getMemory 获取占用的memory资源
// 单位为mi
func (m *k8sNodeControl) getMemory(pods []kubeapi.Pod) (int64, error) {

	var totalRequests int64 = 0
	for i := range pods {
		pod := &pods[i]
		containers := pod.Spec.Containers
		for j := range containers {
			container := &containers[j]
			resources := container.Resources
			memory := resources.Requests.Memory()
			unit := resource.MustParse("1Mi")
			ret, ok := new(inf.Dec).QuoRound(memory.AsDec(), unit.AsDec(), 0, inf.RoundDown).Unscaled()
			if !ok {
				continue
			}
			totalRequests += ret
		}
	}
	return totalRequests, nil
}

// 计算每个poolmgr的pod初始化时所需的cpu requests ,单位是m
func (m *k8sNodeControl) setOriginalPodRequests(node *api.NodeInfo, memorySize int64) error {
	// 记录初始化的内存
	node.OriginalMemorySize = memorySize
	cpu, _, _, _, err := m.k8sClient.ComputeCpuAndMenLimitForPod(node.OriginalCpuSize, memorySize)
	if err != nil {
		return err
	}
	unit := resource.MustParse("1m")
	cpuInt, ok := new(inf.Dec).QuoRound(cpu.AsDec(), unit.AsDec(), 0, inf.RoundDown).Unscaled()
	if !ok {
		return errors.New("[setOriginalPodRequests] unscaled unsuccessfully")
	}
	node.OriginalCpuSize = cpuInt

	return nil
}

func (m *k8sNodeControl) deployPodByNode(node *api.NodeInfo) error {
	if node.ExpectedPodCount <= 0 {
		return errors.New("insufficient resource to deploy")
	}
	return m.CreatePods(node, node.ExpectedPodCount)
}

func (m *k8sNodeControl) CheckNodeInCondition(node *api.NodeInfo, t uint) error {
	switch t {
	case NodeConditionPreparedForInit:
		return m.checkIfPreparedForInit(node)
	case NodeConditionFinishedInit:
		return m.checkIfAllPodsRunningAndPodImageCorrect(node)
	case NodeConditionCorrectRunner:
		return m.checkIfRunnerCorrect(node)
	default:
		panic("no implement")
	}
}

func (m *k8sNodeControl) CheckRunningPodRatio(node *api.NodeInfo, runningPodNum int) (bool, error) {
	return m.checkIfRunningPodOverRatio(node, runningPodNum)
}
func (m *k8sNodeControl) CheckNodeConditionFinishedInit(node *api.NodeInfo) (int, error) {
	return m.checkNodeConditionFinishedInit(node)
}

func (m *k8sNodeControl) getAllPods(node *api.NodeInfo) (pods []kubeapi.Pod, err error) {
	return m.k8sClient.GetPodsByLabelAndNode(nil, node.Name)
}

func (m *k8sNodeControl) getAllNamespacesPods(node *api.NodeInfo) (pods []kubeapi.Pod, err error) {
	return m.k8sClient.GetPodsByAllNamespacesAndByNode(node.Name)
}

// checkIfRunnerCorrect 判断node的runner是否过期，如果出错或者某个pod的runner过期，返回false
func (m *k8sNodeControl) checkIfRunnerCorrect(node *api.NodeInfo) error {
	pods, err := m.getAllPods(node)
	if err != nil {
		return fmt.Errorf("get all pods failed: %s", err.Error())
	}

	image := m.k8sInfo.ContainerImage

	// 检查每个pod中的容器image是否和当前配置一致
	for i := range pods {
		pod := &pods[i]
		containers := pod.Spec.Containers

		// 每个pod里必须是一个容器
		if len(containers) != 1 {
			return fmt.Errorf("pod %s has %d container", pod.Name, len(containers))
		}

		container := containers[0]
		if container.Image != image {
			return fmt.Errorf("pod %s image %s is outdated", container.Name, container.Image)
		}
	}
	return nil
}

// checkIfAllPodsRunning 判断是否node的所有pod都是running状态
func (m *k8sNodeControl) checkIfAllPodsRunningAndPodImageCorrect(node *api.NodeInfo) error {
	// 获取all pods
	pods, err := m.k8sClient.GetPodsByLabelAndNode(nil, node.Name)
	if err != nil {
		return fmt.Errorf("get all pods failed: %s", err.Error())
	}
	// 验证pod数量
	if len(pods) != int(node.PodCount) {
		return fmt.Errorf("get %d pods, but expected %d pods", len(pods), node.PodCount)
	}

	image := m.k8sInfo.ContainerImage
	for i := range pods {
		pod := &pods[i]
		if pod.Status.Phase != kubeapi.PodRunning {
			return fmt.Errorf("pod %s phase is not running but %s", pod.Name, pod.Status.Phase)
		}
		containers := pod.Spec.Containers

		if node.ServiceType != api.ServiceTypeCFCKata {
			// 每个pod里必须是一个容器 kata两个以上容器
			if len(containers) != 1 {
				return fmt.Errorf("pod %s has %d container", pod.Name, len(containers))
			}
		}

		container := containers[0]
		if container.Image != image {
			// try to recreate pmpod
			m.k8sClient.DeletePod(pod.Name, true)
			m.k8sClient.CreatePod(pod.Name, node.Name, node.ServiceType, node.OriginalCpuSize, node.OriginalMemorySize)
			return fmt.Errorf("pod %s image %s is outdated", container.Name, container.Image)
		}

	}
	// 确保此时node.ContainerImage 是最新的image
	node.ContainerImage = image

	// 验证ds image 是否与cluster里设置是否一致
	dsPods, err := m.k8sClient.GetDaemonSetsOnNode(node.Name)
	daemonSetCount := getDaemonSetByRunningMode(m.opt.RunningMode)
	if len(dsPods) != daemonSetCount {
		return fmt.Errorf("number of daemon set on node is %d, should be %d", len(dsPods), daemonSetCount)
	}
	for i := range dsPods {
		pod := &dsPods[i]

		if pod.Status.Phase != kubeapi.PodRunning {
			return fmt.Errorf("daemon set %s phase is not running but %s", pod.Name, pod.Status.Phase)
		}

		container := pod.Spec.Containers[0]

		if versionInfo, ok := m.k8sInfo.DsReleaseConfigMap[container.Name]; ok {
			if versionInfo.ImageID != "" && container.Image != versionInfo.ImageID {
				// delete the pod
				m.k8sClient.DeleteDaemonSetPod(pod.Name, true)
				return fmt.Errorf("DaemonSet: %s image check failed:  now is %s, expected %s", container.Name, container.Image,
					versionInfo.ImageID)
			}
			// 修正一下node ds md5
			node.DsReleaseConfigMap[container.Name] = versionInfo.Md5
		}

	}

	// 如果kata 节点，得保证至少可用的runtime
	if node.ServiceType == api.ServiceTypeCFCKata && len(node.AvailableKataRuntimeImageIDs) == 0 {
		return fmt.Errorf("kata node have no available kata runtime images")
	}

	return nil
}

// checkNodeConditionFinishedInit 判断是否node的所有pod都是running状态,如果不是返回Runningpods数量，方便计算RunningPodRatio
func (m *k8sNodeControl) checkNodeConditionFinishedInit(node *api.NodeInfo) (int, error) {
	// 获取all pods
	pods, err := m.k8sClient.GetPodsByLabelAndNode(nil, node.Name)
	if err != nil {
		return 0, fmt.Errorf("get all pods failed: %s", err.Error())
	}
	//计算runningPod数量
	var runningPodNum = 0
	image := m.k8sInfo.ContainerImage
	// 如果是extra资源池中的节点，有自己独立的镜像
	helper := resourcepool.NewHelper(m.logger)
	resourcePool := helper.GetResourcePoolFromNode(node)
	// 如果资源池关闭状态，则默认使用默认池的镜像
	if resourcePool != api.DefaultResourcePool {
		poolConfig := helper.GetExtraPoolConfig(m.k8sInfo, resourcePool)
		if poolConfig != nil {
			// 说明资源池开关开启，且该资源池配置存在，使用该资源池专门的镜像
			image = poolConfig.ContainerImage
		}
	}
	//是否所有的pod都是running状态,并且通过检查
	errorFlag := true
	for i := range pods {
		pod := &pods[i]
		if pod.Status.Phase != kubeapi.PodRunning {
			if errorFlag {
				err = fmt.Errorf("There have pods that are not running")
				errorFlag = false
			}
			continue
		}
		containers := pod.Spec.Containers

		if node.ServiceType != api.ServiceTypeCFCKata {
			// 每个pod里必须是一个容器 kata两个以上容器
			if len(containers) != 1 {
				if errorFlag {
					errorFlag = false
					err = fmt.Errorf("pod %s has %d container", pod.Name, len(containers))
				}
				continue
			}
		}

		container := containers[0]
		if container.Image != image {
			// try to recreate pmpod
			m.k8sClient.DeletePod(pod.Name, true)
			m.k8sClient.CreatePod(pod.Name, node.Name, node.ServiceType, node.OriginalCpuSize, node.OriginalMemorySize)
			if errorFlag {
				errorFlag = false
				err = fmt.Errorf("pod %s image %s is outdated", container.Name, container.Image)
			}
			continue
		}
		runningPodNum++
	}
	if runningPodNum != int(node.ExpectedPodCount) {
		return runningPodNum, fmt.Errorf("get %d pods, but expected %d pods", len(pods), node.ExpectedPodCount)
	}
	//存在非running状态的pod,或者没有通过检查
	if !errorFlag {
		return runningPodNum, err
	}
	//当前集群的节点数量=预期值
	node.PodCount = node.ExpectedPodCount
	// 确保此时node.ContainerImage 是最新的image
	node.ContainerImage = image

	// 验证ds image 是否与cluster里设置是否一致
	dsPods, err := m.k8sClient.GetDaemonSetsOnNode(node.Name)
	daemonSetCount := getDaemonSetByRunningMode(m.opt.RunningMode)
	if len(dsPods) != daemonSetCount {
		return runningPodNum, fmt.Errorf("number of daemon set on node is %d, should be %d", len(dsPods), daemonSetCount)
	}
	for i := range dsPods {
		pod := &dsPods[i]

		if pod.Status.Phase != kubeapi.PodRunning {
			return runningPodNum, fmt.Errorf("daemon set %s phase is not running but %s", pod.Name, pod.Status.Phase)
		}

		container := pod.Spec.Containers[0]

		if versionInfo, ok := m.k8sInfo.DsReleaseConfigMap[container.Name]; ok {
			if versionInfo.ImageID != "" && container.Image != versionInfo.ImageID {
				// delete the pod
				m.k8sClient.DeleteDaemonSetPod(pod.Name, true)
				return runningPodNum, fmt.Errorf("DaemonSet: %s image check failed:  now is %s, expected %s", container.Name, container.Image,
					versionInfo.ImageID)
			}
			// 修正一下node ds md5
			node.DsReleaseConfigMap[container.Name] = versionInfo.Md5
		}

	}

	// 如果kata 节点，得保证至少可用的runtime
	if node.ServiceType == api.ServiceTypeCFCKata && len(node.AvailableKataRuntimeImageIDs) == 0 {
		return runningPodNum, fmt.Errorf("kata node have no available kata runtime images")
	}

	return runningPodNum, nil
}

// checkIfRunningOverRatio 初始化超时时间后情况下，如果已经running 的 pod达到既定设置比例也认为初始化成功,true 表示达到比例，false表示未达到
func (m *k8sNodeControl) checkIfRunningPodOverRatio(node *api.NodeInfo, runningPodNum int) (bool, error) {
	l := m.logger.WithStringField(nodeIDKey, node.ID)
	// 获取all pods
	ratio := float64(runningPodNum) / float64(node.ExpectedPodCount)
	l.V(5).Infof("node %s ,runningPodNum:%d", node.Name, runningPodNum)
	if ratio >= node.Flavor.RunningPodRatio {
		//更新podCount的个数
		node.PodCount = int64(runningPodNum)
		// 清除poolmgr下pending的pod
		l.V(5).Infof("clean pending pods on node %s", node.Name)
		err := m.k8sClient.CleanNamespacePendingPods(node.Name, api.K8sPoolmgrNamespace)
		if err != nil {
			l.Errorf("clean poolmgr pending pods on node %s failed: %s", node.Name, err.Error())
			return false, err
		}
		return true, nil
	} else {
		return false, nil
	}
}

// checkIfPreparedForInit 判断node是否满足初始化的条件
func (m *k8sNodeControl) checkIfPreparedForInit(node *api.NodeInfo) error {
	pods, err := m.k8sClient.GetDaemonSetsOnNode(node.Name)
	if err != nil {
		return fmt.Errorf("[checkIfPreparedForInit] get daemon set on node %s failed: %s", node.ID, err.Error())
	}

	daemonSetCount := getDaemonSetByRunningMode(m.opt.RunningMode)
	if len(pods) != daemonSetCount {
		return fmt.Errorf("[checkIfPreparedForInit] number of daemon set on node is %d, should be %d", len(pods), daemonSetCount)
	}

	for _, p := range pods {
		if p.Status.Phase != kubeapi.PodRunning {
			return fmt.Errorf("[checkIfPreparedForInit] daemon set %s phase is not running but %s", p.Name, p.Status.Phase)
		}

	}

	// node在初始化之前确保所有的业务pod都被删除了，确保pod不会创建过多。
	pmPods, err := m.k8sClient.GetPodsByLabelAndNode(nil, node.Name)
	if err != nil {
		return fmt.Errorf("[checkIfPreparedForInit]: get pmpod error:%s", err.Error())
	}

	if len(pmPods) != 0 {
		err = m.DeletePods(node, true)
		if err != nil {
			return fmt.Errorf("[checkIfPreparedForInit]: delete pmpod error:%s", err.Error())
		}
		return fmt.Errorf("[checkIfPreparedForInit] node %s all pmpod shuould delete first", node.ID)
	}
	return nil
}

func (m *k8sNodeControl) DeletePod(node *api.NodeInfo, podName string) error {
	return m.k8sClient.DeletePod(podName, true)
}

func (m *k8sNodeControl) DeletePods(node *api.NodeInfo, immediately bool) error {
	l := m.logger.WithStringField(nodeIDKey, node.ID)

	// 删除 node 上的所有 pod
	pods, err := m.k8sClient.GetPodsByLabelAndNode(nil, node.Name)
	for _, pod := range pods {
		l.V(5).Infof("[DeletePods]: delete container=%s", pod.Name)
		if err = m.k8sClient.DeletePod(pod.Name, immediately); err != nil {
			l.Errorf("[DeletePods]: delete container failed, container=%v", pod)
		}
	}

	return nil
}

func (m *k8sNodeControl) CreatePods(node *api.NodeInfo, num int64) error {
	l := m.logger.WithStringField(nodeIDKey, node.ID)
	l.V(5).Infof("[CreatePods] podNumber=%d", num)
	if node.OriginalMemorySize == 0 {
		return errors.New("zero original memory")
	}

	for i := int64(0); i < num; i++ {
		podName := generatePodName(node, m.opt.RunningMode)

		l.V(5).Infof("[CreatePods] deployPodByNode: create pod %s", podName)
		if err := m.k8sClient.CreatePod(podName, node.Name, node.ServiceType, node.OriginalCpuSize, node.OriginalMemorySize); err != nil {
			l.Errorf("[CreatePods] create and wait for pod %s running failed: %s", podName, err.Error())
			return err
		}
	}

	return nil
}

// rebuild a pod(delete the pod and then create the pod), if create timeout, return the new pod name.
func (m *k8sNodeControl) RebuildPod(node *api.NodeInfo, podName string) (string, error) {
	l := m.logger.WithStringField(nodeIDKey, node.ID)
	l.V(5).Infof("[RebuildPods] podName=%s", podName)

	// delete pod
	err := m.k8sClient.DeletePod(podName, true)
	if err != nil {
		l.Errorf("[RebuildPod] delete pod fail: %s: %v", podName, err)
		return "", err
	}
	l.V(5).Infof("[RebuildPods] delete podName=%s first", podName)

	// create pod
	newPodName := generatePodName(node, m.opt.RunningMode)
	l.V(5).Infof("[RebuildPods] oldPodName=%s, newPodName=%s", podName, newPodName)
	// watch pod
	stopChannel := make(chan struct{})
	defer close(stopChannel)

	watchTimeOut := m.k8sInfo.NodeConfig.KataPodRebuildTimeOut
	podCh, err := m.k8sClient.WatchPod(newPodName, stopChannel, watchTimeOut)
	if err != nil {
		l.Errorf("[RebuildPod] cannot start watcher for pod %s: %v", newPodName, err)
		return "", err
	}
	l.V(5).Infof("[RebuildPods] watch pod event: podName=%s", newPodName)

	err = m.k8sClient.CreatePod(newPodName, node.Name, node.ServiceType, node.OriginalCpuSize, node.OriginalMemorySize)
	if err != nil {
		l.Errorf("[RebuildPod] create pod failed %s: %v, oldPodName=%s", newPodName, err, podName)
		return "", err
	}
	l.V(5).Infof("[RebuildPods] create pod again: oldpodName=%s, newpodName=%s", podName, newPodName)
	// check if pod create success
	err = waitForPod(newPodName, podCh)
	if err != nil {
		l.Errorf("[RebuildPod] failed to waitForPod %s: %v", newPodName, err)

		return newPodName, fmt.Errorf("[RebuildPod] failed to waitForPod: %s", err)
	}

	return "", nil
}

type k8sNodeInfo kubeapi.Node

// ConvertToNodeInfo 将k8s node转换为node info
func (node *k8sNodeInfo) ConvertToNodeInfo(k8sInfo *api.K8sInfo) *api.NodeInfo {
	nodeID := node.GenerateID(k8sInfo.CceClusterUUID)
	nodeDSConfigMap := make(map[string]string, len(k8sInfo.DsReleaseConfigMap))
	for ds, dsConf := range k8sInfo.DsReleaseConfigMap {
		nodeDSConfigMap[ds] = dsConf.Md5
	}

	// 复制基础的ClusterLabels
	clusterLabels := make(map[string]string)
	for k, v := range k8sInfo.NodeConfig.ClusterLabels {
		clusterLabels[k] = v
	}

	// extractAndSetResourcePoolFromK8s: 从K8s节点标签中提取ResourcePool信息
	if resourcePool, exists := node.Labels["resourcePool"]; exists && resourcePool != "" {
		// 验证ResourcePool是否在配置中存在
		if isValidResourcePool(k8sInfo, resourcePool) {
			clusterLabels[api.LabelResourcePool] = resourcePool
			logs.V(6).Infof("Set ResourcePool label '%s' for node %s from K8s labels", resourcePool, node.Name)
		} else {
			logs.Warnf("Invalid ResourcePool '%s' found in K8s node %s labels, ignoring", resourcePool, node.Name)
		}
		// 如果无效，不设置标签，自动降级到默认池
	}

	nodeInfo := &api.NodeInfo{
		ID:                 nodeID,
		Name:               node.Name,
		ServiceType:        k8sInfo.ServiceType,
		ClusterLabels:      clusterLabels, // 使用处理后的ClusterLabels
		NodeLabels:         map[string]string{},
		OriginalMemorySize: 0,
		State:              api.NodeStateCceRaw,
		StateUpdateTime:    time.Now(),
		CceClusterUUID:     k8sInfo.CceClusterUUID,
		InstanceShortId:    node.GetInstanceShortID(),
		ContainerImage:     k8sInfo.ContainerImage,
		KataRuntimeImageID: k8sInfo.KataRuntimeImageID,
		DsReleaseConfigMap: nodeDSConfigMap,
		KataContainerSpec:  k8sInfo.KataContainerSpec,
	}
	return nodeInfo
}

// isValidResourcePool 验证ResourcePool是否在配置中存在
func isValidResourcePool(k8sInfo *api.K8sInfo, resourcePool string) bool {
	// 如果ResourcePool扩缩容配置未启用，则认为无效
	if k8sInfo.ResourcePoolConfig == nil || !k8sInfo.ResourcePoolConfig.ScalingEnabled {
		return false
	}

	// "default"总是有效的（代表默认池）
	if resourcePool == "default" {
		return true
	}

	// 检查是否在extraResourcePools中存在
	if k8sInfo.ResourcePoolConfig.ExtraResourcePools != nil {
		_, exists := k8sInfo.ResourcePoolConfig.ExtraResourcePools[resourcePool]
		return exists
	}

	return false
}

// GetInstanceShortID 解析获取cce使用的node instanceShortId，兼容k8s新旧版本
func (node *k8sNodeInfo) GetInstanceShortID() string {
	var shortID string
	u, err := url.Parse(node.Spec.ProviderID)
	if err == nil {
		shortID = u.Hostname()
	}

	if shortID == "" {
		shortID = node.Spec.DoNotUse_ExternalID
	}

	if shortID == "" {
		shortID = node.Name
	}
	return shortID
}

// generateNodeID 生成k8s node的唯一id，即etcd node id
func (node *k8sNodeInfo) GenerateID(cceClusterUUID string) string {
	return cceClusterUUID + ":" + node.Name + ":" + node.GetInstanceShortID()
}

// IsAvailableK8sNode 检查k8s获取的node是否可用
func (node *k8sNodeInfo) IsAvailableK8sNode() bool {
	for _, condition := range node.Status.Conditions {
		if condition.Type == kubeapi.NodeReady {
			if condition.Status == kubeapi.ConditionTrue {
				return true
			}
		}
	}
	return false
}

func (node *k8sNodeInfo) GetAddress(t kubeapi.NodeAddressType) string {
	for _, address := range node.Status.Addresses {
		if address.Type == t {
			return address.Address
		}
	}
	return ""
}
