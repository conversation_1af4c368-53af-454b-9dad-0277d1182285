#!/bin/bash

KUN_ROOT=$(dirname "${BASH_SOURCE}")/../..
source "${KUN_ROOT}/build/lib/init.sh"

GCOVER_OUTPUT_SUBPATH="${GCOVER_OUTPUT_SUBPATH:-_gcover_output/local}"
mkdir -p $GCOVER_OUTPUT_SUBPATH


targets=("${KUN_SERVER_TARGETS[@]}")
for target in "${targets[@]}"; do
    echo "building" $target
    binary=${target##*/}

    cd $target
    go build -o $binary
    mv $binary $KUN_ROOT/$GCOVER_OUTPUT_SUBPATH
    cd -
done