# CFC ResourcePool 实施检查清单

## 第一阶段：基础架构准备（2周）

### 1.1 etcd配置结构扩展
- [ ] **配置字段定义**
  - [ ] 在ClusterConfig中添加resourcePoolConfig字段
  - [ ] 在ClusterConfig中添加集群级scalingOptions和scalingUpOptions字段
  - [ ] 在ClusterConfig中添加description、osType、supportRuntimes字段
  - [ ] 定义ResourcePoolConfig结构体（只包含extraPools）
  - [ ] 定义ResourcePool结构体（支持可选字段）

- [ ] **配置简化设计**
  - [ ] 外部配置作为默认池配置
  - [ ] resourcePoolConfig只放extraPools（新增池子）
  - [ ] 使用supportRuntimes替代复杂的runtimeMappings
  - [ ] 移除SelectionStrategy等复杂配置

- [ ] **配置复用逻辑**
  - [ ] 实现GetEffectivePoolConfig方法
  - [ ] 实现containerImage复用逻辑（extraPools可选配置）
  - [ ] 实现scalingOptions逻辑（extraPools可以有自己的配置）
  - [ ] 实现scalingUpOptions复用逻辑（extraPools统一复用集群配置）
  - [ ] 确保默认池子直接使用外部配置

- [ ] **配置验证逻辑**
  - [ ] 实现配置字段验证函数
  - [ ] 添加池子名称唯一性检查
  - [ ] 添加supportRuntimes完整性检查
  - [ ] 验证配置复用的正确性

- [ ] **向后兼容性**
  - [ ] 确保resourcePoolConfig为nil时系统正常工作
  - [ ] 确保现有集群配置完全不受影响
  - [ ] 验证现有扩缩容逻辑继续工作
  - [ ] 确保现有容器镜像继续使用

### 1.2 ClusterConfigManager实现
- [ ] **基础功能**
  - [ ] 实现NewClusterConfigManager构造函数
  - [ ] 实现GetClusterConfig方法
  - [ ] 实现refreshConfigFromEtcd方法
  - [ ] 实现配置缓存机制

- [ ] **配置监听**
  - [ ] 实现watchAllConfigs方法
  - [ ] 实现配置变更事件处理
  - [ ] 实现配置版本管理
  - [ ] 添加配置变更日志

- [ ] **错误处理**
  - [ ] 实现getDefaultConfig降级逻辑
  - [ ] 添加etcd连接失败处理
  - [ ] 添加配置解析失败处理
  - [ ] 实现配置过期检查

### 1.3 ResourcePool选择逻辑
- [ ] **核心算法**
  - [ ] 实现SelectResourcePool方法
  - [ ] 实现findPoolsForRuntime方法
  - [ ] 实现selectBestPool方法
  - [ ] 实现isGPURuntime检查

- [ ] **运行时选择逻辑**
  - [ ] 实现基于supportRuntimes的池子选择
  - [ ] 实现默认池子降级逻辑
  - [ ] 添加运行时匹配算法

- [ ] **性能优化**
  - [ ] 实现运行时到池子的映射缓存
  - [ ] 添加选择结果缓存
  - [ ] 优化运行时匹配算法

### 1.4 Labels系统扩展
- [ ] **常量定义**
  - [ ] 添加LabelResourcePool常量
  - [ ] 更新相关文档和注释

- [ ] **Labels生成逻辑**
  - [ ] 扩展GetClusterLabels方法
  - [ ] 修改SortedLabels方法
  - [ ] 保持现有Labels逻辑不变

### 1.5 单元测试
- [ ] **配置管理测试**
  - [ ] ClusterConfigManager基础功能测试
  - [ ] 配置加载和缓存测试
  - [ ] 配置监听和更新测试
  - [ ] 错误场景测试

- [ ] **选择逻辑测试**
  - [ ] ResourcePool选择算法测试
  - [ ] 各种策略组合测试
  - [ ] 边界条件测试
  - [ ] 性能基准测试

### 1.6 验收标准检查
- [ ] 配置可以正确加载和热更新
- [ ] ResourcePool选择逻辑单元测试通过
- [ ] 现有功能不受影响
- [ ] 代码覆盖率达到80%以上

## 第二阶段：Redis Key扩展和架构预留（2周）

### 2.1 Key生成逻辑扩展
- [ ] **NodeIndex和PodIndex修改**
  - [ ] 修改NodeIndex.Key()方法
  - [ ] 修改PodIndex.Key()方法
  - [ ] 保持原有Key格式兼容性
  - [ ] 添加ResourcePool标签处理

- [ ] **兼容性开关**
  - [ ] 实现新旧Key格式切换逻辑
  - [ ] 添加渐进式迁移支持
  - [ ] 确保并存期间功能正常

### 2.2 多资源架构预留
- [ ] **接口定义**
  - [ ] 定义ResourceManager接口
  - [ ] 预留AllocateCPU接口
  - [ ] 预留AllocateGPU接口
  - [ ] 实现DefaultResourceManager

- [ ] **Redis Key规范**
  - [ ] 建立可扩展的Key命名规范
  - [ ] 预留CPU资源Key格式
  - [ ] 预留GPU资源Key格式
  - [ ] 文档化Key设计原则

### 2.3 配置结构简化
- [ ] **简化配置设计**
  - [ ] 外部配置作为默认池配置
  - [ ] resourcePoolConfig只包含extraPools
  - [ ] 使用supportRuntimes替代复杂映射
  - [ ] 更新相关的Go结构体定义

- [ ] **扩容逻辑简化**
  - [ ] 简化节点标签设置逻辑
  - [ ] 只保留resourcePool标签
  - [ ] 更新cron模块扩容代码

### 2.4 性能优化
- [ ] **Redis Key分片测试**
  - [ ] 设计分片性能测试用例
  - [ ] 对比新旧Key格式性能
  - [ ] 测试并发访问性能
  - [ ] 分析内存使用情况

- [ ] **基准测试**
  - [ ] 建立性能基准线
  - [ ] 测试内存资源操作延迟
  - [ ] 测试配置加载性能
  - [ ] 测试ResourcePool选择性能

### 2.5 监控和告警
- [ ] **关键指标定义**
  - [ ] 定义Redis Key数量指标
  - [ ] 定义操作延迟指标
  - [ ] 定义配置更新指标
  - [ ] 定义错误率指标

- [ ] **告警规则**
  - [ ] 配置Redis性能告警
  - [ ] 配置配置加载失败告警
  - [ ] 配置ResourcePool选择失败告警

### 2.6 验收标准检查
- [ ] 新旧Key格式可以并存
- [ ] 内存资源分配正常工作
- [ ] 架构支持后续多资源扩展
- [ ] 性能指标不下降
- [ ] 监控指标正常采集

## 第三阶段：模块集成和调度逻辑（2周）

### 3.1 eventhub集成
- [ ] **核心集成**
  - [ ] 在EventhubCore中集成ClusterConfigManager
  - [ ] 修改NewEventhubCore构造函数
  - [ ] 实现getClusterIDFromUserContext方法
  - [ ] 在getFreePod中添加ResourcePool选择

- [ ] **API兼容性**
  - [ ] 确保现有API接口不变
  - [ ] 确保响应格式不变
  - [ ] 添加可选的ResourcePool信息
  - [ ] 保持错误处理逻辑

- [ ] **降级机制**
  - [ ] 实现ResourcePool选择失败降级
  - [ ] 实现配置加载失败降级
  - [ ] 添加降级日志和监控
  - [ ] 确保降级时功能正常

### 3.2 poolmanager适配
- [ ] **标签传递**
  - [ ] 确保ResourcePool标签正确传递
  - [ ] 验证Redis Key生成逻辑
  - [ ] 测试标签在调用链中的传递

- [ ] **Redis Key适配**
  - [ ] 适配新的Key格式
  - [ ] 保持对旧Key格式的支持
  - [ ] 测试Key格式切换逻辑

### 3.3 reserve_ctrl适配
- [ ] **接口适配**
  - [ ] 适配新的Redis Key格式
  - [ ] 保持现有内存资源分配逻辑
  - [ ] 预留多资源接口（不实现）

- [ ] **兼容性测试**
  - [ ] 测试新旧Key格式兼容性
  - [ ] 测试资源分配和释放逻辑
  - [ ] 验证错误处理逻辑

### 3.4 端到端测试
- [ ] **调度流程测试**
  - [ ] 测试完整的函数调用流程
  - [ ] 测试不同运行时的调度
  - [ ] 测试ResourcePool选择逻辑
  - [ ] 测试降级场景

- [ ] **性能测试**
  - [ ] 测试调度延迟
  - [ ] 测试并发调度性能
  - [ ] 对比新旧模式性能
  - [ ] 分析性能瓶颈

### 3.5 验收标准检查
- [ ] 端到端调度流程正常工作
- [ ] 新旧模式可以无缝切换
- [ ] 调度延迟不增加
- [ ] 所有单元测试通过
- [ ] 集成测试通过

## 第四阶段：cron模块扩展和系统集成（2周）

### 4.1 cron模块扩展
- [ ] **扩容逻辑**
  - [ ] 实现isResourcePoolEnabled检查
  - [ ] 实现scaleUpWithResourcePool方法
  - [ ] 实现shouldScalePool判断逻辑
  - [ ] 实现checkResourceThreshold检查

- [ ] **节点标签设置**
  - [ ] 简化节点标签设置逻辑
  - [ ] 只设置resourcePool标签
  - [ ] 测试标签设置正确性
  - [ ] 验证节点归属正确性

- [ ] **配置获取**
  - [ ] 保持现有配置获取方式
  - [ ] 确保配置解析正确
  - [ ] 添加配置验证逻辑
  - [ ] 处理配置缺失场景

### 4.2 系统集成测试
- [ ] **功能测试**
  - [ ] 端到端功能测试
  - [ ] 多集群环境测试
  - [ ] 不同运行时测试
  - [ ] 异常场景测试

- [ ] **性能测试**
  - [ ] 性能基准测试
  - [ ] 压力测试
  - [ ] 长时间稳定性测试
  - [ ] 资源使用情况测试

- [ ] **兼容性测试**
  - [ ] 新旧模式切换测试
  - [ ] 配置迁移测试
  - [ ] 版本兼容性测试
  - [ ] 回滚测试

### 4.3 监控和运维工具
- [ ] **状态监控**
  - [ ] ResourcePool状态监控
  - [ ] 节点分布监控
  - [ ] 资源利用率监控
  - [ ] 调度成功率监控

- [ ] **运维工具**
  - [ ] 配置验证工具
  - [ ] 故障排查工具
  - [ ] 性能分析工具
  - [ ] 日志分析工具

### 4.4 文档和培训
- [ ] **技术文档**
  - [ ] 更新架构文档
  - [ ] 更新运维手册
  - [ ] 更新故障排查指南
  - [ ] 更新API文档

- [ ] **培训材料**
  - [ ] 准备技术培训材料
  - [ ] 准备运维培训材料
  - [ ] 录制演示视频
  - [ ] 组织内部培训

### 4.5 验收标准检查
- [ ] ResourcePool扩缩容正常工作
- [ ] 系统整体功能正常
- [ ] 新旧模式可以平滑切换
- [ ] 监控和告警完善
- [ ] 文档和培训完成

## 上线前最终检查

### 安全检查
- [ ] 代码安全审查
- [ ] 配置安全检查
- [ ] 权限控制验证
- [ ] 敏感信息检查

### 性能检查
- [ ] 性能基准达标
- [ ] 资源使用合理
- [ ] 无内存泄漏
- [ ] 无性能回归

### 稳定性检查
- [ ] 长时间稳定性测试通过
- [ ] 异常场景处理正确
- [ ] 降级机制有效
- [ ] 回滚方案可行

### 运维准备
- [ ] 监控告警配置完成
- [ ] 运维文档准备完成
- [ ] 应急预案准备完成
- [ ] 技术支持团队培训完成

### 发布准备
- [ ] 发布计划制定完成
- [ ] 灰度发布策略确定
- [ ] 回滚方案准备完成
- [ ] 发布通知发送完成
