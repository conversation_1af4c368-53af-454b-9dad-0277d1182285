package main

import (
	"bytes"
	"encoding/json"
	"flag"
	"fmt"
	"io"
	"log"
	"net/http"
	"os"
	"strings"
	"time"

	"github.com/google/uuid"

	"icode.baidu.com/baidu/faas/kun/pkg/bce/auth"
	"icode.baidu.com/baidu/faas/kun/pkg/bce/iam"
)

var (
	defaultEndpointMap = map[string]string{
		"bj": "http://10.16.82.133",
		"gz": "http://10.16.82.133",
		"su": "http://10.16.82.133",
	}
)
var (
	endpoint    string
	iamconfig   string
	accesskey   string
	secrectkey  string
	accountID   string
	serviceName string
	region      string
	chargeStr   string
	chargeItems []string
	startmStr   string
	startTime   time.Time
	durationStr string
	duration    time.Duration
	minuterdc   string
	dayrdc      string
	monthrdc    string
	removeZero  bool
	iamClient   iam.ClientInterface
)

func init() {
	flag.StringVar(&endpoint, "endpoint", "", "账单服务Endpoint")
	flag.StringVar(&iamconfig, "iamconfig", "", "服务IAM配置文件")
	flag.StringVar(&accountID, "account", "", "用户账号ID")
	flag.StringVar(&serviceName, "service", "CFC", "服务名称")
	flag.StringVar(&region, "region", "bj", "账单所属地域")
	flag.StringVar(&chargeStr, "charge-item", "PublicDataTransfer",
		"数据项，可选值：FunctionCallCount, FunctionRunTimeCount, PublicDataTransfer")
	flag.StringVar(&startmStr, "start-time", "", "起始时间，RFC3339格式")
	flag.StringVar(&durationStr, "duration", "24h", "间隔时间")
	flag.BoolVar(&removeZero, "remove-zero", false, "去除为0的数据点")
}

func checkArgs() []error {
	var errs []error
	if len(region) == 0 {
		errs = append(errs, fmt.Errorf("账单所属地域为空"))
	}
	if len(endpoint) == 0 {
		endpoint = defaultEndpointMap[region]
		if len(endpoint) == 0 {
			errs = append(errs, fmt.Errorf("账单服务Endpoint为空"))
		}
	}
	iamClient, err := iam.CreateIAMClient(iamconfig)
	if err != nil {
		errs = append(errs, fmt.Errorf("解析IAM配置失败: %s", err.Error()))
	} else {
		auth, err := iamClient.ServiceAuth()
		if err != nil {
			errs = append(errs, fmt.Errorf("获取服务AK/SK失败: %s", err.Error()))
		} else {
			accesskey = auth.AccessKey
			secrectkey = auth.SecretKey
		}
	}
	if len(accountID) == 0 {
		errs = append(errs, fmt.Errorf("用户账号ID为空"))
	}
	parts := strings.Split(chargeStr, ",")
	for _, p := range parts {
		p = strings.TrimSpace(p)
		if len(p) != 0 {
			chargeItems = append(chargeItems, p)
		}
	}
	if len(chargeItems) == 0 {
		errs = append(errs, fmt.Errorf("计费项为空"))
	}
	tm, err := time.Parse(time.RFC3339, startmStr)
	if err != nil {
		errs = append(errs, fmt.Errorf("解析起始时间失败：%s", err.Error()))
	} else {
		startTime = tm
	}
	du, err := time.ParseDuration(durationStr)
	if err != nil {
		errs = append(errs, fmt.Errorf("解析时间范围失败：%s", err.Error()))
	} else {
		duration = du
	}
	if duration < 0 {
		errs = append(errs, fmt.Errorf("时间范围错误"))
	}
	return errs
}

type OriginDataRequest struct {
	AccountID   string `json:"accountId"`
	ServiceName string `json:"serviceName"`
	Region      string `json:"region"`
	ChargeItem  string `json:"chargeItem"`
	StartTime   string `json:"startTime"`
	EndTime     string `json:"endTime"`
}

type OriginDataResponse struct {
	AccountID   string  `json:"accountId"`
	ServiceName string  `json:"serviceName"`
	InstanceId  string  `json:"instanceId"`
	Region      string  `json:"region"`
	Amount      float64 `json:"amount"`
	Unit        string  `json:"unit"`
	StartTime   string  `json:"startTime"`
	EndTime     string  `json:"endTime"`
	ClkTime     string  `json:"clkTime"`
}

func getUsageOriginData(request *OriginDataRequest) ([]OriginDataResponse, error) {
	body := new(bytes.Buffer)
	err := json.NewEncoder(body).Encode(request)
	if err != nil {
		log.Println(err)
		return nil, err
	}
	req, err := http.NewRequest(http.MethodPost, endpoint+"/v1/resourceusage/usage/origin/data", body)
	req.Header.Set("content-type", "application/json")
	req.Header.Set("x-bce-date", time.Now().Format(time.RFC3339))
	req.Header.Set("Host", req.Host)
	reqid, _ := uuid.NewUUID()
	req.Header.Set("x-bce-request-id", reqid.String())
	auth := auth.NewBceAuth(accesskey, secrectkey)
	signature := auth.NewSigner().
		Method(req.Method).
		Path(req.URL.Path).
		Headers(req.Header).
		WithSignedHeader().
		Expire(3600).
		GetSign()
	req.Header.Set("Authorization", signature)

	client := &http.Client{}
	rsp, err := client.Do(req)
	if err != nil {
		log.Println(err)
		return nil, err
	}
	defer rsp.Body.Close()
	if rsp.StatusCode != http.StatusOK {
		io.Copy(os.Stderr, rsp.Body)
		return nil, fmt.Errorf("request failed")
	}
	var data []OriginDataResponse
	err = json.NewDecoder(rsp.Body).Decode(&data)
	if err != nil {
		log.Println(err)
		return nil, err
	}
	return data, nil
}

func main() {
	flag.Parse()
	if errs := checkArgs(); len(errs) > 0 {
		for _, e := range errs {
			fmt.Println(e.Error())
		}
		return
	}
	duration.String()
	buffer := new(bytes.Buffer)
	buffer.WriteString(fmt.Sprintf("%24s%22s%10s\n", "ChargeItem", "Value", "Unit"))
	for _, item := range chargeItems {
		var amount float64
		unit := "Unknown"

		st := startTime
		last := st.Add(duration)
		if last.After(time.Now()) {
			last = time.Now()
		}

		for st.Before(last) {
			et := st.Add(24 * time.Hour)
			if et.After(last) {
				et = last.Add(1 * time.Second)
			}

			req := &OriginDataRequest{
				AccountID:   accountID,
				ServiceName: serviceName,
				Region:      region,
				ChargeItem:  item,
				StartTime:   st.Format(time.RFC3339),
				EndTime:     et.Format(time.RFC3339),
			}
			rsp, err := getUsageOriginData(req)
			if err != nil {
				return
			}

			if len(rsp) > 0 {
				unit = rsp[0].Unit
				for _, d := range rsp {
					amount += d.Amount
				}
			}
			st = et
		}
		buffer.WriteString(fmt.Sprintf("%24s%22f%10s\n", item, amount, unit))
	}
	fmt.Println(buffer.String())
}
