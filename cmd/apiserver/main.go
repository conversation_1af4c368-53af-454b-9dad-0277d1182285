/**
 * Created Date: Tuesday, July 25th 2017, 1:28:36 pm
 * Author: helonghua
 * -----
 * Modified By: helonghua
 * -----
 * Copyright (c) 2017 Baidu.Inc
 *
 */

package main

import (
	"fmt"
	"os"

	"github.com/spf13/pflag"

	"icode.baidu.com/baidu/faas/kun/cmd/apiserver/app"
	"icode.baidu.com/baidu/faas/kun/cmd/apiserver/options"
	"icode.baidu.com/baidu/faas/kun/pkg/server"
	"icode.baidu.com/baidu/faas/kun/pkg/util/flag"
	"icode.baidu.com/baidu/faas/kun/pkg/util/logs"
	"icode.baidu.com/baidu/faas/kun/pkg/version/verflag"
)

func main() {

	s := options.NewApiServerOptions()
	s.AddFlags(pflag.CommandLine)

	flag.InitFlags()
	logs.InitLogs()

	verflag.PrintAndExitIfRequested()

	stopCh := server.SetupSignalHandler()
	if err := app.Run(s, stopCh); err != nil {
		fmt.Fprintf(os.Stderr, "%v\n", err)
		os.Exit(1)
	}
}
