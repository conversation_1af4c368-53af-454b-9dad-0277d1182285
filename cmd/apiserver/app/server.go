package app

import (
	"io/ioutil"

	"github.com/emicklei/go-restful"
	"github.com/go-yaml/yaml"

	"icode.baidu.com/baidu/faas/kun/cmd/apiserver/options"
	"icode.baidu.com/baidu/faas/kun/pkg/api"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/controllers"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/controllers/v1/billing"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/controllers/v1/cloudfunc"
	triggerhandler "icode.baidu.com/baidu/faas/kun/pkg/apiserver/controllers/v1/trigger"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/controllers/v2/console"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/global"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/maindata"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/order"
	"icode.baidu.com/baidu/faas/kun/pkg/server"
	"icode.baidu.com/baidu/faas/kun/pkg/server/endpoint"
	trigger "icode.baidu.com/baidu/faas/kun/pkg/trigger/relation"
	"icode.baidu.com/baidu/faas/kun/pkg/util/logs"
	"icode.baidu.com/baidu/faas/kun/pkg/version"
)

// Run runs the specified EventhubServer with the given Dependencies.
// This should never exit.
func Run(runOptions *options.ApiServerOptions, stopCh <-chan struct{}) error {
	logs.Infof("Version: %+v", version.Get())

	s, err := CreateServerChain(runOptions, stopCh)
	if err != nil {
		return err
	}

	return s.PrepareRun().Run(stopCh)
}

// CreateServerChain creates the apiservers connected via delegation.
func CreateServerChain(runOptions *options.ApiServerOptions, stopCh <-chan struct{}) (*server.GenericServer, error) {
	if err := Init(runOptions, stopCh); err != nil {
		return nil, err
	}

	config := server.NewRecommendedConfig()
	if err := runOptions.RecommendedOptions.ApplyTo(config); err != nil {
		return nil, err
	}

	s, err := config.Complete().New(options.ApplicationName)
	if err != nil {
		return nil, err
	}

	// Install hook
	//s.AddPreShutdownHook("telemetryShutdownHook", config.TelemetryInfo.Stop)

	// Install API
	InstallAPI(s.Handler.GoRestfulContainer)

	return s, nil
}

// Init xxx
func Init(runOptions *options.ApiServerOptions, stopCh <-chan struct{}) error {

	// 通过读文件方式加载参数
	if err := LoadOptionConf(runOptions, runOptions.ArgsPath); err != nil {
		return err
	}

	api.MaxRetryAttempts = runOptions.MaxRetryAttempts
	api.MaxRetryIntervalInSeconds = runOptions.MaxRetryIntervalInSeconds

	//初始化bos CodeManager

	order.NewOrderManager()
	order.NewBillingChargeClient()
	registerTriggers()

	// 初始化DataCenterManager
	maindata.NewMaindataManager()

	if err := global.NewApiserverCore(runOptions, stopCh); err != nil {
		return err
	}
	// 注册自定义govalidator tag
	global.RegisterFaasRuntimeGovalidatorTag()

	if runOptions.AccountCodeSizetLimit != 0 {
		api.DefaultTotalCodeSizeLimit = runOptions.AccountCodeSizetLimit
	}

	api.InitHeader(runOptions.IAMConfiguration.EnableInf)

	return nil
}

func LoadOptionConf(opts interface{}, confPath string) error {
	//兼容之前的传参
	if confPath == "" {
		return nil
	}

	data, err := ioutil.ReadFile(confPath)

	if err != nil {
		return err
	}

	if err = yaml.Unmarshal(data, opts); err != nil {
		return err
	}

	return nil

}

// 注册触发器
func registerTriggers() {
	trigger.RegisterTrigger(trigger.NewBosTrigger())
	trigger.RegisterTrigger(trigger.NewDuerosTrigger())
	trigger.RegisterTrigger(trigger.NewHTTPTrigger())
	trigger.RegisterTrigger(trigger.NewDuEdgeTrigger())
	trigger.RegisterTrigger(trigger.NewCdnTrigger())
	trigger.RegisterTrigger(trigger.NewCrontabTrigger())
	trigger.RegisterTrigger(trigger.NewCfcEdgeTrigger())
	trigger.RegisterTrigger(trigger.NewAPIGatewayTrigger())
	trigger.RegisterTrigger(trigger.NewBlsTrigger())
}

// InstallAPI xxx
func InstallAPI(container *restful.Container) {
	publicAPIs := new(cloudfunc.FunctionRest).APIs()
	publicAPIs = append(publicAPIs, new(controllers.EventSourceRest).APIs()...)
	publicAPIs = append(publicAPIs, new(cloudfunc.BlueprintRest).PublicAPIs()...)
	publicAPIs = append(publicAPIs, new(triggerhandler.PolicyRest).PublicAPIs()...)
	publicAPIs = append(publicAPIs, new(billing.OrderRest).APIs()...)
	publicAPIs = append(publicAPIs, new(triggerhandler.RelationRest).PublicAPIs()...)
	publicAPIs = append(publicAPIs, new(cloudfunc.RuntimeRest).PublicAPIs()...)
	publicAPIs = append(publicAPIs, new(cloudfunc.TestEvent).TestEventAPIs()...)
	publicAPIs = append(publicAPIs, new(cloudfunc.FunctionReservedRest).PublicAPIs()...)

	// function console接口
	publicAPIs = append(publicAPIs, new(cloudfunc.FunctionRest).ConsoleAPIs()...)
	publicAPIs = append(publicAPIs, new(cloudfunc.FunctionRest).InviteAPIs()...)
	publicAPIs = append(publicAPIs, new(cloudfunc.FunctionRest).AssistAuthorizationAPIs()...)
	publicAPIs = append(publicAPIs, new(triggerhandler.RelationRest).ConsoleAPIs()...)

	// cfc@edge activation
	publicAPIs = append(publicAPIs, new(cloudfunc.CfcEdgeRest).ConsoleAPIs()...)

	// account-limit
	accountAPIs := new(cloudfunc.AccountRest).APIs()

	// function concurrency
	concurrencyAPIs := new(cloudfunc.ConcurrencyRest).APIs()

	// layer
	layerAPIs := new(cloudfunc.LayerRest).PublicAPIs()
	layerAPIs = append(layerAPIs, new(cloudfunc.LayerRest).ConsoleApi()...)

	// v1 接口
	publicV1APIs := append(publicAPIs, accountAPIs...)
	publicV1APIs = append(publicV1APIs, concurrencyAPIs...)
	publicV1APIs = append(publicV1APIs, layerAPIs...)
	publicV1APIs = append(publicV1APIs, new(cloudfunc.RuntimeRest).APIs()...)

	// v2 接口
	publicV2APIs := new(console.ConsoleFunctionRest).ConsoleV2APIs()
	publicV2APIs = append(publicV2APIs, new(console.BlueprintRest).ConsoleAPIs()...)
	publicV2APIs = append(publicV2APIs, new(console.ServiceRest).ConsoleAPIs()...)

	// 内部请求
	privateAPIs := new(cloudfunc.RuntimeRest).APIs()
	privateAPIs = append(privateAPIs, new(cloudfunc.FunctionRest).InsideAPIs()...)
	privateAPIs = append(privateAPIs, new(triggerhandler.PolicyRest).InsideAPIs()...)
	privateAPIs = append(privateAPIs, new(billing.OrderRest).InsideAPIs()...)
	privateAPIs = append(privateAPIs, new(controllers.EventSourceRest).InsideAPIs()...)
	privateAPIs = append(privateAPIs, new(cloudfunc.CodeRest).InsideAPIs()...)
	privateAPIs = append(privateAPIs, new(cloudfunc.AliasResource).InsideAPIs()...)
	privateAPIs = append(privateAPIs, new(cloudfunc.FunctionReservedRest).InsideAPIs()...)
	privateAPIs = append(privateAPIs, new(cloudfunc.NetworkRest).InsideAPIs()...)

	installer := endpoint.NewApiInstaller([]endpoint.ApiVersion{
		{
			Prefix: "/2015-03-31",
			Group:  publicAPIs,
		},
		{
			Prefix: "/2016-08-19",
			Group:  accountAPIs,
		},
		{
			Prefix: "/2017-10-31",
			Group:  concurrencyAPIs,
		},
		{
			Prefix: "/2018-10-31",
			Group:  layerAPIs,
		},
		{
			Prefix: "/v1",
			Group:  publicV1APIs,
		},
		{
			Prefix: "/v2",
			Group:  publicV2APIs,
		},
		{
			Prefix: "/inside-v1",
			Group:  privateAPIs,
		},
	})

	installer.Install(container)
}
