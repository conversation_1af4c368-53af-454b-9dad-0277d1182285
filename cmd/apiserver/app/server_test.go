package app

import (
	"fmt"
	"testing"

	"github.com/stretchr/testify/assert"
	"icode.baidu.com/baidu/faas/kun/cmd/apiserver/options"
)

func TestAddFlags(t *testing.T) {
	opts := options.NewApiServerOptions()

	opts.ArgsPath = "args.yaml"

	LoadOptionConf(opts, opts.ArgsPath)

	fmt.Println(opts.CodeConfiguration)
	fmt.Println(opts.GenericRedisOptions.Endpoints)
	fmt.Println(opts.GenericRedisOptions.Password == "")
	fmt.Println(opts.GenericRedisOptions.KeyPrefix)
	fmt.Println(opts.GenericRedisOptions.EnableCluster)

	assert.NotNil(t, opts.CdnEndpoint)
}
