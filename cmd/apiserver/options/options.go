package options

import (
	"github.com/spf13/pflag"

	"icode.baidu.com/baidu/faas/kun/pkg/api"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/dbengine"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/squashfs"
	"icode.baidu.com/baidu/faas/kun/pkg/bce/iam"
	"icode.baidu.com/baidu/faas/kun/pkg/etcd"
	"icode.baidu.com/baidu/faas/kun/pkg/rpc/db"
	kunRedis "icode.baidu.com/baidu/faas/kun/pkg/rpc/redis"
	genericoptions "icode.baidu.com/baidu/faas/kun/pkg/server/options"
	"icode.baidu.com/baidu/faas/kun/pkg/whitelist"
)

const (
	// ApplicationName 指定应用名称
	ApplicationName = "apiserver"
)

// CodeConfiguration options 用户代码管理配置
type CodeConfiguration struct {
	StorageType                string `yaml:"StorageType"`
	Endpoint                   string `yaml:"Endpoint"`
	AccessKey                  string `yaml:"AccessKey"`
	Bucket                     string `yaml:"Bucket"`
	SecretAccessKey            string `yaml:"SecretAccessKey"`
	MigrationSericeBosEndpoint string `yaml:"MigrationServiceBosEndpoint"`
}

// NewIAMOptions 创建IAM鉴权客户端参数
func NewCodeOptions() *CodeConfiguration {
	return &CodeConfiguration{
		StorageType:                "bos",
		Endpoint:                   "https://bj.bcebos.com",
		AccessKey:                  "396528c7d8ab4f9fbd724ca1cd708462",
		SecretAccessKey:            "8ce85ae89f4d4ae3b7c851f54a16a3af",
		Bucket:                     "cfc-bucket-bj",
		MigrationSericeBosEndpoint: "https://bj.bcebos.com",
	}
}

// AddUniversalFlags parse the flags
func (s *CodeConfiguration) AddCodeFlags(fs *pflag.FlagSet) {
	fs.StringVar(&s.StorageType, "storage-type", s.StorageType, "storage type: bos or minio; default bos")
	fs.StringVar(&s.Endpoint, "endpoint", s.Endpoint, "bos Endpoint for code storage")
	fs.StringVar(&s.AccessKey, "ak", s.AccessKey, "bos Access Key")
	fs.StringVar(&s.SecretAccessKey, "sk", s.SecretAccessKey, "bos Secret AccessKey")
	fs.StringVar(&s.Bucket, "bucket", s.Bucket, "bos Bucket")
}

type ApiServerOptions struct {
	ArgsPath                      string                             // 配置文件，通过读文件的方式加载
	RecommendedOptions            *genericoptions.RecommendedOptions `yaml:"RecommendedOptions"`
	CodeConfiguration             *CodeConfiguration                 `yaml:"CodeConfiguration"`
	DbConfiguration               *db.DbConfig                       `yaml:"DbConfiguration"`
	IAMConfiguration              *iam.IAMOptions                    `yaml:"IAMConfiguration"`
	WhiteListEndpoint             string                             `yaml:"WhiteListEndpoint"`
	EnableCheckWhiteList          bool                               `yaml:"EnableCheckWhiteList"`
	EtcdOptions                   *etcd.Options                      `yaml:"EtcdOptions"`
	GenericRedisOptions           *kunRedis.RedisOptions             `yaml:"GenericRedisOptions"`
	Region                        string                             `yaml:"Region"`
	BillingCatalogEndpoint        string                             `yaml:"BillingCatalogEndpoint"`
	BillingEndpoint               string                             `yaml:"BillingEndpoint"`
	BillingChargeEndpoint         string                             `yaml:"BillingChargeEndpoint"`
	RealNameQualificationEndpoint string                             `yaml:"RealNameQualificationEndpoint"`
	BillingPackageEndpoint        string                             `yaml:"BillingPackageEndpoint"`
	EnableCheckBilling            bool                               `yaml:"EnableCheckBilling"`
	EnableCheckDuedgeTrigger      bool                               `yaml:"EnableCheckDuedgeTrigger"`
	BlsEndpoint                   string                             `yaml:"BlsEndpoint"`
	SourceWhiteListOptions        *whitelist.WhiteListOptions        `yaml:"SourceWhiteListOptions"`
	OpsCenterEndpoint             string                             `yaml:"OpsCenterEndpoint"`
	BmsCertificateSN              string                             `yaml:"BmsCertificateSN"`
	BmsAccountID                  string                             `yaml:"BmsAccountID"`     // 百度消息服务超级证书所有者用户ID
	BmsEndPoint                   string                             `yaml:"BmsEndPoint"`      // 百度消息服务 endpoint
	RocketmqEndpoint              string                             `yaml:"RocketmqEndpoint"` // RocketMQ服务地址
	CdnEndpoint                   string                             `yaml:"CdnEndpoint"`
	VpcEndpoint                   string                             `yaml:"VpcEndpoint"`
	ProxyCtrlEndpoint             string                             `yaml:"ProxyCtrlEndpoint"`
	ApiGatewayEndpoint            string                             `yaml:"ApiGatewayEndpoint"`
	AccountCodeSizetLimit         uint64                             `yaml:"AccountCodeSizetLimit"`
	SquashFsOptions               *squashfs.SquashFsOptions          `yaml:"SquashFsOptions"`
	EnableCheckBms                bool                               `yaml:"EnableCheckBms"`
	FunctionTimeoutMax            int                                `yaml:"FunctionTimeoutMax"`            // 函数最大超时时间 单位秒
	FunctionMemorySizeMax         int                                `yaml:"FunctionMemorySizeMax"`         // 函数最大内存限制 单位M
	FunctionCodeSizeLimitZipped   string                             `yaml:"FunctionCodeSizeLimitZipped"`   // 函数zip包最大限制
	FunctionCodeSizeLimitUnzipped string                             `yaml:"FunctionCodeSizeLimitUnzipped"` // 函数解压最大限制
	PodConcurrentQuotaMax         int                                `yaml:"PodConcurrentQuotaMax"`         // 单实例多并发最大限制
	ReservedSpecMap               map[int][]*api.BccFlavor           `yaml:"ReservedSpecMap"`               // 预留实例规格配置
	CFSEndpoint                   string                             `yaml:"CFSEndpoint"`
	MaxRetryIntervalInSeconds     int64                              `yaml:"MaxRetryIntervalInSeconds"` // 异步调用消息保留最大时间限制
	MaxRetryAttempts              int                                `yaml:"MaxRetryAttempts"`          // 异步调用错误重试次数
	MaindataBNS                   string                             `yaml:"MaindataBNS"`               // 数据中心服务endpoint
	BillingOrderRenewEndpoint     string                             `yaml:"BillingOrderRenewEndpoint"` // 自动续费服务Endpoint
	VpcPermissionWhiteList        []string                           `yaml:"VpcPermissionWhiteList"`    // 允许配置Vpc功能的Uid列表
}

// NewApiServerOptions 新建一个ApiServerOptions对象，包含了所有的配置项。
// 返回值：*ApiServerOptions，指向新创建的ApiServerOptions对象
func NewApiServerOptions() *ApiServerOptions {
	iamConfig := iam.NewIAMOptions()
	codeConfig := NewCodeOptions()

	dbengine.DbConf = db.NewDbConfig()

	s := ApiServerOptions{
		ArgsPath:               "/home/<USER>/faas/apiserver/conf/args.yaml",
		RecommendedOptions:     genericoptions.NewRecommendedOptions(),
		IAMConfiguration:       iamConfig,
		CodeConfiguration:      codeConfig,
		DbConfiguration:        dbengine.DbConf,
		EtcdOptions:            genericoptions.NewEtcdOptions(),
		Region:                 "bj",
		WhiteListEndpoint:      "10.107.37.48:8690",
		EnableCheckWhiteList:   true,
		GenericRedisOptions:    kunRedis.NewRedisOptions(),
		BillingCatalogEndpoint: "facade.internal-qasandbox.baidu-int.com:8993/v1",
		// BillingCatalogEndpoint:        "nmg02-bce-test10.nmg02.baidu.com:8993/v1",
		BillingEndpoint: "order.internal-qasandbox.bce-internal.baidu-int.com",
		// BillingEndpoint:               "nmg02-bce-test6.nmg02.baidu.com:8003",
		BillingPackageEndpoint:        "bjyz-y22-sandbox001.bjyz.baidu.com:8666",
		BillingChargeEndpoint:         "bjyz-y22-sandbox002.bjyz.baidu.com:8130",
		RealNameQualificationEndpoint: "qualify.bce-sandbox.baidu.com",
		EnableCheckBilling:            false,
		EnableCheckDuedgeTrigger:      false,
		SourceWhiteListOptions:        &whitelist.WhiteListOptions{},
		OpsCenterEndpoint:             "http://settings.bce-internal.baidu.com",
		BmsCertificateSN:              "73d1af3f69ec46d98d9f494d7b8b7005",
		BmsAccountID:                  "c7ac82ae14ef42d1a4ffa3b2ececa17f",
		BmsEndPoint:                   "************:8222",
		CdnEndpoint:                   "http://cdn.baidubce.com",
		VpcEndpoint:                   "http://bcc.bj.baidubce.com",
		BlsEndpoint:                   "bls-sandbox.baidu-int.com",
		SquashFsOptions:               squashfs.NewSquashFsOptions(),
		EnableCheckBms:                true,
		FunctionTimeoutMax:            5,
		FunctionMemorySizeMax:         1024,
		FunctionCodeSizeLimitZipped:   "50M",
		FunctionCodeSizeLimitUnzipped: "250M",
		PodConcurrentQuotaMax:         10,
		CFSEndpoint:                   "http://cfs.bj.baidubce.com",
		MaxRetryAttempts:              api.MaxRetryAttempts,
		MaxRetryIntervalInSeconds:     api.MaxRetryIntervalInSeconds,
		ReservedSpecMap: map[int][]*api.BccFlavor{
			128: {
				{
					Cpu:          2,
					Memory:       4,
					SsdDiskSize:  30,
					MaxPodDeploy: 20,
				},
				{
					Cpu:          2,
					Memory:       8,
					SsdDiskSize:  30,
					MaxPodDeploy: 35,
				},
			},
		},
		VpcPermissionWhiteList: []string{},
	}
	s.RecommendedOptions.SecureServing.BindPort = 8080
	// s.RecommendedOptions.TelemetryOptions.ServiceName = ApplicationName
	return &s
}

func (s *ApiServerOptions) AddFlags(fs *pflag.FlagSet) {
	s.IAMConfiguration.AddUniversalFlags(fs)
	s.RecommendedOptions.AddFlags(fs)
	s.GenericRedisOptions.AddRedisFlags(fs)
	s.CodeConfiguration.AddCodeFlags(fs)
	s.DbConfiguration.AddFlags(fs)
	s.SourceWhiteListOptions.AddFlags(fs)
	s.SquashFsOptions.AddFlags(fs)
	genericoptions.AddEtcdFlags(s.EtcdOptions, fs)

	fs.StringVar(&s.ArgsPath, "args-path", s.ArgsPath, "the launching arguments of APIServer")
	fs.StringVar(&s.Region, "region", s.Region, "apiserver running region")

	fs.StringVar(&s.WhiteListEndpoint, "whitelist-endpoint", s.WhiteListEndpoint, "white list service endpoint")
	fs.BoolVar(&s.EnableCheckWhiteList, "enable-whitelist", s.EnableCheckWhiteList, "enable-whitelist")
	fs.StringVar(&s.BillingCatalogEndpoint, "billingCatalog-endpoint", s.BillingCatalogEndpoint, "billingCatalog list service endpoint")
	fs.StringVar(&s.BillingEndpoint, "billing-endpoint", s.BillingEndpoint, "billing list service endpoint")
	fs.StringVar(&s.BillingChargeEndpoint, "billingCharge-endpoint", s.BillingChargeEndpoint, "billing charge service endpoint")
	fs.StringVar(&s.BillingPackageEndpoint, "billingPackage-endpoint", s.BillingPackageEndpoint, "billing package service endpoint")
	fs.StringVar(&s.RealNameQualificationEndpoint, "realNameQualification-endpoint", s.RealNameQualificationEndpoint, "user real name Qualification endpoint")

	// 是否开启
	fs.BoolVar(&s.EnableCheckBilling, "enable-checkbilling", s.EnableCheckBilling, "enable-checkbilling")
	fs.BoolVar(&s.EnableCheckDuedgeTrigger, "enable-check-duedge-trigger", s.EnableCheckBilling, "enable-check-duedge-trigger")
	fs.BoolVar(&s.EnableCheckBms, "enable-checkbms", s.EnableCheckBms, "enable-checkbms")

	fs.StringVar(&s.OpsCenterEndpoint, "opscenter-endpoint", s.OpsCenterEndpoint, "BCE opscenter endpoint")
	fs.StringVar(&s.BmsCertificateSN, "bms-certificate-sn", s.BmsCertificateSN, "BMS certificate sn")
	fs.StringVar(&s.BmsAccountID, "bms-account-id", s.BmsAccountID, "BMS super certificate owner account id")
	fs.StringVar(&s.BmsEndPoint, "bms-endpoint", s.BmsEndPoint, "BMS endpoint")

	fs.StringVar(&s.BlsEndpoint, "bls-endpoint", s.BlsEndpoint, "BLS endpoint")

	fs.StringVar(&s.CdnEndpoint, "cdn-endpoint", s.CdnEndpoint, "CDN endpoint")
	fs.StringVar(&s.VpcEndpoint, "vpc-endpoint", s.VpcEndpoint, "VPC endpoint")
	fs.StringVar(&s.ProxyCtrlEndpoint, "proxyctrl-endpoint", s.ProxyCtrlEndpoint, "ProxyCtrl vip")
	fs.StringVar(&s.ApiGatewayEndpoint, "apigateway-endpoint", s.ApiGatewayEndpoint, "APIGateway endpoint")
	fs.Uint64Var(&s.AccountCodeSizetLimit, "account-code-size", s.AccountCodeSizetLimit, "account code size limit")
	fs.IntVar(&s.FunctionTimeoutMax, "function-timeout-max", s.FunctionTimeoutMax, "function timeout max")
	fs.IntVar(&s.FunctionMemorySizeMax, "function-memorysize-max", s.FunctionMemorySizeMax, "function memorysize max")
	fs.StringVar(&s.FunctionCodeSizeLimitZipped, "function-codesize-limit-zipped", s.FunctionCodeSizeLimitZipped, "function zipped code size limit")
	fs.StringVar(&s.FunctionCodeSizeLimitUnzipped, "function-codesize-limit-unzipped", s.FunctionCodeSizeLimitUnzipped, "function unzipped code size limit")
	fs.IntVar(&s.PodConcurrentQuotaMax, "pod-concurrent-quota-max", s.PodConcurrentQuotaMax, "pod concurrent quota max")
	fs.StringVar(&s.CFSEndpoint, "cfs-endpoint", s.CFSEndpoint, "CFS endpoint")
	fs.IntVar(&s.MaxRetryAttempts, "max-retry-attempts", s.MaxRetryAttempts, "sync max retry attempts")
	fs.IntVar(&api.CustomizedMemorySizeMax, "max-memory-limit", api.CustomizedMemorySizeMax, "max memory size limit")
	fs.IntVar(&api.DefaultBosFunctionTimeout, "max-bos-function-timeout-limit", api.DefaultBosFunctionTimeout, "max bos function timeout limit")
	fs.Int64Var(&s.MaxRetryIntervalInSeconds, "max-retry-interval-inseconds", s.MaxRetryIntervalInSeconds, "sync max retry interval in seconds")
}
