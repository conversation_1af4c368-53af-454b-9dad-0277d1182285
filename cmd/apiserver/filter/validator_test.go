package filter

import (
	"errors"
	"net/http/httptest"
	"strings"
	"testing"
	"unsafe"

	"github.com/emicklei/go-restful"
	"github.com/stretchr/testify/assert"

	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/global"
	"icode.baidu.com/baidu/faas/kun/pkg/server"
)

func buildNewKunCtx(method, uri, body, uid string, pathMapV map[string]string) *server.Context {
	request := httptest.NewRequest(method, uri, strings.NewReader(body))
	restReq := restful.NewRequest(request)
	restRsp := restful.NewResponse(httptest.NewRecorder())
	restReq.Request.Header.Set("Content-Type", "application/json")
	restRsp.SetRequestAccepts(restful.MIME_JSON)
	var pathMap = (*map[string]string)(unsafe.Pointer(uintptr(unsafe.Pointer(restReq)) + uintptr(unsafe.Sizeof(restReq.Request))))
	*pathMap = pathMapV
	return server.BuildContext(restReq, restRsp, "")
}

func TestValidatorCbdQueryParameter(t *testing.T) {
	global.MockAC()
	chain := &restful.FilterChain{}
	cases := []struct {
		in_c         *server.Context
		out_HttpCode int
	}{
		{
			in_c:         buildNewKunCtx("GET", "/v2/console/cbd/functions?Marker=a", "", "", map[string]string{}),
			out_HttpCode: 400,
		},
		{
			in_c:         buildNewKunCtx("GET", "/v2/console/cbd/functions?Marker=1&MaxItems=10&pageNo=1&pageSize=10&runtime=python@", "", "", map[string]string{}),
			out_HttpCode: 400,
		},
		{
			in_c:         buildNewKunCtx("GET", "/v2/console/cbd/functions?Marker=1&MaxItems=10&pageSize=10&runtime=python@", "", "", map[string]string{}),
			out_HttpCode: 400,
		},
		{
			in_c:         buildNewKunCtx("GET", "/v2/console/cbd/functions?Marker=10&MaxItems=100001&pageSize=10&runtime=python2", "", "", map[string]string{}),
			out_HttpCode: 400,
		},
		{
			in_c:         buildNewKunCtx("GET", "/v2/console/cbd/functions?Marker=1&MaxItems=10&pageNo=1&pageSize=10&runtime=python2&Order=FunctionName:asc,Description_:desc", "", "", map[string]string{}),
			out_HttpCode: 400,
		},
		{
			in_c:         buildNewKunCtx("GET", "/v2/console/cbd/functions?Order=FunctionName:asc1,Description:desc", "", "", map[string]string{}),
			out_HttpCode: 400,
		},
		{
			in_c:         buildNewKunCtx("GET", "/v2/console/cbd/functions?Order=FunctionName,Description:desc1", "", "", map[string]string{}),
			out_HttpCode: 400,
		},
		{
			in_c:         buildNewKunCtx("GET", "/v2/console/cbd/functions?Order=FunctionName:,Description:desc1", "", "", map[string]string{}),
			out_HttpCode: 400,
		},
	}
	for _, tc := range cases {
		ValidatorCbdQueryParameter(tc.in_c, chain)
		assert.Equal(t, tc.out_HttpCode, tc.in_c.Response().StatusCode())
	}
}

func TestValidatorQueryParameter(t *testing.T) {
	global.MockAC()
	chain := &restful.FilterChain{}
	cases := []struct {
		in_c         *server.Context
		out_HttpCode int
	}{
		{
			in_c:         buildNewKunCtx("GET", "/v2/console/functions?Marker=a", "", "", map[string]string{}),
			out_HttpCode: 400,
		},
		{
			in_c:         buildNewKunCtx("GET", "/v1/console/functions?Marker=1&MaxItems=10&page=1&pageSize=a", "", "", map[string]string{}),
			out_HttpCode: 400,
		},
		{
			in_c:         buildNewKunCtx("GET", "/v2/console/functions?Marker=1&MaxItems=10&pageNo=1&pageSize=10&orderBy=Function_Name", "", "", map[string]string{}),
			out_HttpCode: 400,
		},
		{
			in_c:         buildNewKunCtx("GET", "/v2/console/functions?Marker=1&MaxItems=10&pageNo=1&pageSize=10&orderBy=FunctionName&order=asc1", "", "", map[string]string{}),
			out_HttpCode: 400,
		},
		{
			in_c:         buildNewKunCtx("GET", "/v2/console/functions?Marker=1&MaxItems=100001&pageNo=1&pageSize=10&orderBy=FunctionName&order=asc", "", "", map[string]string{}),
			out_HttpCode: 400,
		},
		{
			in_c:         buildNewKunCtx("GET", "/v2/console/functions?Marker=1&MaxItems=10&pageNo=1&pageSize=10&&orderBy=FunctionName&order=asc&runtime=python@", "", "", map[string]string{}),
			out_HttpCode: 400,
		},
		{
			in_c:         buildNewKunCtx("GET", "/v2/console/functions?FunctionVersion=a", "", "", map[string]string{}),
			out_HttpCode: 400,
		},
	}
	for _, tc := range cases {
		ValidatorQueryParameter(tc.in_c, chain)
		assert.Equal(t, tc.out_HttpCode, tc.in_c.Response().StatusCode())
	}
}

func TestLogErr(t *testing.T) {
	global.MockAC()
	cases := []struct {
		in_c          *server.Context
		in_err        error
		in_apiVersion string
	}{
		{
			in_c:          buildNewKunCtx("GET", "/v2/console/functions", "", "", map[string]string{}),
			in_err:        errors.New("not nil"),
			in_apiVersion: "v2",
		},
		{
			in_c:          buildNewKunCtx("GET", "/v1/console/functions", "", "", map[string]string{}),
			in_err:        errors.New("not nil"),
			in_apiVersion: "v1",
		},
	}

	for _, tc := range cases {
		out_code := 400
		logErr(tc.in_c, tc.in_err, tc.in_apiVersion)
		assert.Equal(t, out_code, tc.in_c.Response().StatusCode())
	}

}
