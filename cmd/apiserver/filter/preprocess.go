package filter

import (
	"bytes"
	"errors"
	"io/ioutil"

	"github.com/emicklei/go-restful"

	"icode.baidu.com/baidu/faas/kun/pkg/server"
)

//处理request body.
//场景: body‘s payload 为""时无法被json.Unmarshal。需要主动设定为{}
func EmptyBodyPayloadToJson(c *server.Context, chain *restful.FilterChain) {
	r := c.Request().Request
	defer r.Body.Close()
	byteBody, err := ioutil.ReadAll(r.Body)
	if err != nil {
		c.WithErrorLog(errors.New("read from body failed")).WriteTo(c.Response())
		return
	}
	if len(byteBody) == 0 {
		r.Body = ioutil.NopCloser(bytes.NewBuffer([]byte("{}")))
	} else {
		r.Body = ioutil.NopCloser(bytes.NewBuffer(byteBody))
	}
	chain.ProcessFilter(c.Request(), c.Response())
}
