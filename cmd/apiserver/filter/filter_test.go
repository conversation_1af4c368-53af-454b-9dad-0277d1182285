package filter

import (
	"bytes"
	"encoding/json"
	"flag"
	"fmt"
	"io/ioutil"
	"net/http"
	"net/http/httptest"
	"os"
	"testing"
	"time"

	"github.com/google/uuid"

	"icode.baidu.com/baidu/faas/kun/pkg/api"
	"icode.baidu.com/baidu/faas/kun/pkg/util/convert"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/emicklei/go-restful"
	"github.com/jinzhu/gorm"

	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/dao"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/dbengine"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/global"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/order"
	"icode.baidu.com/baidu/faas/kun/pkg/bce/iam"
)

func TestMain(m *testing.M) {
	flag.Set("alsologtostderr", "true")
	flag.Set("log_dir", "/tmp")
	flag.Set("v", "3")
	flag.Parse()
	os.Exit(m.Run())
}

func TestCheckCFCWhiteList(t *testing.T) {
	t.Skip()
	global.MockAC()
	global.AC.Config.EnableCheckWhiteList = true

	request := httptest.NewRequest("POST", "/v1/console/invitation", nil)
	restReq := restful.NewRequest(request)
	domain := iam.Domain{
		ID:   "85ae1423f09f4660becb15d46402e9cd",
		Name: "PASSPORT:**********",
	}
	iamuser := iam.User{
		ID:     "85ae1423f09f4660becb15d46402e9cd",
		Name:   "PASSPORT:**********",
		Domain: &domain,
	}
	restReq.SetAttribute("User", &iamuser)
	response := httptest.NewRecorder()
	restRsp := restful.NewResponse(response)
	CheckCFCWhiteList()(restReq, restRsp, nil)
	println(response.Code)
	if response.Code != 200 {
		t.Error("check user white list failed.")
	}
}

func TestIAMPermissionCheck(t *testing.T) {
	global.MockAC()
	request := httptest.NewRequest("GET", "/v1/functions", nil)
	restReq := restful.NewRequest(request)
	response := httptest.NewRecorder()
	restRsp := restful.NewResponse(response)
	IAMPermissionCheck("*", "", []string{"cfc:CreateFunctions"})(restReq, restRsp, nil)
}

func TestIAMAuthenticateCheck(t *testing.T) {
	global.MockAC()
	request := httptest.NewRequest("GET", "/v1/functions", nil)
	restReq := restful.NewRequest(request)
	response := httptest.NewRecorder()
	restRsp := restful.NewResponse(response)
	IAMAuthenticateCheck(restReq, restRsp, nil)
}

func TestBillingCheck(t *testing.T) {
	global.MockAC()
	order.MockOrderManager()

	var m sqlmock.Sqlmock
	m, dbengine.Engine = MockDB()

	chain := &restful.FilterChain{
		Filters: []restful.FilterFunction{},
		Index:   1,
		Target:  func(req *restful.Request, resp *restful.Response) {},
	}

	u := dao.BillingResource{
		Id:        1,
		AccountId: "c7ac82ae14ef42d1a4ffa3b2ececa17f",
		UpdatedAt: time.Now(),
		CreatedAt: time.Now(),
	}

	domain := iam.Domain{
		ID:   "85ae1423f09f4660becb15d46402e9cd",
		Name: "PASSPORT:**********",
	}
	iamuser := iam.User{
		ID:     "c7ac82ae14ef42d1a4ffa3b2ececa17f",
		Name:   "PASSPORT:**********",
		Domain: &domain,
	}

	request := httptest.NewRequest("POST", "/v1/functions", nil)
	restReq := restful.NewRequest(request)
	restReq.SetAttribute("User", &iamuser)
	response := httptest.NewRecorder()
	restRsp := restful.NewResponse(response)

	cases := []struct {
		outHttpCode int
		caseFunc    func(sqlmock.Sqlmock, *dao.BillingResource)
	}{
		{
			outHttpCode: 200,
			caseFunc: func(m sqlmock.Sqlmock, u *dao.BillingResource) {
				global.AC.Config.EnableCheckBilling = false
				u.ResourceState = "STOPPED"
				m.ExpectQuery(".*").WillReturnRows(GetRowsBillingRes([]dao.BillingResource{*u}))
			},
		},
		{
			outHttpCode: 403,
			caseFunc: func(m sqlmock.Sqlmock, u *dao.BillingResource) {
				global.AC.Config.EnableCheckBilling = true
				u.CreateState = "CREATED"
				m.ExpectQuery(".*").WillReturnRows(GetRowsBillingRes([]dao.BillingResource{*u}))
			},
		},
	}

	for _, c := range cases {
		c.caseFunc(m, &u)
		BillingCheck(restReq, restRsp, chain)
		if response.Code != c.outHttpCode {
			t.Error(response.Code)
			t.Error("check billing failed.")
		}
	}
}

func TestCheckUserBilling(t *testing.T) {
	global.MockAC()
	request := httptest.NewRequest("POST", "/v1/billing/test/cfc/resources/test", nil)
	restReq := restful.NewRequest(request)
	domain := iam.Domain{
		ID:   "85ae1423f09f4660becb15d46402e9cd",
		Name: "PASSPORT:**********",
	}
	iamuser := iam.User{
		ID:     "85ae1423f09f4660becb15d46402e9cd",
		Name:   "PASSPORT:**********",
		Domain: &domain,
	}
	restReq.SetAttribute("User", &iamuser)
	response := httptest.NewRecorder()
	restRsp := restful.NewResponse(response)
	CheckUserBilling(restReq, restRsp, nil)
	if response.Code != 403 {
		t.Error("check user Billing failed.")
	}
}

func TestRealNameCheck(t *testing.T) {
	t.Skip()
	global.MockAC()
	request := httptest.NewRequest("POST", "/v1/functions", nil)
	restReq := restful.NewRequest(request)
	domain := iam.Domain{
		ID:   "c7ac82ae14ef42d1a4ffa3b2ececa17f",
		Name: "PASSPORT:**********",
	}
	iamuser := iam.User{
		ID:     "c7ac82ae14ef42d1a4ffa3b2ececa17f",
		Name:   "PASSPORT:**********",
		Domain: &domain,
	}
	restReq.SetAttribute("User", &iamuser)
	response := httptest.NewRecorder()
	restRsp := restful.NewResponse(response)
	var m sqlmock.Sqlmock
	m, dbengine.Engine = global.MockDB()
	m.ExpectQuery(".*").WillReturnRows(global.GetRowsRealName(global.GetTestRealName(1)))
	RealNameCheck(restReq, restRsp, nil)
	fmt.Println(response.Code)
	m.ExpectQuery(".*").WillReturnRows(global.GetRowsRealName(nil))
	RealNameCheck(restReq, restRsp, nil)
	fmt.Println(response.Code)
}

func TestCheckUserOrderExecutor(t *testing.T) {
	global.MockAC()
	request := httptest.NewRequest("POST", "/v1/order_executor/check", nil)
	restReq := restful.NewRequest(request)
	domain := iam.Domain{
		ID:   "85ae1423f09f4660becb15d46402e9cd",
		Name: "PASSPORT:**********",
	}
	iamuser := iam.User{
		ID:     "85ae1423f09f4660becb15d46402e9cd",
		Name:   "PASSPORT:**********",
		Domain: &domain,
	}
	restReq.SetAttribute("User", &iamuser)
	response := httptest.NewRecorder()
	restRsp := restful.NewResponse(response)
	IAMPermissionCheck("*", "", []string{"cfc:order_check"})(restReq, restRsp, nil)
	CheckUserBilling(restReq, restRsp, nil)
	if response.Code != 403 {
		t.Error("check user order_execute failed.")
	}
}

func MockDB() (sqlmock.Sqlmock, *gorm.DB) {
	db, mock, _ := sqlmock.New()
	gormDB, _ := gorm.Open("mysql", db)
	gormDB.LogMode(true)
	gormDB = gormDB.Set("gorm:update_column", true)
	dbengine.Engine = gormDB
	return mock, gormDB
}

func GetRowsBillingRes(as []dao.BillingResource) *sqlmock.Rows {
	var fieldNames = []string{"id", "account_id", "resource_state", "create_state", "updated_at", "created_at"}
	rows := sqlmock.NewRows(fieldNames)
	for _, a := range as {
		rows = rows.AddRow(a.Id, a.AccountId, a.ResourceState, a.CreateState, a.UpdatedAt, a.CreatedAt)
	}
	return rows
}

func TestCheckTriggerCondition(t *testing.T) {
	global.MockAC()
	tc := []struct {
		dataMissing   bool
		withoutFunc   bool
		noEdgeTrigger bool
		Function      *dao.Function
		resCode       int
	}{
		{
			dataMissing: true,
			Function: &dao.Function{
				Id:             uint(123),
				Uid:            "c7ac82ae14ef42d1a4ffa3b2ececa17f",
				FunctionBrn:    "brn:bce:cfc:bj:c7ac82ae14ef42d1a4ffa3b2ececa17f:function:CfcEdgeTrigger-wrong:1",
				Region:         "bj",
				Timeout:        convert.Int(3),
				EnvironmentStr: `{"Variables":{}}`,
				FunctionConfig: api.FunctionConfig{
					CodeSize:     int32(123),
					FunctionName: "CfcEdgeTrigger",
					Version:      "1",
					Runtime:      "nodejs6.11",
					MemorySize:   convert.Int(128),
				},
			},
			resCode: 400, //without func
		},
		{
			withoutFunc: true,
			Function: &dao.Function{
				Id:             uint(123),
				Uid:            "c7ac82ae14ef42d1a4ffa3b2ececa17f",
				FunctionBrn:    "brn:bce:cfc:bj:c7ac82ae14ef42d1a4ffa3b2ececa17f:function:CfcEdgeTrigger-wrong:1",
				Region:         "bj",
				Timeout:        convert.Int(3),
				EnvironmentStr: `{"Variables":{}}`,
				FunctionConfig: api.FunctionConfig{
					CodeSize:     int32(123),
					FunctionName: "CfcEdgeTrigger",
					Version:      "1",
					Runtime:      "nodejs6.11",
					MemorySize:   convert.Int(128),
				},
			},
			resCode: 404, //without func
		},
		{
			noEdgeTrigger: true,
			Function: &dao.Function{
				Id:             uint(123),
				Uid:            "c7ac82ae14ef42d1a4ffa3b2ececa17f",
				FunctionBrn:    "brn:bce:cfc:bj:c7ac82ae14ef42d1a4ffa3b2ececa17f:function:CfcEdgeTrigger:1",
				Region:         "bj",
				Timeout:        convert.Int(3),
				EnvironmentStr: `{"Variables":{}}`,
				FunctionConfig: api.FunctionConfig{
					CodeSize:     int32(123),
					FunctionName: "CfcEdgeTrigger",
					Version:      "1",
					Runtime:      "nodejs6.11",
					MemorySize:   convert.Int(128),
				},
			},
			resCode: 200, //other trigger
		},
		{
			Function: &dao.Function{
				Id:             uint(123),
				Uid:            "c7ac82ae14ef42d1a4ffa3b2ececa17f",
				FunctionBrn:    "",
				Region:         "bj",
				Timeout:        convert.Int(3),
				EnvironmentStr: `{"Variables":{}}`,
				FunctionConfig: api.FunctionConfig{
					CodeSize:     int32(123),
					FunctionName: "CfcEdgeTrigger",
					Version:      "1",
					Runtime:      "nodejs6.11",
					MemorySize:   convert.Int(128),
				},
			},
			resCode: 400, //invalid brn
		},
		{
			Function: &dao.Function{
				Id:             uint(123),
				Uid:            "c7ac82ae14ef42d1a4ffa3b2ececa17f",
				FunctionBrn:    "brn:bce:cfc:bj:c7ac82ae14ef42d1a4ffa3b2ececa17f:function:CfcEdgeTrigger:1",
				Region:         "bj",
				Timeout:        convert.Int(3),
				EnvironmentStr: `{"Variables":{}}`,
				FunctionConfig: api.FunctionConfig{
					CodeSize:     int32(2097152),
					FunctionName: "CfcEdgeTrigger",
					Version:      "1",
					Runtime:      "nodejs6.11",
					MemorySize:   convert.Int(128),
				},
			},
			resCode: 400, //codesize
		},
		{
			Function: &dao.Function{
				Id:             uint(123),
				Uid:            "c7ac82ae14ef42d1a4ffa3b2ececa17f",
				FunctionBrn:    "brn:bce:cfc:bj:c7ac82ae14ef42d1a4ffa3b2ececa17f:function:CfcEdgeTrigger:1",
				Region:         "bj",
				Timeout:        convert.Int(3),
				EnvironmentStr: `{"Variables":{}}`,
				FunctionConfig: api.FunctionConfig{
					CodeSize:     int32(123),
					FunctionName: "CfcEdgeTrigger",
					Version:      "1",
					Runtime:      "nodejs6.11",
					MemorySize:   convert.Int(256),
				},
			},
			resCode: 400, //memorysize
		},
		{
			Function: &dao.Function{
				Id:             uint(123),
				Uid:            "c7ac82ae14ef42d1a4ffa3b2ececa17f",
				FunctionBrn:    "brn:bce:cfc:bj:c7ac82ae14ef42d1a4ffa3b2ececa17f:function:CfcEdgeTrigger:1",
				Region:         "su",
				Timeout:        convert.Int(3),
				EnvironmentStr: `{"Variables":{}}`,
				FunctionConfig: api.FunctionConfig{
					CodeSize:     int32(123),
					FunctionName: "CfcEdgeTrigger",
					Version:      "1",
					Runtime:      "nodejs6.11",
					MemorySize:   convert.Int(128),
				},
			},
			resCode: 400, //region
		},
		{
			Function: &dao.Function{
				Id:             uint(123),
				Uid:            "c7ac82ae14ef42d1a4ffa3b2ececa17f",
				FunctionBrn:    "brn:bce:cfc:bj:c7ac82ae14ef42d1a4ffa3b2ececa17f:function:CfcEdgeTrigger:$LATEST",
				Region:         "bj",
				Timeout:        convert.Int(3),
				EnvironmentStr: `{"Variables":{}}`,
				FunctionConfig: api.FunctionConfig{
					CodeSize:     int32(123),
					FunctionName: "CfcEdgeTrigger",
					Version:      "$LATEST",
					Runtime:      "nodejs6.11",
					MemorySize:   convert.Int(128),
				},
			},
			resCode: 400, //version
		},
		{
			Function: &dao.Function{
				Id:             uint(123),
				Uid:            "c7ac82ae14ef42d1a4ffa3b2ececa17f",
				FunctionBrn:    "brn:bce:cfc:bj:c7ac82ae14ef42d1a4ffa3b2ececa17f:function:CfcEdgeTrigger:1",
				Region:         "bj",
				Timeout:        convert.Int(6),
				EnvironmentStr: `{"Variables":{}}`,
				FunctionConfig: api.FunctionConfig{
					CodeSize:     int32(123),
					FunctionName: "CfcEdgeTrigger",
					Version:      "1",
					Runtime:      "nodejs6.11",
					MemorySize:   convert.Int(128),
				},
			},
			resCode: 400, // timeout
		},
		{
			Function: &dao.Function{
				Id:             uint(123),
				Uid:            "c7ac82ae14ef42d1a4ffa3b2ececa17f",
				FunctionBrn:    "brn:bce:cfc:bj:c7ac82ae14ef42d1a4ffa3b2ececa17f:function:CfcEdgeTrigger:1",
				Region:         "bj",
				Timeout:        convert.Int(5),
				EnvironmentStr: `{"Variables":{}}`,
				FunctionConfig: api.FunctionConfig{
					CodeSize:     int32(123),
					FunctionName: "CfcEdgeTrigger",
					Version:      "1",
					Runtime:      "java8",
					MemorySize:   convert.Int(128),
				},
			},
			resCode: 400, // runtime
		},
		{
			Function: &dao.Function{
				Id:             uint(123),
				Uid:            "c7ac82ae14ef42d1a4ffa3b2ececa17f",
				FunctionBrn:    "brn:bce:cfc:bj:c7ac82ae14ef42d1a4ffa3b2ececa17f:function:CfcEdgeTrigger:1",
				Region:         "bj",
				Timeout:        convert.Int(5),
				EnvironmentStr: `{"Variables":{"key1": "value1"}}`,
				FunctionConfig: api.FunctionConfig{
					CodeSize:     int32(123),
					FunctionName: "CfcEdgeTrigger",
					Version:      "1",
					Runtime:      "nodejs6.11",
					MemorySize:   convert.Int(128),
				},
			},
			resCode: 400, // environment
		},
		{
			Function: &dao.Function{
				Id:             uint(123),
				Uid:            "c7ac82ae14ef42d1a4ffa3b2ececa17f",
				FunctionBrn:    "brn:bce:cfc:bj:c7ac82ae14ef42d1a4ffa3b2ececa17f:function:CfcEdgeTrigger:1",
				Region:         "bj",
				Timeout:        convert.Int(3),
				EnvironmentStr: `{"Variables":{}}`,
				FunctionConfig: api.FunctionConfig{
					CodeSize:     int32(123),
					FunctionName: "CfcEdgeTrigger",
					Version:      "1",
					Runtime:      "nodejs6.11",
					MemorySize:   convert.Int(128),
				},
			},
			resCode: 200,
		},
	}
	for _, c := range tc {
		var m sqlmock.Sqlmock
		m, dbengine.Engine = MockDB()
		funcSlice := make([]dao.Function, 0)
		if !c.withoutFunc {
			funcSlice = append(funcSlice, *c.Function)
		}
		m.ExpectQuery(".*").WillReturnRows(GetRowsFunctions(funcSlice))
		triggerData := map[string]interface{}{
			"Target": c.Function.FunctionBrn,
			"Source": "cfc-edge/www.a.com",
			"Data": map[string]interface{}{
				"Domain":      "www.a.com",
				"Path":        "/images",
				"EventType":   "viewer-request",
				"FunctionBrn": c.Function.FunctionBrn,
			},
		}
		if c.noEdgeTrigger {
			triggerData["Source"] = "cdn/www.a.com"
		}
		bodyBytes, _ := json.Marshal(triggerData)
		if c.dataMissing {
			bodyBytes = bodyBytes[:len(bodyBytes)-2]
		}
		request := httptest.NewRequest("POST", "/v1/relation", bytes.NewBuffer(bodyBytes))
		request.Header = map[string][]string{
			"Content-Type": {"application/json"},
		}
		request.Header.Set(api.HeaderXRequestID, uuid.New().String())
		restReq := restful.NewRequest(request)
		domain := iam.Domain{
			ID:   "c7ac82ae14ef42d1a4ffa3b2ececa17f",
			Name: "PASSPORT:**********",
		}
		iamuser := iam.User{
			ID:     "c7ac82ae14ef42d1a4ffa3b2ececa17f",
			Name:   "PASSPORT:**********",
			Domain: &domain,
		}
		restReq.SetAttribute("User", &iamuser)
		response := httptest.NewRecorder()
		restRsp := restful.NewResponse(response)
		chain := &restful.FilterChain{
			Filters: []restful.FilterFunction{
				CheckTriggerCondition,
			},
			Target: func(request *restful.Request, response *restful.Response) {
				response.WriteHeader(http.StatusOK)
			},
		}
		chain.ProcessFilter(restReq, restRsp)
		if response.Result().StatusCode != c.resCode {
			defer response.Result().Body.Close()
			body, _ := ioutil.ReadAll(response.Result().Body)
			t.Errorf("expect code %d, but got %d, body is %s", c.resCode, response.Result().StatusCode, string(body))
		}

	}
}

func GetRowsFunctions(as []dao.Function) *sqlmock.Rows {
	var fieldNames = []string{"id", "commit_id", "function_name", "uid", "code_sha256", "code_size", "description", "region", "memory_size", "function_brn", "handler", "runtime", "timeout", "version", "version_desc", "environment", "concurrency", "updated_at", "created_at", "deleted_at", "log_type", "code_id"}
	rows := sqlmock.NewRows(fieldNames)
	for _, a := range as {
		rows = rows.AddRow(a.Id, a.CommitID, a.FunctionName, a.Uid, a.CodeSha256, a.CodeSize, a.Description, a.Region, a.MemorySize, a.FunctionBrn, a.Handler, a.Runtime, a.Timeout, a.Version, a.VersionDesc, a.EnvironmentStr, a.ReservedConcurrentExecutions, a.UpdatedAt, a.CreatedAt, a.DeletedAt, a.LogType, a.CodeID)
	}
	return rows
}
