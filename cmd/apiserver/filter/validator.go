package filter

import (
	"errors"
	"strconv"
	"strings"

	"github.com/asaskevich/govalidator"
	"github.com/emicklei/go-restful"

	"icode.baidu.com/baidu/faas/kun/pkg/api"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/global"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/models"
	resp "icode.baidu.com/baidu/faas/kun/pkg/apiserver/models/function"
	kunErr "icode.baidu.com/baidu/faas/kun/pkg/error"
	"icode.baidu.com/baidu/faas/kun/pkg/server"
	"icode.baidu.com/baidu/faas/kun/pkg/util/convert"
)

func ValidatorQueryParameter(c *server.Context, chain *restful.FilterChain) {
	r := c.Request()
	var apiVersion string
	paths := strings.Split(r.SelectedRoutePath(), "/")
	if len(paths) > 1 {
		apiVersion = paths[1]
	}

	// lambda api
	markerStr := r.QueryParameter("Marker")
	maxItemStr := r.QueryParameter("MaxItems")
	version := r.QueryParameter("FunctionVersion")
	// bce api 支持分页
	pageNoStr := r.QueryParameter("page")
	pageSizeStr := r.QueryParameter("pageSize")

	// 兼容v2参数
	if apiVersion == "v2" {
		pageNoStr = r.QueryParameter("pageNo")
		pageSizeStr = r.QueryParameter("pageSize")
	}

	var err error
	// check IsNumeric
	if (govalidator.IsNumeric(markerStr) && govalidator.IsNumeric(maxItemStr) && govalidator.IsNumeric(pageNoStr) && govalidator.IsNumeric(pageSizeStr)) != true {
		err = errors.New("markerStr|maxItemStr|pageNoStr|pageSizeStr ParameterValueException")
		logErr(c, err, apiVersion)
		return
	}

	// check MaxItems 1-10000
	if maxItemStr != "" {
		maxItem, _ := strconv.ParseInt(maxItemStr, 10, 64)
		if err = models.CheckPtrIntSize(convert.Int(int(maxItem)), 1, 10000); err != nil {
			logErr(c, err, apiVersion)
			return
		}
	}

	// check Version
	if version != "" && version != "ALL" && !api.RegVersion.MatchString(version) {
		err = errors.New("version ParameterValueException")
		logErr(c, err, apiVersion)
		return
	}

	// V2 console
	// bce api 支持单字段排序、functionName模糊检索
	orderBy := r.QueryParameter("orderBy")
	sort := r.QueryParameter("order")
	// bce api 支持rumtime过滤
	runtime := r.QueryParameter("runtime")

	if !govalidator.IsAlphanumeric(orderBy) || !govalidator.IsIn(sort, []string{"", "asc", "desc", "ASC", "DESC"}...) {
		err = errors.New("orderBy|sort|searchFN ParameterValueException")
		logErr(c, err, apiVersion)
		return
	}

	if runtime != "" {
		if !strings.HasPrefix(runtime, "$") {
			if _, ok := global.AC.Cache.RuntimeCache[runtime]; !ok {
				err = errors.New("runtime ParameterValueException")
				logErr(c, err, apiVersion)
				return
			}
		}
	}

	chain.ProcessFilter(c.Request(), c.Response())
}

// 小程序云参数验证
func ValidatorCbdQueryParameter(c *server.Context, chain *restful.FilterChain) {
	r := c.Request()

	// lambda api
	markerStr := r.QueryParameter("Marker")
	maxItemStr := r.QueryParameter("MaxItems")
	// version := r.QueryParameter("FunctionVersion")
	// bce api 支持分页
	pageNoStr := r.QueryParameter("pageNo")
	pageSizeStr := r.QueryParameter("pageSize")

	var err error
	// check IsNumeric
	if (govalidator.IsNumeric(markerStr) && govalidator.IsNumeric(maxItemStr) && govalidator.IsNumeric(pageNoStr) && govalidator.IsNumeric(pageSizeStr)) != true {
		err = errors.New("markerStr|maxItemStr|pageNoStr|pageSizeStr ParameterValueException")
		logErr(c, err, "v2")
		return
	}

	// check MaxItems 1-10000
	if maxItemStr != "" {
		maxItem, _ := strconv.ParseInt(maxItemStr, 10, 64)
		if err = models.CheckPtrIntSize(convert.Int(int(maxItem)), 1, 10000); err != nil {
			logErr(c, err, "v2")
			return
		}
	}

	// bce api 支持rumtime过滤
	runtime := r.QueryParameter("runtime")

	if runtime != "" {
		if _, ok := global.AC.Cache.RuntimeCache[runtime]; !ok {
			err = errors.New("runtime ParameterValueException")
			logErr(c, err, "v2")
			return
		}
	}

	// 兼容小程序云
	// 支持多字段排序
	order := r.QueryParameter("Order")
	orderMap := api.ParseCBDOrder(order)
	for orderBy, sort := range orderMap {
		if !govalidator.IsAlphanumeric(orderBy) || !govalidator.IsIn(sort, []string{"", "asc", "desc", "ASC", "DESC"}...) {
			err = errors.New("orderBy|sort|searchFN ParameterValueException")
			logErr(c, err, "v2")
			return
		}
	}

	chain.ProcessFilter(c.Request(), c.Response())
}

// 错误信息返回
func logErr(c *server.Context, err error, apiVersion string) {
	if err != nil {
		// 兼容v2错误返回
		if apiVersion == "v2" {
			respErr := c.WithWarnLog(kunErr.
				NewInvalidParameterValueException("ParameterValueException", err))
			resp.NewFailResp(respErr).WriteTo(c.Response(), respErr)
			return
		}
		c.WithWarnLog(kunErr.
			NewInvalidParameterValueException("ParameterValueException", err)).WriteTo(c.Response())
		return
	}
}
