package filter

import (
	"bytes"
	"io/ioutil"
	"net/http/httptest"
	"testing"

	"github.com/emicklei/go-restful"
	"github.com/stretchr/testify/assert"

	"icode.baidu.com/baidu/faas/kun/pkg/server"
)

func TestEmptyBodyPayloadToJson(t *testing.T) {
	request := httptest.NewRequest("POST", "http://faas.baidu.com", bytes.NewBuffer([]byte("{}")))
	httpWriter := httptest.NewRecorder()
	restReq := restful.NewRequest(request)
	restRes := restful.NewResponse(httpWriter)
	kunCtx := server.BuildContext(restReq, restRes, "")
	EmptyBodyPayloadToJson(kunCtx, &restful.FilterChain{
		Target: restful.RouteFunction(func(*restful.Request, *restful.Response) {
			//Do
		}),
	})

	request2 := httptest.NewRequest("POST", "http://faas.baidu.com", bytes.NewBuffer([]byte("")))
	restReq2 := restful.NewRequest(request2)
	kunCtx2 := server.BuildContext(restReq2, restRes, "")
	EmptyBodyPayloadToJson(kunCtx2, &restful.FilterChain{
		Target: restful.RouteFunction(func(r *restful.Request, w *restful.Response) {
			byteBody, _ := ioutil.ReadAll(r.Request.Body)
			assert.Equal(t, "{}", string(byteBody))
			defer r.Request.Body.Close()
		}),
	})
}
