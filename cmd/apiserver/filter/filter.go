package filter

import (
	"bytes"
	"errors"
	"io/ioutil"
	"net/http"
	"net/url"
	"regexp"
	"strings"
	"time"

	"github.com/emicklei/go-restful"
	"github.com/google/uuid"
	"go.uber.org/zap"

	"icode.baidu.com/baidu/faas/kun/pkg/api"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/dao"
	apiErr "icode.baidu.com/baidu/faas/kun/pkg/apiserver/error"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/global"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/order"
	"icode.baidu.com/baidu/faas/kun/pkg/bce/brn"
	"icode.baidu.com/baidu/faas/kun/pkg/bce/iam"
	"icode.baidu.com/baidu/faas/kun/pkg/bce/opscenter"
	consoleErr "icode.baidu.com/baidu/faas/kun/pkg/console/error"
	kunErr "icode.baidu.com/baidu/faas/kun/pkg/error"
	"icode.baidu.com/baidu/faas/kun/pkg/util/bytefmt"
	"icode.baidu.com/baidu/faas/kun/pkg/util/json"
	"icode.baidu.com/baidu/faas/kun/pkg/util/logs"
)

const (
	IamPermRead                 = "READ"
	IamPermWrite                = "WRITE"
	IamPermList                 = "LIST"
	realNameQualificationStatus = "PASS"
)

var (
	edgeRuntime = regexp.MustCompile(`^(lua|nodejs|python)([0-9\.]*)$`)
)

// IAMPermissionCheck IAM权限校验
func IAMPermissionCheck(resource, owner string, permissions []string) restful.FilterFunction {
	return func(req *restful.Request, resp *restful.Response, chain *restful.FilterChain) {
		startTime := time.Now()

		authReq := &iam.AuthenticationRequest{}
		r := req.Request
		query := r.URL.Query()
		auth := query.Get("authorization")
		if auth == "" {
			auth = r.Header.Get("Authorization")
		}
		query.Del("authorization")
		authReq.Request.WithMethod(r.Method).
			WithUri(r.URL.Path).WithParams(query).WithHeader(r.Header).WithHost(r.Host)
		authReq.Authorization = auth
		authReq.SecurityToken = r.Header.Get(api.HeaderXBceSecurityToken)
		verifyReq := &iam.SinglePermissionVerifyRequest{}
		verifyReq.WithAuth(authReq).WithService(global.AC.Config.ServiceName).
			WithResource(resource).WithRegion(global.AC.Config.Region)
		verifyReq.RequestId = r.Header.Get(api.HeaderXRequestID)
		for _, p := range permissions {
			verifyReq.AddPermission(p)
		}
		token, verifyRes, err := global.AC.Clients.Iam.PermissionVerify("", verifyReq)
		if err != nil {
			consoleErr.NewUnrecognizedClientException("Fail to parse user authorization", err).WithWarnLog().WriteTo(resp)
			return
		}
		if verifyRes.Effect == iam.PermissionAllow {
			req.SetAttribute("User", &token.User)
		} else {
			consoleErr.NewUnrecognizedClientException("No permission", err).WithWarnLog().WriteTo(resp)
			return
		}
		logs.TimeTrack(startTime, "IAM Permission Check")

		chain.ProcessFilter(req, resp)
	}
}

// IAMAuthenticateCheck check iam auth
func IAMAuthenticateCheck(req *restful.Request, resp *restful.Response, chain *restful.FilterChain) {
	startTime := time.Now()
	authReq := &iam.AuthenticationRequest{}
	r := req.Request
	query := r.URL.Query()
	auth := query.Get("authorization")
	if auth == "" {
		auth = r.Header.Get("Authorization")
	}
	query.Del("authorization")
	authReq.Request.WithMethod(r.Method).
		WithUri(r.URL.Path).WithParams(query).WithHeader(r.Header).WithHost(r.Host)
	authReq.Authorization = auth
	authReq.SecurityToken = r.Header.Get(api.HeaderXBceSecurityToken)
	authReq.RequestId = r.Header.Get(api.HeaderXRequestID)
	token, err := global.AC.Clients.Iam.AuthenticateWithAKSK(authReq)
	if err != nil {
		consoleErr.NewUnrecognizedClientException("Fail to parse user authorization", err).WithWarnLog().WriteTo(resp)
		return
	}
	req.SetAttribute("User", &token.User)
	logs.TimeTrack(startTime, "IAM Authenticate Check")

	chain.ProcessFilter(req, resp)
}

// CheckCFCWhiteList check user in cfc whitelist
func CheckCFCWhiteList() restful.FilterFunction {
	return restful.FilterFunction(func(req *restful.Request, resp *restful.Response, chain *restful.FilterChain) {
		checkCFCWhiteList(req, resp, chain)
	})
}

func checkCFCWhiteList(req *restful.Request, resp *restful.Response, chain *restful.FilterChain) {
	startTime := time.Now()
	if global.AC.Config.EnableCheckWhiteList == false {
		chain.ProcessFilter(req, resp)
		return
	}

	attr := req.Attribute("User")
	user, _ := attr.(*iam.User)
	accountid := user.Domain.ID
	cli, err := opscenter.NewOpsCenter(global.AC.Clients.Iam, global.AC.Config.WhiteListEndpoint)
	if err != nil {
		consoleErr.NewUnrecognizedClientException("create setting client failed.", err).WithWarnLog().WriteTo(resp)
		return
	}
	svcs, err := cli.GetUserNavigationList(accountid)
	if err != nil {
		consoleErr.NewUnrecognizedClientException("get user navigation failed.", err).WithWarnLog().WriteTo(resp)
		return
	}
	logs.TimeTrack(startTime, "Check CFC WhiteList")

	for _, s := range svcs {
		if s == "CFC" {
			if nil != chain { // support test
				chain.ProcessFilter(req, resp)
			}
			return
		}
	}
	consoleErr.NewUnrecognizedClientException("not in whitelist", err).WithWarnLog().WriteTo(resp)
}

// RealNameCheck 检查是否完成实名认证
func RealNameCheck(req *restful.Request, resp *restful.Response, chain *restful.FilterChain) {
	request := req.Request
	requestId := request.Header.Get(api.HeaderXRequestID)
	attr := req.Attribute("User")
	user, _ := attr.(*iam.User)
	accountId := user.Domain.ID
	realNameStatus, err := global.QueryRealName(accountId, requestId)
	if realNameStatus == realNameQualificationStatus {
		//实名认证通过
		chain.ProcessFilter(req, resp)
		return
	}
	//否则实名认证未通过，拦截
	apiErr.NewRealNameQualificationException("query real name qualification error", err).WithWarnLog().WriteTo(resp)
	return
}

// BillingCheck 检查是否已经有可用账单 开通并且为RUNNING状态
func BillingCheck(req *restful.Request, resp *restful.Response, chain *restful.FilterChain) {
	startTime := time.Now()
	request := req.Request
	requestId := request.Header.Get(api.HeaderXRequestID)
	r := req.Attribute("User")
	if r == nil {
		if global.AC.Config.EnableCheckBilling == false {
			chain.ProcessFilter(req, resp)
			return
		}
		consoleErr.NewUnrecognizedClientException("No permission", errors.New("User not found")).WithWarnLog().WriteTo(resp)
		return
	}

	user := r.(*iam.User)
	findResource := new(dao.BillingResource)
	findResource.AccountId = user.Domain.ID
	if rkunErr := dao.FindOneRes(findResource); rkunErr != nil {
		if global.AC.Config.EnableCheckBilling == false {
			logs.Error("BillingCheck: find resource from db failed", zap.String("error", rkunErr.Error()), zap.String("request_id", requestId))
			chain.ProcessFilter(req, resp)
			return
		}
		kunErr.NewServiceException("No permission: Find your billing resource err, Please try again later", nil).WithWarnLog().WriteTo(resp)
		return
	}

	if findResource.ResourceState != "RUNNING" {
		if findResource.ResourceState == "" {
			if requestId == "" {
				requestId = uuid.New().String()
			}
			err := order.CreateOrder(findResource.AccountId, requestId, global.AC.Config.BillingEndpoint, global.AC.Config.BillingCatalogEndpoint, global.AC.Clients.Iam, global.AC.Config.Region)
			if err != nil {
				if global.AC.Config.EnableCheckBilling == false {
					logs.Error("BillingCheck: create order failed", zap.String("error", err.Error()), zap.String("request_id", requestId))
					chain.ProcessFilter(req, resp)
					return
				}
				kunErr.NewUnrecognizedClientException("No permission: Billing created failed, please check whether you have overdue bills!", err).WithWarnLog().WriteTo(resp)
				return
			}
		} else {
			if global.AC.Config.EnableCheckBilling == false {
				logs.Error("BillingCheck: the user does not have a RUNNING resource.", zap.String("uid", findResource.AccountId), zap.String("request_id", requestId))
				chain.ProcessFilter(req, resp)
				return
			}
			consoleErr.NewUnrecognizedClientException("No permission: Don't hava a available resource, please check whether you have overdue bills!", nil).WithWarnLog().WriteTo(resp)
			return
		}
	}
	logs.TimeTrack(startTime, "Billing Check")
	chain.ProcessFilter(req, resp)
}

func CheckUserBilling(req *restful.Request, resp *restful.Response, chain *restful.FilterChain) {
	r := req.Attribute("User")
	if r == nil {
		consoleErr.NewUnrecognizedClientException("No permission", errors.New("User not found")).WithWarnLog().WriteTo(resp)
		return
	}
	user := r.(*iam.User)
	if user.Name != "billing" {
		consoleErr.NewUnrecognizedClientException("No permission", nil).WithWarnLog().WriteTo(resp)
		return
	}
	chain.ProcessFilter(req, resp)
}

func CheckUserOrderExecutor(req *restful.Request, resp *restful.Response, chain *restful.FilterChain) {
	r := req.Attribute("User")
	if r == nil {
		consoleErr.NewUnrecognizedClientException("No permission", errors.New("User not found")).WithWarnLog().WriteTo(resp)
		return
	}
	user := r.(*iam.User)
	if user.Name != "order_execute" {
		consoleErr.NewUnrecognizedClientException("No permission", nil).WithWarnLog().WriteTo(resp)
		return
	}
	chain.ProcessFilter(req, resp)
}

func CheckFunctionExist(req *restful.Request, resp *restful.Response, chain *restful.FilterChain) {
	query := req.Request.URL.Query()
	q1 := query.Get("FunctionBrn")
	q2 := query.Get("Target")

	var funcBrn string
	var err error

	if q1 != "" {
		funcBrn = q1
	} else if q2 != "" {
		funcBrn = q2
	} else {
		relationReq := &api.Relation{}
		bodyBytes, _ := ioutil.ReadAll(req.Request.Body)
		req.Request.Body.Close()
		req.Request.Body = ioutil.NopCloser(bytes.NewBuffer(bodyBytes))
		if err := json.Unmarshal(bodyBytes, relationReq); err != nil {
			kunErr.NewInvalidRequestContentException("", err).WriteTo(resp)
			return
		}
		req.SetAttribute("relationReq", relationReq)
		funcBrn = relationReq.Target
	}

	if funcBrn == "" {
		kunErr.NewInvalidParameterValueException("function brn is empty", nil).WriteTo(resp)
		return
	}

	attr := req.Attribute("User")
	user, _ := attr.(*iam.User)
	uid := user.Domain.ID

	parseRes, _ := brn.Parse(funcBrn)
	resource := strings.Split(parseRes.Resource, ":")
	if len(resource) != 3 {
		kunErr.NewInvalidParameterValueException("invalid function brn", nil).WriteTo(resp)
		return
	}

	if !api.RegVersion.MatchString(resource[2]) {
		err = dao.FindOneAlias(&dao.Alias{AliasBrn: funcBrn, Uid: uid})
	} else {
		err = dao.FindOneFunc(&dao.Function{FunctionBrn: funcBrn, Uid: uid})
	}

	if err != nil {
		apiErr.NewObjectNotFoundException("function doesn't exist", err).WriteTo(resp)
		return
	}

	chain.ProcessFilter(req, resp)
}

func CheckTriggerCondition(req *restful.Request, resp *restful.Response, chain *restful.FilterChain) {
	rlat := &api.Relation{}
	requestId := req.Request.Header.Get(api.HeaderXRequestID)
	logger := logs.NewLogger().WithField("request_id", requestId)
	bodyBytes, _ := ioutil.ReadAll(req.Request.Body)
	req.Request.Body.Close()
	req.Request.Body = ioutil.NopCloser(bytes.NewBuffer(bodyBytes))
	if err := json.Unmarshal(bodyBytes, rlat); err != nil {
		logger.Error("invalid trigger config ", zap.String("error", err.Error()))
		kunErr.NewInvalidRequestContentException("invalid trigger config ", err).WriteTo(resp)
		return
	}
	sourceSlice := strings.SplitN(rlat.Source, "/", 2)
	if sourceSlice[0] != api.TriggerTypeCfcEdge {
		chain.ProcessFilter(req, resp)
		return
	}

	funcBrn := rlat.Target
	if funcBrn == "" {
		logger.Error("invalid function brn")
		kunErr.NewInvalidParameterValueException("invalid function brn ", nil).WriteTo(resp)
		return
	}
	findFunc := &dao.Function{
		FunctionBrn: funcBrn,
	}
	if err := dao.FindOneFunc(findFunc); err != nil {
		logger.Error("function doesn't exist")
		apiErr.NewObjectNotFoundException("function doesn't exist", err).WriteTo(resp)
		return
	}
	findFunc.DealResFunction()

	req.SetAttribute("functionInfo", findFunc)

	// 以下为cfc@edge触发器关于函数的校验规则
	// code_size <= 1MB
	maxCodeSize, _ := bytefmt.ToBytes("1MB")
	if uint64(findFunc.CodeSize) > maxCodeSize {
		logger.Error("function's code size can't larger than 1MB ")
		kunErr.NewInvalidParameterValueException("function's code size can't larger than 1MB ", nil).WriteTo(resp)
		return
	}
	// memory_size = 128MB
	if *findFunc.MemorySize != 128 {
		logger.Error("function's memory size must be 128MB ")
		kunErr.NewInvalidParameterValueException("function's memory size must be 128MB ", nil).WriteTo(resp)
		return
	}
	// region = bj
	if findFunc.Region != "bj" {
		logger.Error("cfc@Edge only support bj region ")
		kunErr.NewInvalidParameterValueException("cfc@Edge only support bj region ", nil).WriteTo(resp)
		return
	}
	// version != $LATEST
	if !regexp.MustCompile("^([0-9]+)$").MatchString(findFunc.Version) {
		logger.Error("cfc@Edge can't bind to $LATEST function version ")
		kunErr.NewInvalidParameterValueException("cfc@Edge can't bind to $LATEST function version ", nil).WriteTo(resp)
		return
	}
	// timeout <= 5s
	if *findFunc.Timeout > 5 {
		logger.Error("function's timeout is longer than 5 seconds ")
		kunErr.NewInvalidParameterValueException("function's timeout is longer than 5 seconds ", nil).WriteTo(resp)
		return
	}
	// runtime in (nodejs,python)
	if !edgeRuntime.MatchString(findFunc.Runtime) {
		logger.Error("cfc@edge is only support Node.js or Python runtime ")
		kunErr.NewInvalidParameterValueException("cfc@edge is only support Node.js or Python runtime ", nil).WriteTo(resp)
		return
	}

	// environment = ""
	hasEnvironment := true
	if findFunc.Environment == nil || findFunc.Environment.Variables == nil || len(findFunc.Environment.Variables) == 0 {
		hasEnvironment = false
	}

	if hasEnvironment {
		logger.Error("cfc@edge can't support Environment ")
		kunErr.NewInvalidParameterValueException("cfc@edge can't support Environment ", nil).WriteTo(resp)
		return
	}
	chain.ProcessFilter(req, resp)
}

//请求用户实名认证接口
func queryRealNameQualification(accountId string, requestId string) (*api.RealNameQualification, error) {
	body := new(bytes.Buffer)
	url, _ := getUrlPathWithParams(global.AC.Config.RealNameQualificationEndpoint+"/qualify/v2/qualification/realname", map[string]string{"accountId": accountId})
	req, err := http.NewRequest(http.MethodGet, url, body)
	if err != nil {
		return nil, err
	}
	rsp, err := order.HttpRequestForService(req, requestId, global.AC.Clients.Iam)
	if err != nil {
		return nil, err
	}
	realNameQualification := &api.RealNameQualification{}
	json.NewDecoder(rsp.Body).Decode(realNameQualification)
	return realNameQualification, nil
}

//url格式化
func getUrlPathWithParams(urlPath string, paramMap map[string]string) (urlPathNew string, err error) {
	params := url.Values{}
	var parseURL *url.URL
	parseURL, err = url.Parse(urlPath)
	if err != nil {
		return "", err
	}
	if paramMap != nil {
		for k, v := range paramMap {
			params.Set(k, v)
		}
	}
	parseURL.RawQuery = params.Encode()
	urlPathNew = parseURL.String()
	return urlPathNew, nil
}
