package options

import (
	"github.com/spf13/pflag"

	"icode.baidu.com/baidu/faas/kun/pkg/bce/iam"
	"icode.baidu.com/baidu/faas/kun/pkg/etcd"
	eventhub "icode.baidu.com/baidu/faas/kun/pkg/eventhub/client"
	"icode.baidu.com/baidu/faas/kun/pkg/kafka"
	"icode.baidu.com/baidu/faas/kun/pkg/rpc/db"
	kunRedis "icode.baidu.com/baidu/faas/kun/pkg/rpc/redis"
	genericoptions "icode.baidu.com/baidu/faas/kun/pkg/server/options"
)

const (
	// ApplicationName 指定应用名称
	ApplicationName = "crontab"
)

// CrontabOptions describe options that api gateway needs
type CrontabOptions struct {
	RecommendedOptions  *genericoptions.RecommendedOptions
	IAMOptions          *iam.IAMOptions
	GenericRedisOptions *kunRedis.RedisOptions
	EventHubOptions     *eventhub.EventHubOptions
	DbConfig            *db.DbConfig
	KafkaOptions        *kafka.Options
	EtcdOptions         *etcd.Options
	EventTopic          string
	ReservedConcurrencyNum int
}

// NewCrontabOptions creates a new CrontabOptions object with default parameters
func NewCrontabOptions() *CrontabOptions {
	s := CrontabOptions{
		RecommendedOptions:  genericoptions.NewRecommendedOptions(),
		IAMOptions:          iam.NewIAMOptions(),
		GenericRedisOptions: kunRedis.NewRedisOptions(),
		EventHubOptions: &eventhub.EventHubOptions{
			Host: "127.0.0.1",
			Port: 8100,
		},
		DbConfig:     db.NewDbConfig(),
		EtcdOptions:  genericoptions.NewEtcdOptions(),
		KafkaOptions: kafka.NewKafkaOptions(),
		ReservedConcurrencyNum: 10,
	}
	//s.RecommendedOptions.TelemetryOptions.ServiceName = ApplicationName
	return &s
}

// AddFlags adds flags for a specific APIServer to the specified FlagSet
func (s *CrontabOptions) AddFlags(fs *pflag.FlagSet) {
	s.IAMOptions.AddUniversalFlags(fs)
	s.RecommendedOptions.AddFlags(fs)
	s.GenericRedisOptions.AddRedisFlags(fs)
	s.EventHubOptions.AddEventHubFlags(fs)
	s.DbConfig.AddFlags(fs)
	s.KafkaOptions.AddUniversalFlags(fs)
	genericoptions.AddEtcdFlags(s.EtcdOptions, fs)
	fs.StringVar(&s.EventTopic, "event-topic", s.EventTopic, "invoke event kafka topic")
	fs.IntVar(&s.ReservedConcurrencyNum, "reserved-concurrency", s.ReservedConcurrencyNum, "reserved concurrency")
}
