package app

import (
	"icode.baidu.com/baidu/faas/kun/cmd/crontab/app/options"
	"icode.baidu.com/baidu/faas/kun/pkg/crontab"
	"icode.baidu.com/baidu/faas/kun/pkg/server"
	genericserver "icode.baidu.com/baidu/faas/kun/pkg/server"
	"icode.baidu.com/baidu/faas/kun/pkg/util/logs"
	"icode.baidu.com/baidu/faas/kun/pkg/version"
)

// Run runs the specified ApiGatewayServer with the given Dependencies.
// This should never exit.
func Run(runOptions *options.CrontabOptions, stopCh <-chan struct{}) error {
	logs.Infof("Version: %+v", version.Get())

	s, err := CreateServerChain(runOptions, stopCh)
	if err != nil {
		return err
	}

	return s.PrepareRun().Run(stopCh)
}

// CreateServerChain creates the http-trigger connected via delegation.
func CreateServerChain(runOptions *options.CrontabOptions, stopCh <-chan struct{}) (*server.GenericServer, error) {
	err := crontab.Init(runOptions, stopCh)
	if err != nil {
		return nil, err
	}
	config := genericserver.NewRecommendedConfig()
	if err = runOptions.RecommendedOptions.ApplyTo(config); err != nil {
		return nil, err
	}
	s, err := config.Complete().New(options.ApplicationName)
	if err != nil {
		return nil, err
	}
	// Install hook
	s.AddPostStartHook("demoPostStartHook", crontab.PostStartHook)
	s.AddPreShutdownHook("demoPreShutdownHook", crontab.PreShutdownHook)

	// Install API
	crontab.InstallAPI(s.Handler.GoRestfulContainer)

	return s, nil
}
