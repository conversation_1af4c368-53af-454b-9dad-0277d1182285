package main

/**
* cli tool for Prometheus metric To Argus 简易版
 */

import (
	"fmt"
	"io"
	"os"

	"github.com/spf13/cobra"

	etcdmetric "icode.baidu.com/baidu/faas/kun/cmd/prometheus2argus/etcd-metric"
)

func main() {
	cmd := newRootCommand(os.Stdin, os.Stdout, os.Stderr)
	if err := cmd.Execute(); err != nil {
		fmt.Println(err)
		os.Exit(1)
	}
}

func newRootCommand(in io.Reader, out, err io.Writer) *cobra.Command {
	cmds := &cobra.Command{
		Use:   "prometheus2argus [OPTIONS] COMMAND [ARG...]",
		Short: "prometheus指标转换成argus格式",
		Long:  "prometheus指标转换成argus格式",
		Args:  cobra.NoArgs,
		RunE: func(cmd *cobra.Command, args []string) error {
			return cmd.Help()
		},
	}
	cmds.AddCommand(etcdmetric.NewETCDCmd(out))
	return cmds
}
