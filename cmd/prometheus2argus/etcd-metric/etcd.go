package etcdmetric

import (
	"bufio"
	"fmt"
	"io"
	"net/http"
	"os"
	"os/exec"
	"path/filepath"
	"strings"
	"time"

	"github.com/coreos/etcd/pkg/transport"
	"github.com/spf13/cobra"
)

type etcdMetricOptions struct {
	url     string
	cacert  string
	cert    string
	key     string
	metrics []string //输出的指标项
	tlsAuth bool
}

//
func NewETCDCmd(out io.Writer) *cobra.Command {
	o := new(etcdMetricOptions)

	cmd := &cobra.Command{
		Use:   "etcd",
		Short: "etcd metrics",
		Long:  "etcd metrics",
		Run: func(cmd *cobra.Command, args []string) {
			o.Run(cmd, out)
		},
	}

	binFile, _ := exec.LookPath(os.Args[0])
	binPath, _ := filepath.Abs(binFile + "/../")

	cmd.Flags().StringVar(&o.url, "url", "https://127.0.0.1:2379/metrics", "etcd endpoint")
	cmd.Flags().StringVar(&o.cacert, "cacert", binPath+"/certs/ca.pem", "ca.pem path")
	cmd.Flags().StringVar(&o.cert, "cert", binPath+"/certs/etcd.pem", "client cert path")
	cmd.Flags().StringVar(&o.key, "key", binPath+"/certs/etcd-key.pem", "Private key file  path")
	cmd.Flags().BoolVar(&o.tlsAuth, "tls-auth", true, "")
	cmd.Flags().StringSliceVar(&o.metrics, "metrics", []string{"etcd_debugging_mvcc_db_total_size_in_bytes", "go_goroutines"}, "etcd endpoint")
	return cmd
}
func (o *etcdMetricOptions) Run(cmd *cobra.Command, out io.Writer) {

	client := http.Client{
		Timeout: time.Duration(3 * time.Second),
	}

	if o.tlsAuth {
		tlsInfo := transport.TLSInfo{
			CertFile:           o.cert,
			KeyFile:            o.key,
			TrustedCAFile:      o.cacert,
			InsecureSkipVerify: true,
		}
		tlsConfig, err := tlsInfo.ClientConfig()
		if err != nil {
			fmt.Fprintf(out, "tlsConfig client err: %s\n", err.Error())
			return
		}
		client.Transport = &http.Transport{
			MaxIdleConnsPerHost: 10,
			TLSClientConfig:     tlsConfig,
		}
	}

	req, _ := http.NewRequest("GET", o.url, nil)
	resp, err := client.Do(req)

	if err != nil {
		fmt.Fprintf(out, "http get err: %s\n", err.Error())
		return
	}
	defer resp.Body.Close()

	// body, err := ioutil.ReadAll(resp.Body)
	// if err != nil {
	// 	fmt.Fprintf(out, "io read err: %s\n", err.Error())
	// 	return
	// }

	scanner := bufio.NewScanner(resp.Body)
	for i := 1; scanner.Scan(); i++ {
		text := scanner.Text()
		for _, metric := range o.metrics {
			if strings.Contains(text, metric) && !strings.HasPrefix(text, "#") {
				textArr := strings.Split(text, " ")
				if len(textArr) != 2 {
					break
				}
				var new float64
				fmt.Sscanf(textArr[1], "%e", &new)
				fmt.Fprintf(out, "%s:%d\n", textArr[0], uint64(new))
			}
		}
	}
}
