package options

import (
	"github.com/spf13/pflag"

	cronclient "icode.baidu.com/baidu/faas/kun/pkg/cron/client"
	"icode.baidu.com/baidu/faas/kun/pkg/etcd"
	genericoptions "icode.baidu.com/baidu/faas/kun/pkg/server/options"
	caasstub "icode.baidu.com/baidu/faas/kun/pkg/stubs/stubapi/caas-stub"
	funcletstub "icode.baidu.com/baidu/faas/kun/pkg/stubs/stubapi/funclet"
)

// StubsOptions descript options that stubs needs
type StubsOptions struct {
	RecommendedOptions  *genericoptions.RecommendedOptions
	EnableStrictInvoker bool
	FuncletStubOptions  *funcletstub.Options
	CCEStubOptions      *caasstub.CCEOptions
	CronClientOptions   *cronclient.CronOptions
	EtcdOptions         *etcd.Options
	RunningMode         string
}

// NewStubsOptions creates a new StubsOptions object with default parameters
func NewStubsOptions() *StubsOptions {
	s := StubsOptions{
		RecommendedOptions:  genericoptions.NewRecommendedOptions(),
		EnableStrictInvoker: false,
		FuncletStubOptions:  funcletstub.NewOptions(),
		CCEStubOptions:      caasstub.NewCCEOptions(),
		CronClientOptions:   cronclient.NewCronOptions(),
		EtcdOptions:         genericoptions.NewEtcdOptions(),
		RunningMode:         "cloud",
	}
	return &s
}

// AddFlags adds flags for a specific APIServer to the specified FlagSet
func (s *StubsOptions) AddFlags(fs *pflag.FlagSet) {
	s.RecommendedOptions.AddFlags(fs)
	s.FuncletStubOptions.AddFlags(fs)
	s.CCEStubOptions.AddFlags(fs)
	s.CronClientOptions.AddFlags(fs)
	genericoptions.AddEtcdFlags(s.EtcdOptions, fs)
	fs.BoolVar(&s.EnableStrictInvoker, "strict-invoker", s.EnableStrictInvoker,
		"Enable strict invoker")
	fs.StringVar(&s.RunningMode, "running-mode", s.RunningMode, "cloud | ote | duedge")
}
