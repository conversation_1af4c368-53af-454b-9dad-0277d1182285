package app

import (
	"math/rand"
	"time"

	"icode.baidu.com/baidu/faas/kun/cmd/stubs/app/options"
	genericserver "icode.baidu.com/baidu/faas/kun/pkg/server"
	"icode.baidu.com/baidu/faas/kun/pkg/stubs"
	"icode.baidu.com/baidu/faas/kun/pkg/util/logs"
	"icode.baidu.com/baidu/faas/kun/pkg/version"
)

// Run runs the specified EventhubServer with the given Dependencies.
// This should never exit.
func Run(runOptions *options.StubsOptions, stopCh <-chan struct{}) error {
	logs.Infof("Version: %+v", version.Get())
	rand.Seed(time.Now().UnixNano())

	s, err := CreateServerChain(runOptions, stopCh)
	if err != nil {
		return err
	}

	return s.PrepareRun().Run(stopCh)
}

// CreateServerChain creates the apiservers connected via delegation.
func CreateServerChain(runOptions *options.StubsOptions, stopCh <-chan struct{}) (*genericserver.GenericServer, error) {

	config := genericserver.NewRecommendedConfig()
	if err := runOptions.RecommendedOptions.ApplyTo(config); err != nil {
		return nil, err
	}

	s, err := config.Complete().New("stub")
	if err != nil {
		return nil, err
	}

	stubs.Init(runOptions)

	// Install API
	stubs.InstallAPI(s.Handler.GoRestfulContainer, runOptions)

	return s, nil
}
