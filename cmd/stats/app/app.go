package app

import (
	"context"
	"fmt"

	"icode.baidu.com/baidu/faas/kun/cmd/stats/options"
	"icode.baidu.com/baidu/faas/kun/pkg/kafka"
	"icode.baidu.com/baidu/faas/kun/pkg/util/logs"
	"icode.baidu.com/baidu/faas/kun/pkg/version"
)

// Run runs the specified StatServer with the given Dependencies.
func Run(opt *options.StatsOptions, stopCh <-chan struct{}) error {
	logs.Infof("Version: %+v", version.Get())

	consumerGroup, err := kafka.NewSaramaConsumerGroup(opt.KafkaOptions, context.Background())
	if err != nil {
		return fmt.Errorf("Failed to create consumer: %s", err)
	}

	mgr := newManager(consumerGroup, opt, stopCh)

	consumerGroup.SetHandler(mgr)
	consumerGroup.StartConsumerGroup()

	mgr.Wait()
	return nil
}
