package app

import (
	"encoding/json"
	"errors"
	"fmt"
	"testing"
	"time"

	"github.com/IBM/sarama"
	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/assert"

	"icode.baidu.com/baidu/faas/kun/cmd/stats/options"
	"icode.baidu.com/baidu/faas/kun/pkg/api"
	"icode.baidu.com/baidu/faas/kun/pkg/bce/bcm"
	"icode.baidu.com/baidu/faas/kun/pkg/bce/bcm/mock"
	"icode.baidu.com/baidu/faas/kun/pkg/kafka"
	kmock "icode.baidu.com/baidu/faas/kun/pkg/kafka/mock"
)

func TestClearWorkspace(t *testing.T) {
	mockCtrl := gomock.NewController(t)
	defer mockCtrl.Finish()

	stopCh := make(chan struct{})

	mgr := newManager(&kmock.ConsumerGroupMock{}, options.NewStatsOptions(), stopCh)

	bcmClient := mock.NewMockInterface(mockCtrl)
	bcmClient.EXPECT().PushMultiUserMetricData(gomock.Any()).Return(nil).AnyTimes()
	mgr.bcmClient = bcmClient

	mgr.requestCh <- &CommitPack{
		Messages: make(map[int32]*sarama.ConsumerMessage, 0),
	}

	mgr.consumerStatus = kafka.ConsumerCleanup
	mgr.clearWorkspace()
	for len(mgr.requestCh) > 0 {
		<-time.After(1 * time.Millisecond)
	}
}

func TestGetUnixMins(t *testing.T) {
	opt := options.NewStatsOptions()
	mgr := newManager(nil, opt, nil)
	res := mgr.getUnixMins(1575277146)
	assert.Equal(t, uint64(26254), res)
}

func TestWorker(t *testing.T) {
	mockCtrl := gomock.NewController(t)
	defer mockCtrl.Finish()

	stopCh := make(chan struct{})

	req := &bcm.UserMetricData{
		Scope:  bcm.BCMScopeCFC,
		UserID: "userid",
	}

	bcmClient := mock.NewMockInterface(mockCtrl)
	bcmClient.EXPECT().PushMultiUserMetricData(req).Return(nil)
	bcmClient.EXPECT().PushMultiUserMetricData(req).Return(errors.New("err"))
	mgr := newManager(&kmock.ConsumerGroupMock{}, options.NewStatsOptions(), stopCh)
	mgr.bcmClient = bcmClient
	mgr.wg.Add(1)
	mgr.startWoker(stopCh)

	mgr.requestCh <- &CommitPack{
		BcmRequests: []*bcm.UserMetricData{req},
		Messages:    make(map[int32]*sarama.ConsumerMessage, 0),
	}

	mgr.requestCh <- &CommitPack{
		BcmRequests: []*bcm.UserMetricData{req},
		Messages:    make(map[int32]*sarama.ConsumerMessage, 0),
	}

	time.Sleep(10 * time.Millisecond)
	close(stopCh)

	assert.Equal(t, 0, len(mgr.requestCh))
	mgr.Wait()
}

func TestHandleNotifyAndError(t *testing.T) {
	mgr := newManager(&kmock.ConsumerGroupMock{}, options.NewStatsOptions(), nil)
	mgr.consumerGroup = &kmock.ConsumerGroupMock{}
	mgr.HandleError(errors.New("err"))
	mgr.HandleNotify(&kafka.Notification{})
}

func TestHandleMessage(t *testing.T) {
	stopCh := make(chan struct{}, 0)
	mgr := newManager(&kmock.ConsumerGroupMock{}, options.NewStatsOptions(), stopCh)

	close(stopCh)

	infos := []struct {
		in  *api.StatisticInfo
		out string
	}{
		{
			in: &api.StatisticInfo{
				UserID:     "uid",
				RequestID:  "123-456",
				Region:     "bj",
				Function:   "func1",
				Version:    "$LATEST",
				StartTime:  1575379088,
				Duration:   100,
				MemoryUsed: 128345,
				StatusCode: 200,
			},
			out: "uid",
		},
		{
			in: &api.StatisticInfo{
				UserID:     "uid",
				RequestID:  "123-456",
				Region:     "bj",
				Function:   "func1",
				Version:    "$LATEST",
				StartTime:  0,
				Duration:   0,
				MemoryUsed: 0,
				StatusCode: 403,
			},
			out: "uid",
		},
		{
			in: &api.StatisticInfo{
				UserID:     "uid",
				RequestID:  "123-456",
				Region:     "bj",
				Function:   "func1",
				Version:    "$LATEST",
				StartTime:  0,
				Duration:   0,
				MemoryUsed: 0,
				StatusCode: 502,
			},
			out: "uid",
		},
		{
			in: &api.StatisticInfo{
				UserID:     "uid",
				RequestID:  "123-456",
				Region:     "bj",
				Function:   "func2",
				Version:    "$LATEST",
				StartTime:  0,
				Duration:   0,
				MemoryUsed: 0,
				StatusCode: 429,
			},
			out: "uid",
		},
	}

	for _, info := range infos {
		v, _ := json.Marshal(info.in)
		msg := &sarama.ConsumerMessage{
			Value: v,
		}
		err := mgr.HandleMessage(msg)
		assert.Nil(t, err)
		mgr.prepareMetric()

		pack := <-mgr.requestCh

		bs, _ := json.Marshal(pack.BcmRequests[0])
		fmt.Printf(string(bs))

		if pack.BcmRequests[0].UserID != info.out {
			t.Fatalf("want:%v, but got:%v\n", info.out, pack.BcmRequests[0].UserID)
		}
	}

}

func TestUpdateMessageMarker(t *testing.T) {
	m := &manager{}
	m.updateMessageMarker(&sarama.ConsumerMessage{
		Partition: 0,
		Offset:    15,
	})
	assert.Equal(t, int64(15), m.messageMarker[0].Offset)
}
