package app

import (
	"time"

	"github.com/IBM/sarama"

	"icode.baidu.com/baidu/faas/kun/pkg/bce/bcm"
	"icode.baidu.com/baidu/faas/kun/pkg/kafka"
)

type ResultMap map[uint64]*UserMap
type UserMap map[string]*BrnMap
type BrnMap map[string]*Counter

type Counter struct {
	Brn     string
	Region  string
	Version string
	Uid     string

	// 需要计算多个统计型的采用[4]int存放：Cost[MAX]、Cost[MIN]...
	Duration       [4]float64
	MemoryUsed     [4]float64 //该次调用的内存使用情况
	MemoryDuration [4]float64 //MemoryUsed*Duration

	TotalCnt         int //调用总次数
	FunctionErrorCnt int //非429的4xx类型错误，包括函数内部错误、欠费错误、超时错误等等
	ServerErrorCnt   int //服务内部的5xx类型错误
	ConcurrencyLimit int //并发超限次数

	LastTime time.Time
}

// 统计类型
const (
	MAX = 0
	MIN = 1
	SUM = 2
	CNT = 3
)

const MB float64 = 1.0 * 1024 * 1024

const (
	METRIC_INVOCATION_COUNT    = "InvocationCount"
	METRIC_INVOCATION_DURATION = "InvocationDuration"
	METRIC_FUNCTION_ERRORS     = "FunctionErrors"
	METRIC_SERVER_ERRORS       = "ServerErrors"
	METRIC_MEMORY_USAGE        = "MemoryUsage"
	METRIC_MEMORY_DURATION     = "MemoryDuration"
	METRIC_CONCURRENCY_LIMIT   = "ConcurrencyLimit"
)

// CommitPack 表示一次提交任务
type CommitPack struct {
	Messages    map[int32]*sarama.ConsumerMessage // 待 commit 的 message，包含多个 partition
	BcmRequests []*bcm.UserMetricData             // 待推给 bcm 的数据
	Status      kafka.NotificationType            // 生成本任务时的 kafka 消费状态，rebalance 时本任务最终会被丢掉
}

type LogData struct {
	Message string `json:"message"`
}
