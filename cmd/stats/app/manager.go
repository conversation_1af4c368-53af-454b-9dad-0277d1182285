package app

import (
	"encoding/json"
	"fmt"
	"math"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/IBM/sarama"
	"go.uber.org/zap"

	"icode.baidu.com/baidu/faas/kun/cmd/stats/options"
	"icode.baidu.com/baidu/faas/kun/pkg/api"
	"icode.baidu.com/baidu/faas/kun/pkg/bce/bcm"
	"icode.baidu.com/baidu/faas/kun/pkg/kafka"
	"icode.baidu.com/baidu/faas/kun/pkg/util/logs"
)

type manager struct {
	bcmClient bcm.Interface

	resultMap    *ResultMap // 统计结果
	resultMapMux sync.Mutex
	requestCh    chan *CommitPack

	consumerGroup  kafka.ConsumerGroupInterface
	messageMarker  map[int32]*sarama.ConsumerMessage // 记录每次要 commit 的 messages
	consumerStatus kafka.NotificationType

	wg sync.WaitGroup

	interval int // 推送 bcm 时间间隔
}

func newManager(consumerGroup kafka.ConsumerGroupInterface, opt *options.StatsOptions, stopCh <-chan struct{}) *manager {
	m := &manager{
		bcmClient:     bcm.NewClient(opt.BCM),
		consumerGroup: consumerGroup,
		resultMap:     &ResultMap{},
		requestCh:     make(chan *CommitPack, 10),
		wg:            sync.WaitGroup{},
		interval:      opt.BCM.PushInterval,
	}

	m.startWoker(stopCh)
	m.wg.Add(1)

	return m
}

func (m *manager) Wait() {
	m.wg.Wait()
}

func (m *manager) HandleNotify(ntf *kafka.Notification) {
	logs.Infof("kafka notify: %s", ntf.Type.String())

	if ntf.Type == kafka.ConsumerCleanup {
		m.consumerStatus = kafka.ConsumerCleanup
	}

	if ntf.Type == kafka.ConsumerSetup {
		m.clearWorkspace()
		logs.Debug("changing status to setup")
		m.consumerStatus = kafka.ConsumerSetup
	}
}

func (m *manager) HandleError(err error) {
	logs.Errorf("kafka connect error: %v", err)
}

func (m *manager) HandleMessage(message *sarama.ConsumerMessage) error {
	raw := &LogData{}
	if err := json.Unmarshal(message.Value, &raw); err != nil {
		logs.Errorf("json unmarshal err %s", err.Error())
		return err
	}
	v := api.StatisticInfo{}
	if err := json.Unmarshal([]byte(raw.Message), &v); err != nil || len(v.UserID) == 0 {
		logs.Errorf("json unmarshal err %s", err.Error(), zap.String("rawdata", raw.Message))
		return err
	}

	m.resultMapMux.Lock()
	defer m.resultMapMux.Unlock()

	m.updateMessageMarker(message)
	return m.computeMetrics(&v)
}

func (m *manager) computeMetrics(v *api.StatisticInfo) error {
	// 一个v即一条message对应一个brn的一次调用信息

	//获取（初始化）counter
	rm := *m.resultMap
	uid := v.UserID
	t := v.StartTime
	tm := m.getUnixMins(t)
	if rm[tm] == nil {
		rm[tm] = &UserMap{}
	}
	um := *rm[tm]
	if um[uid] == nil {
		um[uid] = &BrnMap{}
	}
	brn := strings.Join([]string{v.Region, v.UserID, v.Function}, ".")
	bm := *um[uid]
	if bm[brn] == nil {
		bm[brn] = &Counter{
			Version: v.Version,
			Region:  v.Region,
			Uid:     v.UserID,
		}
	}

	counter := bm[brn]

	//分类统计status code
	counter.TotalCnt++
	if v.StatusCode == 429 {
		counter.ConcurrencyLimit++
	} else if v.StatusCode >= 500 {
		counter.ServerErrorCnt++
	} else if v.StatusCode >= 400 {
		counter.FunctionErrorCnt++
	}

	//统计Duration
	m.computeStatisticalValues(float64(v.Duration), &counter.Duration)
	//统计MemoryUsed
	m.computeStatisticalValues(float64(v.MemoryUsed)/MB, &counter.MemoryUsed)
	//计算MemoryDuration  byte direct to MB ?
	m.computeStatisticalValues(float64(v.MemoryUsed)/MB*float64(v.Duration), &counter.MemoryDuration)

	//最后一次更改时间
	counter.LastTime = time.Unix(v.StartTime/1000, 0)
	return nil
}

func (m *manager) computeStatisticalValues(value float64, metric *[4]float64) {

	//average会由bcm负责计算
	metric[SUM] += value
	metric[CNT]++
	if value > metric[MAX] {
		metric[MAX] = value
	}
	if metric[MIN] == 0 || value < metric[MIN] {
		metric[MIN] = value
		return
	}

}

// 将统计结果组织成待发送的 bcm metric 数据
func (m *manager) prepareMetric() {
	m.resultMapMux.Lock()

	rm := m.resultMap
	m.resultMap = &ResultMap{}
	messageMarker := m.messageMarker
	m.messageMarker = nil

	m.resultMapMux.Unlock()

	//重复消费直接退出
	if messageMarker == nil {
		return
	}

	pack := CommitPack{
		BcmRequests: make([]*bcm.UserMetricData, 0),
		Status:      m.consumerStatus,
	}

	// 一层遍历reslutMap中的UserMap
	for _, um := range *rm {

		//二层遍历UserMap中的BrnMap
		for uid, bm := range *um {

			//三层遍历BrnMap中的每个<brn,Counter>键值对
			for brn, counter := range *bm {

				// every UserMetricData can only contain one type of brn statistc data
				brnData := bcm.UserMetricData{}
				brnData.Scope = bcm.BCMScopeCFC
				brnData.UserID = uid

				//将每个brn的所有指标组成
				ds := []bcm.DimensionType{
					{
						Name:  "UserId",
						Value: uid,
					},
					{
						Name:  "Version",
						Value: counter.Version,
					},
					{
						Name:  "FunctionBrn",
						Value: brn,
					},
				}
				brnData.MetricData = append(brnData.MetricData, *m.cntMetricBuilder(ds, METRIC_INVOCATION_COUNT, counter.TotalCnt, counter))

				brnData.MetricData = append(brnData.MetricData, *m.cntMetricBuilder(ds, METRIC_FUNCTION_ERRORS, counter.FunctionErrorCnt, counter))

				brnData.MetricData = append(brnData.MetricData, *m.cntMetricBuilder(ds, METRIC_SERVER_ERRORS, counter.ServerErrorCnt, counter))

				brnData.MetricData = append(brnData.MetricData, *m.cntMetricBuilder(ds, METRIC_CONCURRENCY_LIMIT, counter.ConcurrencyLimit, counter))

				brnData.MetricData = append(brnData.MetricData, *m.statsMetricBuilder(ds, METRIC_INVOCATION_DURATION, &counter.Duration, counter))

				brnData.MetricData = append(brnData.MetricData, *m.statsMetricBuilder(ds, METRIC_MEMORY_USAGE, &counter.MemoryUsed, counter))

				brnData.MetricData = append(brnData.MetricData, *m.statsMetricBuilder(ds, METRIC_MEMORY_DURATION, &counter.MemoryDuration, counter))

				pack.BcmRequests = append(pack.BcmRequests, &brnData)

			}
		}
	}
	pack.Messages = messageMarker
	m.requestCh <- &pack
}

func (m *manager) cntMetricBuilder(ds []bcm.DimensionType, metricName string, metric int, counter *Counter) *bcm.MetricDatum {
	return &bcm.MetricDatum{
		MetricName: metricName,
		Dimensions: &ds,
		StatisticValues: &bcm.Statistic{
			Sum:         float64(metric),
			SampleCount: 1,
		},
		Timestamp: counter.LastTime,
	}
}

func (m *manager) statsMetricBuilder(ds []bcm.DimensionType, metricName string, metric *[4]float64, counter *Counter) *bcm.MetricDatum {

	metric[SUM], _ = strconv.ParseFloat(fmt.Sprintf("%.1f", metric[SUM]), 64)
	metric[MAX], _ = strconv.ParseFloat(fmt.Sprintf("%.1f", metric[MAX]), 64)
	metric[MIN], _ = strconv.ParseFloat(fmt.Sprintf("%.1f", metric[MIN]), 64)
	return &bcm.MetricDatum{
		MetricName: metricName,
		Dimensions: &ds,
		StatisticValues: &bcm.Statistic{
			Sum:         metric[SUM],
			SampleCount: int(metric[CNT]),
			Maximum:     metric[MAX],
			Minimum:     metric[MIN],
		},
		Timestamp: counter.LastTime,
	}
}

func (m *manager) startWoker(stopCh <-chan struct{}) {
	go func() {
		ticker := time.NewTicker(time.Duration(m.interval) * time.Second)
		defer ticker.Stop()
		for {
			select {
			case <-ticker.C: // 定时汇总一批统计结果
				m.prepareMetric()
			case <-stopCh:
				return
			}
		}
	}()

	go func() {
		for {
			select {
			case <-stopCh:
				logs.Info("catch stop signal, exiting...")
				m.wg.Done()
				return
			case cp := <-m.requestCh:
				m.processCommitPack(cp)
			}
		}
	}()
}

func (m *manager) processCommitPack(p *CommitPack) {
	if p.Status == kafka.ConsumerCleanup {
		return
	}

	// push bcm 时间较长，push 时若发生 rebalance 不能及时 commit，所以这里先 commit
	for _, msg := range p.Messages {
		logs.Debugf("going to commit message: %s", string(msg.Value))
		m.consumerGroup.CommitOffset(msg)
	}

	wg := &sync.WaitGroup{}
	for idx := range p.BcmRequests {
		wg.Add(1)
		go func(idx int) {
			b, _ := json.Marshal(p.BcmRequests[idx])
			if err := m.bcmClient.PushUserMetricData(p.BcmRequests[idx]); err != nil {
				logs.Errorf("size of request is %d", len(b))

				rCount := len(p.BcmRequests[idx].MetricData)
				logs.Errorf("count of record is : %d", rCount)
				logs.Errorf("send data to bcm error: %v", err)
			}
			logs.Infof("Metric data: %s", string(b))
			wg.Done()
		}(idx)
	}
	wg.Wait()
}

// 函数执行时 consumerStatus 为 cleanup，存量数据会被丢弃
func (m *manager) clearWorkspace() {
	m.prepareMetric()
}

// 获取当前分钟数, 用于按分钟计数
func (m *manager) getUnixMins(t int64) uint64 {
	t = t / 1000
	return uint64(math.Floor(float64(t) / 60))
}

func (m *manager) updateMessageMarker(newMsg *sarama.ConsumerMessage) {
	if m.messageMarker == nil {
		m.messageMarker = make(map[int32]*sarama.ConsumerMessage, 0)
	}
	if msg, ok := m.messageMarker[newMsg.Partition]; !ok || newMsg.Offset > msg.Offset {
		m.messageMarker[newMsg.Partition] = newMsg
	}
}
