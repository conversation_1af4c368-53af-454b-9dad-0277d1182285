package options

import (
	"github.com/spf13/pflag"

	"icode.baidu.com/baidu/faas/kun/pkg/bce/bcm"
	"icode.baidu.com/baidu/faas/kun/pkg/kafka"
)

type StatsOptions struct {
	KafkaOptions *kafka.Options
	BCM          *bcm.BcmConfiguration
}

// NewStatsOptions creates a new StatsOptions object with default parameters
func NewStatsOptions() *StatsOptions {
	s := &StatsOptions{
		KafkaOptions: kafka.NewKafkaOptions(),
		BCM:          bcm.NewBcmConfiguration(),
	}
	s.KafkaOptions.ConsumerGroupAutoCommit = false
	return s
}

// AddFlags adds flags for a specific APIServer to the specified FlagSet
func (s *StatsOptions) AddFlags(fs *pflag.FlagSet) {
	s.KafkaOptions.AddUniversalFlags(fs)
	s.BCM.AddUniversalFlags(fs)
}
