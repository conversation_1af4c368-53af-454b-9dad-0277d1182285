package app

import (
	"icode.baidu.com/baidu/faas/kun/cmd/poolmanager/app/options"
	poolmanager "icode.baidu.com/baidu/faas/kun/pkg/poolmanager/handler"
	"icode.baidu.com/baidu/faas/kun/pkg/server"
	"icode.baidu.com/baidu/faas/kun/pkg/util/logs"
	"icode.baidu.com/baidu/faas/kun/pkg/version"
)

// Run runs the specified poolmanagerServer with the given Dependencies.
// This should never exit.
func Run(runOptions *options.PoolManagerOptions, stopCh <-chan struct{}) error {
	logs.Infof("Version: %+v", version.Get())

	poolmanager.Init(runOptions)

	s, err := CreateServerChain(runOptions, stopCh)
	if err != nil {
		return err
	}

	return s.PrepareRun().Run(stopCh)
}

// CreateServerChain creates the apiservers connected via delegation.
func CreateServerChain(runOptions *options.PoolManagerOptions, stopCh <-chan struct{}) (*server.GenericServer, error) {
	config := server.NewRecommendedConfig()
	if err := runOptions.RecommendedOptions.ApplyTo(config); err != nil {
		return nil, err
	}

	s, err := config.Complete().New(options.ApplicationName)
	if err != nil {
		return nil, err
	}

	// Install hook
	//s.AddPreShutdownHook("telemetryShutdownHook", config.TelemetryInfo.Stop)

	// Install API
	poolmanager.InstallAPI(s.Handler.GoRestfulContainer)

	return s, nil
}
