package options

import (
	"github.com/spf13/pflag"

	"icode.baidu.com/baidu/faas/kun/pkg/etcd"
	funclet "icode.baidu.com/baidu/faas/kun/pkg/funclet/client"
	kunRedis "icode.baidu.com/baidu/faas/kun/pkg/rpc/redis"
	genericoptions "icode.baidu.com/baidu/faas/kun/pkg/server/options"
	"icode.baidu.com/baidu/faas/kun/pkg/store/reserve"
)

const (
	// ApplicationName 指定应用名称
	ApplicationName = "poolmanager"
)

// PoolManagerOptions descript options that poolmanager needs
type PoolManagerOptions struct {
	RecommendedOptions          *genericoptions.RecommendedOptions
	GenericRedisOptions         *kunRedis.RedisOptions
	ReserveOptions              *reserve.Options
	GenericEtcdOptions          *etcd.Options
	FuncletOptions              *funclet.FuncletClientOptions
	FuncletConnBackCacheOptions *etcd.FuncletConnBackCacheOptions
	UserIsolation               bool
	ReservedInstanceEnabled     bool
	RunningMode                 string
	GreNetworkCidr              string
	ProxyAgentPort              int
}

const (
	RunningModeCloud  = "cloud"
	RunningModeDuEdge = "duedge"
	RunningModeOTE    = "ote"
)

// NewPoolManagerOptions creates a new PoolManagerOptions object with default parameters
func NewPoolManagerOptions() *PoolManagerOptions {
	s := PoolManagerOptions{
		RecommendedOptions:  genericoptions.NewRecommendedOptions(),
		GenericRedisOptions: kunRedis.NewRedisOptions(),
		ReserveOptions:      reserve.NewOptions(),
		GenericEtcdOptions:  genericoptions.NewEtcdOptions(),
		FuncletOptions: &funclet.FuncletClientOptions{
			AccessMode: funclet.AccessModeNode,
			Port:       8231,
		},
		FuncletConnBackCacheOptions: etcd.NewFuncletConnBackCacheOptions(),
		UserIsolation:               true,
		RunningMode:                 RunningModeCloud,
		GreNetworkCidr:              "192.19.0.0/16",
		ProxyAgentPort:              8332,
		ReservedInstanceEnabled:     false,
	}
	s.RecommendedOptions.SecureServing.BindPort = 8082
	//s.RecommendedOptions.TelemetryOptions.ServiceName = ApplicationName
	return &s
}

// AddFlags adds flags for a specific poolmanager to the specified FlagSet
func (s *PoolManagerOptions) AddFlags(fs *pflag.FlagSet) {
	s.RecommendedOptions.AddFlags(fs)
	genericoptions.AddEtcdFlags(s.GenericEtcdOptions, fs)
	s.FuncletConnBackCacheOptions.AddFlags(fs)
	s.AddUniqueFlags("", fs)

	fs.StringVar(&s.RunningMode, "running-mode", s.RunningMode, ""+
		"Running mode: cloud, duedge or ote . The duedge mode implies --enable-user-isolation=false")

	fs.BoolVar(&s.ReservedInstanceEnabled, "reserved-instance-enabled", s.ReservedInstanceEnabled, "reserved-instance-enabled, default false")
}

// AddUniqueFlags adds unique flags for a specific poolmanager to the specified FlagSet
func (s *PoolManagerOptions) AddUniqueFlags(redisFlagPrefix string, fs *pflag.FlagSet) {
	s.FuncletOptions.AddFuncletClientFlags(fs)
	s.GenericRedisOptions.AddRedisFlagsWithPrefix(redisFlagPrefix, fs)
	s.ReserveOptions.AddFlags(fs)

	fs.BoolVar(&s.UserIsolation, "enable-user-isolation", s.UserIsolation, ""+
		"Enable user isolation or not")
	fs.StringVar(&s.GreNetworkCidr, "gre-network-cidr", s.GreNetworkCidr, ""+
		"alloc gre interface ip inside the cidr")
	fs.IntVar(&s.ProxyAgentPort, "proxy-agent-port", s.ProxyAgentPort, ""+
		"proxy agent service port")
}
