package app

import (
	"icode.baidu.com/baidu/faas/kun/cmd/mqhub/app/options"
	"icode.baidu.com/baidu/faas/kun/pkg/msgtrigger"
	genericserver "icode.baidu.com/baidu/faas/kun/pkg/server"
	"icode.baidu.com/baidu/faas/kun/pkg/util/logs"
	"icode.baidu.com/baidu/faas/kun/pkg/version"
)

// Run runs the specified EventhubServer with the given Dependencies.
// This should never exit.
func Run(runOptions *options.MqhubOptions, stopCh <-chan struct{}) error {
	logs.Infof("Version: %+v", version.Get())

	s, err := CreateServerChain(runOptions, stopCh)
	if err != nil {
		return err
	}

	return s.PrepareRun().Run(stopCh)
}

// CreateServerChain creates the apiservers connected via delegation.
func CreateServerChain(runOptions *options.MqhubOptions, stopCh <-chan struct{}) (*genericserver.GenericServer, error) {
	err := msgtrigger.Init(runOptions, stopCh)
	if err != nil {
		return nil, err
	}
	config := genericserver.NewRecommendedConfig()
	if err = runOptions.RecommendedOptions.ApplyTo(config); err != nil {
		return nil, err
	}
	s, err := config.Complete().New("msgtrigger")
	if err != nil {
		return nil, err
	}
	// Install hook
	s.AddPostStartHook("demoPostStartHook", msgtrigger.PostStartHook)
	s.AddPreShutdownHook("demoPreShutdownHook", msgtrigger.PreShutdownHook)

	return s, nil
}
