package main

import (
	"github.com/emicklei/go-restful"

	"icode.baidu.com/baidu/faas/kun/pkg/server/endpoint"
	"icode.baidu.com/baidu/faas/kun/pkg/util/logs"
)

func stubsLog(req *restful.Request, resp *restful.Response, chain *restful.FilterChain) {
	logs.V(6).Infof("%s %s header (%v) \n ", req.Request.Method, req.Request.RequestURI, req.Request.Header)
	chain.ProcessFilter(req, resp)
}

// InstallAPI xxx
func InstallAPI(container *restful.Container, o *StubsOptions) {
	apis := apiserverStubAPI()
	apis = append(apis, iamV1StubAPI()...)
	apis = append(apis, opscenterStubAPI()...)
	iamapis := iamStubAPI()

	var apiversion = []endpoint.ApiVersion{
		{
			Prefix: "/2015-03-31",
			Group:  apis,
		},
		{
			Prefix: "/v1",
			Group:  apis,
		},
		{
			Prefix: "/v3",
			Group:  iamapis,
		},
		{
			Prefix: "/inside-v1",
			Group:  apis,
		},
	}
	container.Filter(stubsLog)
	endpoint.NewApiInstaller(apiversion).Install(container)
}

func apiserverStubAPI() []endpoint.ApiSingle {
	s := NewApiserverFunctionStub()
	var apis = []endpoint.ApiSingle{
		{
			Verb:    "GET",
			Path:    "functions/{functionName}",
			Handler: s.GetFunction,
		},
		{
			Verb:    "GET",
			Path:    "runtimes/{runtime}/configuration",
			Handler: s.GetRuntime,
		},
		{
			Verb:    "GET",
			Path:    "/functions/{FunctionBrn}/policy",
			Handler: s.GetPolicy,
		},
	}
	return apis
}

func iamV1StubAPI() []endpoint.ApiSingle {
	s := NewIamStub()
	var apis = []endpoint.ApiSingle{
		{
			Verb:    "POST",
			Path:    "credential",
			Handler: s.Credential,
		},
	}
	return apis
}

func iamStubAPI() []endpoint.ApiSingle {
	s := NewIamStub()
	var apis = []endpoint.ApiSingle{
		{
			Verb:    "GET",
			Path:    "auth/tokens",
			Handler: s.GetAuthTokens,
		},
		{
			Verb:    "POST",
			Path:    "auth/tokens",
			Handler: s.VerifyAuthTokens,
		},
		{
			Verb:    "POST",
			Path:    "BCE-CRED/accesskeys",
			Handler: s.VerifySign,
		},
		{
			Verb:    "GET",
			Path:    "users/{userId}/accesskeys",
			Handler: s.GetAccessKeys,
		},
		{
			Verb:    "POST",
			Path:    "BCE-CRED/permissions",
			Handler: s.VerifyResultWithToken,
		},
	}
	return apis
}

func opscenterStubAPI() []endpoint.ApiSingle {
	s := NewOpcenterStub()
	var apis = []endpoint.ApiSingle{
		{
			Verb:    "POST",
			Path:    "/settings/quota/get",
			Handler: s.GetSingleQuota,
		},
		{
			Verb:    "POST",
			Path:    "/settings/quota/list_multi",
			Handler: s.GetBatchQuota,
		},
	}
	return apis
}
