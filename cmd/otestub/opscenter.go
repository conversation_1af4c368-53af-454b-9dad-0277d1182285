package main

import (
	"io/ioutil"

	"github.com/emicklei/go-restful"

	"icode.baidu.com/baidu/faas/kun/pkg/bce/opscenter"
	"icode.baidu.com/baidu/faas/kun/pkg/util/json"
	"icode.baidu.com/baidu/faas/kun/pkg/util/logs"
)

// OpcenterStub xxx
type OpcenterStub struct {
}

// NewOpcenterStub xxx
func NewOpcenterStub() *OpcenterStub {
	return &OpcenterStub{}
}

// GetSingleQuota xxx
func (s *OpcenterStub) GetSingleQuota(request *restful.Request, response *restful.Response) {
	logs.Infof("GetSingleQuota")
	resp := opscenter.SingleQuotaResponse{
		Quota: "300",
	}
	response.WriteHeaderAndEntity(200, resp)
}

// GetBatchQuota xxx
func (s *OpcenterStub) GetBatchQuota(request *restful.Request, response *restful.Response) {
	logs.Infof("GetBatchQuota")
	body, _ := ioutil.ReadAll(request.Request.Body)
	req := opscenter.BatchQuotaRequest{}
	json.Unmarshal(body, &req)

	quotas := map[string]map[string]string{}
	for _, userID := range req.UserValues {
		quotas[userID] = map[string]string{
			"user_concurrency": "300",
		}
	}
	resp := opscenter.BatchQuotaResponse{
		Quotas: quotas,
	}
	logs.Infof("%+v", resp)
	response.WriteHeaderAndEntity(200, resp)
}
