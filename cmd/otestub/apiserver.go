package main

import (
	"regexp"
	"strconv"
	"time"

	"github.com/emicklei/go-restful"

	"icode.baidu.com/baidu/faas/kun/pkg/api"
	"icode.baidu.com/baidu/faas/kun/pkg/util/json"
)

// ApiserverFunctionStub xxx
type ApiserverFunctionStub struct {
	defaultFunctionTemplate string
	defaultRuntime          *api.RuntimeConfiguration
	memoryPattern           *regexp.Regexp
	defaultPolicy           *api.GetPolicyOutput
}

// NewApiserverFunctionStub xxx
func NewApiserverFunctionStub() *ApiserverFunctionStub {
	s := `
	{
	  "Code": {
		"Location": "http://cfcbj.bj.bcebos.com/ote/hello.zip?authorization=bce-auth-v1/4746faeb37854542b225fef7cc87fc36/2018-12-03T07:17:34Z/-1/host/62c208f66f501430d6b4293bbf437f2d52f250b60eb1d0004f36c062532d1ebc",
		"RepositoryType": "Mock"
	  },
	  "Configuration": {
		"Id": 1011,
		"Uid": "42f6fbc2cd374bfcb80d9967370fd8ff",
		"Description": "",
		"FunctionBrn": "brn:bce:cfc:bj:e16ea3ddcc65d97108a4c9bfea14edb8:function:test:$LATEST",
		"Region": "bj",
		"Timeout": 3,
		"VersionDesc": "",
		"Role": "BceServiceRole_cfc",
		"UpdatedAt": "2018-01-31T15:07:14+08:00",
		"LastModified": "2018-01-31T15:07:14+08:00",
		"CodeSha256": "jHWDkTGEkDJtL5hyhuCyuxFN9dCAR2yBuwlbgnvDwys=",
		"CodeSize": 227,
		"FunctionArn": "brn:bce:cfc:bj:e16ea3ddcc65d97108a4c9bfea14edb8:function:test:$LATEST",
		"FunctionName": "test",
		"Handler": "index.handler",
		"Version": "$LATEST",
		"Runtime": "nodejs6.11",
		"MemorySize": 128,
		"Environment": {
		  "Variables": {
			"key1": "haha",
			"key2": "hehe"
		  }
		},
		"CommitId": "962a5b42-ecb8-4b22-aa3f-496784b422d9"
	  },
	  "EnviromentVariables": [
		{
		  "Key": "key1",
		  "Value": "haha"
		},
		{
		  "Key": "key2",
		  "Value": "hehe"
		}
	  ]
	}`

	r := &api.RuntimeConfiguration{
		Name: "nodejs6.11",
		Bin:  "/var/runtime/bin/node",
		Path: "/var/faas/runtime/node-v6.11.3-linux-x64",
		Args: []string{"var/runtime/lib/node_modules/kunruntime/index.js"},
	}
	p := &api.GetPolicyOutput{
		Condition: []struct {
			Source        string
			SourceAccount string
			Effect        string
			Action        string
		}{{
			Source:        "bos/test-bucket",
			SourceAccount: "",
			Effect:        "Allow",
			Action:        "cfc:InvokeFunction",
		}},
	}

	return &ApiserverFunctionStub{
		defaultFunctionTemplate: s,
		defaultRuntime:          r,
		// 在函数名内使用__mem:128__来指定函数的内存规格
		memoryPattern: regexp.MustCompile("__mem:(\\d+)__"),
		defaultPolicy: p,
	}
}

// GetFunction xx
func (s ApiserverFunctionStub) GetFunction(request *restful.Request, response *restful.Response) {
	f := &api.GetFunctionOutput{}
	if err := json.Unmarshal([]byte(s.defaultFunctionTemplate), f); err != nil {
		panic(err)
	}
	fname := request.PathParameter("functionName")
	f.Configuration.SetFunctionName(fname)

	qualifier := request.QueryParameter("Qualifier")
	if qualifier != "" {
		f.Configuration.SetVersion(qualifier)
	} else {
		f.Configuration.SetVersion("$LATEST")
	}

	if match := s.memoryPattern.FindStringSubmatch(fname); len(match) > 0 {
		memory, _ := strconv.ParseInt(match[1], 10, 64)
		f.Configuration.SetMemorySize(memory)
	}

	f.Configuration.SetDescription("THIS IS NOT A REAL FUNCTION " + time.Now().String())
	response.WriteHeaderAndEntity(200, f)
}

func (s ApiserverFunctionStub) GetRuntime(request *restful.Request, response *restful.Response) {
	response.WriteHeaderAndEntity(200, s.defaultRuntime)
}

func (s ApiserverFunctionStub) GetPolicy(request *restful.Request, response *restful.Response) {
	response.WriteHeaderAndEntity(200, s.defaultPolicy)
}
