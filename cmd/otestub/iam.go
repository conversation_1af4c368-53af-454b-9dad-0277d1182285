package main

import (
	"time"

	"github.com/emicklei/go-restful"

	"icode.baidu.com/baidu/faas/kun/pkg/bce/iam"
	"icode.baidu.com/baidu/faas/kun/pkg/bce/sts_credential"
	"icode.baidu.com/baidu/faas/kun/pkg/util/logs"
)

// IamStub xxx
type IamStub struct {
}

// NewIamStub xxx
func NewIamStub() *IamStub {
	return &IamStub{}
}

// AuthTokenResp xxx
type AuthTokenResp struct {
	Token *iam.Token `json:"token"`
}

// GetAuthTokens xxx
func (s IamStub) GetAuthTokens(request *restful.Request, response *restful.Response) {
	logs.V(5).Infof("GetAuthTokens %+v", request)
	resp := &AuthTokenResp{
		Token: &iam.Token{
			ExpiresAt: time.Now().Add(30 * time.Minute),
			IssuedAt:  time.Now(),
		},
	}
	response.WriteHeaderAndEntity(200, resp)
}

// VerifyAuthTokens xxx
func (s IamStub) VerifyAuthTokens(request *restful.Request, response *restful.Response) {
	logs.V(5).Infof("VerifyAuthTokens %+v", request)
	resp := &AuthTokenResp{
		Token: &iam.Token{
			ExpiresAt: time.Now().Add(30 * time.Minute),
			IssuedAt:  time.Now(),
			User: iam.User{
				ID: "stub-user-id",
				Domain: &iam.Domain{
					Name: "Default",
				},
				Name:     "stub-user",
				Password: "stub-user-password",
			},
		},
	}
	response.WriteHeaderAndEntity(201, resp)
}

// VerifySign xxx
func (s IamStub) VerifySign(request *restful.Request, response *restful.Response) {
	logs.V(5).Infof("VerifySign %+v", request)
	resp := &AuthTokenResp{
		Token: &iam.Token{
			ExpiresAt: time.Now().Add(30 * time.Minute),
			IssuedAt:  time.Now(),
		},
	}
	response.WriteHeaderAndEntity(200, resp)
}

type AccessKeyResp struct {
	AccessKeys []*iam.Accesskey `json:"accesskeys"`
}

func (s IamStub) GetAccessKeys(request *restful.Request, response *restful.Response) {
	logs.V(5).Infof("GetAccessKeys %+v", request)
	resp := &AccessKeyResp{
		AccessKeys: []*iam.Accesskey{
			{
				CredentialID: "CredentialID",
				AccessKey:    "AccessKey",
				SecretKey:    "SecretKey",
				ProjectID:    "ProjectID",
				UserID:       "UserID",
			},
		},
	}
	response.WriteHeaderAndEntity(200, resp)
}

type VerifyResultWithTokenResp struct {
	Result *iam.SinglePermissionVerifyResult `json:"verify_result"`
	Token  *iam.Token                        `json:"token"`
}

func (s IamStub) VerifyResultWithToken(request *restful.Request, response *restful.Response) {
	logs.V(5).Infof("VerifyResultWithToken %+v", request)
	resp := VerifyResultWithTokenResp{
		Result: &iam.SinglePermissionVerifyResult{
			Effect: "ALLOW",
			Id:     "acl id",
			Eid:    "entry id",
		},
		Token: &iam.Token{
			ExpiresAt: time.Now().Add(30 * time.Minute),
			IssuedAt:  time.Now(),
			User: iam.User{
				Domain: &iam.Domain{
					ID: "user id",
				},
			},
		},
	}
	response.WriteHeaderAndEntity(200, resp)
}

func (s IamStub) Credential(request *restful.Request, response *restful.Response) {
	logs.V(5).Infof("Credential %+v", request)
	resp := &sts_credential.StsCredential{
		AccessKeyId:     "ak",
		AccessKeySecret: "sk",
		SessionToken:    "token",
		Expiration:      time.Now().Add(30 * time.Minute),
		UserId:          "userid",
		RoleId:          "roleID",
	}
	response.WriteHeaderAndEntity(200, resp)
}
