package main

import (
	"fmt"
	"math/rand"
	"os"
	"time"

	"github.com/spf13/pflag"

	"icode.baidu.com/baidu/faas/kun/pkg/server"
	genericserver "icode.baidu.com/baidu/faas/kun/pkg/server"
	genericoptions "icode.baidu.com/baidu/faas/kun/pkg/server/options"
	"icode.baidu.com/baidu/faas/kun/pkg/util/flag"
	"icode.baidu.com/baidu/faas/kun/pkg/util/logs"
	"icode.baidu.com/baidu/faas/kun/pkg/version"
	"icode.baidu.com/baidu/faas/kun/pkg/version/verflag"
)

func main() {
	s := NewStubsOptions()
	s.AddFlags(pflag.CommandLine)

	flag.InitFlags()
	logs.InitLogs()
	defer logs.FlushLogs()

	verflag.PrintAndExitIfRequested()

	stopCh := server.SetupSignalHandler()
	if err := Run(s, stopCh); err != nil {
		fmt.Fprintf(os.Stderr, "%v\n", err)
		os.Exit(1)
	}
}

// Run runs the specified EventhubServer with the given Dependencies.
// This should never exit.
func Run(runOptions *StubsOptions, stopCh <-chan struct{}) error {
	logs.Infof("Version: %+v", version.Get())
	rand.Seed(time.Now().UnixNano())

	s, err := CreateServerChain(runOptions, stopCh)
	if err != nil {
		return err
	}

	return s.PrepareRun().Run(stopCh)
}

// CreateServerChain creates the apiservers connected via delegation.
func CreateServerChain(runOptions *StubsOptions, stopCh <-chan struct{}) (*genericserver.GenericServer, error) {

	config := genericserver.NewRecommendedConfig()
	if err := runOptions.RecommendedOptions.ApplyTo(config); err != nil {
		return nil, err
	}

	s, err := config.Complete().New("stub")
	if err != nil {
		return nil, err
	}

	// Install API
	InstallAPI(s.Handler.GoRestfulContainer, runOptions)

	return s, nil
}

// StubsOptions descript options that stubs needs
type StubsOptions struct {
	RecommendedOptions  *genericoptions.RecommendedOptions
	EnableStrictInvoker bool
}

// NewStubsOptions creates a new StubsOptions object with default parameters
func NewStubsOptions() *StubsOptions {
	s := StubsOptions{
		RecommendedOptions: genericoptions.NewRecommendedOptions(),
	}
	return &s
}

// AddFlags adds flags for a specific APIServer to the specified FlagSet
func (s *StubsOptions) AddFlags(fs *pflag.FlagSet) {
	s.RecommendedOptions.AddFlags(fs)
}
