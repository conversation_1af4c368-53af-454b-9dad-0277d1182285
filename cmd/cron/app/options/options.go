package options

import (
	"github.com/spf13/pflag"

	"icode.baidu.com/baidu/faas/kun/pkg/bce/iam"
	"icode.baidu.com/baidu/faas/kun/pkg/etcd"
	eventhub "icode.baidu.com/baidu/faas/kun/pkg/eventhub/client"
	funclet "icode.baidu.com/baidu/faas/kun/pkg/funclet/client"
	kunRedis "icode.baidu.com/baidu/faas/kun/pkg/rpc/redis"
	genericoptions "icode.baidu.com/baidu/faas/kun/pkg/server/options"
	ote_fi "icode.baidu.com/baidu/faas/kun/pkg/store/node-info"
	"icode.baidu.com/baidu/faas/kun/pkg/store/reserve"
	"icode.baidu.com/baidu/faas/kun/pkg/util/baiduhi"
)

const (
	// ApplicationName 指定应用名称
	ApplicationName = "cron"
)

// cron的运行模式
const (
	RunningModeCloud  = "cloud"
	RunningModeDuedge = "duedge"
	RunningModeOTE    = "ote"
)

// 同步floating ip的方式
const (
	SyncIPTypeName     = "name"
	SyncIPTypeCCE      = "cce"
	SyncIPTypeInternal = "internal"
)

// CronOptions descript options that Cron needs
type CronOptions struct {
	RecommendedOptions          *genericoptions.RecommendedOptions
	RedisClusterOptions         *kunRedis.RedisOptions // 新的redis集群
	ReserveOptions              *reserve.Options
	GenericEtcdOptions          *etcd.Options
	TaskSchedulerOptions        *TaskSchedulerOptions
	InspectionOptions           *InspectionOptions
	EventhubOptions             *eventhub.EventHubOptions
	FuncletOptions              *funclet.FuncletClientOptions
	FuncletConnBackCacheOptions *ote_fi.Options
	RunningMode                 string // 运行模式，当前支持cloud、duedge和ote
	ElectionPrefix              string // 选举使用的etcd前缀
	EnableElection              bool
	CodeSweeperOptions          *TaskCodeSweeperOptions
	IAMConfiguration            *iam.IAMOptions
	BaiduHiOptions              *baiduhi.BaiduHiClientOption
	ConfDir                     string
	ProxyAgentPort              int
	CpulimitRatio               float64
}

type TaskCommonSchedulerOptions struct {
	MaxPodExpire        int // 允许的Pod的最大闲置时间，单位是秒
	TaskInterval        int // 周期性任务的间隔时间，单位是秒
	StateStableDuration int // 确保etcd上的node状态同步的时间，单位是秒
}

type TaskCloudSchedulerOptions struct {
	AutoOutdateNode       bool   // 是否在健康检查时将不健康node转移至k8s层 TODO: 这个需要与其他配置整合
	SyncIPType            string // 同步ip的方式，可选name、cce、internal
	FunctionTTL           int    // pod在一次函数调用中允许的最长运行时间，单位是秒
	DaemonSetReadyTimeout int    // 初始化node前等待daemon set状态正常的最长时间，单位是秒
	InitNodeTimeout       int    // 初始化node时等待所有pod状态转为running的最长时间，单位是秒
}

type TaskSchedulerOptions struct {
	*TaskCommonSchedulerOptions
	*TaskCloudSchedulerOptions
}

// InspectionOptions 是健康检查相关参数
type InspectionOptions struct {
	SickPodMaxRatio     float64 // 单个node允许的sick pod最大比例
	DubiousNodeMaxTimes uint64  // dubious node错误次数超过后会被下线
}

type TaskCodeSweeperOptions struct {
	CodeExpiration    int
	TrashExpiration   int
	ApiserverEndpoint string
	EnableSweep       bool
}

// FuncletConnBackCacheOptions funclet 连接信息选项
// 适用于Funclet需要通过PodIP访问的场景，用于控制相应的缓存信息
type FuncletConnBackCacheOptions struct {
	ExpirySecond int // etcd缓存失效时间，单位 s
}

// NewCronOptions creates a new CronOptions object with default parameters
func NewCronOptions() *CronOptions {
	s := CronOptions{
		RecommendedOptions:  genericoptions.NewRecommendedOptions(),
		RedisClusterOptions: kunRedis.NewRedisOptions(),
		ReserveOptions:      reserve.NewOptions(),
		GenericEtcdOptions:  genericoptions.NewEtcdOptions(),
		TaskSchedulerOptions: &TaskSchedulerOptions{
			TaskCommonSchedulerOptions: &TaskCommonSchedulerOptions{
				MaxPodExpire:        1 * 60,
				TaskInterval:        10,
				StateStableDuration: 2,
			},
			TaskCloudSchedulerOptions: &TaskCloudSchedulerOptions{
				AutoOutdateNode:       false,
				SyncIPType:            SyncIPTypeCCE,
				FunctionTTL:           6 * 60,
				DaemonSetReadyTimeout: 2 * 60,
				InitNodeTimeout:       1 * 60,
			},
		},
		InspectionOptions: &InspectionOptions{
			SickPodMaxRatio:     0.3,
			DubiousNodeMaxTimes: 6,
		},
		FuncletOptions: &funclet.FuncletClientOptions{
			AccessMode: funclet.AccessModeNode,
			Port:       8231,
		},
		FuncletConnBackCacheOptions: ote_fi.NewOptions(),
		RunningMode:                 RunningModeCloud,
		ElectionPrefix:              "cron",
		EnableElection:              false,
		CodeSweeperOptions: &TaskCodeSweeperOptions{
			CodeExpiration:  30 * 24 * 3600,
			TrashExpiration: 7 * 24 * 3600,
			EnableSweep:     false,
		},
		IAMConfiguration: iam.NewIAMOptions(),
		BaiduHiOptions:   baiduhi.DefaultBaiduHiOptions(),
		ConfDir:          "/home/<USER>/faas/cron/conf",
		EventhubOptions:  &eventhub.EventHubOptions{},
		ProxyAgentPort:   8332,
		CpulimitRatio:    0.6,
	}
	s.RecommendedOptions.SecureServing.BindPort = 8082
	//s.RecommendedOptions.TelemetryOptions.ServiceName = ApplicationName
	return &s
}

// AddFlags adds flags for a specific Cron to the specified FlagSet
func (s *CronOptions) AddFlags(fs *pflag.FlagSet) {
	s.RecommendedOptions.AddFlags(fs)
	s.RedisClusterOptions.AddRedisFlags(fs)
	s.ReserveOptions.AddFlags(fs)
	s.FuncletConnBackCacheOptions.AddFlags(fs)
	genericoptions.AddEtcdFlags(s.GenericEtcdOptions, fs)
	s.FuncletOptions.AddFuncletClientFlags(fs)
	s.IAMConfiguration.AddUniversalFlags(fs)
	s.BaiduHiOptions.AddFlags(fs)
	s.EventhubOptions.AddEventHubFlags(fs)

	fs.StringVar(&s.RunningMode, "running-mode", s.RunningMode,
		"Set kind of task to run, e.g. [cloud | duedge | ote]")

	fs.StringVar(&s.ElectionPrefix, "election-prefix", s.ElectionPrefix,
		"Set prefix of election")

	fs.BoolVar(&s.EnableElection, "enable-election", s.EnableElection,
		"Whether elect or not")

	fs.BoolVar(&s.TaskSchedulerOptions.AutoOutdateNode, "auto-outdate-node", s.TaskSchedulerOptions.AutoOutdateNode,
		"Outdate node which is unhealthy after inspection")

	fs.StringVar(&s.TaskSchedulerOptions.SyncIPType, "sync-ip-type", s.TaskSchedulerOptions.SyncIPType,
		"How synchronize floating ip, eg. name, cce, internal")

	fs.IntVar(&s.TaskSchedulerOptions.MaxPodExpire, "max-pod-expire", s.TaskSchedulerOptions.MaxPodExpire,
		"")

	fs.IntVar(&s.TaskSchedulerOptions.DaemonSetReadyTimeout, "daemonset-ready-timeout", s.TaskSchedulerOptions.DaemonSetReadyTimeout,
		"")

	fs.IntVar(&s.TaskSchedulerOptions.InitNodeTimeout, "init-node-timeout", s.TaskSchedulerOptions.InitNodeTimeout,
		"")

	fs.IntVar(&s.TaskSchedulerOptions.TaskInterval, "task-interval", s.TaskSchedulerOptions.TaskInterval,
		"")

	fs.IntVar(&s.TaskSchedulerOptions.StateStableDuration, "state-stable-duration", s.TaskSchedulerOptions.StateStableDuration,
		"")

	fs.IntVar(&s.TaskSchedulerOptions.FunctionTTL, "function-ttl", s.TaskSchedulerOptions.FunctionTTL,
		"")

	fs.Float64Var(&s.InspectionOptions.SickPodMaxRatio, "sick-pod-max-ratio", s.InspectionOptions.SickPodMaxRatio,
		"")

	fs.Uint64Var(&s.InspectionOptions.DubiousNodeMaxTimes, "dubious-node-max-times", s.InspectionOptions.DubiousNodeMaxTimes,
		"")

	fs.StringVar(&s.CodeSweeperOptions.ApiserverEndpoint, "apiserver-endpoint", s.CodeSweeperOptions.ApiserverEndpoint,
		"apiserver endpoint")
	fs.IntVar(&s.CodeSweeperOptions.CodeExpiration, "code-expiration", s.CodeSweeperOptions.CodeExpiration,
		"code expiration")
	fs.IntVar(&s.CodeSweeperOptions.TrashExpiration, "trash-expiration", s.CodeSweeperOptions.TrashExpiration,
		"trash expiration")
	fs.BoolVar(&s.CodeSweeperOptions.EnableSweep, "enable-sweep", s.CodeSweeperOptions.EnableSweep,
		"enable-sweep")
	fs.StringVar(&s.ConfDir, "conf-dir", s.ConfDir, "conf-dir")
	fs.IntVar(&s.ProxyAgentPort, "proxy-agent-port", s.ProxyAgentPort, "port of proxy agent")

	fs.Float64Var(&s.CpulimitRatio, "cpu-limit-ratio", s.CpulimitRatio, "the actual value of cpu limit =  the computing result *  CpulimitRatio ")
}
