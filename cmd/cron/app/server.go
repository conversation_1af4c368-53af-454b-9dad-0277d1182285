package app

import (
	"icode.baidu.com/baidu/faas/kun/cmd/cron/app/options"
	"icode.baidu.com/baidu/faas/kun/pkg/cron"
	"icode.baidu.com/baidu/faas/kun/pkg/server"
	"icode.baidu.com/baidu/faas/kun/pkg/util/logs"
	"icode.baidu.com/baidu/faas/kun/pkg/version"
)

// Run runs the specified cron server with the given Dependencies.
// This should never exit.
func Run(runOptions *options.CronOptions, stopCh <-chan struct{}) error {
	logs.Infof("Version: %+v", version.Get())

	cron.Init(runOptions)

	s, err := CreateServerChain(runOptions, stopCh)
	if err != nil {
		return err
	}

	return s.PrepareRun().Run(stopCh)
}

// CreateServerChain creates the apiservers connected via delegation.
func CreateServerChain(runOptions *options.CronOptions, stopCh <-chan struct{}) (*server.GenericServer, error) {

	config := server.NewRecommendedConfig()
	if err := runOptions.RecommendedOptions.ApplyTo(config); err != nil {
		return nil, err
	}

	s, err := config.Complete().New(options.ApplicationName)
	if err != nil {
		return nil, err
	}

	s.AddPreShutdownHook("Cron PreShutdownHook", cron.Stop)

	// Install API
	cron.InstallAPI(s.Handler.GoRestfulContainer)

	return s, nil
}
