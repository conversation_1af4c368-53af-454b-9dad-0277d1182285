package main

import (
	"fmt"
	"os"

	"github.com/spf13/pflag"

	"icode.baidu.com/baidu/faas/kun/cmd/cron/app"
	"icode.baidu.com/baidu/faas/kun/cmd/cron/app/options"
	"icode.baidu.com/baidu/faas/kun/pkg/util/flag"
	"icode.baidu.com/baidu/faas/kun/pkg/util/logs"
	"icode.baidu.com/baidu/faas/kun/pkg/util/wait"
	"icode.baidu.com/baidu/faas/kun/pkg/version/verflag"
)

func main() {
	s := options.NewCronOptions()
	s.AddFlags(pflag.CommandLine)

	flag.InitFlags()
	logs.InitLogs()
	defer logs.FlushLogs()

	verflag.PrintAndExitIfRequested()

	if err := app.Run(s, wait.NeverStop); err != nil {
		fmt.Fprintf(os.Stderr, "%v\n", err)
		os.Exit(1)
	}
}
