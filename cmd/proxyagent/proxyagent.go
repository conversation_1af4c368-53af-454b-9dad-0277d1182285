package main

/*
 #cgo CFLAGS: -Wall
 extern void nsexec();

  __attribute__((constructor)) void enter_namespace(void) {
	  nsexec();
 }
*/
import "C"

import (
	"fmt"
	"os"

	"github.com/spf13/pflag"

	"icode.baidu.com/baidu/faas/kun/cmd/proxyagent/app"
	"icode.baidu.com/baidu/faas/kun/cmd/proxyagent/app/options"
	"icode.baidu.com/baidu/faas/kun/pkg/server"
	"icode.baidu.com/baidu/faas/kun/pkg/util/flag"
	"icode.baidu.com/baidu/faas/kun/pkg/util/logs"
	"icode.baidu.com/baidu/faas/kun/pkg/version/verflag"
)

func main() {

	s := options.NewProxyAgentOptions()
	s.AddFlags(pflag.CommandLine)

	flag.InitFlags()
	logs.InitLogs()

	verflag.PrintAndExitIfRequested()

	stopCh := server.SetupSignalHandler()
	if err := app.Run(s, stopCh); err != nil {
		_, _ = fmt.Fprintf(os.Stderr, "%v\n", err)
		os.Exit(1)
	}
}
