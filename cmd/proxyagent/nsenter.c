#include <sched.h>
#include <stdio.h>
#include <unistd.h>
#include <fcntl.h>
#include <stdlib.h>
#include <string.h>
#include <errno.h>

#define PATH_MAX        4096

void nsexec(void) {
    char pathbuf[PATH_MAX];
    char *target_namespaces[] = {"ns/net", "ns/mnt","ns/ipc","ns/uts","ns/pid",0};
    char **ptr = target_namespaces;
    int fd = -1;
    char * pid_str = getenv("NSENTER_PID");
    if (pid_str == NULL || strlen(pid_str) == 0) {
        return ;
    }
    int pid = atoi(pid_str);
    if (pid <= 0) {
        fprintf(stderr, "Pid is invalid, raw data = %s\n", pid_str);
        exit(1);
    }
    while(*ptr != 0)
    {
        snprintf(pathbuf, sizeof(pathbuf), "/proc/%u/%s", pid, *ptr);
        if ((fd = open(pathbuf, O_RDONLY)) == -1)
        {
            fprintf(stderr, "Trying to open namespace '%s': %s\n", pathbuf, strerror(errno));
            exit(1);
        }
        if (syscall(308, fd, 0) != 0)
        {
            fprintf(stderr, "Trying to set namespace '%s': %s\n", pathbuf, strerror(errno));
            exit(1);
        }
        ++ptr;
    }
}

