package options

import (
	"github.com/spf13/pflag"
	genericoptions "icode.baidu.com/baidu/faas/kun/pkg/server/options"
)

type ProxyAgentOptions struct {
	ApplicationName    string
	BasePath           string
	TunnelCidr         string
	RecommendedOptions *genericoptions.RecommendedOptions
}

// NewProxyAgentOptions creates a new ProxyAgentOptions object with default parameters
func NewProxyAgentOptions() *ProxyAgentOptions {
	return &ProxyAgentOptions{
		ApplicationName:    "proxyagent",
		BasePath:           "/root",
		TunnelCidr:         "192.19.0.0/16",
		RecommendedOptions: genericoptions.NewRecommendedOptions(),
	}
}

// AddFlags adds flags to the specified FlagSet
func (s *ProxyAgentOptions) AddFlags(fs *pflag.FlagSet) {
	s.RecommendedOptions.AddFlags(fs)
	fs.StringVar(&s.BasePath, "base-path", s.<PERSON>ath, "base path")
	fs.StringVar(&s.<PERSON>, "tunnel-cidr", s.<PERSON>, "cidr of gre tunnel")
}
