package app

import (
	"errors"
	"net/http"
	"net/http/httptest"
	"strings"
	"testing"
	"time"
	"unsafe"

	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/assert"

	"github.com/emicklei/go-restful"
	"icode.baidu.com/baidu/faas/kun/cmd/proxyagent/app/options"
	"icode.baidu.com/baidu/faas/kun/pkg/proxyagent/network"
	"icode.baidu.com/baidu/faas/kun/pkg/proxyagent/network/mock"
	"icode.baidu.com/baidu/faas/kun/pkg/server"
	"icode.baidu.com/baidu/faas/kun/pkg/util/base64"
)

func TestNewProxyAgent(t *testing.T) {
	s := options.NewProxyAgentOptions()
	s.BasePath = "/tmp"
	network.KernelForwardConfig = "/tmp/ip_forward"

	p, err := NewProxyAgent(s)
	assert.Nil(t, err)
	assert.NotNil(t, p)
}

func TestRun(t *testing.T) {
	opt := options.NewProxyAgentOptions()
	opt.BasePath = "/tmp"
	network.KernelForwardConfig = "/tmp/ip_forward"

	stopCh := make(chan struct{}, 0)
	waitCh := make(chan struct{}, 0)

	go func() {
		err := Run(opt, stopCh)
		assert.Nil(t, err)
		waitCh <- struct{}{}
	}()

	time.Sleep(1 * time.Second)
	close(stopCh)
	<-waitCh
}

func TestProxyAgent_notifyOccupy(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	m := mock.NewMockInterface(ctrl)
	proxyAgent := &ProxyAgent{
		Options:    options.NewProxyAgentOptions(),
		NetWorkMgr: m,
	}

	vpcCidr := base64.EncodeToString([]byte("**********/16"))

	ctx := BuildNewKunCtx("POST", "/v1/proxyagent/occupy?vip=**********&eniMacAddr=FF:FF:FF:FF:FF&vpcCidr="+string(vpcCidr), "", "", nil)
	m.EXPECT().InitNetwork("**********", "FF:FF:FF:FF:FF", "**********/16").Return(nil)
	proxyAgent.notifyOccupy(ctx)
	assert.Equal(t, http.StatusOK, ctx.Response().StatusCode())

	m.EXPECT().InitNetwork("**********", "FF:FF:FF:FF:FF", "**********/16").Return(errors.New("x"))
	proxyAgent.notifyOccupy(ctx)
	assert.NotEqual(t, http.StatusOK, ctx.Response().StatusCode())

	ctx = BuildNewKunCtx("POST", "/v1/proxyagent/occupy?eniMacAddr=FF:FF:FF:FF:FF", "", "", nil)
	proxyAgent.notifyOccupy(ctx)
	assert.NotEqual(t, http.StatusOK, ctx.Response().StatusCode())
}

func TestProxyAgent_notifyRelease(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	m := mock.NewMockInterface(ctrl)
	proxyAgent := &ProxyAgent{
		Options:    options.NewProxyAgentOptions(),
		NetWorkMgr: m,
	}

	ctx := BuildNewKunCtx("POST", "/v1/proxyagent/release", "", "", nil)
	m.EXPECT().ResetNetwork().Return(nil)
	proxyAgent.notifyRelease(ctx)
	assert.Equal(t, http.StatusOK, ctx.Response().StatusCode())

	m.EXPECT().ResetNetwork().Return(errors.New("err"))
	proxyAgent.notifyRelease(ctx)
	assert.NotEqual(t, http.StatusOK, ctx.Response().StatusCode())
}

func TestProxyAgent_healthCheck(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	m := mock.NewMockInterface(ctrl)
	proxyAgent := &ProxyAgent{
		Options:    options.NewProxyAgentOptions(),
		NetWorkMgr: m,
	}

	ctx := BuildNewKunCtx("POST", "/v1/proxyagent/health", "", "", nil)
	proxyAgent.health(ctx)
	assert.NotEqual(t, http.StatusOK, ctx.Response().StatusCode())

	ctx = BuildNewKunCtx("POST", "/v1/proxyagent/health?eniMacAddr=FF:FF:FF:FF:FF", "", "", nil)
	m.EXPECT().HealthCheck("FF:FF:FF:FF:FF").Return(nil)
	proxyAgent.health(ctx)
	assert.Equal(t, http.StatusOK, ctx.Response().StatusCode())

	m.EXPECT().HealthCheck("FF:FF:FF:FF:FF").Return(errors.New("err"))
	proxyAgent.health(ctx)
	assert.NotEqual(t, http.StatusOK, ctx.Response().StatusCode())
}

func TestProxyAgent_addTunnelPeer(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	m := mock.NewMockInterface(ctrl)
	proxyAgent := &ProxyAgent{
		Options:    options.NewProxyAgentOptions(),
		NetWorkMgr: m,
	}

	ctx := BuildNewKunCtx("POST", "/v1/proxyagent/addPeer", "", "", nil)
	proxyAgent.addTunnelPeer(ctx)
	assert.NotEqual(t, http.StatusOK, ctx.Response().StatusCode())

	ctx = BuildNewKunCtx("POST", "/v1/proxyagent/addrPeer?peerNodeIP=***********&&peerTunnelIP=********", "", "", nil)
	m.EXPECT().AddTunnelPeer("***********", "********").Return(nil)
	proxyAgent.addTunnelPeer(ctx)
	assert.Equal(t, http.StatusOK, ctx.Response().StatusCode())

	m.EXPECT().AddTunnelPeer("***********", "********").Return(errors.New("err"))
	proxyAgent.addTunnelPeer(ctx)
	assert.NotEqual(t, http.StatusOK, ctx.Response().StatusCode())
}

func TestProxyAgent_removePeer(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	m := mock.NewMockInterface(ctrl)
	proxyAgent := &ProxyAgent{
		Options:    options.NewProxyAgentOptions(),
		NetWorkMgr: m,
	}

	ctx := BuildNewKunCtx("POST", "/v1/proxyagent/removePeer", "", "", nil)
	proxyAgent.removeTunnelPeer(ctx)
	assert.NotEqual(t, http.StatusOK, ctx.Response().StatusCode())

	ctx = BuildNewKunCtx("POST", "/v1/proxyagent/removePeer?peerNodeIP=***********&&peerTunnelIP=********", "", "", nil)
	m.EXPECT().RemoveTunnelPeer("***********", "********").Return(nil)
	proxyAgent.removeTunnelPeer(ctx)
	assert.Equal(t, http.StatusOK, ctx.Response().StatusCode())

	m.EXPECT().RemoveTunnelPeer("***********", "********").Return(errors.New("err"))
	proxyAgent.removeTunnelPeer(ctx)
	assert.NotEqual(t, http.StatusOK, ctx.Response().StatusCode())
}

func BuildNewKunCtx(method, uri, body, uid string, pathMapV map[string]string) *server.Context {
	request := httptest.NewRequest(method, uri, strings.NewReader(body))
	restReq := restful.NewRequest(request)
	restRsp := restful.NewResponse(httptest.NewRecorder())
	restReq.Request.Header.Set("Content-Type", "application/json")
	restRsp.SetRequestAccepts(restful.MIME_JSON)
	if pathMapV != nil {
		var pathMap *map[string]string = (*map[string]string)(unsafe.Pointer(uintptr(unsafe.Pointer(restReq)) + uintptr(unsafe.Sizeof(restReq.Request))))
		*pathMap = pathMapV
	}
	return server.BuildContext(restReq, restRsp, "")
}
