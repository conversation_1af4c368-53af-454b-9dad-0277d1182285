package app

import (
	"errors"
	"net/http"

	"icode.baidu.com/baidu/faas/kun/pkg/api"
	kunErr "icode.baidu.com/baidu/faas/kun/pkg/error"
	"icode.baidu.com/baidu/faas/kun/pkg/server"
	"icode.baidu.com/baidu/faas/kun/pkg/util/base64"
)

// notifyOccupy 通知 agent 被占用，执行网络环境初始化
func (proxyAgent *ProxyAgent) notifyOccupy(ctx *server.Context) {
	vip := ctx.Request().QueryParameter(api.QueryVIP)
	eniMacAddr := ctx.Request().QueryParameter(api.QueryENIMACAddr)
	vpcCidrEncoded := ctx.Request().QueryParameter(api.VpcCidr)

	if vip == "" || eniMacAddr == "" {
		ctx.WithErrorLog(errors.New("vip or eniMacAddr is empty")).WriteTo(ctx.Response())
		return
	}

	vpcCidr, err := base64.DecodeString(vpcCidrEncoded)
	if err != nil {
		ctx.WithErrorLog(err).WriteTo(ctx.Response())
		return
	}

	if err := proxyAgent.NetWorkMgr.InitNetwork(vip, eniMacAddr, string(vpcCidr)); err != nil {
		ctx.WithErrorLog(kunErr.NewServiceException(err.Error(), nil)).WriteTo(ctx.Response())
		return
	}

	ctx.Response().WriteHeader(http.StatusOK)
}

// notifyRelease 通知 agent 不再被占用，恢复网络环境
func (proxyAgent *ProxyAgent) notifyRelease(ctx *server.Context) {
	if err := proxyAgent.NetWorkMgr.ResetNetwork(); err != nil {
		ctx.WithErrorLog(kunErr.NewServiceException(err.Error(), nil)).
			WriteTo(ctx.Response()).WriteTo(ctx.Response())
		return
	}

	ctx.Response().WriteHeader(http.StatusOK)
}

// health 执行健康检查
func (proxyAgent *ProxyAgent) health(ctx *server.Context) {
	eniMacAddr := ctx.Request().QueryParameter(api.QueryENIMACAddr)
	if eniMacAddr == "" {
		ctx.WithErrorLog(errors.New("eniMacAddr is empty")).WriteTo(ctx.Response())
		return
	}

	if err := proxyAgent.NetWorkMgr.HealthCheck(eniMacAddr); err != nil {
		ctx.WithErrorLog(kunErr.NewServiceException(err.Error(), nil)).WriteTo(ctx.Response())
		return
	}

	ctx.Response().WriteHeader(http.StatusOK)
}

func (proxyAgent *ProxyAgent) addTunnelPeer(ctx *server.Context) {
	peerNodeIP := ctx.Request().QueryParameter(api.QueryPeerNodeIP)
	peerTunnelIP := ctx.Request().QueryParameter(api.QueryPeerTunnelIP)
	if peerNodeIP == "" || peerTunnelIP == "" {
		ctx.WithErrorLog(errors.New("peer node ip or tunnel ip is empty")).WriteTo(ctx.Response())
		return
	}

	if err := proxyAgent.NetWorkMgr.AddTunnelPeer(peerNodeIP, peerTunnelIP); err != nil {
		ctx.WithErrorLog(kunErr.NewServiceException(err.Error(), nil)).WriteTo(ctx.Response())
		return
	}

	ctx.Response().WriteHeader(http.StatusOK)
}

func (proxyAgent *ProxyAgent) removeTunnelPeer(ctx *server.Context) {
	peerNodeIP := ctx.Request().QueryParameter(api.QueryPeerNodeIP)
	peerTunnelIP := ctx.Request().QueryParameter(api.QueryPeerTunnelIP)
	if peerNodeIP == "" || peerTunnelIP == "" {
		ctx.WithErrorLog(errors.New("peer node ip or tunnel ip is empty")).WriteTo(ctx.Response())
		return
	}

	if err := proxyAgent.NetWorkMgr.RemoveTunnelPeer(peerNodeIP, peerTunnelIP); err != nil {
		ctx.WithErrorLog(kunErr.NewServiceException(err.Error(), nil)).WriteTo(ctx.Response())
		return
	}

	ctx.Response().WriteHeader(http.StatusOK)
}

func (proxyAgent *ProxyAgent) hello(ctx *server.Context) {
	ctx.Response().WriteHeader(http.StatusOK)
}
