package app

import (
	"icode.baidu.com/baidu/faas/kun/cmd/proxyagent/app/options"
	"icode.baidu.com/baidu/faas/kun/pkg/proxyagent/network"
	genericserver "icode.baidu.com/baidu/faas/kun/pkg/server"
	"icode.baidu.com/baidu/faas/kun/pkg/util/logs"
	"icode.baidu.com/baidu/faas/kun/pkg/version"
)

type ProxyAgent struct {
	Options *options.ProxyAgentOptions
	NetWorkMgr network.Interface
}

func NewProxyAgent(s *options.ProxyAgentOptions) (*ProxyAgent, error) {
	nm, err := network.NewManager(s.BasePath, s.TunnelCidr)
	if err != nil {
		return nil, err
	}

	return &ProxyAgent{
		Options:  s,
		NetWorkMgr: nm,
	}, nil
}

func Run(opt *options.ProxyAgentOptions, stopCh <-chan struct{}) error {
	logs.Infof("Version: %+v", version.Get())

	proxyAgent, err := NewProxyAgent(opt)
	if err != nil {
		logs.Fatalf("Error: %v", err)
	}

	server, err := CreateServerChain(proxyAgent, opt)
	if err != nil {
		return err
	}
	return server.PrepareRun().Run(stopCh)
}

func CreateServerChain(server *ProxyAgent, opt *options.ProxyAgentOptions) (*genericserver.GenericServer, error) {
	config := genericserver.NewRecommendedConfig()
	if err := opt.RecommendedOptions.ApplyTo(config); err != nil {
		return nil, err
	}
	s, err := config.Complete().New(opt.ApplicationName)
	if err != nil {
		return nil, err
	}
	server.InstallAPI(s.Handler.GoRestfulContainer)
	return s, nil
}