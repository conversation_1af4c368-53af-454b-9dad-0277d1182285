package app

import (
	"github.com/emicklei/go-restful"
	"icode.baidu.com/baidu/faas/kun/pkg/server"
	"icode.baidu.com/baidu/faas/kun/pkg/server/endpoint"
)

func (proxyAgent *ProxyAgent) InstallAPI(container *restful.Container) {
	var apis = []endpoint.ApiSingle{
		// {
		// 	Verb:    "POST",
		// 	Path:    "init",
		// 	Handler: server.WrapRestRouteFunc(proxyAgent.init),
		// },
		// {
		// 	Verb:    "POST",
		// 	Path:    "bindvip",
		// 	Handler: genericserver.WrapRestRouteFunc(proxyAgent.getContainerList),
		// },
		{
			Verb:    "POST",
			Path:    "occupy",
			Handler: server.WrapRestRouteFunc(proxyAgent.notifyOccupy),
		},
		{
			Verb:    "POST",
			Path:    "release",
			Handler: server.WrapRestRouteFunc(proxyAgent.notifyRelease),
		},
		{
			Verb:    "POST",
			Path:    "health",
			Handler: server.WrapRestRouteFunc(proxyAgent.health),
		},
		{
			Verb:    "POST",
			Path:    "addPeer",
			Handler: server.WrapRestRouteFunc(proxyAgent.addTunnelPeer),
		},
		{
			Verb:    "POST",
			Path:    "removePeer",
			Handler: server.WrapRestRouteFunc(proxyAgent.removeTunnelPeer),
		},
		{
			Verb:    "GET",
			Path:    "hello",
			Handler: server.WrapRestRouteFunc(proxyAgent.hello),
		},
	}

	var apiversion = []endpoint.ApiVersion{
		{
			Prefix: "/v1/proxyagent",
			Group:  apis,
		},
	}
	endpoint.NewApiInstaller(apiversion).Install(container)
}
