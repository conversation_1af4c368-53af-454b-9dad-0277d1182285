package app

import (
	"github.com/emicklei/go-restful"

	"icode.baidu.com/baidu/faas/kun/cmd/omg/handles"
	"icode.baidu.com/baidu/faas/kun/cmd/omg/oclient"
	"icode.baidu.com/baidu/faas/kun/cmd/omg/options"
	"icode.baidu.com/baidu/faas/kun/pkg/server"
	"icode.baidu.com/baidu/faas/kun/pkg/server/endpoint"
	"icode.baidu.com/baidu/faas/kun/pkg/util/logs"
	"icode.baidu.com/baidu/faas/kun/pkg/version"
)

// Run runs the specified EventhubServer with the given Dependencies.
// This should never exit.
func Run(runOptions *options.OmgOptions, stopCh <-chan struct{}) error {
	logs.Infof("Version: %+v", version.Get())

	s, err := CreateServerChain(runOptions, stopCh)
	if err != nil {
		return err
	}

	return s.PrepareRun().Run(stopCh)
}

// CreateServerChain creates the apiservers connected via delegation.
func CreateServerChain(runOptions *options.OmgOptions, stopCh <-chan struct{}) (*server.GenericServer, error) {
	if err := Init(runOptions); err != nil {
		return nil, err
	}

	config := server.NewRecommendedConfig()
	if err := runOptions.RecommendedOptions.ApplyTo(config); err != nil {
		return nil, err
	}

	s, err := config.Complete().New(options.ApplicationName)
	if err != nil {
		return nil, err
	}

	// Install API
	InstallAPI(s.Handler.GoRestfulContainer)

	return s, nil
}

// Init xxx
func Init(runOptions *options.OmgOptions) error {
	oclient.NewOmgClients(runOptions)
	oclient.NewOmgConfig(runOptions)
	return nil
}

// InstallAPI xxx
func InstallAPI(container *restful.Container) {
	privateAPIs := handles.InsideAPIs()
	privateAPIs = append(privateAPIs, handles.BlueprintInsideAPIs()...)
	privateAPIs = append(privateAPIs, handles.TesteventInsideAPIs()...)
	privateAPIs = append(privateAPIs, handles.LayersInsideAPIs()...)
	privateAPIs = append(privateAPIs, handles.SecureContainerInsideAPIs()...)
	privateAPIs = append(privateAPIs, handles.HttpTriggerInsideAPIs()...)

	installer := endpoint.NewApiInstaller([]endpoint.ApiVersion{
		{
			Prefix: "/inside-v1",
			Group:  privateAPIs,
		},
	})

	installer.Install(container)
}
