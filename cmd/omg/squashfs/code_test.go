package squashfs

import (
	"testing"

	"github.com/stretchr/testify/assert"

	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/code"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/dao"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/global"
)

func TestFaasUploadCodeNew(t *testing.T) {
	code.MockCode()
	global.MockAC()
	f := new(dao.Function)
	f.CodeSha256 = "test_codesha256"
	f.FunctionName = "test"
	f.Uid = "uiduid"
	_, _, err := FaasUploadCodeSquashFs(*f, code.MockBytes)
	assert.NotNil(t, err)
}

func TestFaasUploadSquashFs(t *testing.T) {
	code.MockCode()
	f := new(dao.Function)
	f.CodeID = "test_codeId"
	f.FunctionName = "test"
	f.Uid = "uiduid"
	squashfsSha256, _ := FaasUploadSquashFs(*f, code.MockBytes)
	assert.NotEqual(t, nil, squashfsSha256)
}

func TestCheckSquashfsSha256(t *testing.T) {
	code.MockCode()
	f := new(dao.Function)
	f.CodeID = "test_codeId"
	f.FunctionName = "test"
	f.Uid = "uiduid"

	_, _, err := CheckSquashfsSha256(*f)
	assert.Nil(t, err)
}
