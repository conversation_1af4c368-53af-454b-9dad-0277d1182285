package squashfs

import (
	"fmt"
	"log"
	"os"
	"strconv"

	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/squashfs"

	"github.com/spf13/pflag"

	"icode.baidu.com/baidu/faas/kun/cmd/omg/app"
	"icode.baidu.com/baidu/faas/kun/cmd/omg/options"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/code"
	"icode.baidu.com/baidu/faas/kun/pkg/util/flag"
	"icode.baidu.com/baidu/faas/kun/pkg/version/verflag"
)

/**
生成bin文件时，需要将该文件放在omg根目录下
线上执行bin文件的流程
1、本地修改相应的配置信息，主要是dbconfig和codeconfig
2、将squashfs_main文件放在omg根目录下，交叉编译成bin文件squashfs_main
3、将生成的bin文件上传到线上机器上faas/omg目录下
4、同时需要将apiserver-bin项目中的squashfs-tools下的mksquashfs bin文件拷贝到线上机器faas/omg目录下
5、执行脚本 ./squashfs_main check 0 1>>squashfs.out 2>>squashfs.err, 其中0为参数，check为operateType, squashfs.out保存正常标准输出
   squashfs.err 保存错误信息输出

*/

const (
	MaxInt64    = 1<<63 - 1
	ConvertType = "convert"
	CheckType   = "check"
)

func main() {
	var (
		startId     int64
		endId       = int64(MaxInt64)
		operateType string
	)
	s := options.NewOmgOptions()
	s.AddFlags(pflag.CommandLine)

	flag.InitFlags()

	verflag.PrintAndExitIfRequested()

	code.NewCodeManager(s.CodeConfiguration, squashfs.NewSquashFsClient(squashfs.NewSquashFsOptions()))
	if err := app.Init(s); err != nil {
		fmt.Fprintf(os.Stderr, "%v\n", err)
		os.Exit(1)
	}

	operateType = os.Args[1]

	startIdStr := os.Args[2]
	startId, err := strconv.ParseInt(startIdStr, 10, 64)
	if err != nil {
		log.Print("params invalid\n")
		os.Exit(2)
	}
	if len(os.Args) > 3 {
		endIdStr := os.Args[3]
		endId, err = strconv.ParseInt(endIdStr, 10, 64)
		if err != nil {
			log.Print("params invalid\n")
			os.Exit(3)
		}
	}

	switch operateType {
	case ConvertType:
		// 函数zip制作squashfs镜像
		ConvertCodeToSquashfs(startId, endId)
	case CheckType:
		// 校验squashfs_sha256
		CheckSquashfs(startId, endId)
	default:
		log.Print("params invalid")
	}
}
