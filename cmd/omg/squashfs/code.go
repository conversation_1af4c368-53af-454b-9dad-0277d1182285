package squashfs

import (
	"crypto/sha256"
	"errors"
	"fmt"
	"io/ioutil"
	"net/http"
	"os"
	"os/exec"
	"path/filepath"

	"github.com/google/uuid"

	"icode.baidu.com/baidu/faas/kun/cmd/omg/oclient"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/dao"
	"icode.baidu.com/baidu/faas/kun/pkg/util/base64"
	"icode.baidu.com/baidu/faas/kun/pkg/util/zip"
)

func generateCodeMeta(byteArray []byte) (meta map[string]interface{}) {
	meta = make(map[string]interface{})
	hasher := sha256.New()
	hasher.Write(byteArray)
	shaByte := hasher.Sum(nil)
	meta["CodeSha256"] = base64.EncodeToString(shaByte)
	meta["CodeSize"] = int64(len(byteArray))
	meta["x_sha256"] = fmt.Sprintf("%x", shaByte)
	return meta
}

// 数据库及bos文件准备完成后, 改用 new key
func getOldKey(uid, functionName, x_sha256 string) string {
	return fmt.Sprintf("%s/%s_%s.zip", uid, functionName, x_sha256)
}

func getNewKey(uid, functionName, codeID string) string {
	return fmt.Sprintf("%s/%s_%s.zip", uid, functionName, codeID)
}

func getSquashfsKey(uid, functionName, codeID string) string {
	return fmt.Sprintf("%s/%s_%s.sqfs", uid, functionName, codeID)
}

// 以下代码用于将遗留bos代码转化为squashfs镜像
// 上传函数代码的squashfs镜像到bos, CodeId为空时
func FaasUploadCodeSquashFs(f dao.Function, byteArray []byte) (squashFsSha256 *string, codeID string, err error) {
	meta := generateCodeMeta(byteArray)
	x_sha256 := meta["x_sha256"].(string)
	codeID = uuid.New().String()
	oldObjectKey := getOldKey(f.Uid, f.FunctionName, x_sha256)
	newObjectKey := getNewKey(f.Uid, f.FunctionName, codeID)
	if err = oclient.Oc.Bos.BasicCopyObject(newObjectKey, oldObjectKey); err != nil {
		return
	}
	var squashFsSha256Str string
	squashFsKey := getSquashfsKey(f.Uid, f.FunctionName, codeID)
	squashFsSha256Str, err = faasUploadCodeSqfs(byteArray, f.CodeSize, squashFsKey, "apiserver_squashfs")
	if err != nil {
		return
	}

	squashFsSha256 = &squashFsSha256Str
	return
}

// 上传函数代码的squashfs镜像到bos, CodeId不为空时
func FaasUploadSquashFs(f dao.Function, byteArray []byte) (squashFsSha256 *string, err error) {
	var squashFsSha256Str string
	squashFsKey := getSquashfsKey(f.Uid, f.FunctionName, f.CodeID)
	squashFsSha256Str, err = faasUploadCodeSqfs(byteArray, f.CodeSize, squashFsKey, "apiserver_squashfs")
	if err != nil {
		return
	}
	squashFsSha256 = &squashFsSha256Str
	return
}

// 用于遗留bos代码转squashfs镜像
func faasUploadCodeSqfs(byteArray []byte, codeSize int32, squashFsKey, tmpDirPrefix string) (codeSha256 string, err error) {
	// 生成临时目录
	tmpDir, err := ioutil.TempDir("", tmpDirPrefix)
	if err != nil {
		return
	}
	// 删除目录
	defer os.RemoveAll(tmpDir)
	targetDir := filepath.Join(tmpDir, "squashfs_dir")
	targetFileName := filepath.Join(tmpDir, "target.sqfs")
	// 1. 加压文件到目录
	err = zip.UnzipFromBytes(targetDir, byteArray, int64(codeSize))
	if err != nil {
		return
	}
	// 2.mksquashfs 需要 squashfs-tools
	err = mkSquashFs(targetDir, targetFileName)
	if err != nil {
		return
	}
	b, err := ioutil.ReadFile(targetFileName)
	if err != nil {
		panic(err)
	}
	hasher := sha256.New()
	hasher.Write(b)
	shaByte := hasher.Sum(nil)
	codeSha256 = base64.EncodeToString(shaByte)
	// 3. 上传镜像到bos
	if _, err = oclient.Oc.Bos.Upload(b, squashFsKey); err != nil {
		return
	}
	return
}

// 用于遗留bos代码转squashfs镜像
func mkSquashFs(sourceDir, targetFile string) error {
	cmdPath := "./mksquashfs"
	mkSquashFsOptions := []string{"-comp", "gzip", "-Xcompression-level", "1", "-noI", "-noD", "-noF", "-no-sparse", "-no-fragments"}
	arg := []string{sourceDir, targetFile}
	arg = append(arg, mkSquashFsOptions...)
	c := exec.Command(cmdPath, arg...)
	c.Env = []string{"PATH=/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin"}
	msg, err := c.CombinedOutput()
	if err != nil {
		return errors.New(fmt.Sprintf("%s %v", msg, err))
	}
	return nil
}

func CheckSquashfsSha256(f dao.Function) (string, bool, error) {
	// 生成SquashFsUrl
	url, err := oclient.Oc.Bos.FaasSquashFsCodeDownloadUrl(f.FunctionName, f.Uid, f.CodeID)
	if err != nil {
		return "", false, err
	}

	// 根据url获取squashfs镜像
	resp, err := http.Get(url)
	if err != nil {
		return "", false, err
	}

	defer resp.Body.Close()

	b, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return "", false, err
	}
	// 计算squashfs_sha256
	hasher := sha256.New()
	hasher.Write(b)
	shaByte := hasher.Sum(nil)
	squashfsSha256 := base64.EncodeToString(shaByte)

	res := f.SquashFsSha256 != nil && *f.SquashFsSha256 == squashfsSha256

	return squashfsSha256, res, nil
}
