package squashfs

import (
	"fmt"
	"log"
	"time"

	"icode.baidu.com/baidu/faas/kun/cmd/omg/oclient"

	"github.com/jinzhu/gorm"

	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/dao"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/dbengine"
)

var (
	Size = 100
)

// 批量获取函数
func GetFunctions(startId, endId int64) ([]dao.Function, error) {
	var (
		db            *gorm.DB
		functionSlice = make([]dao.Function, 0)
		err           error
	)

	db = dbengine.DBInstance().Model(dao.Function{}).Where("id > ? AND id <= ?", startId, endId)
	db = db.Unscoped().Scopes(dao.ScopeRudDeletedAt)
	db = db.Order(fmt.Sprintf("%s %s", "id", "asc"))
	db = db.Limit(Size)

	err = db.Find(&functionSlice).Error
	if err != nil {
		log.Print(fmt.Sprintf("list functions fail, startId: %d, endId: %d, err: %v\n", startId, endId, err))
	}

	return functionSlice, err
}

// 更新函数信息
func UpdateFunction(condFunc dao.Function, updateFunction dao.Function) error {
	var updateSql string
	db := dbengine.DBInstance()
	if updateFunction.CodeID != "" {
		updateSql = "UPDATE `functions` set `squashfs_sha256`=?, `code_id`=?, `updated_at`=? WHERE (`id`=? AND `code_sha256`=?)"
		db.Exec(updateSql, *updateFunction.SquashFsSha256, updateFunction.CodeID, updateFunction.UpdatedAt, condFunc.Id, condFunc.CodeSha256)
	} else {
		updateSql = "UPDATE `functions` set `squashfs_sha256`=?, `updated_at`=? WHERE (`id`=? AND `code_sha256`=?)"
		db.Exec(updateSql, *updateFunction.SquashFsSha256, updateFunction.UpdatedAt, condFunc.Id, condFunc.CodeSha256)
	}

	if err := db.Error; err != nil {
		log.Print(fmt.Sprintf("update function fail, ID: %d, err: %v\n", condFunc.Id, err))
		return err
	}

	fmt.Printf("update function success, ID: %d\n", condFunc.Id)
	return nil
}

// 将遗留bos代码转换成squashfs镜像
func ConvertCodeToSquashfs(startID, endID int64) {
	for {
		var (
			functions = make([]dao.Function, 0)
			err       error
		)
		// 从DB中获取函数列表
		functions, err = GetFunctions(startID, endID)
		if err != nil {
			fmt.Println(err)
			continue
		} else {
			if len(functions) > 0 {
				// 循环遍历 查看squashfs_sha256是否为空
				for _, f := range functions {
					var (
						bytes                        = make([]byte, 0)
						codeId                       string
						squashFsSha256               *string
						updateFunction, condFunction dao.Function
					)
					startID = int64(f.Id)
					if *f.SquashFsSha256 != "" {
						continue
					}

					startTime := time.Now()
					// 从bos上下载代码
					bytes, err = oclient.Oc.Bos.FaasGetCodeObject(f.FunctionName, f.Uid, f.CodeSha256, f.CodeID)
					if err != nil {
						log.Print(fmt.Sprintf("get code object from bos fail, ID: %d, brn: %s, err: %v\n", f.Id, f.FunctionBrn, err))
						continue
					}

					fmt.Printf("get code object from bos success, ID: %d, brn: %s\n", f.Id, f.FunctionBrn)

					// 上传squashfs镜像到bos, 两种情况
					if f.CodeID == "" {
						// CodeId为空时，需要重新将用户函数代码上传到bos,生成新的code_sha256, code_id, squashfs_sha256, code_size, 然后更新db
						squashFsSha256, codeId, err = FaasUploadCodeSquashFs(f, bytes)
						if err != nil {
							log.Print(fmt.Sprintf("update code squashfs (copy code objectKey) fail, ID: %d, brn: %s, err: %v\n", f.Id, f.FunctionBrn, err))
							continue
						}
						fmt.Printf("update code to bos success (copy code objectKey), ID: %d, brn: %s\n", f.Id, f.FunctionBrn)

						// 更新db中的code_sha256, code_id, squashfs_sha256, code_size
						condFunction.Id = f.Id
						condFunction.CodeSha256 = f.CodeSha256

						updateFunction.SquashFsSha256 = squashFsSha256
						updateFunction.CodeID = codeId
						updateFunction.UpdatedAt = f.UpdatedAt
					} else {
						// CodeId不为空，只需要上传squashfs镜像到bos，生成squashfs_sha256，然后更新db
						squashFsSha256, err = FaasUploadSquashFs(f, bytes)
						if err != nil {
							log.Print(fmt.Sprintf("upload squashfs fail, ID:%d, err:%v\n", f.Id, err))
							continue
						}
						fmt.Printf("update squashfs success, ID: %d, Brn:%v\n", f.Id, f.FunctionBrn)

						// 更新db中的squashfs_sha256
						condFunction.Id = f.Id
						condFunction.CodeSha256 = f.CodeSha256

						updateFunction.SquashFsSha256 = squashFsSha256
						updateFunction.UpdatedAt = f.UpdatedAt
					}

					// 更新db中函数信息
					err = UpdateFunction(condFunction, updateFunction)
					if err != nil {
						continue
					}

					fmt.Printf("cost time : %v\n", time.Since(startTime))
				}

				//// 测试
				//break

			} else {
				break
			}
		}

	}
	return
}

// 校验squashfs_sha256
func CheckSquashfs(startID, endID int64) {
	for {
		var (
			functions      = make([]dao.Function, 0)
			squashfsSha256 string
			err            error
		)
		// 从DB中获取函数列表
		functions, err = GetFunctions(startID, endID)
		if err != nil {
			fmt.Println(err)
			continue
		} else {
			if len(functions) > 0 {
				// 循环遍历 查看squashfs_sha256是否为空
				for _, f := range functions {
					var isEqual bool

					startID = int64(f.Id)
					if *f.SquashFsSha256 == "" {
						continue
					}

					startTime := time.Now()
					squashfsSha256, isEqual, err = CheckSquashfsSha256(f)
					if err != nil || !isEqual {
						log.Print(fmt.Sprintf("sqfs checksum failed, ID: %d, brn: %s, f.SquashfsSha256: %s, expected squashfs_sha256: %s, err: %v\n", f.Id, f.FunctionBrn, *f.SquashFsSha256, squashfsSha256, err))
						continue
					}

					fmt.Printf("sqfs checksum success, ID: %d, brn: %s\n", f.Id, f.FunctionBrn)

					fmt.Printf("cost time : %v\n", time.Since(startTime))
				}

				//// 测试
				//break

			} else {
				break
			}
		}

	}
	return
}
