package squashfs

import (
	"testing"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/stretchr/testify/assert"

	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/code"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/dao"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/dbengine"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/global"
)

func TestGetFunctions(t *testing.T) {
	var m sqlmock.Sqlmock
	m, dbengine.Engine = global.MockDB()
	m.ExpectQuery(".*").WillReturnRows(global.GetRowsFunctions(global.GetTestFunc(1)))

	_, err := GetFunctions(0, 1)
	assert.Equal(t, nil, err)
}

func TestUpdateFunction(t *testing.T) {
	var (
		m                    sqlmock.Sqlmock
		condFunc, updateFunc dao.Function
	)
	condFunc.Id = 1
	squashfsSha256 := "test"
	updateFunc.SquashFsSha256 = &squashfsSha256
	m, dbengine.Engine = global.MockDB()
	m.ExpectExec(".*").WillReturnResult(sqlmock.NewResult(1, 1))

	err := UpdateFunction(condFunc, updateFunc)
	assert.Equal(t, nil, err)
}

func TestConvertCodeToSquashfs(t *testing.T) {
	code.MockCode()

	var m sqlmock.Sqlmock
	m, dbengine.Engine = global.MockDB()
	m.ExpectQuery(".*").WillReturnRows(global.GetRowsFunctions(global.GetTestFunc(0)))
	ConvertCodeToSquashfs(0, 1)
}

func TestCheckSquashfs(t *testing.T) {
	code.MockCode()

	var m sqlmock.Sqlmock
	m, dbengine.Engine = global.MockDB()
	m.ExpectQuery(".*").WillReturnRows(global.GetRowsFunctions(global.GetTestFunc(0)))
	CheckSquashfs(0, 1)
}
