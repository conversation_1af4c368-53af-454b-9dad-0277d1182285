package handles

import (
	"net/http"

	"icode.baidu.com/baidu/faas/kun/cmd/omg/oclient"
	"icode.baidu.com/baidu/faas/kun/pkg/api"
	"icode.baidu.com/baidu/faas/kun/pkg/server"
)

type SecureContainerKey struct {
	Key    string
	Status string
}

func Create(c *server.Context) {
	request := c.Request().Request
	request.ParseForm()

	key := request.FormValue("Key")
	status := request.FormValue("Status")

	if err := oclient.Oc.Redis.SetKey(key, status); err != nil {
		c.WithWarnLog(err).WriteTo(c.Response())
		return
	}
	c.Response().WriteHeaderAndEntity(http.StatusOK, AmisSuccMsg("success"))
}

func ListSecureContainerKey(c *server.Context) {
	request := c.Request().Request
	request.ParseForm()

	sc := &SecureContainerKey{
		Key: api.EnableSecureContainer,
	}

	var (
		res string
		err error
	)
	if res, err = oclient.Oc.Redis.GetKey(sc.Key); err != nil {
		c.WithWarnLog(err).WriteTo(c.Response())
		return
	}

	sc.Status = res

	amis := new(AmisRep)
	amis.Status = 0

	repMap := make(map[string]interface{})
	formatRes := make([]interface{}, 0)
	formatRes = append(formatRes, sc)
	repMap["rows"] = formatRes
	amis.Data = repMap
	c.Response().WriteHeaderAndEntity(http.StatusOK, amis)
}

func UpdateSecureContainerKey(c *server.Context) {
	request := c.Request().Request
	request.ParseForm()

	scKey := request.FormValue("Key")
	status := request.FormValue("Status")

	if err := oclient.Oc.Redis.SetKey(scKey, status); err != nil {
		c.WithWarnLog(err).WriteTo(c.Response())
		return
	}
	c.Response().WriteHeaderAndEntity(http.StatusOK, AmisSuccMsg("success"))
}
