package handles

import (
	"testing"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/stretchr/testify/assert"

	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/code"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/dao"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/dbengine"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/global"
	"icode.baidu.com/baidu/faas/kun/pkg/server"
)

func TestListVersions(t *testing.T) {
	global.MockAC()
	cases := []struct {
		in_c         *server.Context
		out_HttpCode int
	}{
		{
			in_c:         global.BuildNewKunCtx("GET", "/blueprints/versions", ``, "uiduid", map[string]string{}),
			out_HttpCode: 200,
		},
	}
	for _, tc := range cases {
		var m sqlmock.Sqlmock
		m, dbengine.Engine = global.MockDB()
		m.ExpectQuery(".*").WillReturnRows(global.GetRowsBlueprints(global.GetTestBlueprint(1)))

		ListVersions(tc.in_c)
		assert.Equal(t, tc.out_HttpCode, tc.in_c.Response().StatusCode())
	}
}

func TestListBlueprints(t *testing.T) {
	global.MockAC()
	cases := []struct {
		in_c         *server.Context
		out_HttpCode int
	}{
		{
			in_c:         global.BuildNewKunCtx("GET", "/blueprints", ``, "uiduid", map[string]string{}),
			out_HttpCode: 200,
		},
		{
			in_c:         global.BuildNewKunCtx("GET", "/blueprints?page=1&searchWord=python", ``, "uiduid", map[string]string{}),
			out_HttpCode: 200,
		},
	}
	for _, tc := range cases {
		var m sqlmock.Sqlmock
		m, dbengine.Engine = global.MockDB()
		m.ExpectQuery(".*").WillReturnRows(global.GetRowsBlueprints(global.GetTestBlueprint(1)))

		ListBlueprints(tc.in_c)
		assert.Equal(t, tc.out_HttpCode, tc.in_c.Response().StatusCode())
	}
}

func TestCreateBlueprint(t *testing.T) {
	global.MockAC()
	code.MockCode()
	cases := []struct {
		in_c         *server.Context
		out_HttpCode int
	}{
		{
			in_c:         global.BuildNewKunCtx("POST", "/blueprints?SyncBlueprint=true&CodeUrl=http://baidu.bos.com&BrandNewBlueprint=true&Name=blue1&Runtime=python2.7&Handler=index.handler&NewZipFileKey=abcde&Layers=a,b,c,d,e,f", ``, "uiduid", map[string]string{}),
			out_HttpCode: 500,
		},
		{
			in_c:         global.BuildNewKunCtx("POST", "/blueprints?SyncBlueprint=true&CodeUrl=http://baidu.bos.com&BrandNewBlueprint=true&Name=blue1&Runtime=python2.7&Handler=index.handler&NewZipFileKey=abcde", ``, "uiduid", map[string]string{}),
			out_HttpCode: 201,
		},
	}
	for _, tc := range cases {
		var m sqlmock.Sqlmock
		m, dbengine.Engine = global.MockDB()
		m.ExpectExec(".*").WillReturnResult(sqlmock.NewResult(1, 1))
		CreateBlueprint(tc.in_c)
		assert.Equal(t, tc.out_HttpCode, tc.in_c.Response().StatusCode())
	}
}

func TestGenerateCodeUrl(t *testing.T) {
	global.MockAC()
	code.MockCode()
	cases := []struct {
		in_c         *server.Context
		out_HttpCode int
	}{
		{
			in_c:         global.BuildNewKunCtx("POST", "/blueprints?NewZipFileKey=abcde", ``, "uiduid", map[string]string{}),
			out_HttpCode: 200,
		},
	}
	for _, tc := range cases {
		var m sqlmock.Sqlmock
		m, dbengine.Engine = global.MockDB()
		m.ExpectExec(".*").WillReturnResult(sqlmock.NewResult(1, 1))
		GenerateCodeUrl(tc.in_c)
		assert.Equal(t, tc.out_HttpCode, tc.in_c.Response().StatusCode())
	}
}

func TestUpdateBlueprint(t *testing.T) {
	global.MockAC()
	code.MockCode()
	cases := []struct {
		in_c         *server.Context
		out_HttpCode int
	}{
		{
			in_c:         global.BuildNewKunCtx("PUT", "/blueprints?UUID=uuid&Name=blue1&Runtime=python2.7&Handler=index.handler&NewZipFileKey=abcde", ``, "uiduid", map[string]string{}),
			out_HttpCode: 201,
		},
		{
			in_c:         global.BuildNewKunCtx("PUT", "/blueprints?UUID=uuid&Name=blue1&Runtime=python2.7&Handler=index.handler&NewZipFileKey=abcde&Layers=a,b,c,d,e,f", ``, "uiduid", map[string]string{}),
			out_HttpCode: 500,
		},
	}
	for _, tc := range cases {
		var m sqlmock.Sqlmock
		m, dbengine.Engine = global.MockDB()
		m.ExpectExec(".*").WillReturnResult(sqlmock.NewResult(1, 1))
		UpdateBlueprint(tc.in_c)
		assert.Equal(t, tc.out_HttpCode, tc.in_c.Response().StatusCode())
	}
}

func TestDeleteBlueprint(t *testing.T) {
	global.MockAC()
	code.MockCode()
	cases := []struct {
		in_c         *server.Context
		out_HttpCode int
	}{
		{
			in_c:         global.BuildNewKunCtx("DELETE", "/blueprints?UUID=uuid&BosObjKey=abcde", ``, "uiduid", map[string]string{}),
			out_HttpCode: 200,
		},
	}
	for _, tc := range cases {
		var m sqlmock.Sqlmock
		m, dbengine.Engine = global.MockDB()
		m.ExpectExec("^DELETE (.+)").WillReturnResult(sqlmock.NewResult(1, 1))
		DeleteBlueprint(tc.in_c)
		assert.Equal(t, tc.out_HttpCode, tc.in_c.Response().StatusCode())
	}
}

func TestChangeStatus(t *testing.T) {
	global.MockAC()
	code.MockCode()
	cases := []struct {
		in_c         *server.Context
		out_HttpCode int
	}{
		{
			in_c:         global.BuildNewKunCtx("PUT", "/blueprints/deploy?Name=blue&UUID=uuid&ActionType=online", ``, "uiduid", map[string]string{}),
			out_HttpCode: 200,
		},
	}
	for _, tc := range cases {
		var m sqlmock.Sqlmock
		m, dbengine.Engine = global.MockDB()
		m.ExpectExec("^UPDATE (.+)").WillReturnResult(sqlmock.NewResult(1, 1))
		m.ExpectExec("^UPDATE (.+)").WillReturnResult(sqlmock.NewResult(1, 1))
		ChangeStatus(tc.in_c)
		assert.Equal(t, tc.out_HttpCode, tc.in_c.Response().StatusCode())
	}
}

func TestDownloadBlueprintCode(t *testing.T) {
	code.MockCode()
	codeUrl := "http://baidu.bos.com"
	_, err := DownloadBlueprintCode(codeUrl)
	assert.Equal(t, nil, err)
}

func TestDealBlueprint(t *testing.T) {
	bps := make([]interface{}, 0)
	des := "test"
	bp := &dao.Blueprint{
		Id:          1,
		Uuid:        "uuid",
		Name:        "test",
		Description: &des,
		KeywordsStr: "[test]",
		LayersStr:   "[test-layer]",
	}
	bp.Layers = []string{"test-layer"}
	bps = append(bps, *bp)
	res := dealBlueprint(bps)

	assert.Equal(t, true, len(res) > 0)
}
