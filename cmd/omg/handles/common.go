package handles

import (
	"github.com/gogo/protobuf/proto"
	"icode.baidu.com/baidu/ksarch/gobns/bns"
)

func returnSuccessLayout(i interface{}) map[string]interface{} {

	return map[string]interface{}{
		"msg":    "ok",
		"status": 0,
		"data":   i,
	}
}

func getInstanceInfos(service string) ([]*bns.InstanceInfo, error) {
	c := bns.New()
	var reply bns.LocalNamingResponse

	err := c.Call(&bns.LocalNamingRequest{
		ServiceName: proto.String(service),
	}, &reply)

	if err != nil {
		return nil, err
	}
	return reply.InstanceInfo, nil
}
