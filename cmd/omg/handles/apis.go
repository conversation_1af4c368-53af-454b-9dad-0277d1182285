package handles

import (
	"errors"

	"github.com/emicklei/go-restful"

	"icode.baidu.com/baidu/faas/kun/cmd/omg/oclient"
	"icode.baidu.com/baidu/faas/kun/pkg/server"
	"icode.baidu.com/baidu/faas/kun/pkg/server/endpoint"
)

func requestAuthCheck(c *server.Context, chain *restful.FilterChain) {
	aToken := c.Request().HeaderParameter("AMIS_TOKEN")
	aUser := c.Request().HeaderParameter("AMIS_USER")
	aRoles := c.Request().HeaderParameter("AMIS_ROLES")
	aPerms := c.Request().HeaderParameter("AMIS_PERMS")
	authRes := oclient.Oc.Auth.Check(aToken)
	c.Logger().Debugf("[user:%s][token:%s][roles:%s][perms:%s][authRes:%v]", aUser, aToken, aRoles, aPerms, authRes)
	if authRes != true {
		c.WithErrorLog(errors.New("yours token is error")).WriteTo(c.Response())
		return
	}
	chain.ProcessFilter(c.Request(), c.Response())
}

func setReqHeader(c *server.Context, chain *restful.FilterChain) {
	c.HTTPRequest().Header.Set("Content-Type", "application/json")
	chain.ProcessFilter(c.Request(), c.Response())
}

// InsideAPIs xxx
func InsideAPIs() []endpoint.ApiSingle {
	apis := []endpoint.ApiSingle{
		// get invite link, GET /inside-v1/functions/{FunctionName}
		{
			Verb:    "GET",
			Path:    "/functions",
			Handler: server.WrapRestRouteFunc(ListFunctions),
			Filters: []restful.FilterFunction{
				server.WrapRestFilterFunction(requestAuthCheck),
			},
		},
		{
			Verb:    "GET",
			Path:    "/functions/{FunctionBrn}",
			Handler: server.WrapRestRouteFunc(GetFunctionDetail),
			Filters: []restful.FilterFunction{
				server.WrapRestFilterFunction(requestAuthCheck),
			},
		},
		{
			Verb:    "GET",
			Path:    "/etcd/{key}",
			Handler: server.WrapRestRouteFunc(GetEtcdValueByKey),
			Filters: []restful.FilterFunction{
				server.WrapRestFilterFunction(requestAuthCheck),
			},
		},
		{
			Verb:    "GET",
			Path:    "/nodes",
			Handler: server.WrapRestRouteFunc(GetNodeList),
			Filters: []restful.FilterFunction{
				server.WrapRestFilterFunction(requestAuthCheck),
			},
		},
		{
			Verb:    "GET",
			Path:    "/clusters",
			Handler: server.WrapRestRouteFunc(GetClusterList),
			Filters: []restful.FilterFunction{
				server.WrapRestFilterFunction(requestAuthCheck),
			},
		},
		{
			Verb:    "GET",
			Path:    "/nodes/{ClusterID}/static",
			Handler: server.WrapRestRouteFunc(GetNodesStatisticInfo),
			Filters: []restful.FilterFunction{
				server.WrapRestFilterFunction(requestAuthCheck),
			},
		},
		{
			Verb:    "GET",
			Path:    "/nodes/{FloatingIP}/pods",
			Handler: server.WrapRestRouteFunc(GetPodSatusByNode),
			Filters: []restful.FilterFunction{
				server.WrapRestFilterFunction(requestAuthCheck),
			},
		},
		{
			Verb:    "GET",
			Path:    "/nodes/{nodeID}/reserveMemory",
			Handler: server.WrapRestRouteFunc(GetNodeReserveMemory),
			Filters: []restful.FilterFunction{
				server.WrapRestFilterFunction(requestAuthCheck),
			},
		},
		{
			Verb:    "GET",
			Path:    "/nodes/{nodeID}/podsdata",
			Handler: server.WrapRestRouteFunc(GetNodePodsData),
			Filters: []restful.FilterFunction{
				server.WrapRestFilterFunction(requestAuthCheck),
			},
		},
		{
			Verb:    "GET",
			Path:    "/nodes/{nodeID}/history",
			Handler: server.WrapRestRouteFunc(GetNodeHistoryRecords),
			Filters: []restful.FilterFunction{
				server.WrapRestFilterFunction(requestAuthCheck),
			},
		},
		{
			Verb:    "GET",
			Path:    "/memory/{ScanKey}/data",
			Handler: server.WrapRestRouteFunc(GetRedisMemoryData),
			Filters: []restful.FilterFunction{
				server.WrapRestFilterFunction(requestAuthCheck),
			},
		},
		{
			Verb:    "GET",
			Path:    "/allRedisKeys",
			Handler: server.WrapRestRouteFunc(GetAllRedisKeys),
			Filters: []restful.FilterFunction{
				server.WrapRestFilterFunction(requestAuthCheck),
			},
		},
		{
			Verb:    "GET",
			Path:    "/nodes/distribution",
			Handler: server.WrapRestRouteFunc(NodeStatusDistribute),
			Filters: []restful.FilterFunction{
				server.WrapRestFilterFunction(requestAuthCheck),
			},
		},
		{
			Verb:    "GET",
			Path:    "/nodes/distributionByCluster",
			Handler: server.WrapRestRouteFunc(NodeStatusDistributeByCluster),
			Filters: []restful.FilterFunction{
				server.WrapRestFilterFunction(requestAuthCheck),
			},
		},
		{
			Verb:    "PUT",
			Path:    "/nodes/{nodeID}/{op}",
			Handler: server.WrapRestRouteFunc(updateNode),
			Filters: []restful.FilterFunction{
				server.WrapRestFilterFunction(requestAuthCheck),
			},
		},
		{
			Verb:    "DELETE",
			Path:    "/nodes/{nodeID}",
			Handler: server.WrapRestRouteFunc(deleteNode),
			Filters: []restful.FilterFunction{
				server.WrapRestFilterFunction(requestAuthCheck),
			},
		},
		{
			Verb:    "DELETE",
			Path:    "/nodes",
			Handler: server.WrapRestRouteFunc(deleteNodes),
			Filters: []restful.FilterFunction{
				server.WrapRestFilterFunction(requestAuthCheck),
			},
		},
		{
			Verb:    "GET",
			Path:    "/nodes/user_distribution",
			Handler: server.WrapRestRouteFunc(NodeUserDistribute),
			Filters: []restful.FilterFunction{
				server.WrapRestFilterFunction(requestAuthCheck),
			},
		},
		{
			Verb:    "GET",
			Path:    "/nodes/user_distributionByCluster",
			Handler: server.WrapRestRouteFunc(NodeUserDistributeByCluster),
			Filters: []restful.FilterFunction{
				server.WrapRestFilterFunction(requestAuthCheck),
			},
		},
		{
			Verb:    "PUT",
			Path:    "/nodes/state",
			Handler: server.WrapRestRouteFunc(UpdateNodeState),
			Filters: []restful.FilterFunction{
				server.WrapRestFilterFunction(requestAuthCheck),
			},
		},
		{
			Verb:    "GET",
			Path:    "/functions/{FunctionBrn}/concurrency",
			Handler: server.WrapRestRouteFunc(GetConcurrencyDetail),
			Filters: []restful.FilterFunction{
				server.WrapRestFilterFunction(requestAuthCheck),
			},
		},
		{
			Verb:    "PUT",
			Path:    "/k8sinfo/kataImageList",
			Handler: server.WrapRestRouteFunc(updateKataRuntimeList),
			Filters: []restful.FilterFunction{
				server.WrapRestFilterFunction(requestAuthCheck),
			},
		},
		{
			Verb:    "PUT",
			Path:    "/node/kataImage/gray",
			Handler: server.WrapRestRouteFunc(grayReleaseKataRuntimeImage),
			Filters: []restful.FilterFunction{
				server.WrapRestFilterFunction(requestAuthCheck),
			},
		},
		{
			Verb:    "PUT",
			Path:    "/node/kataImage/full",
			Handler: server.WrapRestRouteFunc(fullReleaseKataRuntimeImage),
			Filters: []restful.FilterFunction{
				server.WrapRestFilterFunction(requestAuthCheck),
			},
		},
	}
	return apis
}

// BlueprintInsideAPIs xxx
func BlueprintInsideAPIs() []endpoint.ApiSingle {
	apis := []endpoint.ApiSingle{
		// list blueprints, GET /inside-v1/blueprints
		{
			Verb:    "GET",
			Path:    "/blueprints",
			Handler: server.WrapRestRouteFunc(ListBlueprints),
			Filters: []restful.FilterFunction{
				server.WrapRestFilterFunction(requestAuthCheck),
			},
		},
		// list all versions of a blueprints, GET /inside-v1/blueprints/versions
		{
			Verb:    "GET",
			Path:    "/blueprints/versions",
			Handler: server.WrapRestRouteFunc(ListVersions),
			Filters: []restful.FilterFunction{
				server.WrapRestFilterFunction(requestAuthCheck),
			},
		},
		// list code url of a blueprints, GET /inside-v1/blueprints/code
		{
			Verb:    "GET",
			Path:    "/blueprints/code",
			Handler: server.WrapRestRouteFunc(GenerateCodeUrl),
			Filters: []restful.FilterFunction{
				server.WrapRestFilterFunction(requestAuthCheck),
			},
		},
		// create blueprint, POST /inside-v1/blueprints
		{
			Verb:    "POST",
			Path:    "/blueprints",
			Handler: server.WrapRestRouteFunc(CreateBlueprint),
			Filters: []restful.FilterFunction{
				server.WrapRestFilterFunction(requestAuthCheck),
			},
		},
		// update blueprint, PUT /inside-v1/blueprints
		{
			Verb:    "PUT",
			Path:    "/blueprints",
			Handler: server.WrapRestRouteFunc(UpdateBlueprint),
			Filters: []restful.FilterFunction{
				server.WrapRestFilterFunction(requestAuthCheck),
			},
		},
		// upload zipFile to BOS, POST /inside-v1/zipfile
		{
			Verb:    "POST",
			Path:    "/blueprints/zipfile",
			Handler: server.WrapRestRouteFunc(UploadZipFile),
			Filters: []restful.FilterFunction{
				server.WrapRestFilterFunction(requestAuthCheck),
			},
		},
		// delete blueprint, PUT /inside-v1/blueprints
		// amis 后台使用delete方法请求后端，会把body丢弃，故使用put方法发送请求
		{
			Verb:    "PUT",
			Path:    "/blueprints/delete",
			Handler: server.WrapRestRouteFunc(DeleteBlueprint),
			Filters: []restful.FilterFunction{
				server.WrapRestFilterFunction(requestAuthCheck),
			},
		},
		// change blueprint's status, POST /inside-v1/blueprints/deploy
		{
			Verb:    "PUT",
			Path:    "/blueprints/deploy",
			Handler: server.WrapRestRouteFunc(ChangeStatus),
			Filters: []restful.FilterFunction{
				server.WrapRestFilterFunction(requestAuthCheck),
			},
		},
	}
	return apis
}

// TesteventInsideAPIs xxx
func TesteventInsideAPIs() []endpoint.ApiSingle {
	apis := []endpoint.ApiSingle{
		// list testevents, GET /inside-v1/testevents
		{
			Verb:    "GET",
			Path:    "/testevents",
			Handler: server.WrapRestRouteFunc(ListTestevents),
			Filters: []restful.FilterFunction{
				server.WrapRestFilterFunction(requestAuthCheck),
			},
		},
		// list a testevent, GET /inside-v1/testevents/event
		{
			Verb:    "GET",
			Path:    "/testevents/event",
			Handler: server.WrapRestRouteFunc(GetTestevent),
			Filters: []restful.FilterFunction{
				server.WrapRestFilterFunction(requestAuthCheck),
			},
		},
		// create testevent, POST /inside-v1/testevents
		{
			Verb:    "POST",
			Path:    "/testevents",
			Handler: server.WrapRestRouteFunc(CreateTestevent),
			Filters: []restful.FilterFunction{
				server.WrapRestFilterFunction(requestAuthCheck),
			},
		},
		// update testevent, PUT /inside-v1/testevents
		{
			Verb:    "PUT",
			Path:    "/testevents",
			Handler: server.WrapRestRouteFunc(UpdateTestevent),
			Filters: []restful.FilterFunction{
				server.WrapRestFilterFunction(requestAuthCheck),
			},
		},
		// delete testevent, PUT /inside-v1/testevents
		// amis 后台使用delete方法请求后端，会把body丢弃，故使用put方法发送请求
		{
			Verb:    "PUT",
			Path:    "/testevents/delete",
			Handler: server.WrapRestRouteFunc(DeleteTestevent),
			Filters: []restful.FilterFunction{
				server.WrapRestFilterFunction(requestAuthCheck),
			},
		},
		// change testevent's status, POST /inside-v1/testevents/deploy
		{
			Verb:    "PUT",
			Path:    "/testevents/deploy",
			Handler: server.WrapRestRouteFunc(ChangeTesteventStatus),
			Filters: []restful.FilterFunction{
				server.WrapRestFilterFunction(requestAuthCheck),
			},
		},
	}
	return apis
}

func LayersInsideAPIs() []endpoint.ApiSingle {
	apis := []endpoint.ApiSingle{
		{
			Verb:    "GET",
			Path:    "/layers",
			Handler: server.WrapRestRouteFunc(GetLayers),
			Filters: []restful.FilterFunction{
				server.WrapRestFilterFunction(requestAuthCheck),
			},
		},
		{
			Verb:    "PUT",
			Path:    "/layers/deploy",
			Handler: server.WrapRestRouteFunc(DeployLayer),
			Filters: []restful.FilterFunction{
				server.WrapRestFilterFunction(requestAuthCheck),
			},
		},
	}
	return apis
}

// SecureContainerInsideAPIs
func SecureContainerInsideAPIs() []endpoint.ApiSingle {
	apis := []endpoint.ApiSingle{
		// create secure container key, POST /inside-v1/securecontainer
		{
			Verb:    "POST",
			Path:    "/securecontainer",
			Handler: server.WrapRestRouteFunc(Create),
			Filters: []restful.FilterFunction{
				server.WrapRestFilterFunction(requestAuthCheck),
			},
		},
		// list secure container key, GET /inside-v1/securecontainer
		{
			Verb:    "GET",
			Path:    "/securecontainer",
			Handler: server.WrapRestRouteFunc(ListSecureContainerKey),
			Filters: []restful.FilterFunction{
				server.WrapRestFilterFunction(requestAuthCheck),
			},
		},
		// update secure container key, PUT /inside-v1/securecontainer
		{
			Verb:    "PUT",
			Path:    "/securecontainer",
			Handler: server.WrapRestRouteFunc(UpdateSecureContainerKey),
			Filters: []restful.FilterFunction{
				server.WrapRestFilterFunction(requestAuthCheck),
			},
		},
	}
	return apis
}

// HttptriggerInsideAPIs
func HttpTriggerInsideAPIs() []endpoint.ApiSingle {
	apis := []endpoint.ApiSingle{
		// get all rest apis, GET /inside-v1/httptrigger/restapi
		{
			Verb:    "GET",
			Path:    "/httptrigger/restapi",
			Handler: server.WrapRestRouteFunc(ListAllRestApi),
			Filters: []restful.FilterFunction{
				server.WrapRestFilterFunction(requestAuthCheck),
			},
		},
		// get all black list user, GET /inside-v1/httptrigger/blacklist
		{
			Verb:    "GET",
			Path:    "/httptrigger/blacklist",
			Handler: server.WrapRestRouteFunc(ListAllBlacklist),
			Filters: []restful.FilterFunction{
				server.WrapRestFilterFunction(requestAuthCheck),
			},
		},
		// get all endpoint, GET /inside-v1/httptrigger/endpoint
		{
			Verb:    "GET",
			Path:    "/httptrigger/endpoint",
			Handler: server.WrapRestRouteFunc(ListAllEndpoint),
			Filters: []restful.FilterFunction{
				server.WrapRestFilterFunction(requestAuthCheck),
			},
		},
		// get all deployment, GET /inside-v1/httptrigger/deployment
		{
			Verb:    "GET",
			Path:    "/httptrigger/deployment",
			Handler: server.WrapRestRouteFunc(ListAllDeployment),
			Filters: []restful.FilterFunction{
				server.WrapRestFilterFunction(requestAuthCheck),
			},
		},
		// get all stage, GET /inside-v1/httptrigger/stage
		{
			Verb:    "GET",
			Path:    "/httptrigger/stage",
			Handler: server.WrapRestRouteFunc(ListAllStage),
			Filters: []restful.FilterFunction{
				server.WrapRestFilterFunction(requestAuthCheck),
			},
		},
		// get all proxystorage, GET /inside-v1/httptrigger/proxystorage
		{
			Verb:    "GET",
			Path:    "/httptrigger/proxystorage",
			Handler: server.WrapRestRouteFunc(ListAllProxyStorage),
			Filters: []restful.FilterFunction{
				server.WrapRestFilterFunction(requestAuthCheck),
			},
		},
		// insert user into black list, PUT /inside-v1/httptrigger/blacklist
		{
			Verb:    "PUT",
			Path:    "/httptrigger/blacklist",
			Handler: server.WrapRestRouteFunc(InsertBlacklist),
			Filters: []restful.FilterFunction{
				server.WrapRestFilterFunction(requestAuthCheck),
			},
		},
		// delete user from black list, DELETE /inside-v1/httptrigger/blacklist
		{
			Verb:    "DELETE",
			Path:    "/httptrigger/blacklist",
			Handler: server.WrapRestRouteFunc(DeleteBlacklist),
			Filters: []restful.FilterFunction{
				server.WrapRestFilterFunction(requestAuthCheck),
			},
		},
	}

	return apis
}
