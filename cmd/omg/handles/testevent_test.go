package handles

import (
	"testing"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/stretchr/testify/assert"

	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/dbengine"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/global"
	"icode.baidu.com/baidu/faas/kun/pkg/server"
)

func TestCreateTestEvent(t *testing.T) {
	global.MockAC()
	cases := []struct {
		in_c         *server.Context
		out_HttpCode int
	}{
		{
			in_c:         global.BuildNewKunCtx("POST", "/testevents?Title=HelloWord模版2&Type=普通&Event={\"key1\":\"value1\"}", "", "123", map[string]string{}),
			out_HttpCode: 201,
		},
		{
			in_c:         global.BuildNewKunCtx("POST", "/testevents?Type=普通&Event={\"key1\":\"value1\"}", "", "123", map[string]string{}),
			out_HttpCode: 409,
		},
		{
			in_c:         global.BuildNewKunCtx("POST", "/testevents?Title=HelloWord模版2&Type=普通&Event={\"key1\":\"value1\"},", "", "123", map[string]string{}),
			out_HttpCode: 409,
		},
		{
			in_c:         global.BuildNewKunCtx("POST", "/testevents?Title=HelloWord模版2&TType=普通&Event={\"key1\":\"value1\"}", "", "123", map[string]string{}),
			out_HttpCode: 409,
		},
	}
	for _, tc := range cases {
		var m sqlmock.Sqlmock
		m, dbengine.Engine = global.MockDB()
		m.ExpectExec(".*").WillReturnResult(sqlmock.NewResult(1, 1))
		CreateTestevent(tc.in_c)
		assert.Equal(t, tc.out_HttpCode, tc.in_c.Response().StatusCode())
	}
}

func TestListTestevents(t *testing.T) {
	global.MockAC()

	cases := []struct {
		in_c         *server.Context
		out_HttpCode int
	}{
		{
			in_c:         global.BuildNewKunCtx("GET", "/testevents/", "", "123", map[string]string{}),
			out_HttpCode: 200,
		},
		{
			in_c:         global.BuildNewKunCtx("GET", "/testevents?page=1", "", "123", map[string]string{}),
			out_HttpCode: 200,
		},
		{
			in_c:         global.BuildNewKunCtx("GET", "/testevents?page=1&searchWord=已上线", "", "123", map[string]string{}),
			out_HttpCode: 200,
		},
	}
	for _, tc := range cases {
		var m sqlmock.Sqlmock
		m, dbengine.Engine = global.MockDB()
		m.ExpectQuery(".*").WillReturnRows(global.GetRowsStandardTestEvents(global.GetStandardTestEvents(1)))
		ListTestevents(tc.in_c)
		assert.Equal(t, tc.out_HttpCode, tc.in_c.Response().StatusCode())
	}
}

func TestUpdateStandardTestEvent(t *testing.T) {
	global.MockAC()

	cases := []struct {
		in_c         *server.Context
		out_HttpCode int
	}{
		{
			in_c:         global.BuildNewKunCtx("PUT", "/testevents?ID=1&Type=普通&Event={\"key1\":\"value1\"}", "", "123", map[string]string{}),
			out_HttpCode: 200,
		},
		{
			in_c:         global.BuildNewKunCtx("PUT", "/testevents?Type=普通&Event={\"key1\":\"value1\"}", "", "123", map[string]string{}),
			out_HttpCode: 500,
		},
		{
			in_c:         global.BuildNewKunCtx("PUT", "/testevents?ID=1&Event={\"key1\":\"value1\"}", "", "123", map[string]string{}),
			out_HttpCode: 409,
		},
		{
			in_c:         global.BuildNewKunCtx("PUT", "/testevents?ID=1&Event={\"key1\":\"value1\"},", "", "123", map[string]string{}),
			out_HttpCode: 409,
		},
	}
	for _, tc := range cases {
		var m sqlmock.Sqlmock
		m, dbengine.Engine = global.MockDB()
		m.ExpectExec("^UPDATE (.+)").WillReturnResult(sqlmock.NewResult(1, 1))
		UpdateTestevent(tc.in_c)
		assert.Equal(t, tc.out_HttpCode, tc.in_c.Response().StatusCode())
	}
}

func TestDeleteTestEvent(t *testing.T) {
	global.MockAC()

	cases := []struct {
		in_c         *server.Context
		out_HttpCode int
	}{
		{
			in_c:         global.BuildNewKunCtx("DELETE", "/testevents?ID=1", ``, "123", map[string]string{}),
			out_HttpCode: 200,
		},
		{
			in_c:         global.BuildNewKunCtx("DELETE", "/testevents", ``, "123", map[string]string{}),
			out_HttpCode: 500,
		},
		{
			in_c:         global.BuildNewKunCtx("DELETE", "/testevents?ID=-1", ``, "123", map[string]string{}),
			out_HttpCode: 409,
		},
	}
	for _, tc := range cases {
		var m sqlmock.Sqlmock
		m, dbengine.Engine = global.MockDB()
		m.ExpectExec("^DELETE (.+)").WillReturnResult(sqlmock.NewResult(1, 1))
		DeleteTestevent(tc.in_c)
		assert.Equal(t, tc.out_HttpCode, tc.in_c.Response().StatusCode())
	}
}

func TestChangeTesteventStatus(t *testing.T) {
	global.MockAC()

	cases := []struct {
		in_c         *server.Context
		out_HttpCode int
	}{
		{
			in_c:         global.BuildNewKunCtx("PUT", "/testevents/deploy?ID=1&ActionType=online", "", "123", map[string]string{}),
			out_HttpCode: 200,
		},
		{
			in_c:         global.BuildNewKunCtx("PUT", "/testevents/deploy?ActionType=online", "", "123", map[string]string{}),
			out_HttpCode: 500,
		},
		{
			in_c:         global.BuildNewKunCtx("PUT", "/testevents?ID=1&ActionType=online1", "", "123", map[string]string{}),
			out_HttpCode: 409,
		},
	}
	for _, tc := range cases {
		var m sqlmock.Sqlmock
		m, dbengine.Engine = global.MockDB()
		m.ExpectExec("^UPDATE (.+)").WillReturnResult(sqlmock.NewResult(1, 1))
		ChangeTesteventStatus(tc.in_c)
		assert.Equal(t, tc.out_HttpCode, tc.in_c.Response().StatusCode())
	}
}

func TestGetTestevent(t *testing.T) {
	global.MockAC()

	cases := []struct {
		in_c         *server.Context
		out_HttpCode int
	}{
		{
			in_c:         global.BuildNewKunCtx("GET", "/testevents/event?ID=1", "", "123", map[string]string{}),
			out_HttpCode: 200,
		},
		{
			in_c:         global.BuildNewKunCtx("GET", "/testevents/event", "", "123", map[string]string{}),
			out_HttpCode: 500,
		},
		{
			in_c:         global.BuildNewKunCtx("GET", "/testevents/event?ID=-1", "", "123", map[string]string{}),
			out_HttpCode: 409,
		},
	}
	for _, tc := range cases {
		var m sqlmock.Sqlmock
		m, dbengine.Engine = global.MockDB()
		m.ExpectQuery(".*").WillReturnRows(global.GetRowsStandardTestEvents(global.GetStandardTestEvents(1)))
		GetTestevent(tc.in_c)
		assert.Equal(t, tc.out_HttpCode, tc.in_c.Response().StatusCode())
	}
}
