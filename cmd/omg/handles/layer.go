package handles

import (
	"errors"
	"net/http"
	"strconv"

	"github.com/asaskevich/govalidator"

	"icode.baidu.com/baidu/faas/kun/cmd/omg/oclient"
	"icode.baidu.com/baidu/faas/kun/pkg/api"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/code"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/dao"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/dbengine"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/models"
	"icode.baidu.com/baidu/faas/kun/pkg/bce/brn"
	"icode.baidu.com/baidu/faas/kun/pkg/server"
)

/*
	layer 管理
	公共layer需要将个人的layer设置为公共，所以没有添加删除layer功能
*/

type LayerOutput struct {
	Layer    *api.Layer
	Location string
	Sqfs     string
}

func GetLayers(c *server.Context) {
	if layerVersionBrn := getQueryParameter("brn", c.Request(), ""); layerVersionBrn != "" {
		layer := new(api.Layer)
		layerBrn, err := brn.ParseLayerBrn(layerVersionBrn)
		if err != nil {
			c.WithWarnLog(err).WriteTo(c.Response())
			return
		}
		layer.Brn = layerBrn.ShortBrn()
		layer.Version = layerBrn.LayerVersion
		err = dao.FindOneLayer(layer)
		if err != nil {
			c.WithWarnLog(err).WriteTo(c.Response())
			return
		}

		objectDownload := oclient.Oc.Bos.FaasLayerDownloadUrl(layer.LayerName, layer.Uid, layer.CodeId)
		sqfsKey := code.GetLayerSquashFsKey(layer.Uid, layer.LayerName, layer.CodeId)
		sqfsDownload := oclient.Oc.Bos.GeneratePresignedUrl(sqfsKey)

		output := &LayerOutput{
			Layer:    layer,
			Location: objectDownload,
			Sqfs:     sqfsDownload,
		}
		c.Response().WriteHeaderAndEntity(http.StatusOK, returnSuccessLayout(output))
		return
	}

	containDeleted := getQueryParameter("containDeleted", c.Request(), "0") //0不包含、1包含
	pageNoStr := getQueryParameter("page", c.Request(), "1")
	pageSizeStr := getQueryParameter("pageSize", c.Request(), "10")
	layerVersionBrn := getQueryParameter("LayerVersionBrn", c.Request(), "")
	vended := getQueryParameter("vended", c.Request(), "false")
	runtime := getQueryParameter("CompatibleRuntimes", c.Request(), "")

	f := new(api.Layer)
	f.Uid = getQueryParameter("Uid", c.Request(), "")
	f.LayerName = getQueryParameter("LayerName", c.Request(), "")
	if layerVersionBrn != "" {
		layerBrn, err := brn.ParseLayerBrn(layerVersionBrn)
		if err != nil {
			c.WithWarnLog(err).WriteTo(c.Response())
			return
		}
		f.Brn = layerBrn.ShortBrn()
		f.Version = layerBrn.LayerVersion
	}
	isTrue := true
	if vended == "true" {
		f.IsVended = &isTrue
	}

	if (govalidator.IsNumeric(pageNoStr) && govalidator.IsNumeric(pageSizeStr)) != true {
		c.WithWarnLog(errors.New("ParameterValueException page Or pageSize ")).WriteTo(c.Response())
		return
	}

	var count, pageNo, pageSize int64
	db := dbengine.DBInstance().Model(api.Layer{}).Where(f)
	if runtime != "" {

		db = db.Where("`compatible_runtime` LIKE ?", "%"+runtime+"%")
	}
	if containDeleted == "1" {
		db = db.Unscoped()
	} else {
		db = db.Unscoped().Scopes(dao.ScopeRudDeletedAt)
	}
	db.Count(&count)

	var resMap = make(map[string]interface{})
	layerResSlice := make([]api.Layer, 0)

	pageNo, _ = strconv.ParseInt(pageNoStr, 10, 64)
	pageNo = models.Max(1, pageNo)
	pageSize, _ = strconv.ParseInt(pageSizeStr, 10, 64)
	pageSize = models.Max(1, pageSize)
	pageSize = models.Min(100, pageSize)

	db = db.Offset(int((pageNo - 1) * pageSize)).Limit(int(pageSize))
	resMap["count"] = count

	db.Find(&layerResSlice)

	ml := make([]interface{}, 0)
	for _, functionRes := range layerResSlice {
		ml = append(ml, functionRes)
	}
	resMap["rows"] = ml
	c.Response().WriteHeaderAndEntity(http.StatusOK, returnSuccessLayout(resMap))
}

func DeployLayer(c *server.Context) {
	request := c.Request().Request
	request.ParseForm()

	layerBrn := request.FormValue("Brn")
	version := request.FormValue("Version")
	isVendedStr := request.FormValue("IsVended")
	if layerBrn == "" || version == "" || isVendedStr == "" {
		c.WithWarnLog(errors.New("LayerBrn or Version or IsVended is empty")).WriteTo(c.Response())
		return
	}
	isVended := 0
	if isVendedStr == "true" {
		isVended = 1
	}
	err := dbengine.DBInstance().Exec("UPDATE `layers` SET `is_vended` = ? WHERE `layers`.`brn` =? AND `layers`.`version` =? ;", isVended, layerBrn, version).Error
	if err != nil {
		c.WithWarnLog(err).WriteTo(c.Response())
		return
	}
	c.Response().WriteHeaderAndEntity(http.StatusOK, AmisSuccMsg("success"))
}
