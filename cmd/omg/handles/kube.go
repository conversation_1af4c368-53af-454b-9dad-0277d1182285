package handles

import (
	"net/http"

	"icode.baidu.com/baidu/faas/kun/cmd/omg/oclient"
	"icode.baidu.com/baidu/faas/kun/pkg/server"
)

func updateKataRuntimeList(c *server.Context) {
	request := c.Request().Request
	request.ParseForm()
	clusterID := request.FormValue("CceClusterUUID")
	kataImages := request.FormValue("KataImages")
	err := oclient.Oc.CronClient.UpdateClusterKataRuntimeList(clusterID, kataImages)
	if err != nil {
		c.WithErrorLog(err).WriteTo(c.Response())
		return
	}
	c.Response().WriteHeaderAndEntity(http.StatusOK, returnSuccessLayout(nil))

}
