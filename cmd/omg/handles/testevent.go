package handles

import (
	"encoding/json"
	"errors"
	"fmt"
	"math"
	"net/http"
	"strconv"
	"strings"

	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/dao"
	"icode.baidu.com/baidu/faas/kun/pkg/server"
	"icode.baidu.com/baidu/faas/kun/pkg/util/bytefmt"
)

const (
	TESTEVENT_STATUS_ONLINE  = 1
	TESTEVENT_STATUS_OFFLINE = 2
	TESTEVENT_DEPLOY_OFFLINE = "offline"
	TESTEVENT_DEPLOY_ONLINE  = "online"
	PAGE_SIZE                = 10
	TESTEVENT_SIZE           = 50
)

var (
	DefaultEventLimit, _ = bytefmt.ToBytes("6MB")
)

func CheckTestEvent(event *dao.StandardTestEvent) bool {

	if event.Title == "" || event.Type == "" || !(len(event.Title) <= TESTEVENT_SIZE) {
		return false
	}

	if !CheckEvent(event.Event) {
		return false
	}

	return true
}

//判断Event是否为json字符串
func CheckEvent(event string) bool {
	if len(event) > int(DefaultEventLimit) {
		return false
	}
	emptyObj := make(map[string]interface{})
	if err := json.Unmarshal([]byte(event), &emptyObj); err != nil {
		return false
	}
	return true
}

/**
创建标准事件
*/
func CreateTestevent(c *server.Context) {
	request := c.Request().Request
	request.ParseForm()
	event := &dao.StandardTestEvent{
		Title:  request.FormValue("Title"),
		Type:   request.FormValue("Type"),
		Event:  request.FormValue("Event"),
		Status: TESTEVENT_STATUS_OFFLINE, //默认为下线状态
	}
	//参数验证
	if !CheckTestEvent(event) {
		errMsg := fmt.Sprintf("[params invalid][standard event: %v]", event)
		c.Response().WriteHeaderAndEntity(http.StatusConflict, AmisFailMsg(errMsg))
		return
	}

	//查找是否有同名的测试事件
	findEvent := &dao.StandardTestEvent{
		Title: event.Title,
	}
	if rKunErr := dao.IsStandardEventExist(findEvent); rKunErr == nil && findEvent.ID != 0 {
		errMsg := fmt.Sprintf("[standard test event exists][standard event: %v]", findEvent)
		c.Response().WriteHeaderAndEntity(http.StatusConflict, AmisFailMsg(errMsg))
		return
	}

	if rkunErr := dao.CreateStandardEvent(event); rkunErr != nil {
		c.WithWarnLog(rkunErr).WriteTo(c.Response())
		return
	}
	c.Response().WriteHeaderAndEntity(http.StatusCreated, AmisSuccMsg("success"))
}

/**
获取标准事件列表
*/
func ListTestevents(c *server.Context) {
	request := c.Request().Request
	request.ParseForm()

	var page = 1
	if request.FormValue("page") != "" {
		page, _ = strconv.Atoi(request.FormValue("page"))
	}

	var searchWord = ""
	if request.FormValue("searchWord") != "" {
		searchWord = request.FormValue("searchWord")
		switch searchWord {
		case "已下线":
			searchWord = strconv.Itoa(TESTEVENT_STATUS_OFFLINE)
		case "已上线":
			searchWord = strconv.Itoa(TESTEVENT_STATUS_ONLINE)
		}
	}

	testevent := new(dao.StandardTestEvent)
	testevents, rkunErr := dao.GetStandardEvent(testevent)
	if rkunErr != nil {
		c.WithWarnLog(rkunErr).WriteTo(c.Response())
		return
	}

	filterRes := filterTestevents(testevents, searchWord)
	resLen := len(filterRes)

	amis := new(AmisRep)
	amis.Status = 0

	resMap := make(map[string]interface{})
	resMap["count"] = resLen
	index := int(math.Min(float64(PAGE_SIZE*page), float64(resLen)))
	resMap["rows"] = (filterRes)[PAGE_SIZE*(page-1) : index]

	amis.Data = resMap
	c.Response().WriteHeaderAndEntity(http.StatusOK, amis)
}

/**
获取某个标准测试事件
*/
func GetTestevent(c *server.Context) {
	request := c.Request().Request
	request.ParseForm()

	idStr := request.FormValue("ID")
	if idStr == "" {
		c.WithWarnLog(errors.New("ID is empty")).WriteTo(c.Response())
		return
	}

	id, err := strconv.Atoi(idStr)
	if err != nil {
		c.WithErrorLog(err).WriteTo(c.Response())
		return
	}

	//参数验证
	if id <= 0 {
		errMsg := "params invalid"
		c.Response().WriteHeaderAndEntity(http.StatusConflict, AmisFailMsg(errMsg))
		return
	}

	standardTestEvent := &dao.StandardTestEvent{}
	standardTestEvent.ID = id

	if rkunErr := dao.GetOneStandardEvent(standardTestEvent); rkunErr != nil {
		c.WithWarnLog(rkunErr).WriteTo(c.Response())
		return
	}

	amis := new(AmisRep)
	amis.Status = 0

	//amis要求接口返回为数组格式
	resMap := make(map[string]interface{})
	resMap["rows"] = []*dao.StandardTestEvent{standardTestEvent}
	amis.Data = resMap

	c.Response().WriteHeaderAndEntity(http.StatusOK, amis)
}

/**
根据搜索关键词过滤搜索结果
*/
func filterTestevents(testevents []dao.StandardTestEvent, searchWord string) []dao.StandardTestEvent {
	eventLen := len(testevents)
	filterRes := make([]dao.StandardTestEvent, 0)

	if eventLen == 0 {
		return filterRes
	}

	if searchWord != "" {
		for _, event := range testevents {
			eventInfo := event.Title + event.Type + strconv.Itoa(event.Status)
			if strings.Contains(eventInfo, searchWord) == true {
				filterRes = append(filterRes, event)
			}
		}
		return filterRes
	}
	return testevents
}

/**
更新标准事件
*/
func UpdateTestevent(c *server.Context) {
	request := c.Request().Request
	request.ParseForm()

	idStr := request.FormValue("ID")
	if idStr == "" {
		c.WithWarnLog(errors.New("ID is empty")).WriteTo(c.Response())
		return
	}

	id, err := strconv.Atoi(idStr)
	if err != nil {
		c.WithErrorLog(err).WriteTo(c.Response())
		return
	}

	cond := &dao.StandardTestEvent{}
	cond.ID = id

	updateEvent := &dao.StandardTestEvent{
		Type:  request.FormValue("Type"),
		Event: request.FormValue("Event"),
	}

	//参数验证
	if cond.ID <= 0 || updateEvent.Type == "" || !CheckEvent(updateEvent.Event) {
		errMsg := fmt.Sprintf("[params invalid][standard event: %v]", updateEvent)
		c.Response().WriteHeaderAndEntity(http.StatusConflict, AmisFailMsg(errMsg))
		return
	}

	if rkunErr := dao.UpdateStandardEvent(cond, updateEvent); rkunErr != nil {
		c.WithWarnLog(rkunErr).WriteTo(c.Response())
		return
	}
	c.Response().WriteHeaderAndEntity(http.StatusOK, AmisSuccMsg("success"))

}

/**
删除标准事件
*/
func DeleteTestevent(c *server.Context) {
	request := c.Request().Request
	request.ParseForm()

	idStr := request.FormValue("ID")
	if idStr == "" {
		c.WithWarnLog(errors.New("ID is empty")).WriteTo(c.Response())
		return
	}
	id, err := strconv.Atoi(idStr)
	if err != nil {
		c.WithErrorLog(err).WriteTo(c.Response())
		return
	}

	//参数验证
	if id <= 0 {
		errMsg := "params invalid"
		c.Response().WriteHeaderAndEntity(http.StatusConflict, AmisFailMsg(errMsg))
		return
	}

	deleteEvent := &dao.StandardTestEvent{
		ID: id,
	}

	if rkunErr := dao.DeleteStandardEvent(deleteEvent); rkunErr != nil {
		c.WithWarnLog(rkunErr).WriteTo(c.Response())
		return
	}
	c.Response().WriteHeaderAndEntity(http.StatusOK, AmisSuccMsg("success"))

}

/**
更改测试事件的部署状态
*/
func ChangeTesteventStatus(c *server.Context) {
	request := c.Request().Request
	request.ParseForm()

	idStr := request.FormValue("ID")
	statusReq := request.FormValue("ActionType")
	if idStr == "" || statusReq == "" {
		c.WithWarnLog(errors.New("ID or statusReq is empty")).WriteTo(c.Response())
		return
	}

	statusMap := map[string]int{
		"online":  TESTEVENT_STATUS_ONLINE,
		"offline": TESTEVENT_STATUS_OFFLINE,
	}

	id, err := strconv.Atoi(idStr)
	if err != nil {
		c.WithErrorLog(err).WriteTo(c.Response())
		return
	}
	//参数验证
	if id <= 0 || (statusReq != TESTEVENT_DEPLOY_OFFLINE && statusReq != TESTEVENT_DEPLOY_ONLINE) {
		errMsg := "params invalid"
		c.Response().WriteHeaderAndEntity(http.StatusConflict, AmisFailMsg(errMsg))
		return
	}

	cond := &dao.StandardTestEvent{}
	cond.ID = id

	updateEvent := &dao.StandardTestEvent{
		Status: statusMap[statusReq],
	}

	if rkunErr := dao.UpdateStandardEvent(cond, updateEvent); rkunErr != nil {
		c.WithWarnLog(rkunErr).WriteTo(c.Response())
		return
	}
	c.Response().WriteHeaderAndEntity(http.StatusOK, AmisSuccMsg("success"))
}
