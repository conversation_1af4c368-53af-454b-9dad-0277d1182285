package handles

import (
	"errors"
	"net/http"

	"github.com/coreos/etcd/mvcc/mvccpb"

	"icode.baidu.com/baidu/faas/kun/cmd/omg/oclient"
	"icode.baidu.com/baidu/faas/kun/pkg/api"
	"icode.baidu.com/baidu/faas/kun/pkg/server"
	"icode.baidu.com/baidu/faas/kun/pkg/util/json"
)

func GetEtcdValueByKey(c *server.Context) {
	key := c.Request().PathParameter("key")

	data := make(map[string]interface{})
	if resp, err := oclient.Oc.Etcd.Get(key); err != nil {
		c.WithErrorLog(err).WriteTo(c.Response())
		return
	} else {
		if resp.Count > 0 {
			kv := resp.Kvs[0]
			data["node"] = parseEtcdValue(kv)
		} else {
			c.WithErrorLog(errors.New("The node does not exist.")).WriteTo(c.Response())
			return
		}
	}
	c.Response().WriteHeaderAndEntity(http.StatusOK, returnSuccessLayout(data))
}

func parseEtcdValue(kv *mvccpb.KeyValue) map[string]interface{} {
	node := make(map[string]interface{})
	node["key"] = string(kv.Key)
	node["value"] = string(kv.Value)
	node["dir"] = false
	node["ttl"] = 0
	node["createdIndex"] = kv.CreateRevision
	node["modifiedIndex"] = kv.ModRevision

	tmp := make(map[string]interface{})
	json.Unmarshal(kv.Value, &tmp)
	node["jsonValue"] = tmp
	return node
}

func kv2node(value []byte) *api.NodeInfo {
	node := &api.NodeInfo{}
	json.Unmarshal(value, node)
	return node
}
