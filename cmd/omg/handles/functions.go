package handles

import (
	"context"
	"errors"
	"net/http"
	"strconv"

	"github.com/asaskevich/govalidator"
	"github.com/emicklei/go-restful"

	"icode.baidu.com/baidu/faas/kun/cmd/omg/oclient"
	"icode.baidu.com/baidu/faas/kun/pkg/api"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/dao"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/dbengine"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/models"
	"icode.baidu.com/baidu/faas/kun/pkg/server"
	"icode.baidu.com/baidu/faas/kun/pkg/util/convert"
)

func GetFunctionDetail(c *server.Context) {
	findFunc := new(dao.Function)
	findFunc.FunctionBrn = c.Request().PathParameter("FunctionBrn")
	if findFunc.FunctionBrn == "" {
		c.WithWarnLog(errors.New("FunctionBrn is nil")).WriteTo(c.Response())
		return
	}
	dao.FindOneFunc(findFunc)
	url, err := oclient.Oc.Bos.FaasCodeDownloadUrl(findFunc.FunctionName, findFunc.Uid, findFunc.CodeSha256, findFunc.CodeID)
	if err != nil {
		c.Logger().Errorf("get download url failed, [func: %v][errmsg:%s]", findFunc, err.Error())
	}
	var squashFsUrl string
	if findFunc.SquashFsSha256 != nil && *findFunc.SquashFsSha256 != "" {
		squashFsUrl, err = oclient.Oc.Bos.FaasSquashFsCodeDownloadUrl(findFunc.FunctionName, findFunc.Uid, findFunc.CodeID)
		if err != nil {
			c.Logger().Errorf("get sqfs download url failed, [func: %v][errmsg:%s]", findFunc, err.Error())
		}
	}
	id := findFunc.Id
	c.Response().WriteHeaderAndEntity(http.StatusOK, returnSuccessLayout(map[string]interface{}{
		"Code":          api.CodeStorage{Location: url},
		"Configuration": findFunc,
		"ID":            id,
		"Sqfs":          squashFsUrl,
	}))
}

func ListFunctions(c *server.Context) {
	containDeleted := getQueryParameter("containDeleted", c.Request(), "0") //0不包含、1包含
	commitId := getQueryParameter("CommitId", c.Request(), "")
	pageNoStr := getQueryParameter("page", c.Request(), "1")
	pageSizeStr := getQueryParameter("pageSize", c.Request(), "10")

	f := new(dao.Function)
	f.FunctionBrn = getQueryParameter("FunctionBrn", c.Request(), "")
	f.Uid = getQueryParameter("Uid", c.Request(), "")
	f.FunctionName = getQueryParameter("FunctionName", c.Request(), "")
	if commitId != "" {
		f.CommitID = convert.String(commitId)
	}

	if _, err := govalidator.ValidateStruct(f); err != nil {
		c.WithWarnLog(err).WriteTo(c.Response())
		return
	} else if (govalidator.IsNumeric(pageNoStr) && govalidator.IsNumeric(pageSizeStr)) != true {
		c.WithWarnLog(errors.New("ParameterValueException page Or pageSize ")).WriteTo(c.Response())
		return
	}

	var count, pageNo, pageSize int64
	db := dbengine.DBInstance().Model(dao.Function{}).Where(f)
	if containDeleted == "1" {
		db = db.Unscoped()
	} else {
		db = db.Unscoped().Scopes(dao.ScopeRudDeletedAt)
	}
	db.Count(&count)

	var resMap = make(map[string]interface{})
	functionResSlice := make([]dao.Function, 0)

	pageNo, _ = strconv.ParseInt(pageNoStr, 10, 64)
	pageNo = models.Max(1, pageNo)
	pageSize, _ = strconv.ParseInt(pageSizeStr, 10, 64)
	pageSize = models.Max(1, pageSize)
	pageSize = models.Min(100, pageSize)

	db = db.Offset(int((pageNo - 1) * pageSize)).Limit(int(pageSize))
	resMap["count"] = count

	db.Find(&functionResSlice)

	ml := make([]interface{}, 0)
	for _, functionRes := range functionResSlice {
		functionRes.DealResFunction()
		ml = append(ml, functionRes)
	}
	resMap["rows"] = ml
	c.Response().WriteHeaderAndEntity(http.StatusOK, returnSuccessLayout(resMap))
}

func getQueryParameter(key string, r *restful.Request, defaultValue string) string {
	if r.QueryParameter(key) != "" {
		return r.QueryParameter(key)
	}
	return defaultValue
}

func GetConcurrencyDetail(c *server.Context) {
	f := &dao.Function{
		FunctionBrn: c.Request().PathParameter("FunctionBrn"),
	}

	if err := dao.FindOneFunc(f); err != nil {
		c.WithErrorLog(err).WriteTo(c.Response())
		return
	}

	userFuncs, err := dao.FindFunc(&dao.Function{Uid: f.Uid})
	if err != nil {
		c.WithErrorLog(err).WriteTo(c.Response())
		return
	}

	reservedSum := 0
	for _, uf := range *userFuncs {
		if uf.Version == "$LATEST" {
			reservedSum += *uf.ReservedConcurrentExecutions
		}
	}

	accountQuota, err := oclient.Oc.Ops.GetQuota(context.Background(), f.Uid, "user_concurrency")
	if err != nil {
		c.WithErrorLog(err).WriteTo(c.Response())
		return
	}

	instanceInfos, err := getInstanceInfos(oclient.OCfg.EventhubSerivceName)
	if err != nil {
		c.WithErrorLog(err).WriteTo(c.Response())
		return
	}

	var res = map[string]int{
		"FunctionUsage":   0,
		"UnreservedUsage": 0,
	}

	for _, info := range instanceInfos {
		oclient.Oc.Evt.SetAddress(*info.HostName, int(*info.InstanceStatus.Port))
		detail, err := oclient.Oc.Evt.GetConcurrencyDetail(f.Uid, f.FunctionName)
		if err != nil {
			c.WithErrorLog(err).WriteTo(c.Response())
			return
		}

		res["FunctionUsage"] += detail["FunctionUsage"]
		res["UnreservedUsage"] += detail["UnreservedUsage"]
	}

	res["AccountTotalQuota"] = accountQuota
	res["FunctionQuota"] = *f.ReservedConcurrentExecutions
	res["UnreservedQuota"] = accountQuota - reservedSum

	c.Response().WriteHeaderAndEntity(http.StatusOK, returnSuccessLayout(res))
}
