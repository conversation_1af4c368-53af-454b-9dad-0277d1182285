package handles

import (
	"fmt"
	"io/ioutil"
	"net/http"
	"strconv"
	"strings"

	"icode.baidu.com/baidu/faas/kun/pkg/store/reserve"
	"icode.baidu.com/baidu/faas/kun/pkg/util/logs"

	"icode.baidu.com/baidu/faas/kun/cmd/omg/oclient"
	"icode.baidu.com/baidu/faas/kun/pkg/api"
	"icode.baidu.com/baidu/faas/kun/pkg/omg/amis"
	"icode.baidu.com/baidu/faas/kun/pkg/server"
	"icode.baidu.com/baidu/faas/kun/pkg/util/json"
)

var nodeStatusMap = map[int]string{
	0: "raw",
	1: "initing",
	2: "ready",
	3: "inUse",
	4: "recycling",
	5: "offline",
	6: "deleted",
	7: "abnormal",
}

func NodeStatusDistribute(c *server.Context) {
	nodeDistribute := make(amis.KvData)
	resp, err := oclient.Oc.Etcd.GetWithPrefix("")
	if err != nil {
		c.WithErrorLog(err).WriteTo(c.Response())
		return
	}
	for _, kv := range resp.Kvs {
		if !strings.Contains(string(kv.Key), "node/") {
			continue
		}
		node := kv2node(kv.Value)
		nodeDistribute[string(node.State)]++
	}

	c.Response().WriteHeaderAndEntity(http.StatusOK, amis.ChartResponse{
		Response: amis.Response{
			Msg:    "ok",
			Status: 0,
		},
		Data: amis.Chart{
			Title:   amis.NewTitle("Node 状态分布"),
			Tooltip: amis.DefaultTooltip,
			Series: []*amis.Series{
				amis.NewSeries("Node 状态", amis.KvData2Series(nodeDistribute)),
			},
			KvData: &nodeDistribute,
		},
	})
}

// 根据集群名称查询node
func NodeStatusDistributeByCluster(c *server.Context) {
	nodeDistribute := make(amis.KvData)
	prefix := c.Request().QueryParameter("prefix")
	resp, err := oclient.Oc.Etcd.GetWithPrefix(prefix)
	if err != nil {
		c.WithErrorLog(err).WriteTo(c.Response())
		return
	}
	for _, kv := range resp.Kvs {
		node := kv2node(kv.Value)
		nodeDistribute[string(node.State)]++
	}

	c.Response().WriteHeaderAndEntity(http.StatusOK, amis.ChartResponse{
		Response: amis.Response{
			Msg:    "ok",
			Status: 0,
		},
		Data: amis.Chart{
			Title:   amis.NewTitle("Node 状态分布"),
			Tooltip: amis.DefaultTooltip,
			Series: []*amis.Series{
				amis.NewSeries("Node 状态", amis.KvData2Series(nodeDistribute)),
			},
			KvData: &nodeDistribute,
		},
	})
}

func NodeUserDistribute(c *server.Context) {
	distribute := make(amis.KvData)

	resp, err := oclient.Oc.Etcd.GetWithPrefix("")
	if err != nil {
		c.WithErrorLog(err).WriteTo(c.Response())
		return
	}

	for _, kv := range resp.Kvs {
		if !strings.Contains(string(kv.Key), "node/") {
			continue
		}
		node := kv2node(kv.Value)
		if node == nil || node.State == api.NodeStateCceDeleted {
			continue
		}

		userID := node.NodeLabels.Get(api.LabelUserID)
		if userID == "" {
			userID = "(free)"
		}
		distribute[userID]++
	}
	c.Response().WriteHeaderAndEntity(http.StatusOK, amis.ChartResponse{
		Response: amis.Response{
			Msg:    "ok",
			Status: 0,
		},
		Data: amis.Chart{
			Title:   amis.NewTitle("Node 用户占用"),
			Tooltip: amis.DefaultTooltip,
			Series: []*amis.Series{
				amis.NewSeries("User ID", amis.KvData2Series(distribute)),
			},
			KvData: &distribute,
		},
	})
}

// 根据集群名称查询NodeUser
func NodeUserDistributeByCluster(c *server.Context) {
	distribute := make(amis.KvData)
	prefix := c.Request().QueryParameter("prefix")
	resp, err := oclient.Oc.Etcd.GetWithPrefix(prefix)
	if err != nil {
		c.WithErrorLog(err).WriteTo(c.Response())
		return
	}

	for _, kv := range resp.Kvs {
		node := kv2node(kv.Value)
		if node == nil || node.State == api.NodeStateCceDeleted {
			continue
		}
		userID := node.NodeLabels.Get(api.LabelUserID)
		if userID == "" {
			userID = "(free)"
		}
		distribute[userID]++
	}
	c.Response().WriteHeaderAndEntity(http.StatusOK, amis.ChartResponse{
		Response: amis.Response{
			Msg:    "ok",
			Status: 0,
		},
		Data: amis.Chart{
			Title:   amis.NewTitle("Node 用户占用"),
			Tooltip: amis.DefaultTooltip,
			Series: []*amis.Series{
				amis.NewSeries("User ID", amis.KvData2Series(distribute)),
			},
			KvData: &distribute,
		},
	})
}

func GetNodeList(c *server.Context) {
	prefix := c.Request().QueryParameter("prefix")
	state := c.Request().QueryParameter("state")

	data := make([]map[string]interface{}, 0)
	if resp, err := oclient.Oc.Etcd.GetWithPrefix(prefix); err != nil {
		c.WithErrorLog(err).WriteTo(c.Response())
		return
	} else {
		for _, kv := range resp.Kvs {
			node := parseEtcdValue(kv)
			jsonValue := node["jsonValue"].(map[string]interface{})
			if jsonValue["state"] == nil || jsonValue["instanceShortId"] == nil {
				continue
			}
			if state != "" {
				if state == jsonValue["state"] {
					data = append(data, node)
					continue
				}
			} else {
				data = append(data, node)
			}
		}
	}

	c.Response().WriteHeaderAndEntity(http.StatusOK, returnSuccessLayout(data))
}

// 获取集群列表
func GetClusterList(c *server.Context) {
	region := c.Request().QueryParameter("region")
	data := []amis.ClusterName{}
	if resp, err := oclient.Oc.Etcd.GetWithPrefix(""); err != nil {
		c.WithErrorLog(err).WriteTo(c.Response())
		return
	} else {
		for _, kv := range resp.Kvs {
			node := parseEtcdValue(kv)
			jsonValue := node["jsonValue"].(map[string]interface{})
			key := node["key"].(string)
			if strings.Contains(key, "node/") {
				continue
			}
			clusterIndex := strings.Index(key, "k8s/")
			if clusterIndex >= 0 {
				//选出正在使用的集群
				if jsonValue["status"].(int64) != 0 {
					continue
				}
				bceOptions := jsonValue["bceOptions"].(map[string]interface{})
				if !strings.Contains(bceOptions["cceEndpoint"].(string), region) {
					continue
				}
				data = append(data, amis.ClusterName{
					Label: key[clusterIndex+4:],
					Value: key[:clusterIndex] + "node" + key[clusterIndex+3:],
				})
			}
		}
	}
	options := amis.OptionsResponse{Options: data}
	c.Response().WriteHeaderAndEntity(http.StatusOK, returnSuccessLayout(options))
}

func GetNodesStatisticInfo(c *server.Context) {
	prefix := c.Request().QueryParameter("prefix")
	clusterID := c.Request().PathParameter("ClusterID")
	statisticInfo := make(map[string]int, 0)
	statisticInfo["all_node"] = 0
	statisticInfo["zero_memory"] = 0
	statisticInfo["nonZero_memory"] = 0
	statisticInfo["fake_node"] = 0
	statisticInfo["cold_node"] = 0
	if resp, err := oclient.Oc.Etcd.GetWithPrefix(prefix); err != nil {
		c.WithErrorLog(err).WriteTo(c.Response())
		return
	} else {
		for _, kv := range resp.Kvs {
			if !strings.Contains(string(kv.Key), "node/") {
				continue
			}
			n := kv2node(kv.Value)
			if n.CceClusterUUID == clusterID {
				statisticInfo["all_node"]++
				state := string(n.State)
				statisticInfo[state]++
				if n.State == "SCHEDULE_initialized" {
					_, err := oclient.Oc.PoolmgrReserveManager.GetColdNodeMemory(n)
					if err != nil && err == reserve.ErrResourceNotExist {
						statisticInfo["fake_node"]++
					}
					if err == nil {
						statisticInfo["cold_node"]++
					}
				}
				if n.State == "SCHEDULE_busy" {
					reservedMemory, err := oclient.Oc.PoolmgrReserveManager.GetWarmNodeMemory(n)
					if err != nil {
						logs.Infof("err: %v", err)
						continue
					}
					if reservedMemory > 0 {
						statisticInfo["nonZero_memory"]++
					} else {
						statisticInfo["zero_memory"]++
					}
				}
			}
		}
	}
	c.Response().WriteHeaderAndEntity(http.StatusOK, statisticInfo)
}

func GetPodSatusByNode(c *server.Context) {
	floatingIP := c.Request().PathParameter("FloatingIP")
	resp, err := http.Get(fmt.Sprintf("http://%s:8231/v1/funclet/faas-containers", floatingIP))
	if err != nil {
		c.WithErrorLog(err).WriteTo(c.Response())
		return
	}
	defer resp.Body.Close()
	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		c.WithErrorLog(err).WriteTo(c.Response())
		return
	}
	tmp := make([]*api.ContainerInfo, 0)
	json.Unmarshal(body, &tmp)
	c.Response().WriteHeaderAndEntity(http.StatusOK, returnSuccessLayout(map[string]interface{}{
		"rows":  tmp,
		"count": len(tmp),
	}))
}

func GetNodeHistoryRecords(c *server.Context) {
	nodeID := c.Request().PathParameter("nodeID")
	countStr := c.Request().QueryParameter("count") // 最大历史版本数量
	count := 20                                     // 默认历史版本数量
	if countStr != "" {
		n, err := strconv.Atoi(countStr)
		if err == nil && n != 0 {
			count = n
		}
	}
	resp, err := oclient.Oc.NodeClient.GetNodeHistoryRecords(nodeID, count)
	if err != nil {
		c.WithErrorLog(err).WriteTo(c.Response())
		return
	}
	c.Response().WriteHeaderAndEntity(http.StatusOK, returnSuccessLayout(map[string]interface{}{
		"rows":  resp,
		"count": len(resp),
	}))
}

var (
	validStates = []api.NodeStateType{
		api.NodeStateScheduleBusy,
		api.NodeStateScheduleInactive,
		api.NodeStateScheduleObsolete,
		api.NodeStateScheduleError,
		api.NodeStateScheduleInitialized,
		api.NodeStateScheduleOffline,
		api.NodeStateScheduleOutdated,
	}
)

func isValidState(state api.NodeStateType) bool {
	for _, s := range validStates {
		if s == state {
			return true
		}
	}
	return false
}

func UpdateNodeState(c *server.Context) {
	r := c.Request().Request
	nodeId := r.FormValue("jsonValue[id]")
	stateStr := r.FormValue("jsonValue[state]")
	if !isValidState(api.NodeStateType(stateStr)) {
		err := fmt.Errorf("invalid state '%s'", stateStr)
		c.WithErrorLog(err).WriteTo(c.Response())
		return
	}

	nodeState := api.NodeStateType(stateStr)

	nodeInfo, err := oclient.Oc.NodeClient.GetNode(nodeId)
	if err != nil {
		c.WithErrorLog(err).WriteTo(c.Response())
		return
	}
	nodeInfo.State = nodeState
	if err := oclient.Oc.NodeClient.SetNode(nodeInfo); err != nil {
		c.WithErrorLog(err).WriteTo(c.Response())
		return
	}
	c.Response().WriteHeaderAndEntity(http.StatusOK, returnSuccessLayout(nil))
}

// updateNode 转发更新node的请求到cron，调用相应的cron client接口
func updateNode(c *server.Context) {
	nodeID := c.Request().PathParameter("nodeID")
	op := c.Request().PathParameter("op")
	var err error
	switch op {
	case "offline":
		err = oclient.Oc.CronClient.OfflineNode(nodeID)
	case "upgrade":
		err = oclient.Oc.CronClient.UpgradeNode(nodeID)
	case "freeze":
		err = oclient.Oc.CronClient.FreezeNode(nodeID)
	case "thaw":
		err = oclient.Oc.CronClient.ThawNode(nodeID)
	default:
		err = fmt.Errorf("wrong operation type %s", op)
	}

	if err != nil {
		c.WithErrorLog(err).WriteTo(c.Response())
		return
	}
	c.Response().WriteHeaderAndEntity(http.StatusOK, returnSuccessLayout(nil))
}

// deleteNode 删除etcd的一个node
// delete接口暂时放omg，后续加入cron
func deleteNode(c *server.Context) {
	nodeID := c.Request().PathParameter("nodeID")
	err := deleteVoidNode(nodeID)
	if err != nil {
		c.WithErrorLog(err).WriteTo(c.Response())
		return
	}

	c.Response().WriteHeaderAndEntity(http.StatusOK, returnSuccessLayout(nil))
}

// deleteNodes 批量删除etcd的node数据，只对cce deleted状态生效。可以用count调整删除的数量，默认是20个。
func deleteNodes(c *server.Context) {
	countStr := c.Request().QueryParameter("count") // 最大node数
	count := 20                                     // 默认node数
	if countStr != "" {
		n, err := strconv.Atoi(countStr)
		if err == nil && n != 0 {
			count = n
		}
	}

	nodes, err := oclient.Oc.NodeClient.GetAllNodes()
	if err != nil {
		c.WithErrorLog(err).WriteTo(c.Response())
		return
	}

	deletedCount := 0
	for _, node := range nodes {
		if node.State != api.NodeStateCceDeleted {
			continue
		}

		err = deleteVoidNode(node.ID)
		if err != nil { // 跳过删除出错的
			continue
		}

		deletedCount++
		if deletedCount >= count {
			break
		}
	}

	c.Response().WriteHeaderAndEntity(http.StatusOK,
		returnSuccessLayout(fmt.Sprintf("deleted %d nodes", deletedCount)))
}

// deleteVoidNode 删除etcd中指定id的无用node数据，连node attr一并删除。只允许对cce deleted或error状态的node操作。
func deleteVoidNode(nodeID string) error {
	node, err := oclient.Oc.NodeClient.GetNode(nodeID)
	if err != nil {
		return err
	}
	if node.State != api.NodeStateCceDeleted && node.State != api.NodeStateCceError {
		return fmt.Errorf("node state must be %s or %s", api.NodeStateCceDeleted, api.NodeStateCceError)
	}

	err = oclient.Oc.NodeClient.DeleteEntireNode(nodeID)
	if err != nil {
		return err
	}

	return nil
}

// 灰度kata runtime
func grayReleaseKataRuntimeImage(c *server.Context) {
	request := c.Request().Request
	request.ParseForm()
	clusterID := request.FormValue("CceClusterUUID")
	kataImageID := request.FormValue("KataImageID")
	nodeID := request.FormValue("nodeID")
	err := oclient.Oc.CronClient.GrayRelaeseKataRuntimeImage(clusterID, nodeID, kataImageID)
	if err != nil {
		c.WithErrorLog(err).WriteTo(c.Response())
		return
	}
	c.Response().WriteHeaderAndEntity(http.StatusOK, returnSuccessLayout(nil))
}

// 全量设置kata runtime image
func fullReleaseKataRuntimeImage(c *server.Context) {
	request := c.Request().Request
	request.ParseForm()
	clusterID := request.FormValue("CceClusterUUID")
	kataImageID := request.FormValue("KataImageID")
	err := oclient.Oc.CronClient.FullRelaeseKataRuntimeImage(clusterID, kataImageID)
	if err != nil {
		c.WithErrorLog(err).WriteTo(c.Response())
		return
	}
	c.Response().WriteHeaderAndEntity(http.StatusOK, "nil")
}

type PodData struct {
	api.ContainerInfo
	PodConcurrency int64
}

// 获取每个pod当前并发数（只有warm pod才有，其他状态pod的并发数设置为0）
func GetNodePodsData(c *server.Context) {
	nodeID := c.Request().PathParameter("nodeID")
	nodeInfo, err := oclient.Oc.NodeClient.GetNode(nodeID)
	if err != nil {
		c.WithErrorLog(err).WriteTo(c.Response())
		return
	}

	floatingIP := nodeInfo.FloatingIP
	resp, err := http.Get(fmt.Sprintf("http://%s:8231/v1/funclet/faas-containers", floatingIP))
	if err != nil {
		c.WithErrorLog(err).WriteTo(c.Response())
		return
	}
	defer resp.Body.Close()
	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		c.WithErrorLog(err).WriteTo(c.Response())
		return
	}
	tmp := make([]*api.ContainerInfo, 0)
	json.Unmarshal(body, &tmp)

	respData := make([]*PodData, 0)
	for i := range tmp {
		container := tmp[i]
		var podConcurr int64
		if container.Status == "warm" {
			podInfo := container.Container2Pod(nodeInfo)
			podConcurr, err = oclient.Oc.PoolmgrReserveManager.GetWarmPodWithConcurrency(podInfo)
			if err != nil {
				podConcurr = 0
			}

		} else {
			podConcurr = 0
		}

		pod := &PodData{ContainerInfo: *container, PodConcurrency: podConcurr}
		respData = append(respData, pod)
	}

	c.Response().WriteHeaderAndEntity(http.StatusOK, returnSuccessLayout(map[string]interface{}{
		"rows":  respData,
		"count": len(respData),
	}))
}

// 获取某个node当前剩余内存
func GetNodeReserveMemory(c *server.Context) {
	//get the node info from etcd first
	nodeID := c.Request().PathParameter("nodeID")
	nodeInfo, err := oclient.Oc.NodeClient.GetNode(nodeID)
	if err != nil {
		c.WithErrorLog(err).WriteTo(c.Response())
	}
	var reservedMemory int64
	if nodeInfo.State == api.NodeStateScheduleBusy {
		reservedMemory, err = oclient.Oc.PoolmgrReserveManager.GetWarmNodeMemory(nodeInfo)
		if err != nil {
			c.WithErrorLog(err).WriteTo(c.Response())
		}
	} else {
		reservedMemory, err = oclient.Oc.PoolmgrReserveManager.GetColdNodeMemory(nodeInfo)
		if err != nil {
			c.WithErrorLog(err).WriteTo(c.Response())
		}
	}

	c.Response().WriteHeaderAndEntity(http.StatusOK, returnSuccessLayout(map[string]interface{}{
		"reservedMemory": reservedMemory,
	}))
}

// 获取redis里所有key
func GetAllRedisKeys(c *server.Context) {
	redisKeys, err := oclient.Oc.PoolmgrReserveManager.ScanAllKeys()
	if err != nil {
		c.WithErrorLog(err).WriteTo(c.Response())
	}
	c.Response().WriteHeaderAndEntity(http.StatusOK, returnSuccessLayout(map[string]interface{}{
		"rows":  redisKeys,
		"count": len(redisKeys),
	}))

}

// 获取redis里面全部数据
func GetRedisMemoryData(c *server.Context) {
	scanKey := c.Request().PathParameter("ScanKey")
	redisData, err := oclient.Oc.PoolmgrReserveManager.ScanMemoryData(scanKey)
	if err != nil {
		c.WithErrorLog(err).WriteTo(c.Response())
	}
	c.Response().WriteHeaderAndEntity(http.StatusOK, returnSuccessLayout(map[string]interface{}{
		"rows":  redisData,
		"count": len(redisData),
	}))

}
