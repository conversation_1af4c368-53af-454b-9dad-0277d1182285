package handles

import (
	"bytes"
	"errors"
	"io/ioutil"
	"math"
	"net/http"
	"strconv"
	"strings"
	"time"

	"icode.baidu.com/baidu/faas/kun/cmd/omg/oclient"

	"github.com/google/uuid"

	"icode.baidu.com/baidu/faas/kun/pkg/api"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/dao"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/models"
	"icode.baidu.com/baidu/faas/kun/pkg/server"
	"icode.baidu.com/baidu/faas/kun/pkg/util/json"
)

type AmisRep struct {
	Status int                    `json:"status"`
	Msg    string                 `json:"msg"`
	Data   map[string]interface{} `json:"data"`
}

type OmgBlueprint struct {
	Id             uint   `json:"-"`
	Uuid           string `json:"UUID"`
	Name           string
	Description    *string
	KeywordsStr    string
	BosObjKey      string
	Runtime        string
	Handler        string
	Version        string
	Status         string
	Extra          string
	LinksStr       string
	UpdatedAt      time.Time
	CreatedAt      time.Time
	EnvironmentStr string
	AuthorsStr     string
	CodeUpdateType string `json:"Type,omitempty"`
	Environment    *api.Environment
	MemorySize     *int
	Timeout        *int
	TriggerType    string
	Keywords       []string
	Links          map[string]string
	Authors        []string
	LayersStr      string
	Layers         []string
}

// ListVersions 列出某个蓝图的所有版本
func ListVersions(c *server.Context) {
	request := c.Request().Request
	request.ParseForm()

	bp := &dao.Blueprint{
		Name: request.FormValue("Name"),
	}

	var page = 1
	if request.FormValue("page") != "" {
		page, _ = strconv.Atoi(request.FormValue("page"))
	}

	if bps, rkunErr := dao.ListBlueprints(bp); rkunErr != nil {
		c.WithWarnLog(rkunErr).WriteTo(c.Response())
	} else {
		bpsLen := len(*bps)
		amis := new(AmisRep)
		amis.Status = 0

		repMap := make(map[string]interface{})
		repMap["count"] = bpsLen
		bpsRes := models.FormatBlueprints(bps)
		index := int(math.Min(float64(10*page), float64(bpsLen)))
		repMap["rows"] = (bpsRes)[10*(page-1) : index]
		amis.Data = repMap

		c.Response().WriteHeaderAndEntity(http.StatusOK, amis)
	}
}

/*
   处理AMis首页列表页面的Get请求
   从db读取后再根据搜索关键字过滤
*/
func ListBlueprints(c *server.Context) {
	request := c.Request().Request
	request.ParseForm()

	var page = 1
	if request.FormValue("page") != "" {
		page, _ = strconv.Atoi(request.FormValue("page"))
	}

	searchWord := request.FormValue("searchWord")

	bp := new(dao.Blueprint)
	bps, rkunErr := dao.ListBlueprintsInside(bp)
	if rkunErr != nil {
		c.WithWarnLog(rkunErr).WriteTo(c.Response())
		return
	}

	filterRes := filterBpList(*bps, searchWord)
	resLen := len(*filterRes)

	amis := new(AmisRep)
	amis.Status = 0

	repMap := make(map[string]interface{})
	repMap["count"] = resLen
	index := int(math.Min(float64(10*page), float64(resLen)))
	bpRes := models.FormatBlueprints(filterRes)
	ombpRes := dealBlueprint(bpRes)
	repMap["rows"] = (ombpRes)[10*(page-1) : index]
	amis.Data = repMap
	c.Response().WriteHeaderAndEntity(http.StatusOK, amis)
}

func dealBlueprint(bps []interface{}) []interface{} {
	res := make([]interface{}, 0)
	if len(bps) > 0 {
		for _, v := range bps {
			bp := v.(dao.Blueprint)
			ombp := &OmgBlueprint{
				Id:             bp.Id,
				Uuid:           bp.Uuid,
				Name:           bp.Name,
				Description:    bp.Description,
				KeywordsStr:    bp.KeywordsStr,
				BosObjKey:      bp.BosObjKey,
				Runtime:        bp.Runtime,
				Handler:        bp.Handler,
				Version:        bp.Version,
				Status:         bp.Status,
				Extra:          bp.Extra,
				LinksStr:       bp.LinksStr,
				UpdatedAt:      bp.UpdatedAt,
				CreatedAt:      bp.CreatedAt,
				EnvironmentStr: bp.EnvironmentStr,
				AuthorsStr:     bp.AuthorsStr,
				CodeUpdateType: bp.CodeUpdateType,
				Environment:    bp.Environment,
				MemorySize:     bp.MemorySize,
				Timeout:        bp.Timeout,
				TriggerType:    bp.TriggerType,
				Keywords:       bp.Keywords,
				Links:          bp.Links,
				Authors: <AUTHORS>
				LayersStr:      bp.LayersStr,
				Layers:         bp.Layers,
			}
			res = append(res, ombp)
		}
	}
	return res
}

func CreateBlueprint(c *server.Context) {
	request := c.Request().Request
	request.ParseForm()
	desc := request.FormValue("Description")
	memoryStr := request.FormValue("MemorySize")
	timeoutStr := request.FormValue("Timeout")

	memory, _ := strconv.Atoi(memoryStr)
	timeout, _ := strconv.Atoi(timeoutStr)

	deployments := 1
	bpReq := &dao.Blueprint{
		Description:    &desc,
		Uuid:           uuid.New().String(),
		Name:           request.FormValue("Name"),
		KeywordsStr:    request.FormValue("Keywords"),
		Runtime:        request.FormValue("Runtime"),
		Handler:        request.FormValue("Handler"),
		Version:        request.FormValue("Version"),
		BosObjKey:      request.FormValue("NewZipFileKey"),
		CodeUpdateType: request.FormValue("Type"),
		Status:         api.BlueprintOffline,
		EnvironmentStr: request.FormValue("Environment"),
		LinksStr:       request.FormValue("Links"),
		Deployments:    &deployments,
		AuthorsStr:     request.FormValue("Authors"),
		LayersStr:      request.FormValue("Layers"),
	}

	if checkErr := models.CheckLayers(bpReq.LayersStr); checkErr != nil {
		c.WithWarnLog(checkErr).WriteTo(c.Response())
		return
	}
	extraMap := make(map[string]interface{})
	extraMap["MemorySize"] = memory
	extraMap["Timeout"] = timeout
	extraMap["TriggerType"] = request.FormValue("TriggerType")
	extraStr, _ := json.Marshal(extraMap)
	bpReq.Extra = string(extraStr)

	var (
		codeBytes []byte
		err       error
	)

	if request.FormValue("SyncBlueprint") == "true" {
		bpReq.Uuid = uuid.New().String()
		codeUrl := request.FormValue("CodeUrl")
		//download zip包
		codeBytes, err = DownloadBlueprintCode(codeUrl)
		if err != nil {
			c.WithWarnLog(err).WriteTo(c.Response())
			return
		}
		//upload code zip to bos
		name := strings.Split(bpReq.Name, "_")[0]
		newBosKey, err := oclient.Oc.Bos.FaasUploadBlueprintZip(codeBytes, name)
		if err != nil {
			c.WithWarnLog(err).WriteTo(c.Response())
			return
		}
		bpReq.BosObjKey = newBosKey
	}

	// 如果创建全新的蓝图，首先检查名字是否重复。
	if request.FormValue("BrandNewBlueprint") == "true" {
		findBlueprint := new(dao.Blueprint)
		findBlueprint.Name = bpReq.Name
		if rkunErr := dao.FindOneBlueprint(findBlueprint); rkunErr == nil && findBlueprint.Uuid != "" {
			errMsg := "Blueprint's name repeated : " + findBlueprint.Name
			if err := oclient.Oc.Bos.DeleteObject(bpReq.BosObjKey); err != nil {
				errMsg = errMsg + ".  Fail to delete uploaded zipfile: " + err.Error()
			}
			c.Response().WriteHeaderAndEntity(http.StatusConflict, AmisFailMsg(errMsg))
			return
		}
	}

	if rkunErr := dao.CreateBlueprint(bpReq); rkunErr != nil {
		c.WithWarnLog(rkunErr).WriteTo(c.Response())
		return
	}

	c.Response().WriteHeaderAndEntity(http.StatusCreated, AmisSuccMsg(""))
}

// 生成blueprint code url
func GenerateCodeUrl(c *server.Context) {
	request := c.Request().Request
	request.ParseForm()
	bosKey := request.FormValue("NewZipFileKey")
	url := oclient.Oc.Bos.GeneratePresignedUrl(bosKey)
	amis := new(AmisRep)
	amis.Status = 0
	repMap := make(map[string]interface{})
	repMap["CodeUrl"] = url
	amis.Data = repMap
	c.Response().WriteHeaderAndEntity(http.StatusOK, amis)

}

func UpdateBlueprint(c *server.Context) {
	request := c.Request().Request
	request.ParseForm()

	desc := request.FormValue("Description")
	bpUUID := request.FormValue("UUID")
	if bpUUID == "" {
		c.WithWarnLog(errors.New("UUID is empty")).WriteTo(c.Response())
		return
	}

	memoryStr := request.FormValue("MemorySize")
	timeoutStr := request.FormValue("Timeout")
	memory, _ := strconv.Atoi(memoryStr)
	timeout, _ := strconv.Atoi(timeoutStr)
	bpReq := &dao.Blueprint{
		Description:    &desc,
		KeywordsStr:    request.FormValue("Keywords"),
		Runtime:        request.FormValue("Runtime"),
		Handler:        request.FormValue("Handler"),
		BosObjKey:      request.FormValue("BosObjKey"),
		CodeUpdateType: request.FormValue("Type"),
		EnvironmentStr: request.FormValue("Environment"),
		LinksStr:       request.FormValue("Links"),
		AuthorsStr:     request.FormValue("Authors"),
		LayersStr:      request.FormValue("Layers"),
	}
	if checkErr := models.CheckLayers(bpReq.LayersStr); checkErr != nil {
		c.WithWarnLog(checkErr).WriteTo(c.Response())
		return
	}
	extraMap := make(map[string]interface{})
	extraMap["MemorySize"] = memory
	extraMap["Timeout"] = timeout
	extraMap["TriggerType"] = request.FormValue("TriggerType")
	extraStr, _ := json.Marshal(extraMap)
	bpReq.Extra = string(extraStr)

	condBlueprint := dao.Blueprint{
		Uuid: bpUUID,
	}

	// delete old zipFile if there's new zipfile
	if newObjKey := request.FormValue("NewZipFileKey"); len(newObjKey) > 0 {
		if err := oclient.Oc.Bos.DeleteObject(bpReq.BosObjKey); err != nil {
			c.WithWarnLog(err).WriteTo(c.Response())
			return
		}
		bpReq.BosObjKey = newObjKey
	}
	if rkunErr := dao.UpdateBlueprint(condBlueprint, bpReq); rkunErr != nil {
		c.WithWarnLog(rkunErr).WriteTo(c.Response())
		return
	}
	c.Response().WriteHeaderAndEntity(http.StatusCreated, AmisSuccMsg(""))
}

func UploadZipFile(c *server.Context) {
	request := c.Request().Request
	request.ParseMultipartForm(32 << 20)

	zipFileReader, handler, err := request.FormFile("file")
	if err != nil {
		c.WithWarnLog(err).WriteTo(c.Response())
		return
	}
	defer zipFileReader.Close()

	buf := new(bytes.Buffer)
	buf.ReadFrom(zipFileReader)

	bosObjKey, err := oclient.Oc.Bos.FaasUploadBlueprintZip(buf.Bytes(), handler.Filename)
	if err != nil {
		c.WithWarnLog(err).WriteTo(c.Response())
		return
	}

	amis := new(AmisRep)
	amis.Status = 0
	amis.Msg = ""
	repMap := make(map[string]interface{})
	repMap["filename"] = strings.Split(bosObjKey, "/")[1]
	repMap["value"] = bosObjKey
	amis.Data = repMap

	c.Response().WriteHeaderAndEntity(http.StatusOK, amis)
}

// DeleteBlueprint 分别删除Bos Zipfile和数据库记录
func DeleteBlueprint(c *server.Context) {
	request := c.Request().Request
	request.ParseForm()

	bpUUID := request.FormValue("UUID")
	bosObjKey := request.FormValue("BosObjKey")

	if bpUUID == "" || bosObjKey == "" {
		c.WithWarnLog(errors.New("UUID or BosObjKey is empty")).WriteTo(c.Response())
		return
	}

	bpReq := &dao.Blueprint{
		Uuid: bpUUID,
	}

	if rkunErr := dao.DeleteBlueprint(bpReq); rkunErr != nil {
		c.WithWarnLog(rkunErr).WriteTo(c.Response())
		return
	}

	if err := oclient.Oc.Bos.DeleteObject(bosObjKey); err != nil {
		c.WithWarnLog(err).WriteTo(c.Response())
		return
	}

	c.Response().WriteHeaderAndEntity(http.StatusOK, AmisSuccMsg(""))
}

// ChangeStatus 更改蓝图的部署状态
func ChangeStatus(c *server.Context) {
	request := c.Request().Request
	request.ParseForm()

	name := request.FormValue("Name")
	bpUUID := request.FormValue("UUID")
	statusReq := request.FormValue("ActionType")
	if name == "" || bpUUID == "" || statusReq == "" {
		c.WithWarnLog(errors.New("Name or UUID or statusReq is empty")).WriteTo(c.Response())
		return
	}

	bpReq := new(dao.Blueprint)
	bpCondition := dao.Blueprint{
		Name: name,
	}

	// 把一个版本置为online/beta/default时，先把该蓝图其它online/beta/default的版本状态置为offline
	// 以保证每个蓝图最多有一个online/beta/default版本。
	if statusReq == api.BlueprintOnline || statusReq == api.BlueprintBeta || statusReq == api.BlueprintDefault {
		bpCondition.Status = statusReq
		bpReq.Status = api.BlueprintOffline
		if rkunErr := dao.UpdateBlueprint(bpCondition, bpReq); rkunErr != nil {
			c.WithWarnLog(rkunErr).WriteTo(c.Response())
			return
		}
	}

	bpCondition.Uuid = bpUUID
	bpCondition.Status = ""
	bpReq.Status = statusReq

	if rkunErr := dao.UpdateBlueprint(bpCondition, bpReq); rkunErr != nil {
		c.WithWarnLog(rkunErr).WriteTo(c.Response())
		return
	}
	c.Response().WriteHeaderAndEntity(http.StatusOK, AmisSuccMsg(""))
}

/*
   当蓝图含有online或beta版本时，这两个版本会放入filterRes，其余offline版本不再放入filterRes。
   当蓝图只有offline版本时，把最新的版本放入filterRes。
   最后根据searchWord过滤结果。
*/
func filterBpList(bps []dao.Blueprint, searchWord string) *[]dao.Blueprint {
	bpsLen := len(bps)
	filterRes := make([]dao.Blueprint, 0)
	if bpsLen == 0 {
		return &filterRes
	}

	for i := 0; i < bpsLen-1; i++ {
		if bps[i].Status != api.BlueprintOffline {
			filterRes = append(filterRes, bps[i])
		} else if (bps[i].Name != bps[i+1].Name) && (len(filterRes) == 0 || bps[i].Name != filterRes[len(filterRes)-1].Name) {
			filterRes = append(filterRes, bps[i])
		}
	}

	if bps[bpsLen-1].Status != api.BlueprintOffline || len(filterRes) == 0 || bps[bpsLen-1].Name != filterRes[len(filterRes)-1].Name {
		filterRes = append(filterRes, bps[bpsLen-1])
	}

	// 根据searchWord进一步过滤结果
	if searchWord != "" {
		searchResult := make([]dao.Blueprint, 0)
		for _, bp := range filterRes {
			bpInfoString := bp.Name + *bp.Description + bp.KeywordsStr + bp.Runtime + bp.Handler + bp.Version + bp.Status
			if strings.Contains(bpInfoString, searchWord) == true {
				searchResult = append(searchResult, bp)
			}
		}
		return &searchResult
	}

	return &filterRes
}

func AmisSuccMsg(msg string) *AmisRep {
	amis := new(AmisRep)
	amis.Status = 0
	amis.Msg = msg
	return amis
}

func AmisFailMsg(msg string) *AmisRep {
	amis := new(AmisRep)
	amis.Status = 1
	amis.Msg = msg
	return amis
}

// 根据蓝图code URL获取蓝图代码zip
func DownloadBlueprintCode(codeUrl string) ([]byte, error) {
	resp, err := http.Get(codeUrl)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()
	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}
	return body, nil
}
