package handles

import (
	"fmt"
	"net/http"
	"strconv"

	"github.com/emicklei/go-restful"

	"icode.baidu.com/baidu/faas/kun/cmd/omg/oclient"
	"icode.baidu.com/baidu/faas/kun/pkg/httptrigger/dao"
	"icode.baidu.com/baidu/faas/kun/pkg/httptrigger/proxy_config_store"
	"icode.baidu.com/baidu/faas/kun/pkg/server"
)

func ListAllRestApi(c *server.Context) {
	request := c.Request().Request
	request.ParseForm()

	ra := &dao.RestApi{
		Uuid: request.FormValue("uuid"),
		Uid:  request.FormValue("uid"),
	}

	raSlice := make([]dao.RestApi, 0)
	count, err := getDataFromDB(c.Request(), ra.TableName(), ra, &raSlice)
	if err != nil {
		c.WithWarnLog(err).WriteTo(c.Response())
		return
	}

	c.Response().WriteHeaderAndEntity(http.StatusOK, getAmisData(count, raSlice))
}

func ListAllBlacklist(c *server.Context) {
	request := c.Request().Request
	request.ParseForm()

	bl := &dao.BlackList{
		Uid: request.FormValue("uid"),
	}

	blSlice := make([]dao.BlackList, 0)
	count, err := getDataFromDB(c.Request(), bl.TableName(), bl, &blSlice)
	if err != nil {
		c.WithWarnLog(err).WriteTo(c.Response())
		return
	}

	amis := new(AmisRep)
	amis.Status = 0

	repMap := make(map[string]interface{})
	repMap["count"] = count
	repMap["rows"] = blSlice
	amis.Data = repMap

	c.Response().WriteHeaderAndEntity(http.StatusOK, amis)
}

func ListAllEndpoint(c *server.Context) {
	request := c.Request().Request
	request.ParseForm()

	ep := &dao.Endpoint{
		ResourcePath: request.FormValue("resource_path"),
		Method:       request.FormValue("method"),
	}

	if apiId := request.FormValue("api_id"); apiId != "" {
		if id, err := strconv.Atoi(apiId); err == nil {
			ep.ApiID = uint64(id)
		}
	}

	epSlice := make([]dao.Endpoint, 0)
	count, err := getDataFromDB(c.Request(), ep.TableName(), ep, &epSlice)
	if err != nil {
		c.WithWarnLog(err).WriteTo(c.Response())
		return
	}

	c.Response().WriteHeaderAndEntity(http.StatusOK, getAmisData(count, epSlice))
}

func ListAllDeployment(c *server.Context) {
	request := c.Request().Request
	request.ParseForm()

	dp := &dao.Deployment{
		Brn: request.FormValue("brn"),
	}

	if epId := request.FormValue("endpoint_id"); epId != "" {
		if id, err := strconv.Atoi(epId); err == nil {
			dp.EndpointID = uint64(id)
		}
	}

	if stageId := request.FormValue("stage_id"); stageId != "" {
		if id, err := strconv.Atoi(stageId); err == nil {
			dp.StageID = uint64(id)
		}
	}

	if apiId := request.FormValue("api_id"); apiId != "" {
		if id, err := strconv.Atoi(apiId); err == nil {
			sg := &dao.Stage{
				ApiID: uint64(id),
			}

			sgSlice := make([]dao.Stage, 0)
			_, err := getDataFromDB(c.Request(), sg.TableName(), sg, &sgSlice)
			if err != nil {
				c.WithWarnLog(err).WriteTo(c.Response())
				return
			}

			if len(sgSlice) != 0 {
				dp.StageID = sgSlice[0].ID
			} else {
				amis := &AmisRep{
					Status: 0,
					Data: map[string]interface{}{
						"count": 0,
						"rows":  sgSlice,
					},
				}
				c.Response().WriteHeaderAndEntity(http.StatusOK, amis)
				return
			}
		}
	}

	dpSlice := make([]dao.Deployment, 0)
	count, err := getDataFromDB(c.Request(), dp.TableName(), dp, &dpSlice)
	if err != nil {
		c.WithWarnLog(err).WriteTo(c.Response())
		return
	}

	c.Response().WriteHeaderAndEntity(http.StatusOK, getAmisData(count, dpSlice))
}

func ListAllStage(c *server.Context) {
	request := c.Request().Request
	request.ParseForm()

	sg := &dao.Stage{}
	if apiId := request.FormValue("api_id"); apiId != "" {
		if id, err := strconv.Atoi(apiId); err == nil {
			sg.ApiID = uint64(id)
		}
	}

	sgSlice := make([]dao.Stage, 0)
	count, err := getDataFromDB(c.Request(), sg.TableName(), sg, &sgSlice)
	if err != nil {
		c.WithWarnLog(err).WriteTo(c.Response())
		return
	}

	c.Response().WriteHeaderAndEntity(http.StatusOK, getAmisData(count, sgSlice))
}

func ListAllProxyStorage(c *server.Context) {
	request := c.Request().Request
	request.ParseForm()

	ps := &dao.ProxyStorage{
		ApiUuid:   request.FormValue("api_uuid"),
		StageName: request.FormValue("stage_name"),
	}

	psSlice := make([]dao.ProxyStorage, 0)
	count, err := getDataFromDB(c.Request(), ps.TableName(), ps, &psSlice)
	if err != nil {
		c.WithWarnLog(err).WriteTo(c.Response())
		return
	}

	c.Response().WriteHeaderAndEntity(http.StatusOK, getAmisData(count, psSlice))
}

func InsertBlacklist(c *server.Context) {
	request := c.Request().Request
	request.ParseForm()

	redisErr := InsertBlacklistRedis(request)
	if redisErr != nil {
		err := fmt.Errorf("omg insert user into redis error: %v", redisErr)
		c.WithWarnLog(err).WriteTo(c.Response())
		return
	}

	mysqlErr := InsertBlacklistMysql(request)
	if mysqlErr != nil {
		err := fmt.Errorf("omg insert user into mysql error: %v", mysqlErr)
		c.WithWarnLog(err).WriteTo(c.Response())
		return
	}

	c.Response().WriteHeaderAndEntity(http.StatusOK, AmisSuccMsg("success"))
}

func DeleteBlacklist(c *server.Context) {
	request := c.Request().Request
	request.ParseForm()

	redisErr := DeleteBlacklistRedis(request)
	if redisErr != nil {
		err := fmt.Errorf("omg delete user into redis error: %v", redisErr)
		c.WithWarnLog(err).WriteTo(c.Response())
		return
	}

	mysqlErr := DeleteBlacklistMysql(request)
	if mysqlErr != nil {
		err := fmt.Errorf("omg delete user into mysql error: %v", mysqlErr)
		c.WithWarnLog(err).WriteTo(c.Response())
		return
	}

	c.Response().WriteHeaderAndEntity(http.StatusOK, AmisSuccMsg("success"))
}

func InsertBlacklistMysql(request *http.Request) error {
	bl := &dao.BlackList{
		Uid: request.FormValue("uid"),
	}

	// 如果用户已经被加入黑名单，则先解封它
	if err := oclient.Oc.Mysql.Where(bl).Delete(bl).Error; err != nil {
		return err
	}

	bl.Reason = request.FormValue("reason")
	return oclient.Oc.Mysql.Create(bl).Error
}

func InsertBlacklistRedis(request *http.Request) error {
	uid := request.FormValue("uid")
	key := fmt.Sprintf("%s_%s", proxy_config_store.PrefixBlacklist, uid)
	reason := request.FormValue("reason")

	_, err := oclient.Oc.Redis.Redis().Set(key, reason, -1).Result()
	return err
}

func DeleteBlacklistMysql(request *http.Request) error {
	bl := &dao.BlackList{
		Uid:    request.FormValue("uid"),
		Reason: request.FormValue("reason"),
	}

	return oclient.Oc.Mysql.Where(bl).Delete(bl).Error
}

func DeleteBlacklistRedis(request *http.Request) error {
	uid := request.FormValue("uid")
	key := fmt.Sprintf("%s_%s", proxy_config_store.PrefixBlacklist, uid)

	_, err := oclient.Oc.Redis.Redis().Del(key).Result()
	return err
}

func getPageInfo(r *restful.Request) (int64, int64) {
	pageNoStr := getQueryParameter("page", r, "1")
	pageNo, _ := strconv.ParseInt(pageNoStr, 10, 64)

	pageSizeStr := getQueryParameter("pageSize", r, "10")
	pageSize, _ := strconv.ParseInt(pageSizeStr, 10, 64)

	return pageNo, pageSize
}

func getDataFromDB(r *restful.Request, table string, query interface{}, result interface{}) (int64, error) {
	db := oclient.Oc.Mysql.Table(table).Where(query)
	if db.Error != nil {
		return 0, db.Error
	}

	var count, pageNo, pageSize int64
	if cdb := db.Count(&count); cdb.Error != nil {
		return 0, cdb.Error
	}

	pageNo, pageSize = getPageInfo(r)
	if pageSize < 1 || pageNo < 1 {
		db = db.Find(result)
		return count, db.Error
	} else {
		db = db.Offset(int((pageNo - 1) * pageSize)).Limit(int(pageSize)).Find(result)
		return count, db.Error
	}
}

func getAmisData(count int64, data interface{}) *AmisRep {
	amis := new(AmisRep)
	amis.Status = 0

	repMap := make(map[string]interface{})
	repMap["count"] = count
	repMap["rows"] = data
	amis.Data = repMap

	return amis
}
