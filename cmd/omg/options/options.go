package options

import (
	"github.com/spf13/pflag"

	"icode.baidu.com/baidu/faas/kun/pkg/store/reserve"

	"icode.baidu.com/baidu/faas/kun/cmd/apiserver/options"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/dbengine"
	"icode.baidu.com/baidu/faas/kun/pkg/bce/iam"
	cronClient "icode.baidu.com/baidu/faas/kun/pkg/cron/client"
	"icode.baidu.com/baidu/faas/kun/pkg/etcd"
	"icode.baidu.com/baidu/faas/kun/pkg/rpc/db"
	kunRedis "icode.baidu.com/baidu/faas/kun/pkg/rpc/redis"
	genericoptions "icode.baidu.com/baidu/faas/kun/pkg/server/options"
)

const (
	// ApplicationName 指定应用名称
	ApplicationName = "omg"
)

type OmgOptions struct {
	RecommendedOptions  *genericoptions.RecommendedOptions
	CodeConfiguration   *options.CodeConfiguration
	DbConfiguration     *db.DbConfig
	EtcdOptions         *etcd.Options
	GenericRedisOptions *kunRedis.RedisOptions
	ReserveOptions      *reserve.Options
	CronOption          *cronClient.CronOptions
	OpsCenterEndpoint   string
	IAMConfiguration    *iam.IAMOptions
	AMIS_TOKEN          string
	EventhubSerivceName string
}

func NewOmgOptions() *OmgOptions {
	codeConfig := options.NewCodeOptions()

	dbengine.DbConf = db.NewDbConfig()

	s := OmgOptions{
		RecommendedOptions:  genericoptions.NewRecommendedOptions(),
		CodeConfiguration:   codeConfig,
		DbConfiguration:     dbengine.DbConf,
		EtcdOptions:         genericoptions.NewEtcdOptions(),
		GenericRedisOptions: kunRedis.NewRedisOptions(),
		ReserveOptions:      reserve.NewOptions(),
		CronOption:          cronClient.NewCronOptions(),
		OpsCenterEndpoint:   "http://settings.bce-internal.baidu.com",
		IAMConfiguration:    iam.NewIAMOptions(),
		EventhubSerivceName: "cfc-eventhub.BCE.bjyz",
	}
	s.RecommendedOptions.SecureServing.BindPort = 8001
	//s.RecommendedOptions.TelemetryOptions.ServiceName = ApplicationName
	s.AMIS_TOKEN = "VegQ2SNWlDATKGtYCUvZtTSwDbLTEzSa1"
	return &s
}

func (s *OmgOptions) AddFlags(fs *pflag.FlagSet) {
	s.RecommendedOptions.AddFlags(fs)
	s.GenericRedisOptions.AddRedisFlags(fs)
	s.CodeConfiguration.AddCodeFlags(fs)
	s.DbConfiguration.AddFlags(fs)
	s.CronOption.AddFlags(fs)
	genericoptions.AddEtcdFlags(s.EtcdOptions, fs)
	s.IAMConfiguration.AddUniversalFlags(fs)

	fs.StringVar(&s.OpsCenterEndpoint, "opscenter-endpoint", s.OpsCenterEndpoint, "opscenter endpoint")
	fs.StringVar(&s.AMIS_TOKEN, "amis-token", s.AMIS_TOKEN, "AMIS_TOKEN")
	fs.StringVar(&s.EventhubSerivceName, "eventhub-service-name", s.EventhubSerivceName, "eventhub service name")
}
