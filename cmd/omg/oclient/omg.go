package oclient

import (
	"github.com/jinzhu/gorm"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/squashfs"

	"icode.baidu.com/baidu/faas/kun/pkg/store/reserve"

	"icode.baidu.com/baidu/faas/kun/cmd/omg/options"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/code"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/dbengine"
	"icode.baidu.com/baidu/faas/kun/pkg/bce/iam"
	"icode.baidu.com/baidu/faas/kun/pkg/bce/opscenter"
	cronClient "icode.baidu.com/baidu/faas/kun/pkg/cron/client"
	"icode.baidu.com/baidu/faas/kun/pkg/etcd"
	"icode.baidu.com/baidu/faas/kun/pkg/eventhub/client"
	"icode.baidu.com/baidu/faas/kun/pkg/eventhub/rpc"
	"icode.baidu.com/baidu/faas/kun/pkg/rpc/db"
	"icode.baidu.com/baidu/faas/kun/pkg/rpc/redis"
	"icode.baidu.com/baidu/faas/kun/pkg/util/logs"
)

var Oc *OmgClients
var OCfg *Config

type OmgClients struct {
	Etcd                  etcd.BaseInterface
	K8sInfoClient         etcd.K8sInterface
	NodeClient            etcd.NodeInterface
	PoolmgrReserveManager reserve.ReserveManager
	Redis                 *rpc.RedisClient
	CronClient            cronClient.CronClientInterface
	Mysql                 *gorm.DB
	Bos                   code.CodeManagerInterface
	Auth                  *Auth
	Mailer                *Mailer
	Evt                   *client.EventhubInsideClient
	Iam                   iam.ClientInterface
	Ops                   opscenter.OpsCenterInterface
}

type Config struct {
	EventhubSerivceName string
}

//omg认证
type Auth struct {
	AMIS_TOKEN string
	//ip list
}

//发送邮件
type Mailer struct {
}

func NewOmgConfig(runOptions *options.OmgOptions) {
	OCfg = &Config{
		EventhubSerivceName: runOptions.EventhubSerivceName,
	}
}

func NewOmgClients(runOptions *options.OmgOptions) *OmgClients {
	//初始化etcd
	etcdClient := dbengine.NewEtcdClient(runOptions.EtcdOptions, true)
	//初始化mysql连接池
	mysql, err := db.NewMysqlEngine(runOptions.DbConfiguration)
	if err != nil {
		// TODO better error
		logs.Errorf(err.Error())
		return nil
	}

	iamClient, err := iam.CreateIAMClient(runOptions.IAMConfiguration.IAMConfPath)
	if err != nil {
		logs.Errorf(err.Error())
		return nil
	}

	opscli, err := opscenter.NewOpsCenter(iamClient, runOptions.OpsCenterEndpoint)
	if err != nil {
		logs.Errorf(err.Error())
		return nil
	}

	nodeClient := etcd.NewClient()
	nodeClient.StartClient(runOptions.EtcdOptions)

	k8sinfoClient := etcd.NewClient()
	k8sinfoClient.StartClient(runOptions.EtcdOptions)

	redisClusterClient := redis.NewClient(runOptions.GenericRedisOptions)
	poolmgrReserveManager := reserve.NewManager(redisClusterClient, runOptions.ReserveOptions)
	codeCli, _ := code.NewCodeManager(runOptions.CodeConfiguration, squashfs.NewSquashFsClient(squashfs.NewSquashFsOptions()))

	Oc = &OmgClients{
		Etcd:                  etcdClient,
		NodeClient:            nodeClient,
		K8sInfoClient:         k8sinfoClient,
		PoolmgrReserveManager: poolmgrReserveManager,
		Redis:                 rpc.NewRedisClient(runOptions.GenericRedisOptions),
		CronClient:            cronClient.NewCronClient(runOptions.CronOption),
		Mysql:                 mysql,
		Bos:                   codeCli,
		Auth: &Auth{
			AMIS_TOKEN: runOptions.AMIS_TOKEN,
		},
		Mailer: &Mailer{},
		Evt:    client.NewEventhubInsideClient(&client.EventHubOptions{}),
		Iam:    iamClient,
		Ops:    opscli,
	}
	return Oc
}

func (a *Auth) Check(str string) bool {
	return str == a.AMIS_TOKEN
}

func (m *Mailer) SendMail() {

}
