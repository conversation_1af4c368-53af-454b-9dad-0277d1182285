package error

import (
	"net/http"

	kunErr "icode.baidu.com/baidu/faas/kun/pkg/error"
)

const (
	SqlIOException kunErr.ErrorType = "SqlIOException"
)

func NewSqlIOException(cause string, lasterr error) kunErr.FinalError {
	return kunErr.NewGenericException(kunErr.BasicError{
		Code:    SqlIOException,
		Cause:   cause,
		Message: "failed to write sql statement",
		Status:  http.StatusInternalServerError,
	}, lasterr)
}
