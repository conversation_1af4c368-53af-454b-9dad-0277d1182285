package main

import (
	"fmt"
	"os"

	"github.com/spf13/pflag"

	"icode.baidu.com/baidu/faas/kun/cmd/eventhub/app"
	"icode.baidu.com/baidu/faas/kun/cmd/eventhub/app/options"
	"icode.baidu.com/baidu/faas/kun/pkg/server"
	"icode.baidu.com/baidu/faas/kun/pkg/util/flag"
	"icode.baidu.com/baidu/faas/kun/pkg/util/logs"
	"icode.baidu.com/baidu/faas/kun/pkg/version/verflag"
)

func main() {
	s := options.NewEventhubOptions()
	s.AddFlags(pflag.CommandLine)

	flag.InitFlags()
	s.Complete()
	logs.InitLogs()
	logs.InitSummaryLogs()
	logs.InitReportLogs()
	defer logs.FlushLogs()
	verflag.PrintAndExitIfRequested()

	stopCh := server.SetupSignalHandler()
	if err := app.Run(s, stopCh); err != nil {
		fmt.Fprintf(os.Stderr, "%v\n", err)
		os.Exit(1)
	}
}
