package options

import (
	"strings"

	"github.com/spf13/pflag"

	"icode.baidu.com/baidu/faas/kun/pkg/api"
	"icode.baidu.com/baidu/faas/kun/pkg/bce/auth"
	"icode.baidu.com/baidu/faas/kun/pkg/bce/iam"
	"icode.baidu.com/baidu/faas/kun/pkg/etcd"
	invoker "icode.baidu.com/baidu/faas/kun/pkg/invoker/client"
	mqhub "icode.baidu.com/baidu/faas/kun/pkg/mqhub/client"
	"icode.baidu.com/baidu/faas/kun/pkg/report"
	kunRedis "icode.baidu.com/baidu/faas/kun/pkg/rpc/redis"
	genericoptions "icode.baidu.com/baidu/faas/kun/pkg/server/options"
	"icode.baidu.com/baidu/faas/kun/pkg/whitelist"
)

const (
	// ApplicationName 指定应用名称
	ApplicationName = "eventhub"
	// RunningModeCloud 中心化的运行模式，所有模块都部署在 IDC 内部
	RunningModeCloud = "cloud"
	// RunningModeDuEdge DuEdge 云加速运行模式，Eventhub 位于 Edge，apiserver 位于 IDC 内部
	RunningModeDuEdge = "duedge"
	// RunningModeOTE OTE 运行模式，Eventhub 位于 CDN，apiserver 位于 IDC 内部
	RunningModeOTE = "ote"
	// 函数缓存默认过期时间
	defaultFcExpirySecond = 600

	// 别名缓存默认过期时间
	defaultAliasExpirySecond = 600

	// etcd 所有key的前缀
	defaultRegion = "bj"
	// 单用户最大并发数
	defaultUserMaxConcurrency = 1000
)

// EventhubOptions descript options that eventhub needs
type EventhubOptions struct {
	RecommendedOptions                *genericoptions.RecommendedOptions
	IAMOptions                        *iam.IAMOptions
	DuerOptions                       *auth.DuerOptions
	ApiserverEndpoint                 string
	PoolmanagerEndpoint               string
	InvokerOptions                    *invoker.InvokerClientOptions
	RedisOptions                      *kunRedis.RedisOptions
	DataSourceRedisOptions            *kunRedis.RedisOptions
	FunctionCacheOptions              *FunctionCacheOptions
	PolicyCacheOptions                *PolicyCacheOptions
	BillingCacheOptions               *BillingCacheOptions
	AliasCacheOptions                 *AliasCacheOptions
	FuncletConnBackCacheOptions       *etcd.FuncletConnBackCacheOptions
	EtcdOptions                       *etcd.Options
	RunningMode                       string
	Region                            string
	UserMaxConcurrency                int
	DataReportOption                  *report.DataReportOption
	EventSenderOption                 *report.EventSenderOption
	BillReportOption                  *report.BillReportOption
	MqhubClientOption                 *mqhub.MqhubClientOptions
	EnableBillingPermissionCheck      bool
	WhiteListOptions                  *whitelist.WhiteListOptions
	StsForbiddenListOptions           *whitelist.WhiteListOptions
	OpsCenterEndpoint                 string
	UserAvgLimitConcurrency           int // 在同一个集群内，用户维度的并发度限制
	OTEAuthOptions                    *OTEAuthOptions
	EnableSecureContainer             bool // 一键强制切换原BCC集群标识
	VIPUserOptions                    *whitelist.WhiteListOptions
	EnablePodConcurrency              bool                                         // 是否开启pod级别的并发度
	IncrUserConcurrency               float64                                      // 用户设置并发度限制基础上提升比例
	ReservedConfig                    *api.ReservedConfig                          // 预留实例配置
	NodeIsolationUserWhiteListOptions *whitelist.NodeIsolationUserWhiteListOptions //用户白名单，决定独占node还是共享node
	ReservedInstanceEnabled           bool                                         // 预留实例开关
	StsUserConfPath                   string                                       // 开启sts用户白名单配置文件
}

// FunctionCacheOptions xxx
type FunctionCacheOptions struct {
	EnableFunctionCache bool
	Prefix              string
	ExpirySecond        int
}

// NewFunctionCacheOptions xxx
func NewFunctionCacheOptions() *FunctionCacheOptions {
	return &FunctionCacheOptions{
		EnableFunctionCache: true,
		Prefix:              api.FunctionCacheEtcdPrefix,
		ExpirySecond:        defaultFcExpirySecond,
	}
}

// AddFunctionCacheFlags xxx
func (o *FunctionCacheOptions) AddFunctionCacheFlags(fs *pflag.FlagSet) {
	fs.IntVar(&o.ExpirySecond, "func-cache-expiry", o.ExpirySecond, "function cache key expiry second")
	fs.BoolVar(&o.EnableFunctionCache, "enable-func-cache", o.EnableFunctionCache, "enable function cache")
}

// AliasCacheOptions xxx
type AliasCacheOptions struct {
	EnableAliasCache bool
	Prefix           string
	ExpirySecond     int
}

func NewAliasCacheOptions() *AliasCacheOptions {
	return &AliasCacheOptions{
		EnableAliasCache: true,
		Prefix:           api.AliasCacheEtcdPrefix,
		ExpirySecond:     defaultAliasExpirySecond,
	}
}

func (o *AliasCacheOptions) AddAliasCacheFlags(fs *pflag.FlagSet) {
	fs.IntVar(&o.ExpirySecond, "alias-cache-expiry", o.ExpirySecond, "alias cache key expiry second")
	fs.BoolVar(&o.EnableAliasCache, "enable-alias-cache", o.EnableAliasCache, "enable alias cache")
}

// PolicyCacheOptions xxx
type PolicyCacheOptions struct {
	EnablePolicyCache bool
	Prefix            string
}

// NewPolicyCacheOptions xxx
func NewPolicyCacheOptions() *PolicyCacheOptions {
	return &PolicyCacheOptions{
		EnablePolicyCache: true,
		Prefix:            api.PolicyCacheEtcdPrefix,
	}
}

// AddPolicyCacheFlags xxx
func (o *PolicyCacheOptions) AddPolicyCacheFlags(fs *pflag.FlagSet) {
	fs.BoolVar(&o.EnablePolicyCache, "enable-policy-cache", o.EnablePolicyCache, "enable policy cache")
}

// BillingCacheOptions xxx
type BillingCacheOptions struct {
	EnableBillingCache bool
	Prefix             string
}

// NewBillingCacheOptions xxx
func NewBillingCacheOptions() *BillingCacheOptions {
	return &BillingCacheOptions{
		EnableBillingCache: true,
		Prefix:             api.BillingCacheEtcdPrefix,
	}
}

// AddBillingCacheOptions xxx
func (o *BillingCacheOptions) AddBillingCacheFlags(fs *pflag.FlagSet) {
	fs.BoolVar(&o.EnableBillingCache, "enable-billing-cache", o.EnableBillingCache, "enable billing cache")
}

type OTEAuthOptions struct {
	AccessKey string
	SecretKey string
}

func NewOTEAuthOptions() *OTEAuthOptions {
	return &OTEAuthOptions{
		AccessKey: "",
		SecretKey: "",
	}
}

func (o *OTEAuthOptions) AddFlags(fs *pflag.FlagSet) {
	fs.StringVar(&o.AccessKey, "ote-access-key", o.AccessKey, "ote auth access key")
	fs.StringVar(&o.SecretKey, "ote-secret-key", o.SecretKey, "ote auth secret key")
}

// NewEventhubOptions creates a new EventhubOptions object with default parameters
func NewEventhubOptions() *EventhubOptions {
	s := EventhubOptions{
		RecommendedOptions:  genericoptions.NewRecommendedOptions(),
		IAMOptions:          iam.NewIAMOptions(),
		DuerOptions:         auth.NewDuerOptions(),
		ApiserverEndpoint:   "127.0.0.1:8080",
		PoolmanagerEndpoint: "127.0.0.1:8082",
		InvokerOptions: &invoker.InvokerClientOptions{
			Port: 8200,
		},
		RedisOptions:                      kunRedis.NewRedisOptions(),
		DataSourceRedisOptions:            kunRedis.NewRedisOptions(),
		FunctionCacheOptions:              NewFunctionCacheOptions(),
		PolicyCacheOptions:                NewPolicyCacheOptions(),
		BillingCacheOptions:               NewBillingCacheOptions(),
		AliasCacheOptions:                 NewAliasCacheOptions(),
		FuncletConnBackCacheOptions:       etcd.NewFuncletConnBackCacheOptions(),
		RunningMode:                       RunningModeCloud,
		EtcdOptions:                       genericoptions.NewEtcdOptions(),
		Region:                            defaultRegion,
		UserMaxConcurrency:                defaultUserMaxConcurrency,
		DataReportOption:                  report.NewDataReportOption(),
		EventSenderOption:                 report.NewEventSenderOption(),
		BillReportOption:                  report.NewBillReportOption(),
		MqhubClientOption:                 &mqhub.MqhubClientOptions{},
		EnableBillingPermissionCheck:      false,
		WhiteListOptions:                  &whitelist.WhiteListOptions{},
		StsForbiddenListOptions:           &whitelist.WhiteListOptions{},
		OpsCenterEndpoint:                 "http://settings.bce-internal.baidu.com",
		OTEAuthOptions:                    NewOTEAuthOptions(),
		UserAvgLimitConcurrency:           api.DefaultAccountConcurrency,
		EnableSecureContainer:             false,
		VIPUserOptions:                    &whitelist.WhiteListOptions{},
		NodeIsolationUserWhiteListOptions: &whitelist.NodeIsolationUserWhiteListOptions{},
		EnablePodConcurrency:              false,
		IncrUserConcurrency:               0.2,
		ReservedConfig:                    api.NewDefaultReservedConfig(),
		ReservedInstanceEnabled:           false,
		StsUserConfPath:                   "/home/<USER>/faas/eventhub/conf/StsUserConf",
	}
	//s.RecommendedOptions.TelemetryOptions.ServiceName = ApplicationName
	return &s
}

// AddFlags adds flags for a specific APIServer to the specified FlagSet
func (s *EventhubOptions) AddFlags(fs *pflag.FlagSet) {
	s.IAMOptions.AddUniversalFlags(fs)
	s.RecommendedOptions.AddFlags(fs)
	s.DuerOptions.AddFlags(fs)
	s.FunctionCacheOptions.AddFunctionCacheFlags(fs)
	s.AliasCacheOptions.AddAliasCacheFlags(fs)
	s.PolicyCacheOptions.AddPolicyCacheFlags(fs)
	s.BillingCacheOptions.AddBillingCacheFlags(fs)
	s.FuncletConnBackCacheOptions.AddFlags(fs)
	s.RedisOptions.AddRedisFlags(fs)
	s.DataSourceRedisOptions.AddRedisFlagsWithPrefix("ds", fs)
	genericoptions.AddEtcdFlags(s.EtcdOptions, fs)
	s.DataReportOption.AddFlags(fs)
	s.EventSenderOption.AddFlags(fs)
	s.BillReportOption.AddFlags(fs)
	s.MqhubClientOption.AddMqhubFlags(fs)
	s.WhiteListOptions.AddFlags(fs)
	s.NodeIsolationUserWhiteListOptions.AddFlags(fs)
	s.OTEAuthOptions.AddFlags(fs)

	s.EventSenderOption.KafkaOptions = s.DataReportOption.KafkaOptions

	fs.StringVar(&s.ApiserverEndpoint, "apiserver-endpoint", s.ApiserverEndpoint, ""+
		"The endpoint to access api server")

	fs.StringVar(&s.PoolmanagerEndpoint, "poolmanager-endpoint", s.PoolmanagerEndpoint, ""+
		"The endpoint to access pool manager")

	fs.IntVar(&s.InvokerOptions.Port, "invoker-port", s.InvokerOptions.Port, ""+
		"The port to access invoker")

	fs.StringVar(&s.RunningMode, "running-mode", s.RunningMode, "[cloud|duedge|ote]")
	fs.StringVar(&s.Region, "region", s.Region, "[bj|gz]")

	fs.IntVar(&s.UserMaxConcurrency, "user-max-concurrency", s.UserMaxConcurrency, "UserMaxConcurrency")
	fs.BoolVar(&s.EnableBillingPermissionCheck, "enable-billing-permission-check", s.EnableBillingPermissionCheck, "enable-billing-permission-check")

	fs.StringVar(&s.OpsCenterEndpoint, "opscenter-endpoint", s.OpsCenterEndpoint, "BCE opscenter endpoint")

	fs.IntVar(&s.UserAvgLimitConcurrency, "user-avg-limit-concurrency", s.UserAvgLimitConcurrency, "User concurrency limit")
	fs.BoolVar(&s.EnableSecureContainer, "enable-secure-container", s.EnableSecureContainer, "enable-secure-container, default false")
	fs.StringSliceVar(&s.StsForbiddenListOptions.SourceWhiteList, "sts-forbidden-list", s.StsForbiddenListOptions.SourceWhiteList, "sts forbidden list in which the user could not get AK/SK from enviroment")
	fs.StringSliceVar(&s.VIPUserOptions.SourceWhiteList, "vip-users", s.VIPUserOptions.SourceWhiteList, "vip user tags, input like lx:userid")
	fs.BoolVar(&s.EnablePodConcurrency, "enable-pod-concurrency", s.EnablePodConcurrency, "enable-pod-concurrency, default false")
	fs.BoolVar(&s.ReservedInstanceEnabled, "reserved-instance-enabled", s.ReservedInstanceEnabled, "reserved-instance-enabled, default false")
	fs.Float64Var(&s.IncrUserConcurrency, "incr-user-concurrency", s.IncrUserConcurrency, "incr user concurrency, default 0.2")
}

// Complete 对输入的原始参数做处理
func (s *EventhubOptions) Complete() {
	s.OpsCenterEndpoint = "http://" + strings.Trim(s.OpsCenterEndpoint, "http://")
}
