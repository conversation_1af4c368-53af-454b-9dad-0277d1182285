package app

import (
	"net/http"
	"time"

	"icode.baidu.com/baidu/faas/kun/cmd/eventhub/app/options"
	"icode.baidu.com/baidu/faas/kun/pkg/api"
	"icode.baidu.com/baidu/faas/kun/pkg/eventhub"
	"icode.baidu.com/baidu/faas/kun/pkg/eventhub/core"
	"icode.baidu.com/baidu/faas/kun/pkg/eventhub/filters"
	genericserver "icode.baidu.com/baidu/faas/kun/pkg/server"
	"icode.baidu.com/baidu/faas/kun/pkg/util/logs"
	"icode.baidu.com/baidu/faas/kun/pkg/version"
)

// Run runs the specified EventhubServer with the given Dependencies.
// This should never exit.
func Run(runOptions *options.EventhubOptions, stopCh <-chan struct{}) error {
	logs.Infof("Version: %+v", version.Get())

	s, err := CreateServerChain(runOptions, stopCh)
	if err != nil {
		return err
	}

	return s.PrepareRun().Run(stopCh)
}

func GenericServerConfig(runOptions *options.EventhubOptions) (*genericserver.RecommendedConfig, error) {
	config := genericserver.NewRecommendedConfig()
	if err := runOptions.RecommendedOptions.ApplyTo(config); err != nil {
		return nil, err
	}
	return config, nil
}

func CreateServerChainWithEventhub(core *core.EventhubCore, config *genericserver.RecommendedConfig) (*genericserver.GenericServer, error) {
	config.BuildHandlerChainFunc = DecoratedBuildHandlerChain(core)

	s, err := config.Complete().New(options.ApplicationName)
	if err != nil {
		return nil, err
	}

	// Install hook
	// s.AddPostStartHook("demoPostStartHook", eventhub.PostStartHook)
	// s.AddPreShutdownHook("demoPreShutdownHook", eventhub.PreShutdownHook)
	s.AddPreShutdownHook("podCacheCleanupHook", core.Clients.PodCacheClient.Cleanup)
	//s.AddPreShutdownHook("telemetryShutdownHook", config.TelemetryInfo.Stop)

	// Install API
	eventhub.InstallAPI(s.Handler.GoRestfulContainer)

	return s, nil
}

// CreateServerChain creates the apiservers connected via delegation.
func CreateServerChain(runOptions *options.EventhubOptions, stopCh <-chan struct{}) (*genericserver.GenericServer, error) {
	genericServerConfig, err := GenericServerConfig(runOptions)
	if err != nil {
		return nil, err
	}
	core, err := eventhub.Init(runOptions, stopCh)
	if err != nil {
		return nil, err
	}

	genericServerConfig.RequestTimeout = time.Duration(api.MaxRequestTimeout + 40) * time.Second
	return CreateServerChainWithEventhub(core, genericServerConfig)
}

// DecoratedBuildHandlerChain 是默认 BuildHandlerChain 的装饰器，它在默认的基础上
// 增加了eventhub专用的filter
func DecoratedBuildHandlerChain(ec *core.EventhubCore) genericserver.BuildHandlerChainFunc {
	return genericserver.BuildHandlerChainFunc(func(apiHandler http.Handler, c *genericserver.Config) http.Handler {
		handler := filters.WithDemoEventhubFilter(apiHandler, ec)
		return genericserver.DefaultBuildHandlerChain(handler, c)
	})
}
