package main

import (
	"os"

	"github.com/spf13/pflag"

	"icode.baidu.com/baidu/faas/kun/cmd/logstash/app"
	"icode.baidu.com/baidu/faas/kun/pkg/server"
	"icode.baidu.com/baidu/faas/kun/pkg/util/flag"
	"icode.baidu.com/baidu/faas/kun/pkg/util/logs"
	"icode.baidu.com/baidu/faas/kun/pkg/version/verflag"
)

func main() {
	options := app.NewLogstashOptions()
	options.AddFlags(pflag.CommandLine)
	flag.InitFlags()

	logs.InitLogs()
	defer logs.FlushLogs()

	verflag.PrintAndExitIfRequested()
	s, err := app.NewLogstashServer(options)
	if err != nil {
		logs.Error(err.Error())
		return
	}
	if err = s.Run(server.SetupSignalHandler()); err != nil {
		logs.Error(err.Error())
		os.Exit(1)
	}
}
