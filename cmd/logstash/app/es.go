package app

import (
	"errors"
	"fmt"
	"io/ioutil"
	"net/http"
	"net/url"
	"os"
	"time"

	goevents "github.com/docker/go-events"

	"icode.baidu.com/baidu/faas/kun/pkg/cfclog"
	"icode.baidu.com/baidu/faas/kun/pkg/invoker/elastic"
	"icode.baidu.com/baidu/faas/kun/pkg/util/logs"
)

type esLogEntry struct {
	Message   string `json:"message"`
	Requestid string `json:"requestid"`
	Hostname  string `json:"hostname"`
	Timestamp string `json:"timestamp"`
	Sequence  int    `json:"sequence"`
	Source    string `json:"source"`
}

// elastic search
type esLogger struct {
	closed   bool
	hostname string
}

func getIndexName() string {
	now := time.Now().Format("20060102")
	return fmt.Sprintf("cfclog-%s", now)
}

func newEsLogger(config map[string]string) goevents.Sink {
	hostname := config["hostname"]
	return &esLogger{
		closed:   false,
		hostname: hostname,
	}
}

func (r *esLogger) Write(event goevents.Event) error {
	if r.closed {
		return errors.New("esLogger closed")
	}
	evt, ok := event.(*KunLogEvent)
	if !ok {
		return nil
	}
	logev := evt.Entry
	defer os.Remove(logev.LogFile) // 删除文件

	idx := 0
	index := getIndexName()
	builder := elastic.NewBulkBuilder()
	err := translate(logev.LogFile, func(log *cfclog.CfcLog) error {
		if len(log.Message) == 0 {
			return nil
		}
		entry := &esLogEntry{
			Requestid: log.RequestID,
			Message:   string(log.Message),
			Timestamp: log.Created.Format(time.RFC3339),
			Hostname:  r.hostname,
			Sequence:  idx,
			Source:    log.Source,
		}
		builder.WithIndex(index, *(logev.Function.FunctionName), "", entry)
		idx += 1
		return nil
	})

	buffer, err := builder.Build()
	if err != nil {
		logs.Errorf("build bulk failed: %s", err.Error())
		return err
	}
	client := &http.Client{}
	ec := logev.LogConfig
	endpoint, err := url.Parse(ec.Params)
	if err != nil {
		logs.Errorf("parse param %s failed", ec.Params)
		return err
	}
	userinfo := endpoint.User
	endpoint.User = nil
	req, _ := http.NewRequest("POST", endpoint.String()+"/_bulk", buffer)
	if userinfo != nil {
		password, _ := userinfo.Password()
		req.SetBasicAuth(userinfo.Username(), password)
	}
	req.Header.Add("Content-Type", "application/x-ndjson")
	resp, err := client.Do(req)
	if err != nil {
		logs.Errorf("send eslog failed: %s", err.Error())
		return err
	}
	defer resp.Body.Close()
	if resp.StatusCode != http.StatusOK {
		body, _ := ioutil.ReadAll(resp.Body)
		logs.Errorf("send eslog failed. status= %d, body=%s", resp.StatusCode, string(body))
	}
	return nil
}

func (r *esLogger) Close() error {
	r.closed = true
	return nil
}
