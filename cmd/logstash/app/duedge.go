package app

import (
	"errors"
	"net"
	"os"
	"time"

	goevents "github.com/docker/go-events"

	"icode.baidu.com/baidu/faas/kun/pkg/cfclog"
	"icode.baidu.com/baidu/faas/kun/pkg/util/json"
	"icode.baidu.com/baidu/faas/kun/pkg/util/logs"
)

// duedge logger
type duedgeLogger struct {
	hostname string
	endPoint string
	ln       net.Conn
	closed   bool
}

type duedgeLogMsgEntry struct {
	Timestamp time.Time `json:"time"`
	Message   string    `json:"msg"`
	Sequence  int       `json:"seq"`
}

type duedgeLogEntry struct {
	Requestid string              `json:"requestid"`
	Hostname  string              `json:"hostname"`
	Info      []duedgeLogMsgEntry `json:"info"`
}

func newDuedgeLogger(config map[string]string) goevents.Sink {
	hostname := config["hostname"]
	endpoint := config["endpoint"]
	if endpoint == "" {
		return nil
	}
	return &duedgeLogger{
		hostname: hostname,
		endPoint: endpoint,
		closed:   false,
	}
}

func (r *duedgeLogger) dial() error {
	if r.ln == nil {
		conn, err := net.Dial("unix", r.endPoint)
		if err != nil {
			logs.Errorf("logreport reopen %v failed: %v", r.endPoint, err)
			return err
		}
		r.ln = conn
	}
	return nil
}

func (r *duedgeLogger) Write(event goevents.Event) error {
	if r.closed {
		return errors.New("duedgeLogger closed")
	}
	if err := r.dial(); err != nil {
		return err
	}

	// 将日志读取出来，填充成duedgeLogMsgEntry格式。
	evt, ok := event.(*KunLogEvent)
	if !ok {
		return nil
	}
	logev := evt.Entry
	defer os.Remove(logev.LogFile) // 删除文件

	idx := 0
	duedgeLog := duedgeLogEntry{Requestid: logev.RequestID, Hostname: r.hostname}
	err := translate(logev.LogFile, func(log *cfclog.CfcLog) error {
		if len(log.Message) == 0 {
			return nil
		}
		msgEntry := duedgeLogMsgEntry{
			Timestamp: log.Created,
			Message:   string(log.Message),
			Sequence:  idx,
		}
		duedgeLog.Info = append(duedgeLog.Info, msgEntry)
		idx += 1
		return nil
	})
	if err != nil {
		logs.Errorf("translate duedge log failed. %s", err.Error())
		return err
	}
	data, _ := json.Marshal(duedgeLog)
	data = append(data, '\n')

	_, err = r.ln.Write(data)
	if err != nil {
		logs.Errorf("logreport failed: %s", err.Error())
		r.ln.Close()
		r.ln = nil
	}

	return nil
}

func (r *duedgeLogger) Close() error {
	r.closed = true
	return nil
}
