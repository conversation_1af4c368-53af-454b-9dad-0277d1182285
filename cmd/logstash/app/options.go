package app

import (
	"github.com/spf13/pflag"

	genericoptions "icode.baidu.com/baidu/faas/kun/pkg/server/options"
)

const (
	// ApplicationName 指定应用名称
	ApplicationName = "logstash"
)

type LogstashOptions struct {
	RecommendedOptions    *genericoptions.RecommendedOptions
	Region                string
	BosEndpoint           string
	BosWorkerCount        int
	BosRequestKBytes      int
	DuegeEndpoint         string
	FluentdEndpoint       string
	FluentdCbdTag         string
	WithBosLog            bool
	WithDuedgeLog         bool
	WithESLog             bool
	WithKafkaLog          bool
	WithFluentdCbdLog     bool
	WithBlsLog            bool
	BlsEndpoint           string
	BlsBatchRecordsKBytes int
	BlsBatchRecordsLength int
	BlsRecordKBytes       int
	UserLogPath           string
	KafkaConfigFile       string
}

func NewLogstashOptions() *LogstashOptions {
	s := &LogstashOptions{
		RecommendedOptions: genericoptions.NewRecommendedOptions(),
	}
	//s.RecommendedOptions.TelemetryOptions.ServiceName = ApplicationName
	return s
}

func (opts *LogstashOptions) AddFlags(fs *pflag.FlagSet) {
	opts.RecommendedOptions.AddFlags(fs)
	fs.StringVar(&opts.Region, "region", "bj", "服务所在地域")
	fs.StringVar(&opts.BosEndpoint, "bos-log-endpoint", "bj.bcebos.com", "BOS endpoint")
	fs.IntVar(&opts.BosWorkerCount, "bos-worker-count", 20, "BOS日志上传worker数量")
	fs.IntVar(&opts.BosRequestKBytes, "bos-request-kbytes", 2048, "每次请求BOS上传的最大大小")
	fs.StringVar(&opts.DuegeEndpoint, "duedge-log-endpoint", "/var/run/faas/logreport.sock", "duedge endpoint")
	fs.StringVar(&opts.FluentdEndpoint, "fluentd-log-endpoint", "", "fluentd endpoint")
	fs.StringVar(&opts.FluentdCbdTag, "fluentd-cbd-tag", "kafka.cbd_function_invoke_log", "fluentd input http tag for cbd functions")
	fs.BoolVar(&opts.WithBosLog, "enable-boslog", true, "启用bos日志上传")
	fs.BoolVar(&opts.WithDuedgeLog, "enable-duedgelog", true, "启用duedge日志上传")
	fs.BoolVar(&opts.WithESLog, "enable-eslog", false, "启用es日志上传")
	fs.BoolVar(&opts.WithKafkaLog, "enable-kafkalog", false, "启用Kafka传送日志")
	fs.BoolVar(&opts.WithFluentdCbdLog, "enable-fluentd-cbd-log", false, "启用Fluentd转发cbd日志")
	fs.StringVar(&opts.BlsEndpoint, "bls-log-endpoint", "bls-log.bj.baidubce.com", "BLS endpoint")
	fs.BoolVar(&opts.WithBlsLog, "enable-bls-log", true, "启用bls上传日志")
	fs.IntVar(&opts.BlsBatchRecordsKBytes, "bls-batch-records-kbytes", 1024, "每次批量请求BLS上传的最大大小")
	fs.IntVar(&opts.BlsRecordKBytes, "bls-records-kbytes", 512, "上传bls单条日志记录msg最大大小")
	fs.IntVar(&opts.BlsBatchRecordsLength, "bls-batch-records-length", 1000, "每次批量请求BLS上传的最大日志条数")
	fs.StringVar(&opts.UserLogPath, "userlog-path", "/tmp/userlog", "用户日志存储路径")
	fs.StringVar(&opts.KafkaConfigFile, "kafka-config-file", "", "kafka集群配置")
}
