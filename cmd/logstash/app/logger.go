package app

import (
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"os"
	"strings"
	"time"

	goevents "github.com/docker/go-events"

	"icode.baidu.com/baidu/faas/kun/pkg/api"
	"icode.baidu.com/baidu/faas/kun/pkg/cfclog"
	"icode.baidu.com/baidu/faas/kun/pkg/util/logs"
)

type KunLogType int

var (
	defaultCommitInterval = 10 * time.Second
)

const (
	KunLogEntry  KunLogType = 1
	KunLogCommit KunLogType = 2
)

type KunLogger interface {
	Write(log *api.UserRequestLog) error
	Commit() error // 轮转日志
	Close() error
}

type KunLogEvent struct {
	Type  KunLogType
	Entry *api.UserRequestLog
}

type kunLoggerImpl struct {
	queue *goevents.Queue
}

func NewLogger(sink goevents.Sink) KunLogger {
	return &kunLoggerImpl{
		queue: goevents.NewQueue(sink),
	}
}

func (l *kunLoggerImpl) Write(log *api.UserRequestLog) error {
	event := &KunLogEvent{
		Type:  KunLogEntry,
		Entry: log,
	}
	return l.queue.Write(event)
}

func (l *kunLoggerImpl) Commit() error {
	event := &KunLogEvent{
		Type: KunLogCommit,
	}
	return l.queue.Write(event)
}

func (l *kunLoggerImpl) Close() error {
	return l.queue.Close()
}

type KunLogFactory struct {
	closech chan interface{}
	logger  map[string]KunLogger
	logpath string
}

func NewKunLogFactory(opts *LogstashOptions) (*KunLogFactory, error) {
	host, err := os.Hostname()
	if err != nil {
		return nil, err
	}
	f := &KunLogFactory{
		closech: make(chan interface{}),
		logger:  make(map[string]KunLogger),
		logpath: opts.UserLogPath,
	}
	if opts.WithBosLog {
		defaultMaxBytesPerCommit = int64(opts.BosRequestKBytes * 1024)
		config := map[string]string{
			"hostname": host,
			"region":   opts.Region,
			"endpoint": opts.BosEndpoint,
			"datadir":  opts.UserLogPath,
			"workers":  fmt.Sprintf("%d", opts.BosWorkerCount),
		}
		logger := newBosLogger(config)
		f.logger["bos"] = NewLogger(logger)
	}
	if opts.WithBlsLog {
		defaultBlsBatchRecordsKBytes = int64(opts.BlsBatchRecordsKBytes * 1024)

		if opts.BlsRecordKBytes > MaxBoundaryBytes {
			MaxFunctionPrintLogBytes = opts.BlsRecordKBytes - MaxBoundaryBytes
		}
		defaultBlsBatchRecordsLength = opts.BlsBatchRecordsLength
		config := map[string]string{
			"hostname": host,
			"region":   opts.Region,
			"endpoint": opts.BlsEndpoint,
		}
		logger := newBlsLogger(config)
		f.logger["bls"] = NewLogger(logger)
	}
	if opts.WithESLog {
		config := map[string]string{
			"hostname": host,
		}
		logger := newEsLogger(config)
		f.logger["es"] = NewLogger(logger)
	}
	if opts.WithDuedgeLog {
		config := map[string]string{
			"hostname": host,
			"endpoint": opts.DuegeEndpoint,
		}
		logger := newEsLogger(config)
		f.logger["duedge"] = NewLogger(logger)
	}
	if opts.WithFluentdCbdLog {
		config := map[string]string{
			"endpoint": opts.FluentdEndpoint,
			"tag":      opts.FluentdCbdTag,
		}
		logger := newFluentdLogger(config)
		f.logger["fluentd_kafka"] = NewLogger(logger)
	}
	if opts.WithKafkaLog {
		config := map[string]string{
			"hostname":   host,
			"configfile": opts.KafkaConfigFile,
		}
		logger, err := newKafkaLogger(config)
		if err != nil {
			logs.Errorf("create kafka logger failed. %v", err)
		} else {
			f.logger["kafka"] = NewLogger(logger)
		}
	}
	go f.rotateLog()
	return f, nil
}

func (f *KunLogFactory) rotateLog() {
	// 每10s触发一次Commit事件
	ticker := time.Tick(defaultCommitInterval)
	runing := true
	for runing {
		select {
		case <-ticker:
			for _, l := range f.logger {
				l.Commit()
			}
		case <-f.closech:
			runing = false
		}
	}
}

func (f *KunLogFactory) Dispatch(log *api.UserRequestLog) error {
	logType := ""
	if !strings.HasPrefix(log.LogFile, f.logpath) {
		return fmt.Errorf("logfile %s not in %s directory.", log.LogFile, f.logpath)
	}
	if log.LogConfig != nil {
		logType = log.LogConfig.LogType
	}
	if len(logType) <= 0 {
		os.Remove(log.LogFile)
		return errors.New("missing logtype")
	}
	logger, ok := f.logger[logType]
	if !ok {
		os.Remove(log.LogFile)
		return fmt.Errorf("unsupport logtype: %s", logType)
	}
	err := logger.Write(log)
	if err != nil {
		os.Remove(log.LogFile)
		return err
	}
	return nil
}

func (f *KunLogFactory) Close() {
	close(f.closech)
}

// 转换日志格式
func translate(fpath string, cb func(log *cfclog.CfcLog) error) error {
	file, err := os.Open(fpath)
	if err != nil {
		return err
	}
	defer file.Close()

	decoder := json.NewDecoder(file)
	var log cfclog.CfcLog
	for {
		log.Reset()
		err = decoder.Decode(&log)
		if err != nil {
			if err == io.EOF {
				return nil
			}
			return err
		}
		err = cb(&log)
		if err != nil {
			return err
		}
	}
}
