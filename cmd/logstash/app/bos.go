package app

import (
	"crypto/md5"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"os"
	"strconv"
	"strings"
	"time"

	"github.com/baidubce/bce-sdk-go/bce"

	"icode.baidu.com/baidu/faas/kun/pkg/bce/sts_credential"
	"icode.baidu.com/baidu/faas/kun/pkg/util/logs"

	boscli "github.com/baidubce/bce-sdk-go/services/bos"
	bosapi "github.com/baidubce/bce-sdk-go/services/bos/api"
	"github.com/boltdb/bolt"
	goevents "github.com/docker/go-events"

	"icode.baidu.com/baidu/faas/kun/pkg/api"
)

var (
	defaultBosRetryCount     = 2
	defaultRotateInterval    = 10 * time.Minute
	defaultMaxBytesPerCommit = int64(10 * 1024 * 1024) // 10MB
)

type BosClient interface {
	AppendObject(bucketName, objectKey string, offset int64, data []byte) (*bosapi.AppendObjectResult, error)
	GetObjectMeta(bucketName, objectKey string) (*bosapi.GetObjectMetaResult, error)
}

type kunBosClient struct {
	ststoken string
	client   *boscli.Client
}

func (c *kunBosClient) AppendObject(bucketName, objectKey string, offset int64, data []byte) (*bosapi.AppendObjectResult, error) {
	body, err := bce.NewBodyFromBytes(data)
	if err != nil {
		return nil, err
	}
	args := &bosapi.AppendObjectArgs{
		Offset: offset,
	}
	return c.client.AppendObject(bucketName, objectKey, body, args)
}

func (c *kunBosClient) GetObjectMeta(bucketName, objectKey string) (*bosapi.GetObjectMetaResult, error) {
	return c.client.GetObjectMeta(bucketName, objectKey)
}

func kunBosClientFactory(ak, sk, token, region, endpoint string) BosClient {
	logs.V(8).Infof("create bosclient ak=%s sk=%s region=%s endpoint=%s", ak, sk, region, endpoint)
	bosClient, err := boscli.NewClient(ak, sk, endpoint)
	if err != nil {
		logs.Errorf("create bos client failed, %s", err.Error())
		return nil
	}
	bosClient.Config.Credentials.SessionToken = token // 设置sts token

	return &kunBosClient{
		client: bosClient,
	}
}

type bosLogger struct {
	ticktime     int64
	bosWriterMap map[string]goevents.Sink
	hostname     string
	region       string
	endpoint     string
	datadir      string
	uploader     goevents.Sink
	newWriter    func(bosuri, objname, region, endpoint, datadir string, uploader goevents.Sink) (goevents.Sink, error)
}

func newBosLogger(config map[string]string) goevents.Sink {
	uploader := newBosLogUploader(config["datadir"])

	l := &bosLogger{
		hostname:     config["hostname"],
		region:       config["region"],
		endpoint:     config["endpoint"],
		datadir:      config["datadir"],
		newWriter:    newBosLogWriter,
		uploader:     goevents.NewQueue(uploader),
		bosWriterMap: make(map[string]goevents.Sink),
	}
	count, _ := strconv.Atoi(config["workers"])
	uploader.startWorker(l.uploader, count)
	return l
}

func (l *bosLogger) closeWriter() {
	writers := l.bosWriterMap
	go func() {
		for _, w := range writers {
			w.Close()
		}
	}()
	l.bosWriterMap = make(map[string]goevents.Sink)
}

func getPeriod() time.Time {
	now := time.Now()
	return time.Date(now.Year(), now.Month(), now.Day(),
		now.Hour(), now.Minute()/10*10, 0, 0, now.Location())
}

func (l *bosLogger) Write(event goevents.Event) error {
	evt, ok := event.(*KunLogEvent)
	if !ok {
		return nil
	}

	// 每10分钟切割一次日志
	period := getPeriod()
	if evt.Type == KunLogCommit {
		for _, w := range l.bosWriterMap {
			w.Write(event)
		}
		if period.Unix() != l.ticktime { // 切分日志
			l.closeWriter() // 关闭旧的writer
			l.ticktime = period.Unix()
		}
		return nil
	}

	logev := evt.Entry
	// 根据function arn查找对应对Sink
	funcArn := *(logev.Function.FunctionArn)
	key := fmt.Sprintf("%s-%s", funcArn, logev.LogConfig.BosDir)
	sink, ok := l.bosWriterMap[key]
	if !ok {
		// 生成object name
		logger, err := l.newWriter(logev.LogConfig.BosDir,
			getObjectName(logev.Function, l.hostname, period),
			l.region, l.endpoint, l.datadir, l.uploader)
		if err != nil {
			logs.Error(err.Error())
		} else {
			sink = goevents.NewQueue(logger)
			l.bosWriterMap[key] = sink
		}
	}
	if sink != nil {
		return sink.Write(event)
	}
	return nil
}

func (l *bosLogger) Close() error {
	l.closeWriter()
	return nil
}

const (
	logfileBucket = "logfile"
)

type logAppendRequest struct {
	SeqKey     int64                         `json:"seqkey"`  // 根据此key进行排序
	Bucket     string                        `json:"bucket"`  // bucket
	Object     string                        `json:"object"`  // bos对象
	Logfile    []string                      `json:"logfile"` // 日志文件全路径
	Length     int64                         `json:"length"`  // 日志文件长度
	Offset     *int64                        `json:"-"`
	Region     string                        `json:"region"`
	Endpoint   string                        `json:"endpoint"`
	Credential *sts_credential.StsCredential `json:"credential"` // sts token
}

func (r *logAppendRequest) ObjKey() string {
	return fmt.Sprintf("bos://%s/%s", r.Bucket, r.Object)
}

func (r *logAppendRequest) FileKey() string {
	return fmt.Sprintf("bos://%s/%s/%d", r.Bucket, r.Object, r.SeqKey)
}

type logBosObjectInfo struct {
	Bucket   string
	Object   string
	Expire   time.Time
	Objfiles []string
	Offset   int64
}

type bosLogDataStore struct {
	db *bolt.DB
}

func newBosLogDataStore(dbfile string) (*bosLogDataStore, error) {
	db, err := bolt.Open(dbfile, 0640, nil)
	if err != nil {
		return nil, err
	}
	err = db.Update(func(tx *bolt.Tx) error {
		if _, err := tx.CreateBucketIfNotExists([]byte(logfileBucket)); err != nil {
			return err
		}
		return nil
	})
	return &bosLogDataStore{
		db: db,
	}, nil
}

func (s *bosLogDataStore) loadLogfileMap() (map[string]*logAppendRequest, error) {
	logmap := make(map[string]*logAppendRequest)
	err := s.db.View(func(tx *bolt.Tx) error {
		b := tx.Bucket([]byte(logfileBucket))
		return b.ForEach(func(k, v []byte) error {
			var req logAppendRequest
			if err := json.Unmarshal(v, &req); err != nil {
				return err
			}
			logmap[string(k)] = &req
			return nil
		})
	})
	if err != nil {
		return nil, err
	}
	return logmap, nil
}

func (s *bosLogDataStore) createLogfile(fkey string, req *logAppendRequest) error {
	return s.db.Update(func(tx *bolt.Tx) error {
		b := tx.Bucket([]byte(logfileBucket))
		buf, err := json.Marshal(req)
		if err != nil {
			return err
		}
		return b.Put([]byte(fkey), buf)
	})
}

func (s *bosLogDataStore) deleteLogfile(fkey string) error {
	return s.db.Update(func(tx *bolt.Tx) error {
		b := tx.Bucket([]byte(logfileBucket))
		return b.Delete([]byte(fkey))
	})
}

func (s *bosLogDataStore) close() {
	if s.db != nil {
		s.db.Close()
		s.db = nil
	}
}

type logAppendEvent struct {
	Request *logAppendRequest
	Offset  int
}

type logAppendWorker struct {
	notify  goevents.Sink
	factory func(ak, sk, token, region, endpoint string) BosClient
	reqsCh  chan *logAppendRequest
}

func (w *logAppendWorker) run() {
	for {
		select {
		case req, ok := <-w.reqsCh:
			if !ok {
				return
			}
			err := w.appendLog(req)
			if err != nil {
				logs.Error(err.Error())
			}
		}
	}
}

func readFiles(files []string, buffer []byte) (int, error) {
	length := len(buffer)
	offset := 0
	var readerr error
	for _, f := range files {
		file, err := os.Open(f)
		if err != nil { // 跳过无法读取文件
			logs.Warnf("discard logfile %s. open failed: %v", f, err.Error())
			continue
		}
		startat := offset
		readerr = nil
		for readerr == nil && offset < length {
			nread, err := file.Read(buffer[offset:])
			if err != nil {
				if err == io.EOF {
					break
				}
				file.Close()
				readerr = err
			} else {
				offset += nread
			}
		}
		// 该文件读取失败，跳过
		if readerr != nil {
			logs.Warnf("discard logfile %s. read error: %v", f, readerr.Error())
			offset = startat
		}
		file.Close()
	}

	return offset, nil
}

func (w *logAppendWorker) appendLog(req *logAppendRequest) error {
	offset := int64(1) // 如果填0，会导致覆盖
	if req.Offset != nil {
		offset = *req.Offset
	}
	t1 := time.Now()
	prevoff := offset
	defer func() {
		t2 := time.Now()
		logs.V(6).Infof("upload %d logfile(s) length=%d to %s cost %f offset=%d prevoff=%d",
			len(req.Logfile), req.Length, req.ObjKey(), t2.Sub(t1).Seconds(), offset, prevoff)
		for _, f := range req.Logfile {
			os.Remove(f) // 删除文件
		}
		if w.notify != nil {
			req.Offset = &offset
			w.notify.Write(req)
		}
	}()

	length := (req.Length + 1024) / 1024 * 1024
	buffer := make([]byte, length, length)
	nread, err := readFiles(req.Logfile, buffer)
	if err != nil {
		return err
	}
	buffer = buffer[0:nread]

	credential := req.Credential
	client := w.factory(credential.AccessKeyId, credential.AccessKeySecret,
		credential.SessionToken, req.Region, req.Endpoint)
	for i := 0; i < defaultBosRetryCount; i++ {
		//retry 1 times
		rsp, err := client.AppendObject(req.Bucket, req.Object, offset, buffer)
		if err != nil {
			if serr, ok := err.(*bce.BceServiceError); ok {
				logs.Warnf("upload log to bos failed. err: %s", err.Error())
				if serr.Code == "NoSuchKey" {
					//if user delete the object
					offset = 0
					continue
				} else if serr.Code == "OffsetIncorrect" {
					//if offset error
					res, err := client.GetObjectMeta(req.Bucket, req.Object)
					if err != nil {
						return fmt.Errorf("GetObjectMeta failed. err: %s", err.Error())
					}
					if res != nil {
						offset = res.ContentLength
					} else {
						//object not existed
						offset = 0
					}
					continue
				} else {
					//don't retry ,return directly
					return fmt.Errorf("upload log to bos failed. err: %s", err.Error())
				}
			} else {
				return fmt.Errorf("upload log to bos failed. err: %s", err.Error())
			}
		}
		offset = rsp.NextAppendOffset
		if offset < 0 {
			offset = 0
		}
		break
	}
	return nil
}

// 上传日志，支持oom后续传
type bosLogUploader struct {
	objectMap  map[string]*logBosObjectInfo
	logfileMap map[string]*logAppendRequest
	store      *bosLogDataStore
	reqsCh     chan *logAppendRequest
	closed     bool
}

func newBosLogUploader(datadir string) *bosLogUploader {
	dbfile := fmt.Sprintf("%s/bos.db", datadir)
	store, err := newBosLogDataStore(dbfile)
	if err != nil {
		logs.Errorf("open %s failed.", dbfile)
	}

	u := &bosLogUploader{
		store:  store,
		reqsCh: make(chan *logAppendRequest, 1000),
	}
	if err = u.restoredb(); err != nil {
		store.close()
		os.Remove(dbfile)
		u.store = nil
	}
	return u
}

func (u *bosLogUploader) startWorker(notify goevents.Sink, count int) {
	// 创建upload worker
	for i := 0; i < count; i++ {
		wrk := &logAppendWorker{
			factory: kunBosClientFactory,
			notify:  notify,
			reqsCh:  u.reqsCh,
		}
		go wrk.run()
	}
}

func (u *bosLogUploader) restoredb() error {
	if u.store == nil {
		return nil
	}
	logmap, err := u.store.loadLogfileMap()
	if err != nil {
		u.objectMap = make(map[string]*logBosObjectInfo)
		u.logfileMap = make(map[string]*logAppendRequest)
		return err
	}

	now := time.Now()
	objmap := make(map[string]*logBosObjectInfo)
	for k, v := range logmap {
		if v.Credential.Expiration.Before(now) {
			delete(logmap, k)
			u.store.deleteLogfile(k)
		} else {
			okey := v.ObjKey()
			o, ok := objmap[okey]
			if ok {
				o.Objfiles = append(o.Objfiles, v.FileKey())
			} else {
				o := &logBosObjectInfo{
					Bucket:   v.Bucket,
					Object:   v.Object,
					Expire:   time.Now().Add(1 * time.Hour), // 1h后超时
					Objfiles: []string{v.FileKey()},
					Offset:   1, // 触发一次meta查询
				}
				objmap[okey] = o
			}
		}
	}
	u.objectMap = objmap
	u.logfileMap = logmap
	return nil
}

func (u *bosLogUploader) startAppend(info *logBosObjectInfo) {
	if len(info.Objfiles) == 0 {
		return
	}
	first := info.Objfiles[0]
	req, _ := u.logfileMap[first]
	offset := info.Offset
	req.Offset = &offset
	logs.V(6).Infof("append %s  start offset=%d.", req.FileKey(), offset)
	// 取worker
	u.reqsCh <- req
}

func (u *bosLogUploader) onAppendRequest(r *logAppendRequest) error {
	if u.closed {
		return errors.New("bos log appender closed")
	}

	okey := r.ObjKey()
	fkey := r.FileKey()
	// 记录至map
	info, exist := u.objectMap[okey]
	if exist {
		info.Objfiles = append(info.Objfiles, fkey)
	} else {
		info = &logBosObjectInfo{
			Bucket:   r.Bucket,
			Object:   r.Object,
			Expire:   time.Now().Add(1 * time.Hour),
			Objfiles: []string{fkey},
			Offset:   0,
		}
		u.objectMap[okey] = info
	}
	u.logfileMap[fkey] = r

	if u.store != nil {
		if err := u.store.createLogfile(fkey, r); err != nil {
			logs.Warn(err.Error())
		}
	}
	// 第一个请求，启动传输
	if len(info.Objfiles) == 1 {
		u.startAppend(info)
	}
	return nil
}

func (u *bosLogUploader) onAppendFinish(r *logAppendRequest) error {
	okey := r.ObjKey()
	fkey := r.FileKey()

	// 删除map中数据
	delete(u.logfileMap, fkey)
	if u.store != nil {
		if err := u.store.deleteLogfile(fkey); err != nil {
			logs.Warn(err.Error())
		}
	}

	info, exist := u.objectMap[okey]
	if exist {
		info.Offset = *r.Offset
		info.Objfiles = info.Objfiles[1:]
	}

	u.cleanup()

	// 存在后续请求，启动传输
	if exist {
		u.startAppend(info)
	}
	return nil
}

func (u *bosLogUploader) cleanup() {
	// 清理已经过期的传输
	now := time.Now()
	for k, v := range u.objectMap {
		if v.Expire.Before(now) {
			logs.V(6).Infof("cleanup expired object %s.", k)
			delete(u.objectMap, k)
			for _, f := range v.Objfiles {
				logs.V(6).Infof("cleanup expired file %s.", f)
				delete(u.logfileMap, f)
			}
		}
	}
}

func (u *bosLogUploader) Write(event goevents.Event) error {
	req, _ := event.(*logAppendRequest)
	if req.Offset == nil {
		return u.onAppendRequest(req)
	}
	return u.onAppendFinish(req)
}

func (u *bosLogUploader) Close() error {
	u.closed = true
	close(u.reqsCh)
	return nil
}

// bos最大能创建100个bucket，所以bucket由用户进行指定
type bosLogWriter struct {
	bucket   string
	objname  string
	offset   int
	region   string
	endpoint string
	logdir   string
	closed   bool
	uploader goevents.Sink

	// 暂存的日志
	lastcommit time.Time
	pending    []string // 暂存日志名称
	length     int64    // 暂存日志长度
	Credential *sts_credential.StsCredential
}

func newBosLogWriter(bosuri, objname, region, endpoint, logdir string, uploader goevents.Sink) (goevents.Sink, error) {
	invalidBosUri := errors.New(fmt.Sprintf("invalid bos uri: %s", bosuri))
	if !strings.HasPrefix(bosuri, "bos://") {
		return nil, invalidBosUri
	}
	parts := strings.SplitN(bosuri[6:], "/", 2)
	if len(parts) < 1 || len(parts[0]) < 1 {
		return nil, invalidBosUri
	}
	if len(parts) == 2 {
		temp := parts[1]
		if len(temp) > 0 {
			if strings.HasSuffix(temp, "/") {
				objname = fmt.Sprintf("%s%s", temp, objname)
			} else {
				objname = fmt.Sprintf("%s/%s", temp, objname)
			}
		}
	}
	tmpdir := fmt.Sprintf("%s/%s", logdir, "merged")
	if _, err := os.Stat(tmpdir); os.IsNotExist(err) {
		os.Mkdir(tmpdir, 0777)
	}

	return &bosLogWriter{
		bucket:     parts[0],
		objname:    objname,
		region:     region,
		endpoint:   endpoint,
		logdir:     logdir,
		lastcommit: time.Now(),
		uploader:   uploader,
		length:     0,
	}, nil
}

// funcName.version.md5(functionArn/hostname/period)
func getObjectName(config *api.FunctionConfiguration, hostname string, period time.Time) string {
	temp := fmt.Sprintf("%s/%s/%d", *(config.FunctionArn), hostname, period.Unix())
	data := md5.Sum([]byte(temp))
	md5str := fmt.Sprintf("%x", data)
	tmstr := period.Format("200601021504")
	funcName := ""
	if config.FunctionName != nil {
		funcName = *(config.FunctionName)
	}
	funcVer := ""
	if config.Version != nil {
		funcVer = *(config.Version)
	}
	return fmt.Sprintf("%s/%s_%s_%s", funcName, funcVer, tmstr, md5str)
}

func (r *bosLogWriter) doAppend() error {
	if len(r.pending) == 0 {
		return nil
	}
	req := &logAppendRequest{
		SeqKey:     time.Now().UnixNano(),
		Bucket:     r.bucket,
		Object:     r.objname,
		Region:     r.region,
		Endpoint:   r.endpoint,
		Logfile:    r.pending,
		Length:     r.length,
		Credential: r.Credential,
	}
	if r.uploader != nil {
		r.uploader.Write(req)
	} else {
		for _, f := range r.pending {
			os.Remove(f)
		}
	}
	r.pending = []string{}
	r.length = 0
	r.lastcommit = time.Now()
	return nil
}

func (r *bosLogWriter) needCommit() bool {
	if len(r.pending) == 0 {
		r.lastcommit = time.Now()
		return false
	}
	if time.Now().Sub(r.lastcommit) > defaultCommitInterval ||
		r.length >= defaultMaxBytesPerCommit {
		return true
	}
	return false
}

func (r *bosLogWriter) Write(event goevents.Event) error {
	if r.closed {
		return errors.New("boslogger closed")
	}
	evt, ok := event.(*KunLogEvent)
	if !ok {
		return nil
	}
	if evt.Type == KunLogCommit {
		if r.needCommit() {
			r.doAppend()
		}
		return nil
	}
	logev := evt.Entry
	if logev.LogFile == "" {
		return errors.New("filePath is empty")
	}

	if logev.Credential == nil {
		logs.Warnf("%s credential is null", logev.RequestID)
		return errors.New("credential is null")
	}
	if time.Now().After(logev.Credential.Expiration) {
		logs.Warnf("%s credential is expired", logev.RequestID)
		return errors.New("credential is expired")
	}

	stat, err := os.Stat(logev.LogFile)
	if err != nil {
		logs.Warnf("%s stat file failed: %s", logev.RequestID, err.Error())
		return errors.New("stat file failed")
	}
	if stat.Size() == 0 {
		os.Remove(logev.LogFile)
		return nil
	}

	r.pending = append(r.pending, logev.LogFile)
	r.length += stat.Size()

	// 将日志写入bos格式日志文件，然后删除
	r.Credential = logev.Credential

	if r.needCommit() {
		r.doAppend()
	}
	return nil
}

func (r *bosLogWriter) Close() error {
	r.closed = true
	r.doAppend() // flush剩余日志
	return nil
}
