package app

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestNewKunLogFactory(t *testing.T) {
	opt := LogstashOptions{
		Region:            "bj",
		BosEndpoint:       "bj.bcebos.com",
		BosWorkerCount:    2,
		BosRequestKBytes:  100,
		FluentdEndpoint:   "fluentd-endpoint.com",
		FluentdCbdTag:     "functions.cbd-tag",
		WithBosLog:        true,
		WithDuedgeLog:     true,
		WithESLog:         true,
		WithKafkaLog:      false,
		WithFluentdCbdLog: true,
	}
	_, err := NewKunLogFactory(&opt)
	assert.Nil(t, err)
}
