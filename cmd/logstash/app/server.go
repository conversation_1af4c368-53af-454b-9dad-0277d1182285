package app

import (
	"net/http"

	"github.com/emicklei/go-restful"

	"icode.baidu.com/baidu/faas/kun/pkg/api"
	kunErr "icode.baidu.com/baidu/faas/kun/pkg/error"
	"icode.baidu.com/baidu/faas/kun/pkg/server"
	"icode.baidu.com/baidu/faas/kun/pkg/server/endpoint"
	"icode.baidu.com/baidu/faas/kun/pkg/util/logs"
	"icode.baidu.com/baidu/faas/kun/pkg/version"
)

type LogstashServer struct {
	config  *LogstashOptions
	factory *KunLogFactory
}

func NewLogstashServer(opts *LogstashOptions) (*LogstashServer, error) {
	f, err := NewKunLogFactory(opts)
	if err != nil {
		return nil, err
	}
	return &LogstashServer{config: opts, factory: f}, nil
}

func (lss *LogstashServer) reportLog(c *server.Context) {
	request := c.Request()
	response := c.Response()

	var logev api.UserRequestLog
	err := request.ReadEntity(&logev)
	if err != nil {
		c.WithErrorLog(kunErr.NewInvalidRequestContentException("decode request body failed", err)).WriteTo(response)
		return
	}

	err = lss.factory.Dispatch(&logev)
	if err != nil {
		c.WithErrorLog(kunErr.NewValidationException("invalid request", "invalid request", err)).WriteTo(response)
		return
	}
	response.WriteHeader(http.StatusOK)
}

func (lss *LogstashServer) installApis(container *restful.Container) {
	lssApis := []endpoint.ApiSingle{
		{
			Verb:    "POST",
			Path:    "/reportLog",
			Handler: server.WrapRestRouteFunc(lss.reportLog),
			Filters: []restful.FilterFunction{},
		},
	}

	installer := endpoint.NewApiInstaller([]endpoint.ApiVersion{
		{
			Prefix: "/v1/logstash",
			Group:  lssApis,
		},
	})
	installer.Install(container)
}

func (lss *LogstashServer) Run(stopCh <-chan struct{}) error {
	logs.V(4).Infof("logstash Version: %+v", version.Get())
	s, err := CreateServerChain(lss.config, lss)
	if err != nil {
		return err
	}
	return s.PrepareRun().Run(stopCh)
}

func CreateServerChain(opts *LogstashOptions, lss *LogstashServer) (*server.GenericServer, error) {
	config := server.NewRecommendedConfig()
	if err := opts.RecommendedOptions.ApplyTo(config); err != nil {
		return nil, err
	}
	s, err := config.Complete().New(ApplicationName)
	if err != nil {
		return nil, err
	}
	lss.installApis(s.Handler.GoRestfulContainer)
	return s, nil
}
