package app

import (
	"bytes"
	"encoding/json"
	"errors"
	"fmt"
	"io/ioutil"
	"net/http"
	"net/http/httptest"
	"strings"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"

	"icode.baidu.com/baidu/faas/kun/pkg/api"
	"icode.baidu.com/baidu/faas/kun/pkg/bce/sts_credential"
	"icode.baidu.com/baidu/faas/kun/pkg/cfclog"
	_ "icode.baidu.com/baidu/faas/kun/pkg/cfclog/json"
)

func newFluentdLogFile() string {
	directory, _ := ioutil.TempDir("", "cfclog")
	logfile := fmt.Sprintf("%s/%s", directory, "request.log")
	writer, err := cfclog.CreateLogWriter("fluentd_kafka", logfile, 4096)
	fmt.Println(err)
	entry := &cfclog.CfcLog{
		Created:   time.Now(),
		RequestID: "request-id",
		Source:    "stdout",
	}
	for i := 1; i < 5; i++ {
		writer.Write(entry, []byte("log message line"))
	}
	writer.Close()
	return logfile
}

func TestFluentdLogger(t *testing.T) {
	ass := assert.New(t)
	tests := []struct {
		name    string
		event   *KunLogEvent
		fluentd *httptest.Server
		err     error
	}{
		{
			name: "normal",
			event: &KunLogEvent{
				Type: KunLogEntry,
				Entry: &api.UserRequestLog{
					RequestID: "request-id",
					RuntimeID: "runtime-id",
					Function:  &api.FunctionConfiguration{},
					LogFile:   newFluentdLogFile(),
					Credential: &sts_credential.StsCredential{
						AccessKeyId:     "ak",
						AccessKeySecret: "sk",
					},
					LogConfig: &api.LogConfiguration{
						LogType: "fluentd_kafka",
						Params:  "topic=test_topic",
					},
					Result: &api.InvokeResult{},
				},
			},
			fluentd: httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
				w.WriteHeader(http.StatusOK)
				w.Write([]byte("{}"))
			})),
			err: nil,
		}, {
			name: "retry",
			event: &KunLogEvent{
				Type: KunLogEntry,
				Entry: &api.UserRequestLog{
					RequestID: "request-id",
					RuntimeID: "runtime-id",
					Function:  &api.FunctionConfiguration{},
					LogFile:   newFluentdLogFile(),
					Credential: &sts_credential.StsCredential{
						AccessKeyId:     "ak",
						AccessKeySecret: "sk",
					},
					LogConfig: &api.LogConfiguration{
						LogType: "fluentd_kafka",
						Params:  "topic=test_topic",
					},
					Result: &api.InvokeResult{},
				},
			},
			fluentd: func() *httptest.Server {
				retry := 0
				return httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
					if retry > 0 {
						w.WriteHeader(http.StatusOK)
						w.Write([]byte("{}"))
					} else {
						w.WriteHeader(http.StatusInternalServerError)
						w.Write([]byte("error body"))
					}
					retry++
				}))
			}(),
			err: nil,
		}, {
			name: "error-no-credential",
			event: &KunLogEvent{
				Type: KunLogEntry,
				Entry: &api.UserRequestLog{
					RequestID:  "request-id",
					RuntimeID:  "runtime-id",
					Function:   &api.FunctionConfiguration{},
					LogFile:    newFluentdLogFile(),
					Credential: nil,
					LogConfig: &api.LogConfiguration{
						LogType: "fluentd_kafka",
						Params:  "topic=test_topic",
					},
					Result: &api.InvokeResult{},
				},
			},
			fluentd: httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
				w.WriteHeader(http.StatusOK)
				w.Write([]byte("{}"))
			})),
			err: errors.New("entry credential is empty"),
		},
	}

	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			logger := newFluentdLogger(map[string]string{
				"endpoint": test.fluentd.URL,
				"tag":      "kafka.function_invoke_log",
			})
			err := logger.Write(test.event)
			if test.err != nil {
				ass.Error(err, "should error")
				ass.EqualErrorf(err, test.err.Error(), "err mismatch")
			} else {
				ass.NoError(err, "should no error")
			}
		})
	}
}

func TestEncodeFluentLog(t *testing.T) {
	entry := &api.FluentdLogEntry{
		RequestID: "request-123",
		RuntimeID: "runtime-123",
		Function:  &api.FunctionConfiguration{},
		Result:    &api.InvokeResult{},
	}

	buffer := bytes.NewBuffer(nil)
	encoder := json.NewEncoder(buffer)
	err := encoder.Encode(entry)
	assert.Nil(t, err)

	assert.Equal(t, -1, strings.Index(buffer.String(), "invoke_start_time"))
}
