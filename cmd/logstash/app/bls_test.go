package app

import (
	"os"
	"testing"

	"github.com/aws/aws-sdk-go/service/lambda"
	"icode.baidu.com/baidu/faas/kun/pkg/api"
)

func TestBlsLogWriter(t *testing.T) {
	config := make(map[string]string)
	config["hostname"] = "host"
	config["endpoint"] = "host"
	config["region"] = "host"
	l := newBlsLogger(config)

	log := &api.UserRequestLog{
		RequestID: "867cf8ad-fafa-477c-ad2c-d8a51f08da7f",
		Result: &api.InvokeResult{
			Status:   "success",
			MemUsed:  1,
			Duration: 1,
		},
		Function: &api.FunctionConfiguration{
			FunctionConfiguration: lambda.FunctionConfiguration{
				FunctionArn:  stringp("function_arn"),
				FunctionName: stringp("test_func"),
				Version:      stringp("$LATEST"),
			},
		},
		LogConfig: &api.LogConfiguration{
			LogType:   "bls",
			BlsLogSet: "default",
		},
		LogFile: "test.json",
	}
	event := &KunLogEvent{
		Type:  KunLogEntry,
		Entry: log,
	}

	//567bytes
	fi, _ := os.Create(event.Entry.LogFile)
	fi.WriteString(`{"msg":"START RequestId: 867cf8ad-fafa-477c-ad2c-d8a51f08da7f","src":"cfc","rid":"867cf8ad-fafa-477c-ad2c-d8a51f08da7f","ts":"2021-11-25T16:49:39.695167656+08:00"}`)
	fi.WriteString("\n")
	fi.WriteString(`{"msg":"END RequestId: 867cf8ad-fafa-477c-ad2c-d8a51f08da7f","src":"cfc","rid":"867cf8ad-fafa-477c-ad2c-d8a51f08da7f","ts":"2021-11-25T16:49:39.695167656+08:00"}`)
	fi.WriteString("\n")

	fi.WriteString(`{"msg":"REPORT RequestId: 867cf8ad-fafa-477c-ad2c-d8a51f08da7f\u0009Duration: 2.747s\u0009Billed Duration: 2.8s\u0009Max Memory Used: 51.9M","src":"cfc","rid":"867cf8ad-fafa-477c-ad2c-d8a51f08da7f","ts":"2021-11-25T16:49:39.695201299+08:00"}`)

	state, _ := fi.Stat()

	//模拟超出边界的情况
	MaxFunctionPrintLogBytes = int(state.Size()) + 800
	fi.WriteString(`{"msg":"abcsnajk db ueoqh odjs aopijdoi hqwoshdio a hdoiqw jiosjioa howhrqowjioej soiaj dkls jlkq h"}`)
	fi.WriteString(`{"msg":" mock id: 867cf8ad-fafa-477c-ad2c-18a52f08da7f\u0009Duration: 2.747s\u0009Billed Duration: 2.8s\u0009Max Memory Used: 51.9M","src":"cfc","rid":"867cf8ad-fafa-477c-ad2c-d8a51f08da7f","ts":"2021-11-25T16:49:39.695201299+08:00"}`)
	fi.WriteString(`{"msg":" mock id2: 867cf8ad-fafa-477c-ad2c-18a52f08da7f\u0009Duration: 2.747s\u0009Billed Duration: 2.8s\u0009Max Memory Used: 51.9M","src":"cfc","rid":"867cf8ad-fafa-477c-ad2c-d8a51f08da7f","ts":"2021-11-25T16:49:39.695201299+08:00"}`)

	fi.Close()

	l.Write(event)

}

func TestGetLogStreamName(t *testing.T) {

	getLogStreamName("brn", "status")

}
