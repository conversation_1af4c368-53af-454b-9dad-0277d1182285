package app

import (
	"fmt"
	"io/ioutil"
	"testing"
	"time"

	"gopkg.in/yaml.v2"

	"icode.baidu.com/baidu/faas/kun/pkg/api"
	"icode.baidu.com/baidu/faas/kun/pkg/cfclog"
	_ "icode.baidu.com/baidu/faas/kun/pkg/cfclog/json"
	"icode.baidu.com/baidu/faas/kun/pkg/kafka"
)

func TestKafkaConfig(t *testing.T) {
	content := `
services:
- name: kafka1
  broker:
  - "kafka.bj.baidubce.com:9091"
  - "kafka.bj.baidubce.com:9092"
  enabletls: true
  clientpem: /tmp/client.pem
  clientkey: /tmp/client.key
  capem: /tmp/ca.pem
`
	var config kafkaConfig
	err := yaml.Unmarshal([]byte(content), &config)
	if err != nil {
		t.Error(err)
		return
	}
	t.Log(config)
}

type mockProducer struct {
}

func (p *mockProducer) StartProducer(o *kafka.Options) error {
	return nil
}
func (p *mockProducer) StartProducerForHashPartition(o *kafka.Options) error {
	return nil
}

func (p *mockProducer) Close() error {
	return nil
}

func (p *mockProducer) SendMessage(topic string, message string) (partition int32, offset int64, err error) {
	fmt.Printf("topic=%s\n", topic)
	fmt.Println(message)
	return 0, 0, nil
}

func (p *mockProducer) SendMessageWithKey(topic string, message string, key string) (partition int32, offset int64, err error) {
	return 0, 0, nil
}

func (p *mockProducer) SendMessages(topic string, messages []string) (err error) {
	return nil
}

func createRequestLog() string {
	directory, _ := ioutil.TempDir("", "cfclog")
	logfile := fmt.Sprintf("%s/%s", directory, "request.log")
	writer, err := cfclog.CreateLogWriter("kafka", logfile, 4096)
	fmt.Println(err)
	entry := &cfclog.CfcLog{
		Created:   time.Now(),
		RequestID: "2212a30b-ee97-486d-8706-45166a00201c",
		Source:    "stdout",
		// Message: ,
	}
	for i := 1; i < 5; i++ {
		writer.Write(entry, []byte("log message line"))
	}
	writer.Close()
	return logfile
}

func createKafkalogConfig() string {
	content := `
services:
- name: kafka1
  broker:
  - "127.0.0.1:9091"
  enabletls: false
  clientpem: /tmp/client.pem
  clientkey: /tmp/client.key
  capem: /tmp/ca.pem
`
	cfgfile, _ := ioutil.TempFile("", "kafkalog")
	cfgfile.WriteString(content)
	cfgfile.Close()
	return cfgfile.Name()
}

func TestKafkaLogger(t *testing.T) {
	logfile := createRequestLog()
	event := &KunLogEvent{
		Type: KunLogEntry,
		Entry: &api.UserRequestLog{
			RequestID: "2212a30b-ee97-486d-8706-45166a00201c",
			RuntimeID: "pmpod-128-4-1539851156019188080",
			Function:  &api.FunctionConfiguration{},
			LogFile:   logfile,
			LogConfig: &api.LogConfiguration{
				LogType: "kafka",
				Params:  "name=kafka1&topic=dddd",
			},
			Result: &api.InvokeResult{
				Status:   "success",
				MemUsed:  123456,
				Duration: 2000,
			},
		},
	}
	cfgfile := createKafkalogConfig()

	_, err := newKafkaLogger(map[string]string{
		"configfile": "/tmp/config_not_exist",
	})
	t.Log(err)
	if err == nil { // 文件不存在
		t.FailNow()
	}

	file, _ := ioutil.TempFile("", "kafkaconfig")
	file.WriteString("bad yaml config")
	_, err = newKafkaLogger(map[string]string{
		"configfile": file.Name(),
	})
	t.Log(err)
	if err == nil { // 文件不存在
		t.FailNow()
	}

	logger, err := newKafkaLogger(map[string]string{
		"configfile": cfgfile,
	})
	if err != nil {
		t.FailNow()
	}

	// 错误的param格式
	event.Entry.LogConfig.Params = "name%%323kafka1"
	err = logger.Write(event)
	t.Log(err)
	if err == nil {
		t.FailNow()
	}
	// config不存在
	event.Entry.LogConfig.Params = "name=kafka2&topic=dddd"
	err = logger.Write(event)
	t.Log(err)
	if err == nil {
		t.FailNow()
	}
	event.Entry.LogConfig.Params = "name=kafka1&topic=dddd"
	l := logger.(*kafkaLogger)
	l.producers["kafka1"] = &kafkaProducer{
		name:     "kafka1",
		producer: &mockProducer{},
	}
	err = logger.Write(event)
	t.Log(err)
	if err == nil {
		t.FailNow()
	}

	logfile = createRequestLog()
	event.Entry.LogFile = logfile
	err = logger.Write(event)
	if err != nil {
		t.Error(err)
	}
}
