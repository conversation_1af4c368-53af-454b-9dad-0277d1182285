package app

import (
	"bytes"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"io/ioutil"
	"net/http"
	"net/url"
	"os"
	"time"

	"icode.baidu.com/baidu/faas/kun/pkg/api"
	"icode.baidu.com/baidu/faas/kun/pkg/bce/auth"
	"icode.baidu.com/baidu/faas/kun/pkg/bce/sts_credential"
	"icode.baidu.com/baidu/faas/kun/pkg/util/logs"

	goevents "github.com/docker/go-events"
)

const (
	fluentdRetryCount = 2 // 发送到fluentd重试次数
)

type fluentIAMRequest struct {
	Method  string             `json:"method"`
	URI     string             `json:"uri"`
	Headers *map[string]string `json:"headers"`
}

type fluentdIAM struct {
	AuthenticateMethod string                        `json:"authenticate_method"`
	Authorization      string                        `json:"authorization"`
	Credential         *sts_credential.StsCredential `json:"credential"`
	Request            *fluentIAMRequest             `json:"request"`
	RequestID          string                        `json:"request_id"`
}

type fluentdEvent struct {
	FunctionInvokeLog string      `json:"function_invoke_log"`
	Topic             string      `json:"topic"`
	IAM               *fluentdIAM `json:"iam"`
}

type fluentdLogger struct {
	closed   bool
	endpoint string
	tag      string
}

func newFluentdLogger(config map[string]string) goevents.Sink {
	return &fluentdLogger{
		closed:   false,
		endpoint: config["endpoint"],
		tag:      config["tag"],
	}
}

func (r *fluentdLogger) Write(event goevents.Event) error {
	evt, ok := event.(*KunLogEvent)
	if !ok || evt.Type != KunLogEntry {
		return nil
	}
	logev := evt.Entry
	defer os.Remove(logev.LogFile)

	query, err := url.ParseQuery(logev.LogConfig.Params)
	if err != nil {
		logs.Errorf("parse config param %s failed: %v", logev.LogConfig.Params, err)
		return err
	}

	file, err := os.Open(logev.LogFile)
	if err != nil {
		logs.Errorf("open logfile %s failed: %v", logev.LogFile, err)
		return err
	}
	defer file.Close()

	entry := &api.FluentdLogEntry{
		RequestID: logev.RequestID,
		RuntimeID: logev.RuntimeID,
		Function:  logev.Function,
		Result:    logev.Result,
	}
	buffer := bytes.NewBuffer(nil)
	encoder := json.NewEncoder(buffer)
	if err = encoder.Encode(entry); err != nil {
		logs.Errorf("encode fluentdLogEntry failed: %v", err)
		return err
	}
	if _, err = io.Copy(buffer, file); err != nil {
		logs.Errorf("copy logfile failed: %v", err)
		return err
	}

	// 组装fluentd参数
	fluURL, err := url.Parse(fmt.Sprintf("%s/%s", r.endpoint, r.tag))

	if err != nil {
		logs.Errorf("parse fluentd url failed: %v", err)
		return err
	}

	cred := logev.Credential
	if cred == nil {
		err := errors.New("entry credential is empty")
		logs.Errorf("invalid fluentd log entry: %s", err.Error())
		return err
	}

	header := http.Header{}
	header.Set("Host", fluURL.Host)
	header.Set("Content-Type", "application/json")
	header.Set("X-Bce-Request-Id", entry.RequestID)
	header.Set("X-Bce-Date", time.Now().Format(time.RFC3339))
	header.Set("X-Bce-Security-Token", cred.SessionToken)

	fluIAMRequest := &fluentIAMRequest{
		Method:  "POST",
		URI:     fluURL.Path,
		Headers: &map[string]string{},
	}

	for key := range header {
		(*fluIAMRequest.Headers)[key] = header.Get(key)
	}

	auth := auth.NewBceAuth(cred.AccessKeyId, cred.AccessKeySecret)
	sign := auth.NewSigner().
		Method(fluIAMRequest.Method).
		Path(fluIAMRequest.URI).
		Headers(header).
		Expire(3600).
		WithSignedHeader().
		GetSign()

	fluIAM := &fluentdIAM{
		AuthenticateMethod: "aksk",
		Authorization:      sign,
		Credential:         cred,
		Request:            fluIAMRequest,
		RequestID:          entry.RequestID,
	}

	fluEvent := &fluentdEvent{
		FunctionInvokeLog: buffer.String(),
		Topic:             query.Get("topic"),
		IAM:               fluIAM,
	}

	body, err := json.Marshal(fluEvent)
	if err != nil {
		logs.Errorf("parse fluentd event %s failed: %v", fluEvent, err)
		return err
	}
	req, err := http.NewRequest("POST", fluURL.String(), bytes.NewReader(body))
	if err != nil {
		logs.Errorf("make fluentd request failed: %v", err)
		return err
	}
	req.Header.Add("Content-Type", "application/json")
	client := &http.Client{}

	for i := 0; i < fluentdRetryCount; i++ {
		// 发送到fluentd
		resp, err := client.Do(req)
		if err != nil {
			logs.Errorf("send fluentd log failed: %s", err.Error())
			if i < fluentdRetryCount-1 {
				continue
			} else {
				return err
			}
		}
		defer resp.Body.Close()
		if resp.StatusCode != http.StatusOK {
			body, err := ioutil.ReadAll(resp.Body)
			if err != nil {
				logs.Errorf("read fluentd response failed: %s", err.Error())
				if i < fluentdRetryCount-1 {
					continue
				} else {
					return err
				}
			}
			err = fmt.Errorf("fluentd response error, status=%d, body=%s", resp.StatusCode, string(body))
			logs.Errorf("send fluentd log failed: %s", err.Error())
			if i < fluentdRetryCount-1 {
				continue
			} else {
				return err
			}
		}
		break
	}

	return nil
}

func (r *fluentdLogger) Close() error {
	r.closed = true
	return nil
}
