package app

import (
	"fmt"
	"io/ioutil"
	"os"
	"testing"
	"time"

	"github.com/baidubce/bce-sdk-go/bce"

	"icode.baidu.com/baidu/faas/kun/pkg/bce/iam"
	"icode.baidu.com/baidu/faas/kun/pkg/bce/sts_credential"

	"github.com/aws/aws-sdk-go/service/lambda"
	boscli "github.com/baidubce/bce-sdk-go/services/bos"
	bosapi "github.com/baidubce/bce-sdk-go/services/bos/api"
	goevents "github.com/docker/go-events"

	"icode.baidu.com/baidu/faas/kun/pkg/api"
)

type bosLogWriterMock struct {
}

func (m *bosLogWriterMock) Write(event goevents.Event) error {
	return nil
}

func (m *bosLogWriterMock) Close() error {
	return nil
}

func newBosLogWriterMock(bosuri, objname, region, endpoint, datadir string, uploader goevents.Sink) (goevents.Sink, error) {
	fmt.Println(bosuri, objname, region, endpoint)
	return &bosLogWriter{}, nil
}

func TestBosLogger(t *testing.T) {
	s := newBosLogger(map[string]string{
		"hostname": "host111",
		"region":   "bj",
		"endpoint": "bj.baidubce.com",
		"datadir":  "/tmp",
	})
	l := s.(*bosLogger)
	l.newWriter = newBosLogWriterMock

	// l.Write()
	event := &KunLogEvent{
		Type: KunLogCommit,
	}
	err := l.Write(event)
	if err != nil {
		t.Error(err)
		return
	}

	log := &api.UserRequestLog{
		Function: &api.FunctionConfiguration{
			FunctionConfiguration: lambda.FunctionConfiguration{
				FunctionArn:  stringp("function_arn"),
				FunctionName: stringp("test_func"),
				Version:      stringp("$LATEST"),
			},
		},
		LogConfig: &api.LogConfiguration{
			LogType: "bos",
			BosDir:  "/tmp/",
		},
	}
	event = &KunLogEvent{
		Type:  KunLogEntry,
		Entry: log,
	}
	err = l.Write(event)
	if err != nil {
		t.Error(err)
		return
	}
	l.Close()
}

func TestLogWriter(t *testing.T) {
	_, err := newBosLogWriter("bo://a/b/c", "obj1", "bj", "bj.baidubce.com", "", nil)
	if err == nil {
		t.Error("invalid bosurl 1")
		return
	}
	t.Log(err)

	_, err = newBosLogWriter("bos:///a/b/c", "obj1", "bj", "bj.baidubce.com", "", nil)
	if err == nil {
		t.Error("invalid bosurl 2")
		return
	}
	t.Log(err)

	s, err := newBosLogWriter("bos://a/b/c", "obj1", "bj", "bj.baidubce.com", "/tmp", nil)
	if err != nil {
		t.Error(err)
		return
	}
	w := s.(*bosLogWriter)
	if w.bucket != "a" || w.objname != "b/c/obj1" {
		t.Error(w.bucket, w.objname)
		return
	}

	log := &api.UserRequestLog{
		Function: &api.FunctionConfiguration{
			FunctionConfiguration: lambda.FunctionConfiguration{
				FunctionArn:  stringp("function_arn"),
				FunctionName: stringp("test_func"),
				Version:      stringp("$LATEST"),
			},
		},
		LogConfig: &api.LogConfiguration{
			LogType: "bos",
			BosDir:  "/tmp/",
		},
	}
	event := &KunLogEvent{
		Type:  KunLogEntry,
		Entry: log,
	}
	err = w.Write(event)
	if err.Error() != "filePath is empty" {
		t.Fatal(err.Error())
	}
	log.LogFile = "/tmp/logfile_not_exist"
	err = w.Write(event)
	if err.Error() != "credential is null" {
		t.Fatal(err.Error())
	}
	log.Credential = &sts_credential.StsCredential{
		Expiration: time.Now().Add(time.Duration(-1) * time.Second),
	}
	err = w.Write(event)
	if err.Error() != "credential is expired" {
		t.Fatal(err.Error())
	}
	log.Credential.Expiration = time.Now().Add(time.Duration(1) * time.Second)
	err = w.Write(event)
	if err.Error() != "stat file failed" {
		t.Fatal(err.Error())
	}

	w.lastcommit = time.Now()
	if w.needCommit() {
		t.Error("need commit")
		return
	}

}

func stringp(s string) *string {
	return &s
}
func TestObjectName(t *testing.T) {
	period := time.Date(2018, 12, 28, 15, 0, 0, 0, time.UTC)
	config := &api.FunctionConfiguration{
		FunctionConfiguration: lambda.FunctionConfiguration{
			FunctionArn:  stringp("function_arn"),
			FunctionName: stringp("test_func"),
			Version:      stringp("$LATEST"),
		},
	}
	name := getObjectName(config, "host11", period)
	t.Log(name)
	if name != "test_func/$LATEST_201812281500_5d1a6ae4a9360b9505662e207f670667" {
		t.Error(name)
	}
}

func TestWriteBosLog(t *testing.T) {
	if os.Getenv("TestWriteBos") != "1" {
		return
	}

	iamcli, _ := iam.CreateIAMClient("./test.yaml")
	stsToken, err := iamcli.AssumeRole(
		"c7ac82ae14ef42d1a4ffa3b2ececa17f", "BceServiceRole_cfc", "", 3600)
	if err != nil {
		t.Fatal(err)
		return
	}
	fmt.Println(stsToken)

	client, err := boscli.NewClient(stsToken.AccessKeyId, stsToken.AccessKeySecret, "bos-sandbox-bj.baidu-int.com")
	if err != nil {
		t.Fatal(err)
	}
	client.Config.Credentials.SessionToken = stsToken.SessionToken // 设置sts token
	resp, err := client.ListBuckets()
	if err != nil {
		t.Fatal(err)
	}
	t.Log(resp)

	body, err := bce.NewBodyFromString("test data")
	if err != nil {
		t.Fatal(err)
	}
	resp2, err := client.PutObject("bostest", "index.js", body, nil)
	if err != nil {
		t.Error(err)
		return
	}
	t.Log(resp2)
}

func TestWorkerAppendLog(t *testing.T) {
	if os.Getenv("TestWriteBos") != "1" {
		return
	}

	iamcli, _ := iam.CreateIAMClient("./test.yaml")
	stsToken, err := iamcli.AssumeRole(
		"c7ac82ae14ef42d1a4ffa3b2ececa17f", "BceServiceRole_cfc", "", 3600)
	if err != nil {
		t.Error(err)
		return
	}
	// fmt.Println(stsToken)

	worker := &logAppendWorker{
		factory: kunBosClientFactory,
	}

	logfile, err := ioutil.TempFile("", "logfile")
	if err != nil {
		t.Error(err)
		return
	}
	logfile.Write([]byte("this is a log line\n"))
	logfile.Close()
	req := &logAppendRequest{
		Bucket:     "bostest",
		Object:     "gotest",
		Region:     "bj",
		Endpoint:   "bos-sandbox-bj.baidu-int.com",
		Logfile:    []string{logfile.Name()},
		Credential: stsToken,
	}
	err = worker.appendLog(req)
	if err != nil {
		t.Error(err)
		return
	}
}

func TestBosLogDataStore(t *testing.T) {
	dbfile, err := ioutil.TempFile("", "bos.db")
	if err != nil {
		t.Error(err)
		return
	}
	dbfile.Close()
	t.Log(dbfile.Name())
	store, err := newBosLogDataStore(dbfile.Name())
	if err != nil {
		t.Error(err)
		return
	}

	req := &logAppendRequest{
		Bucket:     "bostest",
		Object:     "object123",
		Region:     "bj",
		Endpoint:   "bj.bos.com",
		Logfile:    []string{"/a/b/c/e.txt"},
		Credential: nil,
	}
	if err = store.createLogfile(req.FileKey(), req); err != nil {
		t.Error(err)
		return
	}

	fmap, err := store.loadLogfileMap()
	if err != nil || len(fmap) != 1 {
		t.Error(err)
		return
	}
	t.Log(fmap)

	if err = store.deleteLogfile(req.FileKey()); err != nil {
		t.Error(err)
		return
	}

	fmap, err = store.loadLogfileMap()
	if err != nil || len(fmap) != 0 {
		t.Error(err)
		return
	}
}

func mockBosClientFactory(ak, sk, token, region, endpoint string) BosClient {
	return &bosClientMock{}
}

type bosClientMock struct {
}

func (m *bosClientMock) AppendObject(bucketName, objectKey string, offset int64, data []byte) (*bosapi.AppendObjectResult, error) {
	// hdr := make(http.Header)
	// hdr.Add("x-bce-next-append-offset", fmt.Sprintf("%d", offset+2000))
	// return bos.NewAppendObjectResponse(hdr), nil
	return &bosapi.AppendObjectResult{
		NextAppendOffset: offset + 2000,
	}, nil
}

func (m *bosClientMock) GetObjectMeta(bucketName, objectKey string) (*bosapi.GetObjectMetaResult, error) {
	return nil, nil
}

func TestLogAppendWorker(t *testing.T) {
	reqsCh := make(chan *logAppendRequest, 2)
	notifyCh := goevents.NewChannel(2)
	wrk := &logAppendWorker{
		notify:  notifyCh,
		factory: mockBosClientFactory,
		reqsCh:  reqsCh,
	}
	go wrk.run()

	// 创建临时日志文件
	logfile, err := ioutil.TempFile("", "logfile")
	if err != nil {
		t.Error(err)
		return
	}
	fname := logfile.Name()
	logfile.Close()
	// 随机生成一个请求
	offset := int64(1000)
	req := &logAppendRequest{
		Bucket:     "bostest",
		Object:     "obj1",
		Logfile:    []string{fname},
		Offset:     &offset,
		Region:     "bj",
		Endpoint:   "bj.bcebos.com",
		Credential: &sts_credential.StsCredential{},
	}

	// 从wrkPool中取wrk
	reqsCh <- req

	event := <-notifyCh.C // 等待响应
	rsp, ok := event.(*logAppendRequest)
	if !ok {
		t.Error("invalid type")
		return
	}
	// t.Error(rsp)
	if *rsp.Offset != offset+2000 {
		t.Error(rsp)
	}
	close(reqsCh)
	_, err = os.Stat(fname)
	if err == nil {
		t.Error("should delete logfile")
	}
	t.Log(err)
}

func TestBosLogUploader(t *testing.T) {
	dir, _ := ioutil.TempDir("", "test")
	u := newBosLogUploader(dir)

	req := &logAppendRequest{
		Bucket:     "bostest",
		Object:     "obj1",
		Logfile:    []string{"/ab/c/d/e"},
		Region:     "bj",
		Endpoint:   "bj.bcebos.com",
		Credential: &sts_credential.StsCredential{},
	}

	u.onAppendRequest(req)

	req2 := <-u.reqsCh
	t.Log(req2)

	_, ok := u.logfileMap[req.FileKey()]
	if !ok {
		t.Error("logfile map error")
	}
	o, ok := u.objectMap[req.ObjKey()]
	if !ok {
		t.Error("object map error")
	}
	if len(o.Objfiles) != 1 {
		t.Error("object error")
	}
	t.Log(o)

	m, err := u.store.loadLogfileMap()
	if err != nil {
		t.Error(err)
		return
	}
	t.Log(m)

	offset := int64(2000)
	req.Offset = &offset
	u.onAppendFinish(req)

	_, ok = u.logfileMap[req.FileKey()]
	if ok {
		t.Error("logfile map error 2")
	}
	o, ok = u.objectMap[req.ObjKey()]
	if !ok {
		t.Error("object map error")
	}
	if o.Offset != 2000 || len(o.Objfiles) != 0 {
		t.Error("object error")
	}
	t.Log(o)

	m, err = u.store.loadLogfileMap()
	if err != nil {
		t.Error(err)
		return
	}
	if len(m) != 0 {
		t.Error("store error")
	}
	t.Log(m)

}
