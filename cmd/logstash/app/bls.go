package app

import (
	"crypto/md5"
	"errors"
	"fmt"
	"os"
	"strconv"
	"strings"
	"time"
	"unicode/utf8"

	"github.com/baidubce/bce-sdk-go/bce"
	blscli "github.com/baidubce/bce-sdk-go/services/bls"
	blsapi "github.com/baidubce/bce-sdk-go/services/bls/api"
	goevents "github.com/docker/go-events"
	"go.uber.org/zap"

	"icode.baidu.com/baidu/faas/kun/pkg/cfclog"
	"icode.baidu.com/baidu/faas/kun/pkg/util/json"
	"icode.baidu.com/baidu/faas/kun/pkg/util/logs"
)

var (
	KB                           = 1024
	defaultBlsBatchRecordsKBytes = int64(1 * 1024 * 1024) // 1M
	MaxFunctionPrintLogBytes     = 400 * KB               //bls 单条最多允许512kb，考虑到marshal时添加的字段以及转义字符等
	MaxBoundaryBytes             = 110 * KB
	defaultBlsBatchRecordsLength = 1000 // 批量上传bls最大日志条数

	logType = "JSON"

	blsEntryCount = 2 // 发送bls重试次数
)

//日志边界信息
type LogBoundary struct {
	StartInfo  string
	EndInfo    string
	ReportInfo string
}

type BlsClient interface {
	PushLogRecord(logStoreName string, request PushLogRecordRequest) error // 推送日志接口
}

type kunBlsClient struct {
	ststoken string
	client   *blscli.Client
}

type blsLogMsg struct {
	Log       []string `json:"log"`
	RequestId string   `json:"requestId"`
	Status    string   `json:"status"`
}

type LogRecord struct {
	Message   string `json:"message"`   // 记录日志内容
	Timestamp int    `json:"timestamp"` // 日志记录的时间戳，精确到毫秒
	Sequence  int    `json:"sequence"`
}

type PushLogRecordRequest struct {
	LogStreamName string             `json:"logStreamName"` // 日志流名称，长度不能超过 128 字符，包含的字符仅限于：a-z, A-Z, 0-9, '_', '-', '.'
	LogType       string             `json:"type"`          // 数据类型，JSON/TEXT，默认为 TEXT
	LogRecords    []blsapi.LogRecord `json:"logRecords"`    // 日志记录
}

type blsLogger struct {
	closed   bool
	hostname string
	endpoint string
	region   string
	factory  func(ak, sk, token, region, endpoint string) BlsClient
}

func newBlsLogger(config map[string]string) goevents.Sink {
	return &blsLogger{
		closed:   false,
		hostname: config["hostname"],
		endpoint: config["endpoint"],
		region:   config["region"],
		factory:  kunBlsClientFactory,
	}
}

func kunBlsClientFactory(ak, sk, token, region, endpoint string) BlsClient {
	logs.V(8).Infof("create blsclient ak=%s sk=%s region=%s endpoint=%s", ak, sk, region, endpoint)
	blsClient, err := blscli.NewClient(ak, sk, endpoint)
	if err != nil {
		logs.Errorf("create bls client failed, %s", err.Error())
		return nil
	}
	blsClient.Config.Credentials.SessionToken = token // 设置sts token

	return &kunBlsClient{
		client: blsClient,
	}
}

func (bc *kunBlsClient) PushLogRecord(logStoreName string, request PushLogRecordRequest) error {
	return bc.client.PushLogRecord(logStoreName, request.LogStreamName, request.LogType, request.LogRecords)
}

func (l *blsLogger) Write(event goevents.Event) error {
	evt, ok := event.(*KunLogEvent)
	if !ok || evt.Type != KunLogEntry {
		return nil
	}
	logev := evt.Entry
	defer os.Remove(logev.LogFile)

	logStreamName := getLogStreamName(*logev.Function.FunctionArn, logev.Result.Status)
	blsEntry := PushLogRecordRequest{
		LogStreamName: logStreamName,
		LogType:       logType,
	}
	mapMsg := blsLogMsg{
		RequestId: logev.RequestID,
		Status:    logev.Result.Status,
	}
	msg := []string{}
	idx := 0

	lb := &LogBoundary{}
	size := 0
	err := translate(logev.LogFile, func(log *cfclog.CfcLog) error {

		if len(log.Message) == 0 {
			logs.V(9).Debugf("decode log nil  %s", logev.RequestID)
			return nil
		}
		// 把函数执行的日志拼接成一个完整的log record
		str, _ := strconv.Unquote(string(log.Message))

		//单独提取日志边界
		if strings.Contains(str, logev.RequestID) {

			boundaryStr := fmt.Sprintf("%s ", log.Created.Format(time.RFC3339)) + str

			if strings.Contains(str, "START RequestId") {
				lb.StartInfo = boundaryStr
				msg = append(msg, boundaryStr)
				size += utf8.RuneCountInString(boundaryStr)
				idx += 1
				return nil
			} else if strings.Contains(str, "END RequestId") {
				lb.EndInfo = boundaryStr
				size += utf8.RuneCountInString(boundaryStr)
				idx += 1
				return nil
			} else if strings.Contains(str, "REPORT RequestId") {
				lb.ReportInfo = boundaryStr
				size += utf8.RuneCountInString(boundaryStr)
				idx += 1
				return nil
			}
		}

		if utf8.RuneCountInString(str)+size < MaxFunctionPrintLogBytes {
			msg = append(msg, str)
			size += utf8.RuneCountInString(str)
		}
		idx += 1
		return nil
	})

	if err != nil {
		logs.Errorf("translate bls log failed: %s ; request_id : %s ", err.Error(), logev.RequestID)
		return err
	}

	// 缺少START RequestId时，需在上报信息msg中的第一行加上
	if lb.StartInfo == "" {

		logs.Warnf("Start RequestID info lost . request_id : %s ", logev.RequestID,
			zap.Int("start info length", len(lb.StartInfo)),
			zap.Strings("log info", msg),
		)

		lb.StartInfo = "START RequestId " + logev.RequestID
		firstLine := []string{lb.StartInfo}
		msg = append(firstLine, msg...)
	}

	//拼接日志边界
	if lb.EndInfo == "" || lb.ReportInfo == "" {

		logs.Warnf("End/Report RequestID info lost . request_id : %s ", logev.RequestID,
			zap.Int("end info length", len(lb.StartInfo)),
			zap.Int("report info length", len(lb.StartInfo)),
			zap.Strings("log info", msg),
		)

		lb.EndInfo = "END RequestId " + logev.RequestID
		lb.ReportInfo = "REPORT RequestId " + logev.RequestID

	}

	msg = append(msg, lb.EndInfo)
	msg = append(msg, lb.ReportInfo)

	mapMsg.Log = msg
	msgMapJson, _ := json.Marshal(mapMsg)

	//json解析后超限（增加了新的字段导致）

	if len(msgMapJson) >= MaxBoundaryBytes+MaxFunctionPrintLogBytes {
		logs.Warnf("the size of function log overflow  :  %d,  request_id:", len(msgMapJson), logev.RequestID)

		// 如果marshal后超出限制，则将msg截取一半

		msg = msg[0 : len(msg)/2]
		msg = append(msg, lb.EndInfo)
		msg = append(msg, lb.ReportInfo)

		mapMsg.Log = msg
		msgMapJson, _ = json.Marshal(mapMsg)
	}

	record := blsapi.LogRecord{
		Message:   string(msgMapJson),
		Timestamp: time.Now().UnixNano() / 1e6,
		Sequence:  idx,
	}

	// logs.Infof("msgJson = %s", msgMapJson)
	blsEntry.LogRecords = append(blsEntry.LogRecords, record)
	if err != nil {
		logs.Errorf("translate bls log failed: %s. request_id : %s ", err.Error(), logev.RequestID)
		return err
	}

	credential := logev.Credential
	if credential == nil {
		err := errors.New("entry credential is empty")
		logs.Errorf("invalid bls log entry:  request_id : %s ", err.Error(), logev.RequestID)
		return err
	}

	client := l.factory(credential.AccessKeyId, credential.AccessKeySecret, credential.SessionToken, l.region, l.endpoint)
	for i := 0; i < blsEntryCount; i++ {
		// 发送到bls，重试两次
		err = client.PushLogRecord(logev.LogConfig.BlsLogSet, blsEntry)
		if err != nil {
			if realErr, ok := err.(*bce.BceServiceError); ok {
				logs.Errorf("PUSH TO BLS %d TIMES; push log to bls fail, err: %+v. request id : %s", i, realErr, logev.RequestID)
				continue
			}
		}
		// logs.Infof("push log to bls success")
		break
	}

	return err
}

func (l *blsLogger) Close() error {
	l.closed = true
	return nil
}

func getLogStreamName(brn string, status string) string {
	data := md5.Sum([]byte(brn))
	if status != "success" {
		status = "failed"
	}
	return fmt.Sprintf("cfclog_%x_%s", data, status)
}
