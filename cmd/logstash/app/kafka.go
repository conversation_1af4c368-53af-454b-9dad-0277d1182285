package app

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"io/ioutil"
	"net/url"
	"os"

	"icode.baidu.com/baidu/faas/kun/pkg/kafka"
	"icode.baidu.com/baidu/faas/kun/pkg/util/logs"

	"icode.baidu.com/baidu/faas/kun/pkg/api"
	"icode.baidu.com/baidu/faas/kun/pkg/bce/sts_credential"

	goevents "github.com/docker/go-events"
	"gopkg.in/yaml.v2"
)

type kafkaBroker struct {
	Name          string   `yaml:"name"`
	Brokers       []string `yaml:"broker"`
	EnableTLS     bool     `yaml:"enabletls"`
	ClientPemFile string   `yaml:"clientpem"`
	ClientKeyFile string   `yaml:"clientkey"`
	CaPemFile     string   `yaml:"capem"`
}

type kafkaConfig struct {
	Services []kafkaBroker `yaml:"services"`
}

type kafkaLogger struct {
	closed    bool
	config    map[string]*kafkaBroker
	producers map[string]*kafkaProducer
}

func newKafkaLogger(config map[string]string) (goevents.Sink, error) {
	configfile := config["configfile"]
	content, err := ioutil.ReadFile(configfile)
	if err != nil {
		logs.Errorf("read kafka config %s failed: %v", configfile, err)
		return nil, err
	}
	var kafkacfg kafkaConfig
	err = yaml.Unmarshal(content, &kafkacfg)
	if err != nil {
		logs.Errorf("decode kafka config %s failed: %v", configfile, err)
		return nil, err
	}

	cfgmap := make(map[string]*kafkaBroker)
	for _, c := range kafkacfg.Services {
		cfgmap[c.Name] = &c
	}

	return &kafkaLogger{
		closed:    false,
		config:    cfgmap,
		producers: make(map[string]*kafkaProducer),
	}, nil
}

type kafkaLogEntry struct {
	RequestID  string                        `json:"request_id"`
	RuntimeID  string                        `json:"runtime_id"`
	Function   *api.FunctionConfiguration    `json:"function"`
	Result     *api.InvokeResult             `json:"result"`
	Credential *sts_credential.StsCredential `json:"credential"`
}

func (r *kafkaLogger) getProducer(name string) (*kafkaProducer, error) {
	if producer, ok := r.producers[name]; ok {
		return producer, nil
	}
	config := r.config[name]
	if config == nil {
		return nil, fmt.Errorf("missing broker %s config", name)
	}
	logs.V(6).Infof("create kafka producer: %v", config)
	options := &kafka.Options{
		Brokers:       config.Brokers,
		EnableTLS:     config.EnableTLS,
		ClientPemPath: config.ClientPemFile,
		ClientKeyPath: config.ClientKeyFile,
		CaPemPath:     config.CaPemFile,
	}
	kp := kafka.NewProducer()
	err := kp.StartProducer(options)
	if err != nil {
		logs.Errorf("start producer failed, options: %v, err: %v", options, err)
		return nil, err
	}
	producer := &kafkaProducer{
		name:     name,
		producer: kp,
	}
	r.producers[name] = producer
	return producer, nil
}

func (r *kafkaLogger) Write(event goevents.Event) error {
	evt, ok := event.(*KunLogEvent)
	if !ok || evt.Type != KunLogEntry {
		return nil
	}
	request := evt.Entry
	defer os.Remove(request.LogFile)
	query, err := url.ParseQuery(request.LogConfig.Params)
	if err != nil {
		logs.Errorf("parse config param %s failed: %v", request.LogConfig.Params, err)
		return err
	}
	producer, err := r.getProducer(query.Get("name"))
	if err != nil {
		logs.Errorf("create producer %s failed: %v", query.Get("name"), err)
		return err
	}
	file, err := os.Open(request.LogFile)
	if err != nil {
		logs.Errorf("open logfile %s failed: %v", request.LogFile, err)
		return err
	}
	defer file.Close()

	entry := &kafkaLogEntry{
		RequestID:  request.RequestID,
		RuntimeID:  request.RuntimeID,
		Function:   request.Function,
		Result:     request.Result,
		Credential: request.Credential,
	}
	buffer := bytes.NewBuffer(nil)
	encoder := json.NewEncoder(buffer)
	if err = encoder.Encode(entry); err != nil {
		logs.Errorf("encode kafkaLogEntry failed: %v", err)
		return err
	}
	if _, err = io.Copy(buffer, file); err != nil {
		logs.Errorf("copy logfile failed: %v", err)
		return err
	}
	partition, offset, err := producer.SendMessage(query.Get("topic"), buffer.String())
	logs.V(6).Infof("report done. partition=%d, offset=%d, err=%v", partition, offset, err)
	return err
}

func (r *kafkaLogger) Close() error {
	return nil
}

type kafkaProducer struct {
	name     string
	producer kafka.ProducerInterface
}

func (p *kafkaProducer) SendMessage(topic string, message string) (partition int32, offset int64, err error) {
	return p.producer.SendMessage(topic, message)
}

func (p *kafkaProducer) Close() error {
	return p.producer.Close()
}
