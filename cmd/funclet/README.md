## Funclet

### 功能

用于 FaaS 容器管理，功能包括

- 用户代码下载
- 容器目录挂载
- 容器初始化
- 容器恢复
- 容器销毁

### 部署要求

在默认参数下，Funclet 使用 /var/faas 作为工作目录，其中包括以下四个子目录

- cache   存放用户的代码
- conf    存放容器执行的基础配置，包括环境变量、运行时信息等
- runtime 存放各个语言的运行时
- tmp     临时目录，用于代码下载中转等功能

### 启动

```bash
./funclet --ak=[AK] --sk=[SK] --endpoint=[BOS Endpoint]
```

更多参数可参考 `./funclet -h`

## NSMount

### 功能

进入指定的 Docker 容器，在指定的设备上，挂载指定的目录

### 用法

```bash
NSMOUNT_PID=21600 ./nsmount --device 2065 --filetype ext4 python:/var/runtime,rw conf:/etc/faas task:/var/task
```