/**
 * Created Date: Friday, August 11th 2017, 3:47:02 pm
 * Author: hefangshi
 * -----
 * Last Modified: Fri Aug 11 2017
 * Modified By: hefangshi
 * -----
 * Copyright (c) 2017 Baidu.Inc
 *
 */

package app

import (
	"github.com/emicklei/go-restful"

	"icode.baidu.com/baidu/faas/kun/pkg/api"
	genericserver "icode.baidu.com/baidu/faas/kun/pkg/server"
	"icode.baidu.com/baidu/faas/kun/pkg/server/endpoint"
)

// InstallAPI xxx
func (funcletServer *Server) InstallAPI(container *restful.Container) {
	var apis = []endpoint.ApiSingle{
		{
			Verb:    "POST",
			Path:    "faas-containers/warmup-one",
			Handler: genericserver.WrapRestRouteFuncWithTrace("handler/warmupOne", funcletServer.initOneColdContainerHandle),
		},
		{
			Verb:    "GET",
			Path:    "faas-containers",
			Handler: genericserver.WrapRestRouteFunc(funcletServer.getContainerList),
		},
		{
			Verb:    "GET",
			Path:    "faas-zombie-containers",
			Handler: genericserver.WrapRestRouteFunc(funcletServer.getZombieContainerList),
		},
		{
			Verb:    "POST",
			Path:    "faas-containers/traffic",
			Handler: genericserver.WrapRestRouteFunc(funcletServer.queryContainerTraffic),
		},
		{
			Verb:    "DELETE",
			Path:    "faas-node",
			Handler: genericserver.WrapRestRouteFunc(funcletServer.resetNodeHandle),
		},
		{
			Verb:    "DELETE",
			Path:    "faas-containers/{PodName}",
			Handler: genericserver.WrapRestRouteFunc(funcletServer.resetContainerHandle),
		},
		{
			Verb:    "PUT",
			Path:    "faas-containers/resource",
			Handler: genericserver.WrapRestRouteFunc(funcletServer.UpdateContainersResource),
		},
		{
			Verb:    "POST",
			Path:    "faas-containers/freeze",
			Handler: genericserver.WrapRestRouteFunc(funcletServer.freezeContainerHandle),
		},
		{
			Verb:    "POST",
			Path:    "faas-containers/report",
			Handler: genericserver.WrapRestRouteFunc(funcletServer.reportContainerHandle),
		},
		{
			Verb:    "GET",
			Path:    "faas-sick-pods",
			Handler: genericserver.WrapRestRouteFunc(funcletServer.getSickPodsHandle),
		},
		{
			Verb:    "POST",
			Path:    "faas-sick-pod",
			Handler: genericserver.WrapRestRouteFunc(funcletServer.putSickPodHandle),
		},
		{
			Verb:    "DELETE",
			Path:    "faas-sick-pod",
			Handler: genericserver.WrapRestRouteFunc(funcletServer.deleteSickPodHandle),
		},
	}

	var imageApi = []endpoint.ApiSingle{
		{
			Verb:    "GET",
			Path:    "images",
			Handler: genericserver.WrapRestRouteFunc(funcletServer.listImages),
		},
		{
			Verb:    "POST",
			Path:    "images/checkAndPull",
			Handler: genericserver.WrapRestRouteFunc(funcletServer.checkAndPullImage),
		},
	}
	if funcletServer.Option.FuncletOptions.DockerRuntime == api.RuntimeKata {
		apis = append(apis, imageApi...)
	}
	var apiversion = []endpoint.ApiVersion{
		{
			Prefix: "/v1/funclet",
			Group:  apis,
		},
	}
	endpoint.NewApiInstaller(apiversion).Install(container)
}

// InstallAPI xxx
func (funcletServer *Server) InstallUnixAPI(container *restful.Container) {
	var apis = []endpoint.ApiSingle{
		{
			Verb:    "POST",
			Path:    "faas-containers/traffic",
			Handler: genericserver.WrapRestRouteFunc(funcletServer.queryContainerTraffic),
		},
		{
			Verb:    "POST",
			Path:    "faas-containers/freeze",
			Handler: genericserver.WrapRestRouteFunc(funcletServer.freezeContainerHandle),
		},
		{
			Verb:    "POST",
			Path:    "faas-containers/report",
			Handler: genericserver.WrapRestRouteFunc(funcletServer.reportContainerHandle),
		},
	}
	var apiversion = []endpoint.ApiVersion{
		{
			Prefix: "/v1/funclet",
			Group:  apis,
		},
	}
	endpoint.NewApiInstaller(apiversion).Install(container)
}
