package options

import (
	"github.com/spf13/pflag"
	"time"

	cronclient "icode.baidu.com/baidu/faas/kun/pkg/cron/client"
	genericoptions "icode.baidu.com/baidu/faas/kun/pkg/server/options"
	"icode.baidu.com/baidu/faas/kun/pkg/store/bolt"
)

const (
	// ApplicationName 指定应用名称
	ApplicationName   = "funclet"
	DefaultDockerHost = "unix:///var/run/docker.sock"
	DefaultVersion    = "1.35"
	DefaultRuntime    = "runc"

	RunningModeCloud = "cloud"
	RunningModeOTE   = "ote"
)

// ServerRunOptions funclet 配置
type ServerRunOptions struct {
	RecommendedOptions      *genericoptions.RecommendedOptions
	FuncletOptions          *FuncletOptions
	GracefulShutdownSeconds int
}

type DockerOptions struct {
	DockerSocksHost  string
	DockerAPIVersion string
	DockerRuntime    string
}

func NewDockerOptions() *DockerOptions {
	return &DockerOptions{
		DockerSocksHost:  DefaultDockerHost,
		DockerAPIVersion: DefaultVersion,
		DockerRuntime:    DefaultRuntime,
	}
}

// MountConfiguration Docker目录挂载管理器配置
type MountOptions struct {
	RunnerCmdLine      string
	NSMountExecPath    string
	InternalVolumePath string // 挂载的宿主机存储目录在funclet里的路径
}

func NewMountOptions() *MountOptions {
	return &MountOptions{
		NSMountExecPath:    "/nsmount",
		RunnerCmdLine:      "/init %s",
		InternalVolumePath: "/var/faas",
	}
}

type ContainerManagerOptions struct {
	RunnerContainerCMD string
	TrafficExcludes    []string
	ForbidAccessIp     []string
	LocalNodeIp        string
	AcceptPortList     []string
	BindMountPath      string
	EnableLoopDevice   bool
	*MountOptions
	*DockerOptions
	LocalStoreOptions *bolt.Options
}

func NewContainerMangerOptions() *ContainerManagerOptions {
	return &ContainerManagerOptions{
		RunnerContainerCMD: "/init",
		TrafficExcludes:    []string{"10.0.0.0/8", "**********/12", "***********/16"},
		ForbidAccessIp:     []string{"**********/24"},
		AcceptPortList:     []string{"8080", "53"},
		BindMountPath:      "/mnt/faas",
		MountOptions:       NewMountOptions(),
		DockerOptions:      NewDockerOptions(),
		EnableLoopDevice:   true,
	}
}

type ConnBackPoolOptions struct {
	ReportSeconds int
}

func NewConnBackPoolOptions() *ConnBackPoolOptions {
	return &ConnBackPoolOptions{
		ReportSeconds: 10,
	}
}

func (o *ConnBackPoolOptions) AddFlags(fs *pflag.FlagSet) {
	fs.IntVar(&o.ReportSeconds, "report-seconds",
		o.ReportSeconds, "report funclet info interval seconds")
}

type CriOptions struct {
	CriEndpoint string
	CriAuth     string
}

func NewCriOptions() *CriOptions {
	return &CriOptions{}
}
func (o *CriOptions) AddFlags(fs *pflag.FlagSet) {
	fs.StringVar(&o.CriEndpoint, "cri-endpoint", o.CriEndpoint, "cri-endpoint")
	fs.StringVar(&o.CriAuth, "cri-auth", o.CriAuth, "cri-auth a base64 encoded 'USERNAME[:PASSWORD]'")
}

type SecureOptions struct {
	SecureCpuQuota        int64
	KataSharedSandboxPath string
	KataAgentPath         string
}

func NewSecureOptions() *SecureOptions {
	return &SecureOptions{
		SecureCpuQuota:        50000,
		KataSharedSandboxPath: "/home/<USER>/kata-containers/shared/sandboxes/%s/%s/rootfs",
		KataAgentPath:         "/home/<USER>/run/vc/sbs/%s/agent.json",
	}
}

func (s *SecureOptions) AddFlags(fs *pflag.FlagSet) {
	fs.Int64Var(&s.SecureCpuQuota, "secure-cpu-quota", s.SecureCpuQuota, "secure container cpu quota,default cpuPeriod 100000")
	fs.StringVar(&s.KataSharedSandboxPath, "kata-shared-path", s.KataSharedSandboxPath, "Runtime path")
	fs.StringVar(&s.KataAgentPath, "kata-agent-path", s.KataAgentPath, "Runtime path")
}

type PreWarmOptions struct {
	RuntimePath   string
	RuntimeDirs   string
	PreWarmPeriod int
}

func NewPreWarmOptions() *PreWarmOptions {
	return &PreWarmOptions{
		RuntimePath:   "/var/faas/runtime",
		RuntimeDirs:   "node-v10.15.3-linux-x64;node-v12.2.0-linux-x64;node-v8.5.0-linux-x64",
		PreWarmPeriod: 180,
	}
}

func (s *PreWarmOptions) AddFlags(fs *pflag.FlagSet) {
	fs.IntVar(&s.PreWarmPeriod, "prewarm-period", s.PreWarmPeriod, "prewarm preroid")
	fs.StringVar(&s.RuntimePath, "prewarm-runtime-path", s.RuntimePath, "prewarm runtime path")
	fs.StringVar(&s.RuntimeDirs, "prewarm-runtime-dirs", s.RuntimeDirs, "prewarm runtime dirs")
}

// funclet 配置
type FuncletOptions struct {
	*ContainerManagerOptions
	InvokerSocks             string
	InvokerPort              int
	InvokerDispatcherPort    int
	TargetRuntimePath        string
	TargetConfPath           string
	TargetCodePath           string
	TargetLayerPath          string
	ConfPath                 string
	TmpDiskPath              string
	TmpDiskSize              int64
	RuntimePath              string
	TmpPath                  string
	CachePath                string
	ListenPath               string
	ListenType               string
	ListenPort               int
	ListenV2Path             string
	KataListenAddr           string
	FuncletApiSocks          string
	RunningMode              string
	ReportIntervel           time.Duration
	ReserveCollectorEndpoint string
	FuncletEndpoint          string
	AesEncryptKey            string
	ConnBackPoolOptions      *ConnBackPoolOptions
	CronOptions              *cronclient.CronOptions
	LocalStoreOptions        *bolt.Options

	*CriOptions
	*SecureOptions
	*PreWarmOptions
}

// NewFuncletServerOptions 初始化 funclet 服务配置
func NewFuncletServerOptions() *ServerRunOptions {
	funcletOptions := &FuncletOptions{
		ContainerManagerOptions: NewContainerMangerOptions(),
		RuntimePath:             "/var/faas/runtime",
		CachePath:               "/var/faas/cache",
		TmpPath:                 "/var/faas/tmp",
		ConfPath:                "/var/faas/conf",
		TmpDiskPath:             "/var/faas/ext4tmp",
		TmpDiskSize:             576716800,
		TargetCodePath:          "/var/task",
		TargetConfPath:          "/etc/faas",
		TargetRuntimePath:       "/var/runtime",
		TargetLayerPath:         "/opt",
		InvokerSocks:            "/var/run/faas/.server.sock",
		ListenType:              "unix",
		ListenPath:              "/var/run/faas/.funclet.sock",
		ListenPort:              18200,
		ListenV2Path:            "unix:///var/run/faas/.funclet_v2.sock",
		FuncletApiSocks:         "/var/run/faas-inner/.funcletapi.sock",
		InvokerPort:             8200,
		InvokerDispatcherPort:   18400,
		RunningMode:             RunningModeCloud,
		ConnBackPoolOptions:     NewConnBackPoolOptions(),

		CronOptions:              cronclient.NewCronOptions(),
		LocalStoreOptions:        bolt.NewOptions(),
		CriOptions:               NewCriOptions(),
		SecureOptions:            NewSecureOptions(),
		PreWarmOptions:           NewPreWarmOptions(),
		ReportIntervel:           60 * time.Second,
		AesEncryptKey:            "",
		ReserveCollectorEndpoint: "http://127.0.0.1:8090",
		FuncletEndpoint:          "http://127.0.0.1:8231",
	}
	s := ServerRunOptions{
		RecommendedOptions:      genericoptions.NewRecommendedOptions(),
		FuncletOptions:          funcletOptions,
		GracefulShutdownSeconds: 30,
	}
	s.RecommendedOptions.SecureServing.BindPort = 8231
	//s.RecommendedOptions.TelemetryOptions.ServiceName = ApplicationName
	return &s
}

var (
	TmpBanatCidr    string
	TmpK8sEndpoints = make([]string, 0)
)

// AddFlags 添加 funclet 命令行参数
func (s *ServerRunOptions) AddFlags(fs *pflag.FlagSet) {
	s.RecommendedOptions.AddFlags(fs)
	fs.StringVar(&s.FuncletOptions.RunnerContainerCMD, "runner-cmd", s.FuncletOptions.RunnerContainerCMD, "Command of runner")
	fs.IntVar(&s.FuncletOptions.InvokerPort, "invoker-port", s.FuncletOptions.InvokerPort, "Port of invoker")
	fs.IntVar(&s.FuncletOptions.InvokerDispatcherPort, "invoker-dis-port", s.FuncletOptions.InvokerDispatcherPort, "Port of invoker dispatcher")
	fs.StringVar(&s.FuncletOptions.ListenPath, "listen", s.FuncletOptions.ListenPath, "Unix domain socks of funclet")
	fs.StringVar(&s.FuncletOptions.ListenV2Path, "listen-v2", s.FuncletOptions.ListenV2Path, "Unix domain socks of funclet v2")
	fs.StringVar(&s.FuncletOptions.ListenType, "listentype", s.FuncletOptions.ListenType, "Type of connect (with runner)")
	fs.IntVar(&s.FuncletOptions.ListenPort, "listenport", s.FuncletOptions.ListenPort, "Port of container init")
	fs.StringVar(&s.FuncletOptions.InvokerSocks, "invoker", s.FuncletOptions.InvokerSocks, "Unix domain socks of invoker")
	fs.StringVar(&s.FuncletOptions.FuncletApiSocks, "funclet", s.FuncletOptions.FuncletApiSocks, "Unix domain socks of funclet api")
	fs.StringVar(&s.FuncletOptions.NSMountExecPath, "nsmount", s.FuncletOptions.NSMountExecPath, "Nsmount binary path")
	fs.StringVar(&s.FuncletOptions.RunnerCmdLine, "runner-matcher", s.FuncletOptions.RunnerCmdLine, "Runner process matcher")
	fs.StringVar(&s.FuncletOptions.TargetCodePath, "tcode-path", s.FuncletOptions.TargetCodePath, "Target code path")
	fs.StringVar(&s.FuncletOptions.TargetConfPath, "tconf-path", s.FuncletOptions.TargetConfPath, "Target conf path")
	fs.StringVar(&s.FuncletOptions.TargetRuntimePath, "truntime-path", s.FuncletOptions.TargetRuntimePath, "Target runtime path")
	fs.StringVar(&s.FuncletOptions.TargetLayerPath, "tlayer-path", s.FuncletOptions.TargetLayerPath, "Target layer path")
	fs.StringVar(&s.FuncletOptions.ConfPath, "conf-path", s.FuncletOptions.ConfPath, "Conf path")
	fs.StringVar(&s.FuncletOptions.TmpPath, "tmp-path", s.FuncletOptions.TmpPath, "Tmp path")
	fs.StringVar(&s.FuncletOptions.RuntimePath, "runtime-path", s.FuncletOptions.RuntimePath, "Runtime path")
	fs.StringVar(&s.FuncletOptions.CachePath, "cache-path", s.FuncletOptions.CachePath, "Code cache path")
	fs.StringVar(&s.FuncletOptions.TmpDiskPath, "ext4tmp-path", s.FuncletOptions.TmpDiskPath, "Tmp disk path")
	fs.Int64Var(&s.FuncletOptions.TmpDiskSize, "tmp-disk-size", s.FuncletOptions.TmpDiskSize, "Tmp disk size")
	fs.IntVar(&s.GracefulShutdownSeconds, "graceful-shutdown-seconds", s.GracefulShutdownSeconds, "graceful shutdown seconds")
	fs.StringSliceVar(&s.FuncletOptions.TrafficExcludes, "traffic-excludes", s.FuncletOptions.TrafficExcludes, "Traffic exclude ip list")
	fs.StringSliceVar(&s.FuncletOptions.ForbidAccessIp, "forbid-access-ip", s.FuncletOptions.ForbidAccessIp, "Forbid access ip list")
	fs.StringSliceVar(&s.FuncletOptions.AcceptPortList, "accept-port-list", s.FuncletOptions.AcceptPortList, "accept portlist")

	fs.StringVar(&s.FuncletOptions.RunningMode, "running-mode", s.FuncletOptions.RunningMode, "cloud or ote")
	fs.StringVar(&s.FuncletOptions.DockerAPIVersion, "docker-api-version", s.FuncletOptions.DockerAPIVersion, "set docker server api version")

	fs.StringVar(&s.FuncletOptions.InternalVolumePath, "internal-volume-path", s.FuncletOptions.InternalVolumePath, "funclet container internal volume path")
	fs.StringVar(&s.FuncletOptions.DockerRuntime, "runtime", s.FuncletOptions.DockerRuntime, "runc|kata")
	fs.StringVar(&s.FuncletOptions.KataListenAddr, "kata-listen-addr", s.FuncletOptions.KataListenAddr, "unix vsock")
	fs.StringVar(&s.FuncletOptions.ReserveCollectorEndpoint, "reserve-collector-endpoint", s.FuncletOptions.ReserveCollectorEndpoint, "reserve collector endpoint")
	fs.StringVar(&s.FuncletOptions.FuncletEndpoint, "funclet-endpoint", s.FuncletOptions.FuncletEndpoint, "funclet endpoint")
	fs.StringVar(&s.FuncletOptions.AesEncryptKey, "reserve-collector-AesEncryptKey", s.FuncletOptions.AesEncryptKey, "reserve collector AesEncryptKey")
	fs.DurationVar(&s.FuncletOptions.ReportIntervel, "report-intervel", s.FuncletOptions.ReportIntervel, "report intervel")

	// enable loop device default true
	fs.BoolVar(&s.FuncletOptions.EnableLoopDevice, "enable-loop-device", s.FuncletOptions.EnableLoopDevice, "enable loop sdevice")

	fs.StringVar(&TmpBanatCidr, "banat-cidr", TmpBanatCidr, "tmp option")
	fs.StringSliceVar(&TmpK8sEndpoints, "k8s-endpoints", TmpK8sEndpoints, "tmp option")

	// add local store flags
	s.FuncletOptions.LocalStoreOptions.AddFlags(fs)

	s.FuncletOptions.ConnBackPoolOptions.AddFlags(fs)
	s.FuncletOptions.CronOptions.AddFlags(fs)
	s.FuncletOptions.CriOptions.AddFlags(fs)
	s.FuncletOptions.SecureOptions.AddFlags(fs)
	s.FuncletOptions.PreWarmOptions.AddFlags(fs)
}
