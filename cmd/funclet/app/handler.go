/**
 * Created Date: Thursday, September 21st 2017, 4:40:35 pm
 * Author: hefangshi
 * Copyright (c) 2017 Baidu.Inc
 *
 */

package app

import (
	"errors"
	"net/http"
	"strconv"
	"time"

	"go.uber.org/zap"

	"icode.baidu.com/baidu/faas/kun/pkg/api"
	kunErr "icode.baidu.com/baidu/faas/kun/pkg/error"
	funcletErr "icode.baidu.com/baidu/faas/kun/pkg/funclet/error"
	"icode.baidu.com/baidu/faas/kun/pkg/server"
)

func (server *Server) initOneColdContainerHandle(c *server.Context) {
	response := c.Response()
	var params *api.InitOneColdContainerRequest
	if err := c.Request().ReadEntity(&params); err != nil {
		c.WithWarnLog(kunErr.NewInvalidParameterValueException("", err).WithWarnLog()).WriteTo(response)
		return
	}
	logger := c.Logger()
	fetchSession := c.Request().HeaderParameter(api.HeaderXFetchSession)
	if fetchSession != "" {
		logger = logger.WithField(api.HeaderXFetchSession, fetchSession)
	}
	logger = logger.With(zap.String("brn", params.Configuration.FunctionArn),
		zap.String("runtime", params.Configuration.Runtime))
	logger.V(3).Infof("Init params")
	context := server.Funclet.NewContext(c.Context(), c.RequestID(), logger)
	defer logger.TimeTrack(time.Now(), "Init one cold container")
	res, err := server.Funclet.InitOneColdContainer(context, params, true)
	if err != nil {
		// 出错时 也将containerInfo信息返回给poolmgr
		var fErr = kunErr.FinalError{}
		if kerr, ok := err.(funcletErr.FuncletError); ok {
			fErr = funcletErr.NewContainerException(funcletErr.ContainerInitException, kerr.ErrorLevel, kerr.Err).WithWarnLog()
		} else {
			fErr = funcletErr.NewContainerException(funcletErr.ContainerInitException, "init one err", err).WithWarnLog()
		}

		res.FError = fErr
		response.WriteHeaderAndEntity(http.StatusOK, res)
		return
	}
	response.WriteHeaderAndEntity(http.StatusCreated, res)
}

func (server *Server) resetContainerHandle(c *server.Context) {
	response := c.Response()
	respRequest := c.Request()
	podName := respRequest.PathParameter("PodName")
	if podName == "" {
		c.WithWarnLog(kunErr.NewInvalidParameterValueException("", errors.New("PodName is empty")).WithWarnLog()).WriteTo(response)
		return
	}

	request := &api.CoolDownPodRequest{}
	originalMemorySizeStr := respRequest.QueryParameter(api.OriginalMemorySizeParam)
	if originalMemorySizeStr != "" {
		originalMemorySize, err := strconv.ParseInt(originalMemorySizeStr, 10, 64)
		if err != nil {
			c.WithErrorLog(funcletErr.NewContainerException(funcletErr.ContainerDestroyException, podName, err).WithWarnLog()).WriteTo(response)
			return
		}
		request.OriginalMemoryLimit = &originalMemorySize
	}
	c.Logger().V(3).Infof("Reset container: %+v", podName)
	defer c.Logger().TimeTrack(time.Now(), "Reset container finish")
	context := server.Funclet.NewContext(c.Context(), c.RequestID(), c.Logger())
	if err := server.Funclet.ResetContainer(context, podName, true, request); err != nil {
		c.WithErrorLog(funcletErr.NewContainerException(funcletErr.ContainerDestroyException, podName, err).WithWarnLog()).WriteTo(response)
		return
	}
	response.WriteHeaderAndEntity(http.StatusNoContent, nil)
}

func (server *Server) resetNodeHandle(c *server.Context) {
	response := c.Response()
	c.Logger().V(3).Infof("Reset node")
	defer c.Logger().TimeTrack(time.Now(), "Reset node finish")
	request := &api.ResetNodeRequest{}
	originalMemorySizeStr := c.Request().QueryParameter(api.OriginalMemorySizeParam)
	if originalMemorySizeStr != "" {
		originalMemorySize, err := strconv.ParseInt(originalMemorySizeStr, 10, 64)
		if err != nil {
			c.WithErrorLog(funcletErr.ResetNodeException(err).WithWarnLog()).WriteTo(c.Response())
			return
		}
		request.OriginalMemoryLimit = &originalMemorySize
	}
	if err := server.Funclet.ResetNode(request); err != nil {
		c.WithErrorLog(funcletErr.ResetNodeException(err).WithWarnLog()).WriteTo(response)
		return
	}
	response.WriteHeaderAndEntity(http.StatusNoContent, nil)
}

func (server *Server) getContainerList(c *server.Context) {
	response := c.Response()
	logger := c.Logger()
	fetchSession := c.Request().HeaderParameter(api.HeaderXFetchSession)
	if fetchSession != "" {
		logger = logger.WithField(api.HeaderXFetchSession, fetchSession)
	}
	logger.V(3).Infof("Get container list")
	defer logger.TimeTrack(time.Now(), "Get container list finish")
	criteria := api.NewListPodCriteria()
	criteria.ReadFromRequest(c.HTTPRequest())
	containerList, err := server.Funclet.GetContainerList(criteria)
	if err != nil {
		c.WithErrorLog(funcletErr.NewContainerException(kunErr.ServiceException, "Talk to invoker failed", err).WithErrorLog()).WriteTo(response)
		return
	}
	response.WriteHeaderAndEntity(http.StatusOK, containerList)
}

func (server *Server) queryContainerTraffic(c *server.Context) {
	response := c.Response()
	var params *api.QueryContainerTrafficRequest
	if err := c.Request().ReadEntity(&params); err != nil {
		c.WithWarnLog(kunErr.NewInvalidParameterValueException("", err).WithWarnLog()).WriteTo(response)
		return
	}
	c.Logger().V(3).Infof("Query traffic params: %+v", params)
	defer c.Logger().TimeTrack(time.Now(), "Query container traffic finish")
	t, err := server.Funclet.QueryContainerTraffic(params.PodName, params.ResetTraffic)
	if err != nil {
		c.WithErrorLog(funcletErr.NewContainerException(funcletErr.ContainerTrafficQueryException, params.PodName, err).WithWarnLog()).WriteTo(response)
		return
	}
	response.WriteHeaderAndEntity(http.StatusOK, t)
}

func (server *Server) UpdateContainersResource(c *server.Context) {
	response := c.Response()

	var params *api.UpdateResourceRequest
	if err := c.Request().ReadEntity(&params); err != nil {
		c.WithWarnLog(kunErr.NewInvalidParameterValueException("", err).WithWarnLog()).WriteTo(response)
		return
	}
	c.Logger().V(3).Infof("resource containers list %v", params.Containers)
	defer c.Logger().TimeTrack(time.Now(), "resource containers finish")
	err := server.Funclet.UpdateContainersResource(params)
	if err != nil {
		c.WithErrorLog(funcletErr.NewContainerException(kunErr.ServiceException, "UpdateContainersResource failed", err).WithErrorLog()).WriteTo(response)
		return
	}
	containerList, err := server.Funclet.GetContainerList(api.NewListPodCriteria())
	if err != nil {
		c.WithErrorLog(funcletErr.NewContainerException(kunErr.ServiceException, "Talk to invoker failed", err).WithErrorLog()).WriteTo(response)
		return
	}
	response.WriteHeaderAndEntity(http.StatusOK, containerList)
}

func (server *Server) getZombieContainerList(c *server.Context) {
	response := c.Response()
	c.Logger().V(3).Infof("Get zombie container list")
	defer c.Logger().TimeTrack(time.Now(), "Get zombie container list finish")
	containerList, err := server.Funclet.GetZombieContainerList()
	if err != nil {
		c.WithErrorLog(funcletErr.NewContainerException(kunErr.ServiceException, "GetZombieContainerList failed", err).WithErrorLog()).WriteTo(response)
		return
	}
	response.WriteHeaderAndEntity(http.StatusOK, containerList)
}

func (server *Server) freezeContainerHandle(c *server.Context) {
	response := c.Response()

	var params *api.FreezeRequest
	if err := c.Request().ReadEntity(&params); err != nil {
		c.WithWarnLog(kunErr.NewInvalidParameterValueException("", err).WithWarnLog()).WriteTo(response)
		return
	}
	err := server.Funclet.FreezeContainer(params.PodName, params.State)
	if err != nil {
		c.WithErrorLog(funcletErr.NewContainerException(kunErr.ServiceException, "FreezeContainer failed", err).WithErrorLog()).WriteTo(response)
		return
	}
	response.WriteHeaderAndEntity(http.StatusOK, nil)
}

func (server *Server) reportContainerHandle(c *server.Context) {
	response := c.Response()

	var params *api.ReportContainerStateRequest
	if err := c.Request().ReadEntity(&params); err != nil {
		c.WithWarnLog(kunErr.NewInvalidParameterValueException("", err).WithWarnLog()).WriteTo(response)
		return
	}
	if params.State != api.ReportStateConnected {
		c.WithWarnLog(kunErr.NewInvalidParameterValueException("state error", nil).WithWarnLog()).WriteTo(response)
		return
	}
	c.Logger().V(3).Infof("report container params: %+v", params)

	err := server.Funclet.ReportContainerState(params)
	if err != nil {
		c.WithErrorLog(funcletErr.NewContainerException(kunErr.ServiceException, "ReportContainerState failed", err).WithErrorLog()).WriteTo(response)
		return
	}
	response.WriteHeaderAndEntity(http.StatusOK, nil)
}

func (server *Server) getSickPodsHandle(c *server.Context) {
	response := c.Response()
	logger := c.Logger()
	logger.V(3).Infof("Get sick pods")
	defer logger.TimeTrack(time.Now(), "Get sick pods finish")

	sickPodList, err := server.Funclet.GetAllSickPods()
	if err != nil {
		c.WithErrorLog(funcletErr.NewContainerException(kunErr.ServiceException, "Get sick pods failed", err).WithErrorLog()).WriteTo(response)
		return
	}
	response.WriteHeaderAndEntity(http.StatusOK, sickPodList)
}

func (server *Server) putSickPodHandle(c *server.Context) {
	response := c.Response()
	logger := c.Logger()
	logger.V(3).Infof("Put sick pod")
	defer logger.TimeTrack(time.Now(), "Put sick pod finish")

	var params *api.SickPodRequest
	if err := c.Request().ReadEntity(&params); err != nil {
		c.WithWarnLog(kunErr.NewInvalidParameterValueException("", err).WithWarnLog()).WriteTo(response)
		return
	}
	err := server.Funclet.PutSickPod(params.PodName)
	if err != nil {
		c.WithErrorLog(funcletErr.NewContainerException(kunErr.ServiceException, "Put sick pod failed", err).WithErrorLog()).WriteTo(response)
		return
	}
	response.WriteHeaderAndEntity(http.StatusOK, nil)
}

func (server *Server) deleteSickPodHandle(c *server.Context) {
	response := c.Response()
	logger := c.Logger()

	logger.V(3).Infof("Delete sick pod")
	defer logger.TimeTrack(time.Now(), "Delete sick pod finish")

	var params *api.SickPodRequest
	if err := c.Request().ReadEntity(&params); err != nil {
		c.WithWarnLog(kunErr.NewInvalidParameterValueException("", err).WithWarnLog()).WriteTo(response)
		return
	}
	err := server.Funclet.DeleteSickPod(params.PodName)
	if err != nil {
		c.WithErrorLog(funcletErr.NewContainerException(kunErr.ServiceException, "Delete sick pod failed", err).WithErrorLog()).WriteTo(response)
		return
	}
	response.WriteHeaderAndEntity(http.StatusOK, nil)

}

func (server *Server) listImages(c *server.Context) {
	response := c.Response()
	defer c.Logger().TimeTrack(time.Now(), "list images finish")
	var getALl bool
	if c.Request().QueryParameter("type") == "all" {
		getALl = true
	}
	l, err := server.Funclet.GetImagesList(getALl)
	if err != nil {
		c.WithErrorLog(funcletErr.NewContainerException(kunErr.ServiceException, "list images", err).WithErrorLog()).WriteTo(response)
		return
	}
	response.WriteHeaderAndEntity(http.StatusOK, l)
}

func (server *Server) checkAndPullImage(c *server.Context) {
	response := c.Response()
	defer c.Logger().TimeTrack(time.Now(), "pullImage finish")
	var params *api.FuncletCheckAndPullImagesInput
	if err := c.Request().ReadEntity(&params); err != nil {
		c.WithWarnLog(kunErr.NewInvalidParameterValueException("", err).WithWarnLog()).WriteTo(response)
		return
	}
	m, err := server.Funclet.CheckAndPullImage(params.Images)
	if err != nil {
		c.WithErrorLog(funcletErr.NewContainerException(kunErr.ServiceException, "CheckAndPullImage", err).WithErrorLog())
		return
	}
	response.WriteHeaderAndEntity(http.StatusOK, &api.FuncletCheckAndPullImagesResponse{
		Results: m,
		Err:     err,
	})
}
