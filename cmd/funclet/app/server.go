/**
 * Created Date: Friday, August 11th 2017, 3:39:27 pm
 * Author: hefangshi
 * -----
 * Last Modified: Mon Aug 14 2017
 * Modified By: hefangshi
 * -----
 * Copyright (c) 2017 Baidu.Inc
 *
 */

package app

import (
	"net"
	"os"

	"icode.baidu.com/baidu/faas/kun/cmd/funclet/app/options"
	"icode.baidu.com/baidu/faas/kun/pkg/funclet"
	genericserver "icode.baidu.com/baidu/faas/kun/pkg/server"
	"icode.baidu.com/baidu/faas/kun/pkg/util/logs"
	"icode.baidu.com/baidu/faas/kun/pkg/version"
)

// Server Funclet 服务
type Server struct {
	Option  *options.ServerRunOptions
	Funclet *funclet.Funclet
}

// NewFuncletServer 创建 Funclet 服务
func NewFuncletServer(s *options.ServerRunOptions) (*Server, error) {
	f, err := funclet.NewFunclet(s.FuncletOptions)
	if err != nil {
		return nil, err
	}
	err = f.LocalStoreManager.InitLocalStore(s.FuncletOptions.LocalStoreOptions)
	if err != nil {
		return nil, err
	}
	f.ReportManager.SetLocalStore(f.LocalStoreManager)
	//从本地内存中读取未上报的实例
	ris, err := f.LocalStoreManager.GetAllReserveInstances()
	if err != nil {
		return nil, err
	}

	err = f.ReportManager.AddAllReserveInstance(ris)
	if err != nil {
		return nil, err
	}
	// 启动预留实例上报
	stop := make(chan struct{})
	f.ReportManager.ReportReserveInstanceTask(stop)
	err = f.Init()
	if err != nil {
		return nil, err
	}
	return &Server{
		Funclet: f,
		Option:  s,
	}, nil
}

// Run runs the specified EventhubServer with the given Dependencies.
// This should never exit.
func Run(runOptions *options.ServerRunOptions, stopCh <-chan struct{}) error {
	logs.Infof("Version: %+v", version.Get())
	funcletServer, err := NewFuncletServer(runOptions)
	if err != nil {
		logs.Fatalf("Error: %v", err)
	}

	defer funcletServer.Funclet.LocalStoreManager.CloseLocalStoreClient()
	tcpServer, err := CreateServerChain(funcletServer, runOptions)
	if err != nil {
		return err
	}
	unixServer, err := CreateUnixServerChain(funcletServer, runOptions)
	if err != nil {
		return err
	}
	go unixServer.PrepareRun().Run(stopCh)
	return tcpServer.PrepareRun().Run(stopCh)
}

// CreateServerChain creates the apiservers connected via delegation.
func CreateServerChain(server *Server, runOptions *options.ServerRunOptions) (*genericserver.GenericServer, error) {
	config := genericserver.NewRecommendedConfig()
	if err := runOptions.RecommendedOptions.ApplyTo(config); err != nil {
		return nil, err
	}
	s, err := config.Complete().New(options.ApplicationName)
	if err != nil {
		return nil, err
	}
	s.AddPostStartHook("demoPostStartHook", PostStartHook("funclet tcp"))
	s.AddPreShutdownHook("demoPreShutdownHook", PreShutdownHook("funclet tcp"))
	//s.AddPreShutdownHook("telemetryShutdownHook", config.TelemetryInfo.Stop)
	server.InstallAPI(s.Handler.GoRestfulContainer)
	return s, nil
}

func CreateUnixServerChain(server *Server, runOptions *options.ServerRunOptions) (*genericserver.GenericServer, error) {
	config := genericserver.NewRecommendedConfig()
	if err := runOptions.RecommendedOptions.ApplyTo(config); err != nil {
		return nil, err
	}
	socksPath := runOptions.FuncletOptions.FuncletApiSocks
	os.RemoveAll(socksPath)
	ln, err := net.Listen("unix", socksPath)
	if err != nil {
		return nil, err
	}
	os.Chmod(socksPath, os.ModePerm)
	config.SecureServingInfo = &genericserver.SecureServingInfo{
		Listener: ln,
	}
	s, err := config.Complete().New(options.ApplicationName + "_unix")
	if err != nil {
		return nil, err
	}
	s.AddPostStartHook("demoPostStartHook", PostStartHook("funclet unix"))
	s.AddPreShutdownHook("demoPreShutdownHook", PreShutdownHook("funclet unix"))
	//s.AddPreShutdownHook("telemetryShutdownHook", config.TelemetryInfo.Stop)
	server.InstallUnixAPI(s.Handler.GoRestfulContainer)
	return s, nil
}

// PostStartHook xxx
func PostStartHook(name string) genericserver.PostStartHookFunc {
	return func(context genericserver.PostStartHookContext) error {
		logs.V(5).Infof("%s postStartHook here", name)
		return nil
	}
}

// PreShutdownHook xxx
func PreShutdownHook(name string) genericserver.PreShutdownHookFunc {
	return func() error {
		logs.V(5).Infof("%s preShutdownHook here", name)
		return nil
	}
}
