/**
 * Created Date: Tuesday, July 25th 2017, 1:28:36 pm
 * Author: hefangshi
 * -----
 * Last Modified: Mon Aug 14 2017
 * Modified By: hefangshi
 * -----
 * Copyright (c) 2017 Baidu.Inc
 *
 */

package main

/*
 #cgo CFLAGS: -Wall
 extern void nsexec();

  __attribute__((constructor)) void enter_namespace(void) {
	  nsexec();
 }
*/
import "C"
import (
	"fmt"
	"os"

	"github.com/spf13/pflag"

	"icode.baidu.com/baidu/faas/kun/cmd/funclet/app"
	"icode.baidu.com/baidu/faas/kun/cmd/funclet/app/options"
	"icode.baidu.com/baidu/faas/kun/pkg/server"
	"icode.baidu.com/baidu/faas/kun/pkg/util/flag"
	"icode.baidu.com/baidu/faas/kun/pkg/util/logs"
	"icode.baidu.com/baidu/faas/kun/pkg/version/verflag"
)

func main() {
	s := options.NewFuncletServerOptions()
	s.AddFlags(pflag.CommandLine)
	flag.InitFlags()

	logs.InitLogs()
	defer logs.FlushLogs()

	verflag.PrintAndExitIfRequested()

	stopCh := server.SetupSignalHandler()
	if err := app.Run(s, stopCh); err != nil {
		fmt.Fprintf(os.Stderr, "%v\n", err)
		os.Exit(1)
	}
}
