package main

import (
	"fmt"
	"os"
	"regexp"
	"strings"

	"github.com/spf13/cobra"
	"github.com/spf13/pflag"

	"icode.baidu.com/baidu/faas/kun/pkg/bce/iam"
	"icode.baidu.com/baidu/faas/kun/pkg/bce/opscenter"
)

var useridLen int
var useridPat *regexp.Regexp

func init() {
	useridLen = len("00dc1b52d8354d9193536e4dd2c41ae6")
	useridPat, _ = regexp.Compile("^[a-z0-9]{32}$")
}

var whitelistEndpoint string

type WhiteListOpts struct {
	Endpoint  string
	IamConfig string
	Product   string
}

type WhiteListClient struct {
	IAMClient   iam.ClientInterface
	SettingsCli opscenter.OpsCenterInterface
}

func checkID(id string) bool {
	if len(id) != useridLen {
		return false
	}
	if !useridPat.Match([]byte(id)) {
		return false
	}
	return true
}

func newSetCommand(client *WhiteListClient) *cobra.Command {
	cmd := &cobra.Command{
		Use:   "set userid service",
		Short: "为用户指定服务开通白名单",
		Args: func(cmd *cobra.Command, args []string) error {
			if len(args) != 2 {
				return fmt.Errorf("accepts 2 arg(s), received %d", len(args))
			}
			if !checkID(args[0]) {
				return fmt.Errorf("UserID:%s 格式错误", args[0])
			}
			return nil
		},
		RunE: func(cmd *cobra.Command, args []string) error {
			userid := args[0]
			service := strings.ToUpper(args[1])
			err := client.SettingsCli.AddUserNavigationList(service, userid)
			if err != nil {
				return err
			}
			fmt.Println("OK")
			return nil
		},
	}
	return cmd
}

func newListCommand(client *WhiteListClient) *cobra.Command {
	cmd := &cobra.Command{
		Use:   "list userid",
		Short: "显示用户已经开通白名单的服务列表",
		Args: func(cmd *cobra.Command, args []string) error {
			if len(args) != 1 {
				return fmt.Errorf("accepts 1 arg(s), received %d", len(args))
			}
			if !checkID(args[0]) {
				return fmt.Errorf("UserID:%s 格式错误", args[0])
			}
			return nil
		},
		RunE: func(cmd *cobra.Command, args []string) error {
			userid := args[0]
			services, err := client.SettingsCli.GetUserNavigationList(userid)
			if err != nil {
				return err
			}

			fmt.Printf("已开通服务列表: %s\n", strings.Join(services, ", "))
			return nil
		},
	}
	return cmd
}

func newRootCommand(client *WhiteListClient) *cobra.Command {
	opts := WhiteListOpts{}
	var flags *pflag.FlagSet
	cmd := &cobra.Command{
		Use:              "whitelist [OPTIONS] COMMAND [ARG...]",
		Short:            "开服务白名单小工具",
		TraverseChildren: true, // 父cmd先解析参数
		Args:             cobra.NoArgs,
		RunE: func(cmd *cobra.Command, args []string) error {
			return cmd.Help()
		},
		PersistentPreRunE: func(cmd *cobra.Command, args []string) error {
			iamCli, err := iam.CreateIAMClient(opts.IamConfig)
			if err != nil {
				return err
			}
			setcli, err := opscenter.NewOpsCenter(iamCli, opts.Endpoint)
			if err != nil {
				return err
			}
			client.IAMClient = iamCli
			client.SettingsCli = setcli
			return nil
		},
	}
	flags = cmd.Flags()
	flags.StringVarP(&opts.Endpoint, "endpoint", "e", "http://10.107.37.48:8690", "白名单服务endpoint")
	flags.StringVarP(&opts.IamConfig, "iam-config", "c", "bj.yaml", "IAM服务配置文件")
	cmd.AddCommand(newSetCommand(client), newListCommand(client))
	return cmd
}

func main() {
	client := &WhiteListClient{}
	cmd := newRootCommand(client)
	if err := cmd.Execute(); err != nil {
		fmt.Println(err)
		os.Exit(1)
	}
}
