package app

import (
	"icode.baidu.com/baidu/faas/kun/cmd/httptrigger/app/options"
	"icode.baidu.com/baidu/faas/kun/pkg/httptrigger"
	"icode.baidu.com/baidu/faas/kun/pkg/httptrigger/proxy"
	"icode.baidu.com/baidu/faas/kun/pkg/server"
	"icode.baidu.com/baidu/faas/kun/pkg/util/logs"
	"icode.baidu.com/baidu/faas/kun/pkg/version"
)

// Run runs the specified ApiGatewayServer with the given Dependencies.
// This should never exit.
func Run(runOptions *options.HTTPTriggerOptions, stopCh <-chan struct{}) error {
	logs.Infof("Version: %+v", version.Get())

	s, err := CreateServerChain(runOptions, stopCh)
	if err != nil {
		return err
	}

	return s.PrepareRun().Run(stopCh)
}

// CreateServerChain creates the http-trigger connected via delegation.
func CreateServerChain(runOptions *options.HTTPTriggerOptions, stopCh <-chan struct{}) (*server.GenericServer, error) {
	config := server.NewRecommendedConfig()
	if err := runOptions.RecommendedOptions.ApplyTo(config); err != nil {
		return nil, err
	}

	s, err := config.Complete().New(options.ApplicationName)
	if err != nil {
		return nil, err
	}

	// Install hook
	s.AddPostStartHook("demoPostStartHook", httptrigger.PostStartHook)
	s.AddPreShutdownHook("demoPreShutdownHook", httptrigger.PreShutdownHook)

	// Install Proxy
	proxyInstance, err := proxy.Init(runOptions)
	if err != nil {
		return nil, err
	}
	s.Handler.NonGoRestfulMux.HandlePrefix(proxy.ProxyPrefix, proxyInstance)

	return s, nil
}
