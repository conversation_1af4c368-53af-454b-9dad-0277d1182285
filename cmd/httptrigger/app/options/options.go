package options

import (
	"github.com/spf13/pflag"

	"icode.baidu.com/baidu/faas/kun/pkg/bce/iam"
	eventhub "icode.baidu.com/baidu/faas/kun/pkg/eventhub/client"
	"icode.baidu.com/baidu/faas/kun/pkg/rpc/db"
	kunRedis "icode.baidu.com/baidu/faas/kun/pkg/rpc/redis"
	genericoptions "icode.baidu.com/baidu/faas/kun/pkg/server/options"
)

const (
	// ApplicationName 指定应用名称
	ApplicationName = "httptrigger"
)

// HTTPTriggerOptions describe options that api gateway needs
type HTTPTriggerOptions struct {
	RecommendedOptions  *genericoptions.RecommendedOptions
	IAMOptions          *iam.IAMOptions
	GenericRedisOptions *kunRedis.RedisOptions
	EventHubOptions     *eventhub.EventHubOptions
	DbConfig            *db.DbConfig
}

// NewHTTPTriggerOptions creates a new HTTPTriggerOptions object with default parameters
func NewHTTPTriggerOptions() *HTTPTriggerOptions {
	s := HTTPTriggerOptions{
		RecommendedOptions:  genericoptions.NewRecommendedOptions(),
		IAMOptions:          iam.NewIAMOptions(),
		GenericRedisOptions: kunRedis.NewRedisOptions(),
		EventHubOptions: &eventhub.EventHubOptions{
			Host: "127.0.0.1",
			Port: 8100,
		},
		DbConfig: db.NewDbConfig(),
	}
	//s.RecommendedOptions.TelemetryOptions.ServiceName = ApplicationName
	return &s
}

// AddFlags adds flags for a specific APIServer to the specified FlagSet
func (s *HTTPTriggerOptions) AddFlags(fs *pflag.FlagSet) {
	s.IAMOptions.AddUniversalFlags(fs)
	s.RecommendedOptions.AddFlags(fs)
	s.GenericRedisOptions.AddRedisFlags(fs)
	s.EventHubOptions.AddEventHubFlags(fs)
	s.DbConfig.AddFlags(fs)
}
