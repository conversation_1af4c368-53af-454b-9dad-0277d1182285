/**
 * Created Date: Tuesday, July 10th 2018, 3:11:53 pm
 * Author: hefangshi
 * Copyright (c) 2018 Baidu.Inc
 *
 */

package main

import (
	"fmt"
	"os"

	"github.com/spf13/pflag"

	"icode.baidu.com/baidu/faas/kun/cmd/eventhub/app"
	"icode.baidu.com/baidu/faas/kun/cmd/eventhub/app/options"
	poolmgrOptions "icode.baidu.com/baidu/faas/kun/cmd/poolmanager/app/options"
	"icode.baidu.com/baidu/faas/kun/pkg/eventhub"
	"icode.baidu.com/baidu/faas/kun/pkg/eventhub/rpc"
	"icode.baidu.com/baidu/faas/kun/pkg/poolmanager/client"
	"icode.baidu.com/baidu/faas/kun/pkg/server"
	"icode.baidu.com/baidu/faas/kun/pkg/util/flag"
	"icode.baidu.com/baidu/faas/kun/pkg/util/logs"
	"icode.baidu.com/baidu/faas/kun/pkg/version/verflag"
)

func main() {
	s := options.NewEventhubOptions()

	s.AddFlags(pflag.CommandLine)

	p := poolmgrOptions.NewPoolManagerOptions()
	p.AddUniqueFlags("poolmanager", pflag.CommandLine)

	flag.InitFlags()
	logs.InitLogs()
	defer logs.FlushLogs()
	verflag.PrintAndExitIfRequested()

	stopCh := server.SetupSignalHandler()

	// Poolmanager 复用 Eventhub 的 ETCD 配置
	p.GenericEtcdOptions = s.EtcdOptions
	// Poolmanager 复用 Eventhub 的 RunningMode 配置
	p.RunningMode = s.RunningMode

	poolManagerClient := client.NewPMFuncCallClient(p)

	core, err := eventhub.Init(s, stopCh)
	core.Clients.PodClient = poolManagerClient
	core.Clients.PodCacheClient = rpc.NewPodCacheScheduler(logs.NewLogger(), poolManagerClient, stopCh)
	if err != nil {
		fmt.Fprintf(os.Stderr, "%v\n", err)
		os.Exit(1)
	}

	config, err := app.GenericServerConfig(s)
	if err != nil {
		fmt.Fprintf(os.Stderr, "%v\n", err)
		os.Exit(1)
	}
	svr, err := app.CreateServerChainWithEventhub(core, config)
	if err != nil {
		fmt.Fprintf(os.Stderr, "%v\n", err)
		os.Exit(1)
	}

	if err := svr.PrepareRun().Run(stopCh); err != nil {
		fmt.Fprintf(os.Stderr, "%v\n", err)
		os.Exit(1)
	}
}
