{"Code": {"Location": "https://runtime-test.bj.bcebos.com/python2.zip", "RepositoryType": ""}, "Configuration": {"Id": 4175, "Uid": "42f6fbc2cd374bfcb80d9967370fd8ff", "Description": "update config", "FunctionBrn": "brn:bce:cfc:bj:1a2cbf55b97ac8a7c760c4177db4e17d:function:bce_python_sdk_test_1528718240:$LATEST", "Region": "bj", "Timeout": 8, "VersionDesc": "", "UpdatedAt": "2018-06-11T19:57:20+08:00", "LastModified": "2018-06-11T19:57:20+08:00", "CodeSha256": "eUjyfeUS3/ha4cSS1h2rbprcBThjgWkEXjuGLpPMPgY=", "CodeSize": 630, "FunctionArn": "brn:bce:cfc:bj:1a2cbf55b97ac8a7c760c4177db4e17d:function:bce_python_sdk_test_1528718240:$LATEST", "FunctionName": "bce_python_sdk_test_1528718240", "Handler": "index.handler", "Version": "$LATEST", "Runtime": "python2", "MemorySize": 128, "Environment": {"Variables": {"dDdDdD": "1"}}, "CommitId": "962a5b42-ecb8-4b22-aa3f-496784b422d9", "LogType": null}, "EnviromentVariables": [{"Key": "dDdDdD", "Value": "1"}]}