{"Code": {"Location": "https://runtime-test.bj.bcebos.com/python3.zip", "RepositoryType": ""}, "Configuration": {"Id": 1011, "Uid": "42f6fbc2cd374bfcb80d9967370fd8ff", "Description": "", "FunctionBrn": "brn:bce:cfc:bj:1a2cbf55b97ac8a7c760c4177db4e17d:function:test:$LATEST", "Region": "bj", "Timeout": 3, "VersionDesc": "", "Role": "BceServiceRole_cfc", "UpdatedAt": "2018-01-31T15:07:14+08:00", "LastModified": "2018-01-31T15:07:14+08:00", "CodeSha256": "FI+ByQv/sK8ZDRFevB041wr6zy4JFb/fCLjGZB7Az5M=", "CodeSize": 215, "FunctionArn": "brn:bce:cfc:bj:1a2cbf55b97ac8a7c760c4177db4e17d:function:test:$LATEST", "FunctionName": "test", "Handler": "index.handler", "Version": "$LATEST", "Runtime": "python3", "MemorySize": 128, "Environment": {"Variables": {"key1": "haha", "key2": "hehe"}}, "CommitId": "962a5b42-ecb8-4b22-aa3f-496784b422d9"}, "EnviromentVariables": [{"Key": "key1", "Value": "haha"}, {"Key": "key2", "Value": "hehe"}]}