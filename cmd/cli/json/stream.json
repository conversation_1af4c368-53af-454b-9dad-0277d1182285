{"RequestID": "111", "PodName": "e3f9b2b410bb", "Code": {"Location": "https://runtime-test.cdn.bcebos.com/java8_stream.zip", "RepositoryType": ""}, "Configuration": {"Id": 1011, "Uid": "42f6fbc2cd374bfcb80d9967370fd8ff", "Description": "", "FunctionBrn": "brn:bce:cfc:bj:1a2cbf55b97ac8a7c760c4177db4e17d:function:test:$LATEST", "Region": "bj", "Timeout": 3, "VersionDesc": "", "Role": "BceServiceRole_cfc", "UpdatedAt": "2018-01-31T15:07:14+08:00", "LastModified": "2018-01-31T15:07:14+08:00", "CodeSha256": "7toCHtxYOtQ9WwlbrwMkhLyq/shrPDz9dzegMFNw4XM=", "CodeSize": 215, "FunctionArn": "brn:bce:cfc:bj:1a2cbf55b97ac8a7c760c4177db4e17d:function:test:$LATEST", "FunctionName": "test", "Handler": "com.baidu.demo.Handler", "Version": "$LATEST", "Runtime": "java8_stream", "MemorySize": 128, "Environment": {"Variables": {"key1": "haha", "key2": "hehe"}}, "CommitID": "962a5b42-ecb8-4b22-aa3f-496784b422d9"}, "EnviromentVariables": [{"Key": "key1", "Value": "haha"}, {"Key": "key2", "Value": "hehe"}]}