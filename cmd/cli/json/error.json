{"Code": {"Location": "http://sl-test.bos.qasandbox.bcetest.baidu.com/sl-error_1ada7eb937c28403fcecbacdf063deaabf652b9307846a1c208c74fc4ae46a.zip?authorization=bce-auth-v1/4a3edfae6da4434fa9c7ceb042a9ca7a/2018-11-02T09:30:03Z/-1/host/cfafd3d2dc41a639d1e1d6e8d659c1f9c6468ae66722eaf96c1c72345aae9316", "RepositoryType": ""}, "Configuration": {"Id": 1011, "Uid": "42f6fbc2cd374bfcb80d9967370fd8ff", "Description": "", "FunctionBrn": "brn:bce:cfc:bj:1a2cbf55b97ac8a7c760c4177db4e17d:function:test:$LATEST", "Region": "bj", "Timeout": 3, "VersionDesc": "", "Role": "BceServiceRole_cfc", "UpdatedAt": "2018-01-31T15:07:14+08:00", "LastModified": "2018-01-31T15:07:14+08:00", "CodeSha256": "Gtp+uTQ3woQD/Oy6zfBj3qq/ZSuTB4RqHCCMdPxK5Go=", "CodeSize": 540176, "FunctionArn": "brn:bce:cfc:bj:640c8817bd1de2928d47256dd0620ce5:function:sl-error:$LATEST", "FunctionName": "test", "Handler": "index.handler", "Version": "$LATEST", "Runtime": "nodejs6.11", "MemorySize": 128, "Environment": {"Variables": {"key1": "haha", "key2": "hehe"}}, "CommitId": "99c570e4-e22c-47eb-b9c7-9952cd94d949"}, "EnviromentVariables": [{"Key": "key1", "Value": "haha"}, {"Key": "key2", "Value": "hehe"}]}