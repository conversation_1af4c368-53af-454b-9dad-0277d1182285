package main

import (
	"github.com/spf13/pflag"

	"icode.baidu.com/baidu/faas/kun/cmd/invoker/app"
	"icode.baidu.com/baidu/faas/kun/pkg/funclet/client"
	"icode.baidu.com/baidu/faas/kun/pkg/server"
	"icode.baidu.com/baidu/faas/kun/pkg/util/flag"
	"icode.baidu.com/baidu/faas/kun/pkg/util/logs"
	"icode.baidu.com/baidu/faas/kun/pkg/version/verflag"
)

type KunInvoker struct {
	// dispatcher *app.DispatchServer
	webinvoker *app.WebInvokeServer
	// httpproxy  *app.HttpProxyServer
	runtimeMap *app.RuntimeMap
}

var kunInvoker *KunInvoker

func main() {
	options := app.NewInvokerOptions()
	options.AddFlags(pflag.CommandLine)
	flag.InitFlags()

	options.LogServer.UserLogFilePath = options.DispatcherV2.UserLogFilePath
	logs.InitLogs()
	app.InitLogs()
	defer logs.FlushLogs()

	verflag.PrintAndExitIfRequested()

	runtimeMap := app.NewRuntimeMap()

	var logServer app.LogStatServer
	if options.RunningMode == "cloud" {
		logServer = app.NewLogStatServer(options.LogServer)
	}
	funcletOpt := &client.FuncletClientOptions{
		AccessMode: client.AccessModeNode,
		ApiSock:    options.FuncletEndpoint,
	}
	funcletCli := client.NewInnerFuncletClient(funcletOpt)
	v2logServer := app.NewDispatchServerV2(options.DispatcherV2, funcletCli, runtimeMap)
	manager := app.NewLogStatManager(options.LogServer, logServer, v2logServer)

	options.LogReport.Region = options.Region
	logReporter, err := app.NewLogReporter(options.LogReport)
	if err != nil {
		logs.Errorf("init log reporter failed, %s", err.Error())
	}
	freezer := app.NewPodFreezer(options.Freezer, funcletCli)

	webinvoker := app.NewWebInvokeServer(options.Webserver,
		logReporter, manager, freezer, runtimeMap)
	if options.RunningMode == "cloud" {
		app.StartTrafficCounter(runtimeMap, funcletCli, options.DispatcherV2.ServerAddress)
		defer app.StopTrafficCounter()
	}
	kunInvoker = &KunInvoker{
		webinvoker: webinvoker,
		runtimeMap: runtimeMap,
	}

	logs.Info("init done")
	go logServer.ListenAndServe()
	go v2logServer.ListenAndServe()

	webinvoker.Run(options, server.SetupSignalHandler())
}
