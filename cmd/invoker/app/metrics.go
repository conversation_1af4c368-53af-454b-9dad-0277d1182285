package app

import (
	"bytes"
	"fmt"
	"time"

	"go.uber.org/zap"

	"github.com/prometheus/client_golang/prometheus"

	"icode.baidu.com/baidu/faas/kun/pkg/api"
	"icode.baidu.com/baidu/faas/kun/pkg/util/logs"
)

type invokeStage int

const (
	StagePrepareTraffic invokeStage = iota
	StageReadEntity
	StageCheckRequest
	StageThawRuntime
	StageWaitRuntime
	StageInvokeFunc
	StageInvokeDone
	StageStopRecvLog
	StageWriteEntity
	StageFreezeRuntime
	StageUserlogReport
)

var (
	applog            *logs.Logger
	invokeStageMetric = newInvokeStageMetric()
)

func InitLogs() {
	applog = logs.NewLogger().WithField(api.AppNameKey, "invoker")
}

func newInvokeStageMetric() *prometheus.SummaryVec {
	metric := prometheus.NewSummaryVec(prometheus.SummaryOpts{
		Namespace: "invoker",
		Name:      "invoke_costms",
		MaxAge:    10 * time.Minute,
		Objectives: map[float64]float64{
			0.6:  0.05,
			0.8:  0.02,
			0.9:  0.01,
			0.95: 0.001,
			0.99: 0.001,
		},
	}, []string{"stage"})
	prometheus.MustRegister(metric)
	return metric
}

func (s invokeStage) String() string {
	return []string{
		"prepare_traffic",
		"read_entity",
		"check_request",
		"thaw_runtime",
		"wait_runtime",
		"invoke_func",
		"invoke_done",
		"stop_recvlog",
		"write_entity",
		"freeze_runtime",
		"userlog_report",
	}[s]
}

type metricStage struct {
	stage    invokeStage
	duration time.Duration
}

func (s *metricStage) String() string {
	return fmt.Sprintf("%s=%.3f", s.stage.String(), s.duration.Seconds()*1e3)
}

type invokeMetric struct {
	requestId    string
	startInvoke  time.Time
	startStage   time.Time
	metricStages []*metricStage
	durationMap  api.DurationMap // 与metricStages保持同步
}

func newInvokeMetric(reqid string) *invokeMetric {
	now := time.Now()
	return &invokeMetric{
		requestId:    reqid,
		startInvoke:  now,
		startStage:   now,
		metricStages: make([]*metricStage, 0, StageUserlogReport+1),
		durationMap:  make(api.DurationMap),
	}
}

func (m *invokeMetric) StepDone(s invokeStage) {
	now := time.Now()
	duration := now.Sub(m.startStage)
	m.metricStages = append(m.metricStages, &metricStage{
		stage:    s,
		duration: duration,
	})
	m.startStage = now

	m.durationMap[s.String()] = duration
}

func (m *invokeMetric) ObserveMetric(l *logs.Logger) {
	now := time.Now()
	var fields []zap.Field
	if l.V(6) != nil {
		fields = make([]zap.Field, 0, len(m.metricStages)+1)
	}
	for _, s := range m.metricStages {
		costms := s.duration.Seconds() * 1e3
		invokeStageMetric.WithLabelValues(s.stage.String()).Observe(costms)
		if l.V(6) != nil {
			fields = append(fields, zap.String(s.stage.String(),
				fmt.Sprintf("%.3f", costms)))
		}
	}
	costms := now.Sub(m.startInvoke).Seconds() * 1e3
	invokeStageMetric.WithLabelValues("total_cost").Observe(costms)
	if l.V(6) != nil {
		fields = append(fields, zap.String("total_cost",
			fmt.Sprintf("%.3f", costms)))
		l.Info("invoke metric", fields...)
	}
}

func (m *invokeMetric) String() string {
	now := time.Now()
	buf := bytes.NewBuffer(nil)
	buf.WriteString("requestid=")
	buf.WriteString(m.requestId)
	for _, s := range m.metricStages {
		buf.WriteByte(' ')
		buf.WriteString(s.String())
	}
	buf.WriteByte(' ')
	buf.WriteString(fmt.Sprintf("total_cost=%.3f", now.Sub(m.startInvoke).Seconds()*1e3))
	return buf.String()
}
