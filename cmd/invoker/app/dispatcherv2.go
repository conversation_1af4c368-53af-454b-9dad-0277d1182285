package app

import (
	"bufio"
	"icode.baidu.com/baidu/faas/kun/pkg/util/logs"
	"net"
	"net/http"
	"net/url"
	"os"
	"time"

	"github.com/gorilla/mux"
	"go.uber.org/zap"

	"icode.baidu.com/baidu/faas/kun/pkg/api"
	"icode.baidu.com/baidu/faas/kun/pkg/funclet/client"
	invokerutils "icode.baidu.com/baidu/faas/kun/pkg/invoker/utils"
	"icode.baidu.com/baidu/faas/kun/pkg/util/json"
)

type DispatchServerV2 struct {
	config     *DispatcherV2Options
	runtimeMap *RuntimeMap
	server     *http.Server
	funcletCli client.FuncletInterface

	storeMap storeMap
	statsMap statsMap
}

func NewDispatchServerV2(c *DispatcherV2Options, funcletCli client.FuncletInterface,
	rtMap *RuntimeMap) *DispatchServerV2 {
	ds := &DispatchServerV2{
		config:     c,
		runtimeMap: rtMap,
		funcletCli: funcletCli,
	}
	for _, item := range rtMap.RuntimeList() {
		ds.storeMap.setNXMap(item.RuntimeID)
	}
	return ds
}

func (s *DispatchServerV2) getRouteHandler() *mux.Router {
	r := mux.NewRouter()
	r.HandleFunc("/invoke", s.invokeHandler)
	r.HandleFunc("/stdout", s.stdlogHandler(StdoutLog))
	r.HandleFunc("/stderr", s.stdlogHandler(StderrLog))
	r.HandleFunc("/statistic", s.statisticHandler)
	r.HandleFunc("/netmon", s.netTrafficHandler)
	return r
}

func (s *DispatchServerV2) ListenAndServe() {
	ln, err := invokerutils.ListenerFromAddress(s.config.ServerAddress, os.ModePerm)
	if err != nil {
		applog.Fatalf("listen %s error: %s", s.config.ServerAddress, err.Error())
		return
	}

	router := s.getRouteHandler()
	mux := http.NewServeMux()
	mux.Handle("/", router)
	server := http.Server{
		Handler: mux,
	}

	s.server = &server
	server.Serve(ln)
}

func (s *DispatchServerV2) StartRecvLog(runtimeID string, requestID string, store LogStatStore) {
	stat := s.statsMap.get(runtimeID)
	if stat != nil {
		store.SetMemUsed(stat.memused)
	}
	s.storeMap.set(runtimeID, requestID, store)
}

func (s *DispatchServerV2) StopRecvLog(runtimeID string, requestID string, store LogStatStore) {
	s.storeMap.del(runtimeID, requestID, store)
}

func (s *DispatchServerV2) KillRuntime(runtimeID string) {
}

func setupHijackConn(w http.ResponseWriter, r *http.Request) (net.Conn, *bufio.ReadWriter, bool) {
	runtimeid := r.Header.Get("x-cfc-runtimeid")
	if len(runtimeid) == 0 {
		applog.V(4).Warnf("miss runtimeid %s", runtimeid)
		http.Error(w, "invalid request", http.StatusBadRequest)
		return nil, nil, false
	}

	hijacker, ok := w.(http.Hijacker)
	if !ok {
		applog.V(4).Warnf("hijack failed %s", runtimeid)
		http.Error(w, "invalid request", http.StatusBadRequest)
		return nil, nil, false
	}
	conn, buf, err := hijacker.Hijack()
	if err != nil {
		applog.V(4).Warnf("hijack %s connection error: %s", runtimeid, err.Error())
		http.Error(w, "invalid request", http.StatusBadRequest)
		return nil, nil, false
	}

	conn.Write([]byte{}) // set raw mode

	return conn, buf, true
}

func (s *DispatchServerV2) stdlogHandler(logfrom int) func(w http.ResponseWriter, r *http.Request) {
	return func(w http.ResponseWriter, r *http.Request) {
		conn, buf, ok := setupHijackConn(w, r)
		if !ok {
			return
		}
		defer conn.Close()

		runtimeid := r.Header.Get("x-cfc-runtimeid")
		logs.Infof("stdlogHandler runtimeid %s logfrom %d connect", runtimeid, logfrom)
		defer logs.Infof("stdlogHandler runtimeid %s logfrom %d disconnect", runtimeid, logfrom)

		m, ok := s.storeMap.getMap(runtimeid)
		if !ok {
			logs.Errorf("stdlogHandler can't get map runtimeid %s", runtimeid)
			return
		}
		receiver := m.NewStdLogReceiver(runtimeid, logfrom, buf, 0)
		receiver.Read()
	}
}

func (s *DispatchServerV2) statisticHandler(w http.ResponseWriter, r *http.Request) {
	conn, _, ok := setupHijackConn(w, r)
	if !ok {
		return
	}
	defer conn.Close()

	runtimeid := r.Header.Get("x-cfc-runtimeid")
	nowstat := &statsConn{
		name:    runtimeid,
		conn:    conn,
		memused: 0,
	}
	s.statsMap.set(runtimeid, nowstat)
	defer s.statsMap.del(runtimeid, nowstat)

	receiver := &statinfoReceiver{
		name: runtimeid,
		conn: conn,
	}
	receiver.Recv(func(info *statInfo) error {
		m, ok := s.storeMap.getMap(runtimeid)
		if !ok {
			logs.Errorf("statisticHandler can't get map runtimeid %s", runtimeid)
		}
		if m != nil {
			store, err := m.getLast()
			if err != nil {
				logs.Errorf("statisticHandler map %s can't get last request %s", m.String(), runtimeid)
			}
			if store != nil {
				store.SetMemUsed(info.MemUsed)
			}
		}
		nowstat.memused = info.MemUsed
		return nil
	})
}

func (s *DispatchServerV2) invokeHandler(w http.ResponseWriter, r *http.Request) {
	conn, _,  ok := setupHijackConn(w, r)
	if !ok {
		applog.Error("setupHijackConn failed")
		return
	}
	defer conn.Close()

	runtimeid := r.Header.Get("x-cfc-runtimeid")
	commitid := r.Header.Get("x-cfc-commitid")
	hostip := r.Header.Get("x-cfc-hostip")
	runtime := s.runtimeMap.NewRuntime(runtimeid)
	if runtime == nil { // 确保只有一个runtime能连接成功
		applog.V(3).Errorf("runtime %s already connected", runtimeid)
		return
	}
	defer func() {
		runtime.Close()
		s.runtimeMap.DelRuntime(runtimeid)
	}()
	runtime.SetVersion(commitid)
	runtime.IPv4Address = hostip
	runtime.v2cliConn = conn

	if s.funcletCli != nil { // 通知funclet，runtime已经连接
		err := s.funcletCli.ReportContainerState(&api.FuncletClientReportContainerStateInput{
			Host:    "localhost",
			PodName: runtimeid,
			State:   api.ReportStateConnected})
		if err != nil {
			applog.Error(err.Error())
		}
	}

	params := r.URL.Query()
	applog.V(6).Infof("runtime %s@%s connect", runtimeid, hostip)
	if runtime.handleRuntimeInit(&params, nil) {
		s.runtimeMap.NotifyRuntimeReady(runtimeid)
	}

	params = make(url.Values)
	decoder := json.NewDecoder(conn)
	for {
		var output InvokeResponse
		err := decoder.Decode(&output) // 读取response
		if err != nil {
			applog.V(4).Infof("decode %s response error %s", runtimeid, err.Error())
			return
		}
		applog.V(6).Info("request done.",
			zap.String("podname", runtimeid),
			zap.String("request_id", output.RequestID),
			zap.Bool("success", output.Success))
		if output.Success {
			params.Set("success", "true")
			runtime.handleInvokeDone(output.RequestID, &params, output.FuncResult)
		} else {
			params.Set("success", "false")
			runtime.handleInvokeDone(output.RequestID, &params, output.FuncError)
		}
	}
}

func (s *DispatchServerV2) netTrafficHandler(w http.ResponseWriter, r *http.Request) {
	runtimeid := r.Header.Get("x-cfc-runtimeid")
	var stat networkStat

	if err := json.NewDecoder(r.Body).Decode(&stat); err != nil {
		applog.V(4).Infof("decode netmon traffic stat %s response error %s", runtimeid, err.Error())
		w.WriteHeader(http.StatusBadRequest)
		return
	}

	val, ok := s.runtimeMap.Load(runtimeid)
	if !ok {
		applog.V(4).Infof("load runtimeid %s failed", runtimeid)
		w.WriteHeader(http.StatusNotFound)
		return
	}

	info, _ := val.(*RuntimeInfo)
	if info.UserID == "" {
		w.WriteHeader(http.StatusOK)
		return
	}

	if _counter != nil {
		now := time.Now()
		_counter.storeStats(stat.InBytes, stat.OutBytes, info.UserID, runtimeid, now)
		_counter.clearMap(now)
	}
	w.WriteHeader(http.StatusOK)
}
