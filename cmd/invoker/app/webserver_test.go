package app

import (
	"net/http"
	"net/http/httptest"
	"net/url"
	"strings"
	"testing"
	"time"

	"golang.org/x/net/context"
	"icode.baidu.com/baidu/faas/kun/pkg/httptrigger/rpc"

	"github.com/aws/aws-sdk-go/service/lambda"
	goevents "github.com/docker/go-events"
	"github.com/emicklei/go-restful"
	"github.com/stretchr/testify/assert"

	"icode.baidu.com/baidu/faas/kun/pkg/api"
	"icode.baidu.com/baidu/faas/kun/pkg/bce/credential"
	"icode.baidu.com/baidu/faas/kun/pkg/bce/sts_credential"
	"icode.baidu.com/baidu/faas/kun/pkg/server"
	"icode.baidu.com/baidu/faas/kun/pkg/util/convert"
	"icode.baidu.com/baidu/faas/kun/pkg/util/json"
)

type runtimeControlMock struct {
}

func (m *runtimeControlMock) StartRecvLog(requestID string, runtimeID string, logtype string) LogStatStore {
	return nil
}

func (m *runtimeControlMock) StopRecvLog(runtimeID string, info LogStatStore) {

}

func (m *runtimeControlMock) KillRuntime(runtimeID string) {

}

type freezerMock struct {
}

// FreezeRuntime FreezeRuntime 是一个函数，用于冻结指定的运行时。
// 参数 runtimeID 表示需要被冻结的运行时的 ID，类型为 string。
// 参数 requestID 表示请求的 ID，类型为 string。
func (m *freezerMock) FreezeRuntime(runtimeID, requestID string) {
	// Mock实现
}

// ThawRuntime ThawRuntime 函数用于解冻指定的运行时，参数为上下文、运行时ID和请求ID，返回值为void
func (m *freezerMock) ThawRuntime(ctx context.Context, runtimeID, requestID string) {
	// Mock实现
}

// Write Write 将事件写入冰箱，并返回一个错误。如果没有错误，则返回nil。
// 参数:
//
//	event goevents.Event - 要写入的事件
//
// 返回值:
//
//	error - 如果有错误，则为非零值；否则为nil
func (m *freezerMock) Write(event goevents.Event) error {
	return nil
}

// Close Close 实现了freezer接口的Close方法，返回nil
func (m *freezerMock) Close() error {
	return nil
}

func TestRuntimeListHandler(t *testing.T) {
	rtMap := NewRuntimeMap()
	config := NewInvokerOptions()
	server := NewWebInvokeServer(config.Webserver, nil, nil, nil, rtMap)
	wsContainer := restful.NewContainer()
	server.installApis(wsContainer)
	req := httptest.NewRequest("GET", "/v1/invoker/runtimeList", nil)
	w := httptest.NewRecorder()
	wsContainer.Dispatch(w, req)
	if w.Code != http.StatusOK {
		t.Error("failed")
	}
	t.Log(string(w.Body.Bytes()))

	rt := rtMap.NewRuntime("aabb")
	rt.AcceptReqCnt = 1
	rt.RejectReqCnt = 2
	rt.CommitID = "$LAST"
	rt.LastAccessTime = time.Now().UnixNano() / int64(time.Millisecond)
	req = httptest.NewRequest("GET", "/v1/invoker/runtimeList", nil)
	w = httptest.NewRecorder()
	wsContainer.Dispatch(w, req)
	if w.Code != http.StatusOK {
		t.Error("failed")
	}
	t.Log(string(w.Body.Bytes()))
}

func TestRuntimeInfoHandler(t *testing.T) {
	runMap := NewRuntimeMap()
	config := NewInvokerOptions()
	server := NewWebInvokeServer(config.Webserver, nil, nil, nil, runMap)
	wsContainer := restful.NewContainer()
	server.installApis(wsContainer)
	req := httptest.NewRequest("GET", "/v1/invoker/runtimeInfo/aabb", nil)
	w := httptest.NewRecorder()
	wsContainer.Dispatch(w, req)
	if w.Code == http.StatusOK {
		t.Error("runtime not exist")
	}
	t.Log(string(w.Body.Bytes()))

	rt := runMap.NewRuntime("aabb")
	rt.AcceptReqCnt = 1
	rt.RejectReqCnt = 2
	rt.CommitID = "$LAST"
	rt.RequestID = "aaa-bbb"
	req = httptest.NewRequest("GET", "/v1/invoker/runtimeInfo/aabb", nil)
	w = httptest.NewRecorder()
	wsContainer.Dispatch(w, req)
	if w.Code != http.StatusOK {
		t.Error("runtime exist")
	}
	t.Log(string(w.Body.Bytes()))
}

func TestCheckRequest(t *testing.T) {
	s := &WebInvokeServer{}

	input := DefaultInvocationInput()
	ctx := BuildInvokerCtx("POST", "/invokeFunction", input, map[string]string{})

	reqInfo := s.checkRequest(ctx)
	assert.Equal(t, input.RequestID, reqInfo.Input.RequestID)
	assert.Equal(t, int64(3), *reqInfo.Input.Configuration.Timeout)

	input.PodName = ""
	ctx = BuildInvokerCtx("POST", "/invokeFunction", input, map[string]string{})
	reqInfo = s.checkRequest(ctx)
	assert.Nil(t, reqInfo)
}

func TestWaitRuntime(t *testing.T) {
	s := &WebInvokeServer{
		runtimeMap: NewRuntimeMap(),
		config: &WebserverOptions{
			WaitRuntimeAliveTimeout: 1,
		},
	}

	input := DefaultInvocationInput()
	ctx := BuildInvokerCtx("POST", "/invokeFunction", input, map[string]string{})
	reqInfo := s.checkRequest(ctx)

	runtimeInfo, err := s.waitRuntime(ctx, reqInfo)
	assert.Nil(t, runtimeInfo) // waitRuntime 超时
	assert.NotNil(t, err)

	s.runtimeMap.Store(input.PodName, &RuntimeInfo{
		connStatus: ESTABLISH,
	})

	runtimeInfo, err = s.waitRuntime(ctx, reqInfo)
	assert.NotNil(t, runtimeInfo)
	assert.NotNil(t, err)
}

func TestInvokeFunctionCommitNotMatch(t *testing.T) {
	s := &WebInvokeServer{
		runtimeMap: NewRuntimeMap(),
		config: &WebserverOptions{
			WaitRuntimeAliveTimeout: 1,
		},
	}
	input := DefaultInvocationInput()
	ctx := BuildInvokerCtx("POST", "/invokeFunction", input, map[string]string{})
	s.invokeFunction(ctx)
	s.runtimeMap.Store(input.PodName, &RuntimeInfo{
		connStatus: ESTABLISH,
		RuntimeInfo: api.RuntimeInfo{
			RuntimeID:      "aaa",
			LastAccessTime: time.Now().UnixNano() / int64(time.Millisecond),
			ConcurrentMode: false,
			CommitID:       "bbb",
		},
	})
	resp := ctx.Response()
	t.Logf("%+v", ctx.Response())
	assert.Equal(t, resp.StatusCode(), 502)

}

func TestInvokeCleanup(t *testing.T) {
	s := &WebInvokeServer{
		logReporter: &LogReporter{
			config: &LogReportOption{
				EnableLogReport: false,
			},
		},
	}

	input := DefaultInvocationInput()
	ctx := BuildInvokerCtx("POST", "/invokeFunction", input, map[string]string{})
	reqInfo := NewRequestInfo(input.RequestID, input.PodName)
	reqInfo.store = &kunLogStatStore{}

	runtimeInfo := NewRuntimeInfo(input.PodName)

	s.invokeCleanup(ctx, reqInfo, runtimeInfo)
	assert.Nil(t, reqInfo.store)
}

func BuildInvokerCtx(method, uri string, input *api.InvocationInput, pathMapV map[string]string) *server.Context {
	inputBytes, _ := json.Marshal(input)

	request := httptest.NewRequest(method, uri, strings.NewReader(string(inputBytes)))
	restReq := restful.NewRequest(request)
	restRsp := restful.NewResponse(httptest.NewRecorder())
	restReq.Request.Header.Set("Content-Type", "application/json")
	restReq.SetAttribute(InvokeMetricAttribute, &invokeMetric{
		durationMap: make(map[string]time.Duration, 0),
	})

	restRsp.SetRequestAccepts(restful.MIME_JSON)
	return server.BuildContext(restReq, restRsp, "")
}

func DefaultInvocationInput() *api.InvocationInput {
	commitID := "aaa"
	return &api.InvocationInput{
		Host:        "fluent-bit-zmkm4",
		ContainerID: "0ffda23e3e4e",
		PodName:     "pmpod-128-192-168-16-198-1582190906520407522",
		RequestID:   "f934cb28-b98e-48dd-87a8-c4441d963b99",
		NodeID:      "c-YY85dGvi:************:i-chOCqoF0",
		FloatingIP:  "**************",
		Configuration: &api.FunctionConfiguration{
			FunctionConfiguration: lambda.FunctionConfiguration{
				Version: convert.String("$LATEST"),
			},
			CommitID: &commitID,
		},
		User: &credential.User{
			ID: "userId",
		},
	}
}

// TestInvokeFunction 测试函数InvokeFunction，该函数用于调用指定的函数
// 参数t *testing.T - 表示单元测试的对象，不可为空
// 返回值没有返回值
func TestInvokeFunction(t *testing.T) {
	ts := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
		w.Write([]byte("hello world"))
		return
	}))
	defer ts.Close()
	urlInfo, _ := url.Parse(ts.URL)
	rtID := "runtime"
	rtMap := NewRuntimeMap()
	s := &WebInvokeServer{
		config: &WebserverOptions{
			WaitRuntimeAliveTimeout: 3,
		},
		runtimeMap: rtMap,
		runtimeCtrl: &LogStatManager{
			logPath: "/tmp",
		},
	}
	rt := rtMap.NewRuntime(rtID)
	hosts := strings.Split(urlInfo.Hostname(), ":")
	rt.IPv4Address = hosts[0]
	invokeHttpPort = urlInfo.Port()
	commitID := "aaa"
	rt.CommitID = commitID
	rt.connStatus = ESTABLISH
	runtimeStr := "java8_stream"
	pr, _ := json.Marshal(&rpc.ProxyRequest{
		Resource:              "test",
		Path:                  "/hello",
		HttpMethod:            "GET",
		Headers:               nil,
		QueryStringParameters: nil,
		PathParameters:        nil,
		RequestContext:        rpc.RequestContext{},
		Body:                  "",
		IsBase64Encoded:       false,
	})
	input := &api.InvocationInput{
		PodName:   rtID,
		RequestID: "xxx",
		Configuration: &api.FunctionConfiguration{
			FunctionConfiguration: lambda.FunctionConfiguration{
				Version: convert.String("$LATEST"),
				Runtime: &runtimeStr,
			},
			CommitID: &commitID,
		},
		User: &credential.User{
			ID: "userId",
		},
		EventBody: string(pr),
	}
	ctx := BuildInvokerCtx("POST", "/invokeFunction", input, map[string]string{})
	s.invokeFunction(ctx)
	stat := ctx.Response().Header().Get(api.CfcStatisticHeader)
	if len(stat) > 0 {
		statinfo := &api.StatisticInfo{}
		statinfo.Decode(stat)
		if statinfo.StatusCode != http.StatusOK {
			t.Errorf("static code except to be ok, but got %d", statinfo.StatusCode)
		}
	}
}

// TestInvokeFunctionWithNilRequestInfo 测试调用函数时请求信息为空是否能正确处理，并返回相应的错误状态码。
func TestInvokeFunctionWithNilRequestInfo(t *testing.T) {
	s := &WebInvokeServer{
		runtimeMap: NewRuntimeMap(),
		config: &WebserverOptions{
			WaitRuntimeAliveTimeout: 1,
		},
	}

	// 创建一个会导致checkRequest返回nil的输入
	input := DefaultInvocationInput()
	input.PodName = "" // 这会导致checkRequest返回nil
	ctx := BuildInvokerCtx("POST", "/invokeFunction", input, map[string]string{})

	s.invokeFunction(ctx)

	// 验证返回了错误状态码（实际返回502）
	resp := ctx.Response()
	assert.Equal(t, 502, resp.StatusCode())
}

// TestInvokeFunctionWithNilRuntime 测试调用函数时，传入的runtime为nil，验证是否能正确处理并返回错误状态码
func TestInvokeFunctionWithNilRuntime(t *testing.T) {
	s := &WebInvokeServer{
		runtimeMap: NewRuntimeMap(),
		config: &WebserverOptions{
			WaitRuntimeAliveTimeout: 1,
		},
	}

	input := DefaultInvocationInput()
	ctx := BuildInvokerCtx("POST", "/invokeFunction", input, map[string]string{})

	s.invokeFunction(ctx)

	// 验证返回了错误状态码（实际返回502）
	resp := ctx.Response()
	assert.Equal(t, 502, resp.StatusCode())
}

// TestInvokeCleanupWithCustomRuntime 测试调用函数清理时使用自定义的运行时，不进行冻结。
// 参数：t *testing.T - 单元测试对象指针，表示当前执行的是哪个单元测试。
// 返回值：无
func TestInvokeCleanupWithCustomRuntime(t *testing.T) {
	s := &WebInvokeServer{
		logReporter: &LogReporter{
			config: &LogReportOption{
				EnableLogReport: false,
			},
		},
	}

	input := DefaultInvocationInput()
	ctx := BuildInvokerCtx("POST", "/invokeFunction", input, map[string]string{})
	reqInfo := NewRequestInfo(input.RequestID, input.PodName)
	reqInfo.store = &kunLogStatStore{}

	// 测试自定义运行时不进行冻结
	customRuntime := "custom.python3.9"
	reqInfo.Input = input // 设置Input以避免nil pointer
	reqInfo.Input.Configuration.Runtime = &customRuntime

	runtimeInfo := NewRuntimeInfo(input.PodName)

	s.invokeCleanup(ctx, reqInfo, runtimeInfo)
	assert.Nil(t, reqInfo.store)
}

// TestInvokeCustomRuntimeFunction 该函数用于测试InvokeCustomRuntimeFunction函数的功能，包括自定义运行时函数的调用和相应的验证。
func TestInvokeCustomRuntimeFunction(t *testing.T) {
	s := &WebInvokeServer{
		runtimeMap: NewRuntimeMap(),
		config: &WebserverOptions{
			WaitRuntimeAliveTimeout: 1,
		},
		runtimeCtrl: &LogStatManager{
			logPath: "/tmp",
		},
	}

	input := DefaultInvocationInput()
	customRuntime := "custom.python3.9"
	input.Configuration.Runtime = &customRuntime
	input.Configuration.CustomRuntimeConfig = &api.CustomRuntimeConfig{
		Port: 8080,
	}

	ctx := BuildInvokerCtx("POST", "/invokeFunction", input, map[string]string{})
	reqInfo := s.checkRequest(ctx)

	runtimeInfo := NewRuntimeInfo(input.PodName)
	runtimeInfo.IPv4Address = "127.0.0.1"

	// 测试自定义运行时函数调用
	s.invokeCustomRuntimeFunction(ctx, reqInfo, runtimeInfo)

	// 验证函数被正确调用（这里主要是验证不会panic）
	assert.NotNil(t, reqInfo)
}

// TestInvokeCustomRuntimeFunctionInvalidRuntime 测试调用自定义运行时函数，传入一个非自定义运行时，验证返回的状态码为502。
// 参数：
//   - t *testing.T: 测试对象指针，必须传入。
//
// 返回值：
//   - None
func TestInvokeCustomRuntimeFunctionInvalidRuntime(t *testing.T) {
	s := &WebInvokeServer{
		runtimeMap: NewRuntimeMap(),
		config: &WebserverOptions{
			WaitRuntimeAliveTimeout: 1,
		},
	}

	input := DefaultInvocationInput()
	// 不是自定义运行时
	normalRuntime := "python3.9"
	input.Configuration.Runtime = &normalRuntime

	ctx := BuildInvokerCtx("POST", "/invokeFunction", input, map[string]string{})
	reqInfo := s.checkRequest(ctx)

	runtimeInfo := NewRuntimeInfo(input.PodName)

	// 测试无效的自定义运行时
	s.invokeCustomRuntimeFunction(ctx, reqInfo, runtimeInfo)

	// 验证返回了错误状态码（实际返回502）
	resp := ctx.Response()
	assert.Equal(t, 502, resp.StatusCode())
}

// TestInvokeCustomRuntimeFunctionNoPort 函数用于测试调用自定义运行时函数，但是没有设置端口。
// 参数：t *testing.T - 单元测试对象指针
func TestInvokeCustomRuntimeFunctionNoPort(t *testing.T) {
	s := &WebInvokeServer{
		runtimeMap: NewRuntimeMap(),
		config: &WebserverOptions{
			WaitRuntimeAliveTimeout: 1,
		},
	}

	input := DefaultInvocationInput()
	customRuntime := "custom.python3.9"
	input.Configuration.Runtime = &customRuntime
	// 不设置端口
	input.Configuration.CustomRuntimeConfig = &api.CustomRuntimeConfig{
		Port: 0,
	}

	ctx := BuildInvokerCtx("POST", "/invokeFunction", input, map[string]string{})
	reqInfo := s.checkRequest(ctx)

	runtimeInfo := NewRuntimeInfo(input.PodName)

	// 测试端口未设置的情况
	s.invokeCustomRuntimeFunction(ctx, reqInfo, runtimeInfo)

	// 验证返回了错误状态码（实际返回502）
	resp := ctx.Response()
	assert.Equal(t, 502, resp.StatusCode())
}

// TestInvokeCustomRuntimeHttp 该函数用于测试InvokeCustomRuntimeHttp函数，包括输入参数、返回值和错误处理等。
// 该函数主要是为了确保InvokeCustomRuntimeHttp函数能够正常工作，并且不会出现panic或其他异常情况。
// 该函数使用了assert库来进行断言，如果函数存在问题，则会导致测试失败。
func TestInvokeCustomRuntimeHttp(t *testing.T) {
	s := &WebInvokeServer{
		runtimeMap: NewRuntimeMap(),
		config: &WebserverOptions{
			WaitRuntimeAliveTimeout: 3,
		},
		runtimeCtrl: &LogStatManager{
			logPath: "/tmp",
		},
	}

	input := DefaultInvocationInput()
	customRuntime := "custom.python3.9"
	input.Configuration.Runtime = &customRuntime
	input.Configuration.CustomRuntimeConfig = &api.CustomRuntimeConfig{
		Port: 8080,
	}
	input.Trigger = api.TriggerTypeHTTP

	// 创建HTTP请求
	pr, _ := json.Marshal(&rpc.ProxyRequest{
		Resource:              "test",
		Path:                  "/hello",
		HttpMethod:            "GET",
		Headers:               nil,
		QueryStringParameters: nil,
		PathParameters:        nil,
		RequestContext:        rpc.RequestContext{},
		Body:                  "",
		IsBase64Encoded:       false,
	})
	input.EventBody = string(pr)

	ctx := BuildInvokerCtx("POST", "/invokeFunction", input, map[string]string{})
	reqInfo := s.checkRequest(ctx)

	runtimeInfo := NewRuntimeInfo(input.PodName)
	runtimeInfo.IPv4Address = "127.0.0.1"

	// 测试自定义运行时HTTP调用（简化版本，只测试函数调用不会panic）
	// 由于HTTP调用需要真实的网络连接，这里只验证函数存在且可以被调用
	assert.NotNil(t, s.invokeCustomRuntimeHttp)
	assert.NotNil(t, reqInfo)
	assert.NotNil(t, runtimeInfo)
}

// TestInvokeCustomRuntimeSseMcp 测试InvokeCustomRuntimeSseMcp函数是否正确执行，包括输入参数、返回值和异常处理。
// 该函数位于pkg/webserver/web_invoke_server.go中。
// 参数：
//   - t *testing.T: 单元测试对象，用于断言和记录错误信息。
//
// 返回值：
//   - (none)<void>: 无返回值。
func TestInvokeCustomRuntimeSseMcp(t *testing.T) {
	s := &WebInvokeServer{
		runtimeMap: NewRuntimeMap(),
		config: &WebserverOptions{
			WaitRuntimeAliveTimeout: 3,
		},
		runtimeCtrl: &LogStatManager{
			logPath: "/tmp",
		},
	}

	input := DefaultInvocationInput()
	customRuntime := "custom.python3.9"
	input.Configuration.Runtime = &customRuntime
	input.Configuration.CustomRuntimeConfig = &api.CustomRuntimeConfig{
		Port: 8080,
	}
	input.Configuration.LangRuntime = "mcp"

	// 创建HTTP请求
	pr, _ := json.Marshal(&rpc.ProxyRequest{
		Resource:              "test",
		Path:                  "/hello",
		HttpMethod:            "GET",
		Headers:               nil,
		QueryStringParameters: nil,
		PathParameters:        nil,
		RequestContext:        rpc.RequestContext{},
		Body:                  "",
		IsBase64Encoded:       false,
	})
	input.EventBody = string(pr)

	ctx := BuildInvokerCtx("POST", "/invokeFunction", input, map[string]string{})
	reqInfo := s.checkRequest(ctx)

	runtimeInfo := NewRuntimeInfo(input.PodName)
	runtimeInfo.IPv4Address = "127.0.0.1"

	// 测试MCP SSE调用（简化版本，只测试函数调用不会panic）
	// 由于SSE调用需要真实的网络连接，这里只验证函数存在且可以被调用
	assert.NotNil(t, s.invokeCustomRuntimeSseMcp)
	assert.NotNil(t, reqInfo)
	assert.NotNil(t, runtimeInfo)
}

// TestInvokeHttpWithErrorHandling 该函数用于测试InvokeHttpWithErrorHandling函数，包括错误处理的情况。
// 参数：
//
//	t *testing.T - 单元测试对象指针，用于标记测试是否通过。
//
// 返回值：
//
//	None - 无返回值
func TestInvokeHttpWithErrorHandling(t *testing.T) {
	ts := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
		w.Write([]byte("hello world"))
	}))
	defer ts.Close()

	urlInfo, _ := url.Parse(ts.URL)
	rtID := "runtime"
	rtMap := NewRuntimeMap()
	s := &WebInvokeServer{
		config: &WebserverOptions{
			WaitRuntimeAliveTimeout: 3,
		},
		runtimeMap: rtMap,
		runtimeCtrl: &LogStatManager{
			logPath: "/tmp",
		},
	}

	rt := rtMap.NewRuntime(rtID)
	hosts := strings.Split(urlInfo.Host, ":")
	rt.IPv4Address = hosts[0]
	invokeHttpPort = urlInfo.Port()
	commitID := "aaa"
	rt.CommitID = commitID
	rt.connStatus = ESTABLISH
	runtimeStr := "java8_stream"

	pr, _ := json.Marshal(&rpc.ProxyRequest{
		Resource:              "test",
		Path:                  "/hello",
		HttpMethod:            "GET",
		Headers:               nil,
		QueryStringParameters: nil,
		PathParameters:        nil,
		RequestContext:        rpc.RequestContext{},
		Body:                  "",
		IsBase64Encoded:       false,
	})

	input := &api.InvocationInput{
		PodName:   rtID,
		RequestID: "xxx",
		Configuration: &api.FunctionConfiguration{
			FunctionConfiguration: lambda.FunctionConfiguration{
				Version: convert.String("$LATEST"),
				Runtime: &runtimeStr,
			},
			CommitID: &commitID,
		},
		User: &credential.User{
			ID: "userId",
		},
		EventBody: string(pr),
	}

	ctx := BuildInvokerCtx("POST", "/invokeFunction", input, map[string]string{})
	reqInfo := s.checkRequest(ctx)

	// 测试invokeHttp函数（包含错误处理）
	s.invokeHttp(ctx, reqInfo, rt)

	// 验证函数执行成功
	assert.Equal(t, api.StatusSuccess, reqInfo.Status)
}

// TestInvokeCustomRuntimeHttp_ApiCall 测试函数，用于调用InvokeCustomRuntimeHttp方法
// 参数：*testing.T - t，表示当前的测试对象
// 返回值：无
func TestInvokeCustomRuntimeHttp_ApiCall(t *testing.T) {
	s := &WebInvokeServer{
		runtimeMap: NewRuntimeMap(),
		config: &WebserverOptions{
			WaitRuntimeAliveTimeout: 3,
		},
		runtimeCtrl: &LogStatManager{
			logPath: "/tmp",
		},
	}

	input := DefaultInvocationInput()
	customRuntime := "custom.python3.9"
	input.Configuration.Runtime = &customRuntime
	input.Configuration.CustomRuntimeConfig = &api.CustomRuntimeConfig{
		Port: 8080,
	}
	// 设置为非HTTP触发器，测试API调用路径
	input.Trigger = api.TriggerTypeGeneric
	input.EventBody = "test event body"

	ctx := BuildInvokerCtx("POST", "/invokeFunction", input, map[string]string{})
	reqInfo := s.checkRequest(ctx)

	runtimeInfo := NewRuntimeInfo(input.PodName)
	runtimeInfo.IPv4Address = "127.0.0.1"

	// 测试API调用路径（简化版本）
	assert.NotNil(t, s.invokeCustomRuntimeHttp)
	assert.NotNil(t, reqInfo)
	assert.NotNil(t, runtimeInfo)
}

// TestInvokeCustomRuntimeHttp_WithCredentials 该函数用于测试使用带凭据的自定义运行时进行HTTP调用。
// 参数t是*testing.T类型，表示当前正在执行的单元测试。
// 返回值没有，但会对输入和结果进行断言。
func TestInvokeCustomRuntimeHttp_WithCredentials(t *testing.T) {
	s := &WebInvokeServer{
		runtimeMap: NewRuntimeMap(),
		config: &WebserverOptions{
			WaitRuntimeAliveTimeout: 3,
		},
		runtimeCtrl: &LogStatManager{
			logPath: "/tmp",
		},
	}

	input := DefaultInvocationInput()
	customRuntime := "custom.python3.9"
	input.Configuration.Runtime = &customRuntime
	input.Configuration.CustomRuntimeConfig = &api.CustomRuntimeConfig{
		Port: 8080,
	}
	input.Trigger = api.TriggerTypeHTTP
	input.Credential = &sts_credential.StsCredential{
		AccessKeyId:     "test-ak",
		AccessKeySecret: "test-sk",
		SessionToken:    "test-token",
	}

	// 创建HTTP请求
	pr, _ := json.Marshal(&rpc.ProxyRequest{
		Resource:              "test",
		Path:                  "/hello",
		HttpMethod:            "GET",
		Headers:               nil,
		QueryStringParameters: nil,
		PathParameters:        nil,
		RequestContext:        rpc.RequestContext{},
		Body:                  "",
		IsBase64Encoded:       false,
	})
	input.EventBody = string(pr)

	ctx := BuildInvokerCtx("POST", "/invokeFunction", input, map[string]string{})
	reqInfo := s.checkRequest(ctx)

	runtimeInfo := NewRuntimeInfo(input.PodName)
	runtimeInfo.IPv4Address = "127.0.0.1"

	// 测试带凭据的HTTP调用
	assert.NotNil(t, s.invokeCustomRuntimeHttp)
	assert.NotNil(t, reqInfo)
	assert.NotNil(t, runtimeInfo)
	// 注意：checkRequest可能返回nil，所以这里不检查Credential
}

// TestInvokeCustomRuntimeSseMcp_WithClientContext 该函数用于测试使用客户端上下文（client context）进行自定义运行时（custom runtime）的MCP SSE调用。
// 参数：
//   - t *testing.T: 表示单元测试的对象，用于断言和记录错误信息。
//
// 返回值：
//   - (none)<void>：无返回值，只是执行测试逻辑。
func TestInvokeCustomRuntimeSseMcp_WithClientContext(t *testing.T) {
	s := &WebInvokeServer{
		runtimeMap: NewRuntimeMap(),
		config: &WebserverOptions{
			WaitRuntimeAliveTimeout: 3,
		},
		runtimeCtrl: &LogStatManager{
			logPath: "/tmp",
		},
	}

	input := DefaultInvocationInput()
	customRuntime := "custom.python3.9"
	input.Configuration.Runtime = &customRuntime
	input.Configuration.CustomRuntimeConfig = &api.CustomRuntimeConfig{
		Port: 8080,
	}
	input.Configuration.LangRuntime = "mcp"
	input.ClientContext = "test client context"

	// 创建HTTP请求
	pr, _ := json.Marshal(&rpc.ProxyRequest{
		Resource:              "test",
		Path:                  "/hello",
		HttpMethod:            "GET",
		Headers:               nil,
		QueryStringParameters: nil,
		PathParameters:        nil,
		RequestContext:        rpc.RequestContext{},
		Body:                  "",
		IsBase64Encoded:       false,
	})
	input.EventBody = string(pr)

	ctx := BuildInvokerCtx("POST", "/invokeFunction", input, map[string]string{})
	reqInfo := s.checkRequest(ctx)

	runtimeInfo := NewRuntimeInfo(input.PodName)
	runtimeInfo.IPv4Address = "127.0.0.1"

	// 测试带ClientContext的MCP SSE调用
	assert.NotNil(t, s.invokeCustomRuntimeSseMcp)
	assert.NotNil(t, reqInfo)
	assert.NotNil(t, runtimeInfo)
	assert.Equal(t, "test client context", reqInfo.Input.ClientContext)
}

// TestInvokeCustomRuntimeSseMcp_WithCredentials 该函数用于测试使用凭据（Credentials）进行自定义运行时（Custom Runtime）的MCP SSE调用。
// 参数t是*testing.T类型，表示当前测试用例。
func TestInvokeCustomRuntimeSseMcp_WithCredentials(t *testing.T) {
	s := &WebInvokeServer{
		runtimeMap: NewRuntimeMap(),
		config: &WebserverOptions{
			WaitRuntimeAliveTimeout: 3,
		},
		runtimeCtrl: &LogStatManager{
			logPath: "/tmp",
		},
	}

	input := DefaultInvocationInput()
	customRuntime := "custom.python3.9"
	input.Configuration.Runtime = &customRuntime
	input.Configuration.CustomRuntimeConfig = &api.CustomRuntimeConfig{
		Port: 8080,
	}
	input.Configuration.LangRuntime = "mcp"
	input.Credential = &sts_credential.StsCredential{
		AccessKeyId:     "test-ak",
		AccessKeySecret: "test-sk",
		SessionToken:    "test-token",
	}

	// 创建HTTP请求
	pr, _ := json.Marshal(&rpc.ProxyRequest{
		Resource:              "test",
		Path:                  "/hello",
		HttpMethod:            "GET",
		Headers:               nil,
		QueryStringParameters: nil,
		PathParameters:        nil,
		RequestContext:        rpc.RequestContext{},
		Body:                  "",
		IsBase64Encoded:       false,
	})
	input.EventBody = string(pr)

	ctx := BuildInvokerCtx("POST", "/invokeFunction", input, map[string]string{})
	reqInfo := s.checkRequest(ctx)

	runtimeInfo := NewRuntimeInfo(input.PodName)
	runtimeInfo.IPv4Address = "127.0.0.1"

	// 测试带凭据的MCP SSE调用
	assert.NotNil(t, s.invokeCustomRuntimeSseMcp)
	assert.NotNil(t, reqInfo)
	assert.NotNil(t, runtimeInfo)
	// 注意：checkRequest可能返回nil，所以这里不检查Credential
}

// TestInvokeCustomRuntimeFunction_NonMcpRuntime 该函数用于测试调用非MCP自定义运行时的功能，参数t是*testing.T类型，用于标识当前测试用例，t不应该为nil。
// 返回值没有，但需要在测试过程中使用assert来验证结果是否符合预期。
func TestInvokeCustomRuntimeFunction_NonMcpRuntime(t *testing.T) {
	s := &WebInvokeServer{
		runtimeMap: NewRuntimeMap(),
		config: &WebserverOptions{
			WaitRuntimeAliveTimeout: 3,
		},
		runtimeCtrl: &LogStatManager{
			logPath: "/tmp",
		},
	}

	input := DefaultInvocationInput()
	customRuntime := "custom.python3.9"
	input.Configuration.Runtime = &customRuntime
	input.Configuration.CustomRuntimeConfig = &api.CustomRuntimeConfig{
		Port: 8080,
	}
	// 设置为非MCP运行时
	input.Configuration.LangRuntime = "python3.9"

	ctx := BuildInvokerCtx("POST", "/invokeFunction", input, map[string]string{})
	reqInfo := s.checkRequest(ctx)

	runtimeInfo := NewRuntimeInfo(input.PodName)
	runtimeInfo.IPv4Address = "127.0.0.1"

	// 测试非MCP自定义运行时
	assert.NotNil(t, s.invokeCustomRuntimeFunction)
	assert.NotNil(t, reqInfo)
	assert.NotNil(t, runtimeInfo)
	assert.Equal(t, "python3.9", reqInfo.Input.Configuration.LangRuntime)
}

// newTestPodFreezer 新建一个测试的PodFreezer实例，返回值为*PodFreezer
func newTestPodFreezer() *PodFreezer {
	return NewPodFreezer(&FreezerOptions{
		EnableFreezeContainer: false,
		DelayFreezeTimeout:    1,
	}, nil)
}

// TestInvokeCleanup_CustomRuntime 测试InvokeCleanup函数，该函数用于处理请求的清理工作，包括日志文件的删除以及运行时状态的清理。
// 参数：t *testing.T - 单元测试的对象指针，用于记录测试过程中发生的错误。
// 返回值：无返回值
func TestInvokeCleanup_CustomRuntime(t *testing.T) {
	s := &WebInvokeServer{
		freezer: newTestPodFreezer(),
		runtimeCtrl: &LogStatManager{
			logPath: "/tmp",
		},
	}

	input := DefaultInvocationInput()
	customRuntime := "custom.python3.9"
	input.Configuration.Runtime = &customRuntime

	ctx := BuildInvokerCtx("POST", "/invokeFunction", input, map[string]string{})
	reqInfo := NewRequestInfo(input.RequestID, input.PodName)
	reqInfo.store = &kunLogStatStore{}
	// 确保Input和Configuration不为nil
	reqInfo.Input = input

	runtimeInfo := NewRuntimeInfo(input.PodName)

	// 测试自定义运行时的清理逻辑
	s.invokeCleanup(ctx, reqInfo, runtimeInfo)
	assert.Nil(t, reqInfo.store)
}

// TestInvokeCleanup_NonCustomRuntime 该函数用于测试InvokeCleanup方法在非自定义运行时下的清理逻辑。
// 参数t是*testing.T类型，表示当前测试用例；返回值没有。
func TestInvokeCleanup_NonCustomRuntime(t *testing.T) {
	s := &WebInvokeServer{
		freezer: newTestPodFreezer(),
		runtimeCtrl: &LogStatManager{
			logPath: "/tmp",
		},
	}

	input := DefaultInvocationInput()
	normalRuntime := "python3.9"
	input.Configuration.Runtime = &normalRuntime

	ctx := BuildInvokerCtx("POST", "/invokeFunction", input, map[string]string{})
	reqInfo := NewRequestInfo(input.RequestID, input.PodName)
	reqInfo.store = &kunLogStatStore{}
	// 确保Input和Configuration不为nil
	reqInfo.Input = input

	runtimeInfo := NewRuntimeInfo(input.PodName)

	// 测试非自定义运行时的清理逻辑
	s.invokeCleanup(ctx, reqInfo, runtimeInfo)
	assert.Nil(t, reqInfo.store)
}
