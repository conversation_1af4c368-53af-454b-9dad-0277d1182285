package app

import (
	"strings"
	"sync"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"

	"icode.baidu.com/baidu/faas/kun/pkg/api"
)

func TestStatisticInfo(t *testing.T) {
	a := &api.StatisticInfo{
		UserID:     "aaa",
		Region:     "bj",
		Function:   "xxxxxdf",
		Version:    "v1",
		StartTime:  1,
		Duration:   2,
		MemoryUsed: 3,
		StatusCode: 4,
	}
	str1 := a.Encode()
	b := &api.StatisticInfo{}
	err := b.Decode(str1)
	if err != nil {
		t.Error(err)
		return
	}
	str2 := b.Encode()
	if strings.Compare(str1, str2) != 0 {
		t.Fail()
	}
}

func TestNetTrafficInfo(t *testing.T) {
	a := &api.NetTrafficInfo{
		UserID:   "xxxxx",
		InBytes:  234,
		OutBytes: 567,
	}
	str1 := a.Encode()
	b := &api.NetTrafficInfo{}
	err := b.Decode(str1)
	if err != nil {
		t.Error(err)
		return
	}
	str2 := b.Encode()
	if strings.Compare(str1, str2) != 0 {
		t.Fail()
	}
}

func TestInvokeBillInfo(t *testing.T) {
	a := &api.InvokeBillInfo{
		UserID:   "xxxx",
		Count:    20,
		MemUsage: 2443,
	}
	str1 := a.Encode()
	b := &api.InvokeBillInfo{}
	err := b.Decode(str1)
	if err != nil {
		t.Error(err)
		return
	}
	str2 := b.Encode()
	if strings.Compare(str1, str2) != 0 {
		t.Fail()
	}
}

func TestTrafficMap(t *testing.T) {
	tc := &trafficCounter{
		trafficMap: &sync.Map{},
	}
	now := time.Now()

	tc.storeStats(1, 1, "user-id", "runtime-id", now)
	_, ok := tc.trafficMap.Load("user-id")
	assert.Equal(t, true, ok)

	tc.storeStats(1, 1, "user-id", "runtime-id", now)
	val, _ := tc.trafficMap.Load("user-id")
	info, _ := val.(*trafficInfo)
	assert.Equal(t, int64(2), info.deltaIn)

	tc.clearMap(now)
	_, ok = tc.trafficMap.Load("user-id")
	assert.Equal(t, true, ok)
}
