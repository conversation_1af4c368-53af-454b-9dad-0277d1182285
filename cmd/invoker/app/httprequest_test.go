package app

import (
	"bytes"
	"encoding/base64"
	"io"
	"net/http"
	"strings"
	"testing"

	"icode.baidu.com/baidu/faas/kun/pkg/httptrigger/rpc"
)

// 用于模拟body读取出错的reader
type errReader struct{}

// Read Read implements the io.Reader interface. It always returns an EOF error.
// func (e *errReader) Read(p []byte) (int, error) { return 0, io.ErrUnexpectedEOF }
func (e *errReader) Read(p []byte) (int, error) { return 0, io.ErrUnexpectedEOF }

// Close Close 实现了io.Closer接口，返回nil表示关闭成功。
// 该函数不会影响已经读取的内容，只是标记当前reader为关闭状态。
func (e *errReader) Close() error { return nil }

// TestConvertProxyRequestToHttp 测试函数ConvertProxyRequestToHttp将rpc.ProxyRequest转换为*http.Request。
// 该函数包含多个测试用例，每个测试用例包含一个名称、一个rpc.ProxyRequest结构体、一个bool类型（表示是否为SSE请求）和一个期望值*http.Request。
// 测试函数首先创建一个http.Request结构体，然后根据rpc.ProxyRequest中的字段设置其属性，最后比较结果与期望值是否相等。
// 如果结果不相等，则输出错误信息并终止测试。
func TestConvertProxyRequestToHttp(t *testing.T) {
	tests := []struct {
		name     string
		req      *rpc.ProxyRequest
		sse      bool
		expected *http.Request
	}{
		{
			name: "normal request without base64",
			req: &rpc.ProxyRequest{
				HttpMethod: "POST",
				Path:       "/test",
				Body:       "test body",
				Headers: map[string]string{
					"Content-Type": "application/json",
					"User-Agent":   "test-agent",
				},
				QueryStringParameters: map[string]string{
					"param1": "value1",
					"param2": "value2",
				},
				PathParameters: map[string]string{
					"id": "123",
				},
				RequestContext: rpc.RequestContext{
					RequestId: "req-123",
					SourceIp:  "127.0.0.1",
					ApiID:     "api-456",
				},
				IsBase64Encoded: false,
			},
			sse: false,
		},
		{
			name: "base64 encoded request",
			req: &rpc.ProxyRequest{
				HttpMethod: "GET",
				Path:       "/api/data",
				Body:       base64.StdEncoding.EncodeToString([]byte("base64 body")),
				Headers: map[string]string{
					"Authorization": "Bearer token",
				},
				IsBase64Encoded: true,
				RequestContext: rpc.RequestContext{
					RequestId: "req-456",
					SourceIp:  "***********",
					ApiID:     "api-789",
				},
			},
			sse: false,
		},
		{
			name: "path with leading slash",
			req: &rpc.ProxyRequest{
				HttpMethod:      "PUT",
				Path:            "/api/users/123",
				Body:            "update data",
				IsBase64Encoded: false,
				RequestContext: rpc.RequestContext{
					RequestId: "req-789",
					SourceIp:  "********",
					ApiID:     "api-101",
				},
			},
			sse: false,
		},
		{
			name: "SSE request",
			req: &rpc.ProxyRequest{
				HttpMethod: "POST",
				Path:       "/events",
				Body:       "event data",
				Headers: map[string]string{
					"Accept": "text/event-stream",
				},
				IsBase64Encoded: false,
				RequestContext: rpc.RequestContext{
					RequestId: "req-sse",
					SourceIp:  "**********",
					ApiID:     "api-sse",
				},
			},
			sse: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := ConvertProxyRequestToHttp(tt.req, tt.sse)

			if result == nil {
				t.Fatal("ConvertProxyRequestToHttp returned nil")
			}

			// 验证HTTP方法
			if result.Method != tt.req.HttpMethod {
				t.Errorf("Expected method %s, got %s", tt.req.HttpMethod, result.Method)
			}

			// 验证URL路径
			expectedPath := tt.req.Path
			if len(expectedPath) > 0 && expectedPath[0] == '/' {
				expectedPath = expectedPath[1:]
			}
			if !strings.Contains(result.URL.Path, expectedPath) {
				t.Errorf("Expected path to contain %s, got %s", expectedPath, result.URL.Path)
			}

			// 验证查询参数
			for key, value := range tt.req.QueryStringParameters {
				if result.URL.Query().Get(key) != value {
					t.Errorf("Expected query parameter %s=%s, got %s", key, value, result.URL.Query().Get(key))
				}
			}

			// 验证请求头
			for key, value := range tt.req.Headers {
				if result.Header.Get(key) != value {
					t.Errorf("Expected header %s=%s, got %s", key, value, result.Header.Get(key))
				}
			}

			// 验证PathParameters header
			if len(tt.req.PathParameters) > 0 {
				pathParamsHeader := result.Header.Get("X-Cfc-PathParameters")
				if pathParamsHeader == "" {
					t.Error("Expected X-Cfc-PathParameters header to be set")
				}
			}

			// 验证RequestContext headers
			if tt.req.RequestContext.RequestId != "" {
				if result.Header.Get("X-Cfc-RequestId") != tt.req.RequestContext.RequestId {
					t.Errorf("Expected X-Cfc-RequestId %s, got %s", tt.req.RequestContext.RequestId, result.Header.Get("X-Cfc-RequestId"))
				}
				if result.Header.Get("X-Cfc-ClientIP") != tt.req.RequestContext.SourceIp {
					t.Errorf("Expected X-Cfc-ClientIP %s, got %s", tt.req.RequestContext.SourceIp, result.Header.Get("X-Cfc-ClientIP"))
				}
				if result.Header.Get("X-Cfc-ApiID") != tt.req.RequestContext.ApiID {
					t.Errorf("Expected X-Cfc-ApiID %s, got %s", tt.req.RequestContext.ApiID, result.Header.Get("X-Cfc-ApiID"))
				}
			}

			// 验证SSE相关header
			if tt.sse {
				if result.Header.Get("Content-Type") != "application/json" {
					t.Errorf("Expected Content-Type application/json for SSE, got %s", result.Header.Get("Content-Type"))
				}
				if result.Header.Get("Content-Length") == "" {
					t.Error("Expected Content-Length header for SSE")
				}
			}

			// 验证请求体
			if tt.req.IsBase64Encoded {
				// 对于base64编码的请求，验证解码后的内容
				bodyBytes, _ := io.ReadAll(result.Body)
				decoded, _ := base64.StdEncoding.DecodeString(tt.req.Body)
				if string(bodyBytes) != string(decoded) {
					t.Errorf("Expected decoded body %s, got %s", string(decoded), string(bodyBytes))
				}
			} else {
				// 对于普通请求，验证原始内容
				bodyBytes, _ := io.ReadAll(result.Body)
				if string(bodyBytes) != tt.req.Body {
					t.Errorf("Expected body %s, got %s", tt.req.Body, string(bodyBytes))
				}
			}
		})
	}
}

// TestConvertHttpResponseToProxy 测试函数，将HTTP响应转换为代理响应。
// 参数：
//   - t *testing.T: 指向testing包中的*testing.T类型，用于记录测试结果。
//
// 返回值：
//   - (none)
func TestConvertHttpResponseToProxy(t *testing.T) {
	tests := []struct {
		name     string
		response *http.Response
		expected *rpc.ProxyResponse
	}{
		{
			name: "JSON response",
			response: &http.Response{
				StatusCode: 200,
				Header: http.Header{
					"Content-Type": []string{"application/json"},
					"Server":       []string{"test-server"},
				},
				Body: io.NopCloser(strings.NewReader(`{"message": "success"}`)),
			},
			expected: &rpc.ProxyResponse{
				StatusCode: 200,
				Headers: map[string]string{
					"Content-Type": "application/json",
					"Server":       "test-server",
				},
				Body:            `{"message": "success"}`,
				IsBase64Encoded: false,
			},
		},
		{
			name: "binary response",
			response: &http.Response{
				StatusCode: 200,
				Header: http.Header{
					"Content-Type": []string{"application/octet-stream"},
				},
				Body: io.NopCloser(bytes.NewReader([]byte{0x01, 0x02, 0x03, 0x04})),
			},
			expected: &rpc.ProxyResponse{
				StatusCode: 200,
				Headers: map[string]string{
					"Content-Type": "application/octet-stream",
				},
				Body:            "AQIDBA==", // base64 encoded
				IsBase64Encoded: true,
			},
		},
		{
			name: "text response",
			response: &http.Response{
				StatusCode: 200,
				Header: http.Header{
					"Content-Type": []string{"text/plain"},
				},
				Body: io.NopCloser(strings.NewReader("Hello, World!")),
			},
			expected: &rpc.ProxyResponse{
				StatusCode: 200,
				Headers: map[string]string{
					"Content-Type": "text/plain",
				},
				Body:            "SGVsbG8sIFdvcmxkIQ==", // base64 encoded
				IsBase64Encoded: true,
			},
		},
		{
			name: "no content type",
			response: &http.Response{
				StatusCode: 200,
				Header:     http.Header{},
				Body:       io.NopCloser(strings.NewReader("raw data")),
			},
			expected: &rpc.ProxyResponse{
				StatusCode:      200,
				Headers:         map[string]string{},
				Body:            "cmF3IGRhdGE=", // base64 encoded
				IsBase64Encoded: true,
			},
		},
		{
			name: "error response",
			response: &http.Response{
				StatusCode: 404,
				Header: http.Header{
					"Content-Type": []string{"text/html"},
				},
				Body: io.NopCloser(strings.NewReader("<h1>Not Found</h1>")),
			},
			expected: &rpc.ProxyResponse{
				StatusCode: 404,
				Headers: map[string]string{
					"Content-Type": "text/html",
				},
				Body:            "<h1>Not Found</h1>",
				IsBase64Encoded: false,
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := ConvertHttpResponseToProxy(tt.response)

			if err != nil {
				t.Fatalf("ConvertHttpResponseToProxy returned error: %v", err)
			}

			if result == nil {
				t.Fatal("ConvertHttpResponseToProxy returned nil")
			}

			// 验证状态码
			if result.StatusCode != tt.expected.StatusCode {
				t.Errorf("Expected status code %d, got %d", tt.expected.StatusCode, result.StatusCode)
			}

			// 验证响应头
			for key, value := range tt.expected.Headers {
				if result.Headers[key] != value {
					t.Errorf("Expected header %s=%s, got %s", key, value, result.Headers[key])
				}
			}

			// 验证是否base64编码
			if result.IsBase64Encoded != tt.expected.IsBase64Encoded {
				t.Errorf("Expected IsBase64Encoded %v, got %v", tt.expected.IsBase64Encoded, result.IsBase64Encoded)
			}

			// 验证响应体
			if result.IsBase64Encoded {
				// 对于base64编码的响应，验证解码后的内容
				decoded, err := base64.StdEncoding.DecodeString(result.Body)
				if err != nil {
					t.Errorf("Failed to decode base64 body: %v", err)
				}
				expectedDecoded, _ := base64.StdEncoding.DecodeString(tt.expected.Body)
				if string(decoded) != string(expectedDecoded) {
					t.Errorf("Expected decoded body %s, got %s", string(expectedDecoded), string(decoded))
				}
			} else {
				// 对于普通响应，验证原始内容
				if result.Body != tt.expected.Body {
					t.Errorf("Expected body %s, got %s", tt.expected.Body, result.Body)
				}
			}
		})
	}
}

// TestConvertProxyRequestToHttp_EdgeCases 测试函数，用于转换 ProxyRequest 到 http.Request，处理边界情况。
// 参数：
//   - t *testing.T：Go 单元测试框架提供的 testing.T 类型的指针，表示当前正在运行的测试。
//
// 返回值：
//   - 无返回值（void）
func TestConvertProxyRequestToHttp_EdgeCases(t *testing.T) {
	tests := []struct {
		name string
		req  *rpc.ProxyRequest
		sse  bool
	}{
		{
			name: "empty request",
			req: &rpc.ProxyRequest{
				HttpMethod:      "",
				Path:            "",
				Body:            "",
				Headers:         map[string]string{},
				IsBase64Encoded: false,
			},
			sse: false,
		},
		{
			name: "nil headers",
			req: &rpc.ProxyRequest{
				HttpMethod:      "GET",
				Path:            "/test",
				Body:            "test",
				Headers:         nil,
				IsBase64Encoded: false,
			},
			sse: false,
		},
		{
			name: "nil query parameters",
			req: &rpc.ProxyRequest{
				HttpMethod:            "GET",
				Path:                  "/test",
				Body:                  "test",
				QueryStringParameters: nil,
				IsBase64Encoded:       false,
			},
			sse: false,
		},
		{
			name: "nil path parameters",
			req: &rpc.ProxyRequest{
				HttpMethod:      "GET",
				Path:            "/test",
				Body:            "test",
				PathParameters:  nil,
				IsBase64Encoded: false,
			},
			sse: false,
		},
		{
			name: "empty request context",
			req: &rpc.ProxyRequest{
				HttpMethod:      "GET",
				Path:            "/test",
				Body:            "test",
				RequestContext:  rpc.RequestContext{},
				IsBase64Encoded: false,
			},
			sse: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := ConvertProxyRequestToHttp(tt.req, tt.sse)

			if result == nil {
				t.Fatal("ConvertProxyRequestToHttp returned nil for edge case")
			}

			// 验证基本结构存在
			if result.URL == nil {
				t.Error("Expected URL to be set")
			}

			if result.Header == nil {
				t.Error("Expected Header to be set")
			}
		})
	}
}

// TestConvertHttpResponseToProxy_ExtraErrorCases 测试函数，用于转换HTTP响应为代理响应，并验证一些额外的错误情况。
// 参数：t *testing.T - 类型为*testing.T的指针，表示当前正在执行的单元测试。
func TestConvertHttpResponseToProxy_ExtraErrorCases(t *testing.T) {
	// nil response
	resp, err := ConvertHttpResponseToProxy(nil)
	if err == nil {
		t.Error("expected error for nil response")
	}
	if resp != nil {
		t.Error("expected nil resp for nil response")
	}

	// response.Body == nil
	r := &http.Response{
		StatusCode: 200,
		Header:     http.Header{},
		Body:       nil,
	}
	resp, err = ConvertHttpResponseToProxy(r)
	if err != nil {
		t.Errorf("unexpected error for nil body: %v", err)
	}
	if resp == nil || resp.Body != "" {
		t.Error("expected empty body for nil Body")
	}

	// body读取出错
	r = &http.Response{
		StatusCode: 200,
		Header:     http.Header{},
		Body:       &errReader{},
	}
	resp, err = ConvertHttpResponseToProxy(r)
	if err == nil {
		t.Error("expected error for body read error")
	}
}
