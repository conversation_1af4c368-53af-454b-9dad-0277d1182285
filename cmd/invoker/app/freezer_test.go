package app

import (
	"context"
	"net/http"
	"testing"
	"time"

	"icode.baidu.com/baidu/faas/kun/pkg/api"
	"icode.baidu.com/baidu/faas/kun/pkg/funclet/client"
)

type funcletMock struct {
	StateMap map[string]api.FreezerState
	client.FuncletInterface
}

func (m *funcletMock) Recover(input *api.FuncletClientRecoverInput) error {
	return nil
}
func (m *funcletMock) WarmUp(input *api.FuncletClientWarmUpInput) (string, error) {
	return "", nil
}
func (m *funcletMock) WarmUpOne(ctx context.Context, input *api.FuncletClientWarmUpOneInput) (*api.InitOneColdContainerResponse, error) {
	return nil, nil
}
func (m *funcletMock) CoolDown(input *api.FuncletClientCoolDownInput) error {
	return nil
}
func (m *funcletMock) ResetNode(input *api.FuncletClientResetNodeInput) error {
	return nil
}
func (m *funcletMock) ListPods(input *api.FuncletClientListPodsInput) (*api.ListPodsResponse, error) {
	return nil, nil
}
func (m *funcletMock) ListZombiePods(input *api.FuncletClientListZombiePodsInput) (*api.ListPodsResponse, error) {
	return nil, nil
}
func (m *funcletMock) Describe(input *api.FuncletClientDescribeInput) (*api.NodeDescription, error) {
	return nil, nil
}
func (m *funcletMock) UpdateResource(input *api.FuncletClientUpdateResourceInput) (*api.ListPodsResponse, error) {
	return nil, nil
}
func (m *funcletMock) SetHTTPClient(client *http.Client) client.FuncletInterface {
	return nil
}
func (m *funcletMock) AddPod(input *api.FuncletClientAddPodInput) error {
	return nil
}
func (m *funcletMock) DeletePod(input *api.FuncletClientDeletePodInput) error {
	return nil
}
func (m *funcletMock) FreezeContainer(input *api.FuncletClientFreezeContainerInput) error {
	m.StateMap[input.PodName] = input.State
	return nil
}
func (m *funcletMock) NetTraffic(input *api.FuncletClientNetTrafficInput) (*api.TrafficStats, error) {
	return nil, nil
}
func (m *funcletMock) ReportContainerState(input *api.FuncletClientReportContainerStateInput) error {
	return nil
}
func (m *funcletMock) GetSickPodList(input *api.FuncletClientGetSickPodListInput) (*api.SickPodListResponse, error) {
	return nil, nil
}
func (m *funcletMock) PutSickPod(input *api.FuncletClientPutSickPodInput, info *api.SickPodRequest) error {
	return nil
}
func (m *funcletMock) DeleteSickPod(input *api.FuncletClientDeleteSickPodInput) error {
	return nil
}
func TestDoFreezeThaw(t *testing.T) {
	o := &FreezerOptions{
		DelayFreezeTimeout:    1,
		EnableFreezeContainer: false,
	}
	m := &funcletMock{
		StateMap: make(map[string]api.FreezerState),
	}
	f := NewPodFreezer(o, m)
	info := &freezeInfo{
		RuntimeID: "aaaa",
		RequestID: "1",
	}
	f.doFreeze(info)
	if m.StateMap["aaaa"] != api.Frozen {
		t.Error(m.StateMap["aaaa"])
		return
	}

	f.doThaw(info)
	if m.StateMap["aaaa"] != api.Thawed {
		t.Error(m.StateMap["aaaa"])
		return
	}
	f.Close()
}

func TestFreezeThawn(t *testing.T) {
	m := &funcletMock{
		StateMap: make(map[string]api.FreezerState),
	}
	o := &FreezerOptions{
		DelayFreezeTimeout:    3,
		EnableFreezeContainer: true,
	}
	// now := time.Now()
	f := NewPodFreezer(o, m)
	f.FreezeRuntime("aaaa", "1111")
	<-time.After(1 * time.Second) // 等1s
	if _, ok := f.waitMap["aaaa"]; !ok {
		t.Error("aaaa not in waitmap")
		return
	}
	<-time.After(3 * time.Second)
	if _, ok := f.waitMap["aaaa"]; ok {
		t.Error("aaaa still in waitmap")
		return
	}
	if m.StateMap["aaaa"] != api.Frozen {
		t.Error(m.StateMap["aaaa"])
		return
	}
	f.FreezeRuntime("aaaa", "1111")
	f.ThawRuntime(context.TODO(), "aaaa", "2222")

	<-time.After(1 * time.Second)
	if _, ok := f.waitMap["aaaa"]; ok {
		t.Error("aaaa still in waitmap")
		return
	}
	if m.StateMap["aaaa"] != api.Thawed {
		t.Error(m.StateMap["aaaa"])
		return
	}
	f.Close()
}

func (m *funcletMock) AccessByPodIP() bool {
	return false
}
