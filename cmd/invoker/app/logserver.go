package app

import (
	"bytes"
	"fmt"
	"io"
	"net"
	"os"
	"sync"
	"syscall"

	"golang.org/x/sys/unix"

	invokerutils "icode.baidu.com/baidu/faas/kun/pkg/invoker/utils"
	"icode.baidu.com/baidu/faas/kun/pkg/util/json"
)

type LogStatManager struct {
	logPath string
	servers []LogStatServer
}

func NewLogStatManager(config *LogServerOptions, servers ...LogStatServer) *LogStatManager {
	return &LogStatManager{
		logPath: config.UserLogFilePath,
		servers: servers,
	}
}

func (m *LogStatManager) StartRecvLog(runtimeid, requestid, logtype string) LogStatStore {
	store := newLogStatStore(requestid, runtimeid, m.logPath, logtype)
	for _, s := range m.servers {
		s.StartRecvLog(runtimeid, requestid, store)
	}
	return store
}

func (m *LogStatManager) StopRecvLog(runtimeid string, requestid string, store LogStatStore) {
	for _, s := range m.servers {
		s.StopRecvLog(runtimeid, requestid, store)
	}
}

func (m *LogStatManager) KillRuntime(runtimeid string) {
	for _, s := range m.servers {
		s.KillRuntime(runtimeid)
	}
}

type LogStatServer interface {
	StartRecvLog(runtimeID string, requestID string, store LogStatStore)
	StopRecvLog(runtimeID string, requestID string, store LogStatStore)
	KillRuntime(runtimeID string)
	ListenAndServe()
}

type kunStoreMap struct {
	sync.Map // string => LogStatStore
}

func (m *kunStoreMap) get(runtimeid string) LogStatStore {
	v, ok := m.Load(runtimeid)
	if ok {
		return v.(LogStatStore)
	}
	return nil
}

func (m *kunStoreMap) set(runtimeid string, store LogStatStore) {
	m.Store(runtimeid, store)
}

func (m *kunStoreMap) del(runtimeid string, store LogStatStore) {
	v, ok := m.Load(runtimeid)
	if ok {
		old := v.(LogStatStore)
		if old == store {
			m.Delete(runtimeid)
		}
	}
}

type statsConn struct {
	name    string
	conn    io.ReadWriteCloser
	memused int64
}

type kunStatsMap struct {
	sync.Map
}

func (m *kunStatsMap) get(runtimeid string) *statsConn {
	v, ok := m.Load(runtimeid)
	if ok {
		return v.(*statsConn)
	}
	return nil
}

func (m *kunStatsMap) set(runtimeid string, sc *statsConn) {
	m.Store(runtimeid, sc)
}

func (m *kunStatsMap) del(runtimeid string, sc *statsConn) {
	v, ok := m.Load(runtimeid)
	if ok {
		old := v.(*statsConn)
		if old == sc {
			m.Delete(runtimeid)
		}
	}
}

type kunLogStatServer struct {
	config   *LogServerOptions
	storeMap kunStoreMap
	statsMap kunStatsMap
}

func NewLogStatServer(c *LogServerOptions) LogStatServer {
	return &kunLogStatServer{
		config: c,
	}
}

func (s *kunLogStatServer) ListenAndServe() {
	ln, err := invokerutils.ListenerFromAddress(s.config.ServerAddress, os.FileMode(0755))
	if err != nil {
		applog.Fatalf("listen %s error: %s", s.config.ServerAddress, err.Error())
		return
	}

	for {
		fd, err := ln.Accept()
		if err != nil {
			applog.V(3).Errorf("Accept error: %v", err)
			return
		}
		go s.statsHandler(fd)
	}
}

func recvFds(uc *net.UnixConn) ([]int, []byte) {
	var fds []int

	buf := make([]byte, 256)
	off := 0
	oob := make([]byte, 128)
	for len(fds) < 2 && off < 256 {
		n, oobn, _, _, err := uc.ReadMsgUnix(buf[off:], oob)
		if err != nil {
			applog.V(3).Errorf("read msg failed: %s", err.Error())
			return nil, nil
		}
		off += n
		if oobn <= 0 {
			continue
		}
		scms, err := unix.ParseSocketControlMessage(oob[:oobn])
		if err != nil {
			applog.V(3).Errorf("parse scm failed: %s", err.Error())
			return nil, nil
		}

		for i := 0; i < len(scms); i++ {
			msgfds, err := unix.ParseUnixRights(&scms[i])
			if err != nil {
				applog.V(3).Errorf("Parse Unix Rights err: %s", err.Error())
				return nil, nil
			}
			fds = append(fds, msgfds...)
		}
	}
	return fds, buf[0:off]
}

func (s *kunLogStatServer) stdlogHandler(runtimeid string, logfrom int, c io.ReadCloser) {
	storep := &s.storeMap
	receiver := &stdlogReceiver{
		name: fmt.Sprintf("%s-%s", runtimeid, logSource[logfrom]),
		conn: c,
	}
	receiver.Read(func(buf []byte, eof bool) (int, error) {
		r := storep.get(runtimeid)
		if r == nil {
			applog.V(9).Info(string(buf))
			return 0, errDiscardUserlog
		}
		return r.WriteStdLog(logfrom, buf, eof)
	})
}

func (s *kunLogStatServer) statsHandler(c net.Conn) {
	uc := c.(*net.UnixConn)
	defer uc.Close()

	fds, data := recvFds(uc) // 接收的fd由runner close
	if len(fds) != 2 {
		applog.V(3).Errorf("recv %n fds", len(fds))
		return
	}

	st := &statInfo{}
	buffer := bytes.NewBufferString(string(data))
	dec := json.NewDecoder(buffer)
	err := dec.Decode(st)
	if err != nil || st.PodName == "" {
		applog.V(3).Errorf("decode failed: %s", err.Error())
		return
	}
	syscall.SetNonblock(fds[0], false)
	syscall.SetNonblock(fds[1], false)
	outlog := os.NewFile(uintptr(fds[0]), fmt.Sprintf("%d-outlog", fds[0]))
	errlog := os.NewFile(uintptr(fds[1]), fmt.Sprintf("%d-errlog", fds[1]))

	sc := &statsConn{
		name:    st.PodName,
		conn:    c,
		memused: st.MemUsed,
	}
	s.statsMap.set(st.PodName, sc)

	defer func() {
		s.statsMap.del(st.PodName, sc)
		outlog.Close()
		errlog.Close()
	}()

	go s.stdlogHandler(st.PodName, StdoutLog, outlog)
	go s.stdlogHandler(st.PodName, StderrLog, errlog)

	storep := &s.storeMap
	stats := &statinfoReceiver{
		name: st.PodName,
		conn: c,
	}
	stats.Recv(func(info *statInfo) error {
		store := storep.get(info.PodName)
		if store != nil {
			store.SetMemUsed(info.MemUsed)
		}
		sc.memused = info.MemUsed
		return nil
	})
}

func (s *kunLogStatServer) StartRecvLog(runtimeid string, requestid string, store LogStatStore) {
	st := s.statsMap.get(runtimeid)
	if st != nil {
		store.SetMemUsed(st.memused)
	}
	s.storeMap.set(runtimeid, store)
}

func (s *kunLogStatServer) StopRecvLog(runtimeid string, requestid string, store LogStatStore) {
	s.storeMap.del(runtimeid, store)
}

func (s *kunLogStatServer) KillRuntime(runtimeID string) {

}
