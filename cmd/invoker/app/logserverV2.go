package app

import (
	"fmt"
	"sync"

	"icode.baidu.com/baidu/faas/kun/pkg/util/logs"
)

type logStatStoreMap struct {
	runtimeID string
	lastReqID string
	storeMap  map[string]LogStatStore
	lock      sync.RWMutex
}

func newLogStatStoreMap(runtimeID string) *logStatStoreMap {
	return &logStatStoreMap{
		runtimeID: runtimeID,
		storeMap:  make(map[string]LogStatStore, 0),
		lock:      sync.RWMutex{},
	}
}

func (ls *logStatStoreMap) set(requestID string, store LogStatStore) {
	ls.lock.Lock()
	defer ls.lock.Unlock()
	ls.storeMap[requestID] = store
	ls.lastReqID = requestID
}

func (ls *logStatStoreMap) get(requestID string) (store LogStatStore) {
	ls.lock.RLock()
	defer ls.lock.RUnlock()
	store, ok := ls.storeMap[requestID]
	if !ok {
		return nil
	}
	return store
}

func (ls *logStatStoreMap) getLast() (store LogStatStore, err error) {
	ls.lock.RLock()
	defer ls.lock.RUnlock()
	if ls.lastReqID != "" {
		store, ok := ls.storeMap[ls.lastReqID]
		if !ok {
			err = fmt.Errorf("get log store of last request id %s failed", ls.lastReqID)
			logs.Errorf("log stat store get last failed: %s", err)
			return nil, err
		}
		return store, nil
	}
	return nil, nil
}

func (ls *logStatStoreMap) del(requestID string, store LogStatStore) {
	ls.lock.Lock()
	defer ls.lock.Unlock()
	old, ok := ls.storeMap[requestID]
	if !ok {
		return
	}
	if old == store {
		delete(ls.storeMap, requestID)
		ls.lastReqID = ""
	}
	return
}

func (ls *logStatStoreMap) String() string {
	ls.lock.RLock()
	defer ls.lock.RUnlock()
	return fmt.Sprintf("logStoreMap-%s", ls.runtimeID)
}

type storeMap struct {
	sync.Map // string => LogStatStore
}

func (m *storeMap) setNXMap(runtimeID string) {
	v, ok := m.Load(runtimeID)
	var needInit bool
	if ok {
		_, ok := v.(*logStatStoreMap)
		if !ok {
			needInit = true
		}
	} else {
		needInit = true
	}
	if needInit {
		m.Store(runtimeID, newLogStatStoreMap(runtimeID))
	}
	return
}

func (m *storeMap) getMap(runtimeID string) (storeMap *logStatStoreMap, ok bool) {
	v, ok := m.Load(runtimeID)
	if ok {
		storeMap, ok = v.(*logStatStoreMap)
		return
	}
	return
}

func (m *storeMap) delMap(runtimeID string) {
	m.Delete(runtimeID)
}

func (m *storeMap) get(runtimeID, requestID string) LogStatStore {
	v, ok := m.Load(runtimeID)
	if !ok {
		return nil
	}
	sm, ok := v.(*logStatStoreMap)
	if !ok {
		return nil
	}
	return sm.get(requestID)
}

func (m *storeMap) set(runtimeID, requestID string, store LogStatStore) (err error) {
	v, ok := m.Load(runtimeID)
	var newMap bool
	var sm *logStatStoreMap
	if ok {
		sm, ok = v.(*logStatStoreMap)
		if !ok {
			newMap = true
			sm = newLogStatStoreMap(runtimeID)
		}
	} else {
		newMap = true
		sm = newLogStatStoreMap(runtimeID)
	}
	sm.set(requestID, store)
	if newMap {
		m.Store(runtimeID, sm)
	}
	return
}

func (m *storeMap) del(runtimeID, requestID string, store LogStatStore) {
	v, ok := m.Load(runtimeID)
	if ok {
		sm, ok := v.(*logStatStoreMap)
		if !ok {
			return
		}
		sm.del(requestID, store)
		return
	}
	return
}

type statsMap struct {
	sync.Map
}

func (m *statsMap) get(runtimeid string) *statsConn {
	v, ok := m.Load(runtimeid)
	if ok {
		return v.(*statsConn)
	}
	return nil
}

func (m *statsMap) set(runtimeid string, sc *statsConn) {
	m.Store(runtimeid, sc)
}

func (m *statsMap) del(runtimeid string, sc *statsConn) {
	v, ok := m.Load(runtimeid)
	if ok {
		old := v.(*statsConn)
		if old == sc {
			m.Delete(runtimeid)
		}
	}
}
