package app

import (
	"github.com/spf13/pflag"

	genericoptions "icode.baidu.com/baidu/faas/kun/pkg/server/options"
)

const (
	// ApplicationName 指定应用名称
	ApplicationName = "invoker"
)

type WebserverOptions struct {
	RecommendedOptions      *genericoptions.RecommendedOptions
	WaitRuntimeAliveTimeout int
	WaitUserlogDone         bool
}

type DispatcherOptions struct {
	ServerAddress string
}

type DispatcherV2Options struct {
	ServerAddress   string
	UserLogFilePath string
}

type FreezerOptions struct {
	EnableFreezeContainer bool
	DelayFreezeTimeout    int
}

type HttpProxyOptions struct {
	EnableHttpProxy         bool
	ProxyAddress            string
	BufLength               int
	WaitRuntimeAliveTimeout int
}

type LogServerOptions struct {
	ServerAddress   string
	UserLogFilePath string
}

type LogReportOption struct {
	EnableLogReport bool
	Region          string
	TargetAddress   string
	WorkerCount     int
}

type InvokerOptions struct {
	RunningMode      string
	Region           string
	FuncletEndpoint  string
	ContainerRuntime string
	Webserver        *WebserverOptions
	Dispatcher       *DispatcherOptions
	DispatcherV2     *DispatcherV2Options
	LogServer        *LogServerOptions
	LogReport        *LogReportOption
	ProxyOpt         *HttpProxyOptions
	Freezer          *FreezerOptions
}

var invokerEnableConcurrent bool

func NewInvokerOptions() *InvokerOptions {
	webserverOpts := WebserverOptions{
		RecommendedOptions:      genericoptions.NewRecommendedOptions(),
		WaitRuntimeAliveTimeout: 3,
	}
	//webserverOpts.RecommendedOptions.TelemetryOptions.ServiceName = ApplicationName
	freezerOpts := FreezerOptions{
		DelayFreezeTimeout:    1,
		EnableFreezeContainer: false,
	}
	dispatcherOpts := DispatcherOptions{
		ServerAddress: "/var/run/faas/.server.sock",
	}
	dispV2Options := DispatcherV2Options{
		ServerAddress:   "/var/run/faas/.status_v2.sock",
		UserLogFilePath: "/var/userlog",
	}
	handleOpts := LogServerOptions{
		ServerAddress:   "/var/run/faas/.status.sock",
		UserLogFilePath: "/tmp/userlog",
	}
	logReport := LogReportOption{
		EnableLogReport: true,
		Region:          "bj",
		TargetAddress:   "http://localhost:8400",
		WorkerCount:     5,
	}
	proxyOpts := &HttpProxyOptions{
		EnableHttpProxy:         false,
		ProxyAddress:            "0.0.0.0:8800",
		BufLength:               2048,
		WaitRuntimeAliveTimeout: 5,
	}
	return &InvokerOptions{

		RunningMode:  "cloud",
		Webserver:    &webserverOpts,
		Dispatcher:   &dispatcherOpts,
		DispatcherV2: &dispV2Options,
		LogReport:    &logReport,
		LogServer:    &handleOpts,
		Freezer:      &freezerOpts,
		ProxyOpt:     proxyOpts,
	}
}

func (opts *InvokerOptions) AddFlags(fs *pflag.FlagSet) {
	opts.Webserver.RecommendedOptions.AddFlags(fs)

	fs.BoolVar(&opts.LogReport.EnableLogReport, "enable-log-report", true, "开启日志上报")
	fs.StringVar(&opts.LogReport.TargetAddress, "log-report-address", "http://localhost:8400", "日志上传接口")
	fs.IntVar(&opts.LogReport.WorkerCount, "log-report-wokers", 5, "日志上报go routine个数")
	fs.IntVar(&opts.Webserver.WaitRuntimeAliveTimeout, "runtime-alive-timeout", 3, "Timeout(s) to wait runtime alive")
	fs.BoolVar(&opts.Webserver.WaitUserlogDone, "wait-userlog-done", true, "等待用户日志传输结束")
	fs.BoolVar(&opts.Freezer.EnableFreezeContainer, "enable-freeze-container", true, "开启冻结容器功能")
	fs.StringVar(&opts.FuncletEndpoint, "funclet-endpoint", "/var/run/faas-inner/.funcletapi.sock", "Funclet服务接口")
	fs.StringVar(&opts.Region, "region", "bj", "服务所在地域")
	fs.StringVar(&opts.RunningMode, "running-mode", opts.RunningMode, "[cloud|duedge]")
	fs.IntVar(&opts.Freezer.DelayFreezeTimeout, "delay-freeze-timeout", opts.Freezer.DelayFreezeTimeout, "Timeout(s) to wait runtime freeze")
	fs.StringVar(&opts.DispatcherV2.UserLogFilePath, "userlog-filepath", opts.DispatcherV2.UserLogFilePath, "用户日志存储路径")
	fs.StringVar(&opts.DispatcherV2.ServerAddress, "dispatcher-address", "unix:///var/run/faas/.status_v2.sock", "v2调用和日志服务器地址")
	fs.BoolVar(&invokerEnableConcurrent, "enable-concurrent", false, "允许 pod 多并发调用")
	fs.StringVar(&opts.ContainerRuntime, "container-runtime", "runc", "容器运行时")

	// 以下指令即将废弃
	// fs.BoolVar(&opts.ProxyOpt.EnableHttpProxy, "enable-proxy", false, "开启http proxy")
	// fs.StringVar(&opts.ProxyOpt.ProxyAddress, "proxy-address", "0.0.0.0:8800", "http proxy地址")
	// fs.IntVar(&opts.ProxyOpt.BufLength, "header-buffer-length", 2048, "http proxy header buffer长度")
	fs.StringVar(&opts.LogServer.ServerAddress, "logserver-address", "unix:///var/run/faas/.status.sock", "日志服务器地址（废弃）")
	fs.StringVar(&opts.Dispatcher.ServerAddress, "unixsock", "unix:///var/run/faas/.server.sock", "Unix domain socks of invoker（废弃）")
}
