package app

import (
	"net/http"
	"net/http/httptest"
	"strings"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestNetTrafficHandler(t *testing.T) {
	opt := &DispatcherV2Options{}

	d := NewDispatchServerV2(opt, nil, NewRuntimeMap())
	_counter = nil

	d.getRouteHandler()

	resp, req := buildRequestResponse()
	d.netTrafficHandler(resp, req)
	assert.Equal(t, 404, resp.Code)

	runtime := &RuntimeInfo{}
	d.runtimeMap.Store("123", runtime)
	resp, req = buildRequestResponse()
	d.netTrafficHandler(resp, req)
	assert.Equal(t, 200, resp.Code)

	runtime.UserID = "user-id"
	resp, req = buildRequestResponse()
	d.netTrafficHandler(resp, req)
	assert.Equal(t, 200, resp.Code)
}

func buildRequestResponse() (*httptest.ResponseRecorder, *http.Request) {
	body := `{"inbytes":12,"outbytes":27}`
	request := httptest.NewRequest("post", "/netmon", strings.NewReader(body))
	request.Header.Set("x-cfc-runtimeid", "123")
	response := httptest.NewRecorder()
	return response, request
}
