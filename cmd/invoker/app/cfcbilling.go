package app

import (
	"github.com/emicklei/go-restful"

	"icode.baidu.com/baidu/faas/kun/pkg/api"
)

const (
	cfcInvokeBillHeader = "X-Bd-Cfc-InvokeBill"
)

func invokeBillInfo(request *RequestInfo) string {
	if request == nil {
		return ""
	}
	b := api.InvokeBillInfo{
		UserID:   request.Input.User.ID,
		Count:    1,
		MemUsage: (request.MemorySpecSize / 64) * (request.BilledTimeMS / 100),
		BillTime: request.BilledTimeMS,
	}
	return b.Encode()
}

func addInvokeBillHeader(request *RequestInfo, response *restful.Response) {
	header := invokeBillInfo(request)
	if len(header) > 0 {
		response.AddHeader(api.CfcInvokeBillHeader, header)
	}
}
