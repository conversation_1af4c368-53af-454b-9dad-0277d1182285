package app

import (
	"net/http"
	"time"

	"github.com/emicklei/go-restful"

	"icode.baidu.com/baidu/faas/kun/pkg/api"
	"icode.baidu.com/baidu/faas/kun/pkg/bce/brn"
)

func statisticsInfo(request *RequestInfo) string {
	if nil == request {
		return ""
	}
	input := request.Input
	funcname := ""
	if nil != input.Configuration.FunctionName {
		funcname = *(input.Configuration.FunctionName)
	}
	version := ""
	if nil != input.Configuration.Version {
		version = *(input.Configuration.Version)
	}
	funcbrn, _ := brn.Parse(*request.Input.Configuration.FunctionArn)
	timeNowMs := time.Now().UnixNano() / int64(time.Millisecond)
	statusCode := http.StatusExpectationFailed
	if request.Status == api.StatusSuccess {
		statusCode = http.StatusOK
	} else if request.Status == api.StatusTimeout {
		statusCode = http.StatusRequestTimeout
	}
	msg := api.StatisticInfo{
		UserID:     request.Input.User.ID,
		Region:     funcbrn.Region,
		Function:   funcname,
		Version:    version,
		StartTime:  request.InvokeStartTimeMS,
		Duration:   timeNowMs - request.InvokeStartTimeMS,
		MemoryUsed: request.MaxMemUsedBytes,
		StatusCode: statusCode,
	}

	return msg.Encode()
}

func addStatisticHeader(request *RequestInfo, response *restful.Response) {
	header := statisticsInfo(request)
	if len(header) > 0 {
		response.AddHeader(api.CfcStatisticHeader, header)
	}
}
