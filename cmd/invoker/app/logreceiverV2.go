package app

import (
	"bufio"
	"fmt"

	"icode.baidu.com/baidu/faas/kun/pkg/util/logs"
)

const (
	ZeroByte byte = '\000'
	TabByte  byte = '\t'
	LineByte byte = '\n'
)


type stdLogReceiver struct {
	runtimeID    string
	logStoreMap  *logStatStoreMap
	logFrom      int
	name         string
	conn         *bufio.ReadWriter
	bufferLength int
	logger       *logs.Logger
}

func (lsm *logStatStoreMap) NewStdLogReceiver(runtimeID string, logFrom int, conn *bufio.ReadWriter, bufferLength int) *stdLogReceiver {
	name := fmt.Sprintf("%s stdlog.%d", runtimeID, logFrom)
	if bufferLength <= 0 {
		bufferLength = defaultLogBufferLength
	}
	return &stdLogReceiver{
		runtimeID:    runtimeID,
		logStoreMap:  lsm,
		logFrom:      logFrom,
		name:         name,
		conn:         conn,
		bufferLength: bufferLength,
		logger:       logs.NewLogger().WithField("name", name),
	}
}

func (r *stdLogReceiver) Read() {
	buf := make([]byte, r.bufferLength+1)
	next := 0     // offset
	lnTabCnt := 0 // tab count in recent line
	var lnReqID string
	var ls LogStatStore
	for {

		// bytes count from connection
		read, err := r.conn.Read(buf[next:r.bufferLength])
		r.logger.V(9).Infof("recv res: read %d, data %s, err %+v", read, string(buf[next:next+read]), err)
		if err != nil {
			r.logger.V(5).Errorf("recv failed: %s", err.Error())
			return
		}

		// if log store map is empty, discard the data
		if len(r.logStoreMap.storeMap) == 0 {
			r.logger.Warn("can't find log store map")
			next = 0
			continue
		}

		// if can't get the log store of the last request, discard the data
		ls, err = r.logStoreMap.getLast()
		if err != nil {
			r.logger.Warnf("get last log store of %s failed", r.logStoreMap.String())
			next = 0
			continue
		}
		if ls == nil {
			next = 0
			continue
		}

		next += read

		nzero := 0
		lastln := -1
		lastTab := -1

		// parse data
		for i := 0; i < next; i++ {
			if buf[i] == ZeroByte { // \0 means the end of the request
				r.logger.V(9).Infof("next %d lastln %d i %d nzero %d", next, lastln, i, nzero)
				nzero++
				copy(buf[i:], buf[i+1:next]) // remove \0
				i--
				next--
				nwrite, err := ls.WriteStdLog(r.logFrom, buf[lastln+1:i+1], true)
				// write error: discard the log data, start the new line process
				// write all success: start the new line process
				if err != nil || nwrite == i-lastln {
					if err != nil {
						r.logger.Errorf("write std log %s len %d with eof lastln %d i %d failed: %s", ls.String(), nwrite, lastln, i, err)
					} else {
						r.logger.V(6).Infof("write std log %s len %d with eof lastln %d i %d", ls.String(), nwrite, lastln, i)
					}
					lastln = i
					lnTabCnt = 0
					lastTab = -1
					continue
				}
				// write partial success: adjust the next pointer
				r.logger.Warnf("write partial std log %s len %d with eof lastln %d i %d", ls.String(), nwrite, lastln, i)
				i = lastln + nwrite
			} else if buf[i] == LineByte {
				r.logger.V(9).Infof("next %d lastln %d i %d nzero %d", next, lastln, i, nzero)
				nwrite, err := ls.WriteStdLog(r.logFrom, buf[lastln+1:i+1], false)
				// write error: discard the log data, start the new line process
				// write all success: start the new line process
				if err != nil || nwrite == i-lastln {
					if err != nil {
						r.logger.Errorf("write std log %s len %d lastln %d i %d failed: %s", ls.String(), nwrite, lastln, i, err)
					} else {
						r.logger.V(6).Infof("write std log %s len %d lastln %d i %d", ls.String(), nwrite, lastln, i)
					}
					// mark as new line process
					lastln = i
					lnTabCnt = 0
					lastTab = -1
					continue
				}
				// write partial success: adjust the next pointer
				r.logger.Warnf("write partial std log %s len %d lastln %d i %d", ls.String(), nwrite, lastln, i)
				i = lastln + nwrite
			} else if buf[i] == TabByte {
				r.logger.V(9).Infof("next %d lastln %d i %d nzero %d", next, lastln, i, nzero)
				lnTabCnt++
				if lnTabCnt == 2 {
					lnReqID = string(buf[lastTab+1 : i])
					r.logger.V(6).Infof("parse request id %s", lnReqID)
					lls := r.logStoreMap.get(lnReqID)
					if lls != nil {
						ls = lls
					} else {
						r.logger.Errorf("map %s can't find log store of request %s", ls.String(), lnReqID)
					}
				}
				lastTab = i
			}
		}

		// Parse long data:
		// when there are no \0 character and no new line, check the buffer size
		// when buffer is full, add \n character; otherwise, continue read from buffer
		if nzero == 0 && lastln < 0 {
			if next == r.bufferLength {
				buf[next] = LineByte
				nwrite, err := ls.WriteStdLog(r.logFrom, buf[lastln+1:next+1], false)
				if err != nil || nwrite == next-lastln {
					if err != nil {
						r.logger.Errorf("buffer full write std log len %d err %s with eof lastln %d next %d", nwrite, err, lastln, next, err)
					} else {
						r.logger.V(6).Infof("buffer full write std log len %d err %s with eof lastln %d next %d", nwrite, err, lastln, next)
					}
					next = 0
				} else if nwrite > 0 {
					r.logger.Warnf("buffer full write partial std log len %d err %s with eof lastln %d next %d", ls.String(), nwrite, lastln, next)
					next -= nwrite
					copy(buf[0:], buf[nwrite:next+1])
				}
				lastln = -1
				lastTab = -1
				lnTabCnt = 0
			} else {
				continue
			}
		}

		r.logger.V(9).Infof("next %d lastln %d nzero %d", next, lastln, nzero)
		copy(buf[0:], buf[lastln+1:next])
		next = next - lastln - 1
		lastln = -1
		lastTab = -1
		lnTabCnt = 0
	}
}
