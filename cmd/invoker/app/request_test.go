package app

import (
	"strings"
	"testing"

	"github.com/aws/aws-sdk-go/service/lambda"
	"icode.baidu.com/baidu/faas/kun/pkg/api"
)

func TestRequestInfo(t *testing.T) {
	req := NewRequestInfo("aa", "bb")
	req.SetInitTime(1, 2)
	if req.InitStartTimeMS != 1 ||
		req.InitDoneTimeMS != 2 {
		t.<PERSON>rro<PERSON>("init time error: [%d-%d]", req.InitStartTimeMS, req.InitDoneTimeMS)
	}

	req.InvokeResult(api.StatusSuccess, "result")
	t.Log(req.Output.LogMessage)
}

// mock LogStatStore

type mockLogStatStore struct {
	logs      []string
	memUsed   int64
	logDone   bool
	logFile   string
	closeData string
}

// Receiver Receiver 返回一个字符串，表示接收器的名称，在这种情况下是"mock"
func (m *mockLogStatStore) Receiver() string { return "mock" }

// String String() string
// 返回一个字符串，表示对象的类型和值。如果对象实现了 fmt.Stringer 接口，则使用该接口的 String 方法；否则，将调用 fmt.Sprint 函数。
func (m *mockLogStatStore) String() string { return "mockLogStatStore" }

// WriteStdLog mockLogStatStore.WriteStdLog
// 将从指定位置开始的字节数组写入标准日志，并返回写入的字节数和错误信息（如果有）。
// 参数from：int - 起始位置，表示要写入的字节在缓冲区中的索引，从0开始计数。
// 参数buf：[]byte - 包含要写入的字节的切片。
// 参数eof：bool - 是否为文件结束符，true表示是，false表示不是。
// 返回值1: int - 返回写入的字节数。
// 返回值2: error - 如果写入过程中出现错误，则返回该错误；否则返回nil。
func (m *mockLogStatStore) WriteStdLog(from int, buf []byte, eof bool) (int, error) {
	return len(buf), nil
}

// WriteCfcLog 写入cfc日志到存储中，参数为字符串类型的日志内容，返回值为error类型，表示操作是否成功，如果成功则返回nil
func (m *mockLogStatStore) WriteCfcLog(log string) error { m.logs = append(m.logs, log); return nil }

// SetMemUsed 设置内存使用量，单位为字节（byte）
func (m *mockLogStatStore) SetMemUsed(used int64) { m.memUsed = used }

// LogFile LogFile 返回日志文件名，如果没有则为空字符串
// 参数：无
// 返回值：string - 日志文件名
func (m *mockLogStatStore) LogFile() string { return m.logFile }

// MemUsed MemUsed 返回当前内存使用量，单位为字节（byte）
func (m *mockLogStatStore) MemUsed() int64 { return m.memUsed }

// LogDone LogDone 设置是否已完成日志，并返回之前的状态值
// 参数：
//
//	set bool - 是否已完成日志，true表示已完成，false表示未完成
//
// 返回值：
//
//	bool - 返回之前的状态值，true表示已完成，false表示未完成
func (m *mockLogStatStore) LogDone(set bool) bool { m.logDone = set; return m.logDone }

// Close Close 实现了 LogStatStore 接口的 Close 方法，用于关闭日志统计存储。
// 返回值：
//   - string: 关闭时的数据，如果没有则为空字符串；
//   - error: 如果发生错误，则返回该错误；否则返回 nil。
func (m *mockLogStatStore) Close() (string, error) { return m.closeData, nil }

// Wait Wait 等待函数，无参数和返回值
// 函数类型：Wait
// 参数：无参数
func (m *mockLogStatStore) Wait() {}

// TestRequestInfo_InvokeStartAndDone 测试函数，用于调用RequestInfo的InvokeStart和InvokeDone方法，并检查是否正确设置了store、写入了日志以及计算了费用。
// 参数：t *testing.T - 单元测试模块，用于记录错误信息。
func TestRequestInfo_InvokeStartAndDone(t *testing.T) {
	req := NewRequestInfo("reqid", "rtid")
	store := &mockLogStatStore{memUsed: 128, logFile: "mock.log", closeData: "logdata"}
	// 构造必要的Input/Output结构
	ver := "v1"
	req.Input = &api.InvocationInput{
		Configuration: &api.FunctionConfiguration{
			FunctionConfiguration: lambda.FunctionConfiguration{
				Version: &ver,
			},
		},
	}
	req.Output.LogMessage = []string{}
	req.InvokeStart(store)
	if req.store != store {
		t.Error("InvokeStart should set store")
	}
	if len(store.logs) == 0 || store.logs[0] == "" {
		t.Error("InvokeStart should write log")
	}
	req.Status = api.StatusRuning
	req.InvokeDone(false)
	if req.BilledTimeMS == 0 {
		t.Error("InvokeDone should set BilledTimeMS")
	}
	if len(req.Output.LogMessage) == 0 || req.Output.LogMessage[0] != "logdata" {
		t.Error("InvokeDone should append logdata")
	}
}

// TestRequestInfo_CleanInputOutput 测试函数TestRequestInfo_CleanInputOutput，该函数用于清除请求信息的输入和输出部分。
// 参数t是*testing.T类型，表示单元测试对象；返回值没有。
func TestRequestInfo_CleanInputOutput(t *testing.T) {
	req := NewRequestInfo("reqid", "rtid")
	req.Input = &api.InvocationInput{
		EventBody:     "body",
		ClientContext: "ctx",
	}
	req.Output.FuncResult = "result"
	req.Output.FuncError = "error"
	req.CleanInput()
	if req.Input.EventBody != "" || req.Input.ClientContext != "" {
		t.Error("CleanInput should clear EventBody and ClientContext")
	}
	req.CleanOutput()
	if req.Output.FuncResult != "" || req.Output.FuncError != "" {
		t.Error("CleanOutput should clear FuncResult and FuncError")
	}
}

// TestRequestInfo_InvokeResult 测试函数TestRequestInfo_InvokeResult，该函数用于测试RequestInfo结构体的InvokeResult方法。
// 该方法用于设置请求的执行结果，包括成功和失败两种情况。如果当前请求处于运行中状态（Status为Running），则会将请求标记为成功或失败，并设置相应的结果信息；否则，不应该改变请求的状态和结果信息。
// 参数t是*testing.T类型，用于记录测试过程中发生的错误。
// 返回值没有。
func TestRequestInfo_InvokeResult(t *testing.T) {
	req := NewRequestInfo("reqid", "rtid")
	req.Status = api.StatusRuning
	req.Output.FuncResult = ""
	req.Output.FuncError = ""
	req.InvokeResult(api.StatusSuccess, "ok")
	if req.Status != api.StatusSuccess || req.Output.FuncResult != "ok" {
		t.Error("InvokeResult should set success result")
	}

	req.Status = api.StatusRuning
	req.InvokeResult(api.StatusFailed, "fail")
	if req.Status != api.StatusFailed || req.Output.FuncError != "Unhandled" || req.Output.ErrorInfo != "fail" {
		t.Error("InvokeResult should set fail result")
	}

	// 非running状态不应变更
	req.Status = api.StatusSuccess
	req.Output.FuncResult = "should not change"
	req.InvokeResult(api.StatusFailed, "fail2")
	if req.Output.FuncResult != "should not change" {
		t.Error("InvokeResult should not change result if not running")
	}
}

// TestRequestInfo_SetNonTimeoutErrorInfoAndTimeout 测试函数TestRequestInfo_SetNonTimeoutErrorInfoAndTimeout，该函数用于设置非超时错误信息和超时错误信息以及状态。
// 参数t是*testing.T类型的指针，表示一个单元测试；返回值没有。
func TestRequestInfo_SetNonTimeoutErrorInfoAndTimeout(t *testing.T) {
	req := NewRequestInfo("reqid", "rtid")
	req.Output.FuncError = ""
	req.Output.ErrorInfo = ""
	req.Status = api.StatusRuning
	req.SetNonTimeoutErrorInfo("errinfo")
	if req.Output.FuncError != "Unhandled" || req.Output.ErrorInfo != "errinfo" || req.Status != api.StatusFailed {
		t.Error("SetNonTimeoutErrorInfo should set error info and status")
	}

	req.Status = api.StatusRuning
	req.SetTimeoutErrorInfo()
	if req.Output.FuncError != "Unhandled" || req.Output.ErrorInfo != api.InvokeTimeout || req.Status != api.StatusTimeout {
		t.Error("SetTimeoutErrorInfo should set timeout info and status")
	}
}

// TestRequestInfo_WriteFuncErrorToCFCMsg 测试函数RequestInfo_WriteFuncErrorToCFCMsg，该函数用于将函数调用失败的错误信息写入到CFC消息中。
// 参数：t *testing.T - 类型为*testing.T，表示单元测试的对象，用于报告结果和记录panic状态。
// 返回值：无返回值
func TestRequestInfo_WriteFuncErrorToCFCMsg(t *testing.T) {
	req := NewRequestInfo("reqid", "rtid")
	store := &mockLogStatStore{}
	req.store = store
	req.WriteFuncErrorToCFCMsg("errMsg")
	if len(store.logs) == 0 || !strings.Contains(store.logs[0], "Function Invoke Failed") {
		t.Error("WriteFuncErrorToCFCMsg should write error log")
	}
}

// TestRequestInfo_Notify 该函数用于测试RequestInfo结构体的Notify方法，主要验证通知事件是否正确发送到了channel中或者是否panic。
func TestRequestInfo_Notify(t *testing.T) {
	req := NewRequestInfo("reqid", "rtid")
	ch := make(chan string, 1)
	req.SyncChannel = ch
	req.Notify("event1")
	select {
	case v := <-ch:
		if v != "event1" {
			t.Error("Notify should send event to channel")
		}
	default:
		t.Error("Notify should send event")
	}

	// 测试recover分支
	req.SyncChannel = nil
	req.Notify("event2") // 不应panic
}
