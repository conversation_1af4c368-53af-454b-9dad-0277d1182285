package app

import (
	"bufio"
	"errors"
	"io"

	_ "icode.baidu.com/baidu/faas/kun/pkg/cfclog/bos"
	_ "icode.baidu.com/baidu/faas/kun/pkg/cfclog/json"

	"icode.baidu.com/baidu/faas/kun/pkg/util/json"
)

const (
	defaultLogBufferLength = 64 * 1024 // 默认使用64KB缓冲区接收日志
	defaultUserLogLength   = 4 * 1024
	maxUserLogSize         = 6 * 1024 * 1024

	CfcsysLog = 0
	StdoutLog = 1
	StderrLog = 2
)

var (
	logSource         = []string{"cfc", "stdout", "stderr"}
	errReceiverClosed = errors.New("buffer has closed.")
	errDiscardUserlog = errors.New("discard user log")
)

type stdlogReceiver struct {
	name string
	conn io.ReadCloser
}

func (r *stdlogReceiver) Read(callbk func([]byte, bool) (int, error)) {
	buf := make([]byte, defaultLogBufferLength)
	next := 0 // 偏移量

	for {
		read, err := r.conn.Read(buf[next:])
		if err != nil {
			applog.V(5).Infof("recv %s failed: %s", r.name, err.Error())
			return
		}
		if callbk == nil {
			next = 0
			continue
		}

		next += read

		nzero := 0
		lastln := -1
		for i := 0; i < next; i++ {
			if buf[i] == '\000' {
				nzero++
				copy(buf[i:], buf[i+1:next]) // remove \0
				i--
				next--
			} else if buf[i] == '\n' {
				lastln = i
			}
		}

		if nzero == 0 && lastln < 0 {
			if next == len(buf) { // 缓冲区满，且未收到换行
				buf[next-1] = '\n'
				lastln = next - 1
			} else { // 缓冲区未满，继续接收
				continue
			}
		}

		nwrite, err := callbk(buf[:lastln+1], nzero > 0)
		applog.V(9).Infof("write log done. nwrite=%d, lastln=%d, next=%d",
			nwrite, lastln, next)
		if err != nil { // 写入失败丢弃
			next = 0
		} else if nwrite > 0 { // 写入了部分日志
			copy(buf[0:], buf[nwrite:next])
			next -= nwrite
		}
	}
}

func (r *stdlogReceiver) Close() {
	r.conn.Close()
}

type statInfo struct {
	PodName string `json:"podname,omitempty"`
	MemUsed int64  `json:"memory,omitempty"`
}

type statinfoReceiver struct {
	name string
	conn io.ReadWriter
}

func (r *statinfoReceiver) Recv(callbk func(*statInfo) error) {
	reader := bufio.NewReader(r.conn)
	for {
		line, _, err := reader.ReadLine()
		if err != nil {
			applog.V(5).Infof("recv %s status failed: %s", r.name, err.Error())
			break
		}

		stat := &statInfo{}
		err = json.Unmarshal(line, stat)
		if err != nil {
			applog.V(4).Warnf("recv %s status failed: %s", r.name, err.Error())
			break
		}
		if len(stat.PodName) == 0 {
			stat.PodName = r.name
		}
		if err = callbk(stat); err != nil {
			applog.V(4).Warnf("write %s failed: %s", r.name, err.Error())
		}
	}
}
