package app

import (
	"fmt"
	"io/ioutil"
	"net"
	"os"
	"syscall"
	"testing"
	"time"
)

func sendData(conn *net.UnixConn, data []byte) error {
	_, err := conn.Write(data)
	return err
}

func sendSock(conn *net.UnixConn, data []byte, fd int) error {
	applog.V(5).Infof("sendsock %s fd: %d", string(data), fd)
	oob := syscall.UnixRights(fd)
	_, _, err := conn.WriteMsgUnix(data, oob, nil)
	return err
}

func TestLogStatServer(t *testing.T) {
	filepath, err := ioutil.TempDir("", "invoker")
	sockfile := filepath + "/cfc-runner.sock"
	o := &LogServerOptions{
		ServerAddress:   fmt.Sprintf("unix://%s", sockfile),
		UserLogFilePath: "/tmp",
	}
	s := NewLogStatServer(o)
	go s.ListenAndServe()

	<-time.NewTimer(1 * time.Second).C

	// 连接server
	raddr := &net.UnixAddr{
		Name: sockfile,
		Net:  "unix",
	}
	conn, err := net.DialUnix("unix", nil, raddr)
	if err != nil {
		t.Fatal(err)
	}
	defer conn.Close()
	err = sendData(conn, []byte("{\"podname\":\"pod-xxxx-128-aaa\"}"))
	if err != nil {
		t.Fatal(err)
	}

	outr, outw, _ := os.Pipe()
	errr, errw, _ := os.Pipe()
	defer func() {
		outr.Close()
		outw.Close()
		errr.Close()
		errw.Close()
	}()
	err = sendSock(conn, []byte("outfd"), int(outr.Fd()))
	if err != nil {
		t.Fatal(err)
	}
	err = sendSock(conn, []byte("errfd"), int(errr.Fd()))
	if err != nil {
		t.Fatal(err)
	}

	go func() {
		outw.WriteString("runner outlog line 1\n\000xxxxxx\n\ndddddd\000\000\naaaaaaaaaa\n")
		errw.WriteString("runner errlog line 2\n\000err xxxxxx\n\n err dddddd\000\000\naa  errr aaaaaaaa\n")
		outw.WriteString("runner outlog line 3\naaaaaa\n\ncccc\000\000dddd\n")
		errw.WriteString("runner errlog line 4\naaaaaa\n\ncccc\000\000dddd\n")
	}()
	size := []int64{140723380133950, 240723380133952, 340723380133950}
	for _, m := range size {
		sendData(conn, []byte(fmt.Sprintf("{\"memory\": %d}\n", m)))
		<-time.NewTimer(1 * time.Second).C
	}

	store := newLogStatStore("xxxxx-xxx", "pod-xxxx-128-aaa", "", "bos")
	s.StartRecvLog("pod-xxxx-128-aaa", "xxxxx-xxx", store)
	go func() {
		outw.WriteString("runner outlog line 5\nxxxxxx\n\ndddddd\000\000\n")
	}()
	<-time.NewTimer(1 * time.Second).C
	data, _ := store.Close()
	s.StopRecvLog("pod-xxxx-128-aaa", "xxxxx-xxx", store)
	os.RemoveAll("./pod-xxxx-128-aaa")
	mem := store.MemUsed()
	if data != "runner outlog line 5\nxxxxxx\n\ndddddd\n" {
		t.Fatal(data)
	}
	if mem != 340723380133950 {
		t.Fatal(mem)
	}
}
