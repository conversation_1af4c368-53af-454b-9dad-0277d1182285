package app

import (
	"context"
	"fmt"
	"net"
	"net/http"
	"net/url"

	goevents "github.com/docker/go-events"
	"go.uber.org/zap"

	"icode.baidu.com/baidu/faas/kun/pkg/api"
	logclient "icode.baidu.com/baidu/faas/kun/pkg/logstash/client"
)

type LogReporter struct {
	config *LogReportOption
	writer goevents.Sink
	workCh chan *api.UserRequestLog
}

func NewLogReporter(o *LogReportOption) (*LogReporter, error) {
	if !o.EnableLogReport {
		return nil, fmt.Errorf("enable-log-report is false")
	}
	addr, err := url.Parse(o.TargetAddress)
	if err != nil {
		return nil, err
	}

	reporter := &LogReporter{
		config: o,
		workCh: make(chan *api.UserRequestLog, 100),
	}

	baseurl := o.TargetAddress
	var httpcli *http.Client
	if addr.Scheme == "unix" {
		httpcli = &http.Client{
			Transport: &http.Transport{
				DialContext: func(_ context.Context, _, _ string) (net.Conn, error) {
					return net.Dial("unix", o.TargetAddress)
				},
			},
		}
		baseurl = "http://localhost"
	}
	for i := 0; i < o.WorkerCount; i++ {
		w := &logReportWorker{
			reqsCh:  reporter.workCh,
			client:  logclient.NewLogstashClient(),
			baseurl: baseurl,
		}
		if httpcli != nil {
			w.client.SetHttpClient(httpcli)
		}
		go w.run()
	}

	reporter.writer = goevents.NewQueue(reporter)
	return reporter, nil
}

func (r *LogReporter) Report(request *RequestInfo, filePath string) error {
	if !r.config.EnableLogReport {
		return nil
	}
	if len(filePath) == 0 {
		return nil
	}
	// 将数据序列化后，不再引用request对象，让其尽快释放
	logreq := &api.UserRequestLog{
		RequestID:  request.RequestID,
		RuntimeID:  request.RuntimeID,
		Function:   request.Input.Configuration,
		LogFile:    filePath,
		Credential: request.Credential,
		LogConfig:  request.Input.LogConfig,
	}

	result := &api.InvokeResult{
		MemUsed:  request.MaxMemUsedBytes,
		Duration: request.InvokeDoneTimeMS - request.InvokeStartTimeMS,
	}
	switch request.Status {
	case api.StatusSuccess:
		result.Status = "success"
	case api.StatusFailed:
		result.Status = "failed"
	case api.StatusTimeout:
		result.Status = "timeout"
	}
	logreq.Result = result

	return r.writer.Write(logreq)
}

func (r *LogReporter) Write(event goevents.Event) error {
	info, _ := event.(*api.UserRequestLog)
	r.workCh <- info
	return nil
}

func (r *LogReporter) Close() error {
	close(r.workCh)
	return nil
}

type logReportWorker struct {
	reqsCh  chan *api.UserRequestLog
	client  logclient.LogstashClient
	baseurl string
}

func (w *logReportWorker) run() {
	for {
		select {
		case req, ok := <-w.reqsCh:
			if !ok {
				return
			}
			err := w.reportLog(req)
			if err != nil {
				applog.Error(err.Error())
			}
		}
	}
}

func (w *logReportWorker) reportLog(info *api.UserRequestLog) error {
	err := w.client.ReportLog(w.baseurl, info)
	applog.V(6).Info(fmt.Sprintf("report log done, err=%v", err), zap.String("request_id", info.RequestID))
	return err
}
