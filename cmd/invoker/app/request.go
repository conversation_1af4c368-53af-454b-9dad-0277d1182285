package app

import (
	"fmt"
	"time"

	"icode.baidu.com/baidu/faas/kun/pkg/api"
	"icode.baidu.com/baidu/faas/kun/pkg/util/bytefmt"
)

const (
	baseDuration               = time.Duration(100) * time.Millisecond
	maxDuration  time.Duration = 1<<63 - 1
)

func ceilBillDuration(d time.Duration) time.Duration {
	r := d % baseDuration
	if r == 0 {
		if d == 0 {
			return baseDuration
		}
		return d
	}
	if d1 := d + baseDuration - r; d1 > d {
		return d1
	}
	return maxDuration
}

type RequestInfo struct {
	api.RequestInfo
	store LogStatStore

	SyncChannel chan string
}

func NewRequestInfo(requestID, runtimeID string) *RequestInfo {
	return &RequestInfo{
		RequestInfo: api.RequestInfo{
			RequestID:         requestID,
			RuntimeID:         runtimeID,
			InvokeStartTimeMS: time.Now().UnixNano() / int64(time.Millisecond),
		},
		SyncChannel: make(chan string, 2),
	}
}

func (info *RequestInfo) SetInitTime(preInit, postInit int64) {
	info.InitStartTimeMS = preInit
	info.InitDoneTimeMS = postInit
}

func (info *RequestInfo) InvokeStart(store LogStatStore) {
	info.store = store
	info.store.WriteCfcLog(fmt.Sprintf("START RequestId: %s Version: %s\n",
		info.RequestID, *info.Input.Configuration.Version))
}

// 减少生命周期，使其尽快释放内存
func (info *RequestInfo) CleanInput() {
	input := info.Input
	if input != nil {
		input.EventBody = ""
		input.ClientContext = ""
	}
}

func (info *RequestInfo) CleanOutput() {
	info.Output.FuncResult = ""
	info.Output.FuncError = ""
}

func (info *RequestInfo) InvokeResult(status api.RequestStatus, result string) {
	if info.Status != api.StatusRuning {
		return
	}
	info.Status = status
	if status == api.StatusSuccess {
		info.Output.FuncResult = result
	} else {
		info.Output.FuncError = "Unhandled"
		info.Output.ErrorInfo = result
	}
}

// SetNonTimeoutErrorInfo 设置非超时错误信息
// 参数：errInfo (string) - 错误信息
// 返回值：无
func (info *RequestInfo) SetNonTimeoutErrorInfo(errInfo string) {
	// FuncError 目前只有Unhandled，表示函数没有执行成功
	info.Output.FuncError = "Unhandled"
	info.Output.ErrorInfo = errInfo
	info.Status = api.StatusFailed
}

// timeout错误信息固定
func (info *RequestInfo) SetTimeoutErrorInfo() {
	info.Output.FuncError = "Unhandled"
	info.Output.ErrorInfo = api.InvokeTimeout
	info.Status = api.StatusTimeout
}

func (info *RequestInfo) WriteFuncErrorToCFCMsg(errMsg string) {
	info.store.WriteCfcLog(fmt.Sprintf("Function Invoke Failed %s\n", errMsg))
}

func (info *RequestInfo) InvokeDone(waitlog bool) {
	info.InvokeDoneTimeMS = time.Now().UnixNano() / int64(time.Millisecond)
	duration := (time.Duration)(info.InvokeDoneTimeMS-info.InvokeStartTimeMS) * time.Millisecond
	billed := ceilBillDuration(duration)

	// 计算BilledTimeMS
	info.BilledTimeMS = billed.Nanoseconds() / int64(time.Millisecond)
	info.MaxMemUsedBytes = info.store.MemUsed()
	if waitlog {
		info.waitLogDone() // 先等待用户日志接收完成
	} else {
		info.store.LogDone(true)
	}
	info.store.WriteCfcLog(fmt.Sprintf("END RequestId: %s\n", info.RequestID))
	info.store.WriteCfcLog(fmt.Sprintf("REPORT RequestId: %s\tDuration: %s\tBilled Duration: %s\tMax Memory Used: %s",
		info.RequestID, duration.String(), billed.String(), bytefmt.ByteSize(uint64(info.MaxMemUsedBytes))))
	logdata, _ := info.store.Close()
	info.Output.LogMessage = append(info.Output.LogMessage, logdata)
}

func (info *RequestInfo) waitLogDone() {
	store := info.store
	if store.LogDone(false) {
		return
	}

	go func() { // 最多等待100ms
		timer := time.NewTimer(20 * time.Millisecond)
		<-timer.C
		store.LogDone(true) // 强制触发
		timer.Stop()
	}()
	store.Wait()
}

func (info *RequestInfo) Notify(event string) {
	defer func() {
		if r := recover(); r != nil {
			applog.V(3).Errorf("recover %v", r)
		}
	}()
	if info.SyncChannel != nil {
		info.SyncChannel <- event
	}
}
