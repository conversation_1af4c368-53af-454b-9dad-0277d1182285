package app

import (
	"bufio"
	"bytes"
	"encoding/base64"
	"errors"
	"fmt"
	"net"
	"net/http"
	"net/url"
	runtimeStack "runtime"
	"strings"
	"time"

	"github.com/emicklei/go-restful"
	"go.uber.org/zap"

	"icode.baidu.com/baidu/faas/kun/pkg/api"
	commonErr "icode.baidu.com/baidu/faas/kun/pkg/error"
	kunErr "icode.baidu.com/baidu/faas/kun/pkg/error"
	"icode.baidu.com/baidu/faas/kun/pkg/httptrigger/rpc"
	invokerErr "icode.baidu.com/baidu/faas/kun/pkg/invoker/error"
	"icode.baidu.com/baidu/faas/kun/pkg/server"
	"icode.baidu.com/baidu/faas/kun/pkg/server/endpoint"
	genericfilters "icode.baidu.com/baidu/faas/kun/pkg/server/filters"
	"icode.baidu.com/baidu/faas/kun/pkg/util/json"
	"icode.baidu.com/baidu/faas/kun/pkg/util/split"
	"icode.baidu.com/baidu/faas/kun/pkg/version"
)

var (
	invokeHttpPort = "8080"
)

const (
	InvokeMetricAttribute = "invokeMetric"
)

type WebInvokeServer struct {
	config      *WebserverOptions
	runtimeMap  *RuntimeMap
	runtimeCtrl *LogStatManager
	logReporter *LogReporter
	freezer     *PodFreezer
}

func NewWebInvokeServer(c *WebserverOptions,
	logReporter *LogReporter, runtimeCtrl *LogStatManager,
	freezer *PodFreezer, rtMap *RuntimeMap) *WebInvokeServer {
	return &WebInvokeServer{
		config:      c,
		runtimeMap:  rtMap,
		logReporter: logReporter,
		freezer:     freezer,
		runtimeCtrl: runtimeCtrl,
	}
}

func (s *WebInvokeServer) checkRequest(c *server.Context) (reqinfo *RequestInfo) {
	metric, _ := c.Request().Attribute(InvokeMetricAttribute).(*invokeMetric)
	response := c.Response()
	prepareTraffic()
	metric.StepDone(StagePrepareTraffic)

	var input api.InvocationInput
	decoder := json.NewDecoder(c.HTTPRequest().Body)
	err := decoder.Decode(&input)
	if err != nil {
		c.WithErrorLog(kunErr.NewInvalidRequestContentException("decode request body failed", err)).WriteTo(response)
		return
	}
	c.Logger().V(5).Info("recv request.", zap.String("podname", input.PodName))
	metric.StepDone(StageReadEntity)
	applog.V(6).Infof("checkRequest input:%s", input.EventBody)

	// 设置默认值
	if input.Configuration.FunctionArn == nil {
		defaultFunctionArn := ""
		input.Configuration.FunctionArn = &defaultFunctionArn
	}
	if input.Configuration.Timeout == nil || *(input.Configuration.Timeout) == 0 {
		var dafaultTimeout int64 = 3
		input.Configuration.Timeout = &dafaultTimeout
	}

	runtimeID := input.PodName
	requestID := input.RequestID
	if runtimeID == "" || requestID == "" {
		c.WithErrorLog(kunErr.NewInvalidRequestContentException("missing PodName or RequestID", nil)).WriteTo(response)
		return
	}

	reqinfo = NewRequestInfo(requestID, runtimeID)
	reqinfo.Input = &input
	return
}

func (s *WebInvokeServer) waitRuntime(c *server.Context, reqinfo *RequestInfo) (runtime *RuntimeInfo, err error) {
	response := c.Response()
	runtimeID := reqinfo.RuntimeID
	input := reqinfo.Input
	runtime = s.runtimeMap.WaitRuntimeReady(runtimeID, s.config.WaitRuntimeAliveTimeout)
	if runtime == nil {
		err = errors.New("waitRuntime: runtime not exist")
		c.WithErrorLog(invokerErr.NewInvalidRuntimeException("runtime not exist", nil)).WriteTo(response)
		return
	}

	if input.Configuration.CommitID != nil &&
		*(input.Configuration.CommitID) != runtime.CommitID {
		err = errors.New("waitRuntime: commitid mismatch")
		c.WithErrorLog(invokerErr.NewInvalidRuntimeException("commitid mismatch", nil)).WriteTo(response)
		return
	}

	if input.Configuration.MemorySize != nil {
		reqinfo.MemorySpecSize = *(input.Configuration.MemorySize)
	} else {
		reqinfo.MemorySpecSize = 128
	}
	runtime.UserID = input.User.ID
	reqinfo.Credential = input.Credential
	return runtime, err
}

// invokeCleanup invokeCleanup 函数用于清理请求的资源，包括冻结运行时、上报日志等操作。
// 参数：
//
//	c *server.Context - Context对象指针，包含了HTTP请求相关信息和日志记录器。
//	reqinfo *RequestInfo - RequestInfo类型指针，包含了请求的所有信息，如输入、输出、配置等。
//	runtime *RuntimeInfo - RuntimeInfo类型指针，包含了运行时的信息，如运行时ID、请求ID等。
//
// 返回值：
//
//	无返回值
func (s *WebInvokeServer) invokeCleanup(c *server.Context, reqinfo *RequestInfo, runtime *RuntimeInfo) {
	// _, span := trace.SpanFromContext(c.Context()).Tracer().Start(c.Context(), "WebInvokeServer/invokeCleanup")
	// defer span.End()

	metric, _ := c.Request().Attribute(InvokeMetricAttribute).(*invokeMetric)
	runtime.InvokeDone(reqinfo, false)
	reqinfo.Output.LogMessage = nil
	// 自定义运行时不进行冻结,会有并发问题
	if s.freezer != nil && !strings.HasPrefix(*(reqinfo.Input.Configuration.Runtime), "custom.") {
		s.freezer.FreezeRuntime(reqinfo.RuntimeID, reqinfo.RequestID)
		metric.StepDone(StageFreezeRuntime)
	}
	if s.logReporter != nil {
		err := s.logReporter.Report(reqinfo, reqinfo.store.LogFile())
		if err != nil {
			applog.V(6).Info(err.Error())
			// span.SetStatus(codes.Internal, err.Error())
		}
		metric.StepDone(StageUserlogReport)
	}
	reqinfo.store = nil
	metric.ObserveMetric(c.Logger())
}

// invokeFunction 调用函数，处理请求，返回结果
// 参数c：*server.Context，包含http请求上下文信息
// 返回值无返回值
func (s *WebInvokeServer) invokeFunction(c *server.Context) {
	invokeBegin := time.Now()
	response := c.Response()
	metric := newInvokeMetric(c.HTTPRequest().Header.Get(api.HeaderXRequestID))
	c.Request().SetAttribute(InvokeMetricAttribute, metric)
	reqinfo := s.checkRequest(c)
	if reqinfo == nil {
		c.Logger().Errorf("[invokeFunction] check request error: reqinfo is nil")
		c.WithErrorLog(invokerErr.NewInvalidRuntimeException("check request error", nil)).WriteTo(response)
		return
	}
	metric.StepDone(StageCheckRequest)
	if s.freezer != nil {
		s.freezer.ThawRuntime(c.Context(), reqinfo.RuntimeID, reqinfo.RequestID)
		metric.StepDone(StageThawRuntime)
	}
	runtime, err := s.waitRuntime(c, reqinfo)
	if runtime == nil || err != nil {
		c.Logger().Errorf("response: %v", c.Response())
		c.WithErrorLog(invokerErr.NewInvalidRuntimeException("can't get function compute resource", err)).WriteTo(response)
		return
	}
	metric.StepDone(StageWaitRuntime)
	defer s.invokeCleanup(c, reqinfo, runtime)

	input := reqinfo.Input
	reqinfo.Output.DurationMap = metric.durationMap
	// 如果是自定义运行时函数
	if reqinfo.Input.Configuration.Runtime != nil &&
		strings.HasPrefix(*(reqinfo.Input.Configuration.Runtime), "custom.") {
		s.invokeCustomRuntimeFunction(c, reqinfo, runtime)
		return
	}
	if input.Configuration.Runtime != nil &&
		strings.HasSuffix(*(input.Configuration.Runtime), "stream") {
		s.invokeHttp(c, reqinfo, runtime)
		return
	}

	// sse额外解析请求，实时返回给client
	if input.Configuration.Sse {
		// 防止后面把eventBody清空了
		eventBody := reqinfo.Input.EventBody
		go s.sseInvokeHttp(c, eventBody, reqinfo, runtime)
	}

	// sc, span := trace.SpanFromContext(c.Context()).Tracer().Start(c.Context(), "WebInvokeServer/invokeFunc")
	// defer span.End()

	reqinfo.Status = api.StatusRuning
	functionTimeout := int(*(input.Configuration.Timeout))
	invokeInfo := &InvokeInfo{
		RequestID:       reqinfo.RequestID,
		Version:         *input.Configuration.Version,
		FunctionTimeout: functionTimeout,
		EventObject:     input.EventBody,
		ClientContext:   input.ClientContext,
		FunctionBrn:     *(input.Configuration.FunctionArn),
	}
	if invokeInfo.EventObject == "" {
		invokeInfo.EventObject = "{}"
	}

	if reqinfo.Credential != nil {
		invokeInfo.AccessKeyID = reqinfo.Credential.AccessKeyId
		invokeInfo.AccessKeySecret = reqinfo.Credential.AccessKeySecret
		invokeInfo.SecurityToken = reqinfo.Credential.SessionToken
	}
	logtype := ""
	if input.LogConfig != nil {
		logtype = input.LogConfig.LogType
	}
	store := s.runtimeCtrl.StartRecvLog(reqinfo.RuntimeID, reqinfo.RequestID, logtype)
	reqinfo.InvokeStart(store)

	applog.V(7).Infof("invokeFunction runtime: %s", *input.Configuration.Runtime)

	isSse := false
	// *只有 runtime 为 mcp 的才放开单实例多并发
	if strings.Contains(*(input.Configuration.Runtime), "mcp") {
		isSse = true
	}
	applog.V(7).Infof("invokeFunction isSse: %t", isSse)

	err = runtime.InvokeFunc(reqinfo, invokeInfo, isSse)
	metric.StepDone(StageInvokeFunc)
	reqinfo.CleanInput()
	invokeInfo = nil

	timeout := false
	response.PrettyPrint(false)
	if err != nil {
		// 增加并发度异常错误的函数错误标记
		reqinfo.InvokeResult(api.StatusConcurrencyExceed, api.InvokeConcurrencyExceed)

		errMsg := fmt.Sprintf("RequestId: %s invoke function error %s", reqinfo.RequestID, err.Error())
		m := kunErr.AwsErrorMessage{
			ErrorMessage: errMsg,
		}
		reqinfo.Output.FuncResult = m.String()
		reqinfo.WriteFuncErrorToCFCMsg(errMsg)
		reqinfo.InvokeDone(s.config.WaitUserlogDone)
		s.runtimeCtrl.StopRecvLog(reqinfo.RuntimeID, reqinfo.RequestID, reqinfo.store)
		response.WriteEntity(&(reqinfo.Output))
		// span.SetStatus(codes.Internal, "invoke function error")
		return
	}

	timer := time.NewTimer(time.Duration(functionTimeout) * time.Second)
	select {
	case <-reqinfo.SyncChannel:

	case <-timer.C:
		reqinfo.InvokeResult(api.StatusTimeout, api.InvokeTimeout)
		timeout = true
	}
	timer.Stop()
	metric.StepDone(StageInvokeDone)

	if timeout {
		errMsg := fmt.Sprintf("RequestId: %s Task timed out after %d seconds", reqinfo.RequestID, functionTimeout)
		m := kunErr.AwsErrorMessage{
			ErrorMessage: errMsg,
		}
		reqinfo.Output.FuncResult = m.String()
		reqinfo.WriteFuncErrorToCFCMsg(errMsg)
	}

	reqinfo.InvokeDone(s.config.WaitUserlogDone)
	addInvokeBillHeader(reqinfo, response)
	addStatisticHeader(reqinfo, response)
	addNetTrafficHeader(response)
	// sse的head在第一次返回时候，就已经确定，通过body回传计费信息
	if input.Configuration.Sse {
		reqinfo.Output.InvokeBillInfo = response.Header().Get(api.CfcInvokeBillHeader)
		reqinfo.Output.StatisticInfo = response.Header().Get(api.CfcStatisticHeader)
		reqinfo.Output.NetTrafficInfos = append(reqinfo.Output.NetTrafficInfos, response.Header()[api.CfcNetTrafficHeader]...)
	}
	s.runtimeCtrl.StopRecvLog(reqinfo.RuntimeID, reqinfo.RequestID, reqinfo.store)
	metric.StepDone(StageStopRecvLog)
	applog.V(7).Infof("reqinfo output:%v", reqinfo.Output)
	if !timeout {
		reqinfo.Output.DurationMap.Add("total_invoke", invokeBegin) // 计入总时间
		response.WriteEntity(&(reqinfo.Output))
	} else {
		response.WriteEntity(&(reqinfo.Output))
		// span.AddEvent(sc, "invoke timeout")
		// 超时后杀掉容器
		// s.runtimeMap.Delete(runtime.RuntimeID)
		// runtime.Close()
		// s.runtimeCtrl.KillRuntime(runtime.RuntimeID)
	}
	metric.StepDone(StageWriteEntity)
	reqinfo.CleanOutput()
}

// fork原调用逻辑，专门针对自定义运行时的函数
func (s *WebInvokeServer) invokeCustomRuntimeFunction(c *server.Context, reqinfo *RequestInfo, runtime *RuntimeInfo) {
	c.Logger().Infof("custom runtime function invoke start")
	response := c.Response()
	if reqinfo.Input.Configuration.Runtime == nil || !strings.HasPrefix(*reqinfo.Input.Configuration.Runtime, "custom.") {
		c.Logger().Errorf("[invokeCustomRuntimeFunction] invalid runtime")
		c.WithErrorLog(invokerErr.NewInvalidRuntimeException("invalid custom runtime", nil)).WriteTo(response)
		return
	}
	if reqinfo.Input.Configuration.CustomRuntimeConfig == nil || reqinfo.Input.Configuration.CustomRuntimeConfig.Port <= 0 {
		c.Logger().Errorf("[invokeCustomRuntimeHttp] port is not set, requestId: %s", reqinfo.RequestID)
		c.WithErrorLog(invokerErr.NewInvalidRuntimeException("custom runtime port is not set", nil)).WriteTo(response)
		return
	}

	reqinfo.Status = api.StatusRuning
	logtype := ""
	if reqinfo.Input.LogConfig != nil {
		logtype = reqinfo.Input.LogConfig.LogType
	}
	// todo 这里的日志会有并发问题，后续需要修复，目前存储的[runtimeId, logStore]，后续要加一层requestId [runtimeId, requestId, logStore]
	// todo 后需要将日志维度转换为requestId维度
	store := s.runtimeCtrl.StartRecvLog(reqinfo.RuntimeID, reqinfo.RequestID, logtype)
	reqinfo.InvokeStart(store)
	// 外部的defer invokeClean会负责清理
	runtime.SetCustomRuntimeFunctionInfo(reqinfo)
	// 非mcp自定义运行时如果是api请求 则默认调用 post /invoke
	// 如果是http请求(从httptrigger触发器过来的请求) 则正常执行http调用
	if !strings.Contains(reqinfo.Input.Configuration.LangRuntime, "mcp") {
		s.invokeCustomRuntimeHttp(c, reqinfo, runtime)
		return
	}
	if strings.Contains(reqinfo.Input.Configuration.LangRuntime, "mcp") {
		s.invokeCustomRuntimeSseMcp(c, reqinfo, runtime)
		return
	}
}

// invokeCustomRuntimeSseMcp 调用自定义运行时SSE MCP请求
// 参数c：*server.Context，上下文对象指针
// 参数reqinfo：*RequestInfo，请求信息对象指针
// 参数runtime：*RuntimeInfo，运行时信息对象指针
// 返回值类型void，无返回值
func (s *WebInvokeServer) invokeCustomRuntimeSseMcp(c *server.Context, reqinfo *RequestInfo, runtime *RuntimeInfo) {

	metric := c.Request().Attribute(InvokeMetricAttribute).(*invokeMetric)
	response := c.Response()
	defer func() {
		if r := recover(); r != nil {
			buf := make([]byte, 8192)
			buf = buf[:runtimeStack.Stack(buf, false)]
			applog.Errorf("Panic recovered in invoker sseInvokeHttp, %+v \n %s", r, buf)
			c.WithErrorLog(commonErr.NewServiceException("panic recovered in invoker sseInvokeHttp", nil)).WriteTo(response)
		}
	}()
	input := reqinfo.Input
	functionTimeout := int(*(input.Configuration.Timeout))

	port := input.Configuration.CustomRuntimeConfig.Port
	req := &rpc.ProxyRequest{}
	err := json.Unmarshal([]byte(input.EventBody), req)
	if err != nil {
		c.WithErrorLog(commonErr.NewInvalidRequestContentException("unmarshal failed.", err)).WriteTo(response)
		return
	}
	httpreq := ConvertProxyRequestToHttp(req, true)
	if httpreq == nil || httpreq.URL == nil {
		c.WithErrorLog(commonErr.NewInvalidRequestContentException("convert proxy request to http request failed.", err)).WriteTo(response)
		return
	}
	httpreq.URL.Host = fmt.Sprintf("%s:%d", runtime.IPv4Address, port)
	c.Logger().Infof("Invoker request Pod, url: %s", httpreq.URL.String())
	// 添加ClientContext header
	if len(input.ClientContext) > 0 {
		httpreq.Header.Set("X-Cfc-ClientContext",
			base64.StdEncoding.EncodeToString([]byte(input.ClientContext)))
	}
	// 添加AK/SK/Token header
	if reqinfo.Credential != nil {
		httpreq.Header.Set("X-Cfc-AccessKeyID", reqinfo.Credential.AccessKeyId)
		httpreq.Header.Set("X-Cfc-AccessKeySecret", reqinfo.Credential.AccessKeySecret)
		httpreq.Header.Set("X-Cfc-SecurityToken", reqinfo.Credential.SessionToken)
	}
	httpreq.Header.Set("Connection", "close")
	client := http.Client{
		Timeout: time.Duration(functionTimeout) * time.Second,
		CheckRedirect: func(req *http.Request, via []*http.Request) error {
			return http.ErrUseLastResponse
		}, // 禁止redirect
	}
	retryDeadLine := time.Now().Add(time.Duration(s.config.WaitRuntimeAliveTimeout) * time.Second)
	retryDuration := time.Duration(50) * time.Millisecond
	// 先默认设置成功
	for time.Now().Before(retryDeadLine) {
		c.Logger().Infof("[%s] Invoker request Pod", reqinfo.RequestID)
		httprsp, err := client.Do(httpreq)
		if err != nil {
			// 发生超时错误，等待响应头超时，即函数超时直接break
			if netErr, ok := err.(net.Error); ok && netErr.Timeout() {
				c.Logger().Infof("request timeout: %s", err.Error())
				// 超时
				reqinfo.SetTimeoutErrorInfo()
				break
			}
			// 其他错误，重试（例如连接失败), 这里通常失败的原因是连接建立不起来，所以尝试重试
			c.Logger().Infof("request failed: %s retry after %s", err.Error(), retryDuration.String())
			<-time.After(retryDuration)
			if retryDuration < time.Second {
				retryDuration += time.Duration(100) * time.Millisecond
			}
			// 先设置error信息，下一轮for循环可能重试成功，成功再修改状态，当超过retryDeadLine说明是真有错误
			reqinfo.SetNonTimeoutErrorInfo("connetion refused, please check port or command or server code")
			continue
		}
		reqinfo.Status = api.StatusSuccess
		c.Logger().Infof("[%s] Received response: %v", reqinfo.RequestID, httprsp)
		// 逐行解析 SSE 响应
		if httprsp != nil && httprsp.Body != nil {
			contentType := httprsp.Header.Get("Content-Type")
			applog.V(7).Infof("[%s] contentType: %s", reqinfo.RequestID, contentType)
			if strings.Contains(contentType, "text/event-stream") {
				// mcp的sse长连接请求
				c.Logger().Infof("[%s] SSE response", reqinfo.RequestID)
				response.Header().Add("Content-Type", "text/event-stream")
				response.Header().Add("Cache-Control", "no-cache")
				response.Header().Add("Connection", "keep-alive")
			}
			scanner := bufio.NewScanner(httprsp.Body)
			scanner.Split(split.CustomSplitFunc)
			// 因为client设置了超时时间，当client超时时，scanner.Scan()会返回false
			for scanner.Scan() {
				line := scanner.Text()
				// 解析事件
				if strings.HasPrefix(line, "data:") || strings.HasPrefix(line, "event:") ||
					strings.HasPrefix(line, "id:") || strings.HasPrefix(line, "retry:") {
					c.Logger().Infof("Received event data: %s", line)
					message := fmt.Sprintf("%s\n", line)
					response.Write([]byte(message))
					response.Flush()
				} else if line == "" {
					// 空行表示事件结束
					continue
				} else {
					c.Logger().Infof("Unhandled line: %s", line)
					// response.Write([]byte(line))
				}
			}
			timeoutErr := scanner.Err()
			if timeoutErr != nil {
				if netErr, ok := timeoutErr.(net.Error); ok && netErr.Timeout() {
					// 超时
					reqinfo.SetTimeoutErrorInfo()
				} else {
					// 否则失败
					reqinfo.SetNonTimeoutErrorInfo(timeoutErr.Error())
					c.Logger().Errorf("scanner error: %s", timeoutErr.Error())
				}
			}
			c.Logger().Infof("[%s] Invoker request Pod, close body", reqinfo.RequestID)
			httprsp.Body.Close()
		}
		break
	}
	c.Logger().Infof("invoke done")
	metric.StepDone(StageInvokeDone)
	reqinfo.InvokeDone(false)
	addInvokeBillHeader(reqinfo, response)
	addStatisticHeader(reqinfo, response)
	addNetTrafficHeader(response)
	// mcp的sse请求/messages请求都通过请求体返回计费信息
	reqinfo.Output.InvokeBillInfo = response.Header().Get(api.CfcInvokeBillHeader)
	reqinfo.Output.StatisticInfo = response.Header().Get(api.CfcStatisticHeader)
	reqinfo.Output.NetTrafficInfos = append(reqinfo.Output.NetTrafficInfos, response.Header()[api.CfcNetTrafficHeader]...)
	// todo 目前日志以runtime维度，并发会存在问题，需要后续修复, 这里会直接清理掉runtime维度的logStore
	s.runtimeCtrl.StopRecvLog(reqinfo.RuntimeID, reqinfo.RequestID, reqinfo.store)
	metric.StepDone(StageStopRecvLog)
	switch reqinfo.Status {
	case api.StatusTimeout:
		// 超时错误信息照常写入FuncResult
		errMsg := fmt.Sprintf("RequestId: %s Task timed out after %d seconds", reqinfo.RequestID, functionTimeout)
		m := kunErr.AwsErrorMessage{
			ErrorMessage: errMsg,
		}
		reqinfo.Output.FuncResult = m.String()
		reqinfo.WriteFuncErrorToCFCMsg(errMsg)
	case api.StatusFailed:
		// 非超时错误，则将错误信息写入ErrorInfo
		if reqinfo.Output.ErrorInfo == "" {
			reqinfo.SetNonTimeoutErrorInfo("UnknownError")
		}
		// 其他状态，无需设置信息，output在处理状态前已经设置好了
	case api.StatusSuccess:
		// 清空错误信息
		reqinfo.Output.FuncError = ""
		reqinfo.Output.ErrorInfo = ""
	default:
	}
	response.WriteEntity(&(reqinfo.Output))
	// if !timeout {
	// 	response.WriteEntity(&(reqinfo.Output))
	// } else {
	// 	errMsg := fmt.Sprintf("RequestId: %s Task timed out after %d seconds", reqinfo.RequestID, functionTimeout)
	// 	m := kunErr.AwsErrorMessage{
	// 		ErrorMessage: errMsg,
	// 	}
	// 	reqinfo.Output.FuncResult = m.String()
	// 	reqinfo.WriteFuncErrorToCFCMsg(errMsg)
	// 	response.WriteEntity(&(reqinfo.Output))
	// 	// span.AddEvent(sc, "invoke http timeout")
	// }
	metric.StepDone(StageWriteEntity)

}

// invokeCustomRuntimeHttp 根据请求信息和运行时信息调用自定义运行时的http服务，返回结果给客户端
// 参数c：*server.Context，上下文对象，包含请求和响应对象
// 参数reqinfo：*RequestInfo，请求信息，包含请求id、输入、输出等信息
// 参数runtime：*RuntimeInfo，运行时信息，包含ip地址、端口等信息
func (s *WebInvokeServer) invokeCustomRuntimeHttp(c *server.Context, reqinfo *RequestInfo, runtime *RuntimeInfo) {
	// 如果触发器类型为httptrigger，透传http请求
	// 如果是其他触发器类型，即api调用 则直接调用post /invoke透传请求体
	metric := c.Request().Attribute(InvokeMetricAttribute).(*invokeMetric)
	response := c.Response()
	input := reqinfo.Input
	functionTimeout := int(*(input.Configuration.Timeout))

	port := input.Configuration.CustomRuntimeConfig.Port
	var httpRequest *http.Request
	var err error
	// httptrigger发送过来的http请求
	if input.Trigger == api.TriggerTypeHTTP {
		req := &rpc.ProxyRequest{}
		err := json.Unmarshal([]byte(input.EventBody), req)
		if err != nil {
			c.WithErrorLog(commonErr.NewInvalidRequestContentException("unmarshal failed.", err)).WriteTo(response)
			return
		}
		httpRequest = ConvertProxyRequestToHttp(req, false)
		if httpRequest == nil || httpRequest.URL == nil {
			c.WithErrorLog(commonErr.NewInvalidRequestContentException("convert proxy request to http request failed.", err)).WriteTo(response)
			return
		}
		httpRequest.URL.Host = fmt.Sprintf("%s:%d", runtime.IPv4Address, port)
	} else {
		// api调用
		var url *url.URL
		url, err = url.Parse(fmt.Sprintf("http://%s:%d/invoke", runtime.IPv4Address, port))
		if err != nil {
			c.WithErrorLog(commonErr.NewInvalidRequestContentException("parse url failed.", err)).WriteTo(response)
			return
		}
		httpRequest, err = http.NewRequest(http.MethodPost, url.String(), strings.NewReader(input.EventBody))
		if err != nil {
			c.WithErrorLog(commonErr.NewInvalidRequestContentException("new request failed.", err)).WriteTo(response)
			return
		}
		httpRequest.Header.Set("X-Cfc-RequestID", reqinfo.RequestID)
		httpRequest.Header.Set("Content-Type", "application/octe-stream")
		if reqinfo.Credential != nil {
			httpRequest.Header.Set("X-Cfc-AccessKeyID", reqinfo.Credential.AccessKeyId)
			httpRequest.Header.Set("X-Cfc-AccessKeySecret", reqinfo.Credential.AccessKeySecret)
			httpRequest.Header.Set("X-Cfc-SecurityToken", reqinfo.Credential.SessionToken)
		}
	}
	client := &http.Client{
		Timeout: time.Duration(functionTimeout) * time.Second,
		CheckRedirect: func(req *http.Request, via []*http.Request) error {
			return http.ErrUseLastResponse
		}, // 禁止redirect
	}
	retryDeadLine := time.Now().Add(time.Duration(s.config.WaitRuntimeAliveTimeout) * time.Second)
	retryDuration := time.Duration(100) * time.Millisecond
	for time.Now().Before(retryDeadLine) {
		httprsp, err := client.Do(httpRequest)
		if err != nil {
			// 发生超时错误，等待响应头超时，即函数超时直接break
			if netErr, ok := err.(net.Error); ok && netErr.Timeout() {
				reqinfo.SetTimeoutErrorInfo()
				break
			}
			// 其他错误，重试（例如连接失败）
			c.Logger().Infof("request failed: %s retry after %s", err.Error(), retryDuration.String())
			<-time.After(retryDuration)
			if retryDuration < time.Second {
				retryDuration += time.Duration(100) * time.Millisecond
			}
			// 先设置error信息，下一轮for循环可能重试成功，成功再修改状态，当超过retryDeadLine说明是真有错误
			// 别直接设置err.Error()，因为err.Error()可能包含敏感信息
			reqinfo.SetNonTimeoutErrorInfo("connetion refused, please check port or command or server code")
			continue
		}
		reqinfo.Status = api.StatusSuccess
		var timeoutErr error
		if input.Trigger == api.TriggerTypeHTTP {
			rsp, err := ConvertHttpResponseToProxy(httprsp)
			// 忽略其他错误
			timeoutErr = err
			data, _ := json.Marshal(rsp)
			reqinfo.Output.FuncResult = string(data)
		} else {
			// api调用, 不支持请求/响应为二进制数据 二进制数据需要走http调用
			defer httprsp.Body.Close()
			buf := &bytes.Buffer{}
			_, err := buf.ReadFrom(httprsp.Body)
			// 忽略其他错误
			timeoutErr = err
			reqinfo.Output.FuncResult = buf.String()
		}
		// 可能响应体过大，导致client发送timeout
		if timeoutErr != nil {
			if netErr, ok := timeoutErr.(net.Error); ok && netErr.Timeout() {
				reqinfo.SetTimeoutErrorInfo()
			}
		}
		break
	}
	metric.StepDone(StageInvokeDone)
	reqinfo.InvokeDone(false)
	addInvokeBillHeader(reqinfo, response)
	addStatisticHeader(reqinfo, response)
	addNetTrafficHeader(response)
	// todo 目前日志以runtime维度，并发会存在问题，需要后续修复, 这里会直接清理掉runtime维度的logStore
	s.runtimeCtrl.StopRecvLog(reqinfo.RuntimeID, reqinfo.RequestID, reqinfo.store)
	metric.StepDone(StageStopRecvLog)
	switch reqinfo.Status {
	case api.StatusTimeout:
		errMsg := fmt.Sprintf("RequestId: %s Task timed out after %d seconds", reqinfo.RequestID, functionTimeout)
		m := kunErr.AwsErrorMessage{
			ErrorMessage: errMsg,
		}
		reqinfo.Output.FuncResult = m.String()
		reqinfo.WriteFuncErrorToCFCMsg(errMsg)
	case api.StatusFailed:
		if reqinfo.Output.ErrorInfo == "" {
			reqinfo.SetNonTimeoutErrorInfo("UnknownError")
		}
	case api.StatusSuccess:
		reqinfo.Output.FuncError = ""
		reqinfo.Output.ErrorInfo = ""
	default:
	}
	response.WriteEntity(&(reqinfo.Output))
	// if !timeout {
	// 	response.WriteEntity(&(reqinfo.Output))
	// } else {
	// 	errMsg := fmt.Sprintf("RequestId: %s Task timed out after %d seconds", reqinfo.RequestID, functionTimeout)
	// 	m := kunErr.AwsErrorMessage{
	// 		ErrorMessage: errMsg,
	// 	}
	// 	reqinfo.Output.FuncResult = m.String()
	// 	reqinfo.WriteFuncErrorToCFCMsg(errMsg)
	// 	response.WriteEntity(&(reqinfo.Output))
	// 	// span.AddEvent(sc, "invoke http timeout")
	// }
	metric.StepDone(StageWriteEntity)
}

// invokeHttp 调用HTTP接口，将请求转换为HTTP请求并发送到指定的地址。
// 如果请求超时或者发生错误，则会记录日志并返回相应的响应。
// 参数：
//   - c *server.Context：上下文对象，包含了请求和响应信息。
//   - reqinfo *RequestInfo：请求信息，包括输入、输出、状态等。
//   - runtime *RuntimeInfo：运行时信息，包括IP地址、最后访问时间等。
//
// 无返回值
func (s *WebInvokeServer) invokeHttp(c *server.Context, reqinfo *RequestInfo, runtime *RuntimeInfo) {
	// sc, span := trace.SpanFromContext(c.Context()).Tracer().Start(c.Context(), "WebInvokeServer/invokeHttp")
	// defer span.End()

	metric := c.Request().Attribute(InvokeMetricAttribute).(*invokeMetric)
	response := c.Response()
	input := reqinfo.Input
	req := &rpc.ProxyRequest{}
	err := json.Unmarshal([]byte(input.EventBody), req)
	if err != nil {
		c.WithErrorLog(commonErr.NewInvalidRequestContentException("unmarshal failed.", err)).WriteTo(response)
		// span.SetStatus(codes.InvalidArgument, err.Error())
		return
	}

	reqinfo.Status = api.StatusRuning
	functionTimeout := int(*(input.Configuration.Timeout))

	logtype := ""
	if input.LogConfig != nil {
		logtype = input.LogConfig.LogType
	}
	store := s.runtimeCtrl.StartRecvLog(reqinfo.RuntimeID, reqinfo.RequestID, logtype)
	reqinfo.InvokeStart(store)
	metric.StepDone(StageInvokeDone)

	httpreq := ConvertProxyRequestToHttp(req, false)
	httpreq.URL.Host = fmt.Sprintf("%s:%s", runtime.IPv4Address, invokeHttpPort)
	// 添加ClientContext header
	if len(input.ClientContext) > 0 {
		httpreq.Header.Set("X-Cfc-ClientContext",
			base64.StdEncoding.EncodeToString([]byte(input.ClientContext)))
	}
	// 添加AK/SK/Token header
	if reqinfo.Credential != nil {
		httpreq.Header.Set("X-Cfc-AccessKeyID", reqinfo.Credential.AccessKeyId)
		httpreq.Header.Set("X-Cfc-AccessKeySecret", reqinfo.Credential.AccessKeySecret)
		httpreq.Header.Set("X-Cfc-SecurityToken", reqinfo.Credential.SessionToken)
	}
	client := http.Client{
		Timeout: time.Duration(functionTimeout) * time.Second,
		CheckRedirect: func(req *http.Request, via []*http.Request) error {
			return http.ErrUseLastResponse
		}, // 禁止redirect
	}

	runtime.LastAccessTime = time.Now().UnixNano() / int64(time.Millisecond)
	timeout := true
	retryDeadLine := time.Now().Add(time.Duration(s.config.WaitRuntimeAliveTimeout) * time.Second)
	retryDuration := time.Duration(100) * time.Millisecond
	for time.Now().Before(retryDeadLine) {
		httprsp, err := client.Do(httpreq)
		if err != nil { // 重试发起请求
			c.Logger().Infof("request failed: %s retry after %s", err.Error(), retryDuration.String())
			<-time.After(retryDuration)
			if retryDuration < time.Second {
				retryDuration += time.Duration(100) * time.Millisecond
			}
			continue
		}
		timeout = false
		rsp, _ := ConvertHttpResponseToProxy(httprsp)
		data, _ := json.Marshal(rsp)
		reqinfo.Output.FuncResult = string(data)
		reqinfo.Status = api.StatusSuccess
		break
	}
	if timeout {
		reqinfo.Status = api.StatusFailed
	}

	reqinfo.InvokeDone(false)
	addInvokeBillHeader(reqinfo, response)
	addStatisticHeader(reqinfo, response)
	addNetTrafficHeader(response)
	s.runtimeCtrl.StopRecvLog(reqinfo.RuntimeID, reqinfo.RequestID, reqinfo.store)
	metric.StepDone(StageStopRecvLog)
	if !timeout {
		response.WriteEntity(&(reqinfo.Output))
	} else {
		errMsg := fmt.Sprintf("RequestId: %s Task timed out after %d seconds", reqinfo.RequestID, functionTimeout)
		m := kunErr.AwsErrorMessage{
			ErrorMessage: errMsg,
		}
		reqinfo.Output.FuncResult = m.String()
		reqinfo.WriteFuncErrorToCFCMsg(errMsg)
		response.WriteEntity(&(reqinfo.Output))
		// span.AddEvent(sc, "invoke http timeout")
	}
	metric.StepDone(StageWriteEntity)
}

func (s *WebInvokeServer) sseInvokeHttp(c *server.Context, eventBody string, reqinfo *RequestInfo, runtime *RuntimeInfo) {
	// 防止函数直接close，导致panic
	defer func() {
		if r := recover(); r != nil {
			buf := make([]byte, 8192)
			buf = buf[:runtimeStack.Stack(buf, false)]
			applog.Errorf("Panic recovered in invoker sseInvokeHttp, %+v \n %s", r, buf)
		}
	}()
	response := c.Response()
	input := reqinfo.Input
	req := &rpc.ProxyRequest{}
	// 去除转义
	eventBody = strings.ReplaceAll(eventBody, "\\n", "")
	eventBody = strings.ReplaceAll(eventBody, "\\r", "")
	err := json.Unmarshal([]byte(eventBody), req)
	// 如果解析出错，说明是event形式，默认请求根目录，Body直接使用传入的body
	if err != nil {
		applog.V(7).Infof("unmarshal failed:%v, eventBody:%s", err, eventBody)
		req.Body = eventBody
	}
	applog.V(7).Infof("eventBody:%s", eventBody)

	// 兼容下req解析为空的情况
	if eventBody != "" && req.Body == "" && req.Path == "" {
		req.Body = eventBody
	}

	reqinfo.Status = api.StatusRuning
	functionTimeout := int(*(input.Configuration.Timeout))

	httpreq := ConvertProxyRequestToHttp(req, true)
	httpreq.URL.Host = fmt.Sprintf("%s:%s", runtime.IPv4Address, invokeHttpPort)
	// 添加ClientContext header
	if len(input.ClientContext) > 0 {
		httpreq.Header.Set("X-Cfc-ClientContext",
			base64.StdEncoding.EncodeToString([]byte(input.ClientContext)))
	}
	// 添加AK/SK/Token header
	if reqinfo.Credential != nil {
		httpreq.Header.Set("X-Cfc-AccessKeyID", reqinfo.Credential.AccessKeyId)
		httpreq.Header.Set("X-Cfc-AccessKeySecret", reqinfo.Credential.AccessKeySecret)
		httpreq.Header.Set("X-Cfc-SecurityToken", reqinfo.Credential.SessionToken)
	}
	httpreq.Header.Set("Connection", "close")
	client := http.Client{
		Timeout: time.Duration(functionTimeout) * time.Second,
		CheckRedirect: func(req *http.Request, via []*http.Request) error {
			return http.ErrUseLastResponse
		}, // 禁止redirect
	}

	runtime.LastAccessTime = time.Now().UnixNano() / int64(time.Millisecond)
	retryDeadLine := time.Now().Add(time.Duration(s.config.WaitRuntimeAliveTimeout) * time.Second)
	retryDuration := time.Duration(50) * time.Millisecond
	for time.Now().Before(retryDeadLine) {
		applog.V(7).Infof("[%s] Invoker request Pod", reqinfo.RequestID)
		httprsp, err := client.Do(httpreq)
		applog.V(7).Infof("[%s] Received response: %v", reqinfo.RequestID, httprsp)
		// 逐行解析 SSE 响应
		if httprsp != nil && httprsp.Body != nil {
			contentType := httprsp.Header.Get("Content-Type")
			applog.V(7).Infof("[%s] contentType: %s", reqinfo.RequestID, contentType)
			if strings.Contains(contentType, "text/event-stream") {
				applog.V(7).Infof("[%s] SSE response", reqinfo.RequestID)
				response.Header().Add("Content-Type", "text/event-stream")
				response.Header().Add("Cache-Control", "no-cache")
				response.Header().Add("Connection", "keep-alive")
			}
			scanner := bufio.NewScanner(httprsp.Body)
			scanner.Split(split.CustomSplitFunc)
			for scanner.Scan() {
				if scanner.Err() != nil {
					applog.Errorf("scanner error: %v", scanner.Err())
					break
				}
				line := scanner.Text()
				// 解析事件
				if strings.HasPrefix(line, "data:") || strings.HasPrefix(line, "event:") ||
					strings.HasPrefix(line, "id:") || strings.HasPrefix(line, "retry:") {
					applog.V(7).Infof("Received event data: %s", line)
					message := fmt.Sprintf("%s\n", line)
					response.Write([]byte(message))
					response.Flush()
				} else if line == "" {
					// 空行表示事件结束
					continue
				} else {
					applog.V(7).Infof("Unhandled line: %s", line)
					// response.Write([]byte(line))
				}
			}
			applog.V(7).Infof("[%s] Invoker request Pod, close body", reqinfo.RequestID)
			httprsp.Body.Close()
		}
		if err != nil { // 重试发起请求
			c.Logger().Infof("request failed: %s retry after %s", err.Error(), retryDuration.String())
			<-time.After(retryDuration)
			if retryDuration < time.Second {
				retryDuration += time.Duration(50) * time.Millisecond
			}
			continue
		}
		break
	}
	applog.V(7).Infof("invoke done")
	// sse没有数据了，任务函数执行完了
	reqinfo.Notify("done")
}

func (s *WebInvokeServer) invokeHistory(request *restful.Request, response *restful.Response) {
	response.WriteErrorString(http.StatusNotImplemented, "not implemented")
}

func (s *WebInvokeServer) invokeDetails(request *restful.Request, response *restful.Response) {
	response.WriteErrorString(http.StatusNotImplemented, "not implemented")
}

func (s *WebInvokeServer) runtimeList(request *restful.Request, response *restful.Response) {
	rtList := s.runtimeMap.RuntimeList()
	response.WriteAsJson(rtList)
}

func (s *WebInvokeServer) runtimeInfo(request *restful.Request, response *restful.Response) {
	runtimeID := request.PathParameter("runtimeId")
	val, loaded := s.runtimeMap.Load(runtimeID)
	if !loaded {
		invokerErr.NewInvalidRuntimeException("not found", nil).WriteTo(response)
		return
	}
	rt, _ := val.(*RuntimeInfo)
	response.WriteAsJson(rt)
}

func (s *WebInvokeServer) installApis(container *restful.Container) {
	invokerApis := []endpoint.ApiSingle{
		{
			Verb:    "POST",
			Path:    "/invokeFunction",
			Handler: server.WrapRestRouteFuncWithTrace("handler/invokeFunction", s.invokeFunction),
		},
		{
			Verb:    "GET",
			Path:    "/invokeHistory",
			Handler: s.invokeHistory,
			Filters: []restful.FilterFunction{},
		},
		{
			Verb:    "GET",
			Path:    "/invokeDetails/{requestId}",
			Handler: s.invokeDetails,
			Filters: []restful.FilterFunction{},
		},
		{
			Verb:    "GET",
			Path:    "/runtimeList",
			Handler: s.runtimeList,
			Filters: []restful.FilterFunction{},
		},
		{
			Verb:    "GET",
			Path:    "/runtimeInfo/{runtimeId}",
			Handler: s.runtimeInfo,
			Filters: []restful.FilterFunction{},
		},
	}
	installer := endpoint.NewApiInstaller([]endpoint.ApiVersion{
		{
			Prefix: "/v1/invoker",
			Group:  invokerApis,
		},
	})
	installer.Install(container)
}

func (webIS *WebInvokeServer) Run(runOptions *InvokerOptions, stopCh <-chan struct{}) error {
	applog.V(4).Infof("invoker Version: %+v", version.Get())
	s, err := CreateServerChain(runOptions, webIS)
	if err != nil {
		return err
	}
	return s.PrepareRun().Run(stopCh)
}

func InvokerBuildHandlerChain(apiHandler http.Handler, c *server.Config) http.Handler {
	return genericfilters.WithRequestID(apiHandler)
}

func CreateServerChain(runOptions *InvokerOptions, webIS *WebInvokeServer) (*server.GenericServer, error) {
	config := server.NewRecommendedConfig()
	config.BuildHandlerChainFunc = InvokerBuildHandlerChain
	if err := runOptions.Webserver.RecommendedOptions.ApplyTo(config); err != nil {
		return nil, err
	}

	s, err := config.Complete().New(ApplicationName)
	if err != nil {
		return nil, err
	}

	// Install hook
	// s.AddPreShutdownHook("telemetryShutdownHook", config.TelemetryInfo.Stop)

	webIS.installApis(s.Handler.GoRestfulContainer)
	return s, nil
}
