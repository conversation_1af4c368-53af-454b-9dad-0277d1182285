package app

import (
	"strings"
	"sync"
	"time"

	"github.com/emicklei/go-restful"
	"github.com/google/uuid"

	"icode.baidu.com/baidu/faas/kun/pkg/api"
	"icode.baidu.com/baidu/faas/kun/pkg/funclet/client"
)

type trafficInfo struct {
	userid string

	deltaIn  int64 // 入口流量增量
	deltaOut int64 // 出口流量增量

	alive time.Time // 最后存活时间
	mutex sync.Mutex
}

type networkStat struct {
	InBytes  int64 `json:"inbytes"`
	OutBytes int64 `json:"outbytes"`
}

func (ti *trafficInfo) addstat(in, out int64, now time.Time) {
	ti.mutex.Lock()
	defer ti.mutex.Unlock()
	if in > 0 {
		ti.deltaIn += in
	}
	if out > 0 {
		ti.deltaOut += out
	}
	ti.alive = now
}

// 保留已经失联的container数据10分钟，空闲pod会自动回收
func (ti *trafficInfo) isAlive(now time.Time) bool {
	return now.Sub(ti.alive) < time.Duration(10*time.Minute)
}

func (ti *trafficInfo) commitStat() string {
	if ti.deltaIn <= 0 && ti.deltaOut <= 0 {
		return ""
	}
	ti.mutex.Lock()
	defer ti.mutex.Unlock()
	if ti.deltaIn <= 0 && ti.deltaOut <= 0 {
		return ""
	}
	info := &api.NetTrafficInfo{
		UserID:   ti.userid,
		InBytes:  ti.deltaIn,
		OutBytes: ti.deltaOut,
	}
	ti.deltaIn = 0
	ti.deltaOut = 0
	return info.Encode()
}

// 统计pod流量
type trafficCounter struct {
	runtimeMap *RuntimeMap
	trafficMap *sync.Map
	funcletCli client.FuncletInterface
	commitCh   chan int
	stopCh     chan int

	mutex  sync.Mutex // protect result
	length int
	result [][]string

	initiative bool
	running    bool
}

var _counter *trafficCounter

func StartTrafficCounter(runtimeMap *RuntimeMap, fclient client.FuncletInterface, serverAddress string) {
	if _counter != nil {
		return
	}
	_counter = &trafficCounter{
		runtimeMap: runtimeMap,
		trafficMap: &sync.Map{},
		funcletCli: fclient,
		commitCh:   make(chan int, 100),
		stopCh:     make(chan int, 2),
		running:    true,
	}
	if strings.Index(serverAddress, "vsock") == -1 {
		_counter.initiative = true
	}
	go _counter.collect()
}

func StopTrafficCounter() {
	if _counter == nil {
		return
	}
	_counter.stop()
	_counter = nil
}

func prepareTraffic() {
	if _counter == nil {
		return
	}
	_counter.commit()
}

func addNetTrafficHeader(response *restful.Response) {
	if _counter == nil {
		return
	}
	// 此处不考虑commit是否执行完成，正常情况下fetch会先于invoke完成，同时，commit产生的数据远小于调用数
	headers := _counter.fetch()
	if len(headers) > 0 {
		for _, h := range headers {
			response.AddHeader(api.CfcNetTrafficHeader, h)
		}
	}
}

// runc集群访问funclet获取流量信息，kata集群通过dispatcherV2接口接收流量信息
func (tc *trafficCounter) collect() {
	if tc.initiative {
		go tc.loopCollect()
	}

	for tc.running {
		select {
		case <-tc.commitCh:
			tc.doCommit()
		case <-tc.stopCh:
			tc.running = false
		}
	}
}

func (tc *trafficCounter) loopCollect() {
	timer := time.NewTicker(1 * time.Minute)
	for tc.running {
		select {
		case <-timer.C:
			go tc.doCollect() // 有点耗时，启动goroutine查询
		}
	}
	timer.Stop()
}

func (tc *trafficCounter) doCommit() {
	var result []string
	count := 0
	tc.trafficMap.Range(func(key, val interface{}) bool {
		info := val.(*trafficInfo)
		message := info.commitStat()
		if len(message) > 0 {
			applog.V(7).Infof("nettraffic: %s", message)
			result = append(result, message)
			count++
		}
		return count < 6
	})
	if len(result) == 0 {
		return
	}

	tc.mutex.Lock()
	defer tc.mutex.Unlock()
	tc.result = append(tc.result, result)
	tc.length = len(tc.result)
}

func (tc *trafficCounter) fetch() []string {
	if tc.length <= 0 {
		return nil
	}
	tc.mutex.Lock()
	defer tc.mutex.Unlock()
	if tc.length <= 0 {
		return nil
	}
	head := tc.result[0]
	tc.result = tc.result[1:]
	tc.length = len(tc.result)
	return head
}

func (tc *trafficCounter) commit() {
	tc.commitCh <- 1
}

func (tc *trafficCounter) stop() {
	tc.stopCh <- 1
}

func (tc *trafficCounter) doCollect() {
	now := time.Now()
	runtimes := tc.runtimeMap.RuntimeList()
	for _, r := range runtimes {
		if r.UserID == "" { // 还没有接过请求的pod，一定没有流量数据
			applog.V(8).Infof("nettraffic %s skip", r.RuntimeID)
			continue
		}

		requestID := uuid.New().String()
		stats, err := tc.funcletCli.NetTraffic(&api.FuncletClientNetTrafficInput{PodName: r.RuntimeID, Reset: true, RequestID: requestID})
		if err != nil {
			applog.V(4).Warnf("get %s nettraffic failed, error: %s", r.RuntimeID, err.Error())
			continue
		}
		// 无流量，不用处理
		if stats.INBytes <= 0 && stats.OUTBytes <= 0 {
			continue
		}
		tc.storeStats(stats.INBytes, stats.OUTBytes, r.UserID, r.RuntimeID, now)
	}

	tc.clearMap(now)
}

func (tc *trafficCounter) storeStats(inBytes, outBytes int64, userID, runtimeID string, now time.Time) {
	applog.V(7).Infof("nettraffic %s userid: %s inbytes:%d outbytes:%d",
		runtimeID, userID, inBytes, outBytes)

	info, ok := tc.trafficMap.Load(userID)
	var traffic *trafficInfo
	if !ok { // 新增的pod
		traffic = &trafficInfo{
			userid:   userID,
			deltaIn:  inBytes,
			deltaOut: outBytes,
			alive:    now,
		}
		tc.trafficMap.Store(userID, traffic)
	} else {
		traffic, _ = info.(*trafficInfo)
		traffic.addstat(inBytes, outBytes, now)
	}
}

// 清理长时间不存在的pod流量信息
func (tc *trafficCounter) clearMap(now time.Time) {
	dead := []interface{}{}
	tc.trafficMap.Range(func(key, val interface{}) bool {
		info := val.(*trafficInfo)
		if !info.isAlive(now) {
			dead = append(dead, key)
		}
		return true
	})

	for _, key := range dead {
		applog.V(4).Infof("nettraffic delete %s", key)
		tc.trafficMap.Delete(key)
	}
}
