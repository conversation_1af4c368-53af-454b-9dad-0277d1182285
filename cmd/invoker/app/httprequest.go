package app

import (
	"bytes"
	"encoding/base64"
	"fmt"
	"io"
	"io/ioutil"
	"mime"
	"net/http"
	"net/url"

	"icode.baidu.com/baidu/faas/kun/pkg/httptrigger/rpc"
	"icode.baidu.com/baidu/faas/kun/pkg/util/json"
)

func ConvertProxyRequestToHttp(req *rpc.ProxyRequest, sse bool) *http.Request {
	var body io.Reader
	body = bytes.NewBufferString(req.Body)
	if req.IsBase64Encoded {
		body = base64.NewDecoder(base64.StdEncoding, body)
	}
	// 判断 req.Path 是否以/开头，如果是，则去掉
	path := req.Path
	if len(req.Path) > 0 && req.Path[0] == '/' {
		path = req.Path[1:]
	}
	requrl, _ := url.Parse(fmt.Sprintf("http://127.0.0.1:%s/%s", invokeHttpPort, path))
	q := requrl.Query()
	for k, v := range req.QueryStringParameters {
		q.Set(k, v)
	}
	requrl.RawQuery = q.Encode()
	request, _ := http.NewRequest(req.HttpMethod, requrl.String(), body)
	for k, v := range req.Headers {
		request.Header.Set(k, v)
	}

	// 添加PathParameters
	if len(req.PathParameters) > 0 {
		data, _ := json.Marshal(req.PathParameters)
		request.Header.Set("X-Cfc-PathParameters",
			base64.StdEncoding.EncodeToString(data))
	}

	// 添加RequestContext信息
	request.Header.Set("X-Cfc-RequestId", req.RequestContext.RequestId)
	request.Header.Set("X-Cfc-ClientIP", req.RequestContext.SourceIp)
	request.Header.Set("X-Cfc-ApiID", req.RequestContext.ApiID)
	if sse {
		request.Header.Add("Content-Type", "application/json")
		request.Header.Add("Content-Length", fmt.Sprintf("%d", len(req.Body)))
	}
	return request
}

// ConvertHttpResponseToProxy 将一个 http.Response 转换为 rpc.ProxyResponse，并返回。如果 rsp 是 nil，则会返回一个错误信息。
// 参数：
//   - rsp (http.Response): 需要被转换的 http.Response 对象。如果为 nil，则会返回一个错误信息。
//
// 返回值：
//   - (rpc.ProxyResponse, error): 转换后的 rpc.ProxyResponse 对象和一个可能出现的错误信息。
func ConvertHttpResponseToProxy(rsp *http.Response) (*rpc.ProxyResponse, error) {
	if rsp == nil {
		return nil, fmt.Errorf("response is nil")
	}
	response := &rpc.ProxyResponse{}
	response.StatusCode = rsp.StatusCode
	response.Headers = make(map[string]string)

	for k, v := range rsp.Header {
		if len(v) > 0 {
			response.Headers[k] = v[0]
		}
	}

	contentType := rsp.Header.Get("Content-Type")
	if contentType == "" {
		response.IsBase64Encoded = true
	} else {
		mediaType, _, _ := mime.ParseMediaType(contentType)
		switch mediaType {
		case "application/json", "text/xml", "text/html":
			response.IsBase64Encoded = false
		default:
			response.IsBase64Encoded = true
		}
	}
	var body []byte
	var err error
	if rsp.Body != nil {
		body, err = ioutil.ReadAll(rsp.Body)
		rsp.Body.Close()
	} else {
		body = []byte{}
	}
	if err == nil {
		if response.IsBase64Encoded {
			response.Body = base64.StdEncoding.EncodeToString(body)
		} else {
			response.Body = string(body)
		}
	}
	//
	return response, err
}
