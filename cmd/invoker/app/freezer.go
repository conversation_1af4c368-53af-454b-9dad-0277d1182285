package app

import (
	"fmt"
	"time"

	goevents "github.com/docker/go-events"
	//"go.opentelemetry.io/otel/api/trace"
	"go.uber.org/zap"
	"golang.org/x/net/context"

	"icode.baidu.com/baidu/faas/kun/pkg/api"
	"icode.baidu.com/baidu/faas/kun/pkg/funclet/client"
)

type freezeInfo struct {
	RuntimeID string
	RequestID string
	Deadline  *time.Time
}

type PodFreezer struct {
	config     *FreezerOptions
	funcletCli client.FuncletInterface
	writer     goevents.Sink
	thawMap    map[string]bool        // 已解冻
	waitMap    map[string]*freezeInfo // 等待冻结
	timer      *time.Timer
	closed     bool
}

func NewPodFreezer(config *FreezerOptions, funcletCli client.FuncletInterface) *PodFreezer {
	freezer := &PodFreezer{
		config:     config,
		funcletCli: funcletCli,
		waitMap:    make(map[string]*freezeInfo),
		thawMap:    make(map[string]bool),
		closed:     false,
	}
	writer := goevents.NewQueue(freezer)
	freezer.writer = writer
	return freezer
}

func (f *PodFreezer) checkTimeout() {
	f.timer = nil
	now := time.Now()
	timeout := []*freezeInfo{}
	for _, info := range f.waitMap {
		if info.Deadline.Before(now) { // 超时了，冻结
			timeout = append(timeout, info)
		}
	}
	for _, info := range timeout {
		f.doFreeze(info)
	}
	f.startTimer()
}

// 1秒钟后，再从 waitMap 里拿出 runtime info，同步执行冻结
func (f *PodFreezer) startTimer() {
	sink := f.writer
	if f.timer == nil && len(f.waitMap) > 0 {
		f.timer = time.AfterFunc(1*time.Second, func() {
			sink.Write(&freezeInfo{}) // 写一个空的事件代表超时
		})
	}
}

// 冻结
func (f *PodFreezer) doFreeze(info *freezeInfo) {
	applog.V(5).Info(fmt.Sprintf("freeze runtime: %s", info.RuntimeID), zap.String("request_id", info.RequestID))
	f.funcletCli.FreezeContainer(&api.FuncletClientFreezeContainerInput{
		Host:      "localhost",
		PodName:   info.RuntimeID,
		State:     api.Frozen,
		RequestID: info.RequestID,
	})
	delete(f.waitMap, info.RuntimeID)
	delete(f.thawMap, info.RuntimeID)
}

// 解冻
func (f *PodFreezer) doThaw(info *freezeInfo) {
	if _, ok := f.thawMap[info.RuntimeID]; !ok { // 当前已经解冻，不再重复解冻
		applog.V(5).Info(fmt.Sprintf("thaw runtime: %s", info.RuntimeID), zap.String("request_id", info.RequestID))
		f.funcletCli.FreezeContainer(&api.FuncletClientFreezeContainerInput{
			Host:      "localhost",
			PodName:   info.RuntimeID,
			State:     api.Thawed,
			RequestID: info.RequestID,
		})
	} else {
		applog.V(5).Info(fmt.Sprintf("skip thaw runtime: %s", info.RuntimeID), zap.String("request_id", info.RequestID))
	}
	f.thawMap[info.RuntimeID] = true
	old, ok := f.waitMap[info.RuntimeID]
	if ok {
		applog.V(5).Info(fmt.Sprintf("skip freeze runtime: %s", old.RuntimeID), zap.String("request_id", old.RequestID))
	}
	delete(f.waitMap, info.RuntimeID)
}

func (f *PodFreezer) Write(event goevents.Event) error {
	info, _ := event.(*freezeInfo)
	if len(info.RuntimeID) > 0 {
		if info.Deadline != nil { // 冻结
			f.waitMap[info.RuntimeID] = info
			f.startTimer()
		} else { // 解冻
			f.doThaw(info)
		}
	} else { // 超时事件
		f.checkTimeout()
	}
	return nil
}

func (f *PodFreezer) Close() error {
	f.closed = true
	return nil
}

func (f *PodFreezer) FreezeRuntime(runtimeID, requestID string) {
	if !f.config.EnableFreezeContainer || f.closed {
		return
	}
	deadLine := time.Now().Add(time.Duration(f.config.DelayFreezeTimeout) * time.Second)
	info := &freezeInfo{
		RuntimeID: runtimeID,
		RequestID: requestID,
		Deadline:  &deadLine,
	}
	f.writer.Write(info)
}

func (f *PodFreezer) ThawRuntime(ctx context.Context, runtimeID, requestID string) {
	//_, span := trace.SpanFromContext(ctx).Tracer().Start(ctx, "WebInvoker/thrawRuntime")
	//defer span.End()
	if !f.config.EnableFreezeContainer || f.closed {
		return
	}
	info := &freezeInfo{
		RuntimeID: runtimeID,
		RequestID: requestID,
	}
	f.writer.Write(info)
}
