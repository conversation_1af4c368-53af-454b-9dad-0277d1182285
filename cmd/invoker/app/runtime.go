package app

import (
	"errors"
	"fmt"
	"net"
	"net/http"
	"net/url"
	"strconv"
	"sync"
	"time"

	"go.uber.org/zap"

	"icode.baidu.com/baidu/faas/kun/pkg/api"
	"icode.baidu.com/baidu/faas/kun/pkg/invoker/fastsocket"
	"icode.baidu.com/baidu/faas/kun/pkg/util/json"
)

type RuntimeMap struct {
	sync.Map

	waitMap sync.Map
}

func NewRuntimeMap() *RuntimeMap {
	return &RuntimeMap{}
}

func (m *RuntimeMap) NewRuntime(runtimeID string) *RuntimeInfo {
	r := NewRuntimeInfo(runtimeID)
	_, loaded := m.LoadOrStore(runtimeID, r)
	if loaded {
		return nil
	}
	return r
}

func (m *RuntimeMap) FindRuntime(runtimeID string) *RuntimeInfo {
	val, loaded := m.Load(runtimeID)
	if !loaded {
		return nil
	}
	rt, _ := val.(*RuntimeInfo)
	if rt.ConnStatus() != ESTABLISH {
		return nil
	}
	return rt
}

func (m *RuntimeMap) RuntimeList() []*RuntimeInfo {
	rtlist := []*RuntimeInfo{}
	m.Range(func(key, val interface{}) bool {
		rt, _ := val.(*RuntimeInfo)
		rtlist = append(rtlist, rt)
		return true
	})
	return rtlist
}

func (m *RuntimeMap) NotifyRuntimeReady(runtimeID string) {
	waitChan, exist := m.waitMap.Load(runtimeID)
	applog.V(5).Infof("NotifyRuntimeReady %s exist=%v", runtimeID, exist)

	if exist {
		m.waitMap.Delete(runtimeID)
		close(waitChan.(chan bool))
	}
}

func (m *RuntimeMap) WaitRuntimeReady(runtimeID string, timeout int) *RuntimeInfo {
	rt := m.FindRuntime(runtimeID)
	if rt != nil {
		return rt
	}

	c := make(chan bool)
	waitChan, load := m.waitMap.LoadOrStore(runtimeID, c)
	if !load {
		waitChan = c
	} else {
		close(c)
	}

	timer := time.NewTimer(time.Duration(timeout) * time.Second)
	select {
	case <-waitChan.(chan bool):
		rt = m.FindRuntime(runtimeID)
	case <-timer.C:
		m.waitMap.Delete(runtimeID)
		// TODO fix:多并发下 timeout 周期内 wait 的请求都会失败;
	}
	timer.Stop()

	return rt
}

func (m *RuntimeMap) DelRuntime(runtimeID string) {
	m.Delete(runtimeID)
}

const (
	CONNECTED = 1
	UPGRADED  = 2
	ESTABLISH = 3
	CLOSED    = 4

	RUNNING = 1
	IDLE    = 2
	FREEZE  = 3
)

type InvokeInfo struct {
	RequestID       string
	Version         string
	AccessKeyID     string
	AccessKeySecret string
	SecurityToken   string
	FunctionBrn     string
	FunctionTimeout int
	ClientContext   string `json:",omitempty"`
	EventObject     string `json:",omitempty"`
}

type InvokeRequest struct {
	RequestID       string `json:"requestid"`
	Version         string `json:"version"`
	AccessKeyID     string `json:"accessKey"`
	AccessKeySecret string `json:"secretKey"`
	SecurityToken   string `json:"securityToken"`
	ClientContext   string `json:"clientContext,omitempty"`
	EventObject     string `json:"eventObject,omitempty"`
}

type InvokeResponse struct {
	RequestID  string `json:"requestid"`
	Success    bool   `json:"success"`
	FuncResult string `json:"result,omitempty"`
	FuncError  string `json:"error,omitempty"`
}

type RuntimeInfo struct {
	api.RuntimeInfo

	// 换成 RWLock 会不会好点? requestMap 读比写要频繁得多
	invokeLock sync.Mutex

	clientConn  *fastsocket.Conn
	v2cliConn   net.Conn
	connStatus  int
	IPv4Address string

	requestMap map[string]*RequestInfo
}

func NewRuntimeInfo(runtimeID string) *RuntimeInfo {
	return &RuntimeInfo{
		RuntimeInfo: api.RuntimeInfo{
			RuntimeID:      runtimeID,
			LastAccessTime: time.Now().UnixNano() / int64(time.Millisecond),
			ConcurrentMode: false,
		},
		connStatus: CLOSED,
		requestMap: make(map[string]*RequestInfo),
	}
}

func (info *RuntimeInfo) ConnStatus() int {
	return info.connStatus
}

func (info *RuntimeInfo) SetLoadTime(pre, post int64) {
	info.PreLoadTimeMS = pre
	info.PostLoadTimeMS = post
}

func (info *RuntimeInfo) SetInitTime(pre, post int64) {
	info.PreInitTimeMS = pre
	info.PostInitTimeMS = post
}

func (info *RuntimeInfo) SetVersion(version string) {
	info.CommitID = version
}

func (info *RuntimeInfo) ConcurrentMode(isSse bool) bool {
	if isSse {
		return true
	}
	return info.RuntimeInfo.ConcurrentMode
}

func (info *RuntimeInfo) Upgrade(w http.ResponseWriter, r *http.Request) bool {
	info.connStatus = CONNECTED
	upgrader := fastsocket.Upgrader{
		EnableCompression: false,
	}
	c, err := upgrader.Upgrade(w, r, nil)
	if err != nil {
		applog.V(3).Errorf("upgrade failed, %s", err.Error())
		return false
	}
	info.clientConn = c
	info.connStatus = UPGRADED
	return true
}

func (info *RuntimeInfo) InvokeFunc(request *RequestInfo, invokeInfo *InvokeInfo, isSse bool) error {
	info.invokeLock.Lock()
	defer info.invokeLock.Unlock()

	// info.requestMap 账户级别的最大并发数，podConcurrencyQuota
	if !info.ConcurrentMode(isSse) && len(info.requestMap) > 0 {
		var keys string

		for k := range info.requestMap {
			keys += k + ""
		}

		applog.V(9).Errorf("requestMap size is %d, keys: %s ", len(info.requestMap), keys)

		return errors.New("too many concurrency request")
	}

	info.requestMap[request.RequestID] = request
	info.LastAccessTime = time.Now().UnixNano() / int64(time.Millisecond)
	if info.v2cliConn != nil {
		invokeReq := &InvokeRequest{
			RequestID:       invokeInfo.RequestID,
			Version:         invokeInfo.Version,
			AccessKeyID:     invokeInfo.AccessKeyID,
			AccessKeySecret: invokeInfo.AccessKeySecret,
			SecurityToken:   invokeInfo.SecurityToken,
			ClientContext:   invokeInfo.ClientContext,
			EventObject:     invokeInfo.EventObject,
		}
		encoder := json.NewEncoder(info.v2cliConn)
		err := encoder.Encode(invokeReq)
		if err != nil {
			return fmt.Errorf("marshal invokeReq failed. %s", err.Error())
		}
	} else {
		return errors.New("runtime connection closed")
	}

	if !info.ConcurrentMode(isSse) {
		info.RequestID = request.RequestID
	} else {
		info.RequestID = "concurrency"
	}

	return nil
}

// 专门针对自定义运行时函数在runtime信息中设置信息
func (info *RuntimeInfo) SetCustomRuntimeFunctionInfo(request *RequestInfo) {
	info.invokeLock.Lock()
	defer info.invokeLock.Unlock()
	info.requestMap[request.RequestID] = request
	info.LastAccessTime = time.Now().UnixNano() / int64(time.Millisecond)
	// 自定义运行时函数并发执行---这个字段会影响cron的pod回收时间，必须要加
	info.RequestID = "concurrent"

}

func (info *RuntimeInfo) InvokeDone(request *RequestInfo, signal bool) {
	info.invokeLock.Lock()
	defer info.invokeLock.Unlock()

	if _, load := info.requestMap[request.RequestID]; !load {
		return
	}

	if signal {
		request.Notify("done")
	}
	if request.Status == api.StatusSuccess {
		info.AcceptReqCnt++
	} else if request.Status == api.StatusFailed {
		info.RejectReqCnt++
	}

	delete(info.requestMap, request.RequestID)
	applog.V(9).Infof("runtime[%s] concurrency is %d", info.RuntimeID, len(info.requestMap))
	if len(info.requestMap) == 0 {
		info.RequestID = ""
	}
}

func (info *RuntimeInfo) IsContainRequest(requestID string) bool {
	if requestID != "" {
		if request := info.loadRequest(requestID); request != nil {
			return true
		}
	}
	return false
}

func (info *RuntimeInfo) handleRuntimeInit(params *url.Values, data []byte) bool {
	if info.connStatus == ESTABLISH {
		applog.V(3).Errorf("duplicate /runtimeinit message")
		return false
	}
	preInit, _ := strconv.ParseInt(params.Get("initstart"), 10, 64)
	postInit, _ := strconv.ParseInt(params.Get("initdone"), 10, 64)

	// runtime init 时固定并发模式
	info.RuntimeInfo.ConcurrentMode = invokerEnableConcurrent && params.Get("concurrentmode") == "true"
	applog.V(9).Infof("runtime[%s] concurrentMode is %t", info.RuntimeID, info.ConcurrentMode(false))

	info.SetInitTime(preInit, postInit)
	info.connStatus = ESTABLISH
	return true
}

func (info *RuntimeInfo) loadRequest(requestID string) *RequestInfo {
	info.invokeLock.Lock()
	defer info.invokeLock.Unlock()

	if requset, load := info.requestMap[requestID]; load {
		return requset
	}
	applog.V(3).Error(fmt.Sprintf("request not found in %s runtimeMap", info.RuntimeID),
		zap.String("request_id", requestID))
	return nil
}

func (info *RuntimeInfo) handleRequestInit(requestID string, params *url.Values, data []byte) bool {
	if request := info.loadRequest(requestID); request != nil {
		preInit, _ := strconv.ParseInt(params.Get("initstart"), 10, 64)
		postInit, _ := strconv.ParseInt(params.Get("initdone"), 10, 64)
		request.SetInitTime(preInit, postInit)
		return true
	}
	return false
}

func (info *RuntimeInfo) handleInvokeDone(requestID string, params *url.Values, data string) bool {
	if request := info.loadRequest(requestID); request != nil {
		// TODO: 并发下的 log 收集
		status := api.StatusFailed
		if params.Get("success") == "true" {
			status = api.StatusSuccess
		}
		request.InvokeResult(status, data)
		info.InvokeDone(request, true)
		return true
	}

	return false
}

func (info *RuntimeInfo) Close() {
	info.invokeLock.Lock()
	defer info.invokeLock.Unlock()

	if info.clientConn != nil {
		info.clientConn.Close()
		info.clientConn = nil
	}

	for id, request := range info.requestMap {
		errMsg := fmt.Sprintf("RequestId: %s Process exited before completing request", id)
		request.WriteFuncErrorToCFCMsg(errMsg)
		request.InvokeResult(api.StatusFailed, errMsg)
		request.Notify("done")
	}

	info.RequestID = ""
	info.PreLoadTimeMS = 0
	info.PostLoadTimeMS = 0
	info.PreInitTimeMS = 0
	info.PostInitTimeMS = 0
	info.AcceptReqCnt = 0
	info.RejectReqCnt = 0
	info.connStatus = CLOSED
}
