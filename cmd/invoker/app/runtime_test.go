package app

import (
	"net/url"
	"testing"
)

func TestRuntimeMap(t *testing.T) {
	rtMap := NewRuntimeMap()
	rt := rtMap.NewRuntime("aa")
	if rt == nil {
		t.Error("create runtime aa failed.")
	}
	rt2 := rtMap.NewRuntime("aa")
	if rt2 != nil {
		t.<PERSON>rror("create runtime with same id")
	}

	rt = rtMap.FindRuntime("aa")
	if rt != nil {
		t.<PERSON>rf("invalid runtime with status %d", rt.ConnStatus())
	}
	rtlist := rtMap.RuntimeList()
	if len(rtlist) == 0 {
		t.<PERSON>rror("runtime list error")
	}
	rtMap.DelRuntime("aa")
}

func TestRuntimeInfo(t *testing.T) {
	rtInfo := NewRuntimeInfo("aa")
	if rtInfo.RuntimeID != "aa" || rtInfo.ConnStatus() != CLOSED {
		t.<PERSON>rf("runtime mismatch id = %s, status = %d", rtInfo.RuntimeID, rtInfo.ConnStatus())
	}
	rtInfo.SetInitTime(1, 2)
	rtInfo.SetLoadTime(3, 4)
	if rtInfo.PreInitTimeMS != 1 ||
		rtInfo.PostInitTimeMS != 2 ||
		rtInfo.PreLoadTimeMS != 3 ||
		rtInfo.PostLoadTimeMS != 4 {
		t.Errorf("runtime time mismatch. init[%d-%d], load[%d-%d]",
			rtInfo.PreInitTimeMS, rtInfo.PostInitTimeMS,
			rtInfo.PreLoadTimeMS, rtInfo.PostLoadTimeMS)
	}
}

func TestHandleRuntimeInit(t *testing.T) {
	rt := NewRuntimeInfo("aa")
	params := &url.Values{}
	params.Set("initstart", "10")
	params.Set("initdone", "20")
	ret := rt.handleRuntimeInit(params, nil)
	if !ret ||
		rt.PreInitTimeMS != 10 ||
		rt.PostInitTimeMS != 20 ||
		rt.ConnStatus() != ESTABLISH {
		t.Errorf("handle runtime init failed. %+v", rt)
	}
}

func TestHandleRequestInit(t *testing.T) {
	rt := NewRuntimeInfo("aa")
	req := NewRequestInfo("bb", "aa")
	rt.requestMap["bb"] = req
	params := &url.Values{}
	params.Set("initstart", "10")
	params.Set("initdone", "20")
	ret := rt.handleRequestInit("bb", params, nil)
	if !ret ||
		req.InitStartTimeMS != 10 ||
		req.InitDoneTimeMS != 20 {
		t.Errorf("handle request init failed. %+v", req)
	}
}

func TestHandleLogMessage(t *testing.T) {
	rt := NewRuntimeInfo("aa")
	req := NewRequestInfo("bb", "aa")
	rt.requestMap["bb"] = req
	params := &url.Values{}
	params.Set("initstart", "10")
	params.Set("initdone", "20")
	ret := rt.handleRequestInit("bb", params, []byte("test 123"))
	if !ret {
		t.Errorf("handle log message failed. %+v", req)
	}
	t.Log(req.Output)
}

func TestHandleInvokeDone(t *testing.T) {
	rt := NewRuntimeInfo("aa")
	req := NewRequestInfo("bb", "aa")

	//runnerinfo:=NewRunnerInfo("aaa")
	rt.requestMap["bb"] = req
	params := &url.Values{}
	params.Set("maxmemused", "102400")
	params.Set("success", "true")
	//runnerinfo.maxMemory=5
	ret := rt.handleInvokeDone("bb", params, "result 123")
	if !ret {
		t.Errorf("handle invoke done failed. %+v", req)
	}
}

// TestRuntimeInfo_SetVersion_IsContainRequest 测试函数RuntimeInfo_SetVersion_IsContainRequest，用于设置版本号并判断是否包含请求信息
// 参数t：*testing.T类型，表示测试对象
// 返回值：无
func TestRuntimeInfo_SetVersion_IsContainRequest(t *testing.T) {
	rt := NewRuntimeInfo("aa")
	rt.SetVersion("v2")
	if rt.CommitID != "v2" {
		t.Error("SetVersion failed")
	}
	req := NewRequestInfo("reqid", "aa")
	rt.requestMap["reqid"] = req
	if !rt.IsContainRequest("reqid") {
		t.Error("IsContainRequest should return true")
	}
	if rt.IsContainRequest("notfound") {
		t.Error("IsContainRequest should return false for missing id")
	}
}

// TestRuntimeInfo_InvokeDone_Boundary 测试RuntimeInfo的InvokeDone方法，包括边界情况。
// 参数t是*testing.T类型，用于记录测试结果。
// 返回值没有，该函数主要用于单元测试。
func TestRuntimeInfo_InvokeDone_Boundary(t *testing.T) {
	rt := NewRuntimeInfo("aa")
	req := NewRequestInfo("reqid", "aa")
	rt.requestMap["reqid"] = req
	req.Status = 2 // api.StatusSuccess
	rt.InvokeDone(req, true)
	if _, ok := rt.requestMap["reqid"]; ok {
		t.Error("InvokeDone should remove request from map")
	}
	// 再次调用应无副作用
	rt.InvokeDone(req, true)
}

// TestRuntimeInfo_InvokeFunc_Concurrency 测试函数RuntimeInfo的InvokeFunc方法，并确保在并发请求时能够限制请求数量。
// 参数：t *testing.T - 类型为*testing.T的指针，用于存放对测试结果的断言。
// 返回值：无返回值
func TestRuntimeInfo_InvokeFunc_Concurrency(t *testing.T) {
	rt := NewRuntimeInfo("aa")
	req1 := NewRequestInfo("id1", "aa")
	req2 := NewRequestInfo("id2", "aa")
	rt.requestMap["id1"] = req1
	invokeInfo := &InvokeInfo{RequestID: "id2"}
	err := rt.InvokeFunc(req2, invokeInfo, false)
	if err == nil || err.Error() != "too many concurrency request" {
		t.Error("InvokeFunc should limit concurrency")
	}
}

// TestRuntimeInfo_InvokeFunc_ConnClosed 测试函数RuntimeInfo_InvokeFunc_ConnClosed，该函数用于测试调用函数时连接是否关闭。
// 参数t：*testing.T类型，表示测试对象。
// 返回值：无返回值
func TestRuntimeInfo_InvokeFunc_ConnClosed(t *testing.T) {
	rt := NewRuntimeInfo("aa")
	req := NewRequestInfo("id1", "aa")
	invokeInfo := &InvokeInfo{RequestID: "id1"}
	err := rt.InvokeFunc(req, invokeInfo, true)
	if err == nil || err.Error() != "runtime connection closed" {
		t.Error("InvokeFunc should return error if no connection")
	}
}

// TestRuntimeInfo_SetCustomRuntimeFunctionInfo 测试函数RuntimeInfo_SetCustomRuntimeFunctionInfo，用于设置自定义运行时函数信息
// 参数t *testing.T - 测试用例的输入，类型为*testing.T，表示测试结果的反馈
func TestRuntimeInfo_SetCustomRuntimeFunctionInfo(t *testing.T) {
	rt := NewRuntimeInfo("custom-rt")
	req := NewRequestInfo("reqid", "custom-rt")
	rt.SetCustomRuntimeFunctionInfo(req)
	if rt.requestMap["reqid"] != req {
		t.Error("SetCustomRuntimeFunctionInfo should set requestMap")
	}
	if rt.RequestID != "concurrent" {
		t.Error("SetCustomRuntimeFunctionInfo should set RequestID to 'concurrent'")
	}
}
