package app

import (
	"bytes"
	"io/ioutil"
	"testing"
)

func TestStdlogReceiver(t *testing.T) {
	r := stdlogReceiver{
		name: "stdout",
		conn: ioutil.NopCloser(
			bytes.NewReader([]byte("line1\000line2\n\nline3\000\n"))),
	}
	buf := bytes.NewBuffer(nil)
	r.Read(func(data []byte, eof bool) (int, error) {
		return buf.Write(data)
	})
	t.Log(buf.String())
}

func TestStdinfoReceiver(t *testing.T) {
	r := statinfoReceiver{
		name: "stat",
		conn: bytes.NewBuffer([]byte(`{"podname":"runtime1", "memory":12345}
`)),
	}
	s := statInfo{}
	r.Recv(func(info *statInfo) error {
		s.PodName = info.PodName
		s.MemUsed = info.MemUsed
		return nil
	})
	if s.PodName != "runtime1" || s.MemUsed != 12345 {
		t.<PERSON>al(s)
	}
}
