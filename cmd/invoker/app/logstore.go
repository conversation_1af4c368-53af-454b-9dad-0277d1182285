package app

import (
	"bytes"
	"fmt"
	"os"
	"path"
	"sync"
	"time"

	"go.uber.org/zap"

	"icode.baidu.com/baidu/faas/kun/pkg/cfclog"
)

type LogStatStore interface {
	Receiver() string
	String() string
	WriteStdLog(from int, buf []byte, eof bool) (int, error)
	WriteCfcLog(log string) error
	SetMemUsed(used int64)
	LogFile() string
	MemUsed() int64
	LogDone(set bool) bool
	Close() (string, error)
	Wait() // 等待日志接收完成
}

type bits uint8

const (
	flagClosed bits = 1 << iota
	flagOutdone
	flagErrdone
)

func (b *bits) Set(f bits) {
	*b = *b | f
}

func (b *bits) Has(f bits) bool {
	return *b&f != 0
}

// 用于存储请求日志
type kunLogStatStore struct {
	requestID string
	runtimeID string
	maxMem    int64

	mutex   sync.Mutex
	waitg   sync.WaitGroup
	flags   bits //outdone, errdone, closed
	remain  int  // 内存中剩余用户日志大小
	logbuf  *bytes.Buffer
	fpath   string // 日志文件路径
	logfile cfclog.CfcLogFile
}

var (
	logBufPool = sync.Pool{
		New: func() interface{} {
			return bytes.NewBuffer(make([]byte, 0, defaultUserLogLength))
		},
	}
)

func newLogStatStore(requestID, runtimeID string, fpath, logtype string) LogStatStore {
	logpath := path.Join(fpath, runtimeID)
	_, err := os.Stat(logpath)
	if err != nil {
		if os.IsNotExist(err) {
			err = os.MkdirAll(logpath, 0755)
			if err != nil {
				applog.V(4).Warn(fmt.Sprintf("recvlog %s create dir %s failed: %s",
					runtimeID, logpath, err.Error()), zap.String("request_id", requestID))
				logpath = ""
			}
		} else {
			applog.V(4).Warn(fmt.Sprintf("recvlog %s stat dir %s failed: %s",
				runtimeID, logpath, err.Error()), zap.String("request_id", requestID))
			logpath = ""
		}
	}
	if len(logpath) > 0 {
		logpath = path.Join(logpath, fmt.Sprintf("%s.%d", requestID, time.Now().UnixNano()))
		_, err = os.Stat(logpath)
		if err == nil {
			applog.V(4).Warn(fmt.Sprintf("recvlog %s logfile %s already exist",
				runtimeID, logpath), zap.String("request_id", requestID))
			logpath = ""
		}
	}
	logfile, err := cfclog.CreateLogWriter(logtype, logpath, maxUserLogSize)
	if err != nil {
		logpath = ""
		if logtype == "" || logtype == "none" {
			applog.V(4).Info(fmt.Sprintf("create logfile failed: %v", err),
				zap.String("request_id", requestID))
		} else {
			applog.V(4).Warn(fmt.Sprintf("create logfile failed: %v", err),
				zap.String("request_id", requestID))
		}
	}
	buf := logBufPool.Get().(*bytes.Buffer)
	buf.Reset()
	r := &kunLogStatStore{
		requestID: requestID,
		runtimeID: runtimeID,
		fpath:     logpath,
		remain:    defaultUserLogLength,
		logbuf:    buf,
		logfile:   logfile,
	}
	r.waitg.Add(2)
	return r
}

func (s *kunLogStatStore) Receiver() string {
	return s.runtimeID
}

func (s *kunLogStatStore) String() string {
	return fmt.Sprintf("%s@%s", s.requestID, s.runtimeID)
}

func (s *kunLogStatStore) appendLogbuf(buf []byte, force bool) {
	if s.remain <= 0 && !force {
		return
	}
	s.mutex.Lock()
	defer s.mutex.Unlock()
	logbuf := s.logbuf
	if logbuf == nil || (s.remain <= 0 && !force) {
		return
	}
	to := len(buf)
	if !force && to > s.remain {
		to = s.remain
	}
	if to <= 0 {
		return
	}
	logbuf.Write(buf[:to])
	s.remain -= to
	if to < len(buf) {
		if buf[to-1] != '\n' {
			logbuf.Write([]byte{'\n'})
			s.remain--
		}
	}
}

func (s *kunLogStatStore) WriteStdLog(from int, buf []byte, eof bool) (int, error) {
	if s.flags.Has(flagClosed) {
		return 0, errReceiverClosed
	}

	if eof {
		if from == StdoutLog {
			s.outLogDone()
		} else if from == StderrLog {
			s.errLogDone()
		}
	}
	if len(buf) == 0 {
		return 0, nil
	}

	s.appendLogbuf(buf, false)
	logfile := s.logfile
	if logfile == nil {
		return len(buf), nil
	}
	l := &cfclog.CfcLog{
		Created:   time.Now(),
		RequestID: s.requestID,
		Source:    logSource[from],
	}
	return logfile.Write(l, buf)
}

// 用户日志接收完成之后，仍可以追加cfc系统日志
func (s *kunLogStatStore) WriteCfcLog(log string) error {
	if s.flags.Has(flagClosed) {
		return errReceiverClosed
	}
	s.appendLogbuf([]byte(log), true)
	logfile := s.logfile
	if logfile == nil {
		return nil
	}
	l := &cfclog.CfcLog{
		Created:   time.Now(),
		RequestID: s.requestID,
		Source:    logSource[CfcsysLog],
		Message:   []byte(log),
	}
	_, err := logfile.Write(l, []byte(log))
	return err
}

func (s *kunLogStatStore) SetMemUsed(used int64) {
	if s.maxMem < used {
		s.maxMem = used
	}
}

func (s *kunLogStatStore) LogFile() string {
	return s.fpath
}

func (s *kunLogStatStore) LogData() string {
	logbuf := s.logbuf
	if logbuf == nil {
		applog.V(4).Warnf("readlog %s after closed", s.String())
		return ""
	}
	data := logbuf.String()
	logbuf.Reset()
	logBufPool.Put(logbuf)
	s.logbuf = nil
	return data
}

func (s *kunLogStatStore) MemUsed() int64 {
	return s.maxMem
}

func (s *kunLogStatStore) setFlagLocked(flag bits) {
	if s.flags.Has(flag) {
		return
	}
	if flag == flagOutdone || flag == flagErrdone {
		s.waitg.Done()
	}
	s.flags.Set(flag)
}
func (s *kunLogStatStore) outLogDone() {
	if s.flags.Has(flagOutdone) {
		return
	}
	s.mutex.Lock()
	defer s.mutex.Unlock()
	s.setFlagLocked(flagOutdone)
}

func (s *kunLogStatStore) errLogDone() {
	if s.flags.Has(flagErrdone) {
		return
	}
	s.mutex.Lock()
	defer s.mutex.Unlock()
	s.setFlagLocked(flagErrdone)
}

func (s *kunLogStatStore) LogDone(set bool) bool {
	if !set {
		if !s.flags.Has(flagOutdone) {
			return false
		}
		if !s.flags.Has(flagErrdone) {
			return false
		}
		return true
	}
	s.mutex.Lock()
	defer s.mutex.Unlock()
	if set {
		s.setFlagLocked(flagErrdone)
		s.setFlagLocked(flagOutdone)
	}
	return true
}

func (s *kunLogStatStore) Close() (string, error) {
	if s.flags.Has(flagClosed) {
		return "", errReceiverClosed
	}
	s.mutex.Lock()
	if !s.flags.Has(flagClosed) {
		s.setFlagLocked(flagClosed)
		s.setFlagLocked(flagErrdone)
		s.setFlagLocked(flagOutdone)
	}
	data := s.LogData()
	s.mutex.Unlock()
	if s.logfile != nil {
		s.logfile.Close()
		s.logfile = nil
	}

	return data, nil
}

func (s *kunLogStatStore) Wait() {
	s.waitg.Wait()
}
