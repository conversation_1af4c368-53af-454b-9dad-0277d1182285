package app

import (
	"icode.baidu.com/baidu/faas/kun/cmd/cronjob/options"
	"icode.baidu.com/baidu/faas/kun/pkg/cronjob"
	"icode.baidu.com/baidu/faas/kun/pkg/server"
	"icode.baidu.com/baidu/faas/kun/pkg/util/logs"
	"icode.baidu.com/baidu/faas/kun/pkg/version"
)

func Run(runOptions *options.CronjobOptions, stopCh <-chan struct{}) error {
	logs.Infof("Version: %+v", version.Get())
	if err := cronjob.Init(runOptions); err != nil {
		return err
	}
	s, err := CreateServerChain(runOptions, stopCh)
	if err != nil {
		return err
	}
	return s.PrepareRun().Run(stopCh)
}

// CreateServerChain creates the apiservers connected via delegation.
func CreateServerChain(runOptions *options.CronjobOptions, stopCh <-chan struct{}) (*server.GenericServer, error) {
	config := server.NewRecommendedConfig()
	if err := runOptions.RecommendedOptions.ApplyTo(config); err != nil {
		return nil, err
	}

	s, err := config.Complete().New(options.ApplicationName)
	if err != nil {
		return nil, err
	}

	// Install API
	cronjob.InstallAPI(s.Handler.GoRestfulContainer)

	return s, nil
}
