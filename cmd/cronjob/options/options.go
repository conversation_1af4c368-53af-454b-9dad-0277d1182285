package options

import (
	"time"

	"github.com/spf13/pflag"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/dbengine"
	"icode.baidu.com/baidu/faas/kun/pkg/rpc/db"
	genericoptions "icode.baidu.com/baidu/faas/kun/pkg/server/options"
)

const (
	// ApplicationName 指定应用名称
	ApplicationName = "cronjob"
)

type CronjobOptions struct {
	RecommendedOptions            *genericoptions.RecommendedOptions
	DbConfiguration               *db.DbConfig
	Region                        string
	ApiserverEndpoint             string
	ApiGatewayEndpoint            string
	IAMConfPath                   string
	SyncMysqlInterval             int64
	CleanRemainApiGatewayInterval time.Duration
}

func NewCronjobOptions() *CronjobOptions {
	dbengine.DbConf = db.NewDbConfig()

	o := CronjobOptions{
		RecommendedOptions:            genericoptions.NewRecommendedOptions(),
		DbConfiguration:               dbengine.DbConf,
		Region:                        "bj",
		ApiserverEndpoint:             "127.0.0.1:8600",
		SyncMysqlInterval:             300,
		ApiGatewayEndpoint:            "http://bjhw-sys-rpm8059.bjhw:8803",
		IAMConfPath:                   "/home/<USER>/faas/cronjob/conf/cfc.yaml",
		CleanRemainApiGatewayInterval: 1 * time.Hour,
	}
	o.RecommendedOptions.SecureServing.BindPort = 8080
	return &o
}

func (s *CronjobOptions) AddFlags(fs *pflag.FlagSet) {
	s.RecommendedOptions.AddFlags(fs)
	s.DbConfiguration.AddFlags(fs)
	fs.StringVar(&s.Region, "region", s.Region, "cronjob running region")
	fs.StringVar(&s.ApiGatewayEndpoint, "apigateway-endpoint", s.ApiGatewayEndpoint, "apigateway endpoint")
	fs.StringVar(&s.IAMConfPath, "iam-confpath", s.IAMConfPath, "iam conf path")
	fs.StringVar(&s.ApiserverEndpoint, "apiserver-endpoint", s.Region, "apiserver endpoint")
	fs.Int64Var(&s.SyncMysqlInterval, "sync-mysql-interval", s.SyncMysqlInterval, "sync-mysql-interval")
	fs.DurationVar(&s.CleanRemainApiGatewayInterval, "clean-remain-apigateway-interval", s.CleanRemainApiGatewayInterval, "sync-mysql-interval")
}
