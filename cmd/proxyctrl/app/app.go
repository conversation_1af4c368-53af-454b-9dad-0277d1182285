package app

import (
	"net/http"

	"github.com/emicklei/go-restful"

	"icode.baidu.com/baidu/faas/kun/cmd/proxyctrl/app/options"
	"icode.baidu.com/baidu/faas/kun/pkg/api"
	"icode.baidu.com/baidu/faas/kun/pkg/proxyctrl"
	"icode.baidu.com/baidu/faas/kun/pkg/server"
	"icode.baidu.com/baidu/faas/kun/pkg/server/endpoint"
	"icode.baidu.com/baidu/faas/kun/pkg/util/logs"
	"icode.baidu.com/baidu/faas/kun/pkg/version"
)

type Server struct {
	opt *options.ProxyCtrlOptions
	pc  *proxyctrl.ProxyCtrl
}

func NewServer(opt *options.ProxyCtrlOptions, stopCh <-chan struct{}) (*Server, error) {
	proxyCtrl, err := proxyctrl.NewProxyCtrl(opt, stopCh)
	if err != nil {
		return nil, err
	}

	err = proxyCtrl.Init()
	if err != nil {
		return nil, err
	}
	return &Server{
		opt: opt,
		pc:  proxyCtrl,
	}, nil
}

func Run(opt *options.ProxyCtrlOptions, stopCh <-chan struct{}) error {
	logs.Infof("Version: %+v", version.Get())

	server, err := NewServer(opt, stopCh)
	if err != nil {
		logs.Fatalf("Error: %v", err)
	}

	genericServer, err := CreateServerChain(server, opt)
	if err != nil {
		return err
	}
	return genericServer.PrepareRun().Run(stopCh)
}

func CreateServerChain(p *Server, opt *options.ProxyCtrlOptions) (*server.GenericServer, error) {
	config := server.NewRecommendedConfig()
	if err := opt.RecommendedOptions.ApplyTo(config); err != nil {
		return nil, err
	}
	s, err := config.Complete().New(opt.ApplicationName)
	if err != nil {
		return nil, err
	}
	p.InstallAPI(s.Handler.GoRestfulContainer)
	return s, nil
}

func (srv *Server) InstallAPI(container *restful.Container) {
	var apis = []endpoint.ApiSingle{
		{
			Verb:    "GET",
			Path:    "hello",
			Handler: server.WrapRestRouteFunc(srv.hello),
		},
		{
			Verb:    "POST",
			Path:    "node",
			Handler: server.WrapRestRouteFunc(srv.askForProxy),
		},
	}

	var apiversion = []endpoint.ApiVersion{
		{
			Prefix: "/inside-v1/proxyctrl",
			Group:  apis,
		},
	}
	endpoint.NewApiInstaller(apiversion).Install(container)
}

func (srv *Server) hello(ctx *server.Context) {
	ctx.Response().WriteHeader(http.StatusOK)
}

func (srv *Server) askForProxy(ctx *server.Context) {
	var cfg api.NetworkConfig
	if err := ctx.Request().ReadEntity(&cfg); err != nil {
		ctx.Logger().Errorf("read ask request body failed, err: %v", err)
		ctx.Response().WriteHeader(http.StatusBadRequest)
		return
	}

	go srv.pc.InitProxyNode(&cfg)
	ctx.Response().WriteHeader(http.StatusAccepted)
}
