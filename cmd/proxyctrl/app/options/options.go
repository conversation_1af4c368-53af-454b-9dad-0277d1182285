package options

import (
	"github.com/spf13/pflag"
	"icode.baidu.com/baidu/faas/kun/pkg/apiserver/client"
	"icode.baidu.com/baidu/faas/kun/pkg/bce/eni"
	"icode.baidu.com/baidu/faas/kun/pkg/bce/iam"
	"icode.baidu.com/baidu/faas/kun/pkg/etcd"
	genericoptions "icode.baidu.com/baidu/faas/kun/pkg/server/options"
	"icode.baidu.com/baidu/faas/kun/pkg/whitelist"
)

type ProxyCtrlOptions struct {
	RecommendedOptions       *genericoptions.RecommendedOptions
	IAMConfiguration         *iam.IAMOptions
	VIPUserOptions           *whitelist.WhiteListOptions
	EtcdOptions              *etcd.Options // 需要与普通 Node/K8S 区分前缀
	ApiserverOptions         *client.ApiserverOptions
	ApplicationName          string
	AgentPort                int    // proxyAgent 模块占用端口
	NodeBillingMode          string // 后付费 "postpay" or 预付费 "prepay"
	ScalingIntervalSeconds   int    // 扩缩容检查时间间隔
	SyncNodesIntervalSeconds int    // 同步 CCE/K8S/ETCD Node 时间间隔
	MinRedundantNodeCount    int    // 最小冗余 proxy node 数
	MaxRedundantNodeCount    int    // 最大冗余 proxy node 数
	TaskInterval             int
	EniOptions               *eni.Options
	EnableScale              bool
}

// NewProxyCtrlOptions creates a new ProxyCtrlOptions object with default parameters
func NewProxyCtrlOptions() *ProxyCtrlOptions {
	return &ProxyCtrlOptions{
		RecommendedOptions:     genericoptions.NewRecommendedOptions(),
		ApiserverOptions:       &client.ApiserverOptions{},
		IAMConfiguration:       iam.NewIAMOptions(),
		EtcdOptions:            genericoptions.NewEtcdOptions(),
		VIPUserOptions:         &whitelist.WhiteListOptions{},
		ApplicationName:        "proxyctrl",
		AgentPort:              8332,
		NodeBillingMode:        "postpay",
		ScalingIntervalSeconds: 10,
		MinRedundantNodeCount:  1,
		MaxRedundantNodeCount:  1,
		TaskInterval:           30,
		EniOptions:             eni.NewOptions(),
		EnableScale:            true,
	}
}

// AddFlags adds flags to the specified FlagSet
func (s *ProxyCtrlOptions) AddFlags(fs *pflag.FlagSet) {
	s.RecommendedOptions.AddFlags(fs)
	s.IAMConfiguration.AddUniversalFlags(fs)
	s.ApiserverOptions.AddApiserverFlags(fs)
	s.EniOptions.AddFlags(fs)
	genericoptions.AddEtcdFlags(s.EtcdOptions, fs)
	fs.IntVar(&s.AgentPort, "agent-port", s.AgentPort, "listen port of proxy agent")
	fs.StringVar(&s.NodeBillingMode, "billing-mode", s.NodeBillingMode, "prepay or postpay of cce proxy node")
	fs.IntVar(&s.ScalingIntervalSeconds, "scaling-interval-seconds", s.ScalingIntervalSeconds, "interval seconds of scaling check")
	fs.IntVar(&s.SyncNodesIntervalSeconds, "sync-nodes-interval-seconds", s.SyncNodesIntervalSeconds, "interval seconds of sync node states")
	fs.IntVar(&s.MinRedundantNodeCount, "min-redundant-node-count", s.MinRedundantNodeCount, "min redundant proxy node count")
	fs.IntVar(&s.MaxRedundantNodeCount, "max-redundant-node-count", s.MaxRedundantNodeCount, "max redundant proxy node count")
	fs.IntVar(&s.TaskInterval, "task-interval", s.TaskInterval, "task execution interval")
	fs.StringSliceVar(&s.VIPUserOptions.SourceWhiteList, "vip-users", s.VIPUserOptions.SourceWhiteList, "vip user tags, input like lx:userid")
	fs.BoolVar(&s.EnableScale, "enable-scale", s.EnableScale, "whether enable auto scaling-up and scaling-down")
}
