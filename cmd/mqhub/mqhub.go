package main

import (
	"fmt"
	"os"

	"github.com/spf13/pflag"

	"icode.baidu.com/baidu/faas/kun/cmd/mqhub/app"
	"icode.baidu.com/baidu/faas/kun/cmd/mqhub/app/options"
	"icode.baidu.com/baidu/faas/kun/pkg/server"
	"icode.baidu.com/baidu/faas/kun/pkg/util/flag"
	"icode.baidu.com/baidu/faas/kun/pkg/util/logs"
	"icode.baidu.com/baidu/faas/kun/pkg/version/verflag"
)

func main() {
	s := options.NewMqhubOptions()
	s.AddFlags(pflag.CommandLine)

	flag.InitFlags()
	logs.InitLogs()
	defer logs.FlushLogs()

	verflag.PrintAndExitIfRequested()

	stopCh := server.SetupSignalHandler()
	if err := app.Run(s, stopCh); err != nil {
		fmt.Fprintf(os.Stderr, "mqhub run err %v\n", err)
		os.Exit(1)
	}
}
