package options

import (
	"time"

	"github.com/spf13/pflag"

	"icode.baidu.com/baidu/faas/kun/pkg/bce/iam"
	"icode.baidu.com/baidu/faas/kun/pkg/etcd"
	eventhub "icode.baidu.com/baidu/faas/kun/pkg/eventhub/client"
	"icode.baidu.com/baidu/faas/kun/pkg/kafka"
	"icode.baidu.com/baidu/faas/kun/pkg/rpc/db"
	kunRedis "icode.baidu.com/baidu/faas/kun/pkg/rpc/redis"
	genericoptions "icode.baidu.com/baidu/faas/kun/pkg/server/options"
	"icode.baidu.com/baidu/faas/kun/pkg/whitelist"
)

const (
	// ApplicationName 指定应用名称
	ApplicationName = "mqhub"

	defaultMainTopic = "fa85b2e03375428ea242ef27a16c33c8__cfc"
)

// EventhubOptions descript options that eventhub needs
type MqhubOptions struct {
	RecommendedOptions              *genericoptions.RecommendedOptions
	IAMOptions                      *iam.IAMOptions
	RedisOptions                    *kunRedis.RedisOptions
	EventHubOptions                 *eventhub.EventHubOptions
	KafkaOptions                    *kafka.Options
	DeadLetterTopic                 string   // 死信队列Topic
	MainTopic                       string   // 主topics
	CallbackTopic                   string   // 回调topic
	VipMainTopic                    string   // vip大客户专用main topic
	VipRetryTopic                   string   // vip大客户专用retry topic
	FunctionExclude                 []string // vip客户排除函数
	KafkaSeekConsumerConcurrencyNum int
	PeonsConcurrencyNum             int
	RedisZsetKeyPrefix              string // zset 前缀
	CheckRedisZsetIntervalMS        int    // 检查间隔时间 单位毫秒
	EtcdOptions                     *etcd.Options
	DbConfig                        *db.DbConfig
	EventSourceEnableTls            bool
	EventSourceMappingBrokers       []string // 触发器Brokers
	EventSourceMappingClientPemPath string
	EventSourceMappingClientKeyPath string
	EventSourceMappingCaPemPath     string
	ApiServerEndpoint               string
	SyncMysqlInterval               time.Duration // 同步数据库间隔时间 单位秒
	VIPUserOptions                  *whitelist.WhiteListOptions
	VIPFunctionTopics               []string                    // vip function专用topic
	VIPFunctionTopicsOptions        *whitelist.WhiteListOptions // vip function与kafka topic对应关系
	ChanLength                      int
	CrontabTopic                    string //crontab专用topic
	CommonTopic                     string // 普通用户异步调用专用topic
	CommonRetryTopic                string // 普通用户异步调用重试topic
	TopicsConfPath                  string // 配置用户topic 的conf文件路径
	DuplicateChecker                bool   // message的去重检测开关，关闭后不进行去重
}

// NewEventhubOptions creates a new EventhubOptions object with default parameters
func NewMqhubOptions() *MqhubOptions {
	s := MqhubOptions{
		RecommendedOptions:              genericoptions.NewRecommendedOptions(),
		IAMOptions:                      iam.NewIAMOptions(),
		RedisOptions:                    kunRedis.NewRedisOptions(),
		EventHubOptions:                 &eventhub.EventHubOptions{},
		KafkaOptions:                    kafka.NewKafkaOptions(),
		DeadLetterTopic:                 "",
		KafkaSeekConsumerConcurrencyNum: 10,
		PeonsConcurrencyNum:             200,
		MainTopic:                       "",
		VipMainTopic:                    "",
		VipRetryTopic:                   "",
		FunctionExclude:                 make([]string, 0),
		RedisZsetKeyPrefix:              "zset",
		CheckRedisZsetIntervalMS:        2000,
		EtcdOptions:                     genericoptions.NewEtcdOptions(),
		DbConfig:                        db.NewDbConfig(),
		EventSourceEnableTls:            true,
		EventSourceMappingClientPemPath: "event_source/client.pem",
		EventSourceMappingClientKeyPath: "event_source/client.key",
		EventSourceMappingCaPemPath:     "event_source/ca.pem",
		ApiServerEndpoint:               "127.0.0.1:8000",
		SyncMysqlInterval:               60,
		VIPUserOptions:                  &whitelist.WhiteListOptions{},
		VIPFunctionTopics:               make([]string, 0),
		VIPFunctionTopicsOptions:        &whitelist.WhiteListOptions{},
		ChanLength:                      400,
		TopicsConfPath:                  "/home/<USER>/faas/mqhub/conf/topics.yaml",
		DuplicateChecker:                false,
	}
	//s.RecommendedOptions.TelemetryOptions.ServiceName = ApplicationName
	return &s
}

// AddFlags adds flags for a specific APIServer to the specified FlagSet
func (s *MqhubOptions) AddFlags(fs *pflag.FlagSet) {
	fs.StringVar(&s.DeadLetterTopic, "dead-letter-topic", s.DeadLetterTopic,
		"kafka dead letter topic")
	fs.StringVar(&s.MainTopic, "main-topic", s.MainTopic,
		"mqhub main topic")
	fs.StringVar(&s.CrontabTopic, "crontab-topic", s.CrontabTopic,
		"mqhub crontab topic")
	fs.StringVar(&s.RedisZsetKeyPrefix, "zset-key-prefix", s.RedisZsetKeyPrefix,
		"retry redis zset key prefix")
	fs.StringVar(&s.CallbackTopic, "callback_topic", s.CallbackTopic,
		"callback topic")
	fs.StringVar(&s.VipMainTopic, "vip-main-topic", s.VipMainTopic,
		"mqhub vip user main topic")
	fs.StringVar(&s.VipRetryTopic, "vip-retry-topic", s.VipRetryTopic,
		"mqhub vip user retry topic")
	fs.StringSliceVar(&s.FunctionExclude, "vip-function-exclude", s.FunctionExclude,
		"mqhub vip user function exclude")
	fs.StringSliceVar(&s.VIPUserOptions.SourceWhiteList, "vip-users", s.VIPUserOptions.SourceWhiteList, "vip user tags, input like lx:userid")
	fs.IntVar(&s.KafkaSeekConsumerConcurrencyNum, "kafka-seek-concurrency", s.KafkaSeekConsumerConcurrencyNum,
		"kafka seek consumer concurrency")
	fs.IntVar(&s.PeonsConcurrencyNum, "peons-concurrency", s.PeonsConcurrencyNum,
		"invoke concurrency")
	fs.IntVar(&s.CheckRedisZsetIntervalMS, "check-interval", s.CheckRedisZsetIntervalMS,
		"check redis zset interval millisecond")
	fs.StringSliceVar(&s.EventSourceMappingBrokers, "es-kafka-brokers", s.EventSourceMappingBrokers,
		"event source kafka brokers")
	fs.BoolVar(&s.EventSourceEnableTls, "es-enable-tls", s.EventSourceEnableTls,
		"enale event source kafka tls")
	fs.StringVar(&s.EventSourceMappingClientPemPath, "es-client-pem-path", s.EventSourceMappingClientPemPath,
		"event source kafka tls client pem file path")
	fs.StringVar(&s.EventSourceMappingClientKeyPath, "es-client-key-path", s.EventSourceMappingClientKeyPath,
		"event source kafka tls client key file path")
	fs.StringVar(&s.EventSourceMappingCaPemPath, "es-ca-pem-path", s.EventSourceMappingCaPemPath,
		"event source kafka tls ca pem file path")
	fs.StringVar(&s.ApiServerEndpoint, "apiserver-endpoint", s.ApiServerEndpoint,
		"api server endpoint")
	fs.DurationVar(&s.SyncMysqlInterval, "sync-mysql-interval", s.SyncMysqlInterval, "sync mysql interval")
	fs.StringSliceVar(&s.VIPFunctionTopics, "vip-function-topics", s.VIPFunctionTopics,
		"vip function topics")
	fs.StringSliceVar(&s.VIPFunctionTopicsOptions.SourceWhiteList, "vip-function-topics-map", s.VIPFunctionTopicsOptions.SourceWhiteList, "vip user function topic tags, like topic1:func1")
	fs.IntVar(&s.ChanLength, "msg-channel-length", s.ChanLength, "msg channel length")
	fs.StringVar(&s.CommonTopic, "common-topic", s.CommonTopic,
		"mqhub common topic")
	fs.StringVar(&s.CommonRetryTopic, "common-retry-topic", s.CommonRetryTopic,
		"mqhub common retry topic")
	fs.StringVar(&s.TopicsConfPath, "topics-conf-path", s.TopicsConfPath, "the topic configuration file path")
	fs.BoolVar(&s.DuplicateChecker, "duplicate-checker", s.DuplicateChecker, "the switch to check whether the message id duplicated")

	s.IAMOptions.AddUniversalFlags(fs)
	s.RedisOptions.AddRedisFlags(fs)
	s.RecommendedOptions.AddFlags(fs)
	s.EventHubOptions.AddEventHubFlags(fs)
	s.KafkaOptions.AddUniversalFlags(fs)
	genericoptions.AddEtcdFlags(s.EtcdOptions, fs)
	s.DbConfig.AddFlags(fs)
}
