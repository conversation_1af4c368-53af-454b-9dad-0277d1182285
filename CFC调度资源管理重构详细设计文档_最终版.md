# CFC调度资源管理重构详细设计文档 - 完整版

## 📋 目录
1. [概述](#概述)
2. [需求分析](#需求分析)
3. [架构设计](#架构设计)
4. [核心实现](#核心实现)
5. [调度器改造](#调度器改造)
6. [Cron模块改造](#cron模块改造)
7. [兼容性策略](#兼容性策略)
8. [配置管理](#配置管理)
9. [测试方案](#测试方案)
10. [实施计划](#实施计划)
11. [风险评估](#风险评估)

## 🎯 概述

### 项目背景
当前CFC调度系统采用统一的资源池管理方式，所有节点共享相同的资源配置。随着业务发展，出现了以下核心需求：

#### 核心需求：运行时调度到不同资源池
- **多OS环境**: 系统需要根据运行时需求自动调度到Ubuntu 16.04、Ubuntu 22.04、CentOS等不同操作系统的节点
- **多运行时支持**: 不同资源池支持不同的运行时版本（如Java8 vs Java17，Node.js16 vs Node.js20）
- **GPU计算**: AI/ML工作负载需要自动调度到GPU节点
- **容器镜像隔离**: 不同资源池使用不同的容器镜像和运行时环境

#### 业务场景
```
场景1: 运行时优先使用默认池（推荐）
函数信息:
{
  "name": "legacyFunction",
  "runtime": "java8"  // 只指定运行时，不指定resourcePool
}
↓
ResourcePool管理器优先检查默认池 → 默认池支持java8 → 使用默认池
↓
生成调度到默认资源池的Pod（Ubuntu 16.04 + Java8）

场景2: 默认池不支持时使用专用池
函数信息:
{
  "name": "modernFunction",
  "runtime": "java17"  // 默认池不支持java17
}
↓
ResourcePool管理器检查默认池 → 不支持java17 → 查找专用池 → 找到ubuntu2204-pool支持
↓
生成调度到ubuntu2204-pool的Pod

场景3: 兜底降级策略
函数信息:
{
  "name": "experimentalFunction",
  "runtime": "unknownRuntime"  // 所有池子都不支持的运行时
}
↓
ResourcePool管理器检查默认池 → 不支持 → 检查专用池 → 都不支持 → 仍降级到默认池
↓
生成调度到默认资源池的Pod，由默认池处理未知运行时
```

### 设计目标
- **函数级ResourcePool选择**: 支持函数根据运行时需求自动选择ResourcePool
- **完美向后兼容**: 存量节点零影响，支持渐进式迁移
- **最小化改动**: 在现有架构基础上扩展，复用已有逻辑
- **零风险升级**: 通过智能兼容策略确保系统稳定性

### 核心价值
1. **函数级灵活性**: 函数可以根据运行时需求自动选择合适的资源池
2. **环境隔离**: 不同运行时环境和OS版本完全隔离
3. **精准调度**: 基于ResourcePool标签实现精准的节点匹配
4. **平滑升级**: 新旧格式并存，支持渐进式迁移
5. **用户优先级调度**: 支持基于用户权限和优先级的资源池访问控制

## 📊 需求分析

### 功能需求

#### FR1: ResourcePool管理
- **FR1.1**: 支持定义多个ResourcePool，每个池子有独立的资源池信息（OS类型、容器镜像、支持的运行时）
- **FR1.2**: 支持池子级别的扩缩容策略配置，可覆盖全局配置
- **FR1.3**: 支持默认池子（复用现有配置）和额外池子的概念
- **FR1.4**: 支持ResourcePool的启用/禁用控制

#### FR2: 运行时调度增强（核心功能）
- **FR2.1**: 根据运行时自动选择调度到合适的资源池(etcd配置中，若无则默认资源池)
- **FR2.2**: 调度器根据ResourcePool配置进行节点筛选和匹配
- **FR2.3**: 当选择的ResourcePool不可用时，自动降级到默认资源池
- **FR2.4**: 优先使用默认池，确保最大兼容性和稳定性
- **FR2.5**: 支持基于用户权限的资源池访问控制和优先级调度


#### FR3: 扩缩容增强
- **FR3.1**: 支持按ResourcePool维度进行扩缩容决策
- **FR3.2**: 新创建的节点能够自动设置正确的ResourcePool标签
- **FR3.3**: 支持池子级别的扩缩容配置覆盖


#### FR4: 配置管理
- **FR4.1**: 支持通过etcd配置ResourcePool
- **FR4.2**: 支持配置的动态更新和生效
- **FR4.3**: 支持配置的验证和错误处理
- **FR4.4**: 支持配置的版本管理和回滚

### 非功能需求

#### NFR1: 兼容性
- **NFR1.1**: 存量节点和Pod完全不受影响
- **NFR1.2**: 支持渐进式迁移，新旧格式并存
- **NFR1.3**: 升级过程零停机，零风险
- **NFR1.4**: 支持配置回滚和降级

#### NFR2: 性能
- **NFR2.1**: 调度性能不受影响，延迟增加<10ms
- **NFR2.2**: 扩缩容决策时间不超过现有时间的120%
- **NFR2.3**: Redis Key分布均匀，避免热点
- **NFR2.4**: 内存使用增加<5%

#### NFR3: 可维护性
- **NFR3.1**: 代码修改点最少，复用现有逻辑
- **NFR3.2**: 配置简单明了，易于理解和维护
- **NFR3.3**: 日志记录完善，便于问题排查
- **NFR3.4**: 文档完整，便于后续开发

### 约束条件
- **C1**: 必须保持现有API的向后兼容性
- **C2**: 不能修改现有的Redis Key格式（存量数据）
- **C3**: 不能影响现有的调度和扩缩容逻辑
- **C4**: 必须支持配置的热更新，不能重启服务

## 🏗️ 架构设计

### 用户专属资源池调度设计

ResourcePool支持用户专属池映射，通过简化的映射关系实现用户到资源池的直接绑定：

```go
// 用户专属池映射：uid -> poolName
UserPoolMappings: map[string]string{
    "premium-user-001": "gpu-pool",        // Premium用户专属GPU池
    "vip-user-002": "high-memory-pool",    // VIP用户专属高内存池
}
```

**调度优先级**：
1. 🥇 检查用户专属池映射 (`UserPoolMappings`)
2. 🥈 检查专属池是否支持运行时 (`SupportRuntimes`)
3. 🥉 降级到通用资源池选择逻辑

### Redis Key格式演进

#### 标签含义澄清
```go
// 标签实际含义 (注意命名误导性)
LabelVipUser      → clusterName (如: "common")
LabelUserID       → 真正的用户ID (如: "dummyUSER") 
LabelResourcePool → 资源池名称 (如: "ubuntu2204-pool")
```

#### Key格式对比
```
原格式 (存量节点兼容):
- Cold Node:  kun:cold_node:{cfc:common}:128
- Warm Node:  kun:warm_node:{cfc:common}:dummyUSER:128
- Warm Pod:   kun:warm_pod:cfc:common:dummyUSER::128:abc123

ResourcePool格式 (新节点):
- Cold Node:  kun:cold_node:{cfc:common,ubuntu2204-pool}:128
- Warm Node:  kun:warm_node:{cfc:common,ubuntu2204-pool}:dummyUSER:128
- Warm Pod:   kun:warm_pod:cfc:common,ubuntu2204-pool:dummyUSER::128:def456

📝 注意：标签按字母排序后用逗号分隔，如 common,ubuntu2204-pool
```

### 多集群合并架构

#### vipUser标签映射策略
针对多个集群具有相同vipUser标签的情况，系统采用智能合并策略：

```
集群A: vipUser="runtime_base_u22"
├── ResourcePools: ["ubuntu2204-pool", "gpu-pool"]
└── 节点: [node-a1, node-a2, node-a3]

集群B: vipUser="runtime_base_u22"
├── ResourcePools: ["ubuntu2204-pool", "ai-pool"]  // 同名去重，不同名合并
└── 节点: [node-b1, node-b2]

集群C: vipUser="runtime_base_u22"
├── ResourcePools: ["edge-pool"]
└── 节点: [node-c1]

↓ 合并后的逻辑视图 ↓

vipUser="runtime_base_u22" (大池子)
├── 合并后ResourcePools: ["ubuntu2204-pool", "gpu-pool", "ai-pool", "edge-pool"]
├── 总节点数: 6个节点 (node-a1~a3, node-b1~b2, node-c1)
└── 调度策略: 函数可以调度到任意一个集群的合适节点
```

#### 合并规则
1. **同名ResourcePool去重**: 相同名称的ResourcePool只保留第一个
2. **不同名ResourcePool合并**: 不同名称的ResourcePool全部合并到大池子中
3. **节点池统一管理**: 所有集群的节点都可以被统一调度
4. **配置优先级**: 第一个启用ResourcePool的集群配置作为默认配置
5. **🆕 引用计数管理**: 使用计数器跟踪集群数量，支持向前兼容的删除操作

#### 引用计数器机制 🆕
```
集群生命周期管理：
├── 集群添加: clusterCount++
├── 集群更新: clusterCount不变
├── 集群删除: clusterCount--
└── 缓存清理: 仅当clusterCount=0时删除整个vipUser缓存

向前兼容性保证：
├── 临时删除: 集群配置临时删除，缓存保留（clusterCount>0）
├── 重新添加: 集群重新上线，自动恢复到原有配置
├── 永久删除: 所有集群都删除时，才清理缓存（clusterCount=0）
└── 配置连续性: 避免因etcd操作导致的配置丢失
```

### 集群架构图

#### 原始架构
```
CFC集群 (cce-u4clzq75)
├── Cold Node Set (集群共享，不区分用户)
│   ├── Redis Key: kun:cold_node:{cfc:common}:128
│   │   ├── ZSet元素1: Score=128(可用内存), Member=node-001|0
│   │   └── ZSet元素2: Score=128(可用内存), Member=node-002|0
│   📝 注意：Cold Node使用ClusterLabels，只包含clusterName，不包含userID
│   📝 Score表示节点可用内存；Member格式为nodeID|occupiedVersion
│   📝 关键：节点被占用时会从Cold Node Set中删除，移动到Warm Node Set
├── Warm Node Set (用户专用)
│   ├── Redis Key: kun:warm_node:{cfc:common}:dummyUSER:128
│   │   ├── ZSet元素1: Score=64(剩余内存), Member=node-003|1  (从Cold Node移动过来)
│   │   ├── ZSet元素2: Score=96(剩余内存), Member=node-004|1
│   │   └── ZSet元素3: Score=32(剩余内存), Member=node-005|1
│   ├── Redis Key: kun:warm_node:{cfc:common}:vipUSER:128
│   │   └── ZSet元素1: Score=256(剩余内存), Member=node-006|1
│   📝 注意：Warm Node使用ClusterLabels + NodeLabels，NodeLabels中包含userID
│   📝 Score表示节点剩余内存；Member格式为nodeID|occupiedVersion
└── Pod Set (用户+代码专用)
    ├── Redis Key: kun:warm_pod:cfc:common:dummyUSER::128:abc123
    │   ├── ZSet元素1: Score=2(并发数), Member=node-003|pod-001|1
    │   └── ZSet元素2: Score=1(并发数), Member=node-004|pod-002|1
    ├── Redis Key: kun:warm_pod:cfc:common:vipUSER::128:def456
    │   └── ZSet元素1: Score=3(并发数), Member=node-006|pod-003|1
    📝 注意：Score表示Pod并发数；Member格式为nodeID|podName|nodeOccupiedVersion
```

#### ResourcePool架构
```
CFC集群 (cce-u4clzq75) - ResourcePool架构
├── Default Pool (无resourcePool标签，完全兼容)
│   ├── Cold Nodes → kun:cold_node:{cfc:common}:128
│   │   └── ZSet元素: Score=128(可用内存), Member=node-001|0
│   ├── Warm Nodes → kun:warm_node:{cfc:common}:dummyUSER:128
│   │   └── ZSet元素: Score=64(剩余内存), Member=node-002|1 (从Cold Node移动过来)
│   └── Pods → kun:warm_pod:cfc:common:dummyUSER::128:abc123
│       └── ZSet元素: Score=1(并发数), Member=node-002|pod-001|1
├── Ubuntu2204 Pool (有resourcePool标签)
│   ├── Cold Nodes → kun:cold_node:{cfc:common,ubuntu2204-pool}:128
│   │   └── ZSet元素: Score=128(可用内存), Member=node-003|0
│   ├── Warm Nodes → kun:warm_node:{cfc:common,ubuntu2204-pool}:dummyUSER:128
│   │   └── ZSet元素: Score=96(剩余内存), Member=node-004|1 (从Cold Node移动过来)
│   └── Pods → kun:warm_pod:cfc:common,ubuntu2204-pool:dummyUSER::128:def456
│       └── ZSet元素: Score=2(并发数), Member=node-004|pod-002|1
└── GPU Pool (有resourcePool标签)
    ├── Cold Nodes → kun:cold_node:{cfc:common,gpu-pool}:1024
    │   └── ZSet元素: Score=1024(可用内存), Member=gpu-node-001|0
    ├── Warm Nodes → kun:warm_node:{cfc:common,gpu-pool}:dummyUSER:1024
    │   └── ZSet元素: Score=512(剩余内存), Member=gpu-node-002|1 (从Cold Node移动过来)
    └── Pods → kun:warm_pod:cfc:common,gpu-pool:dummyUSER::1024:ghi789
        └── ZSet元素: Score=1(并发数), Member=gpu-node-002|ai-pod-001|1

📝 Redis ZSet存储格式说明:
- Cold Node: Score=节点可用内存, Member=nodeID|occupiedVersion
- Warm Node: Score=节点剩余内存, Member=nodeID|occupiedVersion
- Warm Pod: Score=Pod并发数, Member=nodeID|podName|nodeOccupiedVersion
📝 关键机制: 节点被占用时，通过Lua脚本原子性地从Cold Node Set删除并移动到Warm Node Set

### Cold Node到Warm Node转移机制

#### 转移触发条件
当调度器请求获取节点时，会调用`GetReserveByMemorySize()`方法，该方法执行Lua脚本`move_and_incr_score_xx.lua`：

1. **优先查找Warm Node**: 先在Warm Node Set中查找可用节点
2. **降级到Cold Node**: 如果Warm Node Set中没有可用节点，则在Cold Node Set中查找
3. **原子性转移**: 找到Cold Node后，原子性地执行：
   - 从Cold Node Set中删除该节点 (`redis.call('zrem', spare_key, member)`)
   - 计算剩余内存 (`new_score = tonumber(score) - decr_score`)
   - 添加到Warm Node Set (`redis.call('zadd', prefer_key, new_score, member)`)

#### 转移过程示例
```
初始状态:
Cold Node Set: kun:cold_node:{cfc:common}:128
├── node-001|0 (Score: 128)
├── node-002|0 (Score: 128)

用户请求128MB内存:
1. 查找Warm Node Set → 无可用节点
2. 查找Cold Node Set → 找到node-001|0
3. 执行Lua脚本:
   - 从Cold Node Set删除: node-001|0
   - 计算剩余内存: 128 - 128 = 0
   - 添加到Warm Node Set: node-001|1 (Score: 0)

结果状态:
Cold Node Set: kun:cold_node:{cfc:common}:128
├── node-002|0 (Score: 128)

Warm Node Set: kun:warm_node:{cfc:common}:dummyUSER:128
├── node-001|1 (Score: 0)
```

#### 关键特点
- **原子性**: 整个转移过程在一个Lua脚本中完成，保证数据一致性
- **版本控制**: OccupiedVersion会递增，用于并发控制
- **内存计算**: Score从可用内存变为剩余内存
- **不可逆**: 节点一旦移动到Warm Node Set，不会自动回到Cold Node Set
```

## 🔧 核心实现

### 1. API层扩展

#### ResourcePool配置结构
```go
// 详见配置管理章节中的完整定义
// 这里的结构已废弃，请参考配置管理章节中基于原详细设计文档的正确定义
```

#### 标签定义扩展
```go
// pkg/api/label.go
const (
    LabelResourcePool = "resourcePool"
)

// 修改ClusterLabelKeys配置
var cfcLabelsKeys = &LabelKeys{
    ClusterLabelKeys: []string{LabelVipUser, LabelResourcePool}, // 添加LabelResourcePool
    NodeLabelKeys:    []string{LabelUserID, LabelVpcConfigID},
    PodLabelKeys:     nil,
}
```

### 2. 多集群合并核心实现

#### ResourcePool缓存结构设计（修正后）
```go
// pkg/poolmanager/resourcepool/types.go
type ResourcePoolCache struct {
    vipUserLabel       string                       // vipUser标签值（"common"表示公共集群）
    enabled            bool                         // ResourcePool调度功能是否启用
    defaultPoolInfo    *api.ResourcePoolInfo        // 默认池配置（取第一个启用的集群）
    extraResourcePools map[string]*api.ResourcePool // 合并后的额外ResourcePool配置（同名去重，不同名合并）
    userPoolMappings   map[string]string            // 用户到资源池的映射关系 uid -> poolName
    lastUpdateTime     time.Time                    // 最后更新时间
}

type Manager struct {
    logger        *logs.Logger
    etcdPrefix    string

    // vipUser维度的缓存（简化设计）
    vipUserCaches map[string]*ResourcePoolCache // key: vipUser ("common" for public clusters)

    configWatcher clientv3.Watcher
    mu            sync.RWMutex
    stopChan      chan struct{}
    stopped       bool
}
```

#### 多集群合并算法（修正后）
```go
// pkg/poolmanager/resourcepool/cache.go
func (m *Manager) mergeClusterToVipUserCache(vipUser string, k8sInfo *api.K8sInfo) {
    // 标准化vipUser：空值表示公共集群，使用"common"
    if vipUser == "" {
        vipUser = "common"
    }

    // 获取或创建vipUser缓存
    cache, exists := m.vipUserCaches[vipUser]
    if !exists {
        // 创建新的vipUser缓存
        cache = &ResourcePoolCache{
            vipUserLabel:       vipUser,
            clusterCount:       1, // 初始计数为1
            enabled:            k8sInfo.ResourcePoolConfig != nil && k8sInfo.ResourcePoolConfig.Enabled,
            defaultPoolInfo:    k8sInfo.ResourcePoolInfo,
            extraResourcePools: make(map[string]api.ResourcePool),
            lastUpdateTime:     time.Now(),
        }
        m.vipUserCaches[vipUser] = cache
    } else {
        // 合并到现有缓存
        cache.clusterCount++ // 递增计数器

        // 如果当前集群启用了ResourcePool，则整体启用
        if k8sInfo.ResourcePoolConfig != nil && k8sInfo.ResourcePoolConfig.Enabled {
            cache.enabled = true
        }

        // 如果当前缓存没有defaultPoolInfo，使用当前集群的
        if cache.defaultPoolInfo == nil && k8sInfo.ResourcePoolInfo != nil {
            cache.defaultPoolInfo = k8sInfo.ResourcePoolInfo
        }

        cache.lastUpdateTime = time.Now()
    }

    // 🔑 核心合并逻辑：同名去重，不同名合并
    if k8sInfo.ResourcePoolConfig != nil {
        for name, pool := range k8sInfo.ResourcePoolConfig.ExtraResourcePools {
            if existingPool, exists := cache.extraResourcePools[name]; exists {
                // 同名ResourcePool，保留第一个，记录日志
                m.logger.V(6).Infof("ResourcePool '%s' already exists in vipUser '%s', keeping existing one",
                    name, vipUser)
                _ = existingPool // 避免未使用变量警告
            } else {
                // 不同名ResourcePool，直接添加
                cache.extraResourcePools[name] = pool
                m.logger.V(6).Infof("Added ResourcePool '%s' to vipUser '%s'", name, vipUser)
            }
        }
    }
}
```

#### 智能查找算法（修正后）
```go
// pkg/poolmanager/resourcepool/manager.go
func (m *Manager) findClusterConfigByVipUser(vipUser string) *ResourcePoolCache {
    m.mu.RLock()
    defer m.mu.RUnlock()

    // 标准化vipUser：空值表示公共集群，使用"common"
    if vipUser == "" {
        vipUser = "common"
    }

    // 直接查找vipUser缓存
    if cache, exists := m.vipUserCaches[vipUser]; exists {
        m.logger.V(6).Infof("Found vipUser cache for %s (clusterCount: %d)", vipUser, cache.clusterCount)
        return cache
    }

    // 如果没有找到匹配的vipUser，查找公共集群
    if cache, exists := m.vipUserCaches["common"]; exists {
        m.logger.V(6).Infof("Using common cluster cache for vipUser %s", vipUser)
        return cache
    }

    return nil
}

#### 配置删除和一致性保障（新增）

由于简化设计不跟踪具体的clusterID，当etcd配置删除时无法精确知道影响的vipUser。采用**全量重新加载**策略确保数据一致性：

```go
// handleClusterConfigDelete 处理集群配置删除
func (m *Manager) handleClusterConfigDelete(clusterID string) error {
    // 由于我们不跟踪具体的clusterID，无法精确知道哪个vipUser受影响
    // 因此触发全量重新加载来确保数据一致性
    m.logger.Infof("Cluster %s deleted, triggering full reload to maintain consistency", clusterID)

    // 触发全量重新加载
    go m.triggerFullReload()

    return nil
}

// 定期全量重新加载机制（每5分钟）
func (m *Manager) periodicFullReload() {
    defer func() {
        if m.reloadTicker != nil {
            m.reloadTicker.Stop()
        }
    }()

    for {
        select {
        case <-m.reloadTicker.C:
            m.performFullReload()
        case <-m.stopChan:
            return
        }
    }
}

// performFullReload 执行全量重新加载
func (m *Manager) performFullReload() {
    // 创建新的临时缓存
    tempCaches := make(map[string]*ResourcePoolCache)

    // 重新加载所有集群配置到临时缓存
    if err := m.loadAllClustersToCache(etcdClient, m.etcdPrefix, tempCaches); err != nil {
        m.logger.Errorf("Failed to perform full reload: %v", err)
        return
    }

    // 原子性替换缓存
    m.mu.Lock()
    m.vipUserCaches = tempCaches
    m.lastReload = time.Now()
    m.mu.Unlock()

    m.logger.Infof("Full reload completed, loaded %d vipUser caches", len(tempCaches))
}
```

**优势**：
- **简单可靠**: 避免复杂的增量更新逻辑
- **数据一致性**: 确保缓存与etcd完全同步
- **自动修复**: 定期重新加载可以修复任何不一致问题
- **容错性强**: 即使某些增量更新失败，定期重新加载也能保证最终一致性
```

### 3. 兼容性核心实现

#### GetLabels方法兼容改造
```go
// pkg/api/label.go:135-141
func (l RequestLabels) GetLabels(serviceType ServiceType, labelType uint8) (RequestLabels, error) {
    // ... 前面的代码保持不变 ...
    
    labels := make(RequestLabels)
    for _, key := range keys {
        value, ok := l[key]
        if !ok {
            continue // 🔑 关键修改：不报错，直接跳过缺失的标签
        }
        labels[key] = value
    }
    return labels, nil
}
```

#### 兼容处理流程（修正版）
```
存量节点处理流程:
K8s节点标签: {} (无resourcePool标签)
↓
Cron模块读取节点: extractAndSetResourcePoolFromK8s()
↓
K8s标签中无resourcePool → 不设置ClusterLabels[LabelResourcePool]
↓
ClusterLabels: {vipUser: "common"} (只有基础标签)
↓
GetLabels(ServiceTypeCFC, LabelTypeCluster)
↓
遍历 ClusterLabelKeys: [LabelVipUser, LabelResourcePool]
├── LabelVipUser: 找到 → "common"
└── LabelResourcePool: 未找到 → continue跳过
↓
返回 ClusterLabels: {LabelVipUser: "common"}
↓
SortedLabels(): "common"
↓
最终Key: kun:cold_node:{cfc:common}:128
✅ 保持原格式，完美兼容

新节点处理流程:
扩容时设置K8s节点标签: {resourcePool: "ubuntu2204-pool"}
↓
Cron模块读取节点: extractAndSetResourcePoolFromK8s()
↓
从K8s标签读取resourcePool → 验证配置有效性 → 设置到ClusterLabels
↓
ClusterLabels: {vipUser: "common", resourcePool: "ubuntu2204-pool"}
↓
GetLabels(ServiceTypeCFC, LabelTypeCluster)
↓
遍历 ClusterLabelKeys: [LabelVipUser, LabelResourcePool]
├── LabelVipUser: 找到 → "common"
└── LabelResourcePool: 找到 → "ubuntu2204-pool"
↓
返回 ClusterLabels: {LabelVipUser: "common", LabelResourcePool: "ubuntu2204-pool"}
↓
SortedLabels(): "common,ubuntu2204-pool" (按字母排序)
↓
最终Key: kun:cold_node:{cfc:common,ubuntu2204-pool}:128
✅ 使用新格式，支持ResourcePool

关键点：
1. ResourcePool标签来源于K8s节点标签，不是etcd配置
2. 扩容时在K8s节点上设置标签，读取时转换到ClusterLabels
3. 降级策略确保兼容性和容错性
```




## 🔄 兼容性策略

### 核心原则
1. **永远不报错**: 缺少resourcePool标签时使用默认值
2. **优雅降级**: 无标签/空标签/default都使用原格式
3. **Continue而非Error**: 遇到缺失标签时继续执行，不中断

### 兼容性优势
- ✅ **零风险升级**: 存量节点缺少resourcePool标签，GetLabels自动跳过缺失标签，生成原格式Key
- ✅ **渐进式迁移**: 新节点可选择添加resourcePool标签，自动生成新格式Key，新旧格式并存
- ✅ **实现简洁**: 只需修改2个地方，逻辑集中统一，一次修改全局生效

### Key分布示例
```
升级前 (全部原格式):
kun:cold_node:{cfc:common}:128
kun:warm_node:{cfc:common}:dummyUSER:128
kun:warm_pod:cfc:common:dummyUSER::128:abc123

升级后 (混合格式):
默认池: kun:cold_node:{cfc:common}:128
Ubuntu22池: kun:cold_node:{cfc:common:ubuntu2204-pool}:128
GPU池: kun:cold_node:{cfc:common:gpu-pool}:128
```

## 🔧 调度器改造

### 调度流程增强

#### 原有调度流程
```
Pod调度请求 → 节点筛选 → 资源检查 → 调度决策 → 节点分配
```

#### ResourcePool完整流程（CCEv2接口 + 缓存机制 + 复用现有机制）
```
扩容阶段：
扩容需求分析 → 按ResourcePool分类 → CCEv2接口直接设置K8s标签 → 节点创建完成
                     ↓
              1. 🎯 分析各ResourcePool的扩容需求
              2. 🚀 调用ScalingUpWithResourcePool，传入targetPool
              3. 🔧 在CCEv2接口参数中直接设置resourcePool标签
              4. ✅ CCE创建节点时自动设置K8s标签

调度阶段：
函数调度请求 → 现有Schedule方法 → 内部自动增强标签 → 继续现有调度流程 → 节点查找 → 调度决策
                     ↓
              1. 🚀 Schedule方法内部检查是否启用ResourcePool
              2. 🥇 如果启用，从缓存快速读取配置（无etcd访问）
              3. � 根据runtime自动选择ResourcePool并增强标签
              4. 🥉 继续执行原有调度逻辑（GetLabels、Key生成等）
              5. 现有调度器自动处理包含ResourcePool的Key

节点同步阶段：
K8s节点发现 → 读取resourcePool标签 → 转换到ClusterLabels → 生成正确的Redis Key
                     ↓
              1. 🔄 Cron模块定期同步K8s节点信息
              2. 📖 ConvertToNodeInfo读取K8s节点的resourcePool标签
              3. ✅ 验证ResourcePool配置有效性
              4. 🏷️ 设置到NodeInfo.ClusterLabels中

性能优势：
- CCEv2接口直接设置标签，无需后续操作
- 启动时加载配置到缓存，运行时零etcd访问
- watch机制实时更新缓存，配置变更立即生效
- 读写锁保证并发安全，调度延迟降低
```

### ResourcePool管理器模块设计

#### 模块目录结构
```
pkg/poolmanager/resourcepool/
├── manager.go               // ResourcePool管理器主要实现
├── cache.go                 // ResourcePool缓存管理
├── config.go                // 配置加载和验证
├── interface.go             // ResourcePool管理器接口定义
└── types.go                 // ResourcePool相关类型定义
```

#### ResourcePool管理器接口设计
```go
// pkg/poolmanager/resourcepool/interface.go
package resourcepool

import (
    "icode.baidu.com/baidu/faas/kun/pkg/api"
    "icode.baidu.com/baidu/faas/kun/pkg/etcd"
    "icode.baidu.com/baidu/faas/kun/pkg/util/logs"
)

// ManagerInterface 定义ResourcePool管理器的核心接口
type ManagerInterface interface {
    // 初始化ResourcePool缓存（etcdPrefix来自配置参数）
    InitResourcePoolCache(etcdClient *etcd.Client, etcdPrefix string) error

    // 为调度标签增加ResourcePool信息
    EnhanceLabelsWithResourcePool(labels api.RequestLabels, runtime string, clusterID string)

    // 获取缓存状态（用于监控和调试）
    GetResourcePoolCacheStatus() map[string]interface{}
    GetClusterResourcePoolCacheStatus(clusterID string) map[string]interface{}

    // 停止ResourcePool管理器（清理资源）
    Stop() error
}

// NewManager 创建ResourcePool管理器实例
func NewManager(logger *logs.Logger) ManagerInterface {
    return &Manager{
        logger:        logger,
        clusterCaches: make(map[string]*ResourcePoolCache),
    }
}
```

#### 类型定义（修正后）
```go
// pkg/poolmanager/resourcepool/types.go
package resourcepool

import (
    "sync"
    "time"
    "icode.baidu.com/baidu/faas/kun/pkg/api"
    "icode.baidu.com/baidu/faas/kun/pkg/util/logs"
)

// ResourcePoolCache 表示按vipUser合并的ResourcePool配置缓存
type ResourcePoolCache struct {
    vipUserLabel       string                       // vipUser标签值（"common"表示公共集群）
    enabled            bool                         // ResourcePool调度功能是否启用
    defaultPoolInfo    *api.ResourcePoolInfo        // 默认池配置（取第一个启用的集群）
    extraResourcePools map[string]*api.ResourcePool // 合并后的额外ResourcePool配置（同名去重，不同名合并）
    userPoolMappings   map[string]string            // 用户到资源池的映射关系 uid -> poolName
    lastUpdateTime     time.Time                    // 最后更新时间
}

// Manager ResourcePool管理器
type Manager struct {
    logger        *logs.Logger
    etcdPrefix    string
    etcdClient    interface{} // *etcd.Client, 保存etcd客户端引用用于全量重新加载

    // vipUser维度的缓存（简化设计）
    vipUserCaches map[string]*ResourcePoolCache // key: vipUser ("common" for public clusters)

    mu            sync.RWMutex
    stopChan      chan struct{}
    stopped       bool

    // 全量重新加载相关
    reloadTicker  *time.Ticker // 定期全量重新加载定时器
    lastReload    time.Time    // 上次全量重新加载时间
}
```

#### 缓存管理
```go
// pkg/poolmanager/resourcepool/cache.go
package resourcepool

import (
    "context"
    "encoding/json"
    "fmt"
    "strings"
    "time"

    "go.etcd.io/etcd/clientv3"
    "icode.baidu.com/baidu/faas/kun/pkg/api"
    "icode.baidu.com/baidu/faas/kun/pkg/etcd"
)

// ManagerConfig ResourcePool管理器的配置参数
type ManagerConfig struct {
    // etcd前缀，参考cron模块的配置
    // 默认值："kun1.0"，与etcd.Options.Prefix保持一致
    EtcdPrefix string
}

// 从etcd配置中获取prefix的方法（参考cron模块）
func GetEtcdPrefixFromOptions(etcdOptions *etcd.Options) string {
    if etcdOptions.Prefix != "" {
        return etcdOptions.Prefix
    }
    return "kun1.0" // 默认值，与etcd包中的defaultKunPrefix一致
}

// 这部分代码已经在上面的缓存管理部分正确实现了

// 停止ResourcePool管理器
func (m *Manager) Stop() error {
    m.mu.Lock()
    defer m.mu.Unlock()

    if m.stopped {
        return nil
    }

    close(m.stopChan)
    if m.configWatcher != nil {
        m.configWatcher.Close()
    }
    m.stopped = true

    m.logger.Infof("ResourcePool Manager stopped successfully")
    return nil
}
```

### 核心组件改造

#### 1. ResourcePool管理器实现（缓存机制 + 复用现有标签机制）
```go
#### 核心实现（修正后）

所有核心实现已在上面的缓存结构设计和多集群合并算法部分正确定义。主要包括：

1. **初始化**: `InitResourcePoolCache()` - 启动缓存和定期重新加载机制
2. **配置合并**: `mergeClusterToVipUserCache()` - 按vipUser合并集群配置
3. **智能查找**: `findClusterConfigByVipUser()` - 基于vipUser查找配置
4. **一致性保障**: `performFullReload()` - 全量重新加载确保数据一致性
5. **标签增强**: `EnhanceLabelsWithResourcePool()` - 为调度请求添加ResourcePool标签

#### 配置更新处理（修正后）
```go
// 处理单个集群的配置更新
func (m *Manager) handleClusterConfigUpdate(clusterID string, configData []byte) error {
    var k8sInfo api.K8sInfo
    if err := json.Unmarshal(configData, &k8sInfo); err != nil {
        return err
    }

    // 提取vipUser标签
    vipUser := ""
    if k8sInfo.NodeConfig.ClusterLabels != nil {
        vipUser = k8sInfo.NodeConfig.ClusterLabels[api.LabelVipUser]
    }

    m.mu.Lock()
    defer m.mu.Unlock()

    // 合并到vipUser缓存（简化设计）
    m.mergeClusterToVipUserCache(vipUser, &k8sInfo)

    m.logger.Infof("Updated ResourcePool config cache for vipUser %s", vipUser)
    return nil
}

#### 监控和调试接口（修正后）
```go
// 获取缓存状态（用于监控和调试，基于vipUser）
func (m *Manager) GetResourcePoolCacheStatus() map[string]interface{} {
    m.mu.RLock()
    defer m.mu.RUnlock()

    if m.vipUserCaches == nil {
        return map[string]interface{}{
            "initialized": false,
        }
    }

    vipUserStatus := make(map[string]interface{})
    for vipUser, cache := range m.vipUserCaches {
        vipUserStatus[vipUser] = map[string]interface{}{
            "enabled":               cache.enabled,
            "clusterCount":          cache.clusterCount,
            "extraPoolsCount":       len(cache.extraResourcePools),
            "lastUpdateTime":        cache.lastUpdateTime,
            "defaultPoolConfigured": cache.defaultPoolInfo != nil,
        }
    }

    return map[string]interface{}{
        "initialized":     true,
        "vipUsersCount":   len(m.vipUserCaches),
        "lastReload":      m.lastReload,
        "vipUsers":        vipUserStatus,
    }
}

// 获取指定vipUser的缓存状态
func (m *Manager) GetVipUserResourcePoolCacheStatus(vipUser string) map[string]interface{} {
    m.mu.RLock()
    defer m.mu.RUnlock()

    // 标准化vipUser
    if vipUser == "" {
        vipUser = "common"
    }

    cache, exists := m.vipUserCaches[vipUser]
    if !exists {
        return map[string]interface{}{
            "exists": false,
        }
    }

    return map[string]interface{}{
        "exists":                true,
        "enabled":               cache.enabled,
        "clusterCount":          cache.clusterCount,
        "extraPoolsCount":       len(cache.extraResourcePools),
        "lastUpdateTime":        cache.lastUpdateTime,
        "defaultPoolConfigured": cache.defaultPoolInfo != nil,
        "extraPools":            m.getExtraPoolNames(cache),
    }
}

// 获取额外资源池名称列表
func (m *Manager) getExtraPoolNames(cache *ResourcePoolCache) []string {
    names := make([]string, 0, len(cache.extraResourcePools))
    for name := range cache.extraResourcePools {
        names = append(names, name)
    }
    return names
}
```

#### 2. 调度器集成（最小化修改）
调度器只需要最小化的修改来集成ResourcePool功能：

**pkg/schedule/scheduler/scheduler.go 的修改**：
- 在Scheduler结构中添加可选的ResourcePool管理器依赖
- 在调度流程中调用ResourcePool管理器进行标签增强
- 保持现有接口不变，向后兼容

**具体修改点**：
1. **结构体添加字段**：添加`resourcePoolManager`字段（可选）
2. **初始化方法修改**：NewScheduler接受ResourcePool管理器参数（可为nil）
3. **调度逻辑修改**：在Schedule方法中调用标签增强逻辑

**核心逻辑（修正后）**：
```go
// 在现有Schedule方法中添加标签增强逻辑
if s.resourcePoolManager != nil {
    // 修正后：使用vipUser而不是clusterID
    s.resourcePoolManager.EnhanceLabelsWithResourcePool(labels, runtime, vipUser)
}
// 继续原有调度逻辑...
```

**说明**：
- 修正后的接口使用`vipUser`参数而不是`clusterID`
- ResourcePool管理器会根据vipUser查找合并后的配置
- 如果vipUser为空，会自动fallback到"common"公共集群配置

**注意**：所有ResourcePool的具体实现代码都在`pkg/poolmanager/resourcepool`模块中，调度器只是调用接口



#### 3. 系统集成（最小化修改）
系统集成只需要最小化的修改：

**初始化阶段**：
- 可选创建ResourcePool管理器实例
- 将ResourcePool管理器传入Scheduler构造函数
- 如果不启用ResourcePool功能，传入nil即可

**运行阶段**：
- Scheduler自动检查ResourcePool管理器是否存在
- 如果存在，自动进行标签增强
- 如果不存在，保持原有调度行为

**关闭阶段**：
- 清理ResourcePool管理器资源

**注意**：所有ResourcePool管理器的创建、初始化、配置管理等代码都在`pkg/poolmanager/resourcepool`模块中实现



## 🔄 Cron模块改造

### 扩缩容逻辑增强

#### 1. 扩容时通过CCEv2接口直接设置K8s节点ResourcePool标签
```go
// pkg/cron/impl/cce_node_control_v2.go
func (m *cceNodeControlV2) ScalingUpWithResourcePool(number int, targetPool string) error {
    if !m.cceClient.IsClusterRunning(m.k8sInfo.CceClusterUUID) {
        return &ClusterNotRunningError{m.k8sInfo.CceClusterUUID}
    }

    // 默认池降级到原有扩容逻辑
    if targetPool == "default" {
        m.logger.Infof("Default pool scaling, using original ScalingUp method")
        return m.ScalingUp(number)
    }

    // 验证目标ResourcePool是否有效
    if !m.isValidResourcePool(targetPool) {
        m.logger.Warnf("ResourcePool %s not found, fallback to default pool", targetPool)
        return m.ScalingUp(number)
    }

    // 获取ResourcePool特定的扩容配置
    scalingOptions := m.getScalingOptionsForPool(targetPool)

    // 执行专用池扩容，在CCE接口中直接设置ResourcePool标签
    err := m.performScalingUpWithResourcePool(number, scalingOptions, targetPool)
    if err != nil {
        return err
    }

    m.logger.Infof("Successfully scaled up %d nodes for ResourcePool: %s", number, targetPool)
    return nil
}

// 修改现有的扩容方法，支持ResourcePool标签
func (m *cceNodeControlV2) performScalingUpWithResourcePool(number int, scalingOptions *api.ScalingUpOptions, targetPool string) error {
    // ... 现有的扩容逻辑 ...

    for _, flavor := range scalingOptions.Flavors {
        zone, stock := m.getScalableZone(flavor, zoneSubnetMap, zonesUnavailableSpecMap)
        if stock > 0 {
            scalingCount := math.Min(math.Min(float64(number), float64(scalingOptions.SingleScalingRequestSize)), float64(stock))

            // 构建扩容参数，包含K8s节点标签
            param := &rpc.ScalingUpParam{
                Number:                          int(scalingCount),
                Zone:                            zone,
                SubnetUUID:                      zoneSubnetMap[zone].VPCSubnetID,
                InstanceType:                    flavor.InstanceType,
                Region:                          scalingOptions.Region,
                BccImageID:                      bccImageID,
                Cpu:                             flavor.Cpu,
                Memory:                          flavor.Memory,
                RootDiskSizeInGb:                flavor.RootDiskSizeInGb,
                SsdMountPath:                    flavor.SsdMountPath,
                CDSType:                         scalingOptions.CDSType,
                SsdDiskSize:                     flavor.SsdDiskSize,
                AdminPass:                       scalingOptions.AdminPass,
                PreDownloadContainerImageScript: downloadScript,
                // 新增：K8s节点标签（更通用的命名）
                Labels:                          m.buildNodeLabels(targetPool),
            }

            m.logger.Infof("[ScalingUpWithResourcePool] scaling up cluster [%s] zone [%s] resourcePool [%s] number [%d]",
                m.k8sInfo.CceClusterUUID, zone, targetPool, param.Number)

            err = m.cceClient.ScalingUp(m.k8sInfo.CceClusterUUID, param)
            if err != nil {
                m.logger.Errorf("[ScalingUpWithResourcePool] scaling up failed: %s", err)
            } else {
                m.logger.Infof("[ScalingUpWithResourcePool] scaling up success for ResourcePool: %s", targetPool)
                return nil
            }
        }
    }
    return errors.New("[ScalingUpWithResourcePool] all zones are abnormal")
}

// 构建K8s节点标签（只有专用池才设置resourcePool标签）
func (m *cceNodeControlV2) buildNodeLabels(targetPool string) map[string]string {
    labels := make(map[string]string)

    // 关键设计：只有非默认池才设置resourcePool标签
    // 默认池不设置任何resourcePool相关标签，保持原有行为
    if targetPool != "default" {
        labels["resourcePool"] = targetPool
        m.logger.V(6).Infof("Setting resourcePool label: %s", targetPool)
    } else {
        m.logger.V(6).Infof("Default pool scaling, no resourcePool label set")
    }

    // 可以在这里添加其他通用的节点标签
    // labels["nodeType"] = "worker"
    // labels["environment"] = "production"

    return labels
}
```

#### 2. 修正CCEv2扩容接口参数
```go
// pkg/cron/rpc/bce_cce.go (修改ScalingUpParam结构)
type ScalingUpParam struct {
    Number int //scaling up node numbers

    Zone       string
    SubnetUUID string
    Region     string

    InstanceType     int    // v1 api use
    MachineSpec      string // v2 api to use
    Cpu              int
    Memory           int
    RootDiskType     string
    RootDiskSizeInGb int

    // cds config
    SsdMountPath string // path
    CDSType      string // cds type
    SsdDiskSize  int    // cds size

    // os image config
    BccImageID string //  目前只有这个起作用
    OsType     string
    OsVersion  string

    AdminPassType string // v1 api use
    AdminPass     string // custom password

    PreDownloadContainerImageScript string // 预下载容器镜像脚本

    // 新增：K8s节点标签（通用设计，支持resourcePool等各种标签）
    Labels map[string]string // K8s节点标签，包含resourcePool等信息
}
```

#### 3. 修正CCEv2客户端实现
```go
// pkg/cron/rpc/bce_cce_v2.go (修改ScalingUp方法)
func (c *CceClientV2) ScalingUp(clusterUUID string, param *ScalingUpParam) error {
    // ... 现有逻辑 ...

    instanceSet := ccev2.InstanceSet{
        InstanceSpec: ccev2type.InstanceSpec{
            ClusterRole:  "node",
            Existed:      false,
            MachineType:  "BCC",
            InstanceType: "N3",
            VPCConfig: ccev2type.VPCConfig{
                AvailableZone:   ccev2type.AvailableZone(param.Zone),
                SecurityGroupID: c.securityGroupId,
                VPCSubnetID:     param.SubnetUUID,
            },
            InstanceResource: ccev2type.InstanceResource{
                MachineSpec:  param.MachineSpec,
                CPU:          param.Cpu,
                MEM:          param.Memory,
                RootDiskType: bccapi.StorageType(param.RootDiskType),
                RootDiskSize: param.RootDiskSizeInGb,
                CDSList: []ccev2type.CDSConfig{
                    {
                        Path:        param.SsdMountPath,
                        StorageType: bccapi.StorageType(param.CDSType),
                        CDSSize:     param.SsdDiskSize,
                        SnapshotID:  "",
                    },
                },
            },
            ImageID: param.BccImageID,
            InstanceOS: ccev2type.InstanceOS{
                ImageType: "System",
                OSType:    ccev2type.OSType(param.OsType),
                OSVersion: param.OsVersion,
                OSArch:    "x86_64 (64bit)",
            },
            NeedEIP:              false,
            Bid:                  false,
            AdminPassword:        param.AdminPass,
            InstanceChargingType: "Postpaid",
            DeployCustomConfig: ccev2type.DeployCustomConfig{
                PostUserScript: param.PreDownloadContainerImageScript,
            },
            // 关键修改：直接在CCE接口中设置K8s标签
            Labels: param.Labels, // 设置包含resourcePool等信息的K8s标签
        },
        Count: param.Number,
    }

    requestArgs.Instances = append(requestArgs.Instances, &instanceSet)
    resp, err := c.cceClient.CreateInstances(&requestArgs)

    // ... 现有错误处理逻辑 ...
}
```

#### 4. 复用现有的K8s节点同步机制
```go
// pkg/cron/impl/k8s_node_control.go (修改现有的ConvertToNodeInfo方法)
func (node *k8sNodeInfo) ConvertToNodeInfo(k8sInfo *api.K8sInfo) *api.NodeInfo {
    // ... 现有逻辑 ...

    // 复制基础的ClusterLabels
    clusterLabels := make(map[string]string)
    for k, v := range k8sInfo.ClusterLabels {
        clusterLabels[k] = v
    }

    // 从K8s节点标签中读取ResourcePool信息
    if resourcePool, exists := node.Labels["resourcePool"]; exists && resourcePool != "" {
        // 验证ResourcePool是否在配置中存在
        if isValidResourcePool(k8sInfo, resourcePool) {
            clusterLabels[api.LabelResourcePool] = resourcePool
        }
        // 如果无效，不设置标签，自动降级到默认池
    }

    nodeInfo := &api.NodeInfo{
        // ... 现有字段 ...
        ClusterLabels: clusterLabels,  // 使用处理后的ClusterLabels
        // ... 其他字段 ...
    }
    return nodeInfo
}
```

#### 3. 按ResourcePool分类扩缩容
```go
// pkg/cron/subtask/scaling_node.go
func (st *Subtask) autoScalingNodeWithResourcePool() {
    if !st.isResourcePoolEnabled() {
        // 降级到原有逻辑
        st.autoScalingNode()
        return
    }

    st.Logger.Infof("[autoScalingNodeWithResourcePool] ResourcePool enabled, analyzing by pools")

    // 按ResourcePool对节点进行分类
    nodesByPool := st.classifyNodesByResourcePool()

    // 分析各ResourcePool的扩缩容需求
    for poolName, poolNodes := range nodesByPool {
        st.analyzeAndExecutePoolScaling(poolName, poolNodes)
    }
}

func (st *Subtask) classifyNodesByResourcePool() map[string][]*api.NodeInfo {
    result := make(map[string][]*api.NodeInfo)

    for _, node := range st.nodeMap {
        poolName := st.getNodeResourcePool(node)
        result[poolName] = append(result[poolName], node)
    }

    for poolName, poolNodes := range result {
        st.Logger.Infof("[classifyNodesByResourcePool] ResourcePool %s has %d nodes", poolName, len(poolNodes))
    }

    return result
}

func (st *Subtask) getNodeResourcePool(node *api.NodeInfo) string {
    if node.ClusterLabels == nil {
        return "default"
    }

    resourcePool, exists := node.ClusterLabels[api.LabelResourcePool]
    if !exists || resourcePool == "" {
        return "default"
    }

    return resourcePool
}

func (st *Subtask) analyzeAndExecutePoolScaling(poolName string, poolNodes []*api.NodeInfo) {
    logger := st.Logger.WithField("resourcePool", poolName)

    // 获取池子特定的扩缩容配置
    scalingOptions := st.getScalingOptionsForPool(poolName)

    // 复用现有的扩缩容计算逻辑，但基于当前池子的节点
    cnt := st.computeScalingNodeCountForPool(poolNodes, scalingOptions)

    if cnt == 0 {
        logger.Infof("[analyzeAndExecutePoolScaling] No scaling needed for pool %s", poolName)
        return
    }

    switch {
    case cnt > 0:
        logger.Infof("[analyzeAndExecutePoolScaling] Try to scale up %d nodes for pool %s", cnt, poolName)
        st.autoScalingUpNodeForPool(cnt, poolName)
    case cnt < 0:
        if scalingOptions.ScalingDown {
            res := st.autoScalingDownNodeForPool(-cnt, poolName, poolNodes)
            logger.Infof("[analyzeAndExecutePoolScaling] Finished offline %d nodes for pool %s, expected %d", res, poolName, -cnt)
        } else {
            logger.Infof("[analyzeAndExecutePoolScaling] Skip scaling down %d nodes for pool %s", -cnt, poolName)
        }
    }
}

func (st *Subtask) autoScalingUpNodeForPool(count int, poolName string) {
    // 根据池子类型选择不同的扩容方法
    if poolName == "default" {
        // 默认池使用原有扩容逻辑，不设置resourcePool标签
        if err := st.ClusterControl.ScalingUp(count); err != nil {
            st.Logger.Errorf("[autoScalingUpNodeForPool] scaling up failed for default pool: %s", err.Error())
        } else {
            st.Logger.Infof("[autoScalingUpNodeForPool] finished scaling up %d nodes for default pool", count)
        }
    } else {
        // 专用池使用ResourcePool扩容方法，设置resourcePool标签
        if err := st.ClusterControl.ScalingUpWithResourcePool(count, poolName); err != nil {
            st.Logger.Errorf("[autoScalingUpNodeForPool] scaling up failed for pool %s: %s", poolName, err.Error())
        } else {
            st.Logger.Infof("[autoScalingUpNodeForPool] finished scaling up %d nodes for pool %s", count, poolName)
        }
    }
}
```

#### 6. 关键设计要点

##### **ResourcePool标签设置流程（CCEv2优化版）**
```
扩容请求 → 自动确定目标ResourcePool → CCEv2接口直接设置K8s标签 → 节点创建完成 → 节点发现时读取标签 → 设置到ClusterLabels
```

##### **CCEv2接口优势**
1. **一步到位**: 创建节点时直接设置K8s标签，无需后续操作
2. **原子性**: 节点创建和标签设置在同一个CCE接口调用中完成
3. **可靠性**: 避免了节点创建成功但标签设置失败的情况
4. **性能优化**: 减少了额外的K8s API调用

##### **降级策略**
1. **K8s标签不存在**: 不设置resourcePool标签，使用默认池格式
2. **配置中不存在**: 即使K8s有标签，也降级到默认池
3. **功能未启用**: 所有节点都使用默认池格式

##### **兼容性保证**
- **存量节点**: K8s中无resourcePool标签，自动使用默认池
- **新节点**: CCEv2接口直接设置K8s标签，读取时设置到ClusterLabels
- **配置变更**: 动态生效，无需重启服务



## ⚙️ 配置管理

### etcd配置结构

#### 1. 集群配置路径
```
/cfc/clusters/{clusterID}/config
```

#### 2. ResourcePool配置结构（双开关设计）
```go
// pkg/api/cron.go (新增结构体定义)
type ResourcePoolConfig struct {
    SchedulingEnabled  bool                     `json:"schedulingEnabled"`  // poolmanager调度开关：控制是否使用ResourcePool进行函数调度
    ScalingEnabled     bool                     `json:"scalingEnabled"`     // cron扩缩容开关：控制是否对ResourcePool进行资源扩缩容管理
    Version            string                   `json:"version"`            // 配置版本
    UserPoolMappings   map[string]string        `json:"userPoolMappings"`   // 用户到资源池的映射关系 uid -> poolName
    ExtraResourcePools map[string]*ResourcePool `json:"extraResourcePools"` // 额外资源池配置（使用指针提高效率）
}

type ResourcePool struct {
    Enabled          bool                      `json:"enabled"`                  // 单个池子开关：控制该池子是否启用
    ResourcePoolInfo `json:"resourcePoolInfo"` // 池子基本信息
    ScalingOptions   *ScalingOptions           `json:"scalingOptions,omitempty"` // 池子扩缩容配置
}

type ResourcePoolInfo struct {
    Description     string   `json:"description"`               // 资源池描述
    ContainerImage  string   `json:"containerImage,omitempty"`  // 容器镜像，可选
    SupportRuntimes []string `json:"supportRuntimes,omitempty"` // 支持的运行时列表
    DedicatedUsers  []string `json:"dedicatedUsers,omitempty"`  // 专属用户列表，为空表示所有用户可用
}

// pkg/api/cron.go (嵌入到现有结构)
type K8sInfo struct {
    // ... 现有字段保持不变 ...
    ResourcePoolInfo   *ResourcePoolInfo   `json:"resourcePoolInfo,omitempty"`   // 默认池配置
    ResourcePoolConfig *ResourcePoolConfig `json:"resourcePoolConfig,omitempty"` // ResourcePool配置
}
```

#### 3. 双开关设计说明

ResourcePool采用双开关设计，提供细粒度控制：

**第一级开关（全局功能开关）**：
| 开关名称 | 控制模块 | 功能描述 | 影响范围 |
|---------|---------|---------|---------|
| `schedulingEnabled` | poolmanager | 控制函数调度时是否使用ResourcePool | 函数冷启动、热启动的资源池选择 |
| `scalingEnabled` | cron | 控制是否对ResourcePool进行扩缩容管理 | 节点自动扩缩容、资源池监控 |

**第二级开关（单个资源池开关）**：
- `ResourcePool.enabled`：控制单个资源池是否启用
- 只有当vipUser级开关启用且单个池子开关启用时，该资源池才生效

**开关组合场景**：

1. **仅调度**：`schedulingEnabled: true, scalingEnabled: false`
   - 适用于已有固定资源池，只需调度功能
   - 函数会根据ResourcePool配置进行调度，但不会自动扩缩容

2. **仅扩缩容**：`schedulingEnabled: false, scalingEnabled: true`
   - 适用于逐步迁移，先建立资源池管理，后启用调度
   - cron会管理ResourcePool节点扩缩容，但函数调度仍使用原有逻辑

3. **完整功能**：`schedulingEnabled: true, scalingEnabled: true`
   - 完整的ResourcePool功能
   - 函数调度使用ResourcePool，同时自动管理资源池扩缩容

4. **完全禁用**：`schedulingEnabled: false, scalingEnabled: false`
   - 回退到原有的调度和扩缩容逻辑

#### 4. 用户专属资源池映射

通过 `userPoolMappings` 字段实现用户到资源池的直接映射：

```go
// 用户专属池映射关系
UserPoolMappings: map[string]string{
    "premium-user-001": "gpu-pool",        // Premium用户专属GPU池
    "vip-user-002": "high-memory-pool",    // VIP用户专属高内存池
}
```

**调度优先级**：
1. 🥇 检查用户专属池映射
2. 🥈 检查专属池是否支持运行时
3. 🥉 降级到通用资源池选择逻辑

#### 5. 完整配置示例（基于原详细设计文档）
```json
{
  // 现有配置保持不变
  "cceClusterUUID": "cce-u4clzq75",
  "k8sOptions": {
    "containerImage": "registry.baidubce.com/cfc_dev/runner:ubuntu1604-0720-java11",
    "dsReleaseConfigMap": {
      "runtimes": {
        "imageID": "registry.baidubce.com/cfc_dev/runtime:unified-0722-multi-os"
      }
    }
  },
  // 现有的扩缩容配置保持不变
  "scalingOptions": {
    "autoScalingType": "threshold",
    "thresholdMap": {
      "128": {
        "unoccupiedRedundancy": 3,
        "scalingUpTrigger": 1.1,
        "scalingDownTrigger": 1.3,
        "ActualRedundancy": 3
      },
      "256": {
        "unoccupiedRedundancy": 2,
        "scalingUpTrigger": 1.2,
        "scalingDownTrigger": 1.4,
        "ActualRedundancy": 2
      }
    },
    "scalingDown": true,
    "scalingTimeout": 600,
    "maxscalingDownSize": 1
  },
  "scalingUpOptions": {
    "singleScalingRequestSize": 1,
    "maxNodeCount": 50
  },
  // 默认资源池配置（必须配置，用于运行时自动选择）
  "resourcePoolInfo": {
    "description": "默认资源池",
    "supportRuntimes": [
      "nodejs12", "nodejs14", "nodejs16",
      "python27", "python36", "python37", "python38",
      "java8", "java11", "php72", "php74", "go118", "dotnet31"
    ]
  },
  // 新增：ResourcePool配置（双开关设计）
  "resourcePoolConfig": {
    "schedulingEnabled": false,  // poolmanager调度开关：默认关闭，不影响现有集群
    "scalingEnabled": false,     // cron扩缩容开关：默认关闭，不影响现有集群
    "version": "3.0",
    "userPoolMappings": {
      "premium-user-001": "gpu-pool",
      "vip-user-002": "high-memory-pool"
    },
    "extraResourcePools": {
      "ubuntu2204-pool": {
        "enabled": true,  // 单个池子开关：启用该池子
        "resourcePoolInfo": {
          "description": "Ubuntu 22.04 通用资源池",
          "containerImage": "registry.baidubce.com/cfc_dev/runner:ubuntu2204-0720-java17",
          "supportRuntimes": ["nodejs18", "nodejs20", "nodejs22", "java17", "python39", "python310"]
        },
        "scalingOptions": {
          "autoScalingType": "node",
          "thresholdMap": {
            "128": {
              "unoccupiedRedundancy": 3,
              "scalingUpTrigger": 1.1,
              "scalingDownTrigger": 1.3,
              "ActualRedundancy": 3
            }
          },
          "scalingDown": true,
          "scalingTimeout": 600,
          "maxscalingDownSize": 1
        }
      },
      "gpu-pool": {
        "enabled": true,  // 单个池子开关：启用该池子
        "resourcePoolInfo": {
          "description": "GPU计算资源池",
          "containerImage": "registry.baidubce.com/cfc_dev/runner:ubuntu2004-gpu-0720",
          "supportRuntimes": ["python39", "python310", "tensorflow", "pytorch"],
          "dedicatedUsers": ["premium-user-001"]  // 专属用户列表
        },
        "scalingOptions": {
          "autoScalingType": "node",
          "thresholdMap": {
            "1024": {
              "unoccupiedRedundancy": 1,
              "scalingUpTrigger": 1.0,
              "scalingDownTrigger": 1.5,
              "ActualRedundancy": 1
            }
          },
          "scalingDown": false,  // GPU节点不自动缩容
          "scalingTimeout": 900,
          "maxscalingDownSize": 0
        }
      },
      "high-memory-pool": {
        "enabled": true,  // 单个池子开关：启用该池子
        "resourcePoolInfo": {
          "description": "高内存混合资源池",
          "containerImage": "registry.baidubce.com/cfc_dev/runner:ubuntu2204-highmem-0720",
          "supportRuntimes": ["java17", "python310", "nodejs18"],
          "dedicatedUsers": ["vip-user-002"]  // 专属用户列表
        },
        "scalingOptions": {
          "autoScalingType": "node",
          "thresholdMap": {
            "512": {
              "unoccupiedRedundancy": 2,
              "scalingUpTrigger": 1.1,
              "scalingDownTrigger": 1.4,
              "ActualRedundancy": 2
            }
          },
          "scalingDown": true,
          "scalingTimeout": 600,
          "maxscalingDownSize": 1
        }
      }
    }
  }
}
```

### 多集群合并配置示例

#### 场景：多个集群共享相同vipUser
```json
// 集群A配置: /cfc/clusters/cluster-a/config
{
  "cceClusterUUID": "cluster-a",
  "nodeConfig": {
    "serviceType": "cfc",
    "clusterLabels": {
      "vipUser": "runtime_base_u22"  // 相同的vipUser
    }
  },
  "resourcePoolConfig": {
    "enabled": true,
    "extraResourcePools": {
      "ubuntu2204-pool": {
        "resourcePoolInfo": {
          "description": "Ubuntu 22.04 pool",
          "supportRuntimes": ["java17", "nodejs18"]
        }
      },
      "gpu-pool": {
        "resourcePoolInfo": {
          "description": "GPU pool for AI workloads",
          "supportRuntimes": ["python3.9", "tensorflow"]
        }
      }
    }
  }
}

// 集群B配置: /cfc/clusters/cluster-b/config
{
  "cceClusterUUID": "cluster-b",
  "nodeConfig": {
    "serviceType": "cfc",
    "clusterLabels": {
      "vipUser": "runtime_base_u22"  // 相同的vipUser
    }
  },
  "resourcePoolConfig": {
    "enabled": true,  // vipUser级开关：启用该vipUser下所有ResourcePool功能
    "extraResourcePools": {
      "ubuntu2204-pool": {  // 同名ResourcePool，会被去重
        "enabled": true,  // 单个池子开关：启用该池子
        "resourcePoolInfo": {
          "description": "Ubuntu 22.04 pool",
          "supportRuntimes": ["java17", "nodejs18"]
        }
      },
      "ai-pool": {  // 不同名ResourcePool，会被合并
        "enabled": true,  // 单个池子开关：启用该池子
        "resourcePoolInfo": {
          "description": "AI specialized pool",
          "supportRuntimes": ["pytorch", "tensorflow2"]
        }
      }
    }
  }
}

// 集群C配置: /cfc/clusters/cluster-c/config
{
  "cceClusterUUID": "cluster-c",
  "nodeConfig": {
    "serviceType": "cfc",
    "clusterLabels": {
      "vipUser": "runtime_base_u22"  // 相同的vipUser
    }
  },
  "resourcePoolConfig": {
    "enabled": true,  // vipUser级开关：启用该vipUser下所有ResourcePool功能
    "extraResourcePools": {
      "edge-pool": {  // 独有的ResourcePool，会被合并
        "enabled": true,  // 单个池子开关：启用该池子
        "resourcePoolInfo": {
          "description": "Edge computing pool",
          "supportRuntimes": ["nodejs16", "go1.19"]
        }
      }
    }
  }
}
```

#### 合并后的逻辑视图
```json
// vipUser="runtime_base_u22"的合并结果
{
  "clusterVipUserLabel": "runtime_base_u22",
  "clusterIDs": ["cluster-a", "cluster-b", "cluster-c"],
  "enabled": true,
  "extraResourcePools": {
    "ubuntu2204-pool": {  // 保留第一个（来自cluster-a）
      "resourcePoolInfo": {
        "description": "Ubuntu 22.04 pool",
        "supportRuntimes": ["java17", "nodejs18"]
      }
    },
    "gpu-pool": {  // 来自cluster-a
      "resourcePoolInfo": {
        "description": "GPU pool for AI workloads",
        "supportRuntimes": ["python3.9", "tensorflow"]
      }
    },
    "ai-pool": {  // 来自cluster-b
      "resourcePoolInfo": {
        "description": "AI specialized pool",
        "supportRuntimes": ["pytorch", "tensorflow2"]
      }
    },
    "edge-pool": {  // 来自cluster-c
      "resourcePoolInfo": {
        "description": "Edge computing pool",
        "supportRuntimes": ["nodejs16", "go1.19"]
      }
    }
  }
}
```

#### 调度效果
```
函数请求: vipUser="runtime_base_u22", runtime="java17"
↓
查找vipUser缓存: 找到合并后的大池子
↓
ResourcePool选择: java17 → ubuntu2204-pool
↓
节点调度: 可以调度到cluster-a、cluster-b、cluster-c中任何支持ubuntu2204-pool的节点
```

### 双开关设计详解

#### 1. 双开关架构设计

ResourcePool功能采用**双开关设计**，每个独立的资源池都有双开关控制：

**第一级：vipUser级别开关**
```
调度开关 (ResourcePoolConfig.SchedulingEnabled)
├── 控制某个vipUser下所有资源池的调度功能
├── 默认值：false（向后兼容）
└── 影响范围：该vipUser下所有资源池的函数调度

扩缩容开关 (ResourcePoolConfig.ScalingEnabled)
├── 控制某个vipUser下所有资源池的扩缩容功能
├── 默认值：false（向后兼容）
└── 影响范围：该vipUser下所有资源池的节点扩缩容
```

**第二级：单个资源池开关**
```
单个池子开关 (ResourcePool.Enabled)
├── 控制单个池子的启用/禁用
├── 默认值：true（池子配置后默认启用）
└── 影响范围：仅当前池子
```

#### 2. 双开关判断逻辑

**poolmanager调度逻辑**：
```go
// pkg/poolmanager/resourcepool/manager.go
func (m *Manager) isValidResourcePool(poolName string, clusterConfig *ResourcePoolCache) bool {
    // 第一级开关：检查该vipUser下所有资源池的调度开关
    if !clusterConfig.enabled {  // enabled字段对应SchedulingEnabled
        m.logger.V(6).Infof("ResourcePool scheduling not enabled for vipUser: %s", clusterConfig.vipUserLabel)
        return false
    }

    // 2. default池总是启用（如果第一级开关开启）
    if poolName == "default" {
        return true
    }

    // 第二级开关：检查单个池子开关
    if pool, exists := clusterConfig.extraResourcePools[poolName]; exists {
        if !pool.Enabled {
            m.logger.V(6).Infof("ResourcePool %s disabled individually", poolName)
            return false
        }
        return true
    }
    return false
}
```

**cron扩缩容逻辑**：
```go
// pkg/cron/resourcepool/helper.go
func (h *Helper) IsResourcePoolScalingEnabled(k8sInfo *api.K8sInfo) bool {
    if k8sInfo == nil || k8sInfo.ResourcePoolConfig == nil {
        return false
    }
    // 第一级开关：检查该vipUser下所有资源池的扩缩容开关
    return k8sInfo.ResourcePoolConfig.ScalingEnabled
}

func (h *Helper) IsValidResourcePool(k8sInfo *api.K8sInfo, poolName string) bool {
    // 第一级开关：检查vipUser级别的扩缩容开关
    if !h.IsResourcePoolScalingEnabled(k8sInfo) {
        return false
    }

    // 第二级开关：检查单个池子开关
    if pool, exists := k8sInfo.ResourcePoolConfig.ExtraResourcePools[poolName]; exists {
        return pool.Enabled
    }
    return false
}
```

#### 3. 双开关组合效果

| vipUser级开关 | 单个池子开关 | 池子状态 | 说明 |
|-------------|-------------|---------|------|
| false       | true        | ❌ 禁用  | vipUser级开关优先级最高 |
| false       | false       | ❌ 禁用  | vipUser级开关优先级最高 |
| true        | true        | ✅ 启用  | 正常启用状态 |
| true        | false       | ❌ 禁用  | 单个池子被禁用 |

#### 4. 开关使用场景

**vipUser级开关适用场景**：
- 🚀 **功能上线**：为特定vipUser启用ResourcePool功能
- 🛑 **紧急回滚**：出现问题时快速关闭特定vipUser的功能
- 🔧 **维护模式**：系统维护时暂时禁用功能
- 📊 **A/B测试**：对比启用前后的系统表现

**单个池子开关适用场景**：
- 🎯 **渐进式部署**：先启用稳定的池子，再逐步启用新池子
- 🔍 **故障隔离**：某个池子有问题时只禁用该池子
- 🧪 **池子测试**：针对特定池子进行测试
- ⚙️ **配置调试**：调试特定池子的配置问题

### 配置管理策略

#### 1. 缓存机制设计
```
启动阶段:
ResourcePool管理器.InitResourcePoolCache(etcdClient, etcdPrefix) → 读取etcd配置 → 加载到内存缓存 → 启动watch机制

运行阶段:
调度请求 → 从内存缓存读取配置 → 快速响应（无etcd访问）

配置更新:
etcd配置变更 → watch机制触发 → 更新内存缓存 → 立即生效

etcd prefix构造（参考cron模块）:
- etcdPrefix来自配置参数（默认："kun1.0"）
- keyPrefix = etcdPrefix + "/k8s/"
- 完整路径示例：kun1.0/k8s/cluster-001

性能优势:
- 启动时一次性加载，运行时零etcd访问
- 读写锁保证并发安全，读操作无阻塞
- 配置变更实时生效，无需重启服务
- 调度延迟大幅降低，吞吐量显著提升
```

#### 2. 配置层级和继承
```
全局默认配置 (K8sInfo.ScalingOptions)
├── 适用于所有ResourcePool
└── 作为默认值和fallback

ResourcePool特定配置 (ResourcePool.ScalingOptions)
├── 覆盖全局配置
├── 只影响当前ResourcePool
└── 支持部分覆盖（只配置需要修改的字段）
```

#### 2. 配置验证规则
```go
// pkg/api/validation.go
func ValidateK8sInfoWithResourcePool(k8sInfo *K8sInfo) error {
    // 验证默认资源池配置（必须配置）
    if k8sInfo.ResourcePoolInfo == nil {
        return fmt.Errorf("default pool resourcePoolInfo is required for runtime auto-selection")
    }

    if len(k8sInfo.ResourcePoolInfo.SupportRuntimes) == 0 {
        return fmt.Errorf("default pool must specify supportRuntimes for runtime auto-selection")
    }

    // 验证ResourcePool配置
    if k8sInfo.ResourcePoolConfig != nil {
        return ValidateResourcePoolConfig(k8sInfo.ResourcePoolConfig)
    }

    return nil
}

func ValidateResourcePoolConfig(config *ResourcePoolConfig) error {
    if config == nil {
        return nil // 可选配置
    }

    for poolName, pool := range config.ExtraResourcePools {
        // 验证池子名称
        if poolName == "" || poolName == "default" {
            return fmt.Errorf("invalid pool name: %s", poolName)
        }

        // 验证运行时配置
        if len(pool.ResourcePoolInfo.SupportRuntimes) == 0 {
            return fmt.Errorf("pool %s must specify supportRuntimes", poolName)
        }

        // 验证扩缩容配置
        if pool.ScalingOptions != nil {
            if err := validateScalingOptions(pool.ScalingOptions); err != nil {
                return fmt.Errorf("invalid scaling options for pool %s: %v", poolName, err)
            }
        }

        // 验证标签
        if err := validatePoolLabels(pool.Labels); err != nil {
            return fmt.Errorf("invalid labels for pool %s: %v", poolName, err)
        }
    }

    return nil
}
```

#### 3. 配置热更新（集成到ResourcePool管理器缓存机制）
```go
// 配置热更新已集成到ResourcePool管理器的缓存机制中
// 参见上面的 watchResourcePoolConfig 和 handleConfigUpdate 方法

// 热更新流程：
// 1. etcd配置变更
// 2. watch机制触发 watchResourcePoolConfig
// 3. handleConfigUpdate 更新内存缓存
// 4. 新的调度请求立即使用新配置

// 监控和调试接口已在上面的Manager实现中定义

// 传统的独立watcher（已被ResourcePool管理器集成，此处仅作参考）
type ResourcePoolConfigWatcher struct {
    etcdClient *etcd.Client
    callbacks  []func(*api.ResourcePoolConfig)
}

func (w *ResourcePoolConfigWatcher) WatchConfigChanges(clusterID string) {
    watchKey := fmt.Sprintf("/cfc/clusters/%s/config", clusterID)

    for resp := range w.etcdClient.Watch(watchKey) {
        for _, event := range resp.Events {
            if event.Type == etcd.EventTypePut {
                var k8sInfo api.K8sInfo
                if err := json.Unmarshal(event.Kv.Value, &k8sInfo); err != nil {
                    log.Errorf("Failed to unmarshal config: %v", err)
                    continue
                }

                // 验证配置
                if err := api.ValidateResourcePoolConfig(k8sInfo.ResourcePoolConfig); err != nil {
                    log.Errorf("Invalid ResourcePool config: %v", err)
                    continue
                }

                // 通知所有监听者
                for _, callback := range w.callbacks {
                    callback(k8sInfo.ResourcePoolConfig)
                }
            }
        }
    }
}
```

### 调度配置

#### 1. 函数调度配置（根据运行时自动选择ResourcePool）
```json
{
  "functionInfo": {
    "name": "myFunction",
    "runtime": "java17",  // 运行时类型，系统根据此自动选择ResourcePool
    "memorySize": 256,
    "commitID": "abc123def456"
  }
}

// 系统自动生成的Pod信息（调度结果）
{
  "podInfo": {
    "name": "pod-xxx",
    "functionName": "myFunction",
    "runtime": "java17",
    "memorySize": 256,
    "nodeID": "node-001",
    "resourcePool": "ubuntu2204-pool"  // 系统自动选择的ResourcePool
  }
}
```

#### 调度流程示例
```
函数请求: runtime=java17
↓
ResourcePool管理器自动选择: 检查默认池 → 不支持java17 → 查找专用池 → 选择ubuntu2204-pool
↓
调度器查找ubuntu2204-pool中的可用节点
↓
找到匹配节点 → 调度成功
找不到匹配节点 → 降级到默认池子
```

#### 2. 节点标签配置
```json
{
  "nodeInfo": {
    "clusterLabels": {
      "vipUser": "common",
      "resourcePool": "ubuntu2204-pool"
    },
    "nodeLabels": {
      "userID": "dummyUSER",
      "vpcConfigID": "vpc-123",
      "zone": "zone-a"
    },
    "systemLabels": {
      "os": "ubuntu22.04",
      "arch": "x86_64",
      "gpu": "none",
      "runtime": "java17,python3.10"
    }
  }
}
```

## 🧪 测试方案

### 单元测试

#### 1. 多集群合并测试
```go
// pkg/poolmanager/resourcepool/manager_test.go
func TestMultiClusterMerging(t *testing.T) {
    logger := logs.NewLogger()
    manager := &Manager{
        logger:        logger,
        vipUserCaches: make(map[string]*ResourcePoolCache),
    }

    // 模拟多个集群有相同的vipUser
    k8sInfo1 := &api.K8sInfo{
        NodeConfig: api.NodeConfig{
            ClusterLabels: map[string]string{
                api.LabelVipUser: "runtime_base_u22",
            },
        },
        ResourcePoolConfig: &api.ResourcePoolConfig{
            Enabled: true,
            ExtraResourcePools: map[string]api.ResourcePool{
                "ubuntu2204-pool": {
                    ResourcePoolInfo: api.ResourcePoolInfo{
                        SupportRuntimes: []string{"java17", "nodejs18"},
                    },
                },
                "gpu-pool": {
                    ResourcePoolInfo: api.ResourcePoolInfo{
                        SupportRuntimes: []string{"python3.9"},
                    },
                },
            },
        },
    }

    k8sInfo2 := &api.K8sInfo{
        NodeConfig: api.NodeConfig{
            ClusterLabels: map[string]string{
                api.LabelVipUser: "runtime_base_u22",
            },
        },
        ResourcePoolConfig: &api.ResourcePoolConfig{
            Enabled: true,
            ExtraResourcePools: map[string]api.ResourcePool{
                "ubuntu2204-pool": { // 同名ResourcePool，应该去重
                    ResourcePoolInfo: api.ResourcePoolInfo{
                        SupportRuntimes: []string{"java17", "nodejs18"},
                    },
                },
                "ai-pool": { // 不同名ResourcePool，应该合并
                    ResourcePoolInfo: api.ResourcePoolInfo{
                        SupportRuntimes: []string{"tensorflow", "pytorch"},
                    },
                },
            },
        },
    }

    // 合并第一个集群
    manager.clusterToVipUser["cluster1"] = "runtime_base_u22"
    manager.mergeClusterToVipUserCache("cluster1", "runtime_base_u22", k8sInfo1)

    // 合并第二个集群
    manager.clusterToVipUser["cluster2"] = "runtime_base_u22"
    manager.mergeClusterToVipUserCache("cluster2", "runtime_base_u22", k8sInfo2)

    // 验证合并结果
    cache := manager.vipUserCaches["runtime_base_u22"]
    assert.NotNil(t, cache)
    assert.Equal(t, 2, len(cache.clusterIDs))
    assert.Equal(t, 3, len(cache.extraResourcePools)) // ubuntu2204-pool, gpu-pool, ai-pool

    // 验证功能：java17应该选择ubuntu2204-pool
    labels := make(api.RequestLabels)
    manager.EnhanceLabelsWithResourcePool(labels, "java17", "runtime_base_u22")
    assert.Equal(t, "ubuntu2204-pool", labels.Get(api.LabelResourcePool))

    // 验证功能：tensorflow应该选择ai-pool
    labels2 := make(api.RequestLabels)
    manager.EnhanceLabelsWithResourcePool(labels2, "tensorflow", "runtime_base_u22")
    assert.Equal(t, "ai-pool", labels2.Get(api.LabelResourcePool))
}

// 🆕 vipUser缓存管理测试（修正后）
func TestVipUserCacheManagement(t *testing.T) {
    logger := logs.NewLogger()
    manager := &Manager{
        logger:        logger,
        vipUserCaches: make(map[string]*ResourcePoolCache),
    }

    // 模拟集群配置
    k8sInfo := &api.K8sInfo{
        NodeConfig: api.NodeConfig{
            ClusterLabels: map[string]string{
                api.LabelVipUser: "runtime_base_u22",
            },
        },
        ResourcePoolConfig: &api.ResourcePoolConfig{
            Enabled: true,
            ExtraResourcePools: map[string]api.ResourcePool{
                "test-pool": {
                    ResourcePoolInfo: api.ResourcePoolInfo{
                        SupportRuntimes: []string{"java17"},
                    },
                },
            },
        },
    }

    // 第一次合并
    manager.mergeClusterToVipUserCache("runtime_base_u22", k8sInfo)

    // 验证缓存创建
    cache := manager.vipUserCaches["runtime_base_u22"]
    assert.NotNil(t, cache)
    assert.Equal(t, 1, cache.clusterCount)

    // 第二次合并（模拟另一个集群）
    manager.mergeClusterToVipUserCache("runtime_base_u22", k8sInfo)

    // 验证计数器递增
    assert.Equal(t, 2, cache.clusterCount)

    // 验证ResourcePool配置正确
    assert.Equal(t, 1, len(cache.extraResourcePools))
    assert.Contains(t, cache.extraResourcePools, "test-pool")
}

// 🆕 全量重新加载测试
func TestFullReloadMechanism(t *testing.T) {
    logger := logs.NewLogger()
    manager := &Manager{
        logger:        logger,
        vipUserCaches: make(map[string]*ResourcePoolCache),
        lastReload:    time.Now().Add(-10 * time.Minute), // 模拟上次重新加载时间
    }

    // 模拟触发全量重新加载
    manager.triggerFullReload()

    // 验证重新加载逻辑被触发（实际测试中需要mock etcd客户端）
    // 这里主要验证防重复加载逻辑
    lastReload := manager.lastReload

    // 立即再次触发，应该被跳过
    manager.triggerFullReload()

    // 验证时间没有更新（因为被跳过了）
    timeDiff := manager.lastReload.Sub(lastReload)
    assert.True(t, timeDiff < 30*time.Second, "Expected reload to be skipped due to rate limiting")
}
```

#### 2. API层测试
```go
// pkg/api/resourcepool_test.go
func TestResourcePoolConfig_Validation(t *testing.T) {
    tests := []struct {
        name    string
        config  *ResourcePoolConfig
        wantErr bool
    }{
        {
            name: "valid config",
            config: &ResourcePoolConfig{
                Enabled: true,
                ExtraResourcePools: map[string]ResourcePool{
                    "ubuntu2204-pool": {
                        ResourcePoolInfo: ResourcePoolInfo{
                            Description: "Ubuntu 22.04 pool",
                        },
                    },
                },
            },
            wantErr: false,
        },
        {
            name: "invalid pool name",
            config: &ResourcePoolConfig{
                Enabled: true,
                ExtraResourcePools: map[string]ResourcePool{
                    "default": {}, // 不允许使用default作为池子名
                },
            },
            wantErr: true,
        },
    }

    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            err := ValidateResourcePoolConfig(tt.config)
            if (err != nil) != tt.wantErr {
                t.Errorf("ValidateResourcePoolConfig() error = %v, wantErr %v", err, tt.wantErr)
            }
        })
    }
}
```

#### 2. 兼容性测试
```go
// pkg/api/label_test.go
func TestGetLabels_ResourcePoolCompatibility(t *testing.T) {
    tests := []struct {
        name     string
        labels   RequestLabels
        expected RequestLabels
    }{
        {
            name: "存量节点 - 无resourcePool标签",
            labels: RequestLabels{
                LabelUserID: "dummyUSER",
            },
            expected: RequestLabels{
                LabelVipUser: "common", // 假设从其他地方获取
            },
        },
        {
            name: "新节点 - 有resourcePool标签",
            labels: RequestLabels{
                LabelUserID:       "dummyUSER",
                LabelResourcePool: "ubuntu2204-pool",
            },
            expected: RequestLabels{
                LabelVipUser:      "common",
                LabelResourcePool: "ubuntu2204-pool",
            },
        },
    }

    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            result, err := tt.labels.GetLabels(ServiceTypeCFC, LabelTypeCluster)
            assert.NoError(t, err)
            assert.Equal(t, tt.expected, result)
        })
    }
}
```

### 集成测试

#### 1. 调度器集成测试
```go
// pkg/schedule/scheduler_integration_test.go
func TestScheduler_ResourcePoolIntegration(t *testing.T) {
    // 设置测试环境
    scheduler := setupTestScheduler()

    // 创建不同ResourcePool的节点
    defaultNode := createTestNode("node-1", "default", 256)
    ubuntu22Node := createTestNode("node-2", "ubuntu2204-pool", 256)
    gpuNode := createTestNode("node-3", "gpu-pool", 1024)

    scheduler.AddNodes([]*api.NodeInfo{defaultNode, ubuntu22Node, gpuNode})

    // 测试用例1: 根据运行时自动选择ResourcePool（复用现有调度机制）
    labels1 := api.NewRequestLabels()
    labels1.Set(api.LabelVipUser, "common")
    node1, err := scheduler.ScheduleWithResourcePool(labels1, "java17", 128)
    assert.NoError(t, err)
    assert.Equal(t, "node-2", node1.ID) // 自动选择到ubuntu2204-pool的节点

    // 测试用例2: 默认池优先选择
    labels2 := api.NewRequestLabels()
    labels2.Set(api.LabelVipUser, "common")
    node2, err := scheduler.ScheduleWithResourcePool(labels2, "java8", 128)
    assert.NoError(t, err)
    assert.Equal(t, "node-1", node2.ID) // 优先使用默认池

    // 测试用例3: GPU池自动选择
    labels3 := api.NewRequestLabels()
    labels3.Set(api.LabelVipUser, "common")
    node3, err := scheduler.ScheduleWithResourcePool(labels3, "tensorflow", 512)
    assert.NoError(t, err)
    assert.Equal(t, "node-3", node3.ID) // 自动选择到gpu-pool的节点
}
```

#### 2. Cron模块集成测试
```go
// pkg/cron/subtask_integration_test.go
func TestSubtask_ResourcePoolScaling(t *testing.T) {
    // 设置测试环境
    subtask := setupTestSubtask()

    // 配置ResourcePool
    k8sInfo := &api.K8sInfo{
        ResourcePoolConfig: &api.ResourcePoolConfig{
            Enabled: true,
            ExtraResourcePools: map[string]api.ResourcePool{
                "ubuntu2204-pool": {
                    ResourcePoolInfo: api.ResourcePoolInfo{
                        Description: "Test pool",
                    },
                    ScalingOptions: &api.ScalingOptions{
                        MinNodes: 2,
                        MaxNodes: 10,
                    },
                },
            },
        },
    }
    subtask.SetK8sInfo(k8sInfo)

    // 测试扩缩容逻辑
    subtask.Run()

    // 验证结果
    // ... 验证扩缩容是否按ResourcePool正确执行
}
```

### 性能测试

#### 1. 调度性能测试
```go
func BenchmarkScheduler_ResourcePool(b *testing.B) {
    scheduler := setupBenchmarkScheduler()

    // 创建大量节点和Pod
    nodes := createTestNodes(1000) // 1000个节点，分布在不同ResourcePool
    pods := createTestPods(10000)  // 10000个Pod调度请求

    scheduler.AddNodes(nodes)

    b.ResetTimer()
    for i := 0; i < b.N; i++ {
        pod := pods[i%len(pods)]
        _, err := scheduler.Schedule(pod)
        if err != nil {
            b.Fatalf("Schedule failed: %v", err)
        }
    }
}
```

#### 2. Redis Key分布测试
```go
func TestRedisKeyDistribution(t *testing.T) {
    // 测试不同ResourcePool的Key是否均匀分布
    keys := generateResourcePoolKeys(10000)

    distribution := analyzeKeyDistribution(keys)

    // 验证分布均匀性
    for shard, count := range distribution {
        expectedCount := 10000 / len(distribution)
        deviation := math.Abs(float64(count-expectedCount)) / float64(expectedCount)
        assert.Less(t, deviation, 0.1, "Key distribution too skewed for shard %d", shard)
    }
}
```

## � 项目结构

### 新增ResourcePool模块
```
pkg/poolmanager/resourcepool/       // 新增：ResourcePool功能模块
├── interface.go                    // ResourcePool管理器接口定义
├── manager.go                      // ResourcePool管理器主要实现
├── cache.go                        // ResourcePool缓存管理
├── config.go                       // 配置加载和验证
├── types.go                        // ResourcePool相关类型定义
└── manager_test.go                 // ResourcePool管理器单元测试
```

### 修改的现有文件
```
pkg/schedule/scheduler/
├── scheduler.go                    // 最小化修改：添加ResourcePool管理器字段和调用逻辑
└── scheduler_test.go               // 修改：添加ResourcePool测试

pkg/api/
├── label.go                        // 修改：添加LabelResourcePool到ClusterLabelKeys
├── k8s_info.go                     // 修改：添加ResourcePool相关结构
└── types.go                        // 修改：ResourcePool类型定义

pkg/cron/impl/
├── cce_node_control_v2.go          // 修改：添加ScalingUpWithResourcePool方法
├── k8s_node_control.go             // 修改：ConvertToNodeInfo处理resourcePool标签
└── scaling_node.go                 // 修改：按ResourcePool分类扩容

pkg/cron/rpc/
├── bce_cce.go                      // 修改：ScalingUpParam添加Labels字段
└── bce_cce_v2.go                   // 修改：CCEv2接口设置K8s标签
```

### 测试文件
```
pkg/poolmanager/resourcepool/
└── manager_test.go                 // ResourcePool管理器单元测试

pkg/schedule/
└── scheduler_integration_test.go   // 调度器集成测试

pkg/cron/
└── resourcepool_scaling_test.go    // ResourcePool扩容测试
```

## �📋 实施计划

### 阶段1: 基础设施准备 (1-2周)

#### 第1周: API和数据结构
- [ ] **Day 1-2**: API结构定义和扩展
  - ResourcePoolConfig结构定义
  - 标签定义扩展 (LabelResourcePool)
  - 配置验证逻辑
- [ ] **Day 3-4**: 兼容性改造核心
  - 修改GetLabels方法 (continue跳过缺失标签)
  - 修改ClusterLabelKeys配置
  - 兼容性单元测试
- [ ] **Day 5**: 代码审查和优化
  - 代码审查
  - 单元测试完善
  - 文档更新

#### 第2周: 辅助工具和基础组件
- [ ] **Day 1-3**: ResourcePool辅助工具开发
  - Helper类实现
  - 节点分类逻辑
  - 标签设置逻辑
- [ ] **Day 4-5**: 配置管理
  - etcd配置结构
  - 配置验证和热更新
  - 配置管理测试

### 阶段2: 核心功能实现 (2-3周)

#### 第3周: ResourcePool管理器模块开发
- [ ] **Day 1-2**: ResourcePool管理器模块架构
  - 创建pkg/poolmanager/resourcepool目录结构
  - 定义ManagerInterface接口
  - 实现基础类型定义(types.go)
- [ ] **Day 3-4**: 缓存机制实现
  - 多集群配置缓存(cache.go)
  - etcd配置加载和watch机制
  - 配置验证逻辑(config.go)
- [ ] **Day 5**: 标签增强逻辑
  - EnhanceLabelsWithResourcePool实现
  - 运行时自动选择ResourcePool
  - 降级策略实现

#### 第4周: 调度器集成
- [ ] **Day 1-2**: Scheduler最小化改造
  - 在Scheduler结构中添加ResourcePool管理器字段（可选）
  - 在Schedule方法中添加标签增强调用逻辑
  - 保持向后兼容性
- [ ] **Day 3-4**: 系统集成测试
  - 集成测试ResourcePool功能
  - 验证向后兼容性
  - 性能测试和优化
  - 调度策略扩展
  - 调度器测试

#### 第4周: Cron模块改造
- [ ] **Day 1-3**: 扩缩容逻辑扩展
  - 按ResourcePool分类扩缩容
  - 池子级别的扩缩容决策
  - 节点标签自动设置
- [ ] **Day 4-5**: 功能集成和测试
  - Cron模块集成测试
  - 扩缩容功能验证

#### 第5周: 集成和优化
- [ ] **Day 1-2**: 系统集成测试
- [ ] **Day 3-4**: 性能优化和测试
- [ ] **Day 5**: 代码审查和文档

### 阶段3: 测试验证 (1-2周)

#### 第6周: 功能测试
- [ ] **Day 1-2**: 兼容性测试
  - 存量节点兼容性验证
  - 新旧格式并存测试
  - 配置热更新测试
- [ ] **Day 3-4**: 功能测试
  - ResourcePool调度测试
  - 扩缩容功能测试
  - 降级策略测试
- [ ] **Day 5**: 性能和压力测试

#### 第7周: 集成测试和修复
- [ ] **Day 1-3**: 集成测试
  - 端到端测试
  - 多集群测试
  - 异常场景测试
- [ ] **Day 4-5**: 问题修复和优化

### 阶段4: 灰度发布 (1周)

#### 第8周: 灰度验证
- [ ] **Day 1-2**: 小规模集群部署
  - 选择1-2个测试集群
  - 部署新版本
  - 功能验证
- [ ] **Day 3-4**: 性能调优和问题修复
  - 性能调优
  - 问题修复
  - 功能验证
- [ ] **Day 5**: 灰度总结和准备全量发布

### 阶段5: 全量发布 (1周)

#### 第9周: 全量部署
- [ ] **Day 1-2**: 全量集群升级
  - 分批升级策略
  - 实时状态检查
  - 回滚准备
- [ ] **Day 3-4**: 验证和优化
  - 功能验证
  - 性能验证
  - 用户反馈收集
- [ ] **Day 5**: 项目总结
  - 文档完善
  - 项目总结
  - 经验沉淀

## ⚠️ 风险评估

### 技术风险

#### 高风险 (需要重点关注)
1. **兼容性风险**
   - **风险**: 存量节点调度失败
   - **影响**: 业务中断
   - **缓解措施**:
     - 充分的兼容性测试
     - 灰度发布策略
     - 快速回滚机制
   - **应急预案**: 立即回滚到原版本

2. **性能风险**
   - **风险**: 调度性能下降
   - **影响**: 调度延迟增加
   - **缓解措施**:
     - 性能基准测试
     - 代码优化
     - 性能检测
   - **应急预案**: 关闭ResourcePool功能，降级到原逻辑

#### 中风险
3. **配置风险**
   - **风险**: 错误的ResourcePool配置导致调度异常
   - **影响**: 部分ResourcePool不可用
   - **缓解措施**:
     - 配置验证机制
     - 配置模板和文档
     - 配置审查流程
   - **应急预案**: 修正配置或临时禁用问题池子

4. **扩缩容风险**
   - **风险**: ResourcePool扩缩容逻辑错误
   - **影响**: 资源浪费或不足
   - **缓解措施**:
     - 扩缩容测试
     - 状态检查
     - 手动干预机制
   - **应急预案**: 手动调整节点数量

#### 低风险
5. **日志风险**
   - **风险**: 日志记录不完整
   - **影响**: 问题排查困难
   - **缓解措施**:
     - 完善日志记录
     - 结构化日志
     - 定期日志检查
   - **应急预案**: 增加临时日志记录

### 业务风险

#### 中风险
1. **运行时配置风险**
   - **风险**: ResourcePool配置错误导致运行时选择异常
   - **影响**: 函数调度到错误的资源池
   - **缓解措施**:
     - 详细的配置验证
     - 完善的降级机制
     - 监控和告警
   - **应急预案**: 提供专门的技术支持

2. **运维复杂度风险**
   - **风险**: 运维复杂度增加
   - **影响**: 运维效率下降
   - **缓解措施**:
     - 运维工具完善
     - 运维培训
     - 自动化程度提升
   - **应急预案**: 增加运维人力支持

### 项目风险

#### 中风险
1. **进度风险**
   - **风险**: 开发进度延期
   - **影响**: 项目延期交付
   - **缓解措施**:
     - 详细的项目计划
     - 定期进度检查
     - 资源调配
   - **应急预案**: 调整项目范围，分阶段交付

2. **资源风险**
   - **风险**: 开发资源不足
   - **影响**: 项目质量下降
   - **缓解措施**:
     - 提前资源规划
     - 跨团队协作
     - 外部支持
   - **应急预案**: 延期交付或减少功能范围

### 风险管理和应对

#### 关键指标
```yaml
# 功能性指标
- 调度成功率: > 99.9%
- 调度延迟: < 100ms (P99)
- ResourcePool节点数量: 按配置范围
- 扩缩容成功率: > 99%
- 配置更新成功率: > 99%
- 系统错误率: < 0.1%
```

#### 应急响应流程
1. **问题发现** → 2. **问题确认** → 3. **影响评估** → 4. **应急处理** → 5. **根因分析** → 6. **长期修复**

#### 回滚策略
- **配置回滚**: 通过etcd快速回滚配置
- **代码回滚**: 通过版本管理快速回滚代码
- **功能降级**: 通过开关快速禁用ResourcePool功能
- **数据恢复**: 通过备份快速恢复关键数据

## 🎯 总结

### 核心价值
1. **技术价值**: 在合适的地方解决合适的问题，通过最小化改动实现运行时ResourcePool选择功能
2. **业务价值**: 支持用户在运行时动态选择合适的资源池和运行环境，提高资源利用率和用户体验
3. **运维价值**: 提供更精细的资源管理能力，支持按池子维度的运维管理
4. **一致性价值**: 通过全量重新加载机制确保配置删除场景下的数据一致性

### 业务价值详解
1. **运行时灵活性**: 用户可以根据业务需求在运行时动态选择合适的资源池和运行环境
2. **环境隔离**: 不同运行时环境（Java8 vs Java17）和OS版本完全隔离，避免环境冲突
3. **精准调度**: 基于ResourcePool和运行时的双重匹配，确保Pod调度到最合适的节点
4. **平滑迁移**: 支持从旧环境到新环境的渐进式迁移，降低业务风险

### 核心场景（配置驱动的自动选择）
```
场景1: 运行时优先使用默认池（推荐场景）
用户: runtime=java8 (自动选择)
配置: etcd中resourcePoolInfo.supportRuntimes包含"java8"
流程: 调度器从etcd读取默认池配置 → 检查supportRuntimes → 发现支持java8 → 使用默认池
结果: 调度到默认池子 (Ubuntu 16.04 + Java8)
💡 优势: 最大兼容性，使用成熟稳定的环境

场景2: 默认池不支持时使用专用池
用户: runtime=java17 (自动选择)
配置:
  - 默认池resourcePoolInfo.supportRuntimes不包含"java17"
  - ubuntu2204-pool的resourcePoolInfo.supportRuntimes包含"java17"
流程: 调度器检查默认池配置 → 不支持java17 → 查找extraResourcePools配置 → 发现ubuntu2204-pool支持
结果: 调度到Ubuntu 22.04 + Java17环境
💡 说明: 完全基于etcd配置，不硬编码运行时支持

场景3: 特殊工作负载自动选择
用户: runtime=tensorflow (自动选择)
配置:
  - 默认池resourcePoolInfo.supportRuntimes不包含"tensorflow"
  - gpu-pool的resourcePoolInfo.supportRuntimes包含"tensorflow"
流程: 调度器检查默认池配置 → 不支持tensorflow → 查找专用池配置 → 发现gpu-pool支持
结果: 调度到GPU节点
💡 说明: 根据etcd配置自动选择GPU池

场景4: 运行时无任何池支持时的兜底
用户: runtime=unknownRuntime (自动选择)
流程: 调度器检查默认池 → 不支持 → 查找extraResourcePools → 都不支持 → 仍然降级到默认池
结果: 调度到默认池子，由默认池处理未知运行时
💡 说明: 保证调度不会失败，总有兜底方案

场景5: 池子不可用时的降级
用户: runtime=java17 (自动选择到ubuntu2204-pool)
流程: ubuntu2204-pool无可用节点 → 自动降级到默认池子
结果: 调度到默认池子的节点
```

### 自动选择策略说明
```
根据运行时自动选择ResourcePool的优先级:
1. 🥇 默认池支持 → 使用默认池 (最高优先级)
2. 🥈 专用池支持 → 使用专用池 (次优选择)
3. 🥉 都不支持 → 降级到默认池 (兜底保证)

配置依赖:
• 默认池配置: etcd中K8sInfo.resourcePoolInfo.supportRuntimes必须配置
• 专用池配置: etcd中ResourcePoolConfig.extraResourcePools[].resourcePoolInfo.supportRuntimes
• 配置验证: 启动时验证所有池子的运行时配置完整性

设计理念:
• 默认池优先: 保证最大兼容性和稳定性
• 专用池补充: 只在默认池不支持时才使用
• 兜底保证: 确保调度永远不会失败
• 用户无感知: 完全自动化，用户只需指定运行时
• 配置驱动: 所有运行时支持信息从etcd配置中获取，不硬编码
```

### 关键成功因素
1. **完美的向后兼容性**: 确保存量节点和业务不受影响
2. **优雅的实现方案**: 通过智能的兼容策略实现零风险升级
3. **全面的测试验证**: 确保功能正确性和系统稳定性
4. **详细的实施计划**: 确保项目按时高质量交付
5. **数据一致性保障**: 通过全量重新加载机制解决配置删除检测问题

### 设计演进总结
本设计经历了从复杂到简化的演进过程：
1. **初始设计**: 单集群ResourcePool支持
2. **多集群扩展**: 添加多集群合并和引用计数
3. **设计简化**: 移除过度工程化的clusterToVipUser映射，采用vipUser-centric设计
4. **一致性增强**: 添加全量重新加载机制解决配置删除检测问题

### 重要设计变更说明

#### 最终方案：定期重新加载 + 配置变更时触发重新加载
经过深入分析，最终采用简化的可靠方案：

1. **定期全量重新加载**: 保留5分钟定期重新加载，确保最终一致性
2. **配置变更时触发重新加载**: 当检测到ResourcePool配置变更时，立即触发全量重新加载
3. **简化架构**: 移除复杂的即时状态跟踪，避免过度设计

#### 设计考虑
**为什么不采用即时状态跟踪？**
1. **ResourcePool内容复杂**: 不仅要跟踪启用状态，还要精确管理每个ResourcePool的引用计数
2. **多集群共享问题**: 同vipUser的多个集群可能共享某些ResourcePool，需要复杂的引用计数机制
3. **实现复杂度**: 三层状态跟踪（启用状态、ResourcePool引用计数、集群配置映射）过于复杂
4. **可靠性优先**: 简单的全量重新加载更可靠，避免状态不一致的风险

#### 实现方案
```go
// 配置变更时立即触发全量重新加载
func (m *Manager) handleClusterConfigUpdate(clusterID string, configData []byte) error {
    var k8sInfo api.K8sInfo
    if err := json.Unmarshal(configData, &k8sInfo); err != nil {
        return err
    }

    enabled := k8sInfo.ResourcePoolConfig != nil && k8sInfo.ResourcePoolConfig.Enabled

    m.logger.Infof("ResourcePool config changed for cluster %s (enabled: %v), triggering full reload",
        clusterID, enabled)

    // 配置变更时触发全量重新加载，确保立即生效
    go m.triggerFullReload()

    return nil
}
```

#### 最终方案的核心优势
1. **快速响应**: 配置变更时立即触发全量重新加载，通常在1-2秒内生效
2. **连接降级处理**: 当etcd watch长连接断开时，定期重新加载提供了可靠的降级处理机制
3. **自动故障恢复**: 即使某些增量更新失败或丢失，定期重新加载也能自动修复数据不一致问题
4. **架构简化**: 无需复杂的状态跟踪和引用计数，代码简洁可维护
5. **运维简化**: 无需复杂的状态维护和故障排查，系统具有自愈能力
6. **扩展性保障**: 支持任意数量的集群配置变更，无额外状态跟踪开销
7. **性能可控**: 5分钟的重新加载频率对系统性能影响很小，但能提供强一致性保障
8. **可靠性优先**: 避免复杂状态管理可能导致的数据不一致问题

#### 性能分析
- **配置变更响应时间**: 1-2秒（触发全量重新加载）
- **定期重新加载开销**: 每5分钟一次，对系统性能影响微乎其微
- **内存开销**: 只存储最终的vipUser缓存，无额外状态跟踪开销
- **网络开销**: 全量重新加载时一次性读取所有配置，网络请求次数最少

**本设计方案通过深入分析现有架构，采用最优雅的兼容策略和简化设计，实现了运行时ResourcePool选择功能的完美集成，同时通过全量重新加载机制确保了数据一致性和系统可靠性，是一个技术上先进、实施上可行的优秀解决方案。** 🚀

---

## 🧪 测试用例设计

### 测试策略概述

基于双开关设计和服务配置分离部署的特点，设计全面的测试用例覆盖以下场景：
- **服务部署时序**：先服务后配置、先配置后服务
- **开关状态变更**：调度开关、扩缩容开关、单个池子开关的启用/禁用
- **配置内容变更**：supportRuntimes修改、池子添加/删除
- **故障场景**：服务重启、配置回滚、异常恢复

### 测试环境准备

#### 基础配置
```json
// 初始集群配置（ResourcePool功能关闭）
{
  "cceClusterUUID": "test-cluster-001",
  "resourcePoolInfo": {
    "description": "默认资源池",
    "supportRuntimes": ["java8", "nodejs12", "python27"]
  },
  "resourcePoolConfig": {
    "enabled": false,  // vipUser级开关：关闭
    "version": "1.0",
    "extraResourcePools": {
      "ubuntu2204-pool": {
        "enabled": true,  // 单个池子开关：开启
        "resourcePoolInfo": {
          "description": "Ubuntu 22.04 资源池",
          "supportRuntimes": ["java17", "nodejs18", "python39"]
        }
      },
      "gpu-pool": {
        "enabled": false,  // 单个池子开关：关闭
        "resourcePoolInfo": {
          "description": "GPU 资源池",
          "supportRuntimes": ["tensorflow", "pytorch"]
        }
      }
    }
  }
}
```

### 测试用例分类

#### 1. 服务部署时序测试

##### 测试用例 1.1：先上线服务，后上线配置
**测试步骤**：
1. 部署新版本 poolmanager 和 cron 服务（支持ResourcePool功能）
2. 保持现有配置不变（`enabled: false`）
3. 发送函数调用请求（runtime=java8）
4. 更新配置启用ResourcePool功能（`enabled: true`）
5. 发送函数调用请求（runtime=java17）

**预期结果**：
```
步骤3: 调度到默认池（向后兼容）
- 使用现有调度逻辑
- 不生成ResourcePool相关Redis Key
- 函数正常执行

步骤5: 调度到ubuntu2204-pool
- 生成新格式Redis Key: kun:cold_node_set:ubuntu2204-pool:java17:...
- 调度到ubuntu2204-pool节点
- 函数正常执行
```

##### 测试用例 1.2：先上线配置，后上线服务
**测试步骤**：
1. 更新配置启用ResourcePool功能（`enabled: true`）
2. 发送函数调用请求（runtime=java17）
3. 部署新版本 poolmanager 和 cron 服务
4. 发送函数调用请求（runtime=java17）

**预期结果**：
```
步骤2: 调度到默认池（旧服务不识别新配置）
- 使用旧版调度逻辑
- 生成旧格式Redis Key
- 函数正常执行

步骤4: 调度到ubuntu2204-pool（新服务识别新配置）
- 生成新格式Redis Key
- 调度到ubuntu2204-pool节点
- 函数正常执行
```

#### 2. 开关状态变更测试

##### 测试用例 2.1：vipUser级开关启用/禁用
**测试步骤**：
1. 初始状态：`enabled: false`
2. 发送请求：runtime=java17
3. 启用vipUser级开关：`enabled: true`
4. 发送请求：runtime=java17
5. 禁用vipUser级开关：`enabled: false`
6. 发送请求：runtime=java17

**预期结果**：
```
步骤2: 调度到默认池
- ResourcePool功能未启用
- 使用默认调度逻辑

步骤4: 调度到ubuntu2204-pool
- ResourcePool功能启用
- 根据runtime自动选择池子

步骤6: 调度到默认池
- ResourcePool功能禁用
- 回退到默认调度逻辑
- 现有ubuntu2204-pool节点开始回收
```

##### 测试用例 2.2：单个池子开关启用/禁用
**测试步骤**：
1. 初始状态：vipUser级开关=true，ubuntu2204-pool开关=true
2. 发送请求：runtime=java17
3. 禁用ubuntu2204-pool：`ubuntu2204-pool.enabled: false`
4. 发送请求：runtime=java17
5. 启用ubuntu2204-pool：`ubuntu2204-pool.enabled: true`
6. 发送请求：runtime=java17

**预期结果**：
```
步骤2: 调度到ubuntu2204-pool
- 池子正常启用

步骤4: 调度到默认池（降级）
- ubuntu2204-pool被禁用
- 自动降级到默认池
- ubuntu2204-pool节点开始回收

步骤6: 调度到ubuntu2204-pool
- 池子重新启用
- 恢复正常调度
```

#### 3. 配置内容变更测试

##### 测试用例 3.1：supportRuntimes修改
**测试步骤**：
1. 初始配置：ubuntu2204-pool支持["java17", "nodejs18", "python39"]
2. 发送请求：runtime=java17 → 调度到ubuntu2204-pool
3. 发送请求：runtime=python310 → 调度到默认池
4. 修改配置：ubuntu2204-pool支持["java17", "nodejs18", "python39", "python310"]
5. 发送请求：runtime=python310 → 调度到ubuntu2204-pool
6. 修改配置：ubuntu2204-pool支持["nodejs18", "python39"]（移除java17）
7. 发送请求：runtime=java17 → 调度到默认池

**预期结果**：
```
步骤2: ✅ 调度到ubuntu2204-pool（支持java17）
步骤3: ✅ 调度到默认池（ubuntu2204-pool不支持python310）
步骤5: ✅ 调度到ubuntu2204-pool（新增支持python310）
步骤7: ✅ 调度到默认池（ubuntu2204-pool不再支持java17）
```

##### 测试用例 3.2：池子添加/删除
**测试步骤**：
1. 初始配置：只有ubuntu2204-pool
2. 发送请求：runtime=tensorflow → 调度到默认池
3. 添加gpu-pool配置（支持tensorflow）
4. 启用gpu-pool：`gpu-pool.enabled: true`
5. 发送请求：runtime=tensorflow → 调度到gpu-pool
6. 删除gpu-pool配置
7. 发送请求：runtime=tensorflow → 调度到默认池

**预期结果**：
```
步骤2: ✅ 调度到默认池（无专用池支持tensorflow）
步骤5: ✅ 调度到gpu-pool（新增专用池）
步骤7: ✅ 调度到默认池（专用池被删除，现有gpu节点开始回收）
```

#### 4. 复杂场景组合测试

##### 测试用例 4.1：先上线配置，再上线服务，再修改开关
**测试步骤**：
1. 更新配置：`enabled: true`，ubuntu2204-pool.enabled: true
2. 发送请求：runtime=java17（旧服务）
3. 部署新服务
4. 发送请求：runtime=java17（新服务）
5. 禁用vipUser级开关：`enabled: false`
6. 发送请求：runtime=java17
7. 启用vipUser级开关，禁用ubuntu2204-pool：`enabled: true, ubuntu2204-pool.enabled: false`
8. 发送请求：runtime=java17

**预期结果**：
```
步骤2: 调度到默认池（旧服务不识别新配置）
步骤4: 调度到ubuntu2204-pool（新服务识别配置）
步骤6: 调度到默认池（vipUser级开关禁用）
步骤8: 调度到默认池（池子开关禁用）
```

##### 测试用例 4.2：服务重启期间的配置变更
**测试步骤**：
1. 初始状态：ResourcePool功能启用
2. 发送请求：runtime=java17 → ubuntu2204-pool
3. 重启poolmanager服务
4. 在重启期间修改配置：禁用ubuntu2204-pool
5. 服务重启完成
6. 发送请求：runtime=java17

**预期结果**：
```
步骤2: ✅ 调度到ubuntu2204-pool
步骤6: ✅ 调度到默认池（服务重启后读取新配置）
```

#### 5. 故障场景测试

##### 测试用例 5.1：etcd配置损坏恢复
**测试步骤**：
1. 正常状态：ResourcePool功能启用
2. 模拟etcd配置损坏（删除resourcePoolConfig字段）
3. 发送请求：runtime=java17
4. 恢复etcd配置
5. 等待配置重新加载（最多5分钟）
6. 发送请求：runtime=java17

**预期结果**：
```
步骤3: 调度到默认池（配置损坏，功能自动禁用）
步骤6: 调度到ubuntu2204-pool（配置恢复，功能重新启用）
```

##### 测试用例 5.2：配置回滚测试
**测试步骤**：
1. 版本A配置：ubuntu2204-pool支持["java17"]
2. 发送请求：runtime=java17 → ubuntu2204-pool
3. 版本B配置：ubuntu2204-pool支持["nodejs18"]（移除java17）
4. 发送请求：runtime=java17 → 默认池
5. 回滚到版本A配置
6. 发送请求：runtime=java17 → ubuntu2204-pool

**预期结果**：
```
步骤2: ✅ 调度到ubuntu2204-pool
步骤4: ✅ 调度到默认池（配置变更生效）
步骤6: ✅ 调度到ubuntu2204-pool（回滚生效）
```

### 测试验证方法

#### 1. Redis Key验证
```bash
# 检查生成的Redis Key格式
redis-cli --cluster call 127.0.0.1:7000 keys "kun:cold_node_set:*"

# 预期结果示例：
# ResourcePool功能禁用时：
kun:cold_node_set:java8:memory_128:vipUser_common:cluster_test-001

# ResourcePool功能启用时：
kun:cold_node_set:ubuntu2204-pool:java17:memory_128:vipUser_common:cluster_test-001
kun:cold_node_set:default:java8:memory_128:vipUser_common:cluster_test-001
```

#### 2. 调度结果验证
```bash
# 检查Pod调度到的节点标签
kubectl get pods -n poolmgr -o wide --show-labels | grep resourcePool

# 预期结果示例：
# 调度到ubuntu2204-pool时：
pod-xxx   node-001   resourcePool=ubuntu2204-pool

# 调度到默认池时：
pod-xxx   node-002   resourcePool=default
```

#### 3. 日志验证
```bash
# poolmanager日志
grep "Enhanced labels with ResourcePool" /var/log/poolmanager.log
grep "Selected ResourcePool.*for runtime" /var/log/poolmanager.log

# cron日志
grep "ResourcePool.*enabled" /var/log/cron.log
grep "Recycling.*nodes from ResourcePool" /var/log/cron.log
```

#### 4. 配置缓存验证
```bash
# 检查ResourcePool管理器缓存状态
curl http://poolmanager:8080/debug/resourcepool/cache

# 预期返回示例：
{
  "vipUserCaches": {
    "common": {
      "enabled": true,
      "extraResourcePools": {
        "ubuntu2204-pool": {
          "enabled": true,
          "supportRuntimes": ["java17", "nodejs18"]
        }
      }
    }
  }
}
```

### 自动化测试脚本

#### 测试脚本框架
```bash
#!/bin/bash
# ResourcePool功能自动化测试脚本

set -e

# 测试配置
CLUSTER_ID="test-cluster-001"
ETCD_PREFIX="kun1.0"
POOLMANAGER_ENDPOINT="http://poolmanager:8080"
REDIS_ENDPOINT="127.0.0.1:7000"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 配置管理函数
update_cluster_config() {
    local config_file=$1
    log_info "Updating cluster config: $config_file"
    etcdctl put "${ETCD_PREFIX}/k8s/${CLUSTER_ID}" "$(cat $config_file)"
    sleep 2  # 等待配置生效
}

# 函数调用测试
test_function_call() {
    local runtime=$1
    local expected_pool=$2

    log_info "Testing function call: runtime=$runtime, expected_pool=$expected_pool"

    # 发送函数调用请求
    local response=$(curl -s -X POST "${POOLMANAGER_ENDPOINT}/invoke" \
        -H "Content-Type: application/json" \
        -d "{\"runtime\":\"$runtime\",\"memory\":128}")

    # 检查Redis Key
    local redis_keys=$(redis-cli --cluster call $REDIS_ENDPOINT keys "kun:cold_node_set:*" | grep "$runtime")

    if [[ "$redis_keys" == *"$expected_pool"* ]]; then
        log_info "✅ Test passed: $runtime -> $expected_pool"
        return 0
    else
        log_error "❌ Test failed: $runtime -> expected $expected_pool, got $redis_keys"
        return 1
    fi
}

# 测试用例1：先服务后配置
test_case_1() {
    log_info "=== 测试用例1：先上线服务，后上线配置 ==="

    # 步骤1：部署新服务（已完成）
    log_info "Step 1: 新服务已部署"

    # 步骤2：保持配置关闭
    update_cluster_config "configs/resourcepool_disabled.json"

    # 步骤3：测试java8调用（应该使用默认池）
    test_function_call "java8" "default"

    # 步骤4：启用ResourcePool功能
    update_cluster_config "configs/resourcepool_enabled.json"

    # 步骤5：测试java17调用（应该使用ubuntu2204-pool）
    test_function_call "java17" "ubuntu2204-pool"

    log_info "✅ 测试用例1完成"
}

# 测试用例2：开关状态变更
test_case_2() {
    log_info "=== 测试用例2：开关状态变更 ==="

    # vipUser级开关测试
    update_cluster_config "configs/global_switch_off.json"
    test_function_call "java17" "default"

    update_cluster_config "configs/global_switch_on.json"
    test_function_call "java17" "ubuntu2204-pool"

    # 单个池子开关测试
    update_cluster_config "configs/pool_switch_off.json"
    test_function_call "java17" "default"

    update_cluster_config "configs/pool_switch_on.json"
    test_function_call "java17" "ubuntu2204-pool"

    log_info "✅ 测试用例2完成"
}

# 主测试流程
main() {
    log_info "开始ResourcePool功能自动化测试"

    # 检查环境
    if ! command -v etcdctl &> /dev/null; then
        log_error "etcdctl not found"
        exit 1
    fi

    if ! command -v redis-cli &> /dev/null; then
        log_error "redis-cli not found"
        exit 1
    fi

    # 执行测试用例
    test_case_1
    test_case_2

    log_info "🎉 所有测试用例完成"
}

# 执行主函数
main "$@"
```

#### 配置文件示例
```json
// configs/resourcepool_disabled.json
{
  "cceClusterUUID": "test-cluster-001",
  "resourcePoolInfo": {
    "description": "默认资源池",
    "supportRuntimes": ["java8", "nodejs12", "python27"]
  },
  "resourcePoolConfig": {
    "enabled": false,
    "version": "1.0",
    "extraResourcePools": {
      "ubuntu2204-pool": {
        "enabled": true,
        "resourcePoolInfo": {
          "description": "Ubuntu 22.04 资源池",
          "supportRuntimes": ["java17", "nodejs18", "python39"]
        }
      }
    }
  }
}
```

```json
// configs/resourcepool_enabled.json
{
  "cceClusterUUID": "test-cluster-001",
  "resourcePoolInfo": {
    "description": "默认资源池",
    "supportRuntimes": ["java8", "nodejs12", "python27"]
  },
  "resourcePoolConfig": {
    "enabled": true,
    "version": "1.0",
    "extraResourcePools": {
      "ubuntu2204-pool": {
        "enabled": true,
        "resourcePoolInfo": {
          "description": "Ubuntu 22.04 资源池",
          "supportRuntimes": ["java17", "nodejs18", "python39"]
        }
      }
    }
  }
}
```

### 测试执行计划

#### 阶段1：单元测试（开发阶段）
**时间**：开发完成后
**范围**：
- ResourcePool管理器单元测试
- 开关逻辑单元测试
- 配置验证单元测试
- Redis Key生成测试

**验收标准**：
- 单元测试覆盖率 ≥ 90%
- 所有测试用例通过
- 代码静态检查无错误

#### 阶段2：集成测试（测试环境）
**时间**：单元测试通过后
**范围**：
- poolmanager + cron 模块集成测试
- etcd配置管理集成测试
- Redis调度集成测试
- 多集群配置合并测试

**验收标准**：
- 所有集成测试用例通过
- 配置变更在2秒内生效
- 无内存泄漏和性能问题

#### 阶段3：端到端测试（预发环境）
**时间**：集成测试通过后
**范围**：
- 完整调度链路测试
- 真实函数执行测试
- 故障恢复测试
- 性能压力测试

**验收标准**：
- 函数调用成功率 ≥ 99.9%
- 调度延迟增加 ≤ 10ms
- 系统稳定运行24小时

#### 阶段4：生产验证（灰度发布）
**时间**：预发测试通过后
**范围**：
- 小流量灰度验证
- 监控指标验证
- 用户体验验证
- 回滚能力验证

**验收标准**：
- 灰度期间无故障
- 关键指标无异常
- 用户无感知升级

### 测试数据和监控

#### 关键指标监控
```yaml
# 监控指标定义
metrics:
  # 功能指标
  - name: resourcepool_enabled_clusters
    description: 启用ResourcePool功能的集群数量
    type: gauge

  - name: resourcepool_selection_total
    description: ResourcePool选择次数统计
    type: counter
    labels: [pool_name, runtime]

  - name: resourcepool_config_reload_total
    description: 配置重新加载次数
    type: counter
    labels: [trigger_type]  # watch/periodic

  # 性能指标
  - name: resourcepool_selection_duration
    description: ResourcePool选择耗时
    type: histogram

  - name: resourcepool_cache_hit_ratio
    description: 缓存命中率
    type: gauge

  # 错误指标
  - name: resourcepool_config_errors_total
    description: 配置错误次数
    type: counter
    labels: [error_type]
```

#### 测试数据集
```yaml
# 测试运行时列表
test_runtimes:
  - java8      # 默认池支持
  - java11     # 默认池支持
  - java17     # ubuntu2204-pool支持
  - nodejs12   # 默认池支持
  - nodejs18   # ubuntu2204-pool支持
  - python27   # 默认池支持
  - python39   # ubuntu2204-pool支持
  - tensorflow # gpu-pool支持
  - pytorch    # gpu-pool支持
  - unknown    # 无池支持（测试降级）

# 测试配置变更序列
config_changes:
  1. vipUser级开关：false -> true -> false
  2. 池子开关：true -> false -> true
  3. 运行时支持：添加 -> 删除 -> 修改
  4. 池子配置：添加 -> 删除 -> 修改
  5. 复合变更：多个配置同时修改
```

### 验收标准总结

#### 功能验收标准
✅ **向后兼容性**：
- 现有集群无需修改配置即可正常运行
- 现有函数调用行为完全不变
- 现有监控和日志格式保持兼容

✅ **功能正确性**：
- 双开关逻辑正确实现
- 运行时自动选择准确无误
- 配置变更实时生效（≤2秒）
- 故障场景自动降级

✅ **性能要求**：
- 调度延迟增加 ≤ 10ms
- 内存使用增加 ≤ 50MB
- CPU使用增加 ≤ 5%
- 配置重新加载耗时 ≤ 1秒

✅ **可靠性要求**：
- 系统可用性 ≥ 99.9%
- 配置错误自动恢复
- 服务重启后状态正确
- 无内存泄漏和资源泄漏

✅ **可运维性**：
- 完整的日志记录
- 详细的监控指标
- 清晰的错误信息
- 便捷的调试接口

#### 测试完成标志
🎯 **所有测试用例通过**：单元测试、集成测试、端到端测试全部通过
🎯 **性能指标达标**：延迟、吞吐量、资源使用等指标符合要求
🎯 **稳定性验证**：连续运行24小时无故障
🎯 **文档完整**：用户手册、运维手册、故障排查手册齐全
🎯 **团队确认**：开发、测试、运维团队全部确认可以上线

---

**通过以上全面的测试用例设计和验收标准，确保ResourcePool双开关功能的高质量交付，为用户提供稳定可靠的多环境资源池调度能力。** 🚀
