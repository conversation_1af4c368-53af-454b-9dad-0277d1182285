# CFC调度、资源管理重构技术设计文档

## 1. 背景与现状分析

### 1.1 当前架构概述

#### 1.1.1 调度架构
- **eventhub**：负责增加label，处理用户请求
- **poolmanager**：负责调度，采用用户维度在集群层面的调度
- **调度粒度**：集群级调度，整个集群作为一个大池子

#### 1.1.2 资源管理架构
- **cron模块**：负责集群维度的资源伸缩和初始化
- **管理粒度**：集群级资源管理
- **配置方式**：通过etcd动态配置管理集群信息

#### 1.1.3 当前etcd配置结构
```json
{
  "cceClusterUUID": "cce-u4clzq75",
  "k8sOptions": {
    "containerImage": "registry.baidubce.com/cfc_dev/runner:ubuntu1604-0720-java11",
    "dsReleaseConfigMap": {
      "runtimes": {
        "imageID": "registry.baidubce.com/cfc_dev/runtime:ubuntu1604-0722-py3.9-java"
      }
    }
  }
}
```

### 1.2 核心问题分析

#### 1.2.1 OS兼容性问题
- **现状**：common集群使用ubuntu1604基础镜像
- **问题**：高版本运行时（如nodejs18+）依赖高版本系统库（glibc等），无法在当前集群运行
- **当前解决方案**：为新运行时创建新集群
- **痛点**：随着运行时版本不断变化，集群数量持续增长，维护成本过高

#### 1.2.2 运行时镜像维护成本高
- **现状**：维护两个版本的runtimes镜像
- **问题**：每次新增运行时需要在两个基础镜像上适配
- **痛点**：双倍的构建、测试、部署工作量

#### 1.2.3 资源扩展性不足
- **现状**：只支持动态内存分配，通过Redis zset的score保存可用内存
- **问题**：扩展CPU、GPU资源时存在多步操作一致性问题
- **痛点**：如果新增CPU资源的zset，会涉及内存、CPU两步操作的原子性问题

#### 1.2.4 配置管理复杂
- **现状**：daemonset配置静态化，更新需要重启cron模块
- **问题**：集群镜像升级复杂，影响业务连续性
- **痛点**：运维复杂度高，无法热更新

## 2. 目标功能设计

### 2.1 核心目标

#### 2.1.1 更细粒度调度和资源管理
**目标**：从集群级"大池子"转换为节点级"小池子"，支持多种基础OS镜像共存

**实现方案**：
- 在cron扩容时为节点设置`resourcePool`标签
- 通过resourcePool标识小池子（如ubuntu1604-pool、ubuntu2204-pool、gpu-pool）
- 利用etcd动态配置能力管理resourcePool

#### 2.1.2 运行时镜像收敛
**目标**：合并为一个runtimes镜像，根据不同OS挂载适配的runtime

**实现方案**：
- 构建统一的runtime镜像
- 根据节点OS类型动态挂载对应的runtime版本
- 减少镜像维护成本

#### 2.1.3 向后兼容性保证
**目标**：兼容旧的调度和资源管理，避免影响大客户集群

**实现方案**：
- 集群级开关控制新旧功能
- 未开启时维持现状
- 开启时使用新的调度和资源管理

#### 2.1.4 动态配置化
**目标**：daemonset yaml动态配置化，简化集群镜像升级

**实现方案**：
- 配置热更新，无需重启cron模块
- 动态读取runtimes等镜像的yaml配置

#### 2.1.5 多资源支持架构预留
**目标**：保持现有内存资源管理，确保架构能够方便后续兼容CPU和GPU资源

**实现方案**：
- 预留多资源接口和配置字段
- 建立可扩展的Redis Key命名规范
- 保证架构向后兼容性

**当前范围**：只实现内存资源管理，CPU和GPU资源作为后续扩展

### 2.2 关键设计问题

#### 2.2.1 运行时与资源池映射关系维护
**问题**：运行时和资源池的映射关系应该维护在哪里？

**选项分析**：
1. **硬编码在eventhub**：资源池名称分别维护在etcd和eventhub中，一致性难保证
2. **维护在etcd**：eventhub需要额外拉取所有集群配置，耦合度高
3. **统一配置方案**：在集群etcd配置中统一维护资源池定义和运行时映射

**推荐方案**：统一配置方案（详见第3章）

#### 2.2.2 集群级开关设计
**问题**：多集群环境下，各模块如何监听开关变化？

**设计要求**：
- 集群级开关控制
- 各模块能够监听配置变化
- 支持渐进式迁移

## 3. 统一配置方案设计

### 3.1 配置架构设计

#### 3.1.1 统一配置原则
基于demand.md中的问题分析，采用统一配置方案解决运行时与资源池映射关系维护问题：

**核心原则**：
- 所有ResourcePool相关配置统一维护在集群etcd配置中
- eventhub和cron模块共享同一份配置
- 避免配置分散导致的一致性问题

#### 3.1.2 扩展后的etcd配置结构
```json
{
  "cceClusterUUID": "cce-u4clzq75",
  "k8sOptions": {
    "containerImage": "registry.baidubce.com/cfc_dev/runner:ubuntu1604-0720-java11",
    "dsReleaseConfigMap": {
      "runtimes": {
        "imageID": "registry.baidubce.com/cfc_dev/runtime:unified-0722-multi-os"
      }
    }
  },

  // 新增：ResourcePool配置
  "resourcePoolConfig": {
    "enabled": false,  // 集群级开关
    "version": "1.0",

    // 使用map结构定义池子，key为池名，包含自动伸缩配置
    // 简化的池子配置：只需要基本信息和扩容配置
    // 扩容时直接传入label: resourcePool=池子名
    "pools": {
      "ubuntu1604-pool": {
        "description": "Ubuntu 16.04 通用资源池",
        "osType": "ubuntu1604",
        "containerImage": "registry.baidubce.com/cfc_dev/runner:ubuntu1604-0720-java11",
        // 自动伸缩配置（继承集群配置结构）
        "scalingOptions": {
          "autoScalingType": "threshold",
          "thresholdMap": {
            "128": {
              "unoccupiedRedundancy": 3,
              "scalingUpTrigger": 1.1,
              "scalingDownTrigger": 1.3,
              "ActualRedundancy": 3
            },
            "256": {
              "unoccupiedRedundancy": 2,
              "scalingUpTrigger": 1.2,
              "scalingDownTrigger": 1.4,
              "ActualRedundancy": 2
            }
          },
          "scalingDown": true,
          "scalingTimeout": 600,
          "maxscalingDownSize": 1
        },
        "scalingUpOptions": {
          "singleScalingRequestSize": 1,
          "maxNodeCount": 50
        }
      },
      "ubuntu2204-pool": {
        "description": "Ubuntu 22.04 通用资源池",
        "osType": "ubuntu2204",
        "containerImage": "registry.baidubce.com/cfc_dev/runner:ubuntu2204-0720-java17",
        "scalingOptions": {
          "autoScalingType": "threshold",
          "thresholdMap": {
            "128": {
              "unoccupiedRedundancy": 2,
              "scalingUpTrigger": 1.0,
              "scalingDownTrigger": 1.2,
              "ActualRedundancy": 2
            }
          },
          "scalingDown": true,
          "scalingTimeout": 600
        },
        "scalingUpOptions": {
          "singleScalingRequestSize": 2,
          "maxNodeCount": 100
        }
      },
      "gpu-pool": {
        "description": "GPU专用资源池",
        "osType": "ubuntu2204",
        "containerImage": "registry.baidubce.com/cfc_dev/runner:ubuntu2204-gpu-0720",
        "scalingOptions": {
          "autoScalingType": "manual",  // GPU资源手动扩缩容
          "scalingDown": false,
          "scalingTimeout": 1200
        },
        "scalingUpOptions": {
          "singleScalingRequestSize": 1,
          "maxNodeCount": 10
        }
      }
    },
    "defaultPool": "ubuntu1604-pool",

    // 运行时映射关系：简化为池子名到运行时数组的映射
    // eventhub在读取配置时，维护一个map[运行时]池子即可
    "runtimeMappings": {
      "ubuntu1604-pool": [
        "nodejs12", "nodejs14", "nodejs16",
        "python27", "python36", "python37", "python38",
        "java8", "java11",
        "php72", "php74", "php80",
        "go118", "go119",
        "dotnet31", "dotnet60"
      ],
      "ubuntu2204-pool": [
        "nodejs16", "nodejs18", "nodejs20",
        "python38", "python39", "python310", "python311",
        "java11", "java17", "java21",
        "php80", "php81", "php82",
        "go119", "go120", "go121",
        "dotnet60", "dotnet70", "dotnet80"
      ],
      "gpu-pool": [
        "python39-gpu", "python310-gpu",
        "pytorch", "tensorflow", "paddle",
        "cuda-runtime"
      ]
    },

    // 可选：运行时选择策略（简化）
    "selectionStrategy": {
      "preferNewerOS": true,      // 优先选择新版本OS
      "allowFallback": true,      // 允许降级到其他池子
      "gpuOnly": ["python39-gpu", "python310-gpu", "pytorch", "tensorflow", "paddle", "cuda-runtime"]  // 只能在GPU池子运行的运行时
    }
  }
}
```

### 3.2 配置管理机制

#### 3.2.1 统一配置管理器
```go
// 集群配置管理器，主要供eventhub使用
// cron模块保持现有配置获取方式，不需要改动
type ClusterConfigManager struct {
    etcdClient   etcd.EtcdInterface
    configCache  map[string]*ClusterConfig
    cacheMutex   sync.RWMutex
    logger       *logs.Logger
    watchers     map[string][]ConfigWatcher
}

type ClusterConfig struct {
    ClusterID           string                 `json:"cceClusterUUID"`
    K8sOptions          K8sOptions            `json:"k8sOptions"`
    ResourcePoolConfig  *ResourcePoolConfig   `json:"resourcePoolConfig,omitempty"`
    // ... 其他现有字段
}

type ResourcePoolConfig struct {
    Enabled          bool                        `json:"enabled"`
    Version          string                      `json:"version"`
    Pools            map[string]ResourcePool     `json:"pools"`           // 池子配置
    DefaultPool      string                      `json:"defaultPool"`
    RuntimeMappings  RuntimeMappings             `json:"runtimeMappings"` // 简化为 map[string][]string
    SelectionStrategy SelectionStrategy          `json:"selectionStrategy"`
}

type ResourcePool struct {
    Description      string           `json:"description"`
    OSType           string           `json:"osType"`
    ContainerImage   string           `json:"containerImage"`
    ScalingOptions   ScalingOptions   `json:"scalingOptions"`    // 自动伸缩配置
    ScalingUpOptions ScalingUpOptions `json:"scalingUpOptions"`  // 扩容配置
    // 移除NodeSelector和ResourceLimits，扩容时直接传入resourcePool标签
}

type ScalingOptions struct {
    AutoScalingType string                     `json:"autoScalingType"`
    ThresholdMap    map[string]ThresholdConfig `json:"thresholdMap"`
    ScalingDown     bool                       `json:"scalingDown"`
    ScalingTimeout  int                        `json:"scalingTimeout"`
    MaxScalingDownSize int                     `json:"maxscalingDownSize"`
}

type ScalingUpOptions struct {
    SingleScalingRequestSize int `json:"singleScalingRequestSize"`
    MaxNodeCount            int `json:"maxNodeCount"`
}

// 简化的运行时映射结构
type RuntimeMappings map[string][]string  // 池子名 -> 运行时数组

// 简化的选择策略
type SelectionStrategy struct {
    PreferNewerOS bool     `json:"preferNewerOS"`  // 优先选择新版本OS
    AllowFallback bool     `json:"allowFallback"`  // 允许降级到其他池子
    GPUOnly       []string `json:"gpuOnly"`        // 只能在GPU池子运行的运行时列表
}
```

#### 3.2.2 配置获取和缓存机制
```go
// 获取集群配置（带缓存和热更新）
func (ccm *ClusterConfigManager) GetClusterConfig(clusterID string) *ClusterConfig {
    ccm.cacheMutex.RLock()
    config, exists := ccm.configCache[clusterID]
    ccm.cacheMutex.RUnlock()

    if !exists {
        // 从etcd加载配置
        key := fmt.Sprintf("faas-kun1.0/k8s/%s", clusterID)
        config = ccm.loadConfigFromEtcd(key)

        if config == nil {
            config = ccm.getDefaultConfig(clusterID)
        }

        // 缓存配置
        ccm.cacheMutex.Lock()
        ccm.configCache[clusterID] = config
        ccm.cacheMutex.Unlock()

        // 启动配置监听
        go ccm.watchConfigChanges(clusterID)
    }

    return config
}

// 根据运行时选择ResourcePool（新的选择逻辑）
func (ccm *ClusterConfigManager) SelectResourcePool(clusterID, runtimeName string) string {
    config := ccm.GetClusterConfig(clusterID)

    // 检查是否启用ResourcePool功能
    if config.ResourcePoolConfig == nil || !config.ResourcePoolConfig.Enabled {
        return "" // 未启用，使用传统调度
    }

    poolConfig := config.ResourcePoolConfig

    // 新的选择逻辑：遍历池子找到支持该运行时的池子
    candidatePools := ccm.findPoolsForRuntime(poolConfig, runtimeName)

    if len(candidatePools) == 0 {
        return poolConfig.DefaultPool // 没有找到合适的池子，使用默认池子
    }

    // 根据策略选择最合适的池子
    return ccm.selectBestPool(candidatePools, poolConfig.SelectionStrategy, runtimeName)
}

// 查找支持指定运行时的池子（简化版）
func (ccm *ClusterConfigManager) findPoolsForRuntime(poolConfig *ResourcePoolConfig, runtimeName string) []string {
    var candidatePools []string

    // 遍历简化的运行时映射：map[string][]string
    for poolName, runtimes := range poolConfig.RuntimeMappings {
        // 检查该池子是否支持该运行时
        for _, supportedRuntime := range runtimes {
            if supportedRuntime == runtimeName {
                candidatePools = append(candidatePools, poolName)
                break
            }
        }
    }

    return candidatePools
}

// 根据策略选择最佳池子（简化版）
func (ccm *ClusterConfigManager) selectBestPool(candidates []string, strategy SelectionStrategy, runtimeName string) string {
    if len(candidates) == 0 {
        return ""
    }

    // GPU运行时特殊处理
    if ccm.isGPURuntime(runtimeName, strategy.GPUOnly) {
        // 只选择GPU池子
        for _, poolName := range candidates {
            if strings.Contains(poolName, "gpu") {
                return poolName
            }
        }
        if !strategy.AllowFallback {
            return "" // 不允许降级，没有GPU池子可用
        }
    }

    // 优先选择新版本OS的池子
    if strategy.PreferNewerOS {
        for _, poolName := range candidates {
            if strings.Contains(poolName, "ubuntu2204") || strings.Contains(poolName, "ubuntu22") {
                return poolName
            }
        }
    }

    // 默认选择第一个候选池子
    return candidates[0]
}

// 检查是否为GPU运行时
func (ccm *ClusterConfigManager) isGPURuntime(runtimeName string, gpuOnlyList []string) bool {
    for _, gpuRuntime := range gpuOnlyList {
        if gpuRuntime == runtimeName {
            return true
        }
    }
    return false
}

// eventhub初始化时构建运行时到池子的反向映射（可选优化）
func (ccm *ClusterConfigManager) buildRuntimeToPoolMap(clusterID string) map[string]string {
    config := ccm.GetClusterConfig(clusterID)
    if config.ResourcePoolConfig == nil || !config.ResourcePoolConfig.Enabled {
        return nil
    }

    runtimeToPool := make(map[string]string)

    // 遍历池子到运行时的映射，构建反向映射
    for poolName, runtimes := range config.ResourcePoolConfig.RuntimeMappings {
        for _, runtime := range runtimes {
            // 如果运行时已经映射到其他池子，根据策略决定优先级
            if existingPool, exists := runtimeToPool[runtime]; exists {
                // 优先选择新版本OS的池子
                if config.ResourcePoolConfig.SelectionStrategy.PreferNewerOS {
                    if strings.Contains(poolName, "ubuntu2204") && !strings.Contains(existingPool, "ubuntu2204") {
                        runtimeToPool[runtime] = poolName
                    }
                }
            } else {
                runtimeToPool[runtime] = poolName
            }
        }
    }

    return runtimeToPool
}

// 使用反向映射快速查找（可选优化）
func (ccm *ClusterConfigManager) SelectResourcePoolFast(clusterID, runtimeName string) string {
    // 可以在ClusterConfigManager中缓存反向映射
    if runtimeToPoolMap, exists := ccm.runtimeToPoolCache[clusterID]; exists {
        if poolName, found := runtimeToPoolMap[runtimeName]; found {
            return poolName
        }
    }

    // 降级到默认逻辑
    return ccm.SelectResourcePool(clusterID, runtimeName)
}

// 配置变更监听
func (ccm *ClusterConfigManager) watchConfigChanges(clusterID string) {
    key := fmt.Sprintf("faas-kun1.0/k8s/%s", clusterID)

    watchChan := ccm.etcdClient.Watch(key)
    for event := range watchChan {
        if event.Type == etcd.EventTypePut {
            // 重新加载配置
            newConfig := ccm.loadConfigFromEtcd(key)
            if newConfig != nil {
                ccm.cacheMutex.Lock()
                ccm.configCache[clusterID] = newConfig
                ccm.cacheMutex.Unlock()

                // 通知所有监听器
                ccm.notifyWatchers(clusterID, newConfig)
            }
        }
    }
}
```

### 3.3 模块集成方案

#### 3.3.1 eventhub集成
```go
// eventhub中的集成方式
type EventhubCore struct {
    // ... 现有字段
    ClusterConfigManager *ClusterConfigManager
}

// 初始化时创建配置管理器
func NewEventhubCore(runOptions *options.EventhubOptions, stopCh <-chan struct{}) (*EventhubCore, error) {
    // ... 现有初始化逻辑

    // 创建集群配置管理器
    clusterConfigManager := NewClusterConfigManager(etcdClient)

    ec := &EventhubCore{
        // ... 现有字段
        ClusterConfigManager: clusterConfigManager,
    }

    return ec, nil
}

// 在getFreePod函数中使用
func getFreePod(ctx *context.InvokeContext, secureContainer bool) (*api.PodInfo, *api.PodTrace, error) {
    // 现有逻辑保持不变...
    labels = ctx.LabelMgr.GetLabel(ctx.OwnerUser.ID, ctx.Function, clusterVipLabel)

    // 新增：根据运行时选择ResourcePool
    // 从用户维度获取集群ID，兼容现有的集群选择逻辑
    clusterID := ctx.getClusterIDFromUserContext()
    resourcePool := ctx.ClusterConfigManager.SelectResourcePool(clusterID, ctx.Runtime.Runtime)
    if resourcePool != "" {
        labels[api.LabelResourcePool] = resourcePool
    }

    // 调用poolmanager，Redis Key会自动包含ResourcePool信息
    input := &api.GetFreePodRequest{
        Labels: labels,
        // ... 其他参数
    }
    return c.PodClient.GetFreePod(ctx.Context, input)
}

// 从用户上下文获取集群ID的实现
func (ctx *context.InvokeContext) getClusterIDFromUserContext() string {
    // 方案1: 从用户标签中获取集群信息（兼容现有的用户维度集群选择）
    if clusterLabel := ctx.LabelMgr.GetClusterLabel(); clusterLabel != "" {
        return clusterLabel
    }

    // 方案2: 从函数配置中获取集群信息
    if ctx.Function != nil && ctx.Function.ClusterID != "" {
        return ctx.Function.ClusterID
    }

    // 方案3: 从用户配置获取默认集群（保持现有逻辑）
    if ctx.OwnerUser != nil && ctx.OwnerUser.DefaultClusterID != "" {
        return ctx.OwnerUser.DefaultClusterID
    }

    // 方案4: 从全局配置获取默认集群
    return ctx.getDefaultClusterID()
}
```

#### 3.3.2 cron模块集成
**说明**：cron模块保持现有的配置获取方式，不需要改动配置管理逻辑。

```go
// cron模块保持现有结构，无需引入ClusterConfigManager
type CronManager struct {
    // ... 现有字段保持不变
    // 保持现有的配置获取方式
}

// 扩容时根据ResourcePool配置设置标签
func (cm *CronManager) ScaleUpNodes(clusterID string, nodeCount int) error {
    // 使用现有方式获取集群配置
    config := cm.getCurrentClusterConfig(clusterID)  // 保持现有逻辑

    if cm.isResourcePoolEnabled(config) {
        // 使用新的ResourcePool扩容逻辑
        return cm.scaleUpWithResourcePool(config, nodeCount)
    } else {
        // 使用传统扩容逻辑（保持不变）
        return cm.scaleUpLegacy(clusterID, nodeCount)
    }
}

// 检查ResourcePool是否启用（新增辅助方法）
func (cm *CronManager) isResourcePoolEnabled(config *ClusterConfig) bool {
    return config.ResourcePoolConfig != nil && config.ResourcePoolConfig.Enabled
}

func (cm *CronManager) scaleUpWithResourcePool(config *ClusterConfig, nodeCount int) error {
    // 根据资源需求选择合适的ResourcePool（适应map结构）
    for poolName, pool := range config.ResourcePoolConfig.Pools {
        // 根据池子的扩容配置决定是否需要扩容
        if !cm.shouldScalePool(poolName, pool) {
            continue
        }

        // 为新节点设置ResourcePool标签（简化方案）
        nodeLabels := map[string]string{
            "resourcePool": poolName,  // 只需要设置resourcePool标签
        }

        // 使用pool指定的容器镜像和扩容配置
        nodeConfig := &NodeConfig{
            ContainerImage: pool.ContainerImage,
            Labels:        nodeLabels,
            ScalingSize:   pool.ScalingUpOptions.SingleScalingRequestSize,
            MaxNodes:      pool.ScalingUpOptions.MaxNodeCount,
        }

        // 创建节点
        err := cm.createNodesWithConfig(nodeConfig, nodeCount)
        if err != nil {
            return err
        }
    }

    return nil
}

// 判断池子是否需要扩容
func (cm *CronManager) shouldScalePool(poolName string, pool ResourcePool) bool {
    // 检查池子的自动扩容配置
    if pool.ScalingOptions.AutoScalingType == "manual" {
        return false // 手动扩容，不自动处理
    }

    // 检查当前节点数量是否达到上限
    currentNodeCount := cm.getCurrentNodeCount(poolName)
    if currentNodeCount >= pool.ScalingUpOptions.MaxNodeCount {
        return false // 已达到最大节点数
    }

    // 检查资源使用情况和阈值
    for memorySize, threshold := range pool.ScalingOptions.ThresholdMap {
        if cm.checkResourceThreshold(poolName, memorySize, threshold) {
            return true // 需要扩容
        }
    }

    return false
}

// 检查资源阈值
func (cm *CronManager) checkResourceThreshold(poolName, memorySize string, threshold ThresholdConfig) bool {
    // 获取当前资源使用情况
    usage := cm.getResourceUsage(poolName, memorySize)

    // 检查是否触发扩容阈值
    if usage.UtilizationRatio > threshold.ScalingUpTrigger {
        return true
    }

    return false
}
```

## 4. Redis Key设计与多资源支持

### 4.1 Redis Key自动分片设计

#### 4.1.1 现有Key结构分析
```bash
# 当前Key格式
kun:warm_node:cfc:common:dummyUSER::128
kun:warm_pod:cfc:common:dummyUSER:::128:abc123def456
```

#### 4.1.2 ResourcePool扩展后的Key结构
通过在ClusterLabels中增加ResourcePool信息，实现自动分片：

```bash
# 原有格式（未启用ResourcePool）
kun:warm_node:cfc:common:dummyUSER::128

# 新格式（启用ResourcePool后自动分片）
kun:warm_node:cfc:common:ubuntu1604-pool:dummyUSER::128
kun:warm_node:cfc:common:ubuntu2204-pool:dummyUSER::128
kun:warm_node:cfc:common:gpu-pool:dummyUSER::256
```

#### 4.1.3 Key生成逻辑扩展
```go
// 扩展Labels生成逻辑
func (rl *RequestLabels) GetClusterLabels(resourcePool string) api.RequestLabels {
    clusterLabels := api.RequestLabels{}

    // 现有逻辑：添加基础集群标签
    if vipUser := rl.Get(api.LabelVipUser); vipUser != "" {
        clusterLabels.Set(api.LabelVipUser, vipUser)
    }

    // 新增逻辑：添加ResourcePool标签（如果启用）
    if resourcePool != "" {
        clusterLabels.Set(api.LabelResourcePool, resourcePool)
    }

    return clusterLabels
}

// SortedLabels方法自动处理ResourcePool
func (rl RequestLabels) SortedLabels() string {
    keys := make([]string, 0, len(rl))
    for k := range rl {
        keys = append(keys, k)
    }
    sort.Strings(keys)

    values := make([]string, len(keys))
    for i, k := range keys {
        values[i] = rl[k]
    }

    // 自动生成：common:ubuntu1604-pool 或 common（未启用时）
    return strings.Join(values, ":")
}
```

### 4.2 多资源支持架构设计

#### 4.2.1 设计目标
**当前阶段**：保持现有的内存资源管理，确保架构能够方便后续扩展CPU和GPU资源支持。

**后续扩展**：当需要支持CPU和GPU资源时，可以基于当前架构快速实现。

#### 4.2.2 架构兼容性设计
```go
// 预留多资源接口，当前只实现内存资源
type ResourceManager interface {
    // 当前实现：内存资源分配
    AllocateMemory(request *MemoryRequest) (*MemoryResponse, error)
    ReleaseMemory(request *MemoryRequest) error

    // 预留接口：后续扩展CPU资源时实现
    // AllocateCPU(request *CPURequest) (*CPUResponse, error)
    // ReleaseCPU(request *CPURequest) error

    // 预留接口：后续扩展GPU资源时实现
    // AllocateGPU(request *GPURequest) (*GPUResponse, error)
    // ReleaseGPU(request *GPURequest) error
}

// 当前实现：只处理内存资源
type DefaultResourceManager struct {
    redisClient redis.Interface
}

func (rm *DefaultResourceManager) AllocateMemory(request *MemoryRequest) (*MemoryResponse, error) {
    // 现有的内存分配逻辑保持不变
    return rm.allocateMemoryFromRedis(request)
}
```

#### 4.2.3 Redis Key扩展预留
```bash
# 当前实现：内存资源Key
kun:warm_node:cfc:common:ubuntu1604-pool:dummyUSER::128

# 后续扩展：CPU资源Key（预留设计）
# kun:warm_node_cpu:cfc:common:ubuntu1604-pool:dummyUSER::

# 后续扩展：GPU资源Key（预留设计）
# kun:warm_node_gpu:cfc:common:gpu-pool:dummyUSER::
```

#### 4.2.4 配置结构简化
```go
// ResourcePool配置简化：不需要resourceLimits和nodeSelector
// 扩容时直接传入resourcePool标签即可
type ResourcePool struct {
    Description      string           `json:"description"`
    OSType           string           `json:"osType"`
    ContainerImage   string           `json:"containerImage"`
    ScalingOptions   ScalingOptions   `json:"scalingOptions"`
    ScalingUpOptions ScalingUpOptions `json:"scalingUpOptions"`
    // 移除了NodeSelector和ResourceLimits字段
}
```

### 4.3 扩展实施说明

#### 4.3.1 当前实施范围
- ✅ **内存资源管理**：保持现有逻辑，通过ResourcePool实现分片
- ✅ **架构兼容性**：预留接口和配置字段
- ✅ **Redis Key规范**：建立可扩展的Key命名规范

#### 4.3.2 后续扩展路径
当需要支持CPU和GPU资源时，可以基于当前架构：

1. **启用预留配置字段**
2. **实现预留的资源管理接口**
3. **添加对应的Redis Key和Lua脚本**
4. **扩展cron模块的资源回收逻辑**

#### 4.3.3 兼容性保证
- 现有功能完全不受影响
- 配置向后兼容
- 可以渐进式启用新资源类型
```
## 5. 运行时镜像收敛

**说明**：运行时镜像收敛部分由手动完成，不在本期自动化实施范围内。

**手动处理内容**：
- 统一Runtime镜像构建和维护
- 多OS兼容性处理
- 运行时版本管理

**对ResourcePool的影响**：
- 各ResourcePool仍可配置不同的`containerImage`
- 镜像选择逻辑保持在cron模块的扩容配置中
- funclet模块无需改动，保持现有逻辑

## 6. 动态配置化方案

### 6.1 DaemonSet动态配置

#### 6.1.1 当前问题
- daemonset配置静态化，更新需要重启cron模块
- 集群镜像升级复杂，影响业务连续性

#### 6.1.2 本期实施范围调整
**基于您的建议，本期先不完成动态配置daemonset的功能**，专注于核心的ResourcePool调度功能。

**本期实施内容**：
1. ✅ 统一配置管理（ResourcePool配置）
2. ✅ Redis Key自动分片
3. ✅ 多资源原子操作
4. ✅ eventhub和poolmanager集成
5. ✅ cron模块ResourcePool扩缩容

**手动完成内容**：
- 运行时镜像收敛和统一

**后续版本实施**：
- DaemonSet动态配置和热更新
- 更复杂的资源调度策略
- 更多资源类型支持

#### 6.1.3 简化的配置管理方案（本期）
```go
// 本期只需要基础的ResourcePool配置管理
// DaemonSet配置保持现有的静态方式，后续版本再优化

// ResourcePool配置管理（复用已有的ClusterConfigManager）
func (cm *CronManager) updateResourcePoolConfig() {
    // 监听ResourcePool配置变化
    // 根据配置变化调整扩缩容策略
    // 保持DaemonSet配置不变
}

// 简化的节点创建逻辑
func (cm *CronManager) createNodeWithResourcePool(poolConfig ResourcePool) error {
    // 使用现有的节点创建逻辑
    // 只需要在节点标签中添加ResourcePool信息
    nodeLabels := map[string]string{
        "resourcePool": poolConfig.Name,
    }

    return cm.createNodeWithLabels(nodeLabels)
}
```

**说明**：本期实施中，DaemonSet配置保持现有的静态方式，专注于ResourcePool核心功能的实现。
```


## 8. 实施计划与风险控制

### 8.1 分阶段实施策略

#### 8.1.1 第一阶段：基础架构准备（2周）
**目标**：完成统一配置框架和ResourcePool基础架构

**主要工作**：
1. **扩展etcd配置结构**
   - 在现有集群配置中增加resourcePoolConfig字段
   - 实现ClusterConfigManager配置管理器（主要供eventhub使用）
   - cron模块保持现有配置获取方式
   - 添加配置验证和热更新机制

2. **实现ResourcePool选择逻辑**
   - 运行时与ResourcePool映射算法
   - 兼容性检查和降级策略
   - 集群级开关控制

3. **扩展Labels系统**
   - 增加LabelResourcePool常量
   - 修改RequestLabels生成逻辑
   - 保持向后兼容性

**验收标准**：
- 配置可以正确加载和热更新
- ResourcePool选择逻辑单元测试通过
- 现有功能不受影响

#### 8.1.2 第二阶段：Redis Key扩展和架构预留（2周）
**目标**：实现Redis Key自动分片和多资源架构预留

**主要工作**：
1. **Key生成逻辑扩展**
   - 修改NodeIndex.Key()和PodIndex.Key()方法
   - 实现ResourcePool自动分片
   - 保持兼容性开关

2. **多资源架构预留**
   - 预留多资源接口定义
   - 建立可扩展的Redis Key命名规范
   - 简化ResourcePool配置结构（移除resourceLimits和nodeSelector）

3. **性能优化**
   - Redis Key分片性能测试
   - 内存资源操作性能基准测试
   - 监控和告警机制

**验收标准**：
- 新旧Key格式可以并存
- 内存资源分配正常工作
- 架构支持后续多资源扩展
- 性能指标不下降

#### 8.1.3 第三阶段：模块集成和调度逻辑（2周）
**目标**：将ResourcePool集成到eventhub和poolmanager调度流程

**主要工作**：
1. **eventhub集成**
   - 集成ClusterConfigManager
   - 在getFreePod中添加ResourcePool选择逻辑
   - 保持API兼容性

2. **poolmanager适配**
   - 确保ResourcePool标签正确传递
   - 适配新的Redis Key格式
   - 添加降级机制

3. **reserve_ctrl适配**
   - 适配新的Redis Key格式
   - 保持现有内存资源分配逻辑
   - 预留多资源接口（不实现）

**验收标准**：
- 端到端调度流程正常工作
- 新旧模式可以无缝切换
- 调度延迟不增加

#### 8.1.4 第四阶段：cron模块扩展和系统集成（2周）
**目标**：实现ResourcePool扩缩容和系统整体集成

**主要工作**：
1. **cron模块扩展**
   - 支持ResourcePool维度的扩缩容
   - 实现节点ResourcePool标签自动设置
   - 保持现有配置获取方式和DaemonSet配置方式

2. **系统集成测试**
   - 端到端功能测试
   - 性能基准测试
   - 兼容性验证
   - 新旧模式切换测试

3. **监控和运维工具**
   - ResourcePool状态监控
   - 配置变更审计
   - 故障排查工具

**验收标准**：
- ResourcePool扩缩容正常工作
- 系统整体功能正常
- 新旧模式可以平滑切换
- 监控和告警完善

**手动完成**：
- 运行时镜像收敛（由专人手动处理）

**后续版本规划**：
- DaemonSet动态配置和热更新
- 更复杂的资源调度策略

### 8.2 风险控制措施

#### 8.2.1 技术风险控制

**风险1：Redis性能影响**
- **控制措施**：
  - 在测试环境进行充分的性能测试
  - 实现渐进式迁移，可随时回滚
  - 监控Redis关键指标
  - 准备Redis集群扩容方案

**风险2：配置一致性问题**
- **控制措施**：
  - 统一配置管理，避免配置分散
  - 实现配置验证机制
  - 提供配置同步和修复工具
  - 建立配置变更审计日志

**风险3：多资源操作原子性**
- **控制措施**：
  - 使用Lua脚本保证原子性
  - 实现资源分配失败回滚
  - 添加资源一致性检查
  - 提供资源修复工具

#### 8.2.2 业务风险控制

**风险1：大客户集群影响**
- **控制措施**：
  - 默认关闭ResourcePool功能
  - 实现完善的降级机制
  - 建立客户沟通机制
  - 提供专门的技术支持

**风险2：服务可用性影响**
- **控制措施**：
  - 采用蓝绿部署策略
  - 实现流量分级切换
  - 建立实时监控和告警
  - 准备紧急回滚预案

### 8.3 监控和运维

#### 8.3.1 关键指标监控
```go
type ResourcePoolMetrics struct {
    // 调度性能指标
    SchedulingLatency     map[string]float64  // 按池子统计调度延迟
    SchedulingSuccessRate map[string]float64  // 按池子统计成功率

    // 资源利用率指标
    PoolUtilization       map[string]float64  // 各池子资源利用率
    NodeDistribution      map[string]int64    // 节点分布情况

    // 兼容性指标
    LegacyTrafficPercent  float64             // 传统流程流量占比
    NewTrafficPercent     float64             // 新流程流量占比

    // Redis性能指标
    RedisKeyCount         map[string]int64    // 各类型Key数量
    RedisOperationLatency map[string]float64  // Redis操作延迟
}
```

## 9. 总结

### 9.1 技术创新点

1. **配置管理优化**：解决了运行时与资源池映射关系维护的一致性问题，各模块保持现有配置获取方式
2. **Redis Key自动分片**：通过ResourcePool标签实现自动分片，提升性能
3. **配置结构优化**：极简的池子到运行时映射，大幅降低配置复杂度
4. **架构扩展性设计**：预留多资源接口，支持后续CPU和GPU资源扩展
5. **渐进式迁移**：完美的向后兼容性，支持集群级灰度切换

### 9.2 业务价值

1. **降低维护成本**：配置结构极简化，核心扩容参数统一管理
2. **提升调度效率**：细粒度调度和Redis Key分片
3. **加速新功能上线**：新运行时可以快速在现有集群中部署
4. **增强系统扩展性**：为未来支持更多资源类型奠定基础
5. **保证业务连续性**：完美的向后兼容性和渐进式迁移
6. **灵活扩缩容策略**：不同池子可以根据特点配置不同的扩缩容策略

### 9.3 架构优势

1. **向后兼容**：现有大客户集群无需任何改动
2. **渐进式演进**：支持按集群的灰度切换
3. **配置极简化**：使用poolInfo结构化管理，extraPools只需配置差异部分
4. **扩容更简单**：cron模块扩容时直接传入resourcePool=池子名即可
5. **策略灵活性**：不同池子可以配置不同的扩缩容策略，同时核心参数统一管理
6. **性能优化**：Redis Key分片显著提升调度性能

通过这次重构，CFC调度系统将从传统的集群维度调度演进为现代化的ResourcePool调度架构，完美解决了demand.md中提出的所有核心问题，为百度智能云函数计算服务的长期发展提供强有力的技术支撑。

## 附录：实施周期调整

**总实施周期**：**8周**
- 第一阶段：基础架构准备（2周）
- 第二阶段：Redis Key扩展和多资源支持（2周）
- 第三阶段：模块集成和调度逻辑（2周）
- 第四阶段：cron模块扩展和系统集成（2周）

**功能范围调整**：
- ✅ 保留：ResourcePool核心调度功能
- ✅ 保留：Redis Key自动分片
- ✅ 保留：配置结构极简化（外部配置即默认池，resourcePoolConfig只包含extraPools）
- ✅ 保留：基于supportRuntimes的运行时选择
- 🔧 预留：多资源架构设计（预留接口，不实现具体功能）
- 👤 手动：运行时镜像收敛（专人负责）
- ⏸️ 延后：DaemonSet动态配置（后续版本实施）
- ⏸️ 延后：CPU和GPU资源管理（后续版本实施）