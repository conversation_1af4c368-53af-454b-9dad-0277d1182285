# CFC ResourcePool 配置迁移指南

## 1. 迁移原则

### 1.1 核心原则
- **现有配置不动**：保持现有集群etcd配置完全不变
- **外部配置即默认池**：现有集群配置直接作为默认池配置
- **只增加新池**：resourcePoolConfig只包含extraPools（新增池子）
- **渐进式启用**：默认关闭ResourcePool功能，可按需启用

### 1.2 兼容性保证
- ✅ 现有集群无需任何改动
- ✅ 现有扩缩容逻辑继续工作
- ✅ 现有容器镜像继续使用
- ✅ 现有调度逻辑继续工作

## 2. 配置迁移示例

### 2.1 现有集群配置（保持不变）
```json
{
  "cceClusterUUID": "cce-u4clzq75",
  "k8sOptions": {
    "containerImage": "registry.baidubce.com/cfc_dev/runner:ubuntu1604-0720-java11",
    "dsReleaseConfigMap": {
      "funclet": {
        "yamlFilePath": "/etc/k8s/funclet.yaml",
        "imageID": "registry.baidubce.com/cfc_dev/funclet:v1.2.3",
        "md5": "abc123def456"
      },
      "runtimes": {
        "yamlFilePath": "/etc/k8s/runtimes.yaml",
        "imageID": "registry.baidubce.com/cfc_dev/runtime:ubuntu1604-0720",
        "md5": "def456abc123"
      }
    }
  },
  "scalingOptions": {
    "autoScalingType": "threshold",
    "thresholdMap": {
      "128": {
        "unoccupiedRedundancy": 3,
        "scalingUpTrigger": 1.1,
        "scalingDownTrigger": 1.3,
        "ActualRedundancy": 3
      },
      "256": {
        "unoccupiedRedundancy": 2,
        "scalingUpTrigger": 1.2,
        "scalingDownTrigger": 1.4,
        "ActualRedundancy": 2
      }
    },
    "scalingDown": true,
    "scalingTimeout": 600,
    "maxscalingDownSize": 1
  },
  "scalingUpOptions": {
    "singleScalingRequestSize": 1,
    "maxNodeCount": 50
  },
  "description": "默认资源池",
  "osType": "ubuntu1604",
  "supportRuntimes": [
    "nodejs12", "nodejs14", "nodejs16",
    "python27", "python36", "python37", "python38",
    "java8", "java11", "php72", "php74", "go118", "dotnet31"
  ]
}
```

### 2.2 迁移后配置（增量添加）
```json
{
  // 现有配置完全保持不变，并增加默认池配置
  "cceClusterUUID": "cce-u4clzq75",
  "k8sOptions": {
    "containerImage": "registry.baidubce.com/cfc_dev/runner:ubuntu1604-0720-java11",
    "dsReleaseConfigMap": {
      "funclet": {
        "yamlFilePath": "/etc/k8s/funclet.yaml",
        "imageID": "registry.baidubce.com/cfc_dev/funclet:v1.2.3",
        "md5": "abc123def456"
      },
      "runtimes": {
        "yamlFilePath": "/etc/k8s/runtimes.yaml",
        "imageID": "registry.baidubce.com/cfc_dev/runtime:ubuntu1604-0720",
        "md5": "def456abc123"
      }
    }
  },
  "scalingOptions": {
    "autoScalingType": "threshold",
    "thresholdMap": {
      "128": {
        "unoccupiedRedundancy": 3,
        "scalingUpTrigger": 1.1,
        "scalingDownTrigger": 1.3,
        "ActualRedundancy": 3
      },
      "256": {
        "unoccupiedRedundancy": 2,
        "scalingUpTrigger": 1.2,
        "scalingDownTrigger": 1.4,
        "ActualRedundancy": 2
      }
    },
    "scalingDown": true,
    "scalingTimeout": 600,
    "maxscalingDownSize": 1
  },
  "scalingUpOptions": {
    "singleScalingRequestSize": 1,
    "maxNodeCount": 50
  },
  // 外部配置即默认池配置
  "description": "默认资源池",
  "osType": "ubuntu1604",
  "supportRuntimes": [
    "nodejs12", "nodejs14", "nodejs16",
    "python27", "python36", "python37", "python38",
    "java8", "java11", "php72", "php74", "go118", "dotnet31"
  ],

  // 新增：ResourcePool配置（只包含新增池子）
  "resourcePoolConfig": {
    "enabled": false,  // 默认关闭，不影响现有行为
    "version": "1.0",
    "extraPools": {
      // 只包含新增的池子
      "ubuntu2204-pool": {
        "poolInfo": {
          "description": "Ubuntu 22.04 通用资源池",
          "osType": "ubuntu2204",
          "containerImage": "registry.baidubce.com/cfc_dev/runner:ubuntu2204-0720-java17",
          "supportRuntimes": ["nodejs18", "nodejs20", "nodejs22"]
        },
        "scalingOptions": {
          // 可以配置自己的扩缩容策略
          "autoScalingType": "node",
          "thresholdMap": {
            "128": {
              "unoccupiedRedundancy": 2,
              "scalingUpTrigger": 1.0,
              "scalingDownTrigger": 1.2,
              "ActualRedundancy": 2
            }
          }
        }
        // scalingUpOptions复用集群级配置
      }
    }
  }
}
```

## 3. 分阶段迁移策略

### 3.1 第一阶段：配置添加（不启用）
**目标**：在现有配置基础上添加resourcePoolConfig，但保持disabled状态

**操作步骤**：
1. 在etcd中为集群添加description、osType、supportRuntimes字段
2. 添加resourcePoolConfig配置，设置enabled=false
3. 在extraPools中配置新增池子
4. 验证现有功能不受影响

**验证方法**：
```bash
# 验证现有调度功能正常
curl -X POST "http://eventhub/invoke" -d '{"runtime": "nodejs14", ...}'

# 验证现有扩缩容功能正常
kubectl get nodes -l cce-cluster-id=cce-u4clzq75

# 验证配置正确加载
curl "http://api/clusters/cce-u4clzq75/resourcepool/config"
```

### 3.2 第二阶段：功能启用（单池模式）
**目标**：启用ResourcePool功能，但只使用默认池子

**操作步骤**：
1. 设置enabled=true
2. 保持extraPools为空或只有少量新池子
3. 验证调度逻辑切换到ResourcePool模式
4. 监控性能和稳定性

**验证方法**：
```bash
# 启用ResourcePool功能
curl -X POST "http://api/clusters/cce-u4clzq75/resourcepool/toggle" \
  -d '{"enabled": true}'

# 验证Redis Key格式变化
redis-cli --scan --pattern "kun:warm_node:*:common-pool:*"

# 验证调度功能正常
curl -X POST "http://eventhub/invoke" -d '{"runtime": "nodejs14", ...}'
```

### 3.3 第三阶段：多池扩展（按需添加）
**目标**：根据需要添加新的ResourcePool

**操作步骤**：
1. 添加ubuntu2204-pool配置
2. 配置运行时映射
3. 测试新运行时调度
4. 逐步迁移运行时到新池子

**配置示例**：
```json
{
  // 外部配置（默认池）
  "description": "默认资源池",
  "osType": "ubuntu1604",
  "supportRuntimes": [
    "nodejs12", "nodejs14", "python27", "python36", "java8", "java11"
  ],

  // ResourcePool配置（只包含新增池子）
  "resourcePoolConfig": {
    "enabled": true,
    "extraPools": {
      "ubuntu2204-pool": {
        "poolInfo": {
          "description": "Ubuntu 22.04 通用资源池",
          "osType": "ubuntu2204",
          "containerImage": "registry.baidubce.com/cfc_dev/runner:ubuntu2204-0720-java17",
          "supportRuntimes": ["nodejs18", "nodejs20", "python39", "python310", "java17", "java21"]
        },
        "scalingOptions": {
          // 可以根据池子特点配置不同的扩缩容策略
          "autoScalingType": "node",
          "thresholdMap": {
            "128": {
              "unoccupiedRedundancy": 1,  // 新版本运行时可以设置更激进的策略
              "scalingUpTrigger": 0.8,
              "scalingDownTrigger": 1.0,
              "ActualRedundancy": 1
            }
          }
        }
        // scalingUpOptions复用集群级配置
      }
    }
  }
}
```

## 4. 配置复用详解

### 4.1 配置使用规则
| 配置项 | 默认池来源 | 新增池来源 | 说明 |
|--------|------------|------------|------|
| containerImage | k8sOptions.containerImage | poolInfo中指定或复用集群配置 | 默认池复用集群配置 |
| scalingOptions | 集群级scalingOptions | extraPools中指定或复用集群配置 | 新增池可以有自己的扩缩容策略 |
| scalingUpOptions | 集群级scalingUpOptions | 复用集群级scalingUpOptions | 所有池子统一使用集群配置 |
| supportRuntimes | 外部poolInfo.supportRuntimes | extraPools.poolInfo中指定 | 各池子独立配置 |

### 4.2 配置使用示例
```go
// 配置使用逻辑
func GetEffectiveConfig(cluster *ClusterConfig, poolName string) *EffectiveConfig {
    // 检查是否为默认池
    if poolName == "default" || cluster.ResourcePoolConfig == nil {
        // 默认池：直接使用外部配置
        return &EffectiveConfig{
            Name:             "default",
            Description:      cluster.Description,
            OSType:           cluster.OSType,
            ContainerImage:   cluster.K8sOptions.ContainerImage,
            SupportRuntimes:  cluster.SupportRuntimes,
            ScalingOptions:   cluster.ScalingOptions,
            ScalingUpOptions: cluster.ScalingUpOptions,
        }
    }

    // 新增池：使用extraPools中的配置
    if pool, exists := cluster.ResourcePoolConfig.ExtraPools[poolName]; exists {
        effective := &EffectiveConfig{
            Name:            poolName,
            Description:     pool.PoolInfo.Description,
            OSType:          pool.PoolInfo.OSType,
            SupportRuntimes: pool.PoolInfo.SupportRuntimes,
        }

        // containerImage：使用池子配置或复用集群配置
        if pool.PoolInfo.ContainerImage != "" {
            effective.ContainerImage = pool.PoolInfo.ContainerImage
        } else {
            effective.ContainerImage = cluster.K8sOptions.ContainerImage
        }

        // scalingOptions：使用池子自己的配置或复用集群配置
        if pool.ScalingOptions != nil {
            effective.ScalingOptions = *pool.ScalingOptions
        } else {
            effective.ScalingOptions = *cluster.ScalingOptions
        }

        // scalingUpOptions：统一复用集群级配置
        effective.ScalingUpOptions = *cluster.ScalingUpOptions

        return effective
    }

    return nil
}
```

## 5. 回滚方案

### 5.1 紧急回滚
如果启用ResourcePool后出现问题，可以立即回滚：

```bash
# 方法1：禁用ResourcePool功能
curl -X POST "http://api/clusters/cce-u4clzq75/resourcepool/toggle" \
  -d '{"enabled": false}'

# 方法2：删除resourcePoolConfig配置
etcdctl del faas-kun1.0/k8s/cce-u4clzq75/resourcePoolConfig
```

### 5.2 完全回滚
如果需要完全回到原始状态：

```bash
# 恢复原始配置（移除resourcePoolConfig）
etcdctl put faas-kun1.0/k8s/cce-u4clzq75 '原始配置JSON'
```

## 6. 监控和验证

### 6.1 关键监控指标
- **调度成功率**：确保不低于原有水平
- **调度延迟**：确保不高于原有水平
- **Redis Key数量**：监控Key分片效果
- **配置加载时间**：监控配置复用性能

### 6.2 验证脚本
```bash
#!/bin/bash
# 配置迁移验证脚本

echo "1. 验证配置加载..."
curl -s "http://api/clusters/cce-u4clzq75/resourcepool/config" | jq .

echo "2. 验证调度功能..."
curl -s -X POST "http://eventhub/invoke" -d '{"runtime": "nodejs14"}' | jq .

echo "3. 验证Redis Key格式..."
redis-cli --scan --pattern "kun:warm_node:*" | head -10

echo "4. 验证节点标签..."
kubectl get nodes -l resourcePool --show-labels

echo "验证完成！"
```

## 7. 常见问题

### 7.1 Q: 现有集群需要重启服务吗？
A: 不需要。配置是热加载的，添加resourcePoolConfig后会自动生效。

### 7.2 Q: 如何确保配置复用正确？
A: 可以通过API查看effectiveConfigs字段，确认复用的配置是否正确。

### 7.3 Q: 迁移过程中出现问题怎么办？
A: 可以立即设置enabled=false回滚到原有模式，或者完全删除resourcePoolConfig。

### 7.4 Q: 默认池子如何识别？
A: 默认池子就是外部配置，不在extraPools中。当ResourcePool功能未启用或运行时不在extraPools中时，自动使用默认池。

通过这种渐进式的迁移策略，可以确保现有集群的稳定性，同时平滑地引入ResourcePool功能。
