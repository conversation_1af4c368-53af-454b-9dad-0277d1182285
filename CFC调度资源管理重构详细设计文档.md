# CFC调度、资源管理重构详细设计文档

## 1. 项目概述

### 1.1 项目背景
当前CFC调度系统采用集群级"大池子"调度，面临OS兼容性问题、运行时镜像维护成本高、资源扩展性不足等痛点。本项目通过引入ResourcePool概念，实现从集群级调度到节点级细粒度调度的演进。

### 1.2 核心目标
- **解决OS兼容性问题**：支持多种OS版本共存（ubuntu1604、ubuntu2204、GPU等）
- **简化配置管理**：极简的池子到运行时映射配置
- **提升调度性能**：通过Redis Key自动分片优化并发性能
- **保证向后兼容**：现有集群无需改动，支持渐进式迁移

### 1.3 技术架构
```
┌─────────────────────────────────────────────────────────────┐
│                        Cluster Level                        │
│  ┌─────────────────┐  ┌─────────────────┐  ┌──────────────┐ │
│  │ ubuntu1604-pool │  │ ubuntu2204-pool │  │   gpu-pool   │ │
│  │                 │  │                 │  │              │ │
│  │ ┌─────┐ ┌─────┐ │  │ ┌─────┐ ┌─────┐ │  │ ┌─────┐      │ │
│  │ │Node1│ │Node2│ │  │ │Node3│ │Node4│ │  │ │Node5│      │ │
│  │ └─────┘ └─────┘ │  │ └─────┘ └─────┘ │  │ └─────┘      │ │
│  └─────────────────┘  └─────────────────┘  └──────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

## 2. 详细技术设计

### 2.1 配置结构设计

#### 2.1.1 etcd配置扩展
**设计原则**：在现有集群配置基础上增加resourcePoolConfig，复用现有配置作为默认pool

```json
{
  // 现有配置保持不变
  "cceClusterUUID": "cce-u4clzq75",
  "k8sOptions": {
    "containerImage": "registry.baidubce.com/cfc_dev/runner:ubuntu1604-0720-java11",
    "dsReleaseConfigMap": {
      "runtimes": {
        "imageID": "registry.baidubce.com/cfc_dev/runtime:unified-0722-multi-os"
      }
    }
  },
  // 现有的扩缩容配置保持不变
  "scalingOptions": {
    "autoScalingType": "threshold",
    "thresholdMap": {
      "128": {
        "unoccupiedRedundancy": 3,
        "scalingUpTrigger": 1.1,
        "scalingDownTrigger": 1.3,
        "ActualRedundancy": 3
      },
      "256": {
        "unoccupiedRedundancy": 2,
        "scalingUpTrigger": 1.2,
        "scalingDownTrigger": 1.4,
        "ActualRedundancy": 2
      }
    },
    "scalingDown": true,
    "scalingTimeout": 600,
    "maxscalingDownSize": 1
  },
  "scalingUpOptions": {
    "singleScalingRequestSize": 1,
    "maxNodeCount": 50
  },
  "resourcePoolInfo": {
    "description": "默认资源池",
    "osType": "ubuntu1604",
    "supportRuntimes": [
        "nodejs12", "nodejs14", "nodejs16",
        "python27", "python36", "python37", "python38",
        "java8", "java11", "php72", "php74", "go118", "dotnet31"
    ],
  },
  // 新增：ResourcePool配置（复用现有配置）
  // resourcePool只放新增池，外部配置就是默认池配置，也避免影响现有集群逻辑，相当于不管有没有打开开关，原来的调度、资源管理方式不变
  // 新增的struct应该定义在pkg/api/cron.go中，然后嵌入到K8sInfo struct中
  // 关于cron模块执行集群扩容时，是不是要将集群的节点先按resourcePool标签进行分类？没有resourcePool的节点统一放在defaultPool中，然后按已有逻辑处理扩缩容，有resourcePool标签的按resourcePool的配置进行处理
  // cron模块在初始化节点创建pod时，是不是也得根据resourcePool标签来创建pod？默认的都按默认池的配置来创建pod，有resourcePool标签的按resourcePool的配置来创建pod
  // 需要存在为节点选择资源池吗？难道不是根据节点有没有resourcePool标签，来确定节点所属资源池吗？
   // 我理解逻辑应该为在自动伸缩节点，如果开启了资源池，则判断资源池中节点需要扩容还是缩容，扩容时增加相应的标签，而对于默认资源池按原逻辑
   // 是否扩容应该尽可能复用当前已有逻辑，还有一些生成的重复代码记得去掉
  "resourcePoolConfig": {
    "enabled": false,  // 默认关闭，不影响现有集群
    "version": "1.0",
    "extraResourcePools": {
      // 可选：新增其他池子
      "ubuntu2204-pool": {
        "resourcePoolInfo": {
          "description": "Ubuntu 22.04 通用资源池",
          "osType": "ubuntu2204",
          "containerImage": "registry.baidubce.com/cfc_dev/runner:ubuntu2204-0720-java17",
          "supportRuntimes": ["node.js18", "node.js20", "node.js22"],
        },
        "scalingOptions": {
            "autoScalingType": "node",
            "thresholdMap": {
                "128": {
                "unoccupiedRedundancy": 3,
                "scalingUpTrigger": 1.1,
                "scalingDownTrigger": 1.3,
                "ActualRedundancy": 3
                }
            },
            "scalingDown": true,
            "scalingTimeout": 600,
            "scalingDownTimeout": 900,
            "maxscalingDownSize": 1,
            "scalingDownCycleCnt": 1,
            "scalingDownWhiteHours": [
                12,
                0
            ],
            "k8sRawInitializingRatio": 0.4,
            "scalingUpNodeNumThreshold": 10
        }
      }
    }
  }
}
```

#### 2.1.2 Go数据结构定义
```go
// pkg/config/types.go
package config

type ResourcePoolConfig struct {
    Enabled    bool                        `json:"enabled"`
    Version    string                      `json:"version"`
    ExtraPools map[string]ResourcePool     `json:"extraPools"`  // 只包含新增池子
}

type ResourcePool struct {
    PoolInfo         PoolInfo          `json:"poolInfo"`                   // 池子基本信息
    ScalingOptions   *ScalingOptions   `json:"scalingOptions,omitempty"`   // 每个池子可以有自己的扩缩容策略
    // ScalingUpOptions复用集群级配置
}

type PoolInfo struct {
    Description      string   `json:"description"`
    OSType           string   `json:"osType"`
    ContainerImage   string   `json:"containerImage,omitempty"`   // 可选，为空时复用集群配置
    SupportRuntimes  []string `json:"supportRuntimes"`            // 支持的运行时列表
}

// 移除RuntimeMappings，使用PoolInfo.SupportRuntimes替代


type ScalingOptions struct {
    AutoScalingType    string                     `json:"autoScalingType"`
    ThresholdMap       map[string]ThresholdConfig `json:"thresholdMap"`
    ScalingDown        bool                       `json:"scalingDown"`
    ScalingTimeout     int                        `json:"scalingTimeout"`
    MaxScalingDownSize int                        `json:"maxscalingDownSize"`
}

type ScalingUpOptions struct {
    SingleScalingRequestSize int `json:"singleScalingRequestSize"`
    MaxNodeCount            int `json:"maxNodeCount"`
}

type ThresholdConfig struct {
    UnoccupiedRedundancy int     `json:"unoccupiedRedundancy"`
    ScalingUpTrigger     float64 `json:"scalingUpTrigger"`
    ScalingDownTrigger   float64 `json:"scalingDownTrigger"`
    ActualRedundancy     int     `json:"ActualRedundancy"`
}
```

### 2.2 配置管理器设计

#### 2.2.1 ClusterConfigManager实现
```go
// pkg/config/manager.go
package config

import (
    "encoding/json"
    "fmt"
    "sort"
    "strings"
    "sync"
    "time"
    
    "github.com/baidu/faas-kun/pkg/etcd"
    "github.com/baidu/faas-kun/pkg/logs"
)

type ClusterConfigManager struct {
    etcdClient   etcd.EtcdInterface
    configCache  map[string]*ClusterConfig
    cacheMutex   sync.RWMutex
    logger       *logs.Logger
    serviceName  string
}

type ClusterConfig struct {
    ClusterID           string                 `json:"cceClusterUUID"`
    K8sOptions          K8sOptions            `json:"k8sOptions"`
    ScalingOptions      *ScalingOptions       `json:"scalingOptions,omitempty"`      // 集群级扩缩容配置
    ScalingUpOptions    *ScalingUpOptions     `json:"scalingUpOptions,omitempty"`    // 集群级扩容配置
    PoolInfo            *PoolInfo             `json:"poolInfo,omitempty"`            // 默认池信息
    ResourcePoolConfig  *ResourcePoolConfig   `json:"resourcePoolConfig,omitempty"`
    Version             string                `json:"version"`
    Timestamp           time.Time             `json:"timestamp"`
}

func NewClusterConfigManager(serviceName string, etcdClient etcd.EtcdInterface) *ClusterConfigManager {
    ccm := &ClusterConfigManager{
        etcdClient:  etcdClient,
        configCache: make(map[string]*ClusterConfig),
        serviceName: serviceName,
        logger:      logs.GetLogger(),
    }
    
    // 启动配置监听
    go ccm.watchAllConfigs()
    
    return ccm
}

func (ccm *ClusterConfigManager) GetClusterConfig(clusterID string) *ClusterConfig {
    ccm.cacheMutex.RLock()
    config, exists := ccm.configCache[clusterID]
    ccm.cacheMutex.RUnlock()
    
    if !exists || ccm.isConfigStale(config) {
        config = ccm.refreshConfigFromEtcd(clusterID)
    }
    
    return config
}

func (ccm *ClusterConfigManager) refreshConfigFromEtcd(clusterID string) *ClusterConfig {
    key := fmt.Sprintf("faas-kun1.0/k8s/%s", clusterID)
    resp, err := ccm.etcdClient.Get(key)
    if err != nil {
        ccm.logger.Errorf("Failed to get config for cluster %s: %v", clusterID, err)
        return ccm.getDefaultConfig(clusterID)
    }
    
    var config ClusterConfig
    if err := json.Unmarshal(resp.Value, &config); err != nil {
        ccm.logger.Errorf("Failed to parse config for cluster %s: %v", clusterID, err)
        return ccm.getDefaultConfig(clusterID)
    }
    
    config.Version = fmt.Sprintf("%d", resp.ModRevision)
    config.Timestamp = time.Now()
    
    // 更新缓存
    ccm.cacheMutex.Lock()
    ccm.configCache[clusterID] = &config
    ccm.cacheMutex.Unlock()
    
    ccm.logger.Infof("Config refreshed for cluster %s, version: %s", clusterID, config.Version)
    return &config
}

func (ccm *ClusterConfigManager) isConfigStale(config *ClusterConfig) bool {
    return time.Since(config.Timestamp) > 30*time.Second
}

func (ccm *ClusterConfigManager) watchAllConfigs() {
    keyPrefix := "faas-kun1.0/k8s/"
    watchChan := ccm.etcdClient.WatchPrefix(keyPrefix)
    
    for event := range watchChan {
        if event.Type == etcd.EventTypePut {
            clusterID := strings.TrimPrefix(string(event.Key), keyPrefix)
            ccm.logger.Infof("Config changed for cluster %s, refreshing...", clusterID)
            ccm.refreshConfigFromEtcd(clusterID)
        }
    }
}

func (ccm *ClusterConfigManager) getDefaultConfig(clusterID string) *ClusterConfig {
    return &ClusterConfig{
        ClusterID: clusterID,
        K8sOptions: K8sOptions{
            ContainerImage: "registry.baidubce.com/cfc_dev/runner:ubuntu1604-0720-java11",
        },
        ResourcePoolConfig: nil, // 默认不启用ResourcePool
        Version:            "default",
        Timestamp:          time.Now(),
    }
}

// 获取ResourcePool的有效配置（支持默认池和新增池）
func (ccm *ClusterConfigManager) GetEffectivePoolConfig(config *ClusterConfig, poolName string) *EffectivePoolConfig {
    // 检查是否为默认池
    if poolName == "default" || config.ResourcePoolConfig == nil || !config.ResourcePoolConfig.Enabled {
        // 默认池：使用集群级配置
        if config.PoolInfo == nil {
            return nil
        }

        effective := &EffectivePoolConfig{
            Name:             "default",
            Description:      config.PoolInfo.Description,
            OSType:           config.PoolInfo.OSType,
            SupportRuntimes:  config.PoolInfo.SupportRuntimes,
        }

        // 使用集群级配置
        if config.PoolInfo.ContainerImage != "" {
            effective.ContainerImage = config.PoolInfo.ContainerImage
        } else {
            effective.ContainerImage = config.K8sOptions.ContainerImage
        }

        if config.ScalingOptions != nil {
            effective.ScalingOptions = *config.ScalingOptions
        }

        if config.ScalingUpOptions != nil {
            effective.ScalingUpOptions = *config.ScalingUpOptions
        }

        return effective
    }

    // 新增池：使用extraPools中的配置
    pool, exists := config.ResourcePoolConfig.ExtraPools[poolName]
    if !exists {
        return nil
    }

    effective := &EffectivePoolConfig{
        Name:            poolName,
        Description:     pool.PoolInfo.Description,
        OSType:          pool.PoolInfo.OSType,
        SupportRuntimes: pool.PoolInfo.SupportRuntimes,
    }

    // containerImage：使用池子配置或复用集群配置
    if pool.PoolInfo.ContainerImage != "" {
        effective.ContainerImage = pool.PoolInfo.ContainerImage
    } else {
        effective.ContainerImage = config.K8sOptions.ContainerImage
    }

    // scalingOptions：使用池子自己的配置或复用集群配置
    if pool.ScalingOptions != nil {
        effective.ScalingOptions = *pool.ScalingOptions
    } else if config.ScalingOptions != nil {
        effective.ScalingOptions = *config.ScalingOptions
    }

    // scalingUpOptions：统一复用集群级配置
    if config.ScalingUpOptions != nil {
        effective.ScalingUpOptions = *config.ScalingUpOptions
    }

    return effective
}

// 有效的ResourcePool配置（合并后的配置）
type EffectivePoolConfig struct {
    Name             string           `json:"name"`
    Description      string           `json:"description"`
    OSType           string           `json:"osType"`
    ContainerImage   string           `json:"containerImage"`
    SupportRuntimes  []string         `json:"supportRuntimes"`
    ScalingOptions   ScalingOptions   `json:"scalingOptions"`
    ScalingUpOptions ScalingUpOptions `json:"scalingUpOptions"`
}
```

### 2.3 ResourcePool选择逻辑

#### 2.3.1 运行时选择算法
```go
// pkg/config/selector.go
package config

func (ccm *ClusterConfigManager) SelectResourcePool(clusterID, runtimeName string) string {
    config := ccm.GetClusterConfig(clusterID)

    // 检查是否启用ResourcePool功能
    if config.ResourcePoolConfig == nil || !config.ResourcePoolConfig.Enabled {
        return "default" // 未启用，使用默认池
    }

    // 首先检查默认池是否支持该运行时
    if config.PoolInfo != nil && ccm.poolSupportsRuntime(config.PoolInfo.SupportRuntimes, runtimeName) {
        return "default"
    }

    // 查找extraPools中支持该运行时的池子
    for poolName, pool := range config.ResourcePoolConfig.ExtraPools {
        if ccm.poolSupportsRuntime(pool.PoolInfo.SupportRuntimes, runtimeName) {
            return poolName
        }
    }

    // 如果都不支持，降级到默认池
    return "default"
}

func (ccm *ClusterConfigManager) poolSupportsRuntime(supportRuntimes []string, runtimeName string) bool {
    for _, supportedRuntime := range supportRuntimes {
        if supportedRuntime == runtimeName {
            return true
        }
    }
    return false
}
```

### 2.4 Redis Key自动分片设计

#### 2.4.1 Key生成逻辑扩展
```go
// pkg/api/labels.go
package api

const (
    LabelResourcePool = "resourcePool"
    // ... 其他现有标签
)

// 扩展Labels生成逻辑
func (rl *RequestLabels) GetClusterLabels(resourcePool string) RequestLabels {
    clusterLabels := RequestLabels{}

    // 现有逻辑：添加基础集群标签
    if vipUser := rl.Get(LabelVipUser); vipUser != "" {
        clusterLabels.Set(LabelVipUser, vipUser)
    }

    // 新增逻辑：只有非默认池才添加ResourcePool标签
    if resourcePool != "" && resourcePool != "default" {
        clusterLabels.Set(LabelResourcePool, resourcePool)
    }

    return clusterLabels
}

// SortedLabels方法自动处理ResourcePool
func (rl RequestLabels) SortedLabels() string {
    keys := make([]string, 0, len(rl))
    for k := range rl {
        keys = append(keys, k)
    }
    sort.Strings(keys)

    values := make([]string, len(keys))
    for i, k := range keys {
        values[i] = rl[k]
    }

    // 自动生成：
    // - 默认池：common
    // - extraPools：common:ubuntu2204-pool
    return strings.Join(values, ":")
}
```

#### 2.4.3 Key生成具体实现
```go
// pkg/poolmanager/redis_key.go
package poolmanager

import (
    "fmt"
    "strings"
)

// Redis Key生成器
type RedisKeyGenerator struct {
    prefix string  // 通常是 "kun"
}

func NewRedisKeyGenerator(prefix string) *RedisKeyGenerator {
    return &RedisKeyGenerator{prefix: prefix}
}

// 生成热启动节点Key
func (rkg *RedisKeyGenerator) WarmNodeKey(labels map[string]string, memory int64) string {
    labelStr := rkg.buildLabelString(labels)
    return fmt.Sprintf("%s:warm_node:cfc:%s::%d", rkg.prefix, labelStr, memory)
}

// 生成热启动Pod Key
func (rkg *RedisKeyGenerator) WarmPodKey(labels map[string]string, memory int64, podID string) string {
    labelStr := rkg.buildLabelString(labels)
    return fmt.Sprintf("%s:warm_pod:cfc:%s:::%d:%s", rkg.prefix, labelStr, memory, podID)
}

// 生成冷启动节点Key
func (rkg *RedisKeyGenerator) ColdNodeKey(labels map[string]string) string {
    labelStr := rkg.buildLabelString(labels)
    return fmt.Sprintf("%s:cold_node:cfc:%s::", rkg.prefix, labelStr)
}

// 生成预留节点Key
func (rkg *RedisKeyGenerator) ReservedNodeKey(labels map[string]string, memory int64) string {
    labelStr := rkg.buildLabelString(labels)
    return fmt.Sprintf("%s:reserved_node:{%s}:%d", rkg.prefix, labelStr, memory)
}

// 生成预留Pod Key
func (rkg *RedisKeyGenerator) ReservedPodKey(labels map[string]string, memory int64, commitID string) string {
    labelStr := rkg.buildLabelString(labels)
    return fmt.Sprintf("%s:reserved_pod:%s:::%d:%s", rkg.prefix, labelStr, memory, commitID)
}

// 生成可疑Pod Key
func (rkg *RedisKeyGenerator) DubiousPodKey(labels map[string]string, memory int64, commitID string) string {
    labelStr := rkg.buildLabelString(labels)
    return fmt.Sprintf("%s:dubious_pod:%s:::%d:%s", rkg.prefix, labelStr, memory, commitID)
}

// cron模块相关Key生成
func (rkg *RedisKeyGenerator) NodeLockKey(poolName, nodeID string) string {
    if poolName != "" && poolName != "default" {
        return fmt.Sprintf("%s:node_lock:%s:%s", rkg.prefix, poolName, nodeID)
    }
    return fmt.Sprintf("%s:node_lock:%s", rkg.prefix, nodeID)
}

func (rkg *RedisKeyGenerator) ScalingTaskKey(poolName, clusterID, taskID string) string {
    if poolName != "" && poolName != "default" {
        return fmt.Sprintf("%s:scaling_task:%s:%s:%s", rkg.prefix, poolName, clusterID, taskID)
    }
    return fmt.Sprintf("%s:scaling_task:%s:%s", rkg.prefix, clusterID, taskID)
}

// 共享Key生成
func (rkg *RedisKeyGenerator) EtcdLockKey(lockID string) string {
    return fmt.Sprintf("%s:etcd_lock:%s", rkg.prefix, lockID)
}

func (rkg *RedisKeyGenerator) StatsKey(statsType string, labels map[string]string) string {
    labelStr := rkg.buildLabelString(labels)
    return fmt.Sprintf("%s:stats:%s:%s", rkg.prefix, statsType, labelStr)
}

func (rkg *RedisKeyGenerator) MetricsKey(metricsType, module string, poolName string) string {
    if poolName != "" && poolName != "default" {
        return fmt.Sprintf("%s:metrics:%s:%s:%s", rkg.prefix, metricsType, module, poolName)
    }
    return fmt.Sprintf("%s:metrics:%s:%s", rkg.prefix, metricsType, module)
}

// 构建标签字符串（核心逻辑）
func (rkg *RedisKeyGenerator) buildLabelString(labels map[string]string) string {
    // 检查是否有resourcePool标签
    if poolName, exists := labels[LabelResourcePool]; exists {
        // extraPools格式：cfc:common:poolName:userLabel
        userLabel := rkg.getUserLabel(labels)
        return fmt.Sprintf("cfc:common:%s:%s", poolName, userLabel)
    } else {
        // 默认池格式：cfc:common:userLabel
        userLabel := rkg.getUserLabel(labels)
        return fmt.Sprintf("cfc:common:%s", userLabel)
    }
}

// 获取用户标签
func (rkg *RedisKeyGenerator) getUserLabel(labels map[string]string) string {
    if vipUser := labels[LabelVipUser]; vipUser != "" {
        return vipUser
    }
    return "dummyUSER"
}

// 兼容性方法：解析现有Key格式
func (rkg *RedisKeyGenerator) ParseKeyLabels(key string) map[string]string {
    parts := strings.Split(key, ":")
    if len(parts) < 4 {
        return nil
    }

    labels := make(map[string]string)

    // 检查是否为新格式（包含池子名称）
    if len(parts) >= 5 && parts[3] != "common" {
        // 新格式：kun:type:cfc:common:poolName:userLabel::memory
        if len(parts) >= 6 {
            labels[LabelResourcePool] = parts[4]
            if parts[5] != "" {
                labels[LabelVipUser] = parts[5]
            }
        }
    } else {
        // 传统格式：kun:type:cfc:common:userLabel::memory
        if len(parts) >= 5 && parts[4] != "" {
            labels[LabelVipUser] = parts[4]
        }
    }

    return labels
}
```

#### 2.4.2 Redis Key完整格式对比

##### 当前Redis中存在的所有Key类型（按模块职责分类）

**1. cron模块创建和管理的Key**
```bash
# 冷启动节点集合（ZSet，cron扩容时添加新节点）
kun:cold_node:{cfc:common:dummyUSER}:128
kun:cold_node:{cfc:common:vipUSER}:256

# 节点锁（用于扩缩容时的节点锁定）
kun:node_lock:node-001
kun:node_lock:node-002

# 扩容任务状态（用于跟踪扩容进度）
kun:scaling_task:cluster-001:task-123
kun:scaling_task:cluster-002:task-456

# 集群状态缓存（用于缓存集群信息）
kun:cluster_cache:cce-u4clzq75
kun:cluster_cache:cce-abc12345
```

**2. poolmanager模块创建和管理的Key**
```bash
# 热启动节点集合（ZSet，从cold_node转移过来或直接添加）
kun:warm_node:{cfc:common:dummyUSER}:128
kun:warm_node:{cfc:common:dummyUSER}:256

# 热启动Pod（ZSet，存储Pod信息和并发数）
kun:warm_pod:cfc:common:dummyUSER:::128:abc123def456
kun:warm_pod:cfc:common:dummyUSER:::256:def456abc123

# 预留节点和Pod（用于函数预留实例）
kun:reserved_node:{cfc:common:vipUSER}:128
kun:reserved_pod:cfc:common:vipUSER:::128:abc123def456

# 可疑Pod记录（用于异常检测）
kun:dubious_pod:cfc:common:dummyUSER:::128:abc123def456
```

**3. 共享Key（多模块使用）**
```bash
# etcd相关的分布式锁（eventhub、cron、poolmanager都可能使用）
kun:etcd_lock:function_commit_id_123
kun:etcd_lock:node_scaling_456

# 统计和监控Key（由各模块写入，监控系统读取）
kun:stats:pod_count:cfc:common:dummyUSER
kun:stats:node_count:cfc:common:dummyUSER
kun:metrics:latency:get_pod:cfc
kun:metrics:throughput:scaling:cron
```

**Key的生命周期和职责**
```bash
# 节点生命周期：
# 1. cron模块扩容 -> 创建cold_node
# 2. poolmanager调度 -> 从cold_node转移到warm_node
# 3. cron模块缩容 -> 删除warm_node和cold_node

# Pod生命周期：
# 1. poolmanager调度 -> 创建warm_pod
# 2. poolmanager回收 -> 删除warm_pod
# 3. cron模块清理 -> 清理残留Pod
```

**注意：不存在的Key类型**
```bash
# 以下Key类型在当前代码中不存在：
# kun:cold_pod:... （冷启动不创建Pod Key）
# kun:warm_pod_index:... （没有单独的Pod索引Key）
# kun:cold_pod_index:... （没有单独的Pod索引Key）
```

##### ResourcePool改造后的Key格式

**1. 默认池（保持传统格式，完美兼容）**
```bash
# cron模块Key（格式不变）
kun:cold_node:{cfc:common:dummyUSER}:128          # cron扩容时添加
kun:node_lock:node-001                            # cron扩缩容锁定
kun:scaling_task:cluster-001:task-123             # cron扩容任务
kun:cluster_cache:cce-u4clzq75                    # cron集群缓存

# poolmanager模块Key（格式不变）
kun:warm_node:{cfc:common:dummyUSER}:128          # 从cold_node转移或直接添加
kun:warm_pod:cfc:common:dummyUSER:::128:abc123def456  # poolmanager创建
kun:reserved_node:{cfc:common:vipUSER}:128        # poolmanager预留节点
kun:reserved_pod:cfc:common:vipUSER:::128:abc123def456  # poolmanager预留Pod
kun:dubious_pod:cfc:common:dummyUSER:::128:abc123def456  # poolmanager异常Pod

# 共享Key（格式不变）
kun:etcd_lock:function_commit_id_123              # 分布式锁
kun:stats:pod_count:cfc:common:dummyUSER          # 统计监控
kun:metrics:latency:get_pod:cfc                   # 性能监控
```

**2. extraPools（新格式，包含池子名称）**
```bash
# cron模块Key（在ClusterLabels中插入池子名称）
kun:cold_node:{cfc:common:ubuntu2204-pool:dummyUSER}:128     # cron扩容时添加
kun:cold_node:{cfc:common:gpu-pool:dummyUSER}:256           # cron扩容时添加

# cron模块Key（在Key前缀后插入池子名称）
kun:node_lock:ubuntu2204-pool:node-001                      # cron扩缩容锁定
kun:scaling_task:ubuntu2204-pool:cluster-001:task-123       # cron扩容任务

# poolmanager模块Key（在ClusterLabels中插入池子名称）
kun:warm_node:{cfc:common:ubuntu2204-pool:dummyUSER}:128     # 从cold_node转移
kun:warm_node:{cfc:common:gpu-pool:dummyUSER}:256           # 从cold_node转移

kun:warm_pod:cfc:common:ubuntu2204-pool:dummyUSER:::128:def456abc123  # poolmanager创建
kun:warm_pod:cfc:common:gpu-pool:dummyUSER:::256:abc123def456         # poolmanager创建

kun:reserved_node:{cfc:common:ubuntu2204-pool:vipUSER}:128   # poolmanager预留节点
kun:reserved_pod:cfc:common:ubuntu2204-pool:vipUSER:::128:abc123def456  # poolmanager预留Pod

kun:dubious_pod:cfc:common:ubuntu2204-pool:dummyUSER:::128:abc123def456  # poolmanager异常Pod

# 共享Key（统计监控按池子分类）
kun:stats:pod_count:cfc:common:ubuntu2204-pool:dummyUSER    # 统计监控
kun:metrics:latency:get_pod:cfc:ubuntu2204-pool             # 性能监控
```

##### Key格式规律总结

**Key格式规律总结**

**poolmanager模块Key格式**：
```bash
# 传统格式（默认池）
kun:{type}:{cfc:common:userLabel}:memory
kun:{type}:cfc:common:userLabel:::memory:commitID

# 新格式（extraPools）
kun:{type}:{cfc:common:poolName:userLabel}:memory
kun:{type}:cfc:common:poolName:userLabel:::memory:commitID
```

**cron模块Key格式**：
```bash
# 传统格式（默认池）
kun:node_lock:nodeID
kun:scaling_task:clusterID:taskID

# 新格式（extraPools）
kun:node_lock:poolName:nodeID
kun:scaling_task:poolName:clusterID:taskID
```

**关键变化**：
- poolmanager模块：在ClusterLabels中插入`{poolName}`
- cron模块：在Key前缀后插入`{poolName}`
- 默认池不插入池子名称，保持完全兼容
- extraPools插入具体的池子名称

**兼容性保证**：
- 现有Key格式完全不变
- 新Key格式只在使用extraPools时生效
- 可以实现渐进式迁移
- 新旧格式可以并存运行

**实际Key示例对比**：
```bash
# 默认池（兼容现有格式）
kun:warm_node:{cfc:common:dummyUSER}:128
kun:warm_pod:cfc:common:dummyUSER:::128:abc123def456

# extraPools（新格式）
kun:warm_node:{cfc:common:ubuntu2204-pool:dummyUSER}:128
kun:warm_pod:cfc:common:ubuntu2204-pool:dummyUSER:::128:def456abc123
```
```

### 2.5 eventhub模块集成

#### 2.5.1 eventhub核心集成
```go
// cmd/eventhub/core.go
package eventhub

import (
    "github.com/baidu/faas-kun/pkg/config"
    "github.com/baidu/faas-kun/pkg/api"
)

type EventhubCore struct {
    // ... 现有字段
    configManager *config.ClusterConfigManager
}

func NewEventhubCore(runOptions *options.EventhubOptions, stopCh <-chan struct{}) (*EventhubCore, error) {
    // ... 现有初始化逻辑

    // 创建集群配置管理器
    etcdClient := etcd.NewEtcdClient(runOptions.EtcdEndpoints)
    configManager := config.NewClusterConfigManager("eventhub", etcdClient)

    ec := &EventhubCore{
        // ... 现有字段
        configManager: configManager,
    }

    return ec, nil
}

// 在getFreePod函数中使用ResourcePool选择
func getFreePod(ctx *context.InvokeContext, secureContainer bool) (*api.PodInfo, *api.PodTrace, error) {
    // 现有逻辑保持不变...
    labels = ctx.LabelMgr.GetLabel(ctx.OwnerUser.ID, ctx.Function, clusterVipLabel)

    // 新增：根据运行时选择ResourcePool
    clusterID := ctx.getClusterIDFromUserContext()
    resourcePool := ctx.configManager.SelectResourcePool(clusterID, ctx.Runtime.Runtime)

    // 只有非默认池才设置resourcePool标签
    if resourcePool != "" && resourcePool != "default" {
        labels[api.LabelResourcePool] = resourcePool
    }
    // 默认池不设置标签，保持传统Redis Key格式

    // 调用poolmanager，Redis Key会根据是否有resourcePool标签自动选择格式
    input := &api.GetFreePodRequest{
        Labels: labels,
        // ... 其他参数
    }
    return c.PodClient.GetFreePod(ctx.Context, input)
}

// 从用户上下文获取集群ID的实现
func (ctx *context.InvokeContext) getClusterIDFromUserContext() string {
    // 方案1: 从用户标签中获取集群信息（兼容现有的用户维度集群选择）
    if clusterLabel := ctx.LabelMgr.GetClusterLabel(); clusterLabel != "" {
        return clusterLabel
    }

    // 方案2: 从函数配置中获取集群信息
    if ctx.Function != nil && ctx.Function.ClusterID != "" {
        return ctx.Function.ClusterID
    }

    // 方案3: 从用户配置获取默认集群（保持现有逻辑）
    if ctx.OwnerUser != nil && ctx.OwnerUser.DefaultClusterID != "" {
        return ctx.OwnerUser.DefaultClusterID
    }

    // 方案4: 从全局配置获取默认集群
    return ctx.getDefaultClusterID()
}
```

### 2.6 cron模块集成

#### 2.6.1 cron模块扩容逻辑
```go
// cmd/cron/manager.go
package cron

type CronManager struct {
    // ... 现有字段保持不变
    // 保持现有的配置获取方式
}

// 扩容时根据ResourcePool配置设置标签
func (cm *CronManager) ScaleUpNodes(clusterID string, nodeCount int) error {
    // 使用现有方式获取集群配置
    config := cm.getCurrentClusterConfig(clusterID)  // 保持现有逻辑

    if cm.isResourcePoolEnabled(config) {
        // 使用新的ResourcePool扩容逻辑
        return cm.scaleUpWithResourcePool(config, nodeCount)
    } else {
        // 使用传统扩容逻辑（保持不变）
        return cm.scaleUpLegacy(clusterID, nodeCount)
    }
}

// 检查ResourcePool是否启用（新增辅助方法）
func (cm *CronManager) isResourcePoolEnabled(config *ClusterConfig) bool {
    return config.ResourcePoolConfig != nil && config.ResourcePoolConfig.Enabled
}

func (cm *CronManager) scaleUpWithResourcePool(config *ClusterConfig, nodeCount int) error {
    // 检查默认池是否需要扩容
    defaultEffective := cm.configManager.GetEffectivePoolConfig(config, "default")
    if defaultEffective != nil && cm.shouldScalePool("default", defaultEffective) {
        // 为默认池创建节点（不设置resourcePool标签）
        nodeConfig := &NodeConfig{
            ContainerImage: defaultEffective.ContainerImage,
            Labels:        map[string]string{}, // 默认池不设置特殊标签
            ScalingSize:   defaultEffective.ScalingUpOptions.SingleScalingRequestSize,
            MaxNodes:      defaultEffective.ScalingUpOptions.MaxNodeCount,
        }

        err := cm.createNodesWithConfig(nodeConfig, nodeCount)
        if err != nil {
            return err
        }
    }

    // 检查extraPools中的池子是否需要扩容
    if config.ResourcePoolConfig != nil {
        for poolName := range config.ResourcePoolConfig.ExtraPools {
            // 获取有效的池子配置（合并集群配置）
            effectiveConfig := cm.configManager.GetEffectivePoolConfig(config, poolName)
            if effectiveConfig == nil {
                continue
            }

            // 根据池子的扩容配置决定是否需要扩容
            if !cm.shouldScalePool(poolName, effectiveConfig) {
                continue
            }

            // 为新节点设置ResourcePool标签
            nodeLabels := map[string]string{
                "resourcePool": poolName,  // 设置resourcePool标签
            }

            // 使用有效配置（可能复用了集群配置）
            nodeConfig := &NodeConfig{
                ContainerImage: effectiveConfig.ContainerImage,
                Labels:        nodeLabels,
                ScalingSize:   effectiveConfig.ScalingUpOptions.SingleScalingRequestSize,
                MaxNodes:      effectiveConfig.ScalingUpOptions.MaxNodeCount,
            }

            // 创建节点
            err := cm.createNodesWithConfig(nodeConfig, nodeCount)
            if err != nil {
                return err
            }
        }
    }

    return nil
}

// 判断池子是否需要扩容（使用有效配置）
func (cm *CronManager) shouldScalePool(poolName string, effectiveConfig *EffectivePoolConfig) bool {
    // 检查池子的自动扩容配置
    if effectiveConfig.ScalingOptions.AutoScalingType == "manual" {
        return false // 手动扩容，不自动处理
    }

    // 检查当前节点数量是否达到上限
    currentNodeCount := cm.getCurrentNodeCount(poolName)
    if currentNodeCount >= effectiveConfig.ScalingUpOptions.MaxNodeCount {
        return false // 已达到最大节点数
    }

    // 检查资源使用情况和阈值
    for memorySize, threshold := range effectiveConfig.ScalingOptions.ThresholdMap {
        if cm.checkResourceThreshold(poolName, memorySize, threshold) {
            return true // 需要扩容
        }
    }

    return false
}

// 检查资源阈值
func (cm *CronManager) checkResourceThreshold(poolName, memorySize string, threshold ThresholdConfig) bool {
    // 获取当前资源使用情况
    usage := cm.getResourceUsage(poolName, memorySize)

    // 检查是否触发扩容阈值
    if usage.UtilizationRatio > threshold.ScalingUpTrigger {
        return true
    }

    return false
}
```

## 3. 实施计划

### 3.1 分阶段实施策略

#### 3.1.1 第一阶段：基础架构准备（2周）
**目标**：完成统一配置框架和ResourcePool基础架构

**主要工作**：
1. **扩展etcd配置结构**
   - 在现有集群配置中增加resourcePoolConfig字段
   - 实现ClusterConfigManager配置管理器（主要供eventhub使用）
   - cron模块保持现有配置获取方式
   - 添加配置验证和热更新机制

2. **实现ResourcePool选择逻辑**
   - 运行时与ResourcePool映射算法
   - 兼容性检查和降级策略
   - 集群级开关控制

3. **扩展Labels系统**
   - 增加LabelResourcePool常量
   - 修改RequestLabels生成逻辑
   - 保持向后兼容性

**验收标准**：
- 配置可以正确加载和热更新
- ResourcePool选择逻辑单元测试通过
- 现有功能不受影响

#### 3.1.2 第二阶段：Redis Key扩展和架构预留（2周）
**目标**：实现Redis Key自动分片和多资源架构预留

**主要工作**：
1. **Key生成逻辑扩展**
   - 修改NodeIndex.Key()和PodIndex.Key()方法
   - 实现ResourcePool自动分片
   - 保持兼容性开关

2. **多资源架构预留**
   - 预留多资源接口定义
   - 建立可扩展的Redis Key命名规范
   - 简化ResourcePool配置结构（移除resourceLimits和nodeSelector）

3. **性能优化**
   - Redis Key分片性能测试
   - 内存资源操作性能基准测试
   - 监控和告警机制

**验收标准**：
- 新旧Key格式可以并存
- 内存资源分配正常工作
- 架构支持后续多资源扩展
- 性能指标不下降

#### 3.1.3 第三阶段：模块集成和调度逻辑（2周）
**目标**：将ResourcePool集成到eventhub和poolmanager调度流程

**主要工作**：
1. **eventhub集成**
   - 集成ClusterConfigManager
   - 在getFreePod中添加ResourcePool选择逻辑
   - 保持API兼容性

2. **poolmanager适配**
   - 确保ResourcePool标签正确传递
   - 适配新的Redis Key格式
   - 添加降级机制

3. **reserve_ctrl适配**
   - 适配新的Redis Key格式
   - 保持现有内存资源分配逻辑
   - 预留多资源接口（不实现）

**验收标准**：
- 端到端调度流程正常工作
- 新旧模式可以无缝切换
- 调度延迟不增加

#### 3.1.4 第四阶段：cron模块扩展和系统集成（2周）
**目标**：实现ResourcePool扩缩容和系统整体集成

**主要工作**：
1. **cron模块扩展**
   - 支持ResourcePool维度的扩缩容
   - 实现节点ResourcePool标签自动设置
   - 保持现有配置获取方式和DaemonSet配置方式

2. **系统集成测试**
   - 端到端功能测试
   - 性能基准测试
   - 兼容性验证
   - 新旧模式切换测试

3. **监控和运维工具**
   - ResourcePool状态监控
   - 配置变更审计
   - 故障排查工具

**验收标准**：
- ResourcePool扩缩容正常工作
- 系统整体功能正常
- 新旧模式可以平滑切换
- 监控和告警完善

### 3.2 风险控制措施

#### 3.2.1 技术风险控制
1. **Redis性能影响**
   - 在测试环境进行充分的性能测试
   - 实现渐进式迁移，可随时回滚
   - 监控Redis关键指标

2. **配置一致性问题**
   - 统一配置管理，避免配置分散
   - 实现配置验证机制
   - 建立配置变更审计日志

#### 3.2.2 业务风险控制
1. **大客户集群影响**
   - 默认关闭ResourcePool功能
   - 实现完善的降级机制
   - 建立客户沟通机制

2. **服务可用性影响**
   - 采用蓝绿部署策略
   - 实现流量分级切换
   - 建立实时监控和告警

## 4. 监控和运维

### 4.1 关键指标监控
```go
type ResourcePoolMetrics struct {
    // 调度性能指标
    SchedulingLatency     map[string]float64  // 按池子统计调度延迟
    SchedulingSuccessRate map[string]float64  // 按池子统计成功率

    // 资源利用率指标
    PoolUtilization       map[string]float64  // 各池子资源利用率
    NodeDistribution      map[string]int64    // 节点分布情况

    // 兼容性指标
    LegacyTrafficPercent  float64             // 传统流程流量占比
    NewTrafficPercent     float64             // 新流程流量占比

    // Redis性能指标
    RedisKeyCount         map[string]int64    // 各类型Key数量
    RedisOperationLatency map[string]float64  // Redis操作延迟
}
```

### 4.2 故障排查工具
```bash
# ResourcePool状态检查工具
kubectl get nodes -l resourcePool=ubuntu1604-pool
kubectl describe node <node-name>

# Redis Key分布检查
redis-cli --scan --pattern "kun:warm_node:*:ubuntu1604-pool:*"
redis-cli --scan --pattern "kun:warm_node:*:ubuntu2204-pool:*"

# 配置验证工具
cfcctl config validate --cluster-id <cluster-id>
cfcctl resourcepool list --cluster-id <cluster-id>
cfcctl resourcepool status --pool-name ubuntu1604-pool
```

## 5. 总结

### 5.1 技术创新点
1. **配置管理优化**：解决了运行时与资源池映射关系维护的一致性问题
2. **Redis Key自动分片**：通过ResourcePool标签实现自动分片，提升性能
3. **配置结构优化**：极简的池子到运行时映射，大幅降低配置复杂度
4. **架构扩展性设计**：预留多资源接口，支持后续CPU和GPU资源扩展
5. **渐进式迁移**：完美的向后兼容性，支持集群级灰度切换

### 5.2 业务价值
1. **降低维护成本**：配置结构简化，统一管理
2. **提升调度效率**：细粒度调度和Redis Key分片
3. **加速新功能上线**：新运行时可以快速在现有集群中部署
4. **增强系统扩展性**：为未来支持更多资源类型奠定基础
5. **保证业务连续性**：完美的向后兼容性和渐进式迁移

### 5.3 架构优势
1. **向后兼容**：现有大客户集群无需任何改动
2. **渐进式演进**：支持按集群的灰度切换
3. **配置极简化**：移除复杂的resourceLimits和nodeSelector，只需resourcePool标签
4. **扩容更简单**：cron模块扩容时直接传入resourcePool=池子名即可
5. **性能优化**：Redis Key分片显著提升调度性能

通过这次重构，CFC调度系统将从传统的集群维度调度演进为现代化的ResourcePool调度架构，完美解决了OS兼容性、配置管理复杂度、调度性能等核心问题，为百度智能云函数计算服务的长期发展提供强有力的技术支撑。
