cfc-调度、资源管理重构

# #现状
## ##调度
由eventhub负责增加label，然后调度由poolmanager模块负责，主要采用的是从用户维度在集群层面的调度





## ##资源管理
目前资源管理由cron模块负责，也是以集群维度对集群中的资源进行伸缩，初始化

集群维度etcd动态配置示例

```
{
    "k8sOptions": {
        "endpoint": "https://************:2/api",
        "namespace": "poolmgr",
        "daemonSetNamespace": "faas",
        "containerImage": "registry.baidubce.com/cfc_dev/runner:ubuntu1604-0720-java11",
        "imagePullPolicy": "IfNotPresent",
        "cceClusterAPIVersion": "V2",
        "volumeHostPath": "/var/faas/invoker/run",
        "volumeMountPath": "/var/run/faas",
        "imagePullSecrets": [
            "cfc-resource-ccr"
        ],
        "clusterCA": "apiVersion: v1\nclusters:\n- cluster:\n    certificate-authority-data: LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1JSURvRENDQW9pZ0F3SUJBZ0lVTmxndXZHSzFrUEpreitCRlFXenVxMWx1WGdvd0RRWUpLb1pJaHZjTkFRRUwKQlFBd1p6RUxNQWtHQTFVRUJoTUNRMDR4RURBT0JnTlZCQWdUQjBKbGFVcHBibWN4RURBT0JnTlZCQWNUQjBKbAphVXBwYm1jeERqQU1CZ05WQkFvVEJXcHdZV0Z6TVJRd0VnWURWUVFMRXd0amJHOTFaRzVoZEdsMlpURU9NQXdHCkExVUVBeE1GYW5CaFlYTXdJQmNOTWpVd05qRTNNRE15T1RBd1doZ1BNakV5TlRBMU1qUXdNekk1TURCYU1HY3gKQ3pBSkJnTlZCQVlUQWtOT01SQXdEZ1lEVlFRSUV3ZENaV2xLYVc1bk1SQXdEZ1lEVlFRSEV3ZENaV2xLYVc1bgpNUTR3REFZRFZRUUtFd1ZxY0dGaGN6RVVNQklHQTFVRUN4TUxZMnh2ZFdSdVlYUnBkbVV4RGpBTUJnTlZCQU1UCkJXcHdZV0Z6TUlJQklqQU5CZ2txaGtpRzl3MEJBUUVGQUFPQ0FROEFNSUlCQ2dLQ0FRRUEycmxVU2JWUThYeVMKOGVYbTVwVkVtanBnby9objNXUjRrbWx4MUhzL1FEaG00QXBXSXM0ZTk3eW5MWElreVo1R0ppNnBhMnpkRW9uaAo4Q3lqY2ZxRU1maWszNWZ5VmI5MDFyNk1yYXMxbTViTml4dUdwWkdnYkxNUEI3dnU5Z3p2c3oxQUx5UFVTS3NqCll5eU1OQUlRZXdIVzIvamhtc1pMNTQ2R2xFMHJGTVBNZGVSNGphczdTQVNsd1IxVkVKOEtPWnVIOS95MEY0V3gKendET2phRkJFdWMrL1hNWTZidTNMckY1K1ZIamFqQnRxMVliUVdWRXRMMnpRcHJBT3NNTFIrWDhKaExWeTkxYgp0ODZQcnN2YUFWSWJFNHE4WnVyd3krUFAzbjZETG16azd0dHRJcVp4eC9hMzBCK1BQeDMyT25NTlBSalIwdE1sCjJoQk1naEY0bHdJREFRQUJvMEl3UURBT0JnTlZIUThCQWY4RUJBTUNBUVl3RHdZRFZSMFRBUUgvQkFVd0F3RUIKL3pBZEJnTlZIUTRFRmdRVXBOY3VOc2NwL09YSTlOQ0VSaldvOFVEM3M2b3dEUVlKS29aSWh2Y05BUUVMQlFBRApnZ0VCQU1OeWhhaGFJcytDYzJKbUdmckgwTDk3NUg2TjAzTFhsdGlWWHZKZHcreSsrNXR6UXFDYk16U2IwaUsrCjhnLzlSLzFzL25qeEY4d25zeHI2ay9LSGRydTZOa01ZSE5vQklEVHdBODZtYWFva1ZtWmRTaWJqQzh3QmNoWDIKSWJSazNLWkxCeTliNW12Y0dDSkdrUldkNzhvM1diTTZONEZUQUw5WFRKWUxhRjhjT0FMeFJkblJOL252aUwvVwoxL3BvMXdYY1IyWkpIa3ZvVHVXT2VJUVF4NGg4amNTNHpjOFBYV2d4dm4xbXVNcUVUbHhhOHJpc0ZzRXVrcFRlCmpoQjFOcmNWaEdqL0hqZjc5Nms4UGo2cjNIU0FvWW9iQy9LMlhHL0xWOTJrSTJUSUdsMjRib2V4VklhRFdob24KMmZpZzZCZWIwOHZ5Nzh4RWhPWnNpeUtDWlZRPQotLS0tLUVORCBDRVJUSUZJQ0FURS0tLS0tCg==\n    server: https://************:2\n  name: kubernetes\ncontexts:\n- context:\n    cluster: kubernetes\n    user: 6d4235861387423f8cbee1e5c2e0c7f9\n  name: 6d4235861387423f8cbee1e5c2e0c7f9@kubernetes\ncurrent-context: 6d4235861387423f8cbee1e5c2e0c7f9@kubernetes\nkind: Config\npreferences: {}\nusers:\n- name: 6d4235861387423f8cbee1e5c2e0c7f9\n  user:\n    client-certificate-data: 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\n    client-key-data: ********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n",
        "runAsUser": 0,
        "dsReleaseConfigMap": {
            "funclet": {
                "yamlFilePath": "ds-conf/funclet.yaml",
                "imageID": "registry.baidubce.com/cfc_dev/funclet:20250701",
                "md5": "3ce71ef3041519f3cb660a151a02e934"
            },
            "invoker": {
                "yamlFilePath": "ds-conf/invoker.yaml",
                "imageID": "registry.baidubce.com/cfc_dev/invoker:20250701",
                "md5": "9c56e919834538f6c19baf7f4279b252"
            },
            "logstash": {
                "yamlFilePath": "ds-conf/logstash.yaml",
                "imageID": "registry.baidubce.com/cfc_dev/logstash:lz20220711-v1",
                "md5": "a870ffff78442796fa6740d3b59b37ca"
            },
            "runtimes": {
                "yamlFilePath": "ds-conf/runtime.yaml",
                "imageID": "registry.baidubce.com/cfc_dev/runtime:ubuntu1604-0722-py3.9-java",
                "md5": "19b78d02a3577e50dff46026c8bf468d"
            }
        }
    },
    "bceOptions": {
        "accessKeyID": "396528c7d8ab4f9fbd724ca1cd708462",
        "accessKeySecret": "8ce85ae89f4d4ae3b7c851f54a16a3af",
        "cceEndpoint": "http://cce.gz.baidubce.com",
        "securityGroupID": "df234555-afd0-4eaf-a62a-f43f2eb0d555",
        "securityGroupShortId": "g-rqvf0a24wy7z",
        "securityGroupName": "cce-u4clzq75-worker-k4qnmmdq",
        "bccEndpoint": "http://bcclogic.gz.bce-internal.baidu.com",
        "bccOpenAPIEndpoint": "http://bcc.gz.baidubce.com",
        "dockerHubUser": "012b81bdec5946cbaa7950234a283d2e",
        "dockerHubPasswd": "Faas!123",
        "ccrEndpoint": "http://ccr.baidubce.com",
        "ccrProjectName": "cfc_dev"
    },
    "nodeConfig": {
        "serviceType": "cfc",
        "maxPodDeploy": 40,
        "sourcePodExpire": {
            "dasou": 600
        },
        "memoryReserved": 0,
        "podMemoryReservedRatio": 1,
        "cpuUsageReservedRatio": 0.99,
        "applyImageBatchNodeSize": 1,
        "maxNodeExpire": 86400,
        "autoApplyImage": true,
        "syncNode": true
    },
    "scalingUpOptions": {
        "zoneConfigMap": {
            "zoneA": {
                "subnetUUID": "5eb8ef20-7d15-4f65-b5dd-7f5251aee892",
                "vpcSubnetID": "sbn-z90wn7r36k9h",
                "instanceTypes": [
                    10,
                    13
                ]
            },
            "zoneB": {
                "subnetUUID": "96a4642e-9922-426c-bcf4-c572742e5f77",
                "vpcSubnetID": "sbn-m9s017xm6jx7",
                "instanceTypes": [
                    10,
                    13
                ]
            },
            "zoneC": {
                "subnetUUID": "febc13c4-a1db-44b4-a950-bae8e5837531",
                "vpcSubnetID": "sbn-tgupjhd80y2e",
                "instanceTypes": [
                    10,
                    13
                ]
            },
            "zoneD": {
                "subnetUUID": "9b57db2e-4ce0-4423-8185-ddee0db1fcf3",
                "vpcSubnetID": "sbn-jd48cghjtmq2",
                "instanceTypes": [
                    10,
                    13
                ]
            },
            "zoneF": {
                "subnetUUID": "e72a5470-ae6c-453f-bf60-0710e6d70d64",
                "vpcSubnetID": "sbn-820tvcrb2uqy",
                "instanceTypes": [
                    10,
                    13
                ]
            }
        },
        "flavors": [
            {
                "cpu": 2,
                "Memory": 4,
                "rootDiskSizeInGb": 40,
                "ssdDiskSize": 15,
                "maxPodDeploy": 22,
                "spec": "bcc.c4.c2m4",
                "instanceType": 10,
                "charge": 0.0048972,
                "cpuRequestLowerLimit": 42,
                "runningPodRatio": 0.8,
                "nodeUsedRatio": 0.9
            },
            {
                "cpu": 2,
                "Memory": 4,
                "rootDiskSizeInGb": 40,
                "ssdDiskSize": 15,
                "maxPodDeploy": 22,
                "spec": "bcc.c5.c2m4",
                "instanceType": 10,
                "charge": 0.007217,
                "cpuRequestLowerLimit": 42,
                "runningPodRatio": 0.85,
                "nodeUsedRatio": 0.9
            },
            {
                "cpu": 2,
                "Memory": 8,
                "rootDiskSizeInGb": 40,
                "ssdDiskSize": 15,
                "maxPodDeploy": 35,
                "spec": "bcc.g4.c2m8",
                "instanceType": 10,
                "charge": 0.008548,
                "cpuRequestLowerLimit": 42,
                "runningPodRatio": 0.85,
                "nodeUsedRatio": 0.9
            },
            {
                "cpu": 4,
                "Memory": 8,
                "rootDiskSizeInGb": 40,
                "ssdDiskSize": 15,
                "maxPodDeploy": 53,
                "spec": "bcc.c4.c4m8",
                "instanceType": 10,
                "charge": 0.00097944,
                "cpuRequestLowerLimit": 42,
                "runningPodRatio": 0.8,
                "nodeUsedRatio": 0.9
            },
            {
                "cpu": 4,
                "Memory": 16,
                "rootDiskSizeInGb": 48,
                "ssdDiskSize": 15,
                "maxPodDeploy": 113,
                "spec": "bcc.g4.c4m16",
                "instanceType": 10,
                "charge": 0.0132472,
                "cpuRequestLowerLimit": 30,
                "runningPodRatio": 0.85,
                "nodeUsedRatio": 0.98
            },
            {
                "cpu": 8,
                "Memory": 16,
                "rootDiskSizeInGb": 40,
                "ssdDiskSize": 15,
                "maxPodDeploy": 114,
                "spec": "bcc.c4.c8m16",
                "instanceType": 10,
                "charge": 0.0195888,
                "cpuRequestLowerLimit": 42,
                "runningPodRatio": 0.8,
                "nodeUsedRatio": 0.9
            },
            {
                "cpu": 8,
                "Memory": 32,
                "rootDiskSizeInGb": 100,
                "ssdDiskSize": 15,
                "maxPodDeploy": 178,
                "spec": "bcc.g4.c8m32",
                "instanceType": 10,
                "charge": 0.0195888,
                "cpuRequestLowerLimit": 42,
                "runningPodRatio": 0.8,
                "nodeUsedRatio": 0.9
            }
        ],
        "region": "gz",
        "bccImageID": "713042ce-6e0f-4577-81df-000e95b3c353",
        "osType": "linux",
        "osVersion": "7.5 x86_64 (64bit)",
        "osArch": "x86_64 (64bit)",
        "bccOsVersion": "7.5",
        "osName": "CentOS",
        "ssdMountPath": "",
        "adminPassType": "custom",
        "adminPass": "Faas!123",
        "singleScalingRequestSize": 1
    },
    "scalingOptions": {
        "autoScalingType": "nil",
        "thresholdMap": {
            "128": {
                "unoccupiedRedundancy": 3,
                "scalingUpTrigger": 1.1,
                "scalingDownTrigger": 1.3,
                "ActualRedundancy": 3
            }
        },
        "scalingDown": true,
        "scalingTimeout": 600,
        "scalingDownTimeout": 900,
        "maxscalingDownSize": 1,
        "scalingDownCycleCnt": 1,
        "scalingDownWhiteHours": [
            12,
            0
        ],
        "k8sRawInitializingRatio": 0.4,
        "scalingUpNodeNumThreshold": 10
    },
    "cceClusterUUID": "cce-u4clzq75",
    "matchLabel": {},
    "status": 0
}
```






# #问题
|问题|
|-|
|目前common集群使用的基础镜像os为ubuntu1604，部分高版本运行时(exp. nodejs.18及以上)依赖高版本系统库(glibc...)，无法在当前的comon集群运行。目前要兼容需要创建新的集群，让新的运行时调度到新集群上，随着未来运行时和运行时版本不断变化，每次新建集群和维护成本过高。|
|目前维护了两个版本的runtmes镜像，每次新增运行时都需要在两个基础镜像上适配，维护成本高|
|目前只支持动态内存分配，因为节点可用内存资源通过redis的zset中的score保存，由poolmanager调度并扣减可用内存，然后向funclet发送请求warm up一个pod，但后需要拓展cpu资源、gpu资源分配，当前架构拓展性不足，如果直接新增一个zset维护节点的cpu资源话是不是会涉及内存、cpu两步操作的问题？|





# #目标
1. 更细粒度(节点)打通调度和资源管理，让集群从一个大池子转换为多个基础os镜像共存的小池子，在cron层面是不是只需要改动扩缩容模块？在扩容的时候为节点设置resourcePool的label，由resourcePool来标识小池子，gpu也能兼容？可以合理利用etcd动态配置能力。
2. 收敛runtimes镜像，合并为一个，根据不同的os镜像挂载适配的runtime（人工完成）
3. 兼容旧的调度和资源管理，避免影响大客户集群，加开关
4. 希望能在兼容动态内存分配的基础上完成动态cpu分配
5. 尽量复用已有功能，尽可能使用etcd动态配置，可以在etcd集群配置中增加字段设计
6. 需要考虑运行时和资源池的映射关系应该维护在哪里，是直接硬编码在eventhub模块，还是通过etcd动态配置来维护
7. 现状是我们有多个集群，每个集群的etcd配置是上面的json格式，所以开关应该是集群层面的开关，未开启时维持现状，开启时使用新的调度和资源管理，在这种情况下其他各相关模块如何监听到开关的变化
8. cron模块拉取的集群etcd配置文件中需要包含资源池以及资源池扩缩容配置，资源池中应该有镜像配置，资源池名(通常为os名例如ubuntu1604)等基本信息，这里存在的问题是运行时和资源池的映射关系在哪里维护，如果在eventhub中写死，那么资源池名称就分别维护在了etcd和eventhub中，如果维护在etcd中，那么eventhub是不是得额外拉取所有集群的etcd配置信息？相当于eventhub模块和cron模块都依赖于这个配置，感觉耦合度有点高，所以这里有没有一个更加完美的方法