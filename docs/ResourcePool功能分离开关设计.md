# ResourcePool双开关设计

## 🎯 设计目标

每个独立的资源池都有双开关控制，提供细粒度的功能管理：
- **第一级开关**：控制某个vipUser下所有资源池的功能（调度/扩缩容）
- **第二级开关**：控制单个资源池的启用/禁用

## 🔧 开关设计

### 1. 配置结构

```go
type ResourcePoolConfig struct {
    SchedulingEnabled  bool                     `json:"schedulingEnabled"`  // poolmanager调度开关
    ScalingEnabled     bool                     `json:"scalingEnabled"`     // cron扩缩容开关
    Version            string                   `json:"version"`            // 配置版本
    UserPoolMappings   map[string]string        `json:"userPoolMappings"`   // 用户池映射
    ExtraResourcePools map[string]*ResourcePool `json:"extraResourcePools"` // 资源池配置
}
```

### 2. 双开关功能说明

| 开关级别 | 开关名称 | 控制模块 | 功能描述 | 影响范围 |
|---------|---------|---------|---------|---------|
| 第一级 | `schedulingEnabled` | poolmanager | 控制某个vipUser下所有资源池的调度功能 | 该vipUser下所有资源池的函数调度 |
| 第一级 | `scalingEnabled` | cron | 控制某个vipUser下所有资源池的扩缩容功能 | 该vipUser下所有资源池的节点扩缩容 |
| 第二级 | `ResourcePool.enabled` | 通用 | 控制单个资源池的启用/禁用 | 仅当前资源池 |

### 3. 开关组合场景

#### 场景1：仅启用调度，禁用扩缩容
```json
{
  "schedulingEnabled": true,
  "scalingEnabled": false
}
```
- **适用场景**：已有固定资源池，只需要调度功能
- **行为**：函数会根据ResourcePool配置进行调度，但不会自动扩缩容

#### 场景2：仅启用扩缩容，禁用调度
```json
{
  "schedulingEnabled": false,
  "scalingEnabled": true
}
```
- **适用场景**：逐步迁移，先建立资源池管理，后启用调度
- **行为**：cron会管理ResourcePool节点扩缩容，但函数调度仍使用原有逻辑

#### 场景3：全部启用
```json
{
  "schedulingEnabled": true,
  "scalingEnabled": true
}
```
- **适用场景**：完整的ResourcePool功能
- **行为**：函数调度使用ResourcePool，同时自动管理资源池扩缩容

#### 场景4：全部禁用
```json
{
  "schedulingEnabled": false,
  "scalingEnabled": false
}
```
- **适用场景**：完全禁用ResourcePool功能
- **行为**：回退到原有的调度和扩缩容逻辑

## 📁 模块实现

### 1. poolmanager模块

**文件**: `pkg/poolmanager/resourcepool/cache.go`

```go
// 使用schedulingEnabled控制调度功能
enabled: k8sInfo.ResourcePoolConfig != nil && k8sInfo.ResourcePoolConfig.SchedulingEnabled
```

**影响的功能**：
- 函数调度时的资源池选择
- 用户专属池优先级调度
- 运行时匹配和池子选择

### 2. cron模块

**文件**: `pkg/cron/resourcepool/helper.go`

```go
// 使用scalingEnabled控制扩缩容功能
func (h *Helper) IsResourcePoolScalingEnabled(k8sInfo *api.K8sInfo) bool {
    return k8sInfo.ResourcePoolConfig.ScalingEnabled
}
```

**影响的功能**：
- ResourcePool节点自动扩缩容
- 资源池监控和管理
- 节点回收和清理

## 🔄 兼容性设计

### 1. 向后兼容

为保持向后兼容，提供了兼容性方法：

```go
// 兼容性方法，默认检查扩缩容开关
func (h *Helper) IsResourcePoolEnabled(k8sInfo *api.K8sInfo) bool {
    return h.IsResourcePoolScalingEnabled(k8sInfo)
}
```

### 2. 迁移策略

1. **阶段1**：保持现有配置不变，新增开关字段
2. **阶段2**：逐步更新配置，启用功能分离开关
3. **阶段3**：完全切换到新的开关设计

## 📊 配置示例

### 完整配置示例

```json
{
  "resourcePoolConfig": {
    "schedulingEnabled": true,
    "scalingEnabled": true,
    "version": "3.0",
    "userPoolMappings": {
      "premium-user-001": "gpu-pool",
      "vip-user-002": "high-memory-pool"
    },
    "extraResourcePools": {
      "gpu-pool": {
        "enabled": true,
        "poolType": "dedicated",
        "resourcePoolInfo": {
          "description": "GPU专用资源池",
          "supportRuntimes": ["tensorflow", "pytorch"]
        }
      }
    }
  }
}
```

## 🎯 优势

1. **功能解耦**：调度和扩缩容功能独立控制
2. **灵活部署**：支持渐进式功能启用
3. **风险控制**：可以单独测试和验证各个功能
4. **运维友好**：出现问题时可以快速禁用特定功能

## 🔍 监控和日志

### 1. 调度开关监控
- 监控函数调度是否使用ResourcePool
- 记录资源池选择的决策过程

### 2. 扩缩容开关监控
- 监控ResourcePool节点的扩缩容操作
- 记录资源池管理的执行情况

这种功能分离的开关设计提供了更精细的控制粒度，使得ResourcePool功能的部署和管理更加灵活可控。
