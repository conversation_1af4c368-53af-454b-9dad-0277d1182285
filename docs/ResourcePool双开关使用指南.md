# ResourcePool双开关使用指南

## 🎯 概述

ResourcePool功能采用**双开关设计**，提供细粒度的控制能力：
- **全局开关**：控制整个ResourcePool功能的启用/禁用
- **单个池子开关**：控制每个池子的启用/禁用

## 🔧 配置结构

### 基本配置格式
```json
{
  "resourcePoolConfig": {
    "enabled": true,                    // 全局开关
    "version": "1.0",
    "extraResourcePools": {
      "pool-name": {
        "enabled": true,                // 单个池子开关
        "resourcePoolInfo": {
          "description": "池子描述",
          "osType": "ubuntu2204",
          "supportRuntimes": ["java17", "nodejs18"]
        },
        "scalingOptions": { ... }
      }
    }
  }
}
```

### 开关组合效果

| 全局开关 | 池子开关 | 池子状态 | 说明 |
|---------|---------|---------|------|
| `false` | `true`  | ❌ 禁用  | 全局开关优先级最高 |
| `false` | `false` | ❌ 禁用  | 全局开关优先级最高 |
| `true`  | `true`  | ✅ 启用  | 正常启用状态 |
| `true`  | `false` | ❌ 禁用  | 单个池子被禁用 |

## 📋 使用场景

### 1. 功能上线场景

#### 场景1：保守上线
```json
// 第一步：部署新服务，保持功能关闭
{
  "resourcePoolConfig": {
    "enabled": false,  // 全局关闭，确保安全
    "extraResourcePools": {
      "ubuntu2204-pool": {
        "enabled": true,  // 池子配置就绪
        "resourcePoolInfo": { ... }
      }
    }
  }
}

// 第二步：验证服务正常后，启用功能
{
  "resourcePoolConfig": {
    "enabled": true,   // 启用全局开关
    "extraResourcePools": { ... }
  }
}
```

#### 场景2：渐进式上线
```json
// 第一步：启用功能，但只启用稳定的池子
{
  "resourcePoolConfig": {
    "enabled": true,
    "extraResourcePools": {
      "ubuntu2204-pool": {
        "enabled": true,   // 启用稳定池子
        "resourcePoolInfo": { ... }
      },
      "gpu-pool": {
        "enabled": false,  // 暂时禁用新池子
        "resourcePoolInfo": { ... }
      }
    }
  }
}

// 第二步：验证稳定后，逐步启用其他池子
{
  "resourcePoolConfig": {
    "enabled": true,
    "extraResourcePools": {
      "ubuntu2204-pool": {
        "enabled": true,   // 保持启用
        "resourcePoolInfo": { ... }
      },
      "gpu-pool": {
        "enabled": true,   // 启用新池子
        "resourcePoolInfo": { ... }
      }
    }
  }
}
```

### 2. 故障处理场景

#### 场景1：紧急回滚
```json
// 出现问题时，快速关闭整个功能
{
  "resourcePoolConfig": {
    "enabled": false,  // 一键关闭，立即生效
    "extraResourcePools": { ... }
  }
}
```

#### 场景2：故障隔离
```json
// 某个池子有问题，只禁用该池子
{
  "resourcePoolConfig": {
    "enabled": true,   // 保持功能启用
    "extraResourcePools": {
      "ubuntu2204-pool": {
        "enabled": true,   // 正常池子继续服务
        "resourcePoolInfo": { ... }
      },
      "gpu-pool": {
        "enabled": false,  // 禁用问题池子
        "resourcePoolInfo": { ... }
      }
    }
  }
}
```

### 3. 测试和调试场景

#### A/B测试
```json
// 对比测试：部分用户使用新池子，部分用户使用默认池
{
  "resourcePoolConfig": {
    "enabled": true,
    "extraResourcePools": {
      "test-pool": {
        "enabled": true,   // 启用测试池子
        "resourcePoolInfo": {
          "supportRuntimes": ["java17"],  // 只支持特定运行时
          "dedicatedUsers": ["test-user-1", "test-user-2"]  // 只对特定用户开放
        }
      }
    }
  }
}
```

#### 配置调试
```json
// 调试特定池子的配置
{
  "resourcePoolConfig": {
    "enabled": true,
    "extraResourcePools": {
      "debug-pool": {
        "enabled": false,  // 暂时禁用，调试配置
        "resourcePoolInfo": {
          "description": "调试中的池子配置",
          "supportRuntimes": ["experimental-runtime"]
        }
      }
    }
  }
}
```

## 🚀 最佳实践

### 1. 配置管理
- **版本控制**：所有配置变更都要进行版本控制
- **分步部署**：先部署服务，再启用配置
- **监控验证**：每次变更后验证关键指标

### 2. 开关使用
- **全局开关**：用于功能级别的控制
- **池子开关**：用于池子级别的精细控制
- **默认状态**：新池子默认启用，新功能默认禁用

### 3. 故障处理
- **快速响应**：出现问题时优先使用全局开关快速关闭
- **精确隔离**：确定问题范围后使用池子开关精确隔离
- **逐步恢复**：修复问题后逐步启用，避免二次故障

### 4. 测试验证
- **配置验证**：每次变更前验证配置格式正确性
- **功能测试**：验证开关逻辑和调度行为
- **性能测试**：确保变更不影响系统性能

## 📊 监控和告警

### 关键指标
```yaml
# 功能状态监控
- resourcepool_global_enabled: 全局开关状态
- resourcepool_pool_enabled_count: 启用的池子数量
- resourcepool_pool_disabled_count: 禁用的池子数量

# 调度行为监控
- resourcepool_selection_total: 池子选择次数统计
- resourcepool_fallback_total: 降级到默认池次数

# 配置变更监控
- resourcepool_config_change_total: 配置变更次数
- resourcepool_switch_toggle_total: 开关切换次数
```

### 告警规则
```yaml
# 异常告警
- name: ResourcePool功能异常关闭
  condition: resourcepool_global_enabled == 0
  severity: warning

- name: 大量池子被禁用
  condition: resourcepool_pool_disabled_count > 50% of total
  severity: critical

- name: 频繁开关切换
  condition: rate(resourcepool_switch_toggle_total[5m]) > 10
  severity: warning
```

## 🔍 故障排查

### 常见问题

#### 1. 池子不生效
**症状**：配置了池子但函数仍调度到默认池
**排查步骤**：
1. 检查全局开关是否启用：`resourcePoolConfig.enabled`
2. 检查池子开关是否启用：`pool.enabled`
3. 检查运行时支持：`pool.resourcePoolInfo.supportRuntimes`
4. 查看日志：搜索"ResourcePool.*disabled"

#### 2. 配置不生效
**症状**：修改配置后行为没有变化
**排查步骤**：
1. 检查配置格式是否正确
2. 等待配置生效（最多2秒）
3. 查看配置重新加载日志
4. 检查缓存状态：`/debug/resourcepool/cache`

#### 3. 性能问题
**症状**：启用ResourcePool后调度延迟增加
**排查步骤**：
1. 检查配置缓存命中率
2. 查看配置重新加载频率
3. 监控内存使用情况
4. 检查etcd连接状态

---

**通过合理使用双开关设计，可以实现ResourcePool功能的精细化控制，确保系统的稳定性和可靠性。** 🎯
