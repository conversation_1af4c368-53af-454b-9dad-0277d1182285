# 用户专属资源池调度设计

## 🎯 设计目标

实现基于用户ID的专属资源池调度，支持：
1. **用户专属池优先**：用户优先使用自己的专属资源池
2. **智能降级机制**：专属池不可用时自动降级到通用调度逻辑
3. **完美兼容性**：与现有调度逻辑完全兼容
4. **简洁高效**：使用简单的uid->poolName映射关系

## 🏗️ 架构设计

### 调度优先级策略

```
函数请求(userID + runtime)
    ↓
1. 🥇 检查用户专属池映射
   - 查找 userPoolMappings[userID] -> poolName
   - 验证池子启用状态
   - 验证运行时支持
   - 验证用户访问权限
    ↓
2. 🥈 降级到通用调度逻辑
   - 使用现有的运行时选择算法
   - 检查用户对选中池子的访问权限
    ↓
3. 🏁 最终降级到默认池
   - 确保函数总能找到可用资源
```

### 资源池类型

| 池子类型 | 描述 | 访问策略 | 使用场景 |
|---------|------|----------|----------|
| **shared** | 共享池 | 所有用户可访问 | 通用计算资源 |
| **dedicated** | 专属池 | 仅指定用户可访问 | 高优先级用户专用 |
| **hybrid** | 混合池 | 专属用户优先，其他用户可用 | 弹性资源共享 |

## 📋 配置结构（简化版）

### 用户映射配置
```json
{
  "resourcePoolConfig": {
    "enabled": true,
    "version": "2.0",
    "userPoolMappings": {
      "premium-user-001": "gpu-pool",
      "vip-user-002": "high-memory-pool",
      "standard-user-003": "ubuntu2204-pool"
    }
  }
}
```

### 资源池配置
```json
{
  "extraResourcePools": {
    "gpu-pool": {
      "enabled": true,
      "poolType": "dedicated",
      "dedicatedUsers": ["premium-user-001"],
      "resourcePoolInfo": {
        "supportRuntimes": ["tensorflow", "pytorch"]
      }
    }
  }
}
```

## 🔄 调度流程详解

### 1. 用户专属池选择 (selectUserDedicatedPool)

```go
func selectUserDedicatedPool(userID, runtime string) string {
    // 1. 检查用户映射关系
    if userPoolMappings == nil {
        return ""
    }

    // 2. 查找用户专属池映射
    poolName, exists := userPoolMappings[userID]
    if !exists {
        return ""
    }

    // 3. 检查专属池是否可用
    if pool, exists := extraResourcePools[poolName]; exists {
        if pool.Enabled && isRuntimeSupported(pool, runtime) && isUserAllowedInPool(userID, poolName, pool) {
            return poolName
        }
    }

    return ""
}
```

### 2. 数据结构设计

```go
// ResourcePoolConfig 资源池配置（简化版）
type ResourcePoolConfig struct {
    Enabled            bool                       `json:"enabled"`            // 全局开关：控制整个ResourcePool功能是否启用
    Version            string                     `json:"version"`            // 配置版本
    UserPoolMappings   map[string]string          `json:"userPoolMappings"`   // 用户到资源池的映射关系 uid -> poolName
    ExtraResourcePools map[string]*ResourcePool   `json:"extraResourcePools"` // 额外资源池配置（使用指针提高效率）
}

// ResourcePoolCache 缓存结构（简化版）
type ResourcePoolCache struct {
    vipUserLabel       string                       // vipUser标签值
    enabled            bool                         // ResourcePool功能是否启用
    defaultPoolInfo    *api.ResourcePoolInfo        // 默认池配置
    extraResourcePools map[string]*api.ResourcePool // 合并后的额外ResourcePool配置（使用指针）
    userPoolMappings   map[string]string            // 用户到资源池的映射关系 uid -> poolName
    lastUpdateTime     time.Time                    // 最后更新时间
}
```

### 3. 用户权限检查 (isUserAllowedInPool)

```go
func isUserAllowedInPool(userID, poolName string, pool ResourcePool) bool {
    switch pool.PoolType {
    case "shared":
        return true  // 共享池：所有用户可访问

    case "dedicated":
        // 专属池：仅指定用户可访问
        return contains(pool.DedicatedUsers, userID)

    case "hybrid":
        return true  // 混合池：所有用户可访问（实际可加资源使用率检查）

    default:
        return true  // 默认为共享池
    }
}
```

## 🧪 测试场景

### 测试用例覆盖（简化版）

| 场景 | 用户 | 运行时 | 预期池子 | 说明 |
|------|------|--------|----------|------|
| 专属池访问 | premium-user-001 | tensorflow | gpu-pool | 用户映射到GPU池 |
| 混合池访问 | vip-user-002 | python3.10 | high-memory-pool | 用户映射到高内存池 |
| 普通用户 | normal-user | nodejs18 | ubuntu2204-pool | 无映射，使用通用调度逻辑 |
| 运行时不支持 | premium-user-001 | unsupported | default | 专属池不支持时降级到默认池 |

### 兼容性验证

✅ **现有逻辑兼容**：未配置用户映射时使用原有调度逻辑
✅ **默认池保障**：任何情况下都能降级到默认池
✅ **配置热更新**：支持用户映射配置的实时更新
✅ **性能影响最小**：增加的检查逻辑对性能影响微乎其微

## 🚀 实现亮点

### 1. 简化映射关系维护
- **etcd配置驱动**：用户映射关系存储在etcd中，支持热更新
- **缓存优化**：在poolmanager中缓存用户映射，避免频繁etcd访问
- **多集群合并**：支持多集群用户映射配置的智能合并
- **指针优化**：使用`map[string]*ResourcePool`提高内存效率

### 2. 兼容性保障
- **渐进式启用**：通过`ResourcePoolConfig.enabled`开关控制功能启用
- **降级机制**：专属池不可用时自动降级到通用调度逻辑
- **零影响部署**：现有函数调用行为完全不变

### 3. 简洁权限控制
- **直接映射**：uid -> poolName 的简单映射关系
- **多种池子类型**：shared/dedicated/hybrid满足不同需求
- **细粒度控制**：可精确控制每个用户对每个池子的访问权限

## 📊 监控指标

建议添加以下监控指标：

```go
// 用户专属池调度指标
user_dedicated_pool_selections_total{user_id, pool_name, runtime}
user_pool_access_denied_total{user_id, pool_name, reason}
user_fallback_to_default_total{user_id, runtime, reason}
```

## 🔧 配置最佳实践

### 1. 用户分层策略
```json
{
  "premium-users": {
    "dedicatedPools": ["gpu-pool", "high-memory-pool"],
    "fallbackPools": ["ubuntu2204-pool"]
  },
  "vip-users": {
    "dedicatedPools": ["high-memory-pool"],
    "fallbackPools": ["ubuntu2204-pool"]
  },
  "standard-users": {
    "dedicatedPools": [],
    "fallbackPools": ["ubuntu2204-pool"]
  }
}
```

### 2. 池子配置策略
- **GPU池**：dedicated类型，仅高优先级用户
- **高内存池**：hybrid类型，VIP用户优先，其他用户可用
- **通用池**：shared类型，所有用户可用

这个设计完美解决了您提出的需求：**函数到来后先根据uid找用户专属池，不支持时降级到公共池**，同时保持了与现有逻辑的完美兼容性。
