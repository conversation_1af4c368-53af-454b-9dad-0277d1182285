# ResourcePool功能说明

## 概述

ResourcePool功能允许在单个集群中创建多个资源池，每个池子可以有不同的配置，包括：
- 不同的操作系统版本
- 不同的容器镜像
- 不同的运行时支持
- 不同的扩缩容策略

## 核心特性

### 1. 完美向后兼容
- 现有集群配置无需修改
- 默认池行为完全不变
- 新旧Key格式可以并存

### 2. 自动运行时选择
- 根据函数运行时自动选择合适的ResourcePool
- 支持运行时到池子的智能映射
- 降级机制确保兼容性

### 3. Redis Key自动分片
- 不同池子的Key自动分散
- 提升Redis并发性能
- 减少热点Key问题

## 配置格式

### 基本结构

```json
{
  "cceClusterUUID": "cce-u4clzq75",
  "k8sOptions": { ... },
  "scalingOptions": { ... },
  "scalingUpOptions": { ... },
  "poolInfo": {
    "description": "默认池描述",
    "osType": "ubuntu16",
    "supportRuntimes": ["python3", "java8", "nodejs12"]
  },
  "resourcePoolConfig": {
    "enabled": true,
    "version": "v1.0",
    "extraPools": {
      "pool-name": {
        "poolInfo": { ... },
        "scalingOptions": { ... }
      }
    }
  }
}
```

### 配置说明

#### poolInfo（默认池信息）
- `description`: 池子描述
- `osType`: 操作系统类型
- `containerImage`: 容器镜像（可选，为空时使用k8sOptions中的配置）
- `supportRuntimes`: 支持的运行时列表

#### resourcePoolConfig
- `enabled`: 是否启用ResourcePool功能
- `version`: 配置版本
- `extraPools`: 新增池子配置

#### extraPools中的每个池子
- `poolInfo`: 池子基本信息（必填）
- `scalingOptions`: 扩缩容配置（可选，为空时复用集群配置）

## Redis Key格式变化

### 默认池（兼容现有格式）
```bash
# poolmanager模块
kun:warm_node:{cfc:common:dummyUSER}:128
kun:warm_pod:cfc:common:dummyUSER:::128:abc123def456

# cron模块
kun:node_lock:node-001
kun:scaling_task:cluster-001:task-123
```

### extraPools（新格式）
```bash
# poolmanager模块
kun:warm_node:{cfc:common:ubuntu2204-pool:dummyUSER}:128
kun:warm_pod:cfc:common:ubuntu2204-pool:dummyUSER:::128:def456abc123

# cron模块
kun:node_lock:ubuntu2204-pool:node-001
kun:scaling_task:ubuntu2204-pool:cluster-001:task-123
```

## 使用示例

### 1. 启用ResourcePool功能

在etcd中更新集群配置：

```bash
etcdctl put faas-kun1.0/k8s/cce-u4clzq75 '{
  "cceClusterUUID": "cce-u4clzq75",
  "k8sOptions": { ... },
  "resourcePoolConfig": {
    "enabled": true,
    "version": "v1.0",
    "extraPools": {
      "ubuntu2204-pool": {
        "poolInfo": {
          "description": "Ubuntu 22.04 pool",
          "osType": "ubuntu22",
          "containerImage": "registry.baidubce.com/cfc_dev/runner:ubuntu2204-java17",
          "supportRuntimes": ["python3", "java17", "nodejs18"]
        }
      }
    }
  }
}'
```

### 2. 函数调用流程

1. **eventhub接收请求**
   - 获取函数运行时信息
   - 调用ConfigManager选择ResourcePool
   - 在labels中设置resourcePool标签

2. **poolmanager调度**
   - 根据labels生成对应的Redis Key
   - 从对应池子的节点中分配资源

3. **cron扩缩容**
   - 为不同池子创建不同的节点
   - 使用池子特定的配置进行扩缩容

### 3. 运行时映射示例

```go
// 自动选择逻辑
func SelectResourcePool(clusterID, runtime string) string {
    config := configManager.GetClusterConfig(clusterID)
    
    // 检查extraPools
    for poolName, pool := range config.ResourcePoolConfig.ExtraPools {
        if contains(pool.PoolInfo.SupportRuntimes, runtime) {
            return poolName
        }
    }
    
    // 检查默认池
    if contains(config.PoolInfo.SupportRuntimes, runtime) {
        return "default"
    }
    
    // 降级到默认池
    return "default"
}
```

## 监控和运维

### 1. Key分布监控

```bash
# 查看不同池子的Key分布
redis-cli --scan --pattern "kun:warm_node:*" | grep -E "(ubuntu2204-pool|gpu-pool)" | wc -l
```

### 2. 池子状态查询

```bash
# 查看特定池子的节点数量
redis-cli zcard "kun:warm_node:{cfc:common:ubuntu2204-pool:dummyUSER}:128"
```

### 3. 配置验证

```bash
# 验证集群配置
etcdctl get faas-kun1.0/k8s/cce-u4clzq75 | jq '.resourcePoolConfig'
```

## 注意事项

### 1. 配置更新
- 配置更新会自动触发缓存刷新
- 新配置对新请求立即生效
- 现有Pod不受影响

### 2. 池子删除
- 删除池子前确保没有运行中的Pod
- 建议先停止相关函数的调用
- 清理对应的Redis Key

### 3. 性能考虑
- 不同池子的Key会分散到不同Redis分片
- 建议合理规划池子数量
- 监控Redis性能指标

## 故障排查

### 1. 函数调度到错误的池子
- 检查运行时配置是否正确
- 验证supportRuntimes列表
- 查看ConfigManager日志

### 2. Redis Key格式异常
- 检查labels中的resourcePool标签
- 验证Key生成逻辑
- 查看poolmanager日志

### 3. 扩缩容异常
- 检查池子的scalingOptions配置
- 验证节点标签设置
- 查看cron模块日志
