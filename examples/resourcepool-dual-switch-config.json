{"cceClusterUUID": "example-cluster-001", "k8sOptions": {"containerImage": "registry.baidubce.com/cfc_dev/runner:ubuntu1604-0720-java11", "dsReleaseConfigMap": {"runtimes": {"imageID": "registry.baidubce.com/cfc_dev/runtime:unified-0722-multi-os"}}}, "scalingOptions": {"autoScalingType": "threshold", "thresholdMap": {"128": {"unoccupiedRedundancy": 3, "scalingUpTrigger": 1.1, "scalingDownTrigger": 1.3, "ActualRedundancy": 3}, "256": {"unoccupiedRedundancy": 2, "scalingUpTrigger": 1.2, "scalingDownTrigger": 1.4, "ActualRedundancy": 2}}, "scalingDown": true, "scalingTimeout": 600, "maxscalingDownSize": 1}, "scalingUpOptions": {"singleScalingRequestSize": 1, "maxNodeCount": 50}, "resourcePoolInfo": {"description": "默认资源池 - Ubuntu 16.04", "osType": "ubuntu1604", "supportRuntimes": ["nodejs12", "nodejs14", "nodejs16", "python27", "python36", "python37", "python38", "java8", "java11", "php72", "php74", "go118", "dotnet31"]}, "resourcePoolConfig": {"enabled": true, "version": "1.0", "extraResourcePools": {"ubuntu2204-pool": {"enabled": true, "resourcePoolInfo": {"description": "Ubuntu 22.04 通用资源池 - 支持新版本运行时", "osType": "ubuntu2204", "containerImage": "registry.baidubce.com/cfc_dev/runner:ubuntu2204-0720-java17", "supportRuntimes": ["nodejs18", "nodejs20", "nodejs22", "python39", "python310", "python311", "java17", "java21", "go119", "go120", "go121", "dotnet60", "dotnet70"]}, "scalingOptions": {"autoScalingType": "node", "thresholdMap": {"128": {"unoccupiedRedundancy": 2, "scalingUpTrigger": 1.0, "scalingDownTrigger": 1.2, "ActualRedundancy": 2}, "256": {"unoccupiedRedundancy": 1, "scalingUpTrigger": 0.8, "scalingDownTrigger": 1.0, "ActualRedundancy": 1}}, "scalingDown": true, "scalingTimeout": 600, "maxscalingDownSize": 1}}, "gpu-pool": {"enabled": false, "resourcePoolInfo": {"description": "GPU计算资源池 - 支持AI/ML工作负载（暂时禁用）", "osType": "ubuntu2004", "containerImage": "registry.baidubce.com/cfc_dev/runner:ubuntu2004-gpu-0720", "supportRuntimes": ["python39", "python310", "tensorflow", "tensorflow2", "pytorch", "pytorch2", "cuda", "tensorrt"]}, "scalingOptions": {"autoScalingType": "node", "thresholdMap": {"1024": {"unoccupiedRedundancy": 1, "scalingUpTrigger": 1.0, "scalingDownTrigger": 1.5, "ActualRedundancy": 1}, "2048": {"unoccupiedRedundancy": 0, "scalingUpTrigger": 0.8, "scalingDownTrigger": 2.0, "ActualRedundancy": 0}}, "scalingDown": false, "scalingTimeout": 900, "maxscalingDownSize": 0}}, "high-memory-pool": {"enabled": true, "resourcePoolInfo": {"description": "高内存资源池 - 支持大内存工作负载", "osType": "ubuntu2004", "containerImage": "registry.baidubce.com/cfc_dev/runner:ubuntu2004-highmem-0720", "supportRuntimes": ["java11", "java17", "java21", "python38", "python39", "python310", "nodejs16", "nodejs18", "nodejs20", "spark", "hadoop", "elasticsearch"]}, "scalingOptions": {"autoScalingType": "node", "thresholdMap": {"512": {"unoccupiedRedundancy": 2, "scalingUpTrigger": 1.1, "scalingDownTrigger": 1.3, "ActualRedundancy": 2}, "1024": {"unoccupiedRedundancy": 1, "scalingUpTrigger": 1.0, "scalingDownTrigger": 1.5, "ActualRedundancy": 1}}, "scalingDown": true, "scalingTimeout": 800, "maxscalingDownSize": 1}}, "edge-pool": {"enabled": false, "resourcePoolInfo": {"description": "边缘计算资源池 - 低延迟场景（开发中）", "osType": "ubuntu2004", "containerImage": "registry.baidubce.com/cfc_dev/runner:ubuntu2004-edge-0720", "supportRuntimes": ["nodejs16", "nodejs18", "go118", "go119", "go120", "rust", "wasm"]}, "scalingOptions": {"autoScalingType": "node", "thresholdMap": {"64": {"unoccupiedRedundancy": 5, "scalingUpTrigger": 1.2, "scalingDownTrigger": 1.5, "ActualRedundancy": 5}, "128": {"unoccupiedRedundancy": 3, "scalingUpTrigger": 1.1, "scalingDownTrigger": 1.3, "ActualRedundancy": 3}}, "scalingDown": true, "scalingTimeout": 300, "maxscalingDownSize": 2}}}}}