{"resourcePoolConfig": {"schedulingEnabled": true, "scalingEnabled": true, "version": "3.0", "userPoolMappings": {"premium-user-001": "gpu-pool", "vip-user-002": "high-memory-pool"}, "extraResourcePools": {"gpu-pool": {"enabled": true, "resourcePoolInfo": {"description": "GPU专用资源池", "supportRuntimes": ["tensorflow", "pytorch", "python3.10"], "dedicatedUsers": ["premium-user-001"]}, "scalingOptions": {"minNodes": 2, "maxNodes": 10, "scaleUpThreshold": 80, "scaleDownThreshold": 20}}, "high-memory-pool": {"enabled": true, "resourcePoolInfo": {"description": "高内存混合资源池", "supportRuntimes": ["java17", "python3.10", "nodejs18"], "dedicatedUsers": ["vip-user-002"]}, "scalingOptions": {"minNodes": 1, "maxNodes": 8, "scaleUpThreshold": 70, "scaleDownThreshold": 30}}, "ubuntu2204-pool": {"enabled": true, "resourcePoolInfo": {"description": "Ubuntu 22.04共享资源池", "supportRuntimes": ["java17", "python3.10", "nodejs18", "go1.19"]}, "scalingOptions": {"minNodes": 3, "maxNodes": 15, "scaleUpThreshold": 75, "scaleDownThreshold": 25}}}}}