{"resourcePoolConfig": {"enabled": true, "version": "2.0", "userPoolMappings": {"premium-user-001": "gpu-pool", "vip-user-002": "high-memory-pool", "standard-user-003": "ubuntu2204-pool"}, "extraResourcePools": {"gpu-pool": {"enabled": true, "poolType": "dedicated", "dedicatedUsers": ["premium-user-001"], "resourcePoolInfo": {"description": "GPU专属计算资源池", "osType": "ubuntu2204", "supportRuntimes": ["tensorflow", "pytorch", "cuda-python"], "nodeSelector": {"gpu": "nvidia-v100"}}, "scalingOptions": {"singleScalingRequestSize": 1, "maxNodeCount": 10, "minNodeCount": 2}}, "high-memory-pool": {"enabled": true, "poolType": "hybrid", "dedicatedUsers": ["premium-user-001", "vip-user-002"], "resourcePoolInfo": {"description": "高内存混合资源池", "osType": "ubuntu2204", "supportRuntimes": ["java17", "python3.10", "nodejs18"], "nodeSelector": {"memory": "high"}}, "scalingOptions": {"singleScalingRequestSize": 2, "maxNodeCount": 20, "minNodeCount": 3}}, "ubuntu2204-pool": {"enabled": true, "poolType": "shared", "dedicatedUsers": [], "resourcePoolInfo": {"description": "Ubuntu 22.04共享资源池", "osType": "ubuntu2204", "supportRuntimes": ["java17", "python3.10", "nodejs18", "go1.19"], "nodeSelector": {"os": "ubuntu22.04"}}, "scalingOptions": {"singleScalingRequestSize": 3, "maxNodeCount": 50, "minNodeCount": 5}}}}, "poolInfo": {"description": "默认资源池（Ubuntu 16.04）", "osType": "ubuntu1604", "supportRuntimes": ["nodejs12", "nodejs14", "python3.6", "java8"], "nodeSelector": {"os": "ubuntu16.04"}}, "scalingUpOptions": {"singleScalingRequestSize": 1, "maxNodeCount": 100}, "scalingDownOptions": {"singleScalingRequestSize": 1, "minNodeCount": 0}}